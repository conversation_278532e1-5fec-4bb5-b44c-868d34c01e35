<?php

require("../include.php");

require("../functions.php");

// Checking for user permissions
// @todo: update the responses to json and add a success property.
// @todo: refer to VT-136 : Access Control
/*
if ( !CheckUserAccess() ) {
	exit("-1");
}
*/
OpenDatabase();

$sqlSelectSecureRules = "
	SELECT secure_rules FROM sr_tickets WHERE id = '" . (int) $_GET["id"] . "';
";

$res = mysql_query( $sqlSelectSecureRules );

if (!$res) {
	echo "-1";
}

$row = mysql_fetch_assoc( $res );
$srules = explode(',', $row['secure_rules']);

$duplicated = false;

foreach ($srules as $key => $rule) {

	$rule = (int) $rule;

	// @todo: why so many OR conditions between the secure_rules?
	// Can't we just check with:
	// "SELECT ... AND secure_rules LIKE '%" . $rule . "%'

	$sqlSelectSecureRules = "
		SELECT
			secure_rules
		FROM
				sr_tickets WHERE id <> '" . (int) $_GET["id"] . "'
			AND is_confidential = '1'
			AND (
					secure_rules = '" . $rule . "'
				OR  secure_rules LIKE '" . $rule . ",%'
				OR  secure_rules LIKE '%," . $rule . "'
				OR  secure_rules LIKE '%," . $rule . ",%'


				);";

	$result = mysql_query( $sqlSelectSecureRules );
	while ( $found = mysql_fetch_assoc( $result ) ) {
		$duplicated = true;
	}
}

if ( $duplicated ) {
	echo "1";
} else {
	echo "0";
}
