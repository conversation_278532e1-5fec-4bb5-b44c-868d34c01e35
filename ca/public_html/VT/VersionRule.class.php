<?php

namespace VT {

  /**
   * Class for fetching version rule data.
   *
   * This class was written for the zero day page i.e. to extract
   * regexps from version rules but that functionality turned out to
   * be useless. However, having a class that handles version rules
   * can be helpful in future and that is why we are keeping it
   * here. It might be that in future this class serves all ajax
   * requests / php code generated from version_rules.php
   *
   * @requires Package.class.php
   */

class VersionRule {

	private static $whitelist =
		array(
			  'allowed' => array(
									'id'
									,'product_id'
									,'regex'
									,'solution_vuln_id'
								 )
			  ,'searchable' => array(
									'id'
									 )

			  ,'orderable' => array(
									'id'
									)

			  ,'groupable' => array(
									)
			  );


	/**
	 * Db connection that needs to be set at intantiation
	 */
	private $db;

	/**
	 * Db connection that is required for updating the data. This
	 * connection isn't required to read the values and therefore
	 * does not need to be set at instantiation. To set this,
	 * setConfidentialDb() function should be used
	 */
	private $dbConf;

	private $table = 'sr_product_secure';

	/**
	 * @param Boolean $readConfidential
	 *
	 * If true, data will be read from the confidential tables (which
	 * should ideally reside in the vuln_track_confidential database,
	 * however in most cases it is a table with the same name but
	 * postfixed with _confidential
	 * e.g. sr_product_secure_confidential).
	 */
	private $readConfidential = false;

	/**
	 * @param Package $package
	 *
	 * Object that contains package related information
	 * e.g. parameters etc
	 */
	private $package;

	public function __construct( \DB $db, $readConfidential = false ) {
		$this->db = $db;
		$this->readConfidential = $readConfidential ? true : false;

		// Initializing dependencies
		$this->package = new Package\Package( $this->db, $this->readConfidential );
	}

	/**
	 * Function for setting the confidential database
	 *
	 * @param Db $db
	 */
	public function setConfidentialDb( \Db $db ) {
		$this->dbConf = $db;
		$this->package->setConfidentialDb( $db );
	}

	private function read( Array $modifiers = array() ) {
		$columns = !empty( $modifiers['columns'] ) ? $modifiers['columns'] : self::$whitelist['allowed'];
		$where = $modifiers['where'];
		$order = $modifiers['order'];
		$limit = $modifiers['limit'];

		$select = $this->db->select()
			->columns( $columns )
			->from( $this->table )
			->where( $where );

		if ( !empty( $order ) ) {
			$select->orderBy( $order );
		}

		return array( 'rows' => $select->exec(), 'count' => $select->rowCountIgnoreLimit() ) ;
	}

	public function handleRequest( $action ) {
		$response = array( 'success' => false, 'data' => '' );

		switch( $action ) {
		case 'read':
			$userModifiers = Grid::getUserModifiers( self::$whitelist );
			$response['data'] = $this->read( $userModifiers );
			$response['success'] = true;
			break;
		case 'read_sps_params':
			/*
			 * @todo: the version_rule_ids can be a comma separated
			 * list of integers. For now we only support a single one
			 */
			$versionRuleId = (int) $_GET['version_rule_ids'];
			if ( $versionRuleId <= 0 ) {
				break;
			}
			$response['data'] = $this->package->getSpsParams( $versionRuleId );
			$response['success'] = true;
			break;
		case 'read_solutions':
			$versionRuleId = (int) $_GET['version_rule_ids'];
			if ( $versionRuleId <= 0 ) {
				break;
			}
			$response['data'] = $this->package->getSolutions( $versionRuleId, array( 'type' => Package\SolutionType::EXCLUSIVE ) );
			$response['success'] = true;
			break;
		}

		return $response;

	}
}

  }