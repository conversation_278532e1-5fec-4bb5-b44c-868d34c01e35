<?php

namespace VT {

  /**
   * Class for providing backwards compatibility with older versions of the vuln track.
   */

class Compat {

	/**
	 * Db connection that needs to be set at intantiation
	 */
	private $db;

	public function __construct( \DB $db ) {
		$this->db = $db;
	}

	/**
	 * Function to find the version rule id:
	 *
	 * @param $productId Integer
	 * @param $version Integer
	 * @param $specialVersion Integer [Optional]
	 *
	 * @return Mixed
	 *   SUCCESS : Version rule Id
	 *   FAILURE : False
	 */
	public function findAVersionRuleId( $productId, $version, $specialVersion = null ) {

		$conditions = array( 'product_id' => $productId );
		if ( empty( $specialVersion ) ) {
			$conditions[ 'version' ] = $version;
		} else {
			$conditions[ 'special_version' ] = $specialVersion;
		}

		$rows = $this->db->select()
			->distinct()
			->columns( array( 'id' ) )
			->from( 'sr_product_secure' )
			->where( $conditions )
			->exec();

		if ( empty( $rows ) ) {
			return false;
		}

		$versionRuleId = $rows[0]['id'];
		return $versionRuleId;
	}
}

  }