<?php

namespace VT\Package;

class SolutionWriter implements SolutionWriterInterface
{
    /**
     * Ideally we should use the packageId to track the Solutions for the package but the schema has the version rule
     * and the package coupled together. Hence, we use the version rule id to track all Solutions.
     */
    private $versionRuleId;

    /**
     * Db connection that needs to be set at instantiation
     */
    private $db;

    /**
     * Db connection that is required for updating the data. This connection isn't required to read the values and
     * therefore does not need to be set at instantiation. To set this, setConfidentialDb() function should be used
     */
    private $dbConf;

    /**
     * Since the package has a one to one relationship with the version rule, hence we use the version rule id to track
     * all solutions for the package.
     *
     * @param int $versionRuleId
     * @param \DB $db The database connection to use. For the Vuln Track, the db connection uses the mysql user for
     *    administrating the vuln track.
     * @param \DB $dbConf The confidential database connection to use. For the Vuln Track, the db connection uses the
     *  mysql user for administrating the vuln track.
     * @throws \Exception
     */
    public function __construct($versionRuleId, \DB $db, \DB $dbConf)
    {
        // Validate inputs
        if (!$versionRuleId || !is_numeric($versionRuleId)) {
            throw new \Exception('Invalid version rule Id.');
        }

        $this->versionRuleId = $versionRuleId;
        $this->db = $db;
        $this->dbConf = $dbConf;
    }

    /**
     * In order to save the values, the confidential database must be set. After saving it is still not possible to
     * read the values. To read, use the approve() method
     *
     * @todo: Currently, only the special packages are being saved. This is because the there are three separate tables
     * for the solutions and we can't change that because a few applications are relying on the schema by querying the
     * tables directly.
     *
     * @param array $solutions @todo: write about the format
     */
    public function save(array $solutions)
    {
        $this->cleanupDatabase($this->dbConf);

        foreach ($solutions as $solution) {
            // @todo: We move the following functionality to the Package ( or Solution, if it exists ) class.
            $set = array(
                'type' => $solution['type'],
                'iso_code' => $solution['iso_code'],
                'url' => $solution['url'],
                'sha1' => $solution['sha1'],
                'silent_param' => $solution['silent_param']
            );
            if (isset($solution['arch'])) {
                $set['arch'] = $solution['arch'];
            }

            // check if a solution already exists in the table then use the existing solution id
            $existingSolution = $this->dbConf->select()
                ->columns(array('solution_id'))
                ->from('solution')
                ->where($set)
                ->exec();

            if (empty ($existingSolution)) {
                $solutionId = $this->dbConf->insert()
                    ->into('solution')
                    ->set($set)
                    ->exec();
            } else {
                $solutionId = $existingSolution[0]['solution_id'];
            }

            $this->dbConf->insert()
                ->into('version_rule_solution_map')
                ->set(array('version_rule_id' => $this->versionRuleId, 'solution_id' => $solutionId))
                ->exec();
        }
    }

    protected function cleanupDatabase(\DB $db)
    {
        $db->delete()
            ->from('version_rule_solution_map')
            ->where(array('version_rule_id' => $this->versionRuleId))
            ->exec();

        // Removes all solutions that don't have a version rule associated with it
        // cant use delete() since it doesn't support joins
        $db->execRaw('DELETE solution
			FROM solution
			LEFT JOIN version_rule_solution_map
			USING (solution_id)
			WHERE version_rule_solution_map.solution_id IS NULL');
    }

    /**
     * Function to approve the saved changes. This makes the saved data readable (by moving it from the confidential
     * database to the live database)
     */
    public function approve()
    {
        // As a precaution, we clean the DB we are inserting to
        $this->cleanupDatabase($this->db);

        // Get all solutions from the confidential table
        $solutionConfidential = $this->dbConf->select()
            ->columns(array('type', 'url', 'sha1', 'arch', 'silent_param', 'iso_code'))
            ->from('solution')
            ->join('version_rule_solution_map', 'map')
            ->using(array('solution_id'))
            ->where(array('map.version_rule_id' => $this->versionRuleId))
            ->exec();

        // INSERT into the solution table
        foreach ($solutionConfidential as $solution) {
            $setArray = array(
                'type' => $solution['type'],
                'url' => $solution['url'],
                'sha1' => $solution['sha1'],
                'arch' => $solution['arch'],
                'silent_param' => $solution['silent_param'],
                'iso_code' => $solution['iso_code']
            );

            // check if a solution already exists in the table then use the existing solution id
            $existingSolution = $this->db->select()
                ->columns(array('solution_id'))
                ->from('solution')
                ->where($setArray)
                ->exec();

            if (empty ($existingSolution)) {
                $sid = $this->db->insert()
                    ->into('solution')
                    ->set($setArray)
                    ->exec();
            } else {
                $sid = $existingSolution[0]['solution_id'];
            }

            $this->db->insert()
                ->into('version_rule_solution_map')
                ->set(array('version_rule_id' => $this->versionRuleId, 'solution_id' => $sid))
                ->exec();
        }

        $this->cleanupDatabase($this->dbConf);
    }
}
