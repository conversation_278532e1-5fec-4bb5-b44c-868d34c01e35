<?php

namespace VT\Package;

class SolutionReader implements SolutionReaderInterface {

	/**
	 * Ideally we should use the packageId to track the Solutions
	 * for the package but the schema has the version rule and the
	 * package coupled together. Hence, we use the version rule id
	 * to track all Solutions.
	 */
	private $versionRuleId;

	/**
	 * Db connection that needs to be set at intantiation
	 */
	private $db;

	/**
	 * Db connection that is required for updating the data. This
	 * connection isn't required to read the values and therefore
	 * does not need to be set at instantiation. To set this,
	 * setConfidentialDb() function should be used
	 */
	private $dbConf;

	private static $whitelist = array(
									  'searchable' => array(
															'type'
															,'arch'
                                                            ,'iso_code'
															)
									  );

	/**
	 * Since the package has a one to one relationship with the
	 * version rule, hence we use the version rule id to track all
	 * solutions for the package.
	 *
	 * @param Integer $versionRuleId
	 *
	 * @param DB $db The database connection to use. For the Vuln
	 * Track, the db connection uses the mysql user for administrating
	 * the vuln track.
	 *
	 * @param boolean $useConfidential Should be set to true if we use
	 * the vuln_track_confidential table.
	 */
	public function __construct( $versionRuleId, \DB $db ) {

		/*
		 * Validate inputs
		 */
		if ( !$versionRuleId || !is_numeric( $versionRuleId ) ) {
			throw new \Exception( 'Invalid version rule Id.' );
		}

		$this->versionRuleId = $versionRuleId;
		$this->db = $db;
	}

	/**
	 * Function for setting the confidential database
	 *
	 * @param Db $db
	 */
	public function setConfidentialDb( \Db $db ) {
		$this->dbConf = $db;
	}

	/**
	 * Function for reading the package solutions
	 *
	 * @param Array $conditions
	 *
	 * @param Boolean $readConfidential
	 *
	 * If true, data will be read from the confidential tables (which
	 * should ideally reside in the vuln_track_confidential database,
	 * however in most cases it is a table with the same name but
	 * postfixed with _confidential
	 * e.g. sr_product_secure_confidential).
	 */
	public function read( Array $conditions = array(), $readConfidential = false ) {

		$db = null;

		if ( $readConfidential ) {
			if ( empty( $this->dbConf ) ) {
				throw new \Exception( 'Confidential DB connection is not set.' );
			}
			$db = $this->dbConf;
		} else {
			$db = $this->db;
		}

		$where = array();
		/*
		 * Making the 'where' for the query
		 */
		foreach ( $conditions as $name => $value ) {
			if ( !in_array( $name, self::$whitelist['searchable'] ) ) {
				continue; // @todo: log?
			}
            if ( $name == 'arch' && $value == 0 ) {
                continue; // if architecture is zero do not include in the where clause
            }
            $where[ $name ] = $value;
		}

		$where['map.version_rule_id'] = $this->versionRuleId;

		$rows = $db->select()
			->columns( array( 'type'
							  , 'url'
							  , 'sha1'
							  , 'arch'
                              , 'silent_param'
							  , 'iso_code'
							  ) )
			->from( 'solution' )
			->join( 'version_rule_solution_map', 'map' )
			->using( array( 'solution_id' ) )
			->where( $where )
			->orderBy(array( 'iso_code' => 'ASC', 'arch' => 'ASC', 'solution_id' => 'ASC'))
			->exec();

		$types = is_array( $conditions[ 'type' ] ) ? $conditions[ 'type' ] : array( (int) $conditions[ 'type' ] );

		/**
		 * Since the database schema is split between three tables for
		 * the solutions, we need to query other other two (
		 * deprecated ones ) here as well.
		 */
		if ( in_array( SolutionType::GENERIC, $types ) ) {
			throw new Exception( 'SolutionType::GENERIC can not be fetched. Functionality is not implemented.' );
		}

		if ( in_array( SolutionType::LANG_SPECIFIC, $types ) ) {
			throw new Exception( 'SolutionType::LANG_SPECIFIC can not be fetched. Functionality is not implemented.' );
		}

		return $rows;
	}

}