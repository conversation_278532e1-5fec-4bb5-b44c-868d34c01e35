<?php

namespace VT\Package;

interface ParameterReaderInterface {

	/**
	 * Since the package has a one to one relationship with the
	 * version rule, hence we use the version rule id to track all
	 * parameters for the package.
	 *
	 * @param Integer $versionRuleId
	 *
	 * @param DB $db The database connection to use. For the Vuln
	 * Track, the db connection uses the mysql user for administrating
	 * the vuln track.
	 *
	 * @param boolean $useConfidential Should be set to true if we use
	 * the vuln_track_confidential table.
	 */
	public function __construct( $versionRuleId, \DB $db );

	/**
	 * Function for setting the confidential database
	 *
	 * @param Db $db
	 */
	public function setConfidentialDb( \Db $db );

	/**
	 * Function for reading the package parameters
	 *
	 * @param Array $conditions
	 *
	 * @param Boolean $readConfidential
	 *   If true, data will be read from the confidential tables.
	 */
	public function read( Array $conditions = array(), $readConfidential );
}
