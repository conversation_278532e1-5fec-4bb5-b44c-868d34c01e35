<?php

namespace VT\Package;

class ParameterWriter implements ParameterWriterInterface {

	/**
	 * Ideally we should use the packageId to track the Parameters
	 * for the package but the schema has the version rule and the
	 * package coupled together. Hence, we use the version rule id
	 * to track all Solutions.
	 */
	private $versionRuleId;

	/**
	 * Db connection that needs to be set at intantiation
	 */
	private $db;

	/**
	 * Db connection that is required for updating the data. This
	 * connection isn't required to read the values and therefore
	 * does not need to be set at instantiation. To set this,
	 * setConfidentialDb() function should be used
	 */
	private $dbConf;

	/**
	 * Since the package has a one to one relateionship with the
	 * version rule, hence we use the version rule id to track all
	 * parameters for the package.
	 *
	 * @param Integer $versionRuleId
	 *
	 * @param DB $db The database connection to use. For the Vuln
	 * Track, the db connection uses the mysql user for administrating
	 * the vuln track.
	 *
	 * @param boolean $useConfidential Should be set to true if we use
	 * the vuln_track_confidential table.
	 */
	public function __construct( $versionRuleId, \DB $db, \DB $dbConf ) {

		/*
		 * Validate inputs
		 */
		if ( !$versionRuleId || !is_numeric( $versionRuleId ) ) {
			throw new \Exception( 'Invalid version rule Id.' );
		}


		$this->versionRuleId = $versionRuleId;
		$this->db = $db;
		$this->dbConf = $dbConf;
	}

	/**
	 * In order to save the values, the confidential database must
	 * be set. After saving it is still not possible to read the
	 * values. To read, use the approve() method
	 *
	 * @param Array $parameters
	 *   @todo: write about the format
	 *
	 */
	public function save( Array $parameters ) {
		/*
		 * Deleting the parameters and options
		 *
		 * We do it in two steps so that we clean the database
		 * properly in case there is a database inconsistency
		 */
		$query = "DELETE opt
					FROM vuln_track_confidential.version_rule_parameter_group_map map
					JOIN vuln_track_confidential.parameter opt
					USING (parameter_group_id)
					WHERE map.version_rule_id = :version_rule_id";
		$this->dbConf->execRaw( $query, array( ':version_rule_id' => $this->versionRuleId ) );

		$query = "DELETE map, param
					FROM vuln_track_confidential.version_rule_parameter_group_map map
					JOIN vuln_track_confidential.parameter_group param
					USING (parameter_group_id)
					WHERE map.version_rule_id = :version_rule_id";
		$this->dbConf->execRaw( $query, array( ':version_rule_id' => $this->versionRuleId ) );

		foreach ( $parameters as $parameter ) {
			$parameterId = $this->dbConf->insert()
				->into( 'parameter_group' )
				->set( array( 'type' => $parameter['type']
							  ,'name' => $parameter['name']
							  ,'valid_for' => $parameter['valid_for']
							  ) )
				->exec();

			$this->dbConf->insert()
				->into( 'version_rule_parameter_group_map' )
				->set( array( 'version_rule_id' => $this->versionRuleId
							  ,'parameter_group_id' => $parameterId ) )
				->exec();

			foreach( $parameter['parameter'] as $parameterOption ) {
				$this->dbConf->insert()
					->into( 'parameter' )
					->set( array( 'parameter_group_id' => $parameterId
								  ,'description' => $parameterOption['description']
								  ,'value' => $parameterOption['value']
								  ,'warning' => $parameterOption['warning']
								  ) )
					->exec();
			}
		}

	}

	/**
	 * Function to approve the saved changes.
	 *
	 * This moves the data from the confidential database to the live database.
	 */
	public function approve() {
		/*
		 * Deleting the parameters and options
		 *
		 * We do it in two steps so that we clean the database
		 * properly in case there is a database inconsistency
		 */
		$query = "DELETE opt
					FROM vuln_track.version_rule_parameter_group_map map
					JOIN vuln_track.parameter opt
					USING (parameter_group_id)
					WHERE map.version_rule_id = :version_rule_id";
		$this->db->execRaw( $query, array( ':version_rule_id' => $this->versionRuleId ) );

		$query = "DELETE map, param
					FROM vuln_track.version_rule_parameter_group_map map
					JOIN vuln_track.parameter_group param
					USING (parameter_group_id)
					WHERE map.version_rule_id = :version_rule_id";
		$this->db->execRaw( $query, array( ':version_rule_id' => $this->versionRuleId ) );


		/*
		 * Get all parameters from the confidential table
		 */
		$parameterConfidential = $this->dbConf->select()
			->columns( array( 'parameter_group_id', 'type', 'name', 'valid_for' ) )
			->from( 'parameter_group' )
			->join( 'version_rule_parameter_group_map', 'map' )
			->using( array( 'parameter_group_id' ) )
			->where( array( 'map.version_rule_id' => $this->versionRuleId ) )
			->exec();

		/*
		 * INSERT into the parameter table
		 */
		foreach ( $parameterConfidential as $parameter) {
			$pid = $this->db->insert()
				->into( 'parameter_group' )
				->set( array(
							 'type' => $parameter['type']
							 , 'name' => $parameter['name']
							 , 'valid_for' => $parameter['valid_for']
							 ) )
				->exec();
			$this->db->insert()
				->into( 'version_rule_parameter_group_map' )
				->set( array(
							 'version_rule_id' => $this->versionRuleId
							 , 'parameter_group_id' => $pid
							 ) )
				->exec();
			$parameterOptionConfidential = $this->dbConf->select()
				->columns( array( 'description', 'value', 'warning' ) )
				->from( 'parameter' )
				->where( array( 'parameter_group_id' => $parameter['parameter_group_id'] ) )
                ->orderBy( array( "parameter_id" => "ASC" ) )
				->exec();
			foreach ( $parameterOptionConfidential as $option ) {
				$this->db->insert()
					->into( 'parameter' )
					->set( array(
								 'parameter_group_id' => $pid
								 , 'description' => $option['description']
								 , 'value' => $option['value']
								 , 'warning' => $option['warning']
								 ) )
					->exec();
			}
		}

		/*
		 * Deleting the parameters and options from the
		 * confidential tables
		 *
		 * We do it in two steps so that we clean the database
		 * properly in case there is a database inconsistency
		 */
		$query = "DELETE opt
					FROM vuln_track_confidential.version_rule_parameter_group_map map
					JOIN vuln_track_confidential.parameter opt
					USING (parameter_group_id)
					WHERE map.version_rule_id = :version_rule_id";
		$this->dbConf->execRaw( $query, array( ':version_rule_id' => $this->versionRuleId ) );

		$query = "DELETE map, param
					FROM vuln_track_confidential.version_rule_parameter_group_map map
					JOIN vuln_track_confidential.parameter_group param
					USING (parameter_group_id)
					WHERE map.version_rule_id = :version_rule_id";
		$this->dbConf->execRaw( $query, array( ':version_rule_id' => $this->versionRuleId ) );

	}


}