<?php

namespace VT\Package;

interface SolutionWriterInterface {

	/**
	 * Since the package has a one to one relationship with the
	 * version rule, hence we use the version rule id to track all
	 * solutions for the package.
	 *
	 * @param Integer $versionRuleId
	 *
	 * @param DB $db The database connection to use. For the Vuln
	 * Track, the db connection uses the mysql user for administrating
	 * the vuln track.
	 *
	 * @param DB $dbConf The confidential database connection to
	 * use.
	 *
	 */
	public function __construct( $versionRuleId, \DB $db, \Db $dbConf );


	/**
	 * In order to save the values, the confidential database must
	 * be set. After saving it is still not possible to read the
	 * values. To read, use the approve() method
	 *
	 * @param Array $solutions
	 *   @todo: write about the format
	 *
	 * @todo: Currently, only the special packages are being
	 * saved. This is because the there are three separate tables
	 * for the solutions and we can't change that because a few
	 * applications are relying on the schema by querying the
	 * tables directly.
	 */
	public function save( Array $solutions );

	/**
	 * Function to approve the saved changes.
	 *
	 * This makes the saved data readable (by moving it from the
	 * confidential database to the live database)
	 */
	public function approve();

}
