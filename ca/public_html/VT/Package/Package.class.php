<?php

namespace VT\Package {

	/**
	 * Class for fetching package data.
	 *
	 * @requires SolutionReader.class.php
	 * @requires ParameterReader.class.php
	 */

class Package {

	/**
	 * Db connection that needs to be set at intantiation
	 */
	private $db;

	/**
	 * Db connection that is required for updating the data. This
	 * connection isn't required to read the values and therefore
	 * does not need to be set at instantiation. To set this,
	 * setConfidentialDb() function should be used
	 */
	private $dbConf;

	/**
	 * @param Boolean $readConfidential
	 *
	 * If true, data will be read from the confidential tables (which
	 * should ideally reside in the vuln_track_confidential database,
	 * however in most cases it is a table with the same name but
	 * postfixed with _confidential
	 * e.g. sr_product_secure_confidential).
	 */
	private $readConfidential = false;

	/**
	 * @param DB $db The database connection to use. For the Vuln
	 * Track, the db connection uses the mysql user for administrating
	 * the vuln track.
	 *
	 * @param boolean $useConfidential Should be set to true if we use
	 * the vuln_track_confidential table.
	 */
	public function __construct( \DB $db, $readConfidential = false ) {
		$this->db = $db;
		$this->readConfidential = $readConfidential ? true : false;
	}

	/**
	 * Function for setting the confidential database
	 *
	 * @param Db $db
	 */
	public function setConfidentialDb( \Db $db ) {
		$this->dbConf = $db;
	}

	/**
	 * Function for fetching the sps params from the database. It
	 * would be ideal to have a generic way of handling all parameters
	 * but for the sake of simplicity right now, we are handling the
	 * sps params separately.
	 *
	 * @param Integer $versionRuleId
	 */
	public function getSpsParams( $versionRuleId ) {
		$params = new ParameterReader( $versionRuleId, $this->db );
		$params->setConfidentialDb( $this->dbConf );
		return $params->read( array(), $this->readConfidential );
	}

	/**
	 * Function for fetching solutions from the database.
	 *
	 * @todo: Ideally all solutions would be handled in a more generic
	 * way, but currently it is being used for special packages only.
	 *
	 * @param Integer $versionRuleId
	 *
	 * @return Array
	 */
	public function getSolutions( $versionRuleId, Array $conditions = array() ) {
		$solution = new SolutionReader( $versionRuleId, $this->db );
		$solution->setConfidentialDb( $this->dbConf );
		/*
		 * @todo: we should filter the $conditions so that it only
		 * contains the conditions for the solution. Nevertheless, the
		 * Solution class whitelists the $conditions
		 */
		return $solution->read( $conditions, $this->readConfidential );
	}

}
  }