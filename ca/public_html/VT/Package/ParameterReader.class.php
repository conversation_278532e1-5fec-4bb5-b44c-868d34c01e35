<?php

namespace VT\Package;

class ParameterReader implements ParameterReaderInterface {

	/**
	 * Ideally we should use the packageId to track the Parameters
	 * for the package but the schema has the version rule and the
	 * package coupled together. Hence, we use the version rule id
	 * to track all Solutions.
	 */
	private $versionRuleId;

	/**
	 * Db connection that needs to be set at intantiation
	 */
	private $db;

	/**
	 * Db connection that is required for updating the data. This
	 * connection isn't required to read the values and therefore
	 * does not need to be set at instantiation. To set this,
	 * setConfidentialDb() function should be used
	 */
	private $dbConf;

	/**
	 * Since the package has a one to one relateionship with the
	 * version rule, hence we use the version rule id to track all
	 * parameters for the package.
	 *
	 * @param Integer $versionRuleId
	 *
	 * @param DB $db The database connection to use. For the Vuln
	 * Track, the db connection uses the mysql user for administrating
	 * the vuln track.
	 *
	 * @param boolean $useConfidential Should be set to true if we use
	 * the vuln_track_confidential table.
	 */
	public function __construct( $versionRuleId, \DB $db ) {

		/*
		 * Validate inputs
		 */
		if ( !$versionRuleId || !is_numeric( $versionRuleId ) ) {
			throw new \Exception( 'Invalid version rule Id.' );
		}


		$this->versionRuleId = $versionRuleId;
		$this->db = $db;
	}

	/**
	 * Function for setting the confidential database
	 *
	 * @param Db $db
	 */
	public function setConfidentialDb( \Db $db ) {
		$this->dbConf = $db;
	}

	/**
	 * Function for reading the pacakge parameters.
	 */
	public function read( Array $conditions = array(), $readConfidential = false ) {

		$db = null;

		if ( $readConfidential ) {
			if ( empty( $this->dbConf ) ) {
				throw new \Exception( 'Confidential DB connection is not set.' );
			}
			$db = $this->dbConf;
		} else {
			$db = $this->db;
		}

		$rows = $db->select()
			->columns( array( 'parameter_group_id'
							  ,'type'
							  ,'name'
							  ,'valid_for'
							  ,'description'
							  ,'value'
							  ,'warning'
							  ) )
			->from( 'parameter_group' )
			->join( 'version_rule_parameter_group_map', 'map' )
			->using( array( 'parameter_group_id' ) )
			->join( 'parameter' )
			->using( array( 'parameter_group_id' ) )
			->where( array( 'map.version_rule_id' => $this->versionRuleId ) )
			->orderBy( array( 'parameter_id' => 'ASC' ) )
			->exec();

		// index the paramters
		$parameters = array();
		foreach( $rows as $row ) {
			$pid = $row['parameter_group_id'];
			if ( !isset($parameters[$pid]['parameter_group_id']) ) {
				$parameters[$pid]['parameter_group_id'] = $pid;
				$parameters[$pid]['type'] = $row['type'];
				$parameters[$pid]['name'] = $row['name'];
				$parameters[$pid]['valid_for'] = $row['valid_for'];
			}
			$option = array();
			$option['description'] = $row['description'];
			$option['value'] = $row['value'];
			$option['warning'] = $row['warning'];
			$parameters[$pid]['options'][] = $option;
		}

		return $parameters;
	}


}