<?
// Require functions
require("../functions.php");

// Open database connection
OpenDatabase();

// Output HTML header
echo HTMLHeader();

// Need connection to crm database
mysql_close();
OpenDatabase('virus');

// Save and refresh data?
if ( strlen($_GET['parsed_ids']) > 3 )
{
	// Update OR New?
	if ( $_GET['id'] > 0 )
	{
		mysql_query("update secunia_grouping set name = '" . $_GET['name'] . "', critical = '" . $_GET['critical'] . "', first = '" . $_GET['first'] . "', cves = '" . $_GET['cves'] . "', parsed_ids = '" . $_GET['parsed_ids'] . "', secunia_note = '" . $_GET['secunia_note'] . "' where id = '" . $_GET['id'] . "' limit 1");
		$status = 1;
	}
	else
	{
		mysql_query("insert into secunia_grouping (name, critical, first, cves, parsed_ids, secunia_note) values('" . $_GET['name'] . "', '" . $_GET['critical'] . "', '" . $_GET['first'] . "', '" . $_GET['cves'] . "', '" . $_GET['parsed_ids'] . "', '" . $_GET['secunia_note'] . "')");
		$_GET['id'] = mysql_insert_id();
		$status = 1;
	}

	// Refreshing the grouping
	if ( $_GET['id'] > 0 )
	{
		// Select data
		$res = mysql_query("select * from secunia_grouping where id = '" . $_GET['id'] . "' limit 1");
		$drow = mysql_fetch_array($res);

		// Make us an array of those to be grouped
		$parsed_ids = explode(',', $drow['parsed_ids']);

		// Build 'grouped' array
		while ( list($key, $parsed_id) = each($parsed_ids) )
		{
			// Trim it - no blank spaces
			$parsed_id = trim($parsed_id);

			// Select parsed data
			$pres = mysql_query("select * from parsed where parsed_id = '" . mysql_escape_string($parsed_id) . "' limit 1");
			$prow = mysql_fetch_array($pres);

			$grouped[$prow['virus_id']] = $prow['virus_id'];
		}

		// Clean up db - remove all groupings with these parsed ids
		reset($grouped);
		while ( list($virus_id) = each($grouped) )
		{
			// Delete all virus references
			mysql_query("delete from grouped_ref_virus where virus_id = '" . mysql_escape_string($virus_id) . "'");

			// Set the virus_id as done in our todo list
			mysql_query("update parsed set grouped = 1 where virus_id = '" . mysql_escape_string($virus_id) . "'");
		}

		// Build grouping
		reset($parsed_ids);
		while ( list($key, $parsed_id) = each($parsed_ids) )
		{
			// Trim it - no blank spaces
			$parsed_id = trim($parsed_id);

			// Select parsed data
			$pres = mysql_query("select * from parsed where parsed_id = '" . mysql_escape_string($parsed_id) . "' limit 1");
			$prow = mysql_fetch_array($pres);

			// Gather alias data
			$res = mysql_query("select * from alias where virus_id = '" . mysql_escape_string($prow['virus_id']) . "'");
			while ( $row = mysql_fetch_array($res) )
			{
				// Load alias into data array
				$alias[$row['alias']] = 1;
			}

			// Gather cve data
			$res = mysql_query("select * from cve where virus_id = '" . mysql_escape_string($prow['virus_id']) . "'");
			while ( $row = mysql_fetch_array($res) )
			{
				// Load alias into data array
				$cve[$row['cve']] = 1;
			}

			// Overwrite CVE?
			if ( strlen($drow['cves']) > 3 )
			{
				// Reset
				$cve = '';

				// Explode
				$cves = explode(',', $drow['cves']);
				while ( list($key, $cve_number) = each($cves) )
				{
					$cve[$cve_number] = 1;
				}
			}

			// Select parsed data
			$res = mysql_query("select * from parsed where virus_id = '" . mysql_escape_string($prow['virus_id']) . "' limit 1");
			$row = mysql_fetch_array($res);

			// Load name into alias list
			$alias[$row['name']] = 1;

			// Find initial datetime
			if ( strtotime($row['discovered']) < strtotime($data[1]) || !$data[1] ) 
			{
				$data[1] = ( $drow['first'] != '0000-00-00 00:00:00' ? $drow['first'] : $row['discovered'] );
				$data[0] = ( $drow['name'] ? $drow['name'] : $row['name'] );
			}

			// Find last updated datetime
			if ( (strtotime($row['updated']) > strtotime($data[2]) || !$data[2]) && $row['updated'] ) 
			{
				$data[2] = $row['updated'];
			}

			if ( (strtotime($row['discovered']) > strtotime($data[2]) || !$data[2]) && $row['discovered'] ) 
			{
				$data[2] = $row['discovered'];
			}

			// Calc criticality
			if ( $row['severity'] != 'N/A' )
			{
				// Overwrite feature if symantec has set it to a "3/5" risk - then this will be forced a medium alert!
				if ( ($row['vendor_id'] == 'sarc' && $row['severity'] == '3/5') || ($row['vendor_id'] == 'trendmicro' && $row['severity'] == '2/3') )
				{
					$ow_crit = 55;
				}
				elseif ( $row['vendor_id'] == 'sarc' && $row['severity'] == '4/5' )
				{
					$ow_crit = 82;
				}

				$criticality[0] += ( substr($row['severity'], 0, strpos($row['severity'], '/')) / substr($row['severity'], strpos($row['severity'], '/')+1, 3) * 100 );
				$criticality[1]++;
			}

			// Calculated criticality
			$crit = ( $criticality[1] > 1 ? round($criticality[0] / $criticality[1]) : '' );
			$data[3] = ( $ow_crit > $crit ? $ow_crit : $crit );
			if ( $drow['critical'] > 0 )
			{
				$data[3] = $drow['critical'];
			}
		}

		// Write data
		{
			// Make Group
			mysql_query("insert into grouped (name, critical, initial, last, alert_check, alert_check_ca) values('" . mysql_escape_string(CleanName($data[0])) . "', '" . $data[3] . "', '" . mysql_escape_string($data[1]) . "', '" . mysql_escape_string($data[2]) . "', 1, 1)");
			$group_id = mysql_insert_id();

			// Write all virus_id's
			reset($grouped);
			while ( list($virus_id) = @each($grouped) )
			{
				mysql_query("insert into grouped_ref_virus (group_id, virus_id) values('" . $group_id . "', '" . mysql_escape_string($virus_id) . "')");
			}

			// Write all Aliases
			reset($alias);
			while ( list($alias_name, $value) = @each($alias) )
			{
				mysql_query("insert into grouped_ref_alias (group_id, alias) values('" . $group_id . "', '" . mysql_escape_string($alias_name) . "')");
			}

			// Write all CVE
			while ( list($cve_name, $value) = @each($cve) )
			{
				mysql_query("insert into grouped_ref_cve (group_id, cve) values('" . $group_id . "', '" . mysql_escape_string($cve_name) . "')");
			}
		}
	}
}

// Select data - if any
if ( $_GET['id'] )
{
	$res = mysql_query("select * from secunia_grouping where id = '" . $_GET['id'] . "' limit 1");
	$row = mysql_fetch_array($res);
}
?>

<table width="100%" cellpadding="0" cellspacing="0">
<form method="GET" action="grouping.php">
<input type="hidden" name="id" value="<?=intval($_GET['id'])?>">
	<tr>
		<td width="100%" colspan="2" bgcolor="black">
			<font color="white"><b>New/Update Secunia Grouping and Note</b></font>
		</td>
	</tr>
	<tr><td><br></td></tr>

	<tr><td width="100%" colspan="2"><a href="list_groupings.php">[List Groupings]</a> <a href="grouping.php">[New Grouping]</a></td></tr>
	<tr><td><br></td></tr>

	<?
	if ( $status )
	{
		?>
		<tr><td><br></td></tr>
		<tr>
			<td width="15%"><b>Status:</b></td>
			<td width="85%"><b>Saved Data</b></td>
		</tr>
		<?
	}
	?>
	<tr><td><br></td></tr>
	<tr>
		<td width="15%"><b>Group Name</b></td>
		<td width="85%"><input type="text" name="name" value="<?=safehtmlentities($row['name'])?>" style="width: 100%"></td>
	</tr>
	<tr><td><br></td></tr>
	<tr>
		<td width="15%"><b>Critical</b></td>
		<td width="85%"><input type="text" name="critical" value="<?=safehtmlentities($row['critical'])?>" style="width: 100%"></td>
	</tr>
	<tr><td><br></td></tr>
	<tr>
		<td width="15%"><b>First Reported</b></td>
		<td width="85%"><input type="text" name="first" value="<?=safehtmlentities($row['first'])?>" style="width: 100%"></td>
	</tr>
	<tr><td><br></td></tr>
	<tr>
		<td width="15%" valign="top"><b>Secunia Note</b></td>
		<td width="85%"><textarea rows="10" name="secunia_note" style="width: 100%"><?=safehtmlentities($row['secunia_note'])?></textarea></td>
	</tr>
	<tr><td><br></td></tr>
	<tr>
		<td width="15%"><b>CVE References</b></td>
		<td width="85%"><input type="text" name="cves" value="<?=safehtmlentities($row['cves'])?>" style="width: 100%"></td>
	</tr>
	<tr><td><br></td></tr>
	<tr>
		<td width="15%"><b>Parsed IDs</b></td>
		<td width="85%"><input type="text" name="parsed_ids" value="<?=safehtmlentities($row['parsed_ids'])?>" style="width: 100%"></td>
	</tr>
	<tr><td><br></td></tr>
	<tr>
		<td width="15%"></td>
		<td width="85%"><input type="submit" value="Save Data"></b></td>
	</tr>
</table>
</form>

<?
// Need connection to vuln_track database
mysql_close();
OpenDatabase();

// Output footer
echo HTMLFooter();


// Function for "Cleaning" a virus name
function CleanName($name)
{
	// remove: '@mm'
	$name = preg_replace('/@mm$/i', '', $name);

	// remove: '@mm'
	$name = preg_replace('/@m$/i', '', $name);

	// remove: 'w32.'
	$name = preg_replace('/^w32(\.|\/)/i', '', $name);

	// remove: 'worm_'
	$name = preg_replace('/^worm_/i', '', $name);

	// remove: 'pe_'
	$name = preg_replace('/^pe_/i', '', $name);

	// remove: 'troj/'
	$name = preg_replace('/^troj\//i', '', $name);

	// remove: 'troj/'
	$name = preg_replace('/\.worm$/i', '', $name);

	// remove: 'troj/'
	$name = preg_replace('/^backdoor\./i', '', $name);

	// remove: 'x97m'
	#$name = preg_replace('/^x97m\./i', '', $name);

	return $name;
}
?>