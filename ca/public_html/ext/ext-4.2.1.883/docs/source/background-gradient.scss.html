<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">@function linear-gradient-bevel($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        lighten($bg-color, 15%),
        lighten($bg-color, 8%) 30%,
        $bg-color 65%,
        darken($bg-color, 6%)
    ));
}

@function linear-gradient-glossy($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        lighten($bg-color, 15%),
        lighten($bg-color, 5%) 50%,
        $bg-color 51%,
        darken($bg-color, 5%)
    ));
}

@function linear-gradient-recessed($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        darken($bg-color, 10%),
        darken($bg-color, 5%) 10%,
        $bg-color 65%,
        lighten($bg-color, .5%)
    ));
}

@function linear-gradient-matte($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        lighten($bg-color, 3%),
        darken($bg-color, 4%)
    ));
}

@function linear-gradient-matte-reverse($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        darken($bg-color, 6%),
        lighten($bg-color, 4%)
    ));
}

@function linear-gradient-panel-header($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        adjust-color($bg-color, $hue: -0.857deg, $saturation: -1.63%, $lightness: 3.529%),
        adjust-color($bg-color, $hue: 0.158deg, $saturation: -1.21%, $lightness: 0.392%) 45%,
        adjust-color($bg-color, $hue: 1.154deg, $saturation: 0.607%, $lightness: -7.647%) 46%,
        adjust-color($bg-color, $hue: 1.154deg, $saturation: 0.607%, $lightness: -7.647%) 50%,
        adjust-color($bg-color, $hue: 1.444deg, $saturation: -1.136%, $lightness: -4.706%) 51%,
        $bg-color
    ));
}

@function linear-gradient-tabbar($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        adjust-color($bg-color, $hue: 0.0deg, $saturation: 1.604%, $lightness: 4.706%),
        $bg-color
    ));
}

@function linear-gradient-tab($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        adjust-color($bg-color, $hue: 1.382deg, $saturation: -18.571%, $lightness: -4.902%),
        adjust-color($bg-color, $hue: 0.43deg, $saturation: -10.311%, $lightness: -2.157%) 25%,
        $bg-color 45%
    ));
}

@function linear-gradient-tab-active($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        adjust-color($bg-color, $hue: -212.903deg, $saturation: -88.571%, $lightness: 6.863%),
        adjust-color($bg-color, $hue: 0.43deg, $saturation: -6.753%, $lightness: 4.706%) 25%,
        $bg-color 45%
    ));
}

@function linear-gradient-tab-over($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        adjust-color($bg-color, $hue: 4.462deg, $saturation: -9.524%, $lightness: -3.725%),
        adjust-color($bg-color, $hue: 2.272deg, $saturation: 0.0%, $lightness: -1.569%) 25%,
        $bg-color 45%
    ));
}

@function linear-gradient-tab-disabled($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        $bg-color,
        adjust-color($bg-color, $hue: -0.267deg, $saturation: 18.571%, $lightness: 2.941%)
    ));
}

@function linear-gradient-grid-header($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        adjust-color($bg-color, $hue: 0deg, $saturation: 0%, $lightness: 20.392%),
        adjust-color($bg-color, $hue: 220.0deg, $saturation: 5.66%, $lightness: 12.353%)
    ));
}

@function linear-gradient-grid-header-over($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        adjust-color($bg-color, $hue: 0.175deg, $saturation: 0.967%, $lightness: 14.118%),
        adjust-color($bg-color, $hue: 0.175deg, $saturation: 0.967%, $lightness: 14.118%) 39%,
        adjust-color($bg-color, $hue: 0.372deg, $saturation: 0.101%, $lightness: 10.196%) 40%,
        adjust-color($bg-color, $hue: 0.372deg, $saturation: 0.101%, $lightness: 10.196%)
    ));
}

@function linear-gradient-grid-row-over($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        adjust-color($bg-color, $hue: 0.175deg, $saturation: 0.967%, $lightness: 14.118%),
        $bg-color
    ));
}

@function linear-gradient-grid-cell-special($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        $bg-color,
        darken($bg-color, 5)
    ));
}

@function linear-gradient-glossy-button($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        $bg-color,
        adjust-color($bg-color, $hue: 0deg, $saturation: 0%, $lightness: -2.353%) 48%,
        adjust-color($bg-color, $hue: 0deg, $saturation: 0%, $lightness: -11.373%) 52%,
        adjust-color($bg-color, $hue: 0deg, $saturation: 0%, $lightness: -9.412%)
    ));
}

@function linear-gradient-glossy-button-over($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        $bg-color,
        adjust-color($bg-color, $hue: 1.754deg, $saturation: 0.0%, $lightness: -2.157%) 48%,
        adjust-color($bg-color, $hue: 5.833deg, $saturation: -35.135%, $lightness: -9.216%) 52%,
        adjust-color($bg-color, $hue: 5.833deg, $saturation: -27.273%, $lightness: -7.647%)
    ));
}

@function linear-gradient-glossy-button-pressed($direction, $bg-color) {
    @return linear-gradient($direction, color_stops(
        $bg-color,
        adjust-color($bg-color, $hue: -1.839deg, $saturation: -2.18%, $lightness: 2.157%) 48%,
        adjust-color($bg-color, $hue: -2.032deg, $saturation: 37.871%, $lightness: -4.706%) 52%,
        adjust-color($bg-color, $hue: -1.641deg, $saturation: 36.301%, $lightness: -2.549%)
    ));
}

<span id='Global_CSS-css_mixin-background-gradient'>/**
</span> * Creates a background gradient.
 *
 * Example usage:
 *     .foo {
 *          @include background-gradient(#808080, matte, left);
 *     }
 *
 * @param {Color} $bg-color The background color of the gradient
 * @param {String/List} [$type=$base-gradient] The type of gradient to be used. Can either
 * be a String which is a predefined gradient name, or it can can be a list of color stops.
 * If null is passed, this mixin will still set the `background-color` to $bg-color.
 * The available predefined gradient names are:
 *
 * * bevel
 * * glossy
 * * recessed
 * * matte
 * * matte-reverse
 * * panel-header
 * * tabbar
 * * tab
 * * tab-active
 * * tab-over
 * * tab-disabled
 * * grid-header
 * * grid-header-over
 * * grid-row-over
 * * grid-cell-special
 * * glossy-button
 * * glossy-button-over
 * * glossy-button-pressed
 *
 * Each of these gradient names corresponds to a function named linear-gradient[name].
 * Themes can override these functions to customize the color stops that they return.
 * For example, to override the glossy-button gradient function add a function named
 * &quot;linear-gradient-glossy-button&quot; to a file named &quot;sass/etc/mixins/background-gradient.scss&quot;
 * in your theme.  The function should return the result of calling the Compass linear-gradient
 * function with the desired direction and color-stop information for the gradient.  For example:
 *
 *     @function linear-gradient-glossy-button($direction, $bg-color) {
 *         @return linear-gradient($direction, color_stops(
 *             mix(#fff, $bg-color, 10%),
 *             $bg-color 50%,
 *             mix(#000, $bg-color, 5%) 51%,
 *             $bg-color
 *         ));
 *     }
 *
 * @param {String} [$direction=top] The direction of the gradient. Can either be
 * `top` or `left`.
 *
 * @member Global_CSS
 */
@mixin background-gradient($bg-color, $type: $base-gradient, $direction: top) {
    background-image: none;
    background-color: $bg-color;

    @if $base-gradient != null and $bg-color != transparent {
        $gradient: null;

        //color_stops
        @if type-of($type) == &quot;list&quot; {
            $gradient: linear-gradient($direction, $type);
        }

        //default gradients
        @else if $type == bevel {
            $gradient: linear-gradient-bevel($direction, $bg-color);
        } @else if $type == glossy {
            $gradient: linear-gradient-glossy($direction, $bg-color);
        } @else if $type == recessed {
            $gradient: linear-gradient-recessed($direction, $bg-color);
        } @else if $type == matte {
            $gradient: linear-gradient-matte($direction, $bg-color);
        } @else if $type == matte-reverse {
            $gradient: linear-gradient-matte-reverse($direction, $bg-color);
        }

        // gradients
        @else if $type == panel-header {
            $gradient: linear-gradient-panel-header($direction, $bg-color);
        } @else if $type == tabbar {
            $gradient: linear-gradient-tabbar($direction, $bg-color);
        } @else if $type == tab {
            $gradient: linear-gradient-tab($direction, $bg-color);
        } @else if $type == tab-active {
            $gradient: linear-gradient-tab-active($direction, $bg-color);
        } @else if $type == tab-over {
            $gradient: linear-gradient-tab-over($direction, $bg-color);
        } @else if $type == tab-disabled {
            $gradient: linear-gradient-tab-disabled($direction, $bg-color);
        } @else if $type == grid-header {
            $gradient: linear-gradient-grid-header($direction, $bg-color);
        } @else if $type == grid-header-over {
            $gradient: linear-gradient-grid-header-over($direction, $bg-color);
        } @else if $type == grid-row-over {
            $gradient: linear-gradient-grid-row-over($direction, $bg-color);
        } @else if $type == grid-cell-special {
            $gradient: linear-gradient-grid-cell-special($direction, $bg-color);
        } @else if $type == glossy-button or $type == glossy-button-disabled {
            $gradient: linear-gradient-glossy-button($direction, $bg-color);
        } @else if $type == glossy-button-over {
            $gradient: linear-gradient-glossy-button-over($direction, $bg-color);
        } @else if $type == glossy-button-pressed {
            $gradient: linear-gradient-glossy-button-pressed($direction, $bg-color);
        }

        @if $gradient != null {
            @include background-image($gradient);
        }
    }
}</pre>
</body>
</html>
