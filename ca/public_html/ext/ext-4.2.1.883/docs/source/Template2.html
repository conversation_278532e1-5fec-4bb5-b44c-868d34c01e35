<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-grid-column-Template'>/**
</span> * A Column definition class which renders a value by processing a {@link Ext.data.Model Model}'s
 * {@link Ext.data.Model#persistenceProperty data} using a {@link #tpl configured}
 * {@link Ext.XTemplate XTemplate}.
 * 
 *     @example
 *     Ext.create('Ext.data.Store', {
 *         storeId:'employeeStore',
 *         fields:['firstname', 'lastname', 'seniority', 'department'],
 *         groupField: 'department',
 *         data:[
 *             { firstname: &quot;Michael&quot;, lastname: &quot;Scott&quot;,   seniority: 7, department: &quot;Management&quot; },
 *             { firstname: &quot;Dwight&quot;,  lastname: &quot;Schrute&quot;, seniority: 2, department: &quot;Sales&quot; },
 *             { firstname: &quot;Jim&quot;,     lastname: &quot;Halpert&quot;, seniority: 3, department: &quot;Sales&quot; },
 *             { firstname: &quot;Kevin&quot;,   lastname: &quot;Malone&quot;,  seniority: 4, department: &quot;Accounting&quot; },
 *             { firstname: &quot;Angela&quot;,  lastname: &quot;Martin&quot;,  seniority: 5, department: &quot;Accounting&quot; }
 *         ]
 *     });
 *     
 *     Ext.create('Ext.grid.Panel', {
 *         title: 'Column Template Demo',
 *         store: Ext.data.StoreManager.lookup('employeeStore'),
 *         columns: [
 *             { text: 'Full Name',       xtype: 'templatecolumn', tpl: '{firstname} {lastname}', flex:1 },
 *             { text: 'Department (Yrs)', xtype: 'templatecolumn', tpl: '{department} ({seniority})' }
 *         ],
 *         height: 200,
 *         width: 300,
 *         renderTo: Ext.getBody()
 *     });
 */
Ext.define('Ext.grid.column.Template', {
    extend: 'Ext.grid.column.Column',
    alias: ['widget.templatecolumn'],
    requires: ['Ext.XTemplate'],
    alternateClassName: 'Ext.grid.TemplateColumn',

<span id='Ext-grid-column-Template-cfg-tpl'>    /**
</span>     * @cfg {String/Ext.XTemplate} tpl
     * An {@link Ext.XTemplate XTemplate}, or an XTemplate *definition string* to use to process a
     * {@link Ext.data.Model Model}'s {@link Ext.data.Model#persistenceProperty data} to produce a
     * column's rendered value.
     */
    
<span id='Ext-grid-column-Template-cfg-renderer'>    /**
</span>     * @cfg {Object} renderer
     * @hide
     */
    
<span id='Ext-grid-column-Template-cfg-scope'>    /**
</span>     * @cfg {Object} scope
     * @hide
     */

    initComponent: function(){
        var me = this;
        me.tpl = (!Ext.isPrimitive(me.tpl) &amp;&amp; me.tpl.compile) ? me.tpl : new Ext.XTemplate(me.tpl);
        // Set this here since the template may access any record values,
        // so we must always run the update for this column
        me.hasCustomRenderer = true;
        me.callParent(arguments);
    },
    
<span id='Ext-grid-column-Template-method-defaultRenderer'>    defaultRenderer: function(value, meta, record) {
</span>        var data = Ext.apply({}, record.data, record.getAssociatedData());
        return this.tpl.apply(data);
    }
});
</pre>
</body>
</html>
