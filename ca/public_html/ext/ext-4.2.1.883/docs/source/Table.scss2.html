<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">.#{$prefix}grid-row,
.#{$prefix}grid-data-row {
    outline: none; // prevent dotted outline in firefox when focused
}

.#{$prefix}grid-view {
    overflow: hidden;
    position: relative;
}

.#{$prefix}grid-table {
    table-layout: fixed;
    border-collapse: separate;
}

.#{$prefix}grid-td {
    overflow: hidden;
    border-width: 0;
    vertical-align: top;
}

.#{$prefix}grid-cell-inner {
    overflow: hidden;
    white-space: nowrap;
    zoom: 1;
}

.#{$prefix}grid-resize-marker {
    position: absolute;
    z-index: 5;
    top: 0;
}
</pre>
</body>
</html>
