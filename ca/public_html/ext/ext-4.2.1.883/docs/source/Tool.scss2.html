<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-panel-Tool'>/**
</span> * @class Ext.panel.Tool
 */

<span id='Ext-panel-Tool-css_var-S-tool-size'>/**
</span> * @var {number}
 * The size of Tools
 */
$tool-size: 16px !default;

<span id='Ext-panel-Tool-css_var-S-tool-include-hover-icons'>/**
</span> * @var {boolean}
 * True to change the background-position of the Tool on hover. Allows for a separate
 * hover state icon in the sprite.
 */
$tool-include-hover-icons: true !default;

<span id='Ext-panel-Tool-css_var-S-tool-cursor'>/**
</span> * @var {string}
 * The cursor to display when the mouse cursor is over a Tool
 */
$tool-cursor: pointer !default;

<span id='Ext-panel-Tool-css_var-S-tool-opacity'>/**
</span> * @var {number}
 * The opacity of Tools
 */
$tool-opacity: 1 !default;

<span id='Ext-panel-Tool-css_var-S-tool-opacity-over'>/**
</span> * @var {number}
 * The opacity of hovered Tools
 */
$tool-opacity-over: 1 !default;

<span id='Ext-panel-Tool-css_var-S-tool-opacity-pressed'>/**
</span> * @var {number}
 * The opacity of pressed Tools
 */
$tool-opacity-pressed: 1 !default;

<span id='Ext-panel-Tool-css_var-S-tool-background-image'>/**
</span> * @var {string}
 * The sprite to use as the background-image for Tools
 */
$tool-background-image: 'tools/tool-sprites' !default;</pre>
</body>
</html>
