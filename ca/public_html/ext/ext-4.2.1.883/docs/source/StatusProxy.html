<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-dd-StatusProxy'>/**
</span> * A specialized floating Component that supports a drop status icon, {@link Ext.Layer} styles
 * and auto-repair.  This is the default drag proxy used by all Ext.dd components.
 */
Ext.define('Ext.dd.StatusProxy', {
    extend: 'Ext.Component',
<span id='Ext-dd-StatusProxy-property-animRepair'>    animRepair: false,
</span>
<span id='Ext-dd-StatusProxy-property-childEls'>    childEls: [
</span>        'ghost'
    ],

<span id='Ext-dd-StatusProxy-cfg-renderTpl'>    renderTpl: [
</span>        '&lt;div class=&quot;' + Ext.baseCSSPrefix + 'dd-drop-icon&quot;&gt;&lt;/div&gt;' +
        '&lt;div id=&quot;{id}-ghost&quot; class=&quot;' + Ext.baseCSSPrefix + 'dd-drag-ghost&quot;&gt;&lt;/div&gt;'
    ],
    
<span id='Ext-dd-StatusProxy-property-repairCls'>    repairCls: Ext.baseCSSPrefix + 'dd-drag-repair',
</span>
<span id='Ext-dd-StatusProxy-method-constructor'>    /**
</span>     * Creates new StatusProxy.
     * @param {Object} [config] Config object.
     */
    constructor: function(config) {
        var me = this;

        config = config || {};

        Ext.apply(me, {
            hideMode: 'visibility',
            hidden: true,
            floating: true,
            id: me.id || Ext.id(),
            cls: Ext.baseCSSPrefix + 'dd-drag-proxy ' + this.dropNotAllowed,
            shadow: config.shadow || false,
            renderTo: Ext.getDetachedBody()
        });
        me.callParent(arguments);
        this.dropStatus = this.dropNotAllowed;
    },

<span id='Ext-dd-StatusProxy-cfg-dropAllowed'>    /**
</span>     * @cfg {String} dropAllowed
     * The CSS class to apply to the status element when drop is allowed.
     */
    dropAllowed : Ext.baseCSSPrefix + 'dd-drop-ok',

<span id='Ext-dd-StatusProxy-cfg-dropNotAllowed'>    /**
</span>     * @cfg {String} dropNotAllowed
     * The CSS class to apply to the status element when drop is not allowed.
     */
    dropNotAllowed : Ext.baseCSSPrefix + 'dd-drop-nodrop',

<span id='Ext-dd-StatusProxy-method-setStatus'>    /**
</span>     * Updates the proxy's visual element to indicate the status of whether or not drop is allowed
     * over the current target element.
     * @param {String} cssClass The css class for the new drop status indicator image
     */
    setStatus : function(cssClass){
        cssClass = cssClass || this.dropNotAllowed;
        if (this.dropStatus != cssClass) {
            this.el.replaceCls(this.dropStatus, cssClass);
            this.dropStatus = cssClass;
        }
    },

<span id='Ext-dd-StatusProxy-method-reset'>    /**
</span>     * Resets the status indicator to the default dropNotAllowed value
     * @param {Boolean} clearGhost True to also remove all content from the ghost, false to preserve it
     */
    reset : function(clearGhost){
        var me = this,
            clsPrefix = Ext.baseCSSPrefix + 'dd-drag-proxy ';

        me.el.replaceCls(clsPrefix + me.dropAllowed, clsPrefix + me.dropNotAllowed);
        me.dropStatus = me.dropNotAllowed;
        if (clearGhost) {
            me.ghost.update('');
        }
    },

<span id='Ext-dd-StatusProxy-method-update'>    /**
</span>     * Updates the contents of the ghost element
     * @param {String/HTMLElement} html The html that will replace the current innerHTML of the ghost element, or a
     * DOM node to append as the child of the ghost element (in which case the innerHTML will be cleared first).
     */
    update : function(html){
        if (typeof html == &quot;string&quot;) {
            this.ghost.update(html);
        } else {
            this.ghost.update(&quot;&quot;);
            html.style.margin = &quot;0&quot;;
            this.ghost.dom.appendChild(html);
        }
        var el = this.ghost.dom.firstChild;
        if (el) {
            Ext.fly(el).setStyle('float', 'none');
        }
    },

<span id='Ext-dd-StatusProxy-method-getGhost'>    /**
</span>     * Returns the ghost element
     * @return {Ext.Element} el
     */
    getGhost : function(){
        return this.ghost;
    },

<span id='Ext-dd-StatusProxy-method-hide'>    /**
</span>     * Hides the proxy
     * @param {Boolean} clear True to reset the status and clear the ghost contents,
     * false to preserve them
     */
    hide : function(clear) {
        this.callParent();
        if (clear) {
            this.reset(true);
        }
    },

<span id='Ext-dd-StatusProxy-method-stop'>    /**
</span>     * Stops the repair animation if it's currently running
     */
    stop : function(){
        if (this.anim &amp;&amp; this.anim.isAnimated &amp;&amp; this.anim.isAnimated()) {
            this.anim.stop();
        }
    },

<span id='Ext-dd-StatusProxy-method-sync'>    /**
</span>     * Force the Layer to sync its shadow and shim positions to the element
     */
    sync : function(){
        this.el.sync();
    },

<span id='Ext-dd-StatusProxy-method-repair'>    /**
</span>     * Causes the proxy to return to its position of origin via an animation.
     * Should be called after an invalid drop operation by the item being dragged.
     * @param {Number[]} xy The XY position of the element ([x, y])
     * @param {Function} callback The function to call after the repair is complete.
     * @param {Object} scope The scope (`this` reference) in which the callback function is executed.
     * Defaults to the browser window.
     */
    repair : function(xy, callback, scope) {
        var me = this;

        me.callback = callback;
        me.scope = scope;
        if (xy &amp;&amp; me.animRepair !== false) {
            me.el.addCls(me.repairCls);
            me.el.hideUnders(true);
            me.anim = me.el.animate({
                duration: me.repairDuration || 500,
                easing: 'ease-out',
                to: {
                    x: xy[0],
                    y: xy[1]
                },
                stopAnimation: true,
                callback: me.afterRepair,
                scope: me
            });
        } else {
            me.afterRepair();
        }
    },

<span id='Ext-dd-StatusProxy-method-afterRepair'>    // private
</span>    afterRepair : function() {
        var me = this;
    
        me.hide(true);
        me.el.removeCls(me.repairCls);
        if (typeof me.callback == &quot;function&quot;) {
            me.callback.call(me.scope || me);
        }
        delete me.callback;
        delete me.scope;
    }
});
</pre>
</body>
</html>
