<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">//
// Definitions of enums referenced in documentation.
//

<span id='Ext-enums-Layout'>/**
</span> * @enum [Ext.enums.Layout=layout.*]
 * Enumeration of all layout types.
 */

<span id='Ext-enums-Widget'>/**
</span> * @enum [Ext.enums.Widget=widget.*]
 * Enumeration of all xtypes.
 */

<span id='Ext-enums-Plugin'>/**
</span> * @enum [Ext.enums.Plugin=plugin.*]
 * Enumeration of all ptypes.
 */

<span id='Ext-enums-Feature'>/**
</span> * @enum [Ext.enums.Feature=feature.*]
 * Enumeration of all ftypes.
 */

</pre>
</body>
</html>
