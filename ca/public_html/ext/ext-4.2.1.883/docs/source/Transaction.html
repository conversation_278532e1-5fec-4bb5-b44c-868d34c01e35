<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-direct-Transaction'>/**
</span> * Supporting Class for Ext.Direct (not intended to be used directly).
 */
Ext.define('Ext.direct.Transaction', {
    alias: 'direct.transaction',
    alternateClassName: 'Ext.Direct.Transaction',
   
    statics: {
<span id='Ext-direct-Transaction-property-TRANSACTION_ID'>        TRANSACTION_ID: 0
</span>    },
    
<span id='Ext-direct-Transaction-cfg-provider'>    /**
</span>     * @cfg {Ext.direct.Provider} provider Provider to use with this Transaction.
     */
   
<span id='Ext-direct-Transaction-method-constructor'>    /**
</span>     * Creates new Transaction.
     * @param {Object} [config] Config object.
     */
    constructor: function(config) {
        var me = this;
        
        Ext.apply(me, config);

        me.id = me.tid = ++me.self.TRANSACTION_ID;
        me.retryCount = 0;
    },
   
<span id='Ext-direct-Transaction-method-send'>    send: function() {
</span>        var me = this;
        
        me.provider.queueTransaction(me);
    },

<span id='Ext-direct-Transaction-method-retry'>    retry: function() {
</span>        var me = this;
        
        me.retryCount++;
        me.send();
    },

<span id='Ext-direct-Transaction-method-getProvider'>    getProvider: function() {
</span>        return this.provider;
    }
});
</pre>
</body>
</html>
