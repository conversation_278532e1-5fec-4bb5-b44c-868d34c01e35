<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">.#{$prefix}form-trigger {
    cursor: pointer;
    overflow: hidden;
    background-repeat: no-repeat;
    .#{$prefix}item-disabled &amp; {
        cursor: default;
    }
}

.#{$prefix}trigger-noedit {
    cursor: default;
}

.#{$prefix}form-trigger-wrap {
    vertical-align: top;
    // The trigger wrap table needs to have border-collapse: separate or else the
    // horizontal alignment of the text will be off by 1px in IE in neptune (because the 
    // field border is on the trigger wrap)
    border-collapse: separate;
}</pre>
</body>
</html>
