<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">@import 'mixins/no-select';
@import 'mixins/slicer';
@import 'mixins/rotate-element';
@import 'mixins/background-gradient';
@import 'mixins/inner-border';
@import 'mixins/frame';
@import 'mixins/theme-background-image';
@import 'mixins/border-management';
</pre>
</body>
</html>
