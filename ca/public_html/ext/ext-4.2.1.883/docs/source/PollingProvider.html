<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-direct-PollingProvider'>/**
</span> * Provides for repetitive polling of the server at distinct {@link #interval intervals}.
 * The initial request for data originates from the client, and then is responded to by the
 * server.
 * 
 * Configuration for the PollingProvider can be generated by the server-side
 * API portion of the Ext.Direct stack.
 *
 * An instance of PollingProvider may be created directly via the new keyword or by simply
 * specifying `type = 'polling'`. For example:
 *
 *      var pollA = new Ext.direct.PollingProvider({
 *          type:'polling',
 *          url: 'php/pollA.php',
 *      });
 *      Ext.direct.Manager.addProvider(pollA);
 *      pollA.disconnect();
 *      
 *      Ext.direct.Manager.addProvider({
 *          type:'polling',
 *          url: 'php/pollB.php',
 *          id: 'pollB-provider'
 *      });
 *      var pollB = Ext.direct.Manager.getProvider('pollB-provider');
 *
 */
Ext.define('Ext.direct.PollingProvider', {
    extend: 'Ext.direct.JsonProvider',
    alias:  'direct.pollingprovider',
    
    requires: [
        'Ext.Ajax',
        'Ext.util.DelayedTask'
    ],
    
    uses: [
        'Ext.direct.ExceptionEvent',
        'Ext.direct.Manager'
    ],
    
<span id='Ext-direct-PollingProvider-cfg-interval'>    /**
</span>     * @cfg {Number} [interval=3000]
     * How often to poll the server-side in milliseconds. Defaults to every 3 seconds.
     */
    interval: 3000,

<span id='Ext-direct-PollingProvider-cfg-baseParams'>    /**
</span>     * @cfg {Object} [baseParams]
     * An object containing properties which are to be sent as parameters on every polling request
     */
    
<span id='Ext-direct-PollingProvider-cfg-url'>    /**
</span>     * @cfg {String/Function} url
     * The url which the PollingProvider should contact with each request. This can also be
     * an imported Ext.Direct method which will accept the baseParams as its only argument.
     */

    constructor: function(config) {
        var me = this;
        
        me.callParent(arguments);
        
        me.addEvents(
<span id='Ext-direct-PollingProvider-event-beforepoll'>            /**
</span>             * @event beforepoll
             * @preventable
             * Fired immediately before a poll takes place.
             *
             * @param {Ext.direct.PollingProvider} this
             */
            'beforepoll',
            
<span id='Ext-direct-PollingProvider-event-poll'>            /**
</span>             * @event poll
             * Fired immediately after a poll takes place.
             *
             * @param {Ext.direct.PollingProvider} this
             */
            'poll'
        );
    },

<span id='Ext-direct-PollingProvider-method-isConnected'>    /**
</span>     * @inheritdoc
     */
    isConnected: function() {
        return !!this.pollTask;
    },

<span id='Ext-direct-PollingProvider-method-connect'>    /**
</span>     * Connect to the server-side and begin the polling process. To handle each
     * response subscribe to the data event.
     */
    connect: function() {
        var me = this,
            url = me.url;
        
        if (url &amp;&amp; !me.pollTask) {
            me.pollTask = Ext.TaskManager.start({
                run: me.runPoll,
                interval: me.interval,
                scope: me
            });
            
            me.fireEvent('connect', me);
        }
        //&lt;debug&gt;
        else if (!url) {
            Ext.Error.raise('Error initializing PollingProvider, no url configured.');
        }
        //&lt;/debug&gt;
    },

<span id='Ext-direct-PollingProvider-method-disconnect'>    /**
</span>     * Disconnect from the server-side and stop the polling process. The disconnect
     * event will be fired on a successful disconnect.
     */
    disconnect: function() {
        var me = this;
        
        if (me.pollTask) {
            Ext.TaskManager.stop(me.pollTask);
            delete me.pollTask;
            me.fireEvent('disconnect', me);
        }
    },
    
<span id='Ext-direct-PollingProvider-method-runPoll'>    /**
</span>     * @private
     */
    runPoll: function() {
        var me = this,
            url = me.url;
        
        if (me.fireEvent('beforepoll', me) !== false) {
            if (Ext.isFunction(url)) {
                url(me.baseParams);
            }
            else {
                Ext.Ajax.request({
                    url: url,
                    callback: me.onData,
                    scope: me,
                    params: me.baseParams
                });
            }
            
            me.fireEvent('poll', me);
        }
    },

<span id='Ext-direct-PollingProvider-method-onData'>    /**
</span>     * @private
     */
    onData: function(opt, success, response) {
        var me = this, 
            i, len, events;
        
        if (success) {
            events = me.createEvents(response);
            
            for (i = 0, len = events.length; i &lt; len; ++i) {
                me.fireEvent('data', me, events[i]);
            }
        }
        else {
            events = new Ext.direct.ExceptionEvent({
                data: null,
                code: Ext.direct.Manager.exceptions.TRANSPORT,
                message: 'Unable to connect to the server.',
                xhr: response
            });
            
            me.fireEvent('data', me, events);
        }
    }
});</pre>
</body>
</html>
