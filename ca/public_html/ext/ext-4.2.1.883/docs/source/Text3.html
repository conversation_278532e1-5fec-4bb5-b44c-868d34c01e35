<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-layout-component-field-Text'>/**
</span> * Layout class for {@link Ext.form.field.Text} fields. Handles sizing the input field.
 * @private
 */
Ext.define('Ext.layout.component.field.Text', {
    extend: 'Ext.layout.component.field.Field',
    alias: 'layout.textfield',
    requires: ['Ext.util.TextMetrics'],

<span id='Ext-layout-component-field-Text-property-type'>    type: 'textfield',
</span>    
<span id='Ext-layout-component-field-Text-property-canGrowWidth'>    canGrowWidth: true,
</span>
<span id='Ext-layout-component-field-Text-method-beginLayoutCycle'>    beginLayoutCycle: function(ownerContext) {
</span>        this.callParent(arguments);
        
        // Clear height, in case a previous layout cycle stretched it.
        if (ownerContext.heightModel.shrinkWrap) {
            ownerContext.inputContext.el.setStyle('height', '');
        }
    },

<span id='Ext-layout-component-field-Text-method-measureContentWidth'>    measureContentWidth: function (ownerContext) {
</span>        var me = this,
            owner = me.owner,
            width = me.callParent(arguments),
            inputContext = ownerContext.inputContext,
            inputEl, value, calcWidth, max, min;

        if (owner.grow &amp;&amp; me.canGrowWidth &amp;&amp; !ownerContext.state.growHandled) {
            inputEl = owner.inputEl;

            // Find the width that contains the whole text value
            value = Ext.util.Format.htmlEncode(inputEl.dom.value || (owner.hasFocus ? '' : owner.emptyText) || '');
            value += owner.growAppend;
            calcWidth = inputEl.getTextWidth(value) + inputContext.getFrameInfo().width;

            max = owner.growMax;
            min = Math.min(max, width);
            max = Math.max(owner.growMin, max, min);

            // Constrain
            calcWidth = Ext.Number.constrain(calcWidth, owner.growMin, max);
            inputContext.setWidth(calcWidth);
            ownerContext.state.growHandled = true;
            
            // Now that we've set the inputContext, we need to recalculate the width
            inputContext.domBlock(me, 'width');
            width = NaN;
        }
        return width;
    },
    
<span id='Ext-layout-component-field-Text-method-publishInnerHeight'>    publishInnerHeight: function(ownerContext, height) {
</span>        ownerContext.inputContext.setHeight(height - this.measureLabelErrorHeight(ownerContext));
    },

<span id='Ext-layout-component-field-Text-method-beginLayoutFixed'>    beginLayoutFixed: function(ownerContext, width, suffix) {
</span>        var me = this,
            ieInputWidthAdjustment = me.ieInputWidthAdjustment;

        if (ieInputWidthAdjustment) {
            me.adjustIEInputPadding(ownerContext);
            if(suffix === 'px') {
                width -= ieInputWidthAdjustment;
            }
        }

        me.callParent(arguments);
    },

<span id='Ext-layout-component-field-Text-method-adjustIEInputPadding'>    adjustIEInputPadding: function(ownerContext) {
</span>        // adjust for IE 6/7 strict content-box model
        this.owner.bodyEl.setStyle('padding-right', this.ieInputWidthAdjustment + 'px');
    }
});
</pre>
</body>
</html>
