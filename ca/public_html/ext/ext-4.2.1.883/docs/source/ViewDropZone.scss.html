<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">.#{$prefix}tree-drop-ok-append .#{$prefix}dd-drop-icon {
    background-image: theme-background-image('tree/drop-append');
}

.#{$prefix}tree-drop-ok-above .#{$prefix}dd-drop-icon {
    background-image: theme-background-image('tree/drop-above');
}

.#{$prefix}tree-drop-ok-below .#{$prefix}dd-drop-icon {
    background-image: theme-background-image('tree/drop-below');
}

.#{$prefix}tree-drop-ok-between .#{$prefix}dd-drop-icon {
    background-image: theme-background-image('tree/drop-between');
}

.#{$prefix}tree-ddindicator {
    height: 1px;
    border-width: 1px 0px 0px;
    border-style: dotted;
    border-color: green;
}

</pre>
</body>
</html>
