<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-tip-Tip'>/**
</span> * This is the base class for {@link Ext.tip.QuickTip} and {@link Ext.tip.ToolTip} that provides the basic layout and
 * positioning that all tip-based classes require. This class can be used directly for simple, statically-positioned
 * tips that are displayed programmatically, or it can be extended to provide custom tip implementations.
 * @xtype tip
 */
Ext.define('Ext.tip.Tip', {
    extend: 'Ext.panel.Panel',

    alternateClassName: 'Ext.Tip',

<span id='Ext-tip-Tip-cfg-closable'>    /**
</span>     * @cfg {Boolean} [closable=false]
     * True to render a close tool button into the tooltip header.
     */
    
<span id='Ext-tip-Tip-cfg-width'>    /**
</span>     * @cfg {Number} [width='auto']
     * Width in pixels of the tip.  Width will be ignored if it
     * exceeds the bounds of {@link #minWidth} or {@link #maxWidth}.
     */
    
<span id='Ext-tip-Tip-cfg-minWidth'>    /**
</span>     * @cfg {Number} minWidth
     * The minimum width of the tip in pixels.
     */
    minWidth : 40,
<span id='Ext-tip-Tip-cfg-maxWidth'>    /**
</span>     * @cfg {Number} maxWidth
     * The maximum width of the tip in pixels.  The maximum supported value is 500.
     */
    maxWidth : 500,
<span id='Ext-tip-Tip-cfg-shadow'>    /**
</span>     * @cfg {Boolean/String} shadow
     * `true` or &quot;sides&quot; for the default effect, &quot;frame&quot; for 4-way shadow, and &quot;drop&quot;
     * for bottom-right shadow.
     */
    shadow : &quot;sides&quot;,

<span id='Ext-tip-Tip-cfg-defaultAlign'>    /**
</span>     * @cfg {String} defaultAlign
     * **Experimental**. The default {@link Ext.util.Positionable#alignTo} anchor position value
     * for this tip relative to its element of origin.
     */
    defaultAlign : &quot;tl-bl?&quot;,
<span id='Ext-tip-Tip-cfg-constrainPosition'>    /**
</span>     * @cfg {Boolean} constrainPosition
     * If `true`, then the tooltip will be automatically constrained to stay within
     * the browser viewport.
     */
    constrainPosition : true,

<span id='Ext-tip-Tip-cfg-autoRender'>    // private panel overrides
</span>    autoRender: true,
<span id='Ext-tip-Tip-cfg-hidden'>    hidden: true,
</span><span id='Ext-tip-Tip-cfg-baseCls'>    baseCls: Ext.baseCSSPrefix + 'tip',
</span><span id='Ext-tip-Tip-cfg-floating'>    floating: {
</span>        shadow: true,
        shim: true
    },
<span id='Ext-tip-Tip-cfg-focusOnToFront'>    focusOnToFront: false,
</span>
<span id='Ext-tip-Tip-cfg-closeAction'>    /**
</span>     * @cfg {String} closeAction
     * The action to take when the close header tool is clicked:
     *
     * - **{@link #method-destroy}** : {@link #method-remove remove} the window from the DOM and
     *   {@link Ext.Component#method-destroy destroy} it and all descendant Components. The
     *   window will **not** be available to be redisplayed via the {@link #method-show} method.
     *
     * - **{@link #method-hide}** : **Default.** {@link #method-hide} the window by setting visibility
     *   to hidden and applying negative offsets. The window will be available to be
     *   redisplayed via the {@link #method-show} method.
     *
     * **Note:** This behavior has changed! setting *does* affect the {@link #method-close} method
     * which will invoke the approriate closeAction.
     */
    closeAction: 'hide',

<span id='Ext-tip-Tip-property-ariaRole'>    ariaRole: 'tooltip',
</span>
<span id='Ext-tip-Tip-property-alwaysFramed'>    // Flag to Renderable to always look up the framing styles for this Component
</span>    alwaysFramed: true,

<span id='Ext-tip-Tip-cfg-frameHeader'>    frameHeader: false,
</span>
<span id='Ext-tip-Tip-method-initComponent'>    initComponent: function() {
</span>        var me = this;

        me.floating = Ext.apply( {}, {
            shadow: me.shadow,
            constrain: me.constrainPosition
        }, me.self.prototype.floating);
        me.callParent(arguments);

        // Or in the deprecated config. Floating.doConstrain only constrains if the constrain property is truthy.
        me.constrain = me.constrain || me.constrainPosition;
    },

<span id='Ext-tip-Tip-method-showAt'>    /**
</span>     * Shows this tip at the specified XY position.  Example usage:
     *
     *     // Show the tip at x:50 and y:100
     *     tip.showAt([50,100]);
     *
     * @param {Number[]} xy An array containing the x and y coordinates
     */
    showAt : function(xy){
        var me = this;
        this.callParent(arguments);
        // Show may have been vetoed.
        if (me.isVisible()) {
            me.setPagePosition(xy[0], xy[1]);
            if (me.constrainPosition || me.constrain) {
                me.doConstrain();
            }
            me.toFront(true);
        }
    },

<span id='Ext-tip-Tip-method-initDraggable'>    /**
</span>     * @private
     * Set Tip draggable using base Component's draggability.
     */
    initDraggable : function(){
        var me = this;
        me.draggable = {
            el: me.getDragEl(),
            delegate: me.header.el,
            constrain: me,
            constrainTo: me.el.dom.parentNode
        };
        // Important: Bypass Panel's initDraggable. Call direct to Component's implementation.
        Ext.Component.prototype.initDraggable.call(me);
    },

<span id='Ext-tip-Tip-property-ghost'>    // Tip does not ghost. Drag is &quot;live&quot;
</span>    ghost: undefined,
<span id='Ext-tip-Tip-property-unghost'>    unghost: undefined
</span>});
</pre>
</body>
</html>
