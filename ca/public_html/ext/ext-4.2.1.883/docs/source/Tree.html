<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-data-Tree'>/**
</span> * @class Ext.data.Tree
 *
 * This class is used as a container for a series of nodes. The nodes themselves maintain
 * the relationship between parent/child. The tree itself acts as a manager. It gives functionality
 * to retrieve a node by its identifier: {@link #getNodeById}.
 *
 * The tree also relays events from any of it's child nodes, allowing them to be handled in a
 * centralized fashion. In general this class is not used directly, rather used internally
 * by other parts of the framework.
 *
 */
Ext.define('Ext.data.Tree', {
    alias: 'data.tree',

    mixins: {
        observable: &quot;Ext.util.Observable&quot;
    },

<span id='Ext-data-Tree-property-root'>    /**
</span>     * @property {Ext.data.NodeInterface}
     * The root node for this tree
     */
    root: null,

<span id='Ext-data-Tree-method-constructor'>    /**
</span>     * Creates new Tree object.
     * @param {Ext.data.NodeInterface} root (optional) The root node
     */
    constructor: function(root) {
        var me = this;

        me.mixins.observable.constructor.call(me);

        if (root) {
            me.setRootNode(root);
        }
        
        // All these events from tree nodes bubbble up and fire on this Tree
        me.on({
            scope: me,
            idchanged: me.onNodeIdChanged,
            insert: me.onNodeInsert,
            append: me.onNodeAppend,
            remove: me.onNodeRemove
        });
    },

<span id='Ext-data-Tree-method-getRootNode'>    /**
</span>     * Returns the root node for this tree.
     * @return {Ext.data.NodeInterface}
     */
    getRootNode : function() {
        return this.root;
    },

<span id='Ext-data-Tree-method-setRootNode'>    /**
</span>     * Sets the root node for this tree.
     * @param {Ext.data.NodeInterface} node
     * @return {Ext.data.NodeInterface} The root node
     */
    setRootNode : function(node) {
        var me = this;

        me.root = node;

        // If the passed node is currently the root of another Tree, remove it.
        if (node.rootOf) {
            node.rootOf.removeRootNode();
        }

        // If the passed node is owned by some other node, remove it.
        else if (node.parentNode) {
            node.parentNode.removeChild(node);
        }

        // Insert upward link to owning Tree
        node.rootOf = me;

        if (node.fireEventArgs('beforeappend', [null, node]) !== false) {
            node.set('root', true);
            // root node should never be phantom or dirty, so commit it
            node.updateInfo(true, {
                isFirst: true,
                isLast: true,
                depth: 0,
                index: 0,
                parentId: null
            });

            // The following events are fired on this TreePanel by the bubbling from NodeInterface.fireEvent
<span id='Ext-data-Tree-event-append'>            /**
</span>             * @event append
             * @inheritdoc Ext.data.NodeInterface#append
             */
<span id='Ext-data-Tree-event-remove'>            /**
</span>             * @event remove
             * @inheritdoc Ext.data.NodeInterface#remove
             */
<span id='Ext-data-Tree-event-move'>            /**
</span>             * @event move
             * @inheritdoc Ext.data.NodeInterface#move
             */
<span id='Ext-data-Tree-event-insert'>            /**
</span>             * @event insert
             * @inheritdoc Ext.data.NodeInterface#insert
             */
<span id='Ext-data-Tree-event-beforeappend'>            /**
</span>             * @event beforeappend
             * @inheritdoc Ext.data.NodeInterface#beforeappend
             */
<span id='Ext-data-Tree-event-beforeremove'>            /**
</span>             * @event beforeremove
             * @inheritdoc Ext.data.NodeInterface#beforeremove
             */
<span id='Ext-data-Tree-event-beforemove'>            /**
</span>             * @event beforemove
             * @inheritdoc Ext.data.NodeInterface#beforemove
             */
<span id='Ext-data-Tree-event-beforeinsert'>            /**
</span>             * @event beforeinsert
             * @inheritdoc Ext.data.NodeInterface#beforeinsert
             */
<span id='Ext-data-Tree-event-expand'>            /**
</span>             * @event expand
             * @inheritdoc Ext.data.NodeInterface#expand
             */
<span id='Ext-data-Tree-event-collapse'>            /**
</span>             * @event collapse
             * @inheritdoc Ext.data.NodeInterface#collapse
             */
<span id='Ext-data-Tree-event-beforeexpand'>            /**
</span>             * @event beforeexpand
             * @inheritdoc Ext.data.NodeInterface#beforeexpand
             */
<span id='Ext-data-Tree-event-beforecollapse'>            /**
</span>             * @event beforecollapse
             * @inheritdoc Ext.data.NodeInterface#beforecollapse
             */
<span id='Ext-data-Tree-event-sort'>            /**
</span>             * @event sort
             * @inheritdoc Ext.data.NodeInterface#event-sort
             */
<span id='Ext-data-Tree-event-rootchange'>            /**
</span>             * @event rootchange
             * Fires whenever the root node is changed in the tree.
             * @param {Ext.data.Model} root The new root
            */

            me.nodeHash = {};
            node.fireEvent('append', null, node);
            node.fireEvent('rootchange', node);
        }

        return node;
    },

<span id='Ext-data-Tree-method-removeRootNode'>    /**
</span>     * Removes the root node from this tree.
     * @return {Ext.data.NodeInterface} The root node
     */
    removeRootNode: function() {
        var me = this,
            root = me.root;

        root.set('root', false);
        root.fireEvent('remove', null, root, false);
        root.fireEvent('rootchange', null);

        // Unlink root after events so that the required bubbling propagates to all handlers.
        // This unregisters the node and its descendants.
        root.rootOf = me.root = null;
        return root;
    },

<span id='Ext-data-Tree-method-flatten'>    /**
</span>     * Flattens all the nodes in the tree into an array.
     * @private
     * @return {Ext.data.NodeInterface[]} The flattened nodes.
     */
    flatten: function(){
        return Ext.Object.getValues(this.nodeHash);
    },

<span id='Ext-data-Tree-method-onNodeInsert'>    /**
</span>     * Fired when a node is inserted into the root or one of it's children
     * @private
     * @param {Ext.data.NodeInterface} parent The parent node
     * @param {Ext.data.NodeInterface} node The inserted node
     */
    onNodeInsert: function(parent, node) {
        this.registerNode(node, true);
    },

<span id='Ext-data-Tree-method-onNodeAppend'>    /**
</span>     * Fired when a node is appended into the root or one of it's children
     * @private
     * @param {Ext.data.NodeInterface} parent The parent node
     * @param {Ext.data.NodeInterface} node The appended node
     */
    onNodeAppend: function(parent, node) {
        this.registerNode(node, true);
    },

<span id='Ext-data-Tree-method-onNodeRemove'>    /**
</span>     * Fired when a node is removed from the root or one of it's children
     * @private
     * @param {Ext.data.NodeInterface} parent The parent node
     * @param {Ext.data.NodeInterface} node The removed node
     */
    onNodeRemove: function(parent, node) {
        this.unregisterNode(node, true);
    },

<span id='Ext-data-Tree-method-onNodeIdChanged'>    /**
</span>     * Fired when a node's id changes.  Updates the node's id in the node hash.
     * @private
     * @param {Ext.data.NodeInterface} node 
     * @param {Number} oldId The old id
     * @param {Number} newId The new id
     */
    onNodeIdChanged: function(node, oldId, newId, oldInternalId) {
        var nodeHash = this.nodeHash;
    
        nodeHash[node.internalId] = node;
        delete nodeHash[oldInternalId];
    },

<span id='Ext-data-Tree-method-getNodeById'>    /**
</span>     * Gets a node in this tree by its id.
     * @param {String} id
     * @return {Ext.data.NodeInterface} The match node.
     */
    getNodeById : function(id) {
        return this.nodeHash[id];
    },

<span id='Ext-data-Tree-method-registerNode'>    /**
</span>     * Registers a node with the tree
     * @private
     * @param {Ext.data.NodeInterface} node The node to register
     * @param {Boolean} [includeChildren] True to unregister any child nodes
     */
    registerNode : function(node, includeChildren) {
        var me = this,
            children, length, i;

        me.nodeHash[node.internalId] = node;
        if (includeChildren === true) {
            children = node.childNodes;
            length = children.length;
            for (i = 0; i &lt; length; i++) {
                me.registerNode(children[i], true);
            }
        }
    },

<span id='Ext-data-Tree-method-unregisterNode'>    /**
</span>     * Unregisters a node with the tree
     * @private
     * @param {Ext.data.NodeInterface} node The node to unregister
     * @param {Boolean} [includeChildren] True to unregister any child nodes
     */
    unregisterNode : function(node, includeChildren) {
        var me = this,
            children, length, i;

        delete me.nodeHash[node.internalId];
        if (includeChildren === true) {
            children = node.childNodes;
            length = children.length;
            for (i = 0; i &lt; length; i++) {
                me.unregisterNode(children[i], true);
            }
        }
    },

<span id='Ext-data-Tree-method-sort'>    /**
</span>     * Sorts this tree
     * @private
     * @param {Function} sorterFn The function to use for sorting
     * @param {Boolean} recursive True to perform recursive sorting
     */
    sort: function(sorterFn, recursive) {
        this.getRootNode().sort(sorterFn, recursive);
    },

<span id='Ext-data-Tree-method-filter'>     /**
</span>     * Filters this tree
     * @private
     * @param {Function} sorterFn The function to use for filtering
     * @param {Boolean} recursive True to perform recursive filtering
     */
    filter: function(filters, recursive) {
        this.getRootNode().filter(filters, recursive);
    }
});</pre>
</body>
</html>
