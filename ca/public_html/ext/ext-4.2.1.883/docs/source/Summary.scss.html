<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">.#{$prefix}summary-bottom {
    border-bottom-color: $grid-header-background-color;
}

.#{$prefix}docked-summary {
    border-width: 1px;
    border-color: $panel-body-border-color;
    border-style: $panel-body-border-style;
    
    .#{$prefix}grid-table {
        width: 100%;
    }
}

.#{$prefix}grid-row-summary {
    .#{$prefix}grid-cell,
    .#{$prefix}grid-rowwrap,
    .#{$prefix}grid-cell-rowbody {
        border-color: $grid-row-cell-border-color;
        background-color: transparent !important;
        border-top-width: 0;
        font: $grid-row-cell-font;
    }
}

.#{$prefix}grid-with-row-lines .#{$prefix}grid-table-summary {
    border: 0;
}</pre>
</body>
</html>
