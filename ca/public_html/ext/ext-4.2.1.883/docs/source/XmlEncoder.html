<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">// @tag enterprise
<span id='Ext-data-amf-XmlEncoder'>/**
</span> * @class Ext.data.amf.XmlEncoder
 * This class serializes data in the Action Message Format XML (AMFX) format.
 * It can write simple and complex objects, to be used in conjunction with an
 * AMFX-compliant server.
 * To create an encoded XMl, first construct an Encoder:
 *
 *     var encoder = Ext.create('Ext.data.amf.XmlEncoder');
 *
 * Then use the writer methods to out data to the :
 *
 *     encoder.writeObject(1);
 *     encoder.writeObject({a: &quot;b&quot;});
 *
 * And access the data through the #bytes property:
 *     encoder.body;
 *
 * You can also reset the class to start a new body:
 *
 *     encoder.clear();
 *
 * Current limitations:
 * AMF3 format (format:3)
 * - Each object is written out explicitly, not using the reference tables
 *   supported by the AMFX format. This means the function does NOT support
 *   circular reference objects.
 * - Objects that aren't Arrays, Dates, Strings, Document (XML) or primitive
 *   values will be written out as anonymous objects with dynamic data.
 * - If the object has a $flexType field, that field will be used in signifying
 *   the object-type as an attribute, instead of being passed as data.
 * - There's no JavaScript equivalent to the ByteArray type in ActionScript,
 *   hence data will never be searialized as ByteArrays by the writeObject
 *   function. A writeByteArray method is provided for writing out ByteArray objects.
 *
 * For more information on working with AMF data please refer to the
 * [AMF Guide](#/guide/amf).
 */

Ext.define('Ext.data.amf.XmlEncoder', {

    alias: 'data.amf.xmlencoder',

<span id='Ext-data-amf-XmlEncoder-property-body'>    /**
</span>     * @property {String} body - The output string
     */
    body: &quot;&quot;,

    statics: {
<span id='Ext-data-amf-XmlEncoder-method-generateFlexUID'>        /**
</span>         * Utility function to generate a flex-friendly UID
         * @param {Number} id used in the first 8 chars of the id. If not provided, a random number will be used.
         * @return {String} a string-encoded opaque UID
         */
        generateFlexUID: function(id) {
            var uid = &quot;&quot;,
                i, j, t;
            if (id === undefined) {
                id = Ext.Number.randomInt(0, 0xffffffff);
            }
            // The format of a UID is XXXXXXXX-XXXX-XXXX-XXXX-YYYYYYYYXXXX
            // where each X is a random hex digit and each Y is a hex digit from the least significant part of a time stamp.
            t =  (id + 0x100000000).toString(16).toUpperCase(); // padded
            uid = t.substr(t.length - 8, 8); // last 8 chars

            for (j = 0; j &lt; 3; j++) {
                // 3 -XXXX segments
                uid += &quot;-&quot;;
                for (i = 0; i &lt; 4; i++) {
                    uid += Ext.Number.randomInt(0, 15).toString(16).toUpperCase();
                }
            }
            uid += &quot;-&quot;;
            // add timestamp
            t = new Number(new Date()).valueOf().toString(16).toUpperCase(); // get the String representation of milliseconds in hex format
            j = 0;
            if (t.length &lt; 8) { // pad with &quot;0&quot; if needed
                for (i = 0; i &lt; t.length - 8; i++) {
                    j++;
                    uid += &quot;0&quot;;
                }
            }
            // actual timestamp:
            uid += t.substr(-(8-j)); // last few chars
            // and last 4 random digits
            for (i = 0; i &lt; 4; i++) {
                uid += Ext.Number.randomInt(0, 15).toString(16).toUpperCase();
            }
            return uid;
        }
    },

<span id='Ext-data-amf-XmlEncoder-method-constructor'>    /**
</span>     * Creates new encoder.
     * @param {Object} config Configuration options
     */
    constructor: function(config) {
        this.initConfig(config);
        this.clear();
    },

<span id='Ext-data-amf-XmlEncoder-method-clear'>    /**
</span>     * Clears the accumulated data, starting with an empty string
     */
    clear: function() {
        this.body = &quot;&quot;;
    },

<span id='Ext-data-amf-XmlEncoder-method-encodeUndefined'>    /**
</span>     * Returns the encoding for undefined (which is the same as the encoding for null)
     */
    encodeUndefined: function() {
        return this.encodeNull();
    },

<span id='Ext-data-amf-XmlEncoder-method-writeUndefined'>    /**
</span>     * Writes the undefined value to the string
     */
    writeUndefined: function() {
        this.write(this.encodeUndefined());
    },

<span id='Ext-data-amf-XmlEncoder-method-encodeNull'>    /**
</span>     * Returns the encoding for null
     */
    encodeNull: function() {
        return &quot;&lt;null /&gt;&quot;;
    },

<span id='Ext-data-amf-XmlEncoder-method-writeNull'>    /**
</span>     * Writes the null value to the string
     */
    writeNull: function() {
        this.write(this.encodeNull());
    },

<span id='Ext-data-amf-XmlEncoder-method-encodeBoolean'>    /**
</span>     * Returns an encoded boolean
     * @param {Boolean} val a boolean value
     */
    encodeBoolean: function(val) {
        var str;
        if (val) {
            str = &quot;&lt;true /&gt;&quot;;
        } else {
            str = &quot;&lt;false /&gt;&quot;;
        }
        return str;
    },

<span id='Ext-data-amf-XmlEncoder-method-writeBoolean'>    /**
</span>     * Writes a boolean value to the string
     * @param {Boolean} val a boolean value
     */
    writeBoolean: function(val) {
        this.write(this.encodeBoolean(val));
    },


<span id='Ext-data-amf-XmlEncoder-method-encodeString'>    /**
</span>     * Returns an encoded string
     * @param {String} str the string to encode
     */
    encodeString: function(str) {
        var ret;
        if (str === &quot;&quot;) {
            ret = &quot;&lt;string /&gt;&quot;;
        } else {
            ret =&quot;&lt;string&gt;&quot;+str+&quot;&lt;/string&gt;&quot;;
        }
        return ret;
    },

<span id='Ext-data-amf-XmlEncoder-method-writeString'>    /**
</span>     * Writes a string tag with the string content.
     * @param {String} str the string to encode
     */
    writeString: function(str) {
        this.write(this.encodeString(str));
    },

<span id='Ext-data-amf-XmlEncoder-method-encodeInt'>    /**
</span>     * Returns an encoded int
     * @param {Number} num the integer to encode
     */
    encodeInt: function(num) {
        return &quot;&lt;int&gt;&quot; + num.toString() + &quot;&lt;/int&gt;&quot;;
    },

<span id='Ext-data-amf-XmlEncoder-method-writeInt'>    /**
</span>     * Writes a int tag with the content.
     * @param {Number} num the integer to encode
     */
    writeInt: function(num) {
        this.write(this.encodeInt(num));
    },

<span id='Ext-data-amf-XmlEncoder-method-encodeDouble'>    /**
</span>     * Returns an encoded double
     * @param {Number} num the double to encode
     */
    encodeDouble: function(num) {
        return &quot;&lt;double&gt;&quot; + num.toString() + &quot;&lt;/double&gt;&quot;;
    },

<span id='Ext-data-amf-XmlEncoder-method-writeDouble'>    /**
</span>     * Writes a double tag with the content.
     * @param {Number} num the double to encode
     */
    writeDouble: function(num) {
        this.write(this.encodeDouble(num));
    },

<span id='Ext-data-amf-XmlEncoder-method-encodeNumber'>    /**
</span>     * Returns an encoded number. Decides wheter to use int or double encoding.
     * @param {Number} num the number to encode
     */
    encodeNumber: function(num) {
        var maxInt = 0x1fffffff,
            minSignedInt = -0xfffffff;
        //&lt;debug&gt;
        if (typeof(num) !== &quot;number&quot; &amp;&amp; !(num instanceof Number)) {
            Ext.log.warn(&quot;Encoder: writeNumber argument is not numeric. Can't coerce.&quot;);
        }
        // &lt;/debug&gt;

        // switch to the primitive value for handling:
        if (num instanceof Number) {
            num = num.valueOf();
        }
        // Determine if this is an integer or a float.
        if (num % 1 === 0 &amp;&amp; num &gt;= minSignedInt &amp;&amp; num &lt;= maxInt) {
            // The number has no decimal point and is within bounds. Let's encode it.
            return this.encodeInt(num);
        } else {
            return this.encodeDouble(num);
        }
    },

<span id='Ext-data-amf-XmlEncoder-method-writeNumber'>    /**
</span>     * Writes a number, deciding if to use int or double as the tag
     * @param {Number} num the number to encode
     */
    writeNumber: function(num) {
        this.write(this.encodeNumber(num));
    },

<span id='Ext-data-amf-XmlEncoder-method-encodeDate'>    /**
</span>     * Encode a date
     * @param {Date} date the date to encode
     */
    encodeDate: function(date) {
        return &quot;&lt;date&gt;&quot; + (new Number(date)).toString() + &quot;&lt;/date&gt;&quot;;
    },

<span id='Ext-data-amf-XmlEncoder-method-writeDate'>    /**
</span>     * Write a date to the string
     * @param {Date} date the date to encode
     */
    writeDate: function(date) {
        this.write(this.encodeDate(date));
    },

<span id='Ext-data-amf-XmlEncoder-method-encodeEcmaElement'>    /**
</span>     * @private
     * Encodes one ECMA array element
     * @param {String} key the name of the element
     * @param {Object} value the value of the element
     * @return {String} the encoded key-value pair
     */
    encodeEcmaElement: function(key, value) {
        var str = '&lt;item name=&quot;' + key.toString() + '&quot;&gt;' + this.encodeObject(value) + '&lt;/item&gt;';
        return str;
    },

<span id='Ext-data-amf-XmlEncoder-method-encodeArray'>    /**
</span>     * Encodes an array, marking it as an ECMA array if it has associative (non-ordinal) indices
     * @param {Array} array the array to encode
     */
    encodeArray: function(array) {
        var ordinals=[],
            firstNonOrdinal,
            ecmaElements=[],
            length = array.length, // length is of ordinal section only
            i, str;
        for (i in array) {
            if (Ext.isNumeric(i) &amp;&amp; (i % 1 == 0)) {
                //this is an integer. Add to ordinals array
                ordinals[i] = this.encodeObject(array[i]);
            } else {
                ecmaElements.push(this.encodeEcmaElement(i, array[i]));
            }
        }
        firstNonOrdinal=ordinals.length;
        // now, check if we have consecutive numbers in the ordinals array
        for (i = 0; i &lt; ordinals.length; i++) {
            if (ordinals[i] === undefined) {
                //  we have a gap in the array. Mark it - the rest of the items become ECMA elements
                firstNonOrdinal = i;
                break;
            }
        }
        if (firstNonOrdinal &lt; ordinals.length) {
            // transfer some of the elements to the ecma array
            for (i = firstNonOrdinals; i &lt; ordinals.length; i++) {
                if (ordinals[i] !== undefined) {
                    ecmaElements.push(this.encodeEcmaElement(i, ordinals[i]));
                }
            }
            ordinals = ordinals.slice(0, firstNonOrdinal);
        }

        // finally start constructing the string
        str = '&lt;array length=&quot;' + ordinals.length + '&quot;';
        if (ecmaElements.length &gt; 0) {
            str += ' ecma=&quot;true&quot;';
        }
        str += '&gt;';

        // first add the oridnals in consecutive order:
        for (i = 0; i &lt; ordinals.length; i++) { // iterate by counting since we need to guarantee the order
            str += ordinals[i];
        }
        // Now add ECMA items
        for (i in ecmaElements) {
            str += ecmaElements[i];
        }
        // And close the array:
        str += '&lt;/array&gt;';
        return str;
    },

<span id='Ext-data-amf-XmlEncoder-method-writeArray'>    /**
</span>     * Writes an array to the string, marking it as an ECMA array if it has associative (non-ordinal) indices
     * @param {Array} array the array to encode
     */
    writeArray: function(array) {
        this.write(this.encodeArray(array));
    },

<span id='Ext-data-amf-XmlEncoder-method-encodeXml'>    /**
</span>     * Encodes an xml document into a CDATA section
     * @param {XMLElement/HTMLElement} xml an XML document or element (Document type in some browsers)
     */
    encodeXml: function(xml) {
        var str = this.convertXmlToString(xml);
        return &quot;&lt;xml&gt;&lt;![CDATA[&quot; + str + &quot;]]&gt;&lt;/xml&gt;&quot;;
    },

<span id='Ext-data-amf-XmlEncoder-method-writeXml'>    /**
</span>     * Write an XML document to the string
     * @param {XMLElement/HTMLElement} xml an XML document or element (Document type in some browsers)
     */
    writeXml: function(xml) {
        this.write(this.encodeXml(xml));
    },

<span id='Ext-data-amf-XmlEncoder-method-encodeGenericObject'>    /**
</span>     * Encodes a generic object into AMFX format. If a &lt;tt&gt;$flexType&lt;/tt&gt; member is defined, list that as the object type.
     * @param {Object} obj the object to encode
     * @return {String} the encoded text
     */
    encodeGenericObject: function(obj) {
        var traits = [],
            values = [],
            flexType = null,
            i, str;
        for (i in obj) {
            if (i == &quot;$flexType&quot;) {
                flexType = obj[i];
            } else {
                traits.push(this.encodeString(new String(i)));
                values.push(this.encodeObject(obj[i]));
            }
        }
        if (flexType) {
            str = '&lt;object type=&quot;' +flexType + '&quot;&gt;';
        } else {
            str=&quot;&lt;object&gt;&quot;;
        }
        if (traits.length &gt; 0) {
            str += &quot;&lt;traits&gt;&quot;;
            str += traits.join(&quot;&quot;);
            str += &quot;&lt;/traits&gt;&quot;;
        } else {
            str += &quot;&lt;traits /&gt;&quot;;
        }
        str += values.join(&quot;&quot;);
        str += &quot;&lt;/object&gt;&quot;;
        return str;
    },

<span id='Ext-data-amf-XmlEncoder-method-writeGenericObject'>    /**
</span>     * Writes a generic object to the string. If a &lt;tt&gt;$flexType&lt;/tt&gt; member is defined, list that as the object type.
     * @param {Object} obj the object to encode
     */
    writeGenericObject: function(obj) {
        this.write(this.encodeGenericObject(obj));
    },

<span id='Ext-data-amf-XmlEncoder-method-encodeByteArray'>    /**
</span>     * Encodes a byte arrat in AMFX format
     * @param {Array} array the byte array to encode
     */
    encodeByteArray: function(array) {
        var str, i, h;
        if (array.length &gt; 0) {
            str = &quot;&lt;bytearray&gt;&quot;;
            for (i = 0; i &lt; array.length; i++) {
                //&lt;debug&gt;
                if (!Ext.isNumber(array[i])) {
                    Ext.Error.raise(&quot;Byte array contains a non-number: &quot; + array[i]  + &quot; in index: &quot; + i);
                }
                if (array[i] &lt; 0 || array[i] &gt; 255) {
                    Ext.Error.raise(&quot;Byte array value out of bounds: &quot; + array[i]);
                }
                //&lt;/debug&gt;
                h = array[i].toString(16).toUpperCase();
                if (array[i] &lt; 0x10) {
                    h = &quot;0&quot; + h;
                }
                str += h;
            }
            str += &quot;&lt;/bytearray&gt;&quot;;
        } else {
            str = &quot;&lt;bytearray /&gt;&quot;;
        }
        return str;
    },

<span id='Ext-data-amf-XmlEncoder-method-writeByteArray'>    /**
</span>     * Writes an AMFX byte array to the string. This is for convenience only and is not called automatically by writeObject.
     * @param {Array} array the byte array to encode
     */
    writeByteArray: function(array) {
        this.write(this.encodeByteArray(array));
    },

<span id='Ext-data-amf-XmlEncoder-method-encodeObject'>    /**
</span>     * encode the appropriate data item. Supported types:
     * - undefined
     * - null
     * - boolean
     * - integer
     * - double
     * - UTF-8 string
     * - XML Document (identified by being instaneof Document. Can be generated with: new DOMParser()).parseFromString(xml, &quot;text/xml&quot;);
     * - Date
     * - Array
     * - Generic object
     * @param {Object} item A primitive or object to write to the stream
     * @return {String} the encoded object in AMFX format
     */
    encodeObject: function(item) {
        var t = typeof(item);
        //Ext.log(&quot;Writing &quot; + item + &quot; of type &quot; + t);
        if (t === &quot;undefined&quot;) {
            return this.encodeUndefined();
        } else if (item === null) { // can't check type since typeof(null) returns &quot;object&quot;
            return this.encodeNull();
        } else if (Ext.isBoolean(item)) {
            return this.encodeBoolean(item);
        } else if (Ext.isString(item)) {
            return this.encodeString(item);
        } else if (t === &quot;number&quot; || item instanceof Number) { // Can't use Ext.isNumeric since it accepts strings as well
            return this.encodeNumber(item);
        } else if (t === &quot;object&quot;) {
            // Figure out which object this is
            if (item instanceof Date) {
                return this.encodeDate(item);
            } else if (Ext.isArray(item)) {
                return this.encodeArray(item);
            } else if (this.isXmlDocument(item)) {
                return this.encodeXml(item);
            } else {
                // Treat this as a generic object with name/value pairs of data.
                return this.encodeGenericObject(item);
            }
        } else {
            //&lt;debug&gt;
            Ext.log.warn(&quot;AMFX Encoder: Unknown item type &quot; + t + &quot; can't be written to stream: &quot; + item);
            //&lt;/debug&gt;
        }
        return null; // if we reached here, return null
    },

<span id='Ext-data-amf-XmlEncoder-method-writeObject'>    /**
</span>     * Writes the appropriate data item to the string. Supported types:
     * - undefined
     * - null
     * - boolean
     * - integer
     * - double
     * - UTF-8 string
     * - XML Document (identified by being instaneof Document. Can be generated with: new DOMParser()).parseFromString(xml, &quot;text/xml&quot;);
     * - Date
     * - Array
     * - Generic object
     * @param {Object} item A primitive or object to write to the stream
     */
    writeObject: function(item) {
        this.write(this.encodeObject(item));
    },

<span id='Ext-data-amf-XmlEncoder-method-encodeAmfxRemotingPacket'>    /**
</span>     * Encodes an AMFX remoting message with the AMFX envelope.
     * @param {Ext.data.amf.RemotingMessage} message the message to pass on to serialize.
     */
    encodeAmfxRemotingPacket: function(message) {
        var msg, str;
        str = '&lt;amfx ver=&quot;3&quot; xmlns=&quot;http://www.macromedia.com/2005/amfx&quot;&gt;&lt;body&gt;';
        str += message.encodeMessage();
        str += '&lt;/body&gt;&lt;/amfx&gt;';
        return str;
    },

<span id='Ext-data-amf-XmlEncoder-method-writeAmfxRemotingPacket'>    /**
</span>     * Writes an AMFX remoting message with the AMFX envelope to the string.
     * @param {Ext.data.amf.RemotingMessage} message the message to pass on to serialize.
     */
    writeAmfxRemotingPacket: function(params) {
        this.write(this.encodeAmfxRemotingPacket(params));
    },

<span id='Ext-data-amf-XmlEncoder-method-convertXmlToString'>    /**
</span>     * Converts an XML Document object to a string.
     * @param {Object} xml XML document to convert (typically Document object)
     * @return {String} A string representing the document
     * @private
     */
    convertXmlToString: function(xml) {
        var str;
        if (window.XMLSerializer) {
            // this is not IE, so:
            str = new window.XMLSerializer().serializeToString(xml);
        } else {
            //no XMLSerializer, might be an old version of IE
            str = xml.xml;
        }
        return str;
    },
    
<span id='Ext-data-amf-XmlEncoder-method-isXmlDocument'>    /**
</span>     * Tries to determine if an object is an XML document
     * @param {Object} item to identify
     * @return {Boolean} true if it's an XML document, false otherwise
     */
    isXmlDocument: function(item) {
        // We can't test if Document is defined since IE just throws an exception. Instead rely on the DOMParser object
        if (window.DOMParser) {
            if (Ext.isDefined(item.doctype)) {
                return true;
            }
        }
        // Otherwise, check if it has an XML field
        if (Ext.isString(item.xml)) {
            // and we can get the xml
            return true;
        }
        return false;
    },

<span id='Ext-data-amf-XmlEncoder-method-write'>    /**
</span>     * Appends a string to the body of the message
     * @param {String} str the string to append
     * @private
     */
    write: function(str) {
        this.body += str;
    }
});
</pre>
</body>
</html>
