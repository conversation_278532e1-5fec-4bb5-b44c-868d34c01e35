<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">Ext.define('Ext.rtl.form.field.Trigger', {
    override: 'Ext.form.field.Trigger',

    beforeRender: function(){
        if (this.getHierarchyState().rtl) {
            this.extraTriggerCls = this._rtlCls;
        }
        this.callParent(arguments);
    }
});</pre>
</body>
</html>
