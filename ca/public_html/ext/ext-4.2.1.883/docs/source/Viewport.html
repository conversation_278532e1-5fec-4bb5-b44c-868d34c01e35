<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-container-Viewport'>/**
</span> * A specialized container representing the viewable application area (the browser viewport).
 *
 * The Viewport renders itself to the document body, and automatically sizes itself to the size of
 * the browser viewport and manages window resizing. There may only be one Viewport created
 * in a page.
 *
 * Like any {@link Ext.container.Container Container}, a Viewport will only perform sizing and positioning
 * on its child Components if you configure it with a {@link #layout}.
 *
 * A Common layout used with Viewports is {@link Ext.layout.container.Border border layout}, but if the
 * required layout is simpler, a different layout should be chosen.
 *
 * For example, to simply make a single child item occupy all available space, use
 * {@link Ext.layout.container.Fit fit layout}.
 *
 * To display one &quot;active&quot; item at full size from a choice of several child items, use
 * {@link Ext.layout.container.Card card layout}.
 *
 * Inner layouts are available because all {@link Ext.panel.Panel Panel}s
 * added to the Viewport, either through its {@link #cfg-items}, or the {@link #method-add}
 * method of any of its child Panels may themselves have a layout.
 *
 * The Viewport does not provide scrolling, so child Panels within the Viewport should provide
 * for scrolling if needed using the {@link #autoScroll} config.
 *
 * An example showing a classic application border layout:
 *
 *     @example
 *     Ext.create('Ext.container.Viewport', {
 *         layout: 'border',
 *         items: [{
 *             region: 'north',
 *             html: '&lt;h1 class=&quot;x-panel-header&quot;&gt;Page Title&lt;/h1&gt;',
 *             border: false,
 *             margins: '0 0 5 0'
 *         }, {
 *             region: 'west',
 *             collapsible: true,
 *             title: 'Navigation',
 *             width: 150
 *             // could use a TreePanel or AccordionLayout for navigational items
 *         }, {
 *             region: 'south',
 *             title: 'South Panel',
 *             collapsible: true,
 *             html: 'Information goes here',
 *             split: true,
 *             height: 100,
 *             minHeight: 100
 *         }, {
 *             region: 'east',
 *             title: 'East Panel',
 *             collapsible: true,
 *             split: true,
 *             width: 150
 *         }, {
 *             region: 'center',
 *             xtype: 'tabpanel', // TabPanel itself has no title
 *             activeTab: 0,      // First tab active by default
 *             items: {
 *                 title: 'Default Tab',
 *                 html: 'The first tab\'s content. Others may be added dynamically'
 *             }
 *         }]
 *     });
 */
Ext.define('Ext.container.Viewport', {
    extend: 'Ext.container.Container',
    alias: 'widget.viewport',
    requires: ['Ext.EventManager'],
    alternateClassName: 'Ext.Viewport',

    // Privatize config options which, if used, would interfere with the
    // correct operation of the Viewport as the sole manager of the
    // layout of the document body.

<span id='Ext-container-Viewport-cfg-applyTo'>    /**
</span>     * @cfg {String/HTMLElement/Ext.Element} applyTo
     * @private
     */

<span id='Ext-container-Viewport-cfg-allowDomMove'>    /**
</span>     * @cfg {Boolean} allowDomMove
     * @private
     */

<span id='Ext-container-Viewport-cfg-renderTo'>    /**
</span>     * @cfg {String/HTMLElement/Ext.Element} renderTo
     * Always renders to document body.
     * @private
     */

<span id='Ext-container-Viewport-cfg-height'>    /**
</span>     * @cfg {Number} height
     * Sets itself to viewport width.
     * @private
     */

<span id='Ext-container-Viewport-cfg-width'>    /**
</span>     * @cfg {Number} width
     * Sets itself to viewport height.
     * @private
     */

<span id='Ext-container-Viewport-property-isViewport'>    /**
</span>     * @property {Boolean} isViewport
     * `true` in this class to identify an object as an instantiated Viewport, or subclass thereof.
     */
    isViewport: true,

<span id='Ext-container-Viewport-property-ariaRole'>    ariaRole: 'application',
</span>    
<span id='Ext-container-Viewport-property-preserveElOnDestroy'>    preserveElOnDestroy: true,
</span>    
<span id='Ext-container-Viewport-property-viewportCls'>    viewportCls: Ext.baseCSSPrefix + 'viewport',
</span>
<span id='Ext-container-Viewport-method-initComponent'>    initComponent : function() {
</span>        var me = this,
            html = document.body.parentNode,
            el = me.el = Ext.getBody();

        // Get the DOM disruption over with before the Viewport renders and begins a layout
        Ext.getScrollbarSize();
        
        // Clear any dimensions, we will size later on
        me.width = me.height = undefined;

        me.callParent(arguments);
        Ext.fly(html).addCls(me.viewportCls);
        if (me.autoScroll) {
            Ext.fly(html).setStyle(me.getOverflowStyle());
            delete me.autoScroll;
        }
        el.setHeight = el.setWidth = Ext.emptyFn;
        el.dom.scroll = 'no';
        me.allowDomMove = false;
        me.renderTo = me.el;
    },
    
<span id='Ext-container-Viewport-method-applyTargetCls'>    // override here to prevent an extraneous warning
</span>    applyTargetCls: function(targetCls) {
        this.el.addCls(targetCls);
    },
    
<span id='Ext-container-Viewport-method-onRender'>    onRender: function() {
</span>        var me = this;

        me.callParent(arguments);

        // Important to start life as the proper size (to avoid extra layouts)
        // But after render so that the size is not stamped into the body
        me.width = Ext.Element.getViewportWidth();
        me.height = Ext.Element.getViewportHeight();
    },

<span id='Ext-container-Viewport-method-afterFirstLayout'>    afterFirstLayout: function() {
</span>        var me = this;

        me.callParent(arguments);
        setTimeout(function() {
            Ext.EventManager.onWindowResize(me.fireResize, me);
        }, 1);
    },

<span id='Ext-container-Viewport-method-fireResize'>    fireResize : function(width, height){
</span>        // In IE we can get resize events that have our current size, so we ignore them
        // to avoid the useless layout...
        if (width != this.width || height != this.height) {
            this.setSize(width, height);
        }
    },

<span id='Ext-container-Viewport-method-initHierarchyState'>    initHierarchyState: function(hierarchyState) {
</span>        this.callParent([this.hierarchyState = Ext.rootHierarchyState]);
    },
    
<span id='Ext-container-Viewport-method-beforeDestroy'>    beforeDestroy: function(){
</span>        var me = this;
        
        me.removeUIFromElement();
        me.el.removeCls(me.baseCls);
        Ext.fly(document.body.parentNode).removeCls(me.viewportCls);
        me.callParent();
    }
});
</pre>
</body>
</html>
