<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-app-domain-Store'>/**
</span> * This class implements the data store event domain. All classes extending from 
 * {@link Ext.data.AbstractStore} are included in this domain. The selectors are simply
 * store id's or the wildcard &quot;*&quot; to match any store.
 *
 * @protected
 */

Ext.define('Ext.app.domain.Store', {
    extend: 'Ext.app.EventDomain',
    singleton: true,
    
    requires: [
        'Ext.data.AbstractStore'
    ],
    
<span id='Ext-app-domain-Store-property-type'>    type: 'store',
</span><span id='Ext-app-domain-Store-cfg-idProperty'>    idProperty: 'storeId',
</span>    
<span id='Ext-app-domain-Store-method-constructor'>    constructor: function() {
</span>        var me = this;
        
        me.callParent();
        me.monitor(Ext.data.AbstractStore);
    }
});
</pre>
</body>
</html>
