<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-form-field-Spinner'>/**
</span> * @class Ext.form.field.Spinner
 */

<span id='Ext-form-field-Spinner-css_var-S-spinner-btn-height'>/**
</span> * @var {number}
 * The height of the Spinner trigger buttons
 */
$spinner-btn-height: $form-field-height / 2 !default;

<span id='Ext-form-field-Spinner-css_var-S-spinner-toolbar-btn-height'>/**
</span> * @var {number}
 * The height of the Spinner trigger buttons when the Spinner is used on a
 * {@link Ext.toolbar.Toolbar Toolbar}
 */
$spinner-toolbar-btn-height: $form-toolbar-field-height / 2 !default;</pre>
</body>
</html>
