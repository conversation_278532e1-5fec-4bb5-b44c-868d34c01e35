<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-form-field-TextArea'>/**
</span> * @class Ext.form.field.TextArea
 */

<span id='Ext-form-field-TextArea-css_var-S-form-textarea-line-height'>/**
</span> * @var {number/string}
 * The line-height to use for the TextArea's text
 */
$form-textarea-line-height: normal !default;</pre>
</body>
</html>
