<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">// @tag enterprise
<span id='Ext-data-amf-XmlDecoder'>/**
</span> * @class Ext.data.amf.XmlDecoder
 * This class parses an XML-based AMFX message and returns the deserialized
 * objects. You should not need to use this class directly. It's mostly used by
 * the AMFX Direct implementation.
 * To decode a message, first construct a Decoder:
 *
 *      decoder = Ext.create('Ext.data.amf.XmlDecoder');
 *
 * Then ask it to read in the message :
 *
 *     resp = decoder.readAmfxMessage(str);
 *
 * For more information on working with AMF data please refer to the
 * [AMF Guide](#/guide/amf).
 */
Ext.define('Ext.data.amf.XmlDecoder', {

    alias: 'data.amf.xmldecoder',

    statics: {

<span id='Ext-data-amf-XmlDecoder-method-readXml'>        /**
</span>         * Parses an xml string and returns an xml document
         * @private
         * @param {String} xml
         */
        readXml: function(xml) {
            var doc;

            if (window.DOMParser) {
                doc = (new DOMParser()).parseFromString(xml, &quot;text/xml&quot;);
            } else {
                doc = new ActiveXObject(&quot;Microsoft.XMLDOM&quot;);
                doc.loadXML(xml);
            }

            return doc;
        },

<span id='Ext-data-amf-XmlDecoder-method-readByteArray'>        /**
</span>         * parses a node containing a byte array in hexadecimal format, returning the reconstructed array.
         * @param {HTMLElement/XMLElement} node the node
         * @return {Array} a byte array
         */
        readByteArray: function(node) {
            var bytes = [],
                c, i, str;
            str = node.firstChild.nodeValue;
            for (i = 0; i &lt; str.length; i = i + 2) {
                c = str.substr(i, 2);
                bytes.push(parseInt(c, 16));
            }
            return bytes;
        },

<span id='Ext-data-amf-XmlDecoder-method-readAMF3Value'>        /**
</span>         * Deserializes an AMF3 binary object from a byte array
         * @param {Array} bytes the byte array containing one AMF3-encoded value
         * @return {Object} the decoded value
         */
        readAMF3Value: function(bytes) {
            var packet;
            packet = Ext.create('Ext.data.amf.Packet');
            return packet.decodeValue(bytes);
        },

<span id='Ext-data-amf-XmlDecoder-method-decodeTidFromFlexUID'>        /**
</span>         * Accepts Flex-style UID and decodes the number in the first four bytes (8 hex digits) of data.
         * @param {String} messageId the message ID
         * @return {Number} the transaction ID
         */
        decodeTidFromFlexUID: function(messageId) {
            var str;
            str = messageId.substr(0,8);
            return parseInt(str, 16);
        }

    },

<span id='Ext-data-amf-XmlDecoder-method-constructor'>    /**
</span>     * Creates new encoder.
     * @param {Object} config Configuration options
     */
    constructor: function(config) {
        this.initConfig(config);
        this.clear();
    },

<span id='Ext-data-amf-XmlDecoder-method-clear'>    /**
</span>     * Clears the accumulated data and reference tables
     */
    clear: function() {
        // reset reference counters
        this.objectReferences=[];
        this.traitsReferences=[];
        this.stringReferences=[];
    },

<span id='Ext-data-amf-XmlDecoder-method-readAmfxMessage'>    /**
</span>     * Reads and returns a decoded AMFX packet.
     * @param {String} xml the xml of the message
     * @return {Object} the response object containing the message
     */
    readAmfxMessage: function(xml) {
        var doc, amfx, body,
            i, resp={};
        this.clear(); // reset counters
        doc = Ext.data.amf.XmlDecoder.readXml(xml);
        amfx = doc.getElementsByTagName('amfx')[0];
        //&lt;debug&gt;
        if (!amfx) {
            Ext.warn.log(&quot;No AMFX tag in message&quot;);
        }
        if (amfx.getAttribute('ver') != &quot;3&quot;) {
            Ext.Error.raise(&quot;Unsupported AMFX version: &quot; + amfx.getAttribute('ver'));
        }
        //&lt;/debug&gt;
        body = amfx.getElementsByTagName('body')[0];
        resp.targetURI = body.getAttribute('targetURI');
        resp.responseURI = body.getAttribute('responseURI'); // most likely empty string
        for (i = 0; i &lt; body.childNodes.length; i++) {
            if (body.childNodes.item(i).nodeType != 1) {
                // only process element nodes, ignore white space and text nodes
                continue;
            }
            resp.message = this.readValue(body.childNodes.item(i));
            break; // no need to keep iterating
        }
        return resp;
    },

<span id='Ext-data-amf-XmlDecoder-method-readValue'>    /**
</span>     * Parses an HTML element returning the appropriate JavaScript value from the AMFX data.
     * @param {HTMLElement} node the node to parse
     * @return {Object} a JavaScript object or value
     */
    readValue: function(node) {
        var val;
        if (typeof node.normalize === 'function') {
            node.normalize();
        }
        // 2DO: handle references!
        if (node.tagName == &quot;null&quot;) {
            return null;
        } else if (node.tagName == &quot;true&quot;) {
            return true;
        } else if (node.tagName == &quot;false&quot;) {
            return false;
        } else if (node.tagName == &quot;string&quot;) {
            return this.readString(node);
        } else if (node.tagName == &quot;int&quot;) {
            return parseInt(node.firstChild.nodeValue);
        } else if (node.tagName == &quot;double&quot;) {
            return parseFloat(node.firstChild.nodeValue);
        } else if (node.tagName == &quot;date&quot;) {
            val = new Date(parseFloat(node.firstChild.nodeValue));
            // record in object reference table
            this.objectReferences.push(val);
            return val;
        } else if (node.tagName == &quot;dictionary&quot;) {
            return this.readDictionary(node);
        } else if (node.tagName == &quot;array&quot;) {
            return this.readArray(node);
        } else if (node.tagName == &quot;ref&quot;) {
            return this.readObjectRef(node);
        } else if (node.tagName == &quot;object&quot;) {
            return this.readObject(node);
        } else if (node.tagName == &quot;xml&quot;) {
            // the CDATA content of the node is a parseable XML document. parse it.
            return Ext.data.amf.XmlDecoder.readXml(node.firstChild.nodeValue);
        } else if (node.tagName == &quot;bytearray&quot;) {
            // a byte array is usually an AMF stream. Parse it to a byte array, then pass through the AMF decoder to get the objects inside
            return Ext.data.amf.XmlDecoder.readAMF3Value(Ext.data.amf.XmlDecoder.readByteArray(node));
        }
        //&lt;debug&gt;
        Ext.Error.raise(&quot;Unknown tag type: &quot; + node.tagName);
        //&lt;/debug&gt;
        return null;
    },

<span id='Ext-data-amf-XmlDecoder-method-readString'>    /**
</span>     * Reads a string or string reference and return the value
     * @param {HTMLElement/XMLElement} node the node containing a string object
     * @return {String} the parsed string
     */
    readString: function(node) {
        var val;
        if (node.getAttributeNode('id')) {
            return this.stringReferences[parseInt(node.getAttribute('id'))];
        }
        val = (node.firstChild ? node.firstChild.nodeValue : &quot;&quot;) || &quot;&quot;;
        this.stringReferences.push(val);
        return val;
    },

<span id='Ext-data-amf-XmlDecoder-method-readTraits'>    /**
</span>     * Parses and returns an ordered list of trait names
     * @param {HTMLElement/XMLElement} node the traits node from the XML doc
     * @return {Array} an array of ordered trait names or null if it's an externalizable object
     */
    readTraits: function(node) {
        var traits = [], i, rawtraits;
        if (node === null) {
            return null;
        }
        if (node.getAttribute('externalizable') == &quot;true&quot;) {
            // no traits since it's an externalizable or a null object.
            return null;
        }
        if (node.getAttributeNode('id')) {
            // return traits reference
            return this.traitsReferences[parseInt(node.getAttributeNode('id').value)];
        }
        /* // empty anonymous objects still seem to get their empty traits in the reference table
         if (!node.hasChildNodes()) {
         var className = node.parentNode.getElementsByTagName('type');
         if (className.length == 0) {
         return traits; // special case of an anonymous object with no traits. Does not get reference counted
         }
         }
         */
        rawtraits = node.childNodes;
        for (i = 0; i &lt; rawtraits.length; i++) {
            if (rawtraits.item(i).nodeType != 1) {
                // only process element nodes, ignore white space and text nodes
                continue;
            }
            // this will be a string, but let the readValue function handle it nonetheless
            traits.push(this.readValue(rawtraits.item(i)));
        }

        // register traits in ref table:
        this.traitsReferences.push(traits);
        return traits;
    },

<span id='Ext-data-amf-XmlDecoder-method-readObjectRef'>    /**
</span>     * Parses and return an object / array / dictionary / date from reference
     * @param {HTMLElement/XMLElement} node the ref node
     * @return {Object} the previously instantiated object referred to by the ref node
     */
    readObjectRef: function(node) {
        var id;
        id = parseInt(node.getAttribute('id'));
        return this.objectReferences[id];
    },

<span id='Ext-data-amf-XmlDecoder-method-readObject'>    /**
</span>     * Parses and returns an AMFX object.
     * @param {HTMLElement/XMLElement} the `&lt;object&gt;` node to parse
     * @return {Object} the deserialized object
     */
    readObject: function(node) {
        var obj,
            traits = [],
            traitsNode,
            i, j, n,
            key, val,
            klass = null, className;

        className = node.getAttribute('type');
        if (className) {
            klass = Ext.ClassManager.getByAlias('amfx.' + className); // check if special case for class
        }
        obj = klass ? new klass() : (className ? {$className: className} : {}); // if there is no klass, mark the classname for easier parsing of returned results

        // check if we need special handling for this class
        if ((!klass) &amp;&amp; this.converters[className]) {
            obj = this.converters[className](this,node);
            return obj; // we're done
        }

        traitsNode = node.getElementsByTagName('traits')[0];
        traits = this.readTraits(traitsNode);
        //&lt;debug&gt;
        if (traits === null) {
            Ext.Error.raise(&quot;No support for externalizable object: &quot; + className);
        }
        //&lt;/debug&gt;
        // Register object if ref table, in case there's a cyclical reference coming
        this.objectReferences.push(obj);


        // Now we expect an item for each trait name we have. We assume it's an ordered list. We'll skip the first (traits) tag
        j = 0;
        for (i = 0; i &lt; node.childNodes.length; i++) {
            n = node.childNodes.item(i);
            if (n.nodeType != 1) {
                // Ignore text nodes and non-element nodes
                continue;
            }
            if (n.tagName == &quot;traits&quot;) {
                // ignore the traits node. We've already covered it.
                continue;
            }
            key = traits[j];
            val = this.readValue(n);
            j = j + 1;
            obj[key] = val;
            //&lt;debug&gt;
            if (j &gt; traits.length) {
                Ext.Error.raise(&quot;Too many items for object, not enough traits: &quot; + className);
            }
            //&lt;/debug&gt;
        }
        return obj;
    },

<span id='Ext-data-amf-XmlDecoder-method-readArray'>    /**
</span>     * Parses and returns an AMFX array.
     * @param {HTMLElement/XMLElement} node the array node
     * @return {Array} the deserialized array
     */
    readArray: function(node) {
        var arr=[],
            n,i,j,l,name, val, len, childnodes, cn;

        // register array in object references table before we parse, in case of circular references
        this.objectReferences.push(arr);

        len = parseInt(node.getAttributeNode('length').value);
        i = 0;
        // the length only accounts for the ordinal values. For the rest, we'll read them as ECMA key-value pairs
        for (l = 0; l &lt; node.childNodes.length; l++) {
            n = node.childNodes.item(l);
            if (n.nodeType != 1) {
                // Ignore text nodes and non-element nodes
                continue;
            }
            if (n.tagName == &quot;item&quot;) {
                // parse item node
                name = n.getAttributeNode('name').value;
                childnodes = n.childNodes;
                for (j = 0; j &lt; childnodes.length; j++) {
                    cn = childnodes.item(j);
                    if (cn.nodeType != 1) {
                        // Ignore text nodes and non-element nodes
                        continue;
                    }
                    val = this.readValue(cn);
                    break; // out of loop. We've found our value
                }
                arr[name] = val;
            } else {
                // ordinal node
                arr[i] = this.readValue(n);
                i++;
                //&lt;debug&gt;
                if (i &gt; len) {
                    Ext.Error.raise(&quot;Array has more items than declared length: &quot; + i + &quot; &gt; &quot; + len);
                }
                //&lt;/debug&gt;
            }
        }
        //&lt;debug&gt;
        if (i &lt; len) {
            Ext.Error.raise(&quot;Array has less items than declared length: &quot; + i + &quot; &lt; &quot; + len);
        }
        //&lt;/debug&gt;
        return arr;
    },

<span id='Ext-data-amf-XmlDecoder-method-readDictionary'>    /**
</span>     * Parses and returns an AMFX dictionary.
     * @param {HTMLElement/XMLElement} node the `&lt;dictionary&gt;` node
     * @return {Object} a javascript object with the dictionary value-pair elements
     */
    readDictionary: function(node) {
        // For now, handle regular objects
        var dict = {},
            key, val,
            i, j, n, len;

        len = parseInt(node.getAttribute('length'));
        // Register dictionary in the ref table, in case there's a cyclical reference coming
        this.objectReferences.push(dict);


        // now find pairs of keys and values
        key = null;
        val = null;
        j = 0;
        for (i = 0; i &lt; node.childNodes.length; i++) {
            n = node.childNodes.item(i);
            if (n.nodeType != 1) {
                // Ignore text nodes and non-element nodes
                continue;
            }
            if (!key) {
                key = this.readValue(n);
                continue; // next element is the value
            }
            val = this.readValue(n);
            j = j + 1;
            dict[key] = val;
            key = null;
            val = null;
        }
        //&lt;debug&gt;
        if (j != len) {
            Ext.Error.raise(&quot;Incorrect number of dictionary values: &quot; + j + &quot; != &quot; + len);
        }
        //&lt;/debug&gt;
        return dict;
    },


<span id='Ext-data-amf-XmlDecoder-method-convertObjectWithSourceField'>    /**
</span>     * Converts externalizable flex objects with a source array to a regular array.
     * @private
     */
    convertObjectWithSourceField: function(node) {
        var i, n, val;
        for (i = 0; i &lt; node.childNodes.length; i++) {
            n = node.childNodes.item(i);
            if (n.tagName == &quot;bytearray&quot;) {
                val = this.readValue(n);
                this.objectReferences.push(val);
                return val;
            }
        }
        return null; // we shouldn't reach here, but just in case
    },

<span id='Ext-data-amf-XmlDecoder-property-converters'>    /**
</span>     * Converters used in converting specific typed Flex classes to JavaScript usable form.
     * @private
     */

    converters: {
        'flex.messaging.io.ArrayCollection': function(decoder,node) {
            return decoder.convertObjectWithSourceField(node);
        },
        'mx.collections.ArrayList':  function(decoder,node) {
            return decoder.convertObjectWithSourceField(node);
        },
        'mx.collections.ArrayCollection':  function(decoder,node) {
            return decoder.convertObjectWithSourceField(node);
        }
    }
});
</pre>
</body>
</html>
