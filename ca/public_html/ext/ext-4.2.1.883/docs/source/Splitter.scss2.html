<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">.#{$prefix}collapse-el {
    cursor: $collapse-tool-cursor;
}

.#{$prefix}layout-split-left,
.#{$prefix}layout-split-right {
    top: 50%;
    margin-top: round(-$collapse-tool-size / 2);
    width: $splitter-size;
    height: $collapse-tool-size;
}

.#{$prefix}layout-split-top,
.#{$prefix}layout-split-bottom {
    left: 50%;
    width: $collapse-tool-size;
    height: $splitter-size;
    margin-left: round(-$collapse-tool-size / 2);
}

.#{$prefix}layout-split-left {
    background-image: theme-background-image('util/splitter/mini-left');
}

.#{$prefix}layout-split-right {
    background-image: theme-background-image('util/splitter/mini-right');
}

@if $include-rtl {
    .#{$prefix}rtl {
        &amp;.#{$prefix}layout-split-left {
            background-image: theme-background-image('util/splitter/mini-right');
        }

        &amp;.#{$prefix}layout-split-right {
            background-image: theme-background-image('util/splitter/mini-left');
        }
    }
}

.#{$prefix}layout-split-top {
    background-image: theme-background-image('util/splitter/mini-top');
}

.#{$prefix}layout-split-bottom {
    background-image: theme-background-image('util/splitter/mini-bottom');
}

.#{$prefix}splitter-collapsed {
    .#{$prefix}layout-split-left {
        background-image: theme-background-image('util/splitter/mini-right');
    }

    .#{$prefix}layout-split-right {
        background-image: theme-background-image('util/splitter/mini-left');
    }

    @if $include-rtl {
        .#{$prefix}rtl {
            &amp;.#{$prefix}layout-split-left {
                background-image: theme-background-image('util/splitter/mini-left');
            }

            &amp;.#{$prefix}layout-split-right {
                background-image: theme-background-image('util/splitter/mini-right');
            }
        }
    }

    .#{$prefix}layout-split-top {
        background-image: theme-background-image('util/splitter/mini-bottom');
    }

    .#{$prefix}layout-split-bottom {
        background-image: theme-background-image('util/splitter/mini-top');
    }
}

.#{$prefix}splitter-active {
    background-color: $splitter-active-background-color;
    @include opacity($splitter-active-opacity);

    .#{$prefix}collapse-el {
        @include opacity($splitter-active-collapse-tool-opacity);
    }
}
</pre>
</body>
</html>
