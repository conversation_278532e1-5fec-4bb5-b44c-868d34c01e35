<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-panel-Tool'>/**
</span> * This class is used to display small visual icons in the header of a panel. There are a set of
 * 25 icons that can be specified by using the {@link #type} config. The {@link #callback} config
 * can be used to provide a function that will respond to any click events. In general, this class
 * will not be instantiated directly, rather it will be created by specifying the {@link Ext.panel.Panel#tools}
 * configuration on the Panel itself.
 *
 *     @example
 *     Ext.create('Ext.panel.Panel', {
 *         width: 200,
 *         height: 200,
 *         renderTo: document.body,
 *         title: 'A Panel',
 *         tools: [{
 *             type: 'help',
 *             callback: function() {
 *                 // show help here
 *             }
 *         }, {
 *             itemId: 'refresh',
 *             type: 'refresh',
 *             hidden: true,
 *             callback: function() {
 *                 // do refresh
 *             }
 *         }, {
 *             type: 'search',
 *             callback: function (panel) {
 *                 // do search
 *                 panel.down('#refresh').show();
 *             }
 *         }]
 *     });
 *
 * The `callback` config was added in Ext JS 4.2.1 as an alternative to {@link #handler}
 * to provide a more convenient list of arguments. In Ext JS 4.2.1 it is also possible to
 * pass a method name instead of a direct function:
 * 
 *      tools: [{
 *          type: 'help',
 *          callback: 'onHelp',
 *          scope: this
 *      },
 *      ...
 * 
 * The `callback` (or `handler`) name is looked up on the `scope` which will also be the
 * `this` reference when the method is called.
 */
Ext.define('Ext.panel.Tool', {
    extend: 'Ext.Component',
    requires: ['Ext.tip.QuickTipManager'],
    alias: 'widget.tool',

<span id='Ext-panel-Tool-property-isTool'>    /**
</span>     * @property {Boolean} isTool
     * `true` in this class to identify an object as an instantiated Tool, or subclass thereof.
     */
    isTool: true,

<span id='Ext-panel-Tool-cfg-baseCls'>    baseCls: Ext.baseCSSPrefix + 'tool',
</span><span id='Ext-panel-Tool-cfg-disabledCls'>    disabledCls: Ext.baseCSSPrefix + 'tool-disabled',
</span>    
<span id='Ext-panel-Tool-cfg-toolPressedCls'>    /**
</span>     * @cfg
     * @private
     */
    toolPressedCls: Ext.baseCSSPrefix + 'tool-pressed',
<span id='Ext-panel-Tool-cfg-toolOverCls'>    /**
</span>     * @cfg
     * @private
     */
    toolOverCls: Ext.baseCSSPrefix + 'tool-over',

<span id='Ext-panel-Tool-property-ariaRole'>    ariaRole: 'button',
</span>
<span id='Ext-panel-Tool-property-childEls'>    childEls: [
</span>        'toolEl'
    ],

<span id='Ext-panel-Tool-cfg-renderTpl'>    renderTpl: [
</span>        '&lt;img role=&quot;presentation&quot; id=&quot;{id}-toolEl&quot; src=&quot;{blank}&quot; class=&quot;{baseCls}-img {baseCls}-{type}' +
            '{childElCls}&quot; role=&quot;presentation&quot;/&gt;'
    ],

<span id='Ext-panel-Tool-cfg-toolOwner'>    /**
</span>     * @cfg {Ext.Component} toolOwner
     * The owner to report to the `callback` method. Default is `null` for the `ownerCt`.
     * @since 4.2
     */
    toolOwner: null,

<span id='Ext-panel-Tool-cfg-callback'>    /**
</span>     * @cfg {Function} callback A function to execute when the tool is clicked.
     * @cfg {Ext.Component} callback.owner The logical owner of the tool. In a typical
     * `Ext.panel.Panel`, this is set to the owning panel. This value comes from the
     * `toolOwner` config.
     * @cfg {Ext.panel.Tool} callback.tool The tool that is calling.
     * @cfg {Ext.EventObject} callback.event The click event.
     * @since 4.2
     */

<span id='Ext-panel-Tool-cfg-handler'>    /**
</span>     * @cfg {Function} handler
     * A function to execute when the tool is clicked. Arguments passed are:
     *
     * - **event** : Ext.EventObject - The click event.
     * - **toolEl** : Ext.Element - The tool Element.
     * - **owner** : Ext.panel.Header - The host panel header.
     * - **tool** : Ext.panel.Tool - The tool object
     */

<span id='Ext-panel-Tool-cfg-scope'>    /**
</span>     * @cfg {Object} scope
     * The scope to execute the {@link #callback} or {@link #handler} function. Defaults
     * to the tool.
     */

<span id='Ext-panel-Tool-cfg-type'>    /**
</span>     * @cfg {String} type
     * The type of tool to render. The following types are available:
     *
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-close&quot;&gt;&lt;/span&gt; close
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-minimize&quot;&gt;&lt;/span&gt; minimize
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-maximize&quot;&gt;&lt;/span&gt; maximize
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-restore&quot;&gt;&lt;/span&gt; restore
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-toggle&quot;&gt;&lt;/span&gt; toggle
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-gear&quot;&gt;&lt;/span&gt; gear
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-prev&quot;&gt;&lt;/span&gt; prev
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-next&quot;&gt;&lt;/span&gt; next
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-pin&quot;&gt;&lt;/span&gt; pin
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-unpin&quot;&gt;&lt;/span&gt; unpin
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-right&quot;&gt;&lt;/span&gt; right
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-left&quot;&gt;&lt;/span&gt; left
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-down&quot;&gt;&lt;/span&gt; down
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-up&quot;&gt;&lt;/span&gt; up
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-refresh&quot;&gt;&lt;/span&gt; refresh
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-plus&quot;&gt;&lt;/span&gt; plus
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-minus&quot;&gt;&lt;/span&gt; minus
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-search&quot;&gt;&lt;/span&gt; search
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-save&quot;&gt;&lt;/span&gt; save
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-help&quot;&gt;&lt;/span&gt; help
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-print&quot;&gt;&lt;/span&gt; print
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-expand&quot;&gt;&lt;/span&gt; expand
     * - &lt;span class=&quot;x-tool&quot;&gt;&lt;img src=&quot;data:image/gif;base64,R0lGODlhAQABAID/AMDAwAAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==&quot; class=&quot;x-tool-collapse&quot;&gt;&lt;/span&gt; collapse
     */

<span id='Ext-panel-Tool-cfg-tooltip'>    /**
</span>     * @cfg {String/Object} tooltip
     * The tooltip for the tool - can be a string to be used as innerHTML (html tags are accepted) or QuickTips config
     * object
     */

<span id='Ext-panel-Tool-cfg-tooltipType'>     /**
</span>     * @cfg {String} tooltipType
     * The type of tooltip to use. Either 'qtip' (default) for QuickTips or 'title' for title attribute.
     */
    tooltipType: 'qtip',

<span id='Ext-panel-Tool-cfg-stopEvent'>    /**
</span>     * @cfg {Boolean} stopEvent
     * Specify as false to allow click event to propagate.
     */
    stopEvent: true,

<span id='Ext-panel-Tool-cfg-height'>    // Tool size is fixed so that Box layout can avoid measurements.
</span>    height: 15,
<span id='Ext-panel-Tool-cfg-width'>    width: 15,
</span>
<span id='Ext-panel-Tool-property-_toolTypes'>    //&lt;debug&gt;
</span>    _toolTypes: {
        close:1,
        collapse:1,
        down:1,
        expand:1,
        gear:1,
        help:1,
        left:1,
        maximize:1,
        minimize:1,
        minus:1,
        //move:1,
        next:1,
        pin:1,
        plus:1,
        prev:1,
        print:1,
        refresh:1,
        //resize:1,
        restore:1,
        right:1,
        save:1,
        search:1,
        toggle:1,
        unpin:1,
        up:1
    },
<span id='Ext-panel-Tool-method-initComponent'>    //&lt;/debug&gt;
</span>
    initComponent: function() {
        var me = this;
        me.addEvents(
<span id='Ext-panel-Tool-event-click'>            /**
</span>             * @event click
             * Fires when the tool is clicked
             * @param {Ext.panel.Tool} this
             * @param {Ext.EventObject} e The event object
             */
            'click'
        );

        //&lt;debug&gt;
        if (me.id &amp;&amp; me._toolTypes[me.id] &amp;&amp; Ext.global.console) {
            Ext.global.console.warn('When specifying a tool you should use the type option, the id can conflict now that tool is a Component');
        }
        //&lt;/debug&gt;

        me.type = me.type || me.id;

        Ext.applyIf(me.renderData, {
            baseCls: me.baseCls,
            blank: Ext.BLANK_IMAGE_URL,
            type: me.type
        });

        // alias qtip, should use tooltip since it's what we have in the docs
        me.tooltip = me.tooltip || me.qtip;
        me.callParent();
    },

<span id='Ext-panel-Tool-method-afterRender'>    // inherit docs
</span>    afterRender: function() {
        var me = this,
            attr;

        me.callParent(arguments);

        me.el.on({
            click: me.onClick,
            mousedown: me.onMouseDown,
            mouseover: me.onMouseOver,
            mouseout: me.onMouseOut,
            scope: me
        });

        if (me.tooltip) {
            if (Ext.quickTipsActive &amp;&amp; Ext.isObject(me.tooltip)) {
                Ext.tip.QuickTipManager.register(Ext.apply({
                    target: me.id
                }, me.tooltip));
            }
            else {
                attr = me.tooltipType == 'qtip' ? 'data-qtip' : 'title';
                me.el.dom.setAttribute(attr, me.tooltip);
            }
        }
    },

<span id='Ext-panel-Tool-method-getFocusEl'>    getFocusEl: function() {
</span>        return this.el;
    },

<span id='Ext-panel-Tool-method-setType'>    /**
</span>     * Sets the type of the tool. Allows the icon to be changed.
     * @param {String} type The new type. See the {@link #type} config.
     * @return {Ext.panel.Tool} this
     */
    setType: function(type) {
        var me = this,
            oldType = me.type;

        me.type = type;
        if (me.rendered) {
            if (oldType) {
                me.toolEl.removeCls(me.baseCls + '-' + oldType);
            }
            me.toolEl.addCls(me.baseCls + '-' + type);
        } else {
            me.renderData.type = type;
        }
        return me;
    },

<span id='Ext-panel-Tool-method-onClick'>    /**
</span>     * Called when the tool element is clicked
     * @private
     * @param {Ext.EventObject} e
     * @param {HTMLElement} target The target element
     */
    onClick: function(e, target) {
        var me = this;

        if (me.disabled) {
            return false;
        }

        //remove the pressed + over class
        me.el.removeCls(me.toolPressedCls);
        me.el.removeCls(me.toolOverCls);

        if (me.stopEvent !== false) {
            e.stopEvent();
        }

        if (me.handler) {
            Ext.callback(me.handler, me.scope || me, [e, target, me.ownerCt, me]);
        } else if (me.callback) {
            Ext.callback(me.callback, me.scope || me, [me.toolOwner || me.ownerCt, me, e]);
        }
        me.fireEvent('click', me, e);
        return true;
    },

<span id='Ext-panel-Tool-method-onDestroy'>    // inherit docs
</span>    onDestroy: function(){
        if (Ext.quickTipsActive &amp;&amp; Ext.isObject(this.tooltip)) {
            Ext.tip.QuickTipManager.unregister(this.id);
        }
        this.callParent();
    },

<span id='Ext-panel-Tool-method-onMouseDown'>    /**
</span>     * Called when the user presses their mouse button down on a tool
     * Adds the press class ({@link #toolPressedCls})
     * @private
     */
    onMouseDown: function() {
        if (this.disabled) {
            return false;
        }

        this.el.addCls(this.toolPressedCls);
    },

<span id='Ext-panel-Tool-method-onMouseOver'>    /**
</span>     * Called when the user rolls over a tool
     * Adds the over class ({@link #toolOverCls})
     * @private
     */
    onMouseOver: function() {
        if (this.disabled) {
            return false;
        }
        this.el.addCls(this.toolOverCls);
    },

<span id='Ext-panel-Tool-method-onMouseOut'>    /**
</span>     * Called when the user rolls out from a tool.
     * Removes the over class ({@link #toolOverCls})
     * @private
     */
    onMouseOut: function() {
        this.el.removeCls(this.toolOverCls);
    }
});
</pre>
</body>
</html>
