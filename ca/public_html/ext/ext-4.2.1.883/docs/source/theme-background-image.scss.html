<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">/*
 * Method which inserts a full background-image property for a theme image.
 * It checks if the file exists and if it doesn't, it'll throw an error.
 * By default it will not include the background-image property if it is not found,
 * but this can be changed by changing the default value of $include-missing-images to
 * be true.
 */
@function theme-background-image($path, $extension: $image-extension, $nocheck: false) {
    $path: file_join($relative-image-path-for-uis, $path + '.' + $extension);
    $background-image: none;

    @if $path {
        @if $nocheck {
            $background-image: $path;
        } @else if theme_image_exists($image-search-path, $path) {
            $background-image: $path;
        } @else {
            @warn &quot;@theme-background-image: Theme image not found: #{$path}&quot;;
            @if $include-missing-images {
                $background-image: $path;
            }
        }

        @if $background-image != none {
            $background-image: url(file_join($theme-resource-path, $path));
        }
    } @else {
        @warn &quot;@theme-background-image: No arguments passed&quot;;
    }

    @return $background-image;
}
</pre>
</body>
</html>
