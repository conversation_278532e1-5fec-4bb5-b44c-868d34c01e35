<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-tab-Tab-css_mixin-extjs-tab-ui'>/**
</span> * Creates a visual theme for a Tab
 *
 * @param {string} $ui
 * The name of the UI being created. Can not included spaces or special punctuation
 * (used in CSS class names).
 *
 * @param {color} [$ui-background-color=$tab-base-color]
 * The background-color of Tabs
 *
 * @param {color} [$ui-background-color-over=$tab-base-color-over]
 * The background-color of hovered Tabs
 *
 * @param {color} [$ui-background-color-active=$tab-base-color-active]
 * The background-color of the active Tab
 *
 * @param {color} [$ui-background-color-disabled=$tab-base-color-disabled]
 * The background-color of disabled Tabs
 *
 * @param {list} [$ui-border-radius=$tab-border-radius]
 * The border-radius of Tabs
 *
 * @param {number} [$ui-border-width=$tab-border-width]
 * The border-width of Tabs
 *
 * @param {number/list} [$ui-margin=$tab-margin]
 * The border-width of Tabs
 *
 * @param {number/list} [$ui-padding=$tab-padding]
 * The padding of Tabs
 *
 * @param {number/list} [$ui-text-padding=$tab-text-padding]
 * The padding of the Tab's text element
 *
 * @param {color} [$ui-border-color=$tab-border-color]
 * The border-color of Tabs
 *
 * @param {color} [$ui-border-color-over=$tab-border-color-over]
 * The border-color of hovered Tabs
 *
 * @param {color} [$ui-border-color-active=$tab-border-color-active]
 * The border-color of the active Tab
 *
 * @param {color} [$ui-border-color-disabled=$tab-border-color-disabled]
 * The border-color of disabled Tabs
 *
 * @param {string} [$ui-cursor=$tab-cursor]
 * The Tab cursor
 *
 * @param {string} [$ui-cursor-disabled=$tab-cursor-disabled]
 * The cursor of disabled Tabs
 *
 * @param {number} [$ui-font-size=$tab-font-size]
 * The font-size of Tabs
 *
 * @param {number} [$ui-font-size-over=$tab-font-size-over]
 * The font-size of hovered Tabs
 *
 * @param {number} [$ui-font-size-active=$tab-font-size-active]
 * The font-size of the active Tab
 *
 * @param {number} [$ui-font-size-disabled=$tab-font-size-disabled]
 * The font-size of disabled Tabs
 *
 * @param {string} [$ui-font-weight=$tab-font-weight]
 * The font-weight of Tabs
 *
 * @param {string} [$ui-font-weight-over=$tab-font-weight-over]
 * The font-weight of hovered Tabs
 *
 * @param {string} [$ui-font-weight-active=$tab-font-weight-active]
 * The font-weight of the active Tab
 *
 * @param {string} [$ui-font-weight-disabled=$tab-font-weight-disabled]
 * The font-weight of disabled Tabs
 *
 * @param {string} [$ui-font-family=$tab-font-family]
 * The font-family of Tabs
 *
 * @param {string} [$ui-font-family-over=$tab-font-family-over]
 * The font-family of hovered Tabs
 *
 * @param {string} [$ui-font-family-active=$tab-font-family-active]
 * The font-family of the active Tab
 *
 * @param {string} [$ui-font-family-disabled=$tab-font-family-disabled]
 * The font-family of disabled Tabs
 *
 * @param {number} [$ui-line-height=$tab-line-height]
 * The line-height of Tabs
 *
 * @param {color} [$ui-color=$tab-color]
 * The text color of Tabs
 *
 * @param {color} [$ui-color-over=$tab-color-over]
 * The text color of hovered Tabs
 *
 * @param {color} [$ui-color-active=$tab-color-active]
 * The text color of the active Tab
 *
 * @param {color} [$ui-color-disabled=$tab-color-disabled]
 * The text color of disabled Tabs
 *
 * @param {string/list} [$ui-background-gradient=$tab-background-gradient]
 * The background-gradient for Tabs. Can be either the name of a predefined gradient
 * or a list of color stops. Used as the `$type` parameter for 
 * {@link Global_CSS#background-gradient}.
 *
 * @param {string/list} [$ui-background-gradient-over=$tab-background-gradient-over]
 * The background-gradient for hovered Tabs. Can be either the name of a predefined gradient
 * or a list of color stops. Used as the `$type` parameter for 
 * {@link Global_CSS#background-gradient}.
 *
 * @param {string/list} [$ui-background-gradient-active=$tab-background-gradient-active]
 * The background-gradient for the active Tab. Can be either the name of a predefined gradient
 * or a list of color stops. Used as the `$type` parameter for 
 * {@link Global_CSS#background-gradient}.
 *
 * @param {string/list} [$ui-background-gradient-disabled=$tab-background-gradient-disabled]
 * The background-gradient for disabled Tabs. Can be either the name of a predefined gradient
 * or a list of color stops. Used as the `$type` parameter for 
 * {@link Global_CSS#background-gradient}.
 *
 * @param {number} [$ui-inner-border-width=$tab-inner-border-width]
 * The inner border-width of Tabs
 *
 * @param {color} [$ui-inner-border-color=$tab-inner-border-color]
 * The inner border-color of Tabs
 *
 * @param {number} [$ui-icon-width=$tab-icon-width]
 * The width of the Tab close icon
 *
 * @param {number} [$ui-icon-height=$tab-icon-height]
 * The height of the Tab close icon
 *
 * @param {number} [$ui-icon-spacing=$tab-icon-spacing]
 * the space in between the text and the close button
 *
 * @param {list} [$ui-icon-background-position=$tab-icon-background-position]
 * The background-position of Tab icons
 *
 * @param {color} [$ui-glyph-color=$tab-glyph-color]
 * The color of Tab glyph icons
 *
 * @param {color} [$ui-glyph-color-over=$tab-glyph-color-over]
 * The color of a Tab glyph icon when the Tab is hovered
 *
 * @param {color} [$ui-glyph-color-active=$tab-glyph-color-active]
 * The color of a Tab glyph icon when the Tab is active
 *
 * @param {color} [$ui-glyph-color-disabled=$tab-glyph-color-disabled]
 * The color of a Tab glyph icon when the Tab is disabled
 *
 * @param {number} [$ui-glyph-opacity=$tab-glyph-opacity]
 * The opacity of a Tab glyph icon
 *
 * @param {number} [$ui-glyph-opacity-disabled=$tab-glyph-opacity-disabled]
 * The opacity of a Tab glyph icon when the Tab is disabled
 *
 * @param {number} [$ui-opacity-disabled=$tab-opacity-disabled]
 * opacity to apply to the tab's main element when the tab is disabled
 *
 * @param {number} [$ui-text-opacity-disabled=$tab-text-opacity-disabled]
 * opacity to apply to the tab's text element when the tab is disabled
 *
 * @param {number} [$ui-icon-opacity-disabled=$tab-icon-opacity-disabled]
 * opacity to apply to the tab's icon element when the tab is disabled
 *
 * @param {number} [$ui-closable-icon-width=$tab-closable-icon-width]
 * The width of the Tab close icon
 *
 * @param {number} [$ui-closable-icon-height=$tab-closable-icon-height]
 * The height of the Tab close icon
 *
 * @param {number} [$ui-closable-icon-top=$tab-closable-icon-top]
 * The distance to offset the Tab close icon from the top of the tab
 *
 * @param {number} [$ui-closable-icon-right=$tab-closable-icon-right]
 * The distance to offset the Tab close icon from the right of the tab
 *
 * @param {number} [$ui-closable-icon-spacing=$tab-closable-icon-spacing]
 * The space in between the text and the close button
 *
 * @param {color} [$ui-border-bottom-color=$tabbar-strip-border-color]
 * The bottom border color of inactive tabs.
 *
 * @member Ext.tab.Tab
 */ 
@mixin extjs-tab-ui(
    $ui,
    
    $ui-background-color: $tab-base-color,
    $ui-background-color-over: $tab-base-color-over,
    $ui-background-color-active: $tab-base-color-active,
    $ui-background-color-disabled: $tab-base-color-disabled,
    $ui-border-radius: $tab-border-radius,
    $ui-border-width: $tab-border-width,
    $ui-margin: $tab-margin,
    $ui-padding: $tab-padding,
    $ui-text-padding: $tab-text-padding,
    $ui-border-color: $tab-border-color,
    $ui-border-color-over: $tab-border-color-over,
    $ui-border-color-active: $tab-border-color-active,
    $ui-border-color-disabled: $tab-border-color-disabled,
    $ui-cursor: $tab-cursor,
    $ui-cursor-disabled: $tab-cursor-disabled,
    $ui-font-size: $tab-font-size,
    $ui-font-size-over: $tab-font-size-over,
    $ui-font-size-active: $tab-font-size-active,
    $ui-font-size-disabled: $tab-font-size-disabled,
    $ui-font-weight: $tab-font-weight,
    $ui-font-weight-over: $tab-font-weight-over,
    $ui-font-weight-active: $tab-font-weight-active,
    $ui-font-weight-disabled: $tab-font-weight-disabled,
    $ui-font-family: $tab-font-family,
    $ui-font-family-over: $tab-font-family-over,
    $ui-font-family-active: $tab-font-family-active,
    $ui-font-family-disabled: $tab-font-family-disabled,
    $ui-line-height: $tab-line-height,
    $ui-color: $tab-color,
    $ui-color-over: $tab-color-over,
    $ui-color-active: $tab-color-active,
    $ui-color-disabled: $tab-color-disabled,
    $ui-background-gradient: $tab-background-gradient,
    $ui-background-gradient-over: $tab-background-gradient-over,
    $ui-background-gradient-active: $tab-background-gradient-active,
    $ui-background-gradient-disabled: $tab-background-gradient-disabled,

    $ui-inner-border-width: $tab-inner-border-width,
    $ui-inner-border-color: $tab-inner-border-color,

    $ui-icon-width: $tab-icon-width,
    $ui-icon-height: $tab-icon-height,
    $ui-icon-spacing: $tab-icon-spacing,
    $ui-icon-background-position: $tab-icon-background-position,

    $ui-glyph-color: $tab-glyph-color,
    $ui-glyph-color-over: $tab-glyph-color-over,
    $ui-glyph-color-active: $tab-glyph-color-active,
    $ui-glyph-color-disabled: $tab-glyph-color-disabled,
    $ui-glyph-opacity: $tab-glyph-opacity,
    $ui-glyph-opacity-disabled: $tab-glyph-opacity-disabled,

    $ui-opacity-disabled: $tab-opacity-disabled,
    $ui-text-opacity-disabled: $tab-text-opacity-disabled,
    $ui-icon-opacity-disabled: $tab-icon-opacity-disabled,

    $ui-closable-icon-width: $tab-closable-icon-width,
    $ui-closable-icon-height: $tab-closable-icon-height,
    $ui-closable-icon-top: $tab-closable-icon-top,
    $ui-closable-icon-right: $tab-closable-icon-right,
    $ui-closable-icon-spacing: $tab-closable-icon-spacing,
    
    $ui-border-bottom-color: $tabbar-strip-border-color
) {
    $frame-border-width: $ui-border-width $ui-border-width 0 $ui-border-width;
    @include x-frame(
        $cls: 'tab',
        $ui: $ui + '-top',
        $border-radius: $ui-border-radius,
        $border-width: $frame-border-width,
        $background-color: $ui-background-color,
        $background-gradient: $ui-background-gradient,
        $background-direction: top,
        $padding: $ui-padding,
        $table: true
    );

    @include x-frame(
        $cls: 'tab',
        $ui: $ui + '-bottom',
        $border-radius: rotate180($ui-border-radius),
        $border-width: rotate180($frame-border-width),
        $background-color: $ui-background-color,
        $background-gradient: $ui-background-gradient,
        $background-direction: bottom,
        $padding: $ui-padding,
        $table: true
    );

    @include x-frame(
        $cls: 'tab',
        $ui: $ui + '-left',
        $border-radius: $ui-border-radius,
        $border-width: $frame-border-width,
        $background-color: $ui-background-color,
        $background-gradient: $ui-background-gradient,
        $background-direction: top,
        $padding: $ui-padding,
        $table: true,
        $img-ui: $ui + '-top'
    );

    @include x-frame(
        $cls: 'tab',
        $ui: $ui + '-right',
        $border-radius: $ui-border-radius,
        $border-width: $frame-border-width,
        $background-color: $ui-background-color,
        $background-gradient: $ui-background-gradient,
        $background-direction: top,
        $padding: $ui-padding,
        $table: true,
        $img-ui: $ui + '-top'
    );

    .#{$prefix}tab-#{$ui} {
        border-color: $ui-border-color;
        margin: $ui-margin;
        @if $ui-cursor != null { cursor: $ui-cursor; }

        .#{$prefix}tab-inner {
            font-size: $ui-font-size;
            font-weight: $ui-font-weight;
            font-family: $ui-font-family;
            color: $ui-color;
            line-height: $ui-line-height;
            @if $ui-text-padding != 0 {
                padding: $ui-text-padding;
            }
        }
        .#{$prefix}tab-icon-el {
            width: $ui-icon-width;
            height: $ui-icon-height;
            line-height: $ui-icon-height;
            background-position: $ui-icon-background-position;
        }

        .#{$prefix}tab-glyph {
            font-size: $ui-icon-height;
            color: $ui-glyph-color;
            @if $ui-glyph-opacity != 1 {
                // do not use the opacity mixin because we do not want IE's filter version of
                // opacity to be included.  We emulate the opacity setting in IE8m by mixing
                // the icon color into the background color. (see below)
                opacity: $ui-glyph-opacity;
            }
            // In IE8 and below when a glyph contains partially transparent pixels, we 
            // can't apply an opacity filter to the glyph element, because IE8m will render
            //  the partially transparent pixels of the glyph as black. To work around this,
            // we emulate the approximate color that the glyph would have if it had opacity
            // applied by mixing the glyph color with the tab's background-color.
            .#{$prefix}ie8m &amp; {
                color: mix($ui-glyph-color, $ui-background-color, $ui-glyph-opacity * 100);
            }
        }

        @if $include-ie {
            // EXTJSIV-8765: vertical tabs get shifted to the left when focused.  To work
            // around this, we move the left padding to the tab-button el.
            .#{$prefix}strict .#{$prefix}ie9 .#{$prefix}tab-bar-vertical &amp; {
                padding-left: 0;

                .#{$prefix}tab-button {
                    padding-left: left($ui-padding);
                }

                .#{$prefix}tab-icon-el {
                    left: left($ui-padding);
                }
            }
        }
    }

    .#{$prefix}tab-#{$ui}-icon {
        .#{$prefix}tab-inner {
            // even though there is no text we set a width and padding as buttons shrink-wrap around this element
            width: $ui-icon-width;
        }
    }

    @if $include-rtl {
        .#{$prefix}rtl.#{$prefix}tab-#{$ui} {
            margin: rtl($ui-margin);            
        }
    }

    @if $include-rtl {
        .#{$prefix}rtl.#{$prefix}tab-#{$ui} {
            margin: rtl($ui-margin);
        }
    }

    .#{$prefix}tab-#{$ui}-left {
        margin: rtl($ui-margin);
    }

    @if $include-rtl {
        .#{$prefix}rtl.#{$prefix}tab-#{$ui}-left {
            margin: $ui-margin;
        }
    }

    .#{$prefix}tab-#{$ui}-top,
    .#{$prefix}tab-#{$ui}-left,
    .#{$prefix}tab-#{$ui}-right {
        border-bottom: $ui-border-width solid $ui-border-bottom-color;

        @if $ui-background-gradient != null {
            @include background-gradient($ui-background-color, $ui-background-gradient, top);
        
            @if not $supports-gradients or $compile-all {
                .#{$prefix}nlg &amp; {
                    background-image: slicer-background-image(tab-#{$ui}-top, 'tab/tab-#{$ui}-top-bg');
                }
            }
        }

        @if $ui-inner-border-width != 0 {
            @include inner-border(
                $width: $ui-inner-border-width,
                $color: $ui-inner-border-color
            );
        }
    }

    .#{$prefix}tab-#{$ui}-bottom {
        border-top: $ui-border-width solid $ui-border-bottom-color;

        @if $ui-background-gradient != null {
            @include background-gradient($ui-background-color, $ui-background-gradient, bottom);
        
            @if not $supports-gradients or $compile-all {
                .#{$prefix}nlg &amp; {
                    background-image: slicer-background-image(tab-#{$ui}-bottom, 'tab/tab-#{$ui}-bottom-bg');
                }
            }
        }

        @if $ui-inner-border-width != 0 {
            @include inner-border(
                $width: flip-vertical($ui-inner-border-width),
                $color: $ui-inner-border-color
            );
        }
    }

    .#{$prefix}tab-#{$ui}-left {
        @include rotate-element($angle: 270);

        @if $tab-left-rotate-direction == 'right' {
            .#{$prefix}tab-wrap {
                @include rotate-element($angle: 180);
            }
        }
    }

    @if $include-rtl {
        .#{$prefix}rtl.#{$prefix}tab-#{$ui}-left {
            @include rotate-element($angle: 90);
        }
    }

    .#{$prefix}tab-#{$ui}-right {
        @include rotate-element($angle: 90);

        @if $tab-right-rotate-direction == 'left' {
            .#{$prefix}tab-wrap {
                @include rotate-element($angle: 180);
            }
        }
    }

    @if $include-rtl {
        .#{$prefix}rtl.#{$prefix}tab-#{$ui}-right {
            @include rotate-element($angle: 270);
        }
    }

    .#{$prefix}tab-#{$ui}-icon-text-left {
        .#{$prefix}tab-inner {
            padding-left: $ui-icon-width + $ui-icon-spacing + left($ui-text-padding);
        }
    }

    @if $include-rtl {
        .#{$prefix}rtl.#{$prefix}tab-#{$ui}-icon-text-left {
            .#{$prefix}tab-inner {
                padding-left: 0;
                padding-right: $ui-icon-width + $ui-icon-spacing + left($ui-text-padding);
            }
        }
    }

    .#{$prefix}tab-#{$ui}-over {
        @if $ui-border-color-over != $ui-border-color {
            border-color: $ui-border-color-over;
        }

        @if $ui-background-color-over != $ui-background-color {
            background-color: $ui-background-color-over;
        }

        .#{$prefix}tab-inner {
            @if $ui-color-over != $ui-color {
                color: $ui-color-over;
            }
            @if $ui-font-weight-over != $ui-font-weight {
                font-weight: $ui-font-weight-over;
            }
            @if $ui-font-size-over != $ui-font-size {
                font-size: $ui-font-size-over;
            }
            @if $ui-font-family-over != $ui-font-family {
                font-family: $ui-font-family-over;
            }
        }

        .#{$prefix}tab-glyph {
            color: $ui-glyph-color-over;
            // In IE8 and below when a glyph contains partially transparent pixels, we 
            // can't apply an opacity filter to the glyph element, because IE8m will render
            //  the partially transparent pixels of the glyph as black. To work around this,
            // we emulate the approximate color that the glyph would have if it had opacity
            // applied by mixing the glyph color with the tab's background-color.
            .#{$prefix}ie8m &amp; {
                color: mix($ui-glyph-color-over, $ui-background-color-over, $ui-glyph-opacity * 100);
            }
        }
    }

    @if $ui-background-gradient-over != null {
        .#{$prefix}tab-#{$ui}-top-over,
        .#{$prefix}tab-#{$ui}-left-over,
        .#{$prefix}tab-#{$ui}-right-over {
            @include background-gradient($ui-background-color-over, $ui-background-gradient-over, top);
        
            @if not $supports-gradients or $compile-all {
                .#{$prefix}nlg &amp; {
                    background-image: slicer-background-image(tab-#{$ui}-top-over, 'tab/tab-#{$ui}-top-over-bg');
                }
            }
        }
        .#{$prefix}tab-#{$ui}-bottom-over {
            @include background-gradient($ui-background-color-over, $ui-background-gradient-over, bottom);

            @if not $supports-gradients or $compile-all {
                .#{$prefix}nlg &amp; {
                    background-image: slicer-background-image(tab-#{$ui}-bottom-over, 'tab/tab-#{$ui}-bottom-over-bg');
                }
            }
        }
    }

    .#{$prefix}tab-#{$ui}-active {
        @if $ui-border-color-active != $ui-border-color {
            border-color: $ui-border-color-active;
        }
        background-color: $ui-background-color-active;

        .#{$prefix}tab-inner {
            @if $ui-color-active != $ui-color {
                color: $ui-color-active;
            }
            @if $ui-font-weight-active != $ui-font-weight {
                font-weight: $ui-font-weight-active;
            }
            @if $ui-font-size-active != $ui-font-size {
                font-size: $ui-font-size-active;
            }
            @if $ui-font-family-active != $ui-font-family {
                font-family: $ui-font-family-active;
            }
        }

        .#{$prefix}tab-glyph {
            color: $ui-glyph-color-active;
            // In IE8 and below when a glyph contains partially transparent pixels, we 
            // can't apply an opacity filter to the glyph element, because IE8m will render
            //  the partially transparent pixels of the glyph as black. To work around this,
            // we emulate the approximate color that the glyph would have if it had opacity
            // applied by mixing the glyph color with the tab's background-color.
            .#{$prefix}ie8m &amp; {
                color: mix($ui-glyph-color-active, $ui-background-color-active, $ui-glyph-opacity * 100);
            }
        }
    }

    .#{$prefix}tab-#{$ui}-top-active,
    .#{$prefix}tab-#{$ui}-left-active,
    .#{$prefix}tab-#{$ui}-right-active {
        border-bottom: $ui-border-width solid $ui-background-color-active;
        @if $ui-background-gradient-active != null {
            @include background-gradient($ui-background-color-active, $ui-background-gradient-active, top);

            @if not $supports-gradients or $compile-all {
                .#{$prefix}nlg &amp; {
                    background-image: slicer-background-image(tab-#{$ui}-top-active, 'tab/tab-#{$ui}-top-active-bg');
                }
            }
        }
    }

    .#{$prefix}tab-#{$ui}-bottom-active {
        border-top: $ui-border-width solid $ui-background-color-active;
        @if $ui-background-gradient-active != null {
            @include background-gradient($ui-background-color-active, $ui-background-gradient-active, bottom);

            @if not $supports-gradients or $compile-all {
                .#{$prefix}nlg &amp; {
                    background-image: slicer-background-image(tab-#{$ui}-bottom-active, 'tab/tab-#{$ui}-bottom-active-bg');
                }
            }
        }
    }

    .#{$prefix}tab-#{$ui}-disabled {
        @if $ui-border-color-disabled != $ui-border-color {
            border-color: $ui-border-color-disabled;
        }

        @if $ui-opacity-disabled != 1 {
            @include opacity($ui-opacity-disabled);
        }

        @if $ui-cursor-disabled != null {
            cursor: $ui-cursor-disabled;
        }

        .#{$prefix}tab-inner {
            @if $ui-color-disabled != $ui-color {
                color: $ui-color-disabled;
            }
            @if $ui-font-weight-disabled != $ui-font-weight {
                font-weight: $ui-font-weight-disabled;
            }
            @if $ui-font-size-disabled != $ui-font-size {
                font-size: $ui-font-size-disabled;
            }
            @if $ui-font-family-disabled != $ui-font-family {
                font-family: $ui-font-family-disabled;
            }
            @if $ui-text-opacity-disabled != 1 {
                @include opacity($ui-text-opacity-disabled);
            }
        }
        
        @if $ui-icon-opacity-disabled != 1 {
            .#{$prefix}tab-icon-el {
                @include opacity($ui-icon-opacity-disabled);
            }
        }

        .#{$prefix}tab-glyph {
            color: $ui-glyph-color-disabled;
            @if $ui-glyph-opacity-disabled != 1 {
                // do not use the opacity mixin because we do not want IE's filter version of
                // opacity to be included.  We emulate the opacity setting in IE8m by mixing
                // the icon color into the background color. (see below)
                opacity: $ui-glyph-opacity-disabled;
            }
            filter: none; // override opacity from tab-icon-el
            // In IE8 and below when a glyph contains partially transparent pixels, we 
            // can't apply an opacity filter to the glyph element, because IE8m will render
            //  the partially transparent pixels of the glyph as black. To work around this,
            // we emulate the approximate color that the glyph would have if it had opacity
            // applied by mixing the glyph color with the tab's background-color.
            .#{$prefix}ie8m &amp; {
                color: mix($ui-glyph-color-disabled, $ui-background-color-disabled, $ui-glyph-opacity-disabled * 100);
            }
        }
    }

    $disabled-border-color: $ui-border-color-disabled $ui-border-color-disabled $ui-border-bottom-color;
    .#{$prefix}tab-#{$ui}-top-disabled,
    .#{$prefix}tab-#{$ui}-left-disabled,
    .#{$prefix}tab-#{$ui}-right-disabled {
        border-color: $disabled-border-color;
    }

    .#{$prefix}tab-#{$ui}-bottom-disabled {
        border-color: rotate180($disabled-border-color);
    }

    @if $ui-background-gradient-disabled != null {
        .#{$prefix}tab-#{$ui}-top-disabled,
        .#{$prefix}tab-#{$ui}-left-disabled,
        .#{$prefix}tab-#{$ui}-right-disabled {
            @include background-gradient($ui-background-color-disabled, $ui-background-gradient-disabled, top);

            @if not $supports-gradients or $compile-all {
                .#{$prefix}nlg &amp; {
                    background-image: slicer-background-image(tab-#{$ui}-top-disabled, 'tab/tab-#{$ui}-top-disabled-bg');
                }
            }
        }
        .#{$prefix}tab-#{$ui}-bottom-disabled {
            @include background-gradient($ui-background-color-disabled, $ui-background-gradient-disabled, bottom);

            @if not $supports-gradients or $compile-all {
                .#{$prefix}nlg &amp; {
                    background-image: slicer-background-image(tab-#{$ui}-bottom-disabled, 'tab/tab-#{$ui}-bottom-disabled-bg');
                }
            }
        }
    }

    @if not $supports-border-radius or $compile-all {
        .#{$prefix}nbr .#{$prefix}tab-#{$ui} {
            background-image: none;
        }
        .#{$prefix}tab-#{$ui}-top-over,
        .#{$prefix}tab-#{$ui}-left-over,
        .#{$prefix}tab-#{$ui}-right-over {
            .#{$prefix}frame-tl,
            .#{$prefix}frame-bl,
            .#{$prefix}frame-tr,
            .#{$prefix}frame-br,
            .#{$prefix}frame-tc,
            .#{$prefix}frame-bc {
                background-image: slicer-corner-sprite(tab-#{$ui}-top-over, 'tab/tab-#{$ui}-top-over-corners');
            }
            .#{$prefix}frame-ml,
            .#{$prefix}frame-mr {
                background-image: slicer-sides-sprite(tab-#{$ui}-top-over, 'tab/tab-#{$ui}-top-over-sides');
            }
            .#{$prefix}frame-mc {
                background-color: $ui-background-color-over;
                @if $ui-background-gradient-over != null {
                    background-repeat: repeat-x;
                    background-image: slicer-frame-background-image(tab-#{$ui}-top-over, 'tab/tab-#{$ui}-top-over-fbg');
                }
            }
        }

        .#{$prefix}tab-#{$ui}-bottom-over {
            .#{$prefix}frame-tl,
            .#{$prefix}frame-bl,
            .#{$prefix}frame-tr,
            .#{$prefix}frame-br,
            .#{$prefix}frame-tc,
            .#{$prefix}frame-bc {
                background-image: slicer-corner-sprite(tab-#{$ui}-bottom-over, 'tab/tab-#{$ui}-bottom-over-corners');
            }
            .#{$prefix}frame-ml,
            .#{$prefix}frame-mr {
                background-image: slicer-sides-sprite(tab-#{$ui}-bottom-over, 'tab/tab-#{$ui}-bottom-over-sides');
            }
            .#{$prefix}frame-mc {
                background-color: $ui-background-color-over;
                @if $ui-background-gradient-over != null {
                    background-repeat: repeat-x;
                    background-image: slicer-frame-background-image(tab-#{$ui}-bottom-over, 'tab/tab-#{$ui}-bottom-over-fbg');
                }
            }
        }

        .#{$prefix}tab-#{$ui}-top-active,
        .#{$prefix}tab-#{$ui}-left-active,
        .#{$prefix}tab-#{$ui}-right-active {
            .#{$prefix}frame-tl,
            .#{$prefix}frame-bl,
            .#{$prefix}frame-tr,
            .#{$prefix}frame-br,
            .#{$prefix}frame-tc,
            .#{$prefix}frame-bc {
                background-image: slicer-corner-sprite(tab-#{$ui}-top-active, 'tab/tab-#{$ui}-top-active-corners');
            }
            .#{$prefix}frame-ml,
            .#{$prefix}frame-mr {
                background-image: slicer-sides-sprite(tab-#{$ui}-top-active, 'tab/tab-#{$ui}-top-active-sides');
            }
            .#{$prefix}frame-mc {
                background-color: $ui-background-color-active;
                @if $ui-background-gradient-active != null {
                    background-repeat: repeat-x;
                    background-image: slicer-frame-background-image(tab-#{$ui}-top-active, 'tab/tab-#{$ui}-top-active-fbg');
                }
            }
        }

        .#{$prefix}tab-#{$ui}-bottom-active {
            .#{$prefix}frame-tl,
            .#{$prefix}frame-bl,
            .#{$prefix}frame-tr,
            .#{$prefix}frame-br,
            .#{$prefix}frame-tc,
            .#{$prefix}frame-bc {
                background-image: slicer-corner-sprite(tab-#{$ui}-bottom-active, 'tab/tab-#{$ui}-bottom-active-corners');
            }
            .#{$prefix}frame-ml,
            .#{$prefix}frame-mr {
                background-image: slicer-sides-sprite(tab-#{$ui}-bottom-active, 'tab/tab-#{$ui}-bottom-active-sides');
            }
            .#{$prefix}frame-mc {
                background-color: $ui-background-color-active;
                @if $ui-background-gradient-active != null {
                    background-repeat: repeat-x;
                    background-image: slicer-frame-background-image(tab-#{$ui}-bottom-active, 'tab/tab-#{$ui}-bottom-active-fbg');
                }
            }
        }

        .#{$prefix}tab-#{$ui}-top-disabled,
        .#{$prefix}tab-#{$ui}-left-disabled,
        .#{$prefix}tab-#{$ui}-right-disabled {
            .#{$prefix}frame-tl,
            .#{$prefix}frame-bl,
            .#{$prefix}frame-tr,
            .#{$prefix}frame-br,
            .#{$prefix}frame-tc,
            .#{$prefix}frame-bc {
                background-image: slicer-corner-sprite(tab-#{$ui}-top-disabled, 'tab/tab-#{$ui}-top-disabled-corners');
            }
            .#{$prefix}frame-ml,
            .#{$prefix}frame-mr {
                background-image: slicer-sides-sprite(tab-#{$ui}-top-disabled, 'tab/tab-#{$ui}-top-disabled-sides');
            }
            .#{$prefix}frame-mc {
                background-color: $ui-background-color-disabled;
                @if $ui-background-gradient-disabled != null {
                    background-repeat: repeat-x;
                    background-image: slicer-frame-background-image(tab-#{$ui}-top-disabled, 'tab/tab-#{$ui}-top-disabled-fbg');
                }
            }
        }

        .#{$prefix}tab-#{$ui}-bottom-disabled {
            .#{$prefix}frame-tl,
            .#{$prefix}frame-bl,
            .#{$prefix}frame-tr,
            .#{$prefix}frame-br,
            .#{$prefix}frame-tc,
            .#{$prefix}frame-bc {
                background-image: slicer-corner-sprite(tab-#{$ui}-bottom-disabled, 'tab/tab-#{$ui}-bottom-disabled-corners');
            }
            .#{$prefix}frame-ml,
            .#{$prefix}frame-mr {
                background-image: slicer-sides-sprite(tab-#{$ui}-bottom-disabled, 'tab/tab-#{$ui}-bottom-disabled-sides');
            }
            .#{$prefix}frame-mc {
                background-color: $ui-background-color-disabled;
                @if $ui-background-gradient-disabled != null {
                    background-repeat: repeat-x;
                    background-image: slicer-frame-background-image(tab-#{$ui}-bottom-disabled, 'tab/tab-#{$ui}-bottom-disabled-fbg');
                }
            }
        }

        @if $ui-border-width != 0 { 
            .#{$prefix}nbr {
                // frame.scss sets border:0 !important on the tab element in nbr mode.  We
                // need an override  with !important to add the bottom-border back since
                // tabs are special and need the bottom-border to hide the tab-strip's
                // border when the tab is active.
                // This needs to be wrapped in an x-nbr rule to be more specific than
                // the rule that removes the border in x-frame
                .#{$prefix}tab-#{$ui}-top, 
                .#{$prefix}tab-#{$ui}-left, 
                .#{$prefix}tab-#{$ui}-right { 
                    border-bottom-width: $ui-border-width !important;
                }
                .#{$prefix}tab-#{$ui}-bottom {
                    border-top-width: $ui-border-width !important;
                }
            }
        }
    }

    .#{$prefix}tab-#{$ui} {
        .#{$prefix}tab-close-btn {
            width: $ui-closable-icon-width;
            height: $ui-closable-icon-height;
            background-image: theme-background-image('tab/tab-#{$ui}-close');
            @if $tab-closable-icon-opacity != 1 {
                @include opacity($tab-closable-icon-opacity);
            }
        }

        .#{$prefix}tab-close-btn-over {
            @if  $tab-closable-icon-opacity-over != $tab-closable-icon-opacity {
                @include opacity($tab-closable-icon-opacity-over);
            }
            @if $tab-closable-icon-include-hover-background-position {
                background-position: (-$tab-closable-icon-width) 0;
            }
        }
    }

    .#{$prefix}tab-#{$ui} .#{$prefix}tab-close-btn {
        top: $ui-closable-icon-top;
        right: $ui-closable-icon-right;
    }

    @if $include-rtl {
        .#{$prefix}rtl.#{$prefix}tab-#{$ui} .#{$prefix}tab-close-btn {
            right: auto;
            left: $ui-closable-icon-right;
        }
    }

    @if $tab-left-rotate-direction == 'right' {
        .#{$prefix}tab-#{$ui}-left .#{$prefix}tab-close-btn {
            top: auto;
            right: auto;
            bottom: $ui-closable-icon-top;
            left: $ui-closable-icon-right;
        }

        @if $include-rtl {
            .#{$prefix}rtl.#{$prefix}tab-#{$ui}-left .#{$prefix}tab-close-btn {
                left: auto;
                right: $ui-closable-icon-right;
            }
        }
    }

    @if $tab-right-rotate-direction == 'left' {
        .#{$prefix}tab-#{$ui}-right .#{$prefix}tab-close-btn {
            top: auto;
            right: auto;
            bottom: $ui-closable-icon-top;
            left: $ui-closable-icon-right;
        }

        @if $include-rtl {
            .#{$prefix}rtl.#{$prefix}tab-#{$ui}-right .#{$prefix}tab-close-btn {
                left: auto;
                right: $ui-closable-icon-right;
            }
        }
    }

    .#{$prefix}tab-#{$ui}-disabled .#{$prefix}tab-close-btn {
        @if $tab-closable-icon-opacity-disabled != $tab-closable-icon-opacity or $tab-closable-icon-opacity-disabled != $tab-closable-icon-opacity-over {
            @include opacity($tab-closable-icon-opacity-disabled);
        }
        @if $tab-closable-icon-include-hover-background-position {
            background-position: 0 0;
        }
    }

    @if $tab-closable-icon-include-pressed-background-position {
        .#{$prefix}tab-#{$ui}-pressed .#{$prefix}tab-close-btn {
            background-position: (-$ui-closable-icon-width * 2) 0;
        }
    }

    .#{$prefix}tab-#{$ui}-closable .#{$prefix}tab-wrap {
        padding-right: $ui-closable-icon-width + $ui-closable-icon-spacing;
    }

    @if $include-rtl {
        .#{$prefix}rtl.#{$prefix}tab-#{$ui}-closable .#{$prefix}tab-wrap {
            padding-right: 0px;
            padding-left: $ui-closable-icon-width + $ui-closable-icon-spacing;
        }
    }

    @if $include-ie {
        .#{$prefix}ie9m {
            @if $tab-left-rotate-direction == 'right' {
                .#{$prefix}tab-#{$ui}-closable-left .#{$prefix}tab-icon-el {
                    left: auto;
                    right: 0;
                }

                @if $include-rtl {
                    .#{$prefix}rtl.#{$prefix}tab-#{$ui}-closable-left .#{$prefix}tab-icon-el {
                        right: auto;
                        left: 0;
                    }
                }
            }

            @if $tab-right-rotate-direction == 'left' {
                .#{$prefix}tab-#{$ui}-closable-right .#{$prefix}tab-icon-el {
                    left: auto;
                    right: 0;
                }

                @if $include-rtl {
                    .#{$prefix}rtl.#{$prefix}tab-#{$ui}-closable-right .#{$prefix}tab-icon-el {
                        right: auto;
                        left: 0;
                    }
                }
            }
        }
    }

    @include x-slicer(tab-#{$ui}-top-over);
    @include x-slicer(tab-#{$ui}-bottom-over);
    @include x-slicer(tab-#{$ui}-top-active);
    @include x-slicer(tab-#{$ui}-bottom-active);
    @include x-slicer(tab-#{$ui}-top-disabled);
    @include x-slicer(tab-#{$ui}-bottom-disabled);
}</pre>
</body>
</html>
