<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-toolbar-Toolbar'>/**
</span> * @class Ext.toolbar.Toolbar
 */

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-font-size'>/**
</span> * @var {number}
 * The default font-size of Toolbar text
 */
$toolbar-font-size: $font-size !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-background-color'>/**
</span> * @var {color}
 * The background-color of the Toolbar
 */
$toolbar-background-color: $base-color !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-background-gradient'>/**
</span> * @var {string/list}
 * The background-gradient of the Toolbar.  Can be either the name of a predefined gradient
 * or a list of color stops. Used as the `$type` parameter for {@link Global_CSS#background-gradient}.
 */
$toolbar-background-gradient: null !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-horizontal-spacing'>/**
</span> * @var {number}
 * The horizontal spacing of Toolbar items
 */
$toolbar-horizontal-spacing: 2px !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-vertical-spacing'>/**
</span> * @var {number}
 * The vertical spacing of Toolbar items
 */
$toolbar-vertical-spacing: 2px !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-footer-horizontal-spacing'>/**
</span> * @var {number}
 * The horizontal spacing of {@link Ext.panel.Panel#fbar footer} Toolbar items
 */
$toolbar-footer-horizontal-spacing: 6px !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-footer-vertical-spacing'>/**
</span> * @var {number}
 * The vertical spacing of {@link Ext.panel.Panel#fbar footer} Toolbar items
 */
$toolbar-footer-vertical-spacing: 2px !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-footer-background-color'>/**
</span> * @var {color}
 * The background-color of {@link Ext.panel.Panel#fbar footer} Toolbars
 */
$toolbar-footer-background-color: transparent !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-footer-border-width'>/**
</span> * @var {number}
 * The border-width of {@link Ext.panel.Panel#fbar footer} Toolbars
 */
$toolbar-footer-border-width: 0 !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-footer-margin'>/**
</span> * @var {number/list}
 * The margin of {@link Ext.panel.Panel#fbar footer} Toolbars
 */
$toolbar-footer-margin: 0 !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-border-color'>/**
</span> * @var {color}
 * The border-color of Toolbars
 */
$toolbar-border-color: $base-color !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-border-width'>/**
</span> * @var {number}
 * The border-width of Toolbars
 */
$toolbar-border-width: 1px !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-border-style'>/**
</span> * @var {string}
 * The border-style of Toolbars
 */
$toolbar-border-style: solid !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-spacer-width'>/**
</span> * @var {number}
 * The width of Toolbar {@link Ext.toolbar.Spacer Spacers}
 */
$toolbar-spacer-width: 2px !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-separator-color'>/**
</span> * @var {color}
 * The main border-color of Toolbar {@link Ext.toolbar.Separator Separators}
 */
$toolbar-separator-color: adjust-color($base-color, $hue: -1deg, $saturation: 100%, $lightness: -4.3%) !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-separator-highlight-color'>/**
</span> * @var {color}
 * The highlight border-color of Toolbar {@link Ext.toolbar.Separator Separators}
 */
$toolbar-separator-highlight-color: #fff !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-separator-horizontal-margin'>/**
</span> * @var {number/list}
 * The margin of {@link Ext.toolbar.Separator Separators} on a horizontally oriented Toolbar
 */
$toolbar-separator-horizontal-margin: 0 $toolbar-horizontal-spacing 0 0 !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-separator-horizontal-height'>/**
</span> * @var {number}
 * The height of {@link Ext.toolbar.Separator Separators} on a horizontally oriented Toolbar
 */
$toolbar-separator-horizontal-height: 14px !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-separator-horizontal-border-style'>/**
</span> * @var {string}
 * The border-style of {@link Ext.toolbar.Separator Separators} on a horizontally oriented Toolbar
 */
$toolbar-separator-horizontal-border-style: solid !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-separator-horizontal-border-width'>/**
</span> * @var {number}
 * The border-width of {@link Ext.toolbar.Separator Separators} on a horizontally oriented Toolbar
 */
$toolbar-separator-horizontal-border-width: 0 1px !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-separator-vertical-margin'>/**
</span> * @var {number/list}
 * The margin of {@link Ext.toolbar.Separator Separators} on a vertically oriented Toolbar
 */
$toolbar-separator-vertical-margin: 0 5px $toolbar-vertical-spacing !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-separator-vertical-border-style'>/**
</span> * @var {string}
 * The border-style of {@link Ext.toolbar.Separator Separators} on a vertically oriented Toolbar
 */
$toolbar-separator-vertical-border-style: solid none !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-separator-vertical-border-width'>/**
</span> * @var {number}
 * The border-width of {@link Ext.toolbar.Separator Separators} on a vertically oriented Toolbar
 */
$toolbar-separator-vertical-border-width: 1px 0 !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-text-font-family'>/**
</span> * @var {string}
 * The default font-family of Toolbar text
 */
$toolbar-text-font-family: $font-family !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-text-font-size'>/**
</span> * @var {number}
 * The default font-size of Toolbar text
 */
$toolbar-text-font-size: ceil($font-size * .9) !default; //11px

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-text-font-weight'>/**
</span> * @var {number}
 * The default font-size of Toolbar text
 */
$toolbar-text-font-weight: normal !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-text-margin'>/**
</span> * @var {number/list}
 * The margin of Toolbar text
 */
$toolbar-text-margin: 0 6px 0 4px;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-text-color'>/**
</span> * @var {color}
 * The text-color of Toolbar text
 */
$toolbar-text-color: mix(desaturate(lighten($base-color, 37), 5), #000, 30) !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-text-padding'>/**
</span> * @var {number/list}
 * The padding of Toolbar text
 */
$toolbar-text-padding: 3px 4px 0 4px !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-text-line-height'>/**
</span> * @var {number}
 * The line-height of Toolbar text
 */
$toolbar-text-line-height: 16px !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-scroller-width'>/**
</span> * @var {number}
 * The width of Toolbar scrollers
 */
$toolbar-scroller-width: 14px !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-scroller-height'>/**
</span> * @var {number}
 * The height of Toolbar scrollers
 */
$toolbar-scroller-height: 22px !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-scroller-border-color'>/**
</span> * @var {color}
 * The border-color of Toolbar scrollers
 */
$toolbar-scroller-border-color: #8db2e3 !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-scroller-border-width'>/**
</span> * @var {number}
 * The border-width of Toolbar scrollers
 */
$toolbar-scroller-border-width: 0 0 1px !default;

// private
$toolbar-scroller-margin-top: 0 !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-scroller-cursor'>/**
</span> * @var {string}
 * The cursor of Toolbar scrollers
 */
$toolbar-scroller-cursor: pointer !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-scroller-cursor-disabled'>/**
</span> * @var {string}
 * The cursor of disabled Toolbar scrollers
 */
$toolbar-scroller-cursor-disabled: default !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-scroller-opacity-disabled'>/**
</span> * @var {number}
 * The opacity of disabled Toolbar scrollers
 */
$toolbar-scroller-opacity-disabled: 1 !default;

// private
$toolbar-scroll-left-background-x: -$toolbar-scroller-width !default;

<span id='Ext-toolbar-Toolbar-css_var-S-toolbar-tool-background-image'>/**
</span> * @var {string}
 * The sprite to use for {@link Ext.panel.Tool Tools} on a Toolbar
 */
$toolbar-tool-background-image: null !default;

<span id='Ext-toolbar-Toolbar-css_var-S-include-toolbar-default-ui'>/**
</span> * @var {boolean}
 * True to include the &quot;default&quot; toolbar UI
 */
$include-toolbar-default-ui: $include-default-uis !default;
</pre>
</body>
</html>
