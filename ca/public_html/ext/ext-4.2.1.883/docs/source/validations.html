<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-data-validations'>/**
</span> * <AUTHOR> Spencer
 *
 * This singleton contains a set of validation functions that can be used to validate any type of data. They are most
 * often used in {@link Ext.data.Model Models}, where they are automatically set up and executed.
 */
Ext.define('Ext.data.validations', {
    singleton: true,
    
<span id='Ext-data-validations-property-presenceMessage'>    /**
</span>     * @property {String} presenceMessage
     * The default error message used when a presence validation fails.
     */
    presenceMessage: 'must be present',
    
<span id='Ext-data-validations-property-lengthMessage'>    /**
</span>     * @property {String} lengthMessage
     * The default error message used when a length validation fails.
     */
    lengthMessage: 'is the wrong length',
    
<span id='Ext-data-validations-property-formatMessage'>    /**
</span>     * @property {String} formatMessage
     * The default error message used when a format validation fails.
     */
    formatMessage: 'is the wrong format',
    
<span id='Ext-data-validations-property-inclusionMessage'>    /**
</span>     * @property {String} inclusionMessage
     * The default error message used when an inclusion validation fails.
     */
    inclusionMessage: 'is not included in the list of acceptable values',
    
<span id='Ext-data-validations-property-exclusionMessage'>    /**
</span>     * @property {String} exclusionMessage
     * The default error message used when an exclusion validation fails.
     */
    exclusionMessage: 'is not an acceptable value',
    
<span id='Ext-data-validations-property-emailMessage'>    /**
</span>     * @property {String} emailMessage
     * The default error message used when an email validation fails
     */
    emailMessage: 'is not a valid email address',
    
<span id='Ext-data-validations-property-emailRe'>    /**
</span>     * @property {RegExp} emailRe
     * The regular expression used to validate email addresses
     */
    emailRe: /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/,
    
<span id='Ext-data-validations-method-presence'>    /**
</span>     * Validates that the given value is present.
     * For example:
     *
     *     validations: [{type: 'presence', field: 'age'}]
     *
     * @param {Object} config Config object
     * @param {Object} value The value to validate
     * @return {Boolean} True if validation passed
     */
    presence: function(config, value) {
        // No configs read, so allow just value to be passed
        if (arguments.length === 1) {
            value = config;
        }
        
        //we need an additional check for zero here because zero is an acceptable form of present data
        return !!value || value === 0 || value === false;
    },
    
<span id='Ext-data-validations-method-length'>    /**
</span>     * Returns true if the given value is between the configured min and max values.
     * For example:
     *
     *     validations: [{type: 'length', field: 'name', min: 2}]
     *
     * @param {Object} config Config object
     * @param {String} value The value to validate
     * @return {Boolean} True if the value passes validation
     */
    length: function(config, value) {
        if (value === undefined || value === null) {
            return false;
        }
        
        var length = value.length,
            min    = config.min,
            max    = config.max;
        
        if ((min &amp;&amp; length &lt; min) || (max &amp;&amp; length &gt; max)) {
            return false;
        } else {
            return true;
        }
    },
    
<span id='Ext-data-validations-method-email'>    /**
</span>     * Validates that an email string is in the correct format
     * @param {Object} config Config object
     * @param {String} email The email address
     * @return {Boolean} True if the value passes validation
     */
    email: function(config, email) {
        return Ext.data.validations.emailRe.test(email);
    },
    
<span id='Ext-data-validations-method-format'>    /**
</span>     * Returns true if the given value passes validation against the configured `matcher` regex.
     * For example:
     *
     *     validations: [{type: 'format', field: 'username', matcher: /([a-z]+)[0-9]{2,3}/}]
     *
     * @param {Object} config Config object
     * @param {String} value The value to validate
     * @return {Boolean} True if the value passes the format validation
     */
    format: function(config, value) {
        return !!(config.matcher &amp;&amp; config.matcher.test(value));
    },
    
<span id='Ext-data-validations-method-inclusion'>    /**
</span>     * Validates that the given value is present in the configured `list`.
     * For example:
     *
     *     validations: [{type: 'inclusion', field: 'gender', list: ['Male', 'Female']}]
     *
     * @param {Object} config Config object
     * @param {String} value The value to validate
     * @return {Boolean} True if the value is present in the list
     */
    inclusion: function(config, value) {
        return config.list &amp;&amp; Ext.Array.indexOf(config.list,value) != -1;
    },
    
<span id='Ext-data-validations-method-exclusion'>    /**
</span>     * Validates that the given value is not present in the configured `list`.
     * For example:
     *
     *     validations: [{type: 'exclusion', field: 'username', list: ['Admin', 'Operator']}]
     *
     * @param {Object} config Config object
     * @param {String} value The value to validate
     * @return {Boolean} True if the value is not present in the list
     */
    exclusion: function(config, value) {
        return config.list &amp;&amp; Ext.Array.indexOf(config.list,value) == -1;
    }
});</pre>
</body>
</html>
