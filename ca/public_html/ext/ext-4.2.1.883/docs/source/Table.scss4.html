<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-panel-Table'>/**
</span> * @class Ext.panel.Table
 */

<span id='Ext-panel-Table-css_var-S-grid-row-cell-color'>/**
</span> * @var {color}
 * The color of the text in the grid cells
 */
$grid-row-cell-color: null !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-font-size'>/**
</span> * @var {number}
 * The font size of the text in the grid cells
 */
$grid-row-cell-font-size: $font-size !default;
<span id='Ext-panel-Table-css_var-S-grid-row-cell-line-height'>/**
</span> * var {number} $grid-row-cell-line-height
 * The line-height of the text inside the grid cells.
 */
$grid-row-cell-line-height: round($grid-row-cell-font-size * 1.15) !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-font-weight'>/**
</span> * @var {string}
 * The font-weight of the text in the grid cells
 */
$grid-row-cell-font-weight: normal !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-font-family'>/**
</span> * @var {string}
 * The font-family of the text in the grid cells
 */
$grid-row-cell-font-family: $font-family !default;

// private
$grid-row-cell-font: $grid-row-cell-font-weight #{$grid-row-cell-font-size}/#{$grid-row-cell-line-height} $grid-row-cell-font-family !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-background-color'>/**
</span> * @var {color}
 * The background-color of the grid cells
 */
$grid-row-cell-background-color: #fff !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-border-color'>/**
</span> * @var {color}
 * The border-color of row/column borders. Can be specified as a single color, or as a list
 * of colors containing the row border color followed by the column border color.
 */
$grid-row-cell-border-color: #ededed !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-border-style'>/**
</span> * @var {string}
 * The border-style of the row/column borders.
 */
$grid-row-cell-border-style: solid !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-border-width'>/**
</span> * @var {number}
 * The border-width of the row and column borders.
 */
$grid-row-cell-border-width: 1px !default;

<span id='Ext-panel-Table-css_var-S-grid-cell-special-background-color'>/**
</span> * @var {color}
 * The background-color of &quot;special&quot; cells.  Special cells are created by {@link
 * Ext.grid.RowNumberer RowNumberer}, {@link Ext.selection.CheckboxModel Checkbox Selection
 * Model} and {@link Ext.grid.plugin.RowExpander RowExpander}.
 */
$grid-cell-special-background-color: $grid-row-cell-background-color !default;

<span id='Ext-panel-Table-css_var-S-grid-cell-special-background-gradient'>/**
</span> * @var {string}
 * The background-gradient to use for &quot;special&quot; cells.  Special cells are created by {@link
 * Ext.grid.RowNumberer RowNumberer}, {@link Ext.selection.CheckboxModel Checkbox Selection
 * Model} and {@link Ext.grid.plugin.RowExpander RowExpander}.
 */
$grid-cell-special-background-gradient: null !default;

<span id='Ext-panel-Table-css_var-S-grid-cell-special-border-width'>/**
</span> * @var {number}
 * The border-width of &quot;special&quot; cells.  Special cells are created by {@link
 * Ext.grid.RowNumberer RowNumberer}, {@link Ext.selection.CheckboxModel Checkbox Selection
 * Model} and {@link Ext.grid.plugin.RowExpander RowExpander}.
 * Only applies to the vertical border, since the row border width is determined by
 * {#$grid-row-cell-border-width}.
 */
$grid-cell-special-border-width: $grid-row-cell-border-width !default;

<span id='Ext-panel-Table-css_var-S-grid-cell-special-border-color'>/**
</span> * @var {color}
 * The border-color of &quot;special&quot; cells.  Special cells are created by {@link
 * Ext.grid.RowNumberer RowNumberer}, {@link Ext.selection.CheckboxModel Checkbox Selection
 * Model} and {@link Ext.grid.plugin.RowExpander RowExpander}.
 * Only applies to the vertical border, since the row border color is determined by
 * {#$grid-row-cell-border-color}.
 */
$grid-cell-special-border-color: $grid-row-cell-border-color !default;

<span id='Ext-panel-Table-css_var-S-grid-cell-special-border-style'>/**
</span> * @var {string}
 * The border-style of &quot;special&quot; cells.  Special cells are created by {@link
 * Ext.grid.RowNumberer RowNumberer}, {@link Ext.selection.CheckboxModel Checkbox Selection
 * Model} and {@link Ext.grid.plugin.RowExpander RowExpander}.
 * Only applies to the vertical border, since the row border style is determined by
 * {#$grid-row-cell-border-style}.
 */
$grid-cell-special-border-style: $grid-row-cell-border-style !default;

<span id='Ext-panel-Table-css_var-S-grid-cell-special-selected-border-color'>/**
</span> * @var {color}
 * The border-color of &quot;special&quot; cells when the row is selected using a {@link
 * Ext.selection.RowModel Row Selection Model}.  Special cells are created by {@link
 * Ext.grid.RowNumberer RowNumberer}, {@link Ext.selection.CheckboxModel Checkbox Selection
 * Model} and {@link Ext.grid.plugin.RowExpander RowExpander}.
 * Only applies to the vertical border, since the selected row border color is determined by
 * {#$grid-row-cell-selected-border-color}.
 */
$grid-cell-special-selected-border-color: $grid-row-cell-border-color !default;

<span id='Ext-panel-Table-css_var-S-grid-cell-special-over-background-color'>/**
</span> * @var {color} 
 * The background-color of &quot;special&quot; cells when the row is hovered. Special cells are
 * created by {@link Ext.grid.RowNumberer RowNumberer}, {@link Ext.selection.CheckboxModel
 * Checkbox Selection Model} and {@link Ext.grid.plugin.RowExpander RowExpander}.
 */
$grid-cell-special-over-background-color: $grid-row-cell-over-background-color !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-alt-background-color'>/**
</span> * @var {color}
 * The background-color color of odd-numbered rows when the table view is configured with
 * `{@link Ext.view.Table#stripeRows stripeRows}: true`.
 */
$grid-row-cell-alt-background-color: darken($grid-row-cell-background-color, 2) !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-over-border-style'>/**
</span> * @var {string}
 * The border-style of the hovered row
 */
$grid-row-cell-over-border-style: solid !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-over-color'>/**
</span> * @var {color}
 * The text color of the hovered row
 */
$grid-row-cell-over-color: $grid-row-cell-color !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-over-background-color'>/**
</span> * @var {color}
 * The background-color of the hovered row
 */
$grid-row-cell-over-background-color: #ddd !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-over-border-color'>/**
</span> * @var {color}
 * The border-color of the hovered row
 */
$grid-row-cell-over-border-color: $grid-row-cell-border-color !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-selected-border-style'>/**
</span> * @var {string}
 * The border-style of the selected row
 */
$grid-row-cell-selected-border-style: solid !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-selected-color'>/**
</span> * @var {color}
 * The text color of the selected row
 */
$grid-row-cell-selected-color: $grid-row-cell-color !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-selected-background-color'>/**
</span> * @var {color}
 * The background-color of the selected row
 */
$grid-row-cell-selected-background-color: #ccc !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-selected-border-color'>/**
</span> * @var {color}
 * The border-color of the selected row
 */
$grid-row-cell-selected-border-color: $grid-row-cell-border-color !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-focus-border-color'>/**
</span> * @var {color}
 * The border-color of the focused row
 */
$grid-row-cell-focus-border-color: #808080 !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-focus-border-style'>/**
</span> * @var {string}
 * The border-style of the focused row
 */
$grid-row-cell-focus-border-style: dotted !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-focus-color'>/**
</span> * @var {color}
 * The text color of the focused row
 */
$grid-row-cell-focus-color: $grid-row-cell-color !default;

<span id='Ext-panel-Table-css_var-S-grid-row-cell-focus-background-color'>/**
</span> * @var {color}
 * The background-color of the focused row
 */
$grid-row-cell-focus-background-color: $grid-row-cell-background-color !default;

<span id='Ext-panel-Table-css_var-S-grid-no-row-lines-show-focus-border'>/**
</span> * @var {boolean}
 * True to show the focus border when a row is focused even if the grid has no 
 * {@link Ext.panel.Table#rowLines rowLines}.  
 */
$grid-no-row-lines-show-focus-border: false !default;

<span id='Ext-panel-Table-css_var-S-grid-cell-selected-color'>/**
</span> * @var {color} 
 * The text color of a selected cell when using a {@link Ext.selection.CellModel
 * Cell Selection Model}.
 */
$grid-cell-selected-color: $grid-row-cell-selected-color !default;

<span id='Ext-panel-Table-css_var-S-grid-cell-selected-background-color'>/**
</span> * @var {color} 
 * The background-color of a selected cell when using a {@link Ext.selection.CellModel
 * Cell Selection Model}.
 */
$grid-cell-selected-background-color: $grid-row-cell-selected-background-color !default;

<span id='Ext-panel-Table-css_var-S-grid-cell-inner-padding'>/**
</span> * @var {number}
 * The amount of padding to apply to the grid cell's inner div element
 */
$grid-cell-inner-padding: 3px 6px !default;

<span id='Ext-panel-Table-css_var-S-grid-cell-inner-text-overflow'>/**
</span> * @var {string}
 * The type of text-overflow to use on the grid cell's inner div element
 */
$grid-cell-inner-text-overflow: ellipsis !default;

<span id='Ext-panel-Table-css_var-S-grid-body-border-color'>/**
</span> * @var {color}
 * The border-color of the grid body
 */
$grid-body-border-color: $panel-body-border-color !default;

<span id='Ext-panel-Table-css_var-S-grid-body-border-width'>/**
</span> * @var {number}
 * The border-width of the grid body border
 */
$grid-body-border-width: $panel-body-border-width !default;

<span id='Ext-panel-Table-css_var-S-grid-body-border-style'>/**
</span> * @var {string}
 * The border-style of the grid body border
 */
$grid-body-border-style: $panel-body-border-style !default;

<span id='Ext-panel-Table-css_var-S-grid-body-background-color'>/**
</span> * @var {color}
 * The background-color of the grid body
 */
$grid-body-background-color: $panel-body-background-color !default;

<span id='Ext-panel-Table-css_var-S-grid-empty-padding'>/**
</span> * @var {number}
 * The amount of padding to apply to the grid body when the grid contains no data.
 */
$grid-empty-padding: 10px !default;

<span id='Ext-panel-Table-css_var-S-grid-empty-color'>/**
</span> * @var {color}
 * The text color of the {@link Ext.view.Table#emptyText emptyText} in the grid body when
 * the grid contains no data.
 */
$grid-empty-color: #808080 !default;

<span id='Ext-panel-Table-css_var-S-grid-empty-background-color'>/**
</span> * @var {color}
 * The background color of the grid body when the grid contains no data.
 */
$grid-empty-background-color: $panel-body-background-color !default;

<span id='Ext-panel-Table-css_var-S-grid-empty-font-size'>/**
</span> * @var {number}
 * The font-size of the {@link Ext.view.Table#emptyText emptyText} in the grid body when
 * the grid contains no data.
 */
$grid-empty-font-size: $grid-row-cell-font-size !default;

<span id='Ext-panel-Table-css_var-S-grid-empty-font-weight'>/**
</span> * @var {number}
 * The font-weight of the {@link Ext.view.Table#emptyText emptyText} in the grid body when
 * the grid contains no data.
 */
$grid-empty-font-weight: normal !default;

<span id='Ext-panel-Table-css_var-S-grid-empty-font-family'>/**
</span> * @var {number}
 * The font-family of the {@link Ext.view.Table#emptyText emptyText} in the grid body when
 * the grid contains no data.
 */
$grid-empty-font-family: $font-family !default;

<span id='Ext-panel-Table-css_var-S-grid-resize-marker-background-color'>/**
</span> * @var {color}
 * The color of the resize markers that display when dragging a column border to resize
 * the column
 */
$grid-resize-marker-background-color: #0f0f0f !default;

// private
$grid-row-height: $grid-row-cell-line-height + vertical($grid-cell-inner-padding);</pre>
</body>
</html>
