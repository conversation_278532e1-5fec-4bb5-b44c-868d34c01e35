<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-form-field-Trigger'>/**
</span> * @class Ext.form.field.Trigger
 */

<span id='Ext-form-field-Trigger-css_var-S-form-trigger-width'>/**
</span> * @var {number}
 * The width of the Trigger field's trigger element
 */
$form-trigger-width: 22px !default;

<span id='Ext-form-field-Trigger-css_var-S-form-trigger-border-width'>/**
</span> * @var {number/list}
 * The width of the trigger's border
 */
$form-trigger-border-width: 0 !default;

<span id='Ext-form-field-Trigger-css_var-S-form-trigger-border-color'>/**
</span> * @var {color}
 * The color of the trigger's border
 */
$form-trigger-border-color: $form-field-border-color !default;

<span id='Ext-form-field-Trigger-css_var-S-form-trigger-border-style'>/**
</span> * @var {string}
 * The style of the trigger's border
 */
$form-trigger-border-style: $form-field-border-style !default;

<span id='Ext-form-field-Trigger-css_var-S-form-trigger-border-color-over'>/**
</span> * @var {color}
 * The color of the trigger's border when hovered
 */
$form-trigger-border-color-over: $form-field-border-color !default;

<span id='Ext-form-field-Trigger-css_var-S-form-trigger-border-color-focus'>/**
</span> * @var {color}
 * The color of the trigger's border when the field is focused
 */
$form-trigger-border-color-focus: $form-field-focus-border-color !default;

<span id='Ext-form-field-Trigger-css_var-S-form-trigger-border-color-pressed'>/**
</span> * @var {color}
 * The color of the trigger's border when the field is focused and the trigger is hovered
 */
$form-trigger-border-color-pressed: $form-trigger-border-color-over !default;</pre>
</body>
</html>
