<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-button-Split'>/**
</span> * A split button that provides a built-in dropdown arrow that can fire an event separately from the default click event
 * of the button. Typically this would be used to display a dropdown menu that provides additional options to the
 * primary button action, but any custom handler can provide the arrowclick implementation.  Example usage:
 *
 *     @example
 *     // display a dropdown menu:
 *     Ext.create('Ext.button.Split', {
 *         renderTo: Ext.getBody(),
 *         text: 'Options',
 *         // handle a click on the button itself
 *         handler: function() {
 *             alert(&quot;The button was clicked&quot;);
 *         },
 *         menu: new Ext.menu.Menu({
 *             items: [
 *                 // these will render as dropdown menu items when the arrow is clicked:
 *                 {text: 'Item 1', handler: function(){ alert(&quot;Item 1 clicked&quot;); }},
 *                 {text: 'Item 2', handler: function(){ alert(&quot;Item 2 clicked&quot;); }}
 *             ]
 *         })
 *     });
 *
 * Instead of showing a menu, you can provide any type of custom functionality you want when the dropdown
 * arrow is clicked:
 *
 *     Ext.create('Ext.button.Split', {
 *         renderTo: 'button-ct',
 *         text: 'Options',
 *         handler: optionsHandler,
 *         arrowHandler: myCustomHandler
 *     });
 *
 */
Ext.define('Ext.button.Split', {

    /* Begin Definitions */
    alias: 'widget.splitbutton',

    extend: 'Ext.button.Button',
    alternateClassName: 'Ext.SplitButton',
    /* End Definitions */
    
<span id='Ext-button-Split-cfg-arrowHandler'>    /**
</span>     * @cfg {Function} arrowHandler
     * A function called when the arrow button is clicked (can be used instead of click event)
     * @cfg {Ext.button.Split} arrowHandler.this
     * @cfg {Event} arrowHandler.e The click event.
     */
<span id='Ext-button-Split-cfg-arrowTooltip'>    /**
</span>     * @cfg {String} arrowTooltip
     * The title attribute of the arrow.
     */

<span id='Ext-button-Split-cfg-arrowCls'>    // @private
</span>    arrowCls      : 'split',
<span id='Ext-button-Split-property-split'>    split         : true,
</span>
<span id='Ext-button-Split-method-initComponent'>    // @private
</span>    initComponent : function(){
        this.callParent();
<span id='Ext-button-Split-event-arrowclick'>        /**
</span>         * @event arrowclick
         * Fires when this button's arrow is clicked.
         * @param {Ext.button.Split} this
         * @param {Event} e The click event.
         */
        this.addEvents(&quot;arrowclick&quot;);
    },

<span id='Ext-button-Split-method-setArrowHandler'>    /**
</span>     * Sets this button's arrow click handler.
     * @param {Function} handler The function to call when the arrow is clicked.
     * @param {Object} scope (optional) Scope for the function passed above.
     */
    setArrowHandler : function(handler, scope){
        this.arrowHandler = handler;
        this.scope = scope;
    },

<span id='Ext-button-Split-method-onClick'>    // @private
</span>    onClick : function(e, t) {
        var me = this;
        
        e.preventDefault();
        if (!me.disabled) {
            if (me.overMenuTrigger) {
                me.maybeShowMenu();
                me.fireEvent(&quot;arrowclick&quot;, me, e);
                if (me.arrowHandler) {
                    me.arrowHandler.call(me.scope || me, me, e);
                }
            } else {
                me.doToggle();
                me.fireHandler(e);
            }
        }
    }
});</pre>
</body>
</html>
