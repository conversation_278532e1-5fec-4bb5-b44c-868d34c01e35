<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">// rotates an element vertically using a css3 transform
@mixin rotate-element(
    $angle, // angle of rotation (90, 180, or 270)
    $include-ie: $include-ie,
    $background-color: null // bg color to use for ms chroma filter
) {
    $rotation-origin: null;
    $rotation: rotate(#{$angle}deg);
    $ie-rotation: null;
    @if $angle == 90 {
        $ie-rotation: 1;
        $rotation-origin: 0 0;
    } @else if $angle == 180 {
        $rotation-origin: 50% 50%;
        $ie-rotation: 2;
    } @else if $angle == 270 {
        $ie-rotation: 3;
        $rotation-origin: 100% 0;
    }

    -webkit-transform: $rotation; 
    -webkit-transform-origin: $rotation-origin;
    -moz-transform: $rotation;	
    -moz-transform-origin: $rotation-origin;
    -o-transform: $rotation;	
    -o-transform-origin: $rotation-origin;
    transform: $rotation;
    transform-origin: $rotation-origin;

    @if $include-ie {
        // In IE9 and below we have to use a BasicImage filter to achieve 90 or 270 degree
        // rotation of the text container. Text rotated using this methodology does
        // not display using ClearType font unless the element has a background. To
        // work around this, we apply a background color to the text container element
        // and then use a Chroma filter to display all pixels of that color as transparent.
        .#{$prefix}ie9m &amp; {
            @if $background-color != null {
                background-color: $background-color;
                filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=#{$ie-rotation}),
                        progid:DXImageTransform.Microsoft.Chroma(color=#{$background-color});
            } @else {
                filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=#{$ie-rotation});
            }
        }
    }
}

@mixin unrotate-element() {
    -webkit-transform: none; 
    -moz-transform: none;	
    -o-transform: none;	
    transform: none;
    @if $include-ie {
        background-color: transparent;
        filter: none;
    }
}</pre>
</body>
</html>
