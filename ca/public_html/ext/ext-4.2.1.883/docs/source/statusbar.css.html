<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">/* StatusBar - structure */
.x-statusbar .x-status-text {
    cursor: default;
/*
    height: 21px;
    line-height: 21px;
    padding: 0 4px;
*/
}
.x-statusbar .x-status-busy {
    padding-left: 25px !important;
    background: transparent no-repeat 3px 0;
}

.x-toolbar div.xtb-text

.x-statusbar .x-status-text-panel {
    border-top: 1px solid;
    border-right: 1px solid;
    border-bottom: 1px solid;
    border-left: 1px solid;
    padding: 2px 8px 2px 5px;
}

/* StatusBar word processor example styles */

#word-status .x-status-text-panel .spacer {
    width: 60px;
    font-size:0;
    line-height:0;
}
#word-status .x-status-busy {
    padding-left: 25px !important;
    background: transparent no-repeat 3px 0;
}
#word-status .x-status-saved {
    padding-left: 25px !important;
    background: transparent no-repeat 3px 0;
}

/* StatusBar form validation example styles */

.x-statusbar .x-status-error {
    cursor: pointer;
    padding-left: 25px !important;
    background: transparent no-repeat 3px 0;
}
.x-statusbar .x-status-valid {
    padding-left: 25px !important;
    background: transparent no-repeat 3px 0;
}
.x-status-error-list {
    font: 11px tahoma,arial,verdana,sans-serif;
    position: absolute;
    z-index: 9999;
    border-top: 1px solid;
    border-right: 1px solid;
    border-bottom: 1px solid;
    border-left: 1px solid;
    padding: 5px 10px;
}
.x-status-error-list li {
    cursor: pointer;
    list-style: disc;
    margin-left: 10px;
}
.x-status-error-list li a {
    text-decoration: none;
}
.x-status-error-list li a:hover {
    text-decoration: underline;
}


/* *********************************************************** */
/* *********************************************************** */
/* *********************************************************** */


/* StatusBar - visual */

.x-statusbar .x-status-busy {
    background-image: url(../images/loading.gif);
}
.x-statusbar .x-status-text-panel {
    border-color: #99bbe8 #fff #fff #99bbe8;
}

/* StatusBar word processor example styles */

#word-status .x-status-text {
    color: #777;
}
#word-status .x-status-busy {
    background-image: url(../images/saving.gif);
}
#word-status .x-status-saved {
    background-image: url(../images/saved.png);
}

/* StatusBar form validation example styles */

.x-statusbar .x-status-error {
    color: #C33;
    background-image: url(../images/exclamation.gif);
}
.x-statusbar .x-status-valid {
    background-image: url(../images/accept.png);
}
.x-status-error-list {
    border-color: #C33;
    background: white;
}
.x-status-error-list li a {
    color: #15428B;
}</pre>
</body>
</html>
