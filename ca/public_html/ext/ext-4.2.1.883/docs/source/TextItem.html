<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-toolbar-TextItem-method-constructor'><span id='Ext-toolbar-TextItem'>/**
</span></span> * A simple class that renders text directly into a toolbar.
 *
 *     @example
 *     Ext.create('Ext.panel.Panel', {
 *         title: 'Panel with TextItem',
 *         width: 300,
 *         height: 200,
 *         tbar: [
 *             { xtype: 'tbtext', text: 'Sample Text Item' }
 *         ],
 *         renderTo: Ext.getBody()
 *     });
 *
 * @constructor
 * Creates a new TextItem
 * @param {Object} text A text string, or a config object containing a #text property
 */
Ext.define('Ext.toolbar.TextItem', {
    extend: 'Ext.toolbar.Item',
    requires: ['Ext.XTemplate'],
    alias: 'widget.tbtext',
    alternateClassName: 'Ext.Toolbar.TextItem',

<span id='Ext-toolbar-TextItem-cfg-text'>    /**
</span>     * @cfg {String} text
     * The text to be used as innerHTML (html tags are accepted).
     */
    text: '',

<span id='Ext-toolbar-TextItem-cfg-renderTpl'>    renderTpl: '{text}',
</span><span id='Ext-toolbar-TextItem-cfg-baseCls'>    //
</span>    baseCls: Ext.baseCSSPrefix + 'toolbar-text',

<span id='Ext-toolbar-TextItem-method-beforeRender'>    beforeRender : function() {
</span>        var me = this;

        me.callParent();

        Ext.apply(me.renderData, {
            text: me.text
        });
    },

<span id='Ext-toolbar-TextItem-method-setText'>    /**
</span>     * Updates this item's text, setting the text to be used as innerHTML.
     * @param {String} text The text to display (html accepted).
     */
    setText : function(text) {
        var me = this;
        me.text = text;
        if (me.rendered) {
            me.el.update(text);
            me.updateLayout();
        }
    }
});</pre>
</body>
</html>
