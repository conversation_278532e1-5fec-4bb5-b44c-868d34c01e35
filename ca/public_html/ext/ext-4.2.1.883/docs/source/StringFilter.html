<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-ux-grid-filter-StringFilter'>/**
</span> * Filter by a configurable Ext.form.field.Text
 * &lt;p&gt;&lt;b&gt;&lt;u&gt;Example Usage:&lt;/u&gt;&lt;/b&gt;&lt;/p&gt;
 * &lt;pre&gt;&lt;code&gt;
var filters = Ext.create('Ext.ux.grid.GridFilters', {
    ...
    filters: [{
        // required configs
        type: 'string',
        dataIndex: 'name',

        // optional configs
        value: 'foo',
        active: true, // default is false
        iconCls: 'ux-gridfilter-text-icon' // default
        // any Ext.form.field.Text configs accepted
    }]
});
 * &lt;/code&gt;&lt;/pre&gt;
 */
Ext.define('Ext.ux.grid.filter.StringFilter', {
    extend: 'Ext.ux.grid.filter.Filter',
    alias: 'gridfilter.string',

<span id='Ext-ux-grid-filter-StringFilter-cfg-iconCls'>    /**
</span>     * @cfg {String} iconCls
     * The iconCls to be applied to the menu item.
     * Defaults to &lt;tt&gt;'ux-gridfilter-text-icon'&lt;/tt&gt;.
     */
    iconCls : 'ux-gridfilter-text-icon',

<span id='Ext-ux-grid-filter-StringFilter-property-emptyText'>    emptyText: 'Enter Filter Text...',
</span><span id='Ext-ux-grid-filter-StringFilter-property-selectOnFocus'>    selectOnFocus: true,
</span><span id='Ext-ux-grid-filter-StringFilter-property-width'>    width: 125,
</span>
<span id='Ext-ux-grid-filter-StringFilter-method-init'>    /**
</span>     * @private
     * Template method that is to initialize the filter and install required menu items.
     */
    init : function (config) {
        Ext.applyIf(config, {
            enableKeyEvents: true,
            labelCls: 'ux-rangemenu-icon ' + this.iconCls,
            hideEmptyLabel: false,
            labelSeparator: '',
            labelWidth: 29,
            listeners: {
                scope: this,
                keyup: this.onInputKeyUp,
                el: {
                    click: function(e) {
                        e.stopPropagation();
                    }
                }
            }
        });

        this.inputItem = Ext.create('Ext.form.field.Text', config);
        this.menu.add(this.inputItem);
        this.menu.showSeparator = false;
        this.updateTask = Ext.create('Ext.util.DelayedTask', this.fireUpdate, this);
    },

<span id='Ext-ux-grid-filter-StringFilter-method-getValue'>    /**
</span>     * @private
     * Template method that is to get and return the value of the filter.
     * @return {String} The value of this filter
     */
    getValue : function () {
        return this.inputItem.getValue();
    },

<span id='Ext-ux-grid-filter-StringFilter-method-setValue'>    /**
</span>     * @private
     * Template method that is to set the value of the filter.
     * @param {Object} value The value to set the filter
     */
    setValue : function (value) {
        this.inputItem.setValue(value);
        this.fireEvent('update', this);
    },

<span id='Ext-ux-grid-filter-StringFilter-method-isActivatable'>    /**
</span>     * Template method that is to return &lt;tt&gt;true&lt;/tt&gt; if the filter
     * has enough configuration information to be activated.
     * @return {Boolean}
     */
    isActivatable : function () {
        return this.inputItem.getValue().length &gt; 0;
    },

<span id='Ext-ux-grid-filter-StringFilter-method-getSerialArgs'>    /**
</span>     * @private
     * Template method that is to get and return serialized filter data for
     * transmission to the server.
     * @return {Object/Array} An object or collection of objects containing
     * key value pairs representing the current configuration of the filter.
     */
    getSerialArgs : function () {
        return {type: 'string', value: this.getValue()};
    },

<span id='Ext-ux-grid-filter-StringFilter-method-validateRecord'>    /**
</span>     * Template method that is to validate the provided Ext.data.Record
     * against the filters configuration.
     * @param {Ext.data.Record} record The record to validate
     * @return {Boolean} true if the record is valid within the bounds
     * of the filter, false otherwise.
     */
    validateRecord : function (record) {
        var val = record.get(this.dataIndex);

        if(typeof val != 'string') {
            return (this.getValue().length === 0);
        }

        return val.toLowerCase().indexOf(this.getValue().toLowerCase()) &gt; -1;
    },

<span id='Ext-ux-grid-filter-StringFilter-method-onInputKeyUp'>    /**
</span>     * @private
     * Handler method called when there is a keyup event on this.inputItem
     */
    onInputKeyUp : function (field, e) {
        var k = e.getKey();
        if (k == e.RETURN &amp;&amp; field.isValid()) {
            e.stopEvent();
            this.menu.hide();
            return;
        }
        // restart the timer
        this.updateTask.delay(this.updateBuffer);
    }
});
</pre>
</body>
</html>
