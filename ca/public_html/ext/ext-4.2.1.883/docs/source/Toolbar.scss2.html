<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-toolbar-Toolbar-css_mixin-extjs-toolbar-ui'>/**
</span> * Creates a visual theme for a Toolbar.
 * @param {String} $ui
 * The name of the UI
 *
 * @param {color} [$background-color=$toolbar-background-color]
 * The background color of the toolbar
 *
 * @param {string/list} [$background-gradient=$toolbar-background-gradient]
 * The background gradient of the toolbar
 *
 * @param {color} [$border-color=$toolbar-border-color]
 * The border color of the toolbar
 *
 * @param {number} [$border-width=$toolbar-border-width]
 * The border-width of the toolbar
 *
 * @param {string} [$scroller-cursor=$toolbar-scroller-cursor]
 * The cursor of Toolbar scrollers
 *
 * @param {string} [$scroller-cursor-disabled=$toolbar-scroller-cursor-disabled]
 * The cursor of disabled Toolbar scrollers
 *
 * @param {number} [$scroller-opacity-disabled=$toolbar-scroller-opacity-disabled]
 * The opacity of disabled Toolbar scrollers
 *
 * @param {string} [$tool-background-image=$toolbar-tool-background-image]
 * The sprite to use for {@link Ext.panel.Tool Tools} on a Toolbar
 *
 * @member Ext.toolbar.Toolbar
 */
@mixin extjs-toolbar-ui(
    $ui,

    $background-color: $toolbar-background-color,
    $background-gradient: $toolbar-background-gradient,

    $border-color: $toolbar-border-color,
    $border-width: $toolbar-border-width,
    $scroller-cursor: $toolbar-scroller-cursor,
    $scroller-cursor-disabled: $toolbar-scroller-cursor-disabled,
    $scroller-opacity-disabled: $toolbar-scroller-opacity-disabled,
    $tool-background-image: $toolbar-tool-background-image
) {
    .#{$prefix}toolbar-#{$ui} {
        border-color: $border-color;
        border-width: $border-width;

        @include background-gradient($background-color, $background-gradient);

        .#{$prefix}box-scroller {
            cursor: $scroller-cursor;
        }

        .#{$prefix}box-scroller-disabled {
            @if $scroller-opacity-disabled != 1 {
                @include opacity($scroller-opacity-disabled);
            }
            @if $scroller-cursor-disabled != null {
                cursor: $scroller-cursor-disabled;
            }
        }

        @if $include-ext-panel-tool {
            .#{$prefix}tool-img {
                @if $toolbar-tool-background-image != null {
                    background-image: theme-background-image($toolbar-tool-background-image);
                }
                @if $background-gradient == null {
                    // EXTJSIV-8846: partially transparent png images do not display correctly
                    // in winXP/IE8m when the image element has a transparent background.
                    // to fix this, we give the element the same background-color as the toolbar.
                    background-color: $background-color;
                }
            }
        }
    }

    @if not $supports-gradients or $compile-all {
        @if $background-gradient != null {
            .#{$prefix}nlg {
                .#{$prefix}toolbar-#{$ui} {
                    background-image: slicer-background-image(toolbar-#{$ui}, 'toolbar/toolbar-#{$ui}-bg') !important;
                    background-repeat: repeat-x;
                }
            }
            $stretch: slicer-background-stretch(toolbar-#{$ui}, bottom);
        }
    }

    @include x-slicer(toolbar-#{$ui});
}

.#{$prefix}toolbar {
    font-size: $toolbar-font-size;
    border-style: $toolbar-border-style;
    padding: $toolbar-vertical-spacing 0 $toolbar-vertical-spacing $toolbar-horizontal-spacing;
}

// Item padding
.#{$prefix}toolbar-item {
    margin: 0 $toolbar-horizontal-spacing 0 0;
}

// RTL - skew item margin the opposite way
@if $include-rtl {
    .#{$prefix}rtl.#{$prefix}toolbar-item {
        margin: 0 0 0 $toolbar-horizontal-spacing;
    }
}

.#{$prefix}toolbar-text {
    margin: $toolbar-text-margin;
    color: $toolbar-text-color;
    line-height: $toolbar-text-line-height;
    font-family: $toolbar-text-font-family;
    font-size: $toolbar-text-font-size;
    font-weight: $toolbar-text-font-weight;
}

.#{$prefix}toolbar-separator-horizontal {
    margin: $toolbar-separator-horizontal-margin;
    height: $toolbar-separator-horizontal-height;
    border-style: $toolbar-separator-horizontal-border-style;
    border-width: $toolbar-separator-horizontal-border-width;
    border-left-color: $toolbar-separator-color;
    border-right-color: $toolbar-separator-highlight-color;
}

// RTL - skew toolbar padding the opposite way
@if $include-rtl {
    .#{$prefix}rtl.#{$prefix}toolbar {
        padding: $toolbar-vertical-spacing $toolbar-horizontal-spacing $toolbar-vertical-spacing 0;
    }
}

.#{$prefix}toolbar-footer {
    background: $toolbar-footer-background-color;
    border: $toolbar-footer-border-width;
    margin: $toolbar-footer-margin;

    padding: $toolbar-footer-vertical-spacing 0 $toolbar-footer-vertical-spacing $toolbar-footer-horizontal-spacing;

    .#{$prefix}toolbar-item {
        margin: 0 $toolbar-footer-horizontal-spacing 0 0;
    }
}

.#{$prefix}toolbar-spacer {
    width: $toolbar-spacer-width;
}

// Background for overflow button inserted by the Menu box overflow handler within a toolbar
.#{$prefix}toolbar-more-icon {
    background-image: theme-background-image('toolbar/more') !important;
    background-position: center center !important;
    background-repeat: no-repeat;
}

@if $include-toolbar-default-ui {
    @include extjs-toolbar-ui(
        $ui: 'default'
    );
}

.#{$prefix}toolbar-scroll-left {
    background-image: theme-background-image('toolbar/scroll-left');
    background-position: $toolbar-scroll-left-background-x 0;
    width: $toolbar-scroller-width;
    height: $toolbar-scroller-height;
    border-style: solid;
    border-color: $toolbar-scroller-border-color;
    border-width: $toolbar-scroller-border-width;
    margin-top: $toolbar-scroller-margin-top;
}

.#{$prefix}toolbar-scroll-left-hover {
    background-position: 0 0;
}

.#{$prefix}toolbar-scroll-right {
    background-image: theme-background-image('toolbar/scroll-right');
    width: $toolbar-scroller-width;
    height: $toolbar-scroller-height;
    border-style: solid;
    border-color: $toolbar-scroller-border-color;
    border-width: $toolbar-scroller-border-width;
    margin-top: $toolbar-scroller-margin-top;
}

.#{$prefix}toolbar-scroll-right-hover {
    background-position: -$toolbar-scroller-width 0;
}

.#{$prefix}toolbar .#{$prefix}box-menu-after {
    margin: 0 $toolbar-horizontal-spacing 0 $toolbar-horizontal-spacing;
}

.#{$prefix}toolbar-vertical {
    padding: $toolbar-vertical-spacing $toolbar-horizontal-spacing 0 $toolbar-horizontal-spacing;

    .#{$prefix}toolbar-item {
        margin: 0 0 $toolbar-vertical-spacing 0;
    }

    .#{$prefix}toolbar-text {
        margin: rotate90($toolbar-text-margin);
    }

    .#{$prefix}toolbar-separator-vertical {
        margin: $toolbar-separator-vertical-margin;
        border-style: $toolbar-separator-vertical-border-style;
        border-width: $toolbar-separator-vertical-border-width;
        border-top-color: $toolbar-separator-color;
        border-bottom-color: $toolbar-separator-highlight-color;
    }

    .#{$prefix}box-menu-after,
    .#{$prefix}rtl.#{$prefix}box-menu-after {
        margin: $toolbar-vertical-spacing 0 $toolbar-vertical-spacing 0;
        display: block;
        float: none;
    }
}
</pre>
</body>
</html>
