<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">.x-tab-tabmenu-right {
    background: transparent url('images/tab-scroller-menu.gif') no-repeat 0 0;
    width:18px;
    height: 20px;
    cursor:pointer;
    zoom: 1;
    z-index: 6;
    float: right;
}
.x-tab-tabmenu-over {
    background-position: -18px 0;
}
.x-tab-tabmenu-disabled {
    background-position: 0 0;
    opacity:.5;
    -moz-opacity:.5;
    filter:alpha(opacity=50);
    cursor:default;
}
</pre>
</body>
</html>
