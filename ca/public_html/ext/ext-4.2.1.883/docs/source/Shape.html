<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-chart-Shape'>/**
</span> * @private
 */
Ext.define('Ext.chart.Shape', {

    /* Begin Definitions */

    singleton: true,

<span id='Ext-chart-Shape-method-circle'>    /* End Definitions */
</span>
    circle: function (surface, opts) {
        return surface.add(Ext.apply({
            type: 'circle',
            x: opts.x,
            y: opts.y,
            stroke: null,
            radius: opts.radius
        }, opts));
    },
<span id='Ext-chart-Shape-method-line'>    line: function (surface, opts) {
</span>        return surface.add(Ext.apply({
            type: 'rect',
            x: opts.x - opts.radius,
            y: opts.y - opts.radius,
            height: 2 * opts.radius,
            width: 2 * opts.radius / 5
        }, opts));
    },
<span id='Ext-chart-Shape-method-square'>    square: function (surface, opts) {
</span>        return surface.add(Ext.applyIf({
            type: 'rect',
            x: opts.x - opts.radius,
            y: opts.y - opts.radius,
            height: 2 * opts.radius,
            width: 2 * opts.radius,
            radius: null
        }, opts));
    },
<span id='Ext-chart-Shape-method-triangle'>    triangle: function (surface, opts) {
</span>        opts.radius *= 1.75;
        return surface.add(Ext.apply({
            type: 'path',
            stroke: null,
            path: &quot;M&quot;.concat(opts.x, &quot;,&quot;, opts.y, &quot;m0-&quot;, opts.radius * 0.58, &quot;l&quot;, opts.radius * 0.5, &quot;,&quot;, opts.radius * 0.87, &quot;-&quot;, opts.radius, &quot;,0z&quot;)
        }, opts));
    },
<span id='Ext-chart-Shape-method-diamond'>    diamond: function (surface, opts) {
</span>        var r = opts.radius;
        r *= 1.5;
        return surface.add(Ext.apply({
            type: 'path',
            stroke: null,
            path: [&quot;M&quot;, opts.x, opts.y - r, &quot;l&quot;, r, r, -r, r, -r, -r, r, -r, &quot;z&quot;]
        }, opts));
    },
<span id='Ext-chart-Shape-method-cross'>    cross: function (surface, opts) {
</span>        var r = opts.radius;
        r = r / 1.7;
        return surface.add(Ext.apply({
            type: 'path',
            stroke: null,
            path: &quot;M&quot;.concat(opts.x - r, &quot;,&quot;, opts.y, &quot;l&quot;, [-r, -r, r, -r, r, r, r, -r, r, r, -r, r, r, r, -r, r, -r, -r, -r, r, -r, -r, &quot;z&quot;])
        }, opts));
    },
<span id='Ext-chart-Shape-method-plus'>    plus: function (surface, opts) {
</span>        var r = opts.radius / 1.3;
        return surface.add(Ext.apply({
            type: 'path',
            stroke: null,
            path: &quot;M&quot;.concat(opts.x - r / 2, &quot;,&quot;, opts.y - r / 2, &quot;l&quot;, [0, -r, r, 0, 0, r, r, 0, 0, r, -r, 0, 0, r, -r, 0, 0, -r, -r, 0, 0, -r, &quot;z&quot;])
        }, opts));
    },
<span id='Ext-chart-Shape-method-arrow'>    arrow: function (surface, opts) {
</span>        var r = opts.radius;
        return surface.add(Ext.apply({
            type: 'path',
            path: &quot;M&quot;.concat(opts.x - r * 0.7, &quot;,&quot;, opts.y - r * 0.4, &quot;l&quot;, [r * 0.6, 0, 0, -r * 0.4, r, r * 0.8, -r, r * 0.8, 0, -r * 0.4, -r * 0.6, 0], &quot;z&quot;)
        }, opts));
    },
<span id='Ext-chart-Shape-method-drop'>    drop: function (surface, x, y, text, size, angle) {
</span>        size = size || 30;
        angle = angle || 0;
        surface.add({
            type: 'path',
            path: ['M', x, y, 'l', size, 0, 'A', size * 0.4, size * 0.4, 0, 1, 0, x + size * 0.7, y - size * 0.7, 'z'],
            fill: '#000',
            stroke: 'none',
            rotate: {
                degrees: 22.5 - angle,
                x: x,
                y: y
            }
        });
        angle = (angle + 90) * Math.PI / 180;
        surface.add({
            type: 'text',
            x: x + size * Math.sin(angle) - 10, // Shift here, Not sure why.
            y: y + size * Math.cos(angle) + 5,
            text:  text,
            'font-size': size * 12 / 40,
            stroke: 'none',
            fill: '#fff'
        });
    }
});</pre>
</body>
</html>
