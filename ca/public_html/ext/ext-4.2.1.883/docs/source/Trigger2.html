<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-layout-component-field-Trigger'>/**
</span> * Layout class for {@link Ext.form.field.Trigger} fields. Adjusts the input field size to accommodate
 * the trigger button(s).
 * @private
 */
Ext.define('Ext.layout.component.field.Trigger', {

    /* Begin Definitions */

    alias: 'layout.triggerfield',

    extend: 'Ext.layout.component.field.Field',

<span id='Ext-layout-component-field-Trigger-property-type'>    /* End Definitions */
</span>
    type: 'triggerfield',

<span id='Ext-layout-component-field-Trigger-property-borderWidths'>    // Private. Cached extra width values containing width of all a trigger field's &quot;furniture&quot; round the actual input element
</span>    borderWidths: {},

<span id='Ext-layout-component-field-Trigger-method-beginLayout'>    beginLayout: function(ownerContext) {
</span>        var me = this,
            owner = me.owner,
            flags;

        ownerContext.triggerWrap = ownerContext.getEl('triggerWrap');

        me.callParent(arguments);

        // if any of these important states have changed, sync them now:
        flags = owner.getTriggerStateFlags();
        if (flags != owner.lastTriggerStateFlags) {
            owner.lastTriggerStateFlags = flags;
            me.updateEditState();
        }
    },
    
<span id='Ext-layout-component-field-Trigger-method-beginLayoutCycle'>    beginLayoutCycle: function(ownerContext){
</span>        this.callParent(arguments);
        
        // Clear width, in case a previous layout cycle set an explicit width.
        if (ownerContext.widthModel.shrinkWrap &amp;&amp; !this.owner.inputWidth) {
            ownerContext.inputContext.el.setStyle('width', '');
        }    
    },

<span id='Ext-layout-component-field-Trigger-method-beginLayoutFixed'>    beginLayoutFixed: function (ownerContext, width, suffix) {
</span>        var me = this,
            owner = ownerContext.target,
            ieInputWidthAdjustment = me.ieInputWidthAdjustment || 0,
            inputWidth = '100%',
            triggerWrap = owner.triggerWrap;

        me.callParent(arguments);

        owner.inputCell.setStyle('width', '100%');
        if(ieInputWidthAdjustment) {
            me.adjustIEInputPadding(ownerContext);
            if(suffix === 'px') {
                if (owner.inputWidth) {
                    inputWidth = owner.inputWidth - me.getExtraWidth(ownerContext);
                } else {
                    inputWidth = width - ieInputWidthAdjustment - me.getExtraWidth(ownerContext);
                }
                inputWidth += 'px';
            }
        }
        owner.inputEl.setStyle('width', inputWidth);
        inputWidth = owner.inputWidth;
        if (inputWidth) {
            triggerWrap.setStyle('width', inputWidth + (ieInputWidthAdjustment) + 'px');
        } else {
            triggerWrap.setStyle('width', width + suffix);
        }
        triggerWrap.setStyle('table-layout', 'fixed');
    },

<span id='Ext-layout-component-field-Trigger-method-adjustIEInputPadding'>    adjustIEInputPadding: function(ownerContext) {
</span>        // adjust for IE 6/7 strict content-box model
        this.owner.inputCell.setStyle('padding-right', this.ieInputWidthAdjustment + 'px');
    },

<span id='Ext-layout-component-field-Trigger-method-getExtraWidth'>    /**
</span>     * @private
     * Returns the width of the &quot;extras&quot; around the input field. This includes the total width
     * of all the triggers in the field and any outer bordering.
     * 
     * This measurement is used when explicitly sizing the contained input field to a smaller inner
     * width while keeping the outer component width the same. This extra width is subtracted from the
     * total component width to calculate the new width for the input field.
     */
    getExtraWidth: function(ownerContext) {
        var me = this,
            owner = me.owner,
            borderWidths = me.borderWidths,
            ui = owner.ui + owner.triggerEl.getCount();

        if (!(ui in borderWidths)) {
            borderWidths[ui] = ownerContext.triggerWrap.getBorderInfo().width
        }
        return borderWidths[ui] + owner.getTriggerWidth();
    },

<span id='Ext-layout-component-field-Trigger-method-beginLayoutShrinkWrap'>    beginLayoutShrinkWrap: function (ownerContext) {
</span>        var owner = ownerContext.target,
            emptyString = '',
            inputWidth = owner.inputWidth,
            triggerWrap = owner.triggerWrap;

        this.callParent(arguments);

        if (inputWidth) {
            triggerWrap.setStyle('width', inputWidth + 'px');
            inputWidth = (inputWidth - this.getExtraWidth(ownerContext)) + 'px';
            owner.inputEl.setStyle('width', inputWidth);
            owner.inputCell.setStyle('width', inputWidth);
        } else {
            owner.inputCell.setStyle('width', emptyString);
            owner.inputEl.setStyle('width', emptyString);
            triggerWrap.setStyle('width', emptyString);
            triggerWrap.setStyle('table-layout', 'auto');
        }
    },

<span id='Ext-layout-component-field-Trigger-method-getTextWidth'>    getTextWidth: function () {
</span>        var me = this,
            owner = me.owner,
            inputEl = owner.inputEl,
            value;

        // Find the width that contains the whole text value
        value = (inputEl.dom.value || (owner.hasFocus ? '' : owner.emptyText) || '') + owner.growAppend;
        return inputEl.getTextWidth(value);
    },
    
<span id='Ext-layout-component-field-Trigger-method-publishOwnerWidth'>    publishOwnerWidth: function(ownerContext, width) {
</span>        var owner = this.owner;
        this.callParent(arguments);
        if (!owner.grow &amp;&amp; !owner.inputWidth) {
            width -= this.getExtraWidth(ownerContext);
            if (owner.labelAlign != 'top') {
                width -= owner.getLabelWidth();
            }
            ownerContext.inputContext.setWidth(width);
        }    
    },

<span id='Ext-layout-component-field-Trigger-method-publishInnerHeight'>    publishInnerHeight: function(ownerContext, height) {
</span>        ownerContext.inputContext.setHeight(height - this.measureLabelErrorHeight(ownerContext));
    },

<span id='Ext-layout-component-field-Trigger-method-measureContentWidth'>    measureContentWidth: function (ownerContext) {
</span>        var me = this,
            owner = me.owner,
            width = me.callParent(arguments),
            inputContext = ownerContext.inputContext,
            calcWidth, max, min;

        if (owner.grow &amp;&amp; !ownerContext.state.growHandled) {
            calcWidth = me.getTextWidth() + ownerContext.inputContext.getFrameInfo().width;

            max = owner.growMax;
            min = Math.min(max, width);
            max = Math.max(owner.growMin, max, min);

            // Constrain
            calcWidth = Ext.Number.constrain(calcWidth, owner.growMin, max);
            inputContext.setWidth(calcWidth);
            ownerContext.state.growHandled = true;
            
            // Now that we've set the inputContext, we need to recalculate the width
            inputContext.domBlock(me, 'width');
            width = NaN;
        } else if (!owner.inputWidth) {
            width -= me.getExtraWidth(ownerContext);
        }
        return width;
    },

<span id='Ext-layout-component-field-Trigger-method-updateEditState'>    updateEditState: function() {
</span>        var me = this,
            owner = me.owner,
            inputEl = owner.inputEl,
            noeditCls = Ext.baseCSSPrefix + 'trigger-noedit',
            displayed,
            readOnly;

        if (me.owner.readOnly) {
            inputEl.addCls(noeditCls);
            readOnly = true;
            displayed = false;
        } else {
            if (me.owner.editable) {
                inputEl.removeCls(noeditCls);
                readOnly = false;
            } else {
                inputEl.addCls(noeditCls);
                readOnly = true;
            }
            displayed = !me.owner.hideTrigger;
        }

        owner.triggerCell.setDisplayed(displayed);
        inputEl.dom.readOnly = readOnly;
    }
});
</pre>
</body>
</html>
