<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">.#{$prefix}tip {
    position: absolute;
    overflow: visible; /*pointer needs to be able to stick out*/
}

.#{$prefix}tip-body {
    overflow: hidden;
    position: relative;
}

.#{$prefix}tip-anchor {
    position: absolute;
    overflow: hidden;
    border-style: solid;
}

</pre>
</body>
</html>
