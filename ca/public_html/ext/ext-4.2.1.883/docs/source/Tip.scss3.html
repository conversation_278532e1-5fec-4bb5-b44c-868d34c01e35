<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-tip-Tip'>/**
</span> * @class Ext.tip.Tip
 */

<span id='Ext-tip-Tip-css_var-S-tip-background-color'>/**
</span> * @var {color}
 * The background-color of the Tip
 */
$tip-background-color: #fff !default;

<span id='Ext-tip-Tip-css_var-S-tip-background-gradient'>/**
</span> * @var {string/list}
 * The background-gradient of the Tip. Can be either the name of a predefined gradient or a
 * list of color stops. Used as the `$type` parameter for {@link Global_CSS#background-gradient}.
 */
$tip-background-gradient: null !default;

<span id='Ext-tip-Tip-css_var-S-tip-body-color'>/**
</span> * @var {color}
 * The text color of the Tip body
 */
$tip-body-color: #000 !default;

<span id='Ext-tip-Tip-css_var-S-tip-body-font-size'>/**
</span> * @var {number}
 * The font-size of the Tip body
 */
$tip-body-font-size: $font-size !default;

<span id='Ext-tip-Tip-css_var-S-tip-body-font-weight'>/**
</span> * @var {string}
 * The font-weight of the Tip body
 */
$tip-body-font-weight: normal !default;

<span id='Ext-tip-Tip-css_var-S-tip-body-padding'>/**
</span> * @var {number/list}
 * The padding of the Tip body
 */
$tip-body-padding: 3px !default;

<span id='Ext-tip-Tip-css_var-S-tip-body-link-color'>/**
</span> * @var {color}
 * The text color of any anchor tags inside the Tip body
 */
$tip-body-link-color: $tip-body-color !default;

<span id='Ext-tip-Tip-css_var-S-tip-header-color'>/**
</span> * @var {color}
 * The text color of the Tip header
 */
$tip-header-color: $tip-body-color !default;

<span id='Ext-tip-Tip-css_var-S-tip-header-font-size'>/**
</span> * @var {number}
 * The font-size of the Tip header
 */
$tip-header-font-size: $tip-body-font-size !default;

<span id='Ext-tip-Tip-css_var-S-tip-header-font-weight'>/**
</span> * @var {string}
 * The font-weight of the Tip header
 */
$tip-header-font-weight: bold !default;

<span id='Ext-tip-Tip-css_var-S-tip-header-body-padding'>/**
</span> * @var {number/list}
 * The padding of the Tip header's body element
 */
$tip-header-body-padding: 3px 3px 0 3px!default;  

<span id='Ext-tip-Tip-css_var-S-tip-border-color'>/**
</span> * @var {color}
 * The border-color of the Tip
 */
$tip-border-color: $panel-border-color !default;

<span id='Ext-tip-Tip-css_var-S-tip-border-width'>/**
</span> * @var {number}
 * The border-width of the Tip
 */
$tip-border-width: 1px !default;

<span id='Ext-tip-Tip-css_var-S-tip-border-radius'>/**
</span> * @var {number}
 * The border-radius of the Tip
 */
$tip-border-radius: 3px !default;

<span id='Ext-tip-Tip-css_var-S-tip-error-inner-border-color'>/**
</span> * @var {color}
 * The inner border-color of the form field error Tip
 */
$tip-error-inner-border-color: #fff !default;

<span id='Ext-tip-Tip-css_var-S-tip-error-inner-border-width'>/**
</span> * @var {number}
 * The inner border-width of the form field error Tip
 */
$tip-error-inner-border-width: 0 !default;

<span id='Ext-tip-Tip-css_var-S-tip-error-border-color'>/**
</span> * @var {color}
 * The border-color of the form field error Tip
 */
$tip-error-border-color: $tip-border-color !default;

<span id='Ext-tip-Tip-css_var-S-tip-error-border-radius'>/**
</span> * @var {number}
 * The border-radius of the form field error Tip
 */
$tip-error-border-radius: $tip-border-radius !default;

<span id='Ext-tip-Tip-css_var-S-tip-error-border-width'>/**
</span> * @var {number}
 * The border-width of the form field error Tip
 */
$tip-error-border-width: $tip-border-width !default;

<span id='Ext-tip-Tip-css_var-S-tip-error-background-color'>/**
</span> * @var {color}
 * The background-color of the form field error Tip
 */
$tip-error-background-color: $tip-background-color !default;

<span id='Ext-tip-Tip-css_var-S-tip-error-body-padding'>/**
</span> * @var {number/list}
 * The padding of the form field error Tip's body element
 */
$tip-error-body-padding: $tip-body-padding !default;

<span id='Ext-tip-Tip-css_var-S-tip-error-body-color'>/**
</span> * @var {color}
 * The text color of the form field error Tip's body element
 */
$tip-error-body-color: $tip-body-color !default;

<span id='Ext-tip-Tip-css_var-S-tip-error-body-font-size'>/**
</span> * @var {number}
 * The font-size of the form field error Tip's body element
 */
$tip-error-body-font-size: $tip-body-font-size !default;

<span id='Ext-tip-Tip-css_var-S-tip-error-body-font-weight'>/**
</span> * @var {string}
 * The font-weight of the form field error Tip's body element
 */
$tip-error-body-font-weight: $tip-body-font-weight !default;

<span id='Ext-tip-Tip-css_var-S-tip-error-body-link-color'>/**
</span> * @var {color}
 * The color of anchor tags in the form field error Tip's body element
 */
$tip-error-body-link-color: $tip-body-link-color !default;

<span id='Ext-tip-Tip-css_var-S-tip-tool-spacing'>/**
</span> * @var {number}
 * The space between {@link Ext.panel.Tool Tools} in the header
 */
$tip-tool-spacing: 4px !default;

<span id='Ext-tip-Tip-css_var-S-tip-tool-background-image'>/**
</span> * @var {string}
 * The sprite to use for the header {@link Ext.panel.Tool Tools}
 */
$tip-tool-background-image: 'tools/tool-sprites' !default;

<span id='Ext-tip-Tip-css_var-S-include-tip-default-ui'>/**
</span> * @var {boolean}
 * True to include the &quot;default&quot; tip UI
 */
$include-tip-default-ui: $include-default-uis !default;

<span id='Ext-tip-Tip-css_var-S-include-tip-form-invalid-ui'>/**
</span> * @var {boolean}
 * True to include the &quot;form-invalid&quot; tip UI
 */
$include-tip-form-invalid-ui: $include-default-uis !default;</pre>
</body>
</html>
