<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">.#{$prefix}form-trigger {
    background: theme-background-image('form/trigger');
    width: $form-trigger-width;

    @if $form-trigger-border-width != 0 {
        border-width: $form-trigger-border-width;
        border-color: $form-trigger-border-color;
        border-style: $form-trigger-border-style;
    }
}

@if $include-rtl {
    .#{$prefix}rtl.#{$prefix}form-trigger-wrap .#{$prefix}form-trigger {
        background-image: theme-background-image('form/trigger-rtl');
    }
}

.#{$prefix}trigger-cell {
    background-color: $form-field-background-color;
    width: $form-trigger-width;
}

.#{$prefix}form-trigger-over {
    background-position: -$form-trigger-width 0;
    @if $form-trigger-border-width != 0 and $form-trigger-border-color-over != $form-trigger-border-color {
        border-color: $form-trigger-border-color-over;
    }
}

.#{$prefix}form-trigger-wrap-focus .#{$prefix}form-trigger {
    background-position: -($form-trigger-width * 3) 0;
    @if $form-trigger-border-width != 0 and $form-trigger-border-color-focus != $form-trigger-border-color {
        border-color: $form-trigger-border-color-focus;
    }
}

.#{$prefix}form-trigger-wrap-focus .#{$prefix}form-trigger-over {
    background-position: -($form-trigger-width * 4) 0;
}

.#{$prefix}form-trigger-click,
.#{$prefix}form-trigger-wrap-focus .#{$prefix}form-trigger-click {
    background-position: -($form-trigger-width * 2) 0;
    @if $form-trigger-border-width != 0 and $form-trigger-border-color-pressed != $form-trigger-border-color-over {
        border-color: $form-trigger-border-color-pressed;
    }
}

.#{$prefix}form-clear-trigger {
    background-image: theme-background-image('form/clear-trigger');
}

@if $include-rtl {
    .#{$prefix}rtl.#{$prefix}form-trigger-wrap .#{$prefix}form-clear-trigger {
        background-image: theme-background-image('form/clear-trigger-rtl');
    }
}

.#{$prefix}form-search-trigger {
    background-image: theme-background-image('form/search-trigger');
}

@if $include-rtl {
    .#{$prefix}rtl.#{$prefix}form-trigger-wrap .#{$prefix}form-search-trigger {
        background-image: theme-background-image('form/search-trigger-rtl');
    }
}

@if $include-ie {
    // in IE6 quirks, the table cell enclosing the trigger field's input element does not shrink to fit,
    // so it is necessary to set the height of the table cell.
    .#{$prefix}quirks .#{prefix}ie6 {
        .#{$prefix}form-trigger-input-cell {
            height: $form-field-height;
        }
        @if $include-ext-toolbar-toolbar {
            .#{$prefix}field-toolbar .#{$prefix}form-trigger-input-cell {
                height: $form-toolbar-field-height;
            }
        }
    }
}</pre>
</body>
</html>
