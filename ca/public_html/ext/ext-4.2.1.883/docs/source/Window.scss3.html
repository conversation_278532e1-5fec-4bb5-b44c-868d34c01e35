<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-window-Window'>/**
</span> * @class Ext.window.Window
 */

<span id='Ext-window-Window-css_var-S-window-base-color'>/**
</span> * @var {color}
 * The base color of Windows
 */
$window-base-color: $base-color !default;

<span id='Ext-window-Window-css_var-S-window-padding'>/**
</span> * @var {number}
 * The padding of Windows
 */
$window-padding: $panel-frame-padding !default;

<span id='Ext-window-Window-css_var-S-window-border-radius'>/**
</span> * @var {number}
 * The border-radius of Windows
 */
$window-border-radius: $panel-frame-border-radius !default;

<span id='Ext-window-Window-css_var-S-window-border-width'>/**
</span> * @var {number}
 * The border-width of Windows
 */
$window-border-width: 1px !default;

<span id='Ext-window-Window-css_var-S-window-border-color'>/**
</span> * @var {color}
 * The border-color of Windows
 */
$window-border-color: $window-base-color !default;

<span id='Ext-window-Window-css_var-S-window-inner-border-color'>/**
</span> * @var {color}
 * The inner border-color of Windows
 */
$window-inner-border-color: #fff !default;

<span id='Ext-window-Window-css_var-S-window-inner-border-width'>/**
</span> * @var {number}
 * The inner border-width of Windows
 */
$window-inner-border-width: 0 !default;

<span id='Ext-window-Window-css_var-S-window-background-color'>/**
</span> * @var {color}
 * The background-color of Windows
 */
$window-background-color: #fff !default;

<span id='Ext-window-Window-css_var-S-window-body-border-width'>/**
</span> * @var {number}
 * The body border-width of Windows
 */
$window-body-border-width: 1px !default;

<span id='Ext-window-Window-css_var-S-window-body-border-style'>/**
</span> * @var {string}
 * The body border-style of Windows
 */
$window-body-border-style: solid !default;

<span id='Ext-window-Window-css_var-S-window-body-border-color'>/**
</span> * @var {color}
 * The body border-color of Windows
 */
$window-body-border-color: $window-base-color !default;

<span id='Ext-window-Window-css_var-S-window-body-background-color'>/**
</span> * @var {color}
 * The body background-color of Windows
 */
$window-body-background-color: #fff !default;

<span id='Ext-window-Window-css_var-S-window-body-color'>/**
</span> * @var {color}
 * The body text color of Windows
 */
$window-body-color: #000 !default;

<span id='Ext-window-Window-css_var-S-window-header-padding'>/**
</span> * @var {number/list}
 * The padding of Window Headers
 */
$window-header-padding: $panel-frame-header-padding !default;

<span id='Ext-window-Window-css_var-S-window-header-font-size'>/**
</span> * @var {number}
 * The font-size of Window Headers
 */
$window-header-font-size: $panel-header-font-size !default;

<span id='Ext-window-Window-css_var-S-window-header-line-height'>/**
</span> * @var {number}
 * The line-height of Window Headers
 */
$window-header-line-height: $panel-header-line-height !default;

<span id='Ext-window-Window-css_var-S-window-header-color'>/**
</span> * @var {color}
 * The text color of Window Headers
 */
$window-header-color: $panel-header-color !default;

<span id='Ext-window-Window-css_var-S-window-header-background-color'>/**
</span> * @var {color}
 * The background-color of Window Headers
 */
$window-header-background-color: $window-base-color !default;

<span id='Ext-window-Window-css_var-S-window-header-font-weight'>/**
</span> * @var {string}
 * The font-weight of Window Headers
 */
$window-header-font-weight: $panel-header-font-weight !default;

<span id='Ext-window-Window-css_var-S-window-tool-spacing'>/**
</span> * @var {number}
 * The space between the Window {@link Ext.panel.Tool Tools}
 */
$window-tool-spacing: $panel-tool-spacing !default;

<span id='Ext-window-Window-css_var-S-window-tool-background-image'>/**
</span> * @var {string}
 * The background sprite to use for Window {@link Ext.panel.Tool Tools}
 */
$window-tool-background-image: 'tools/tool-sprites' !default;

<span id='Ext-window-Window-css_var-S-window-header-font-family'>/**
</span> * @var {string}
 * The font-family of Window Headers
 */
$window-header-font-family: $panel-header-font-family !default;

<span id='Ext-window-Window-css_var-S-window-header-text-padding'>/**
</span> * @var {number/list}
 * The padding of the Window Header's text element
 */
$window-header-text-padding: $panel-header-text-padding !default;

<span id='Ext-window-Window-css_var-S-window-header-text-transform'>/**
</span> * @var {string}
 * The text-transform of Window Headers
 */
$window-header-text-transform: $panel-header-text-transform !default;

<span id='Ext-window-Window-css_var-S-window-header-icon-width'>/**
</span> * @var {number}
 * The width of the Window Header icon
 */
$window-header-icon-width: $panel-header-icon-width !default;

<span id='Ext-window-Window-css_var-S-window-header-icon-height'>/**
</span> * @var {number}
 * The height of the Window Header icon
 */
$window-header-icon-height: $panel-header-icon-height !default;

<span id='Ext-window-Window-css_var-S-window-header-icon-spacing'>/**
</span> * @var {number}
 * The space between the Window Header icon and text
 */
$window-header-icon-spacing: $panel-header-icon-spacing !default;

<span id='Ext-window-Window-css_var-S-window-header-icon-background-position'>/**
</span> * @var {list}
 * The background-position of  the Window Header icon
 */
$window-header-icon-background-position: $panel-header-icon-background-position !default;

<span id='Ext-window-Window-css_var-S-window-header-glyph-color'>/**
</span> * @var {color}
 * The color of the Window Header glyph icon
 */
$window-header-glyph-color: $window-header-color !default;

<span id='Ext-window-Window-css_var-S-window-header-glyph-opacity'>/**
</span> * @var {number}
 * The opacity of the Window Header glyph icon
 */
$window-header-glyph-opacity: $panel-header-glyph-opacity !default;

<span id='Ext-window-Window-css_var-S-window-header-border-width'>/**
</span> * @var {number}
 * The border-width of Window Headers
 */
$window-header-border-width: 1px !default;

<span id='Ext-window-Window-css_var-S-window-header-inner-border-color'>/**
</span> * @var {color}
 * The inner border-color of Window Headers
 */
$window-header-inner-border-color: #fff !default;

<span id='Ext-window-Window-css_var-S-window-header-inner-border-width'>/**
</span> * @var {number}
 * The inner border-width of Window Headers
 */
$window-header-inner-border-width: 0 !default;

<span id='Ext-window-Window-css_var-S-ui-force-header-border'>/**
</span> * @var {boolean} $ui-force-header-border
 * True to force the window header to have a border on the side facing the window body.
 * Overrides dock layout's border management border removal rules.
 */
$window-force-header-border: true !default;

<span id='Ext-window-Window-css_var-S-window-ghost-opacity'>/**
</span> * @var {number}
 * The opacity of ghost Windows while dragging
 */
$window-ghost-opacity: $panel-ghost-opacity !default;

<span id='Ext-window-Window-css_var-S-window-include-border-management-rules'>/**
</span> * @var {boolean}
 * True to include neptune style border management rules.
 */
$window-include-border-management-rules: false !default;

<span id='Ext-window-Window-css_var-S-window-wrap-border-color'>/**
</span> * @var {color}
 * The color to apply to the border that wraps the body and docked items. The presence of
 * the wrap border is controlled by the {@link #border} config. Only applicable when
 * `$window-include-border-management-rules` is `true`.
 */
$window-wrap-border-color: $window-border-color !default;

<span id='Ext-window-Window-css_var-S-window-wrap-border-width'>/**
</span> * @var {number}
 * The width to apply to the border that wraps the body and docked items. The presence of
 * the wrap border is controlled by the {@link #border} config. Only applicable when
 * `$window-include-border-management-rules` is `true`.
 */
$window-wrap-border-width: 1px !default;

<span id='Ext-window-Window-css_var-S-include-window-default-ui'>/**
</span> * @var {boolean}
 * True to include the &quot;default&quot; window UI
 */
$include-window-default-ui: $include-default-uis !default;</pre>
</body>
</html>
