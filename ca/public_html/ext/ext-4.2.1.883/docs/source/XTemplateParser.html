<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js">// @tag core
<span id='Ext-XTemplateParser'>/**
</span> * This class parses the XTemplate syntax and calls abstract methods to process the parts.
 * @private
 */
Ext.define('Ext.XTemplateParser', {
<span id='Ext-XTemplateParser-method-constructor'>    constructor: function (config) {
</span>        Ext.apply(this, config);
    },

<span id='Ext-XTemplateParser-property-level'>    /**
</span>     * @property {Number} level The 'for' or 'foreach' loop context level. This is adjusted
     * up by one prior to calling {@link #doFor} or {@link #doForEach} and down by one after
     * calling the corresponding {@link #doEnd} that closes the loop. This will be 1 on the
     * first {@link #doFor} or {@link #doForEach} call.
     */

<span id='Ext-XTemplateParser-method-doText'>    /**
</span>     * This method is called to process a piece of raw text from the tpl.
     * @param {String} text
     * @method doText
     */
    // doText: function (text)

<span id='Ext-XTemplateParser-method-doExpr'>    /**
</span>     * This method is called to process expressions (like `{[expr]}`).
     * @param {String} expr The body of the expression (inside &quot;{[&quot; and &quot;]}&quot;).
     * @method doExpr
     */
    // doExpr: function (expr)

<span id='Ext-XTemplateParser-method-doTag'>    /**
</span>     * This method is called to process simple tags (like `{tag}`).
     * @method doTag
     */
    // doTag: function (tag)

<span id='Ext-XTemplateParser-method-doElse'>    /**
</span>     * This method is called to process `&lt;tpl else&gt;`.
     * @method doElse
     */
    // doElse: function ()

<span id='Ext-XTemplateParser-method-doEval'>    /**
</span>     * This method is called to process `{% text %}`.
     * @param {String} text
     * @method doEval
     */
    // doEval: function (text)

<span id='Ext-XTemplateParser-method-doIf'>    /**
</span>     * This method is called to process `&lt;tpl if=&quot;action&quot;&gt;`. If there are other attributes,
     * these are passed in the actions object.
     * @param {String} action
     * @param {Object} actions Other actions keyed by the attribute name (such as 'exec').
     * @method doIf
     */
    // doIf: function (action, actions)

<span id='Ext-XTemplateParser-method-doElseIf'>    /**
</span>     * This method is called to process `&lt;tpl elseif=&quot;action&quot;&gt;`. If there are other attributes,
     * these are passed in the actions object.
     * @param {String} action
     * @param {Object} actions Other actions keyed by the attribute name (such as 'exec').
     * @method doElseIf
     */
    // doElseIf: function (action, actions)

<span id='Ext-XTemplateParser-method-doSwitch'>    /**
</span>     * This method is called to process `&lt;tpl switch=&quot;action&quot;&gt;`. If there are other attributes,
     * these are passed in the actions object.
     * @param {String} action
     * @param {Object} actions Other actions keyed by the attribute name (such as 'exec').
     * @method doSwitch
     */
    // doSwitch: function (action, actions)

<span id='Ext-XTemplateParser-method-doCase'>    /**
</span>     * This method is called to process `&lt;tpl case=&quot;action&quot;&gt;`. If there are other attributes,
     * these are passed in the actions object.
     * @param {String} action
     * @param {Object} actions Other actions keyed by the attribute name (such as 'exec').
     * @method doCase
     */
    // doCase: function (action, actions)

<span id='Ext-XTemplateParser-method-doDefault'>    /**
</span>     * This method is called to process `&lt;tpl default&gt;`.
     * @method doDefault
     */
    // doDefault: function ()

<span id='Ext-XTemplateParser-method-doEnd'>    /**
</span>     * This method is called to process `&lt;/tpl&gt;`. It is given the action type that started
     * the tpl and the set of additional actions.
     * @param {String} type The type of action that is being ended.
     * @param {Object} actions The other actions keyed by the attribute name (such as 'exec').
     * @method doEnd
     */
    // doEnd: function (type, actions) 

<span id='Ext-XTemplateParser-method-doFor'>    /**
</span>     * This method is called to process `&lt;tpl for=&quot;action&quot;&gt;`. If there are other attributes,
     * these are passed in the actions object.
     * @param {String} action
     * @param {Object} actions Other actions keyed by the attribute name (such as 'exec').
     * @method doFor
     */
    // doFor: function (action, actions)

<span id='Ext-XTemplateParser-method-doForEach'>    /**
</span>     * This method is called to process `&lt;tpl foreach=&quot;action&quot;&gt;`. If there are other
     * attributes, these are passed in the actions object.
     * @param {String} action
     * @param {Object} actions Other actions keyed by the attribute name (such as 'exec').
     * @method doForEach
     */
    // doForEach: function (action, actions)

<span id='Ext-XTemplateParser-method-doExec'>    /**
</span>     * This method is called to process `&lt;tpl exec=&quot;action&quot;&gt;`. If there are other attributes,
     * these are passed in the actions object.
     * @param {String} action
     * @param {Object} actions Other actions keyed by the attribute name.
     * @method doExec
     */
    // doExec: function (action, actions)

<span id='Ext-XTemplateParser-method-doTpl'>    /**
</span>     * This method is called to process an empty `&lt;tpl&gt;`. This is unlikely to need to be
     * implemented, so a default (do nothing) version is provided.
     * @method
     */
    doTpl: Ext.emptyFn,

<span id='Ext-XTemplateParser-method-parse'>    parse: function (str) {
</span>        var me = this,
            len = str.length,
            aliases = { elseif: 'elif' },
            topRe = me.topRe,
            actionsRe = me.actionsRe,
            index, stack, s, m, t, prev, frame, subMatch, begin, end, actions,
            prop;

        me.level = 0;
        me.stack = stack = [];

        for (index = 0; index &lt; len; index = end) {
            topRe.lastIndex = index;
            m = topRe.exec(str);

            if (!m) {
                me.doText(str.substring(index, len));
                break;
            }

            begin = m.index;
            end = topRe.lastIndex;

            if (index &lt; begin) {
                me.doText(str.substring(index, begin));
            }

            if (m[1]) {
                end = str.indexOf('%}', begin+2);
                me.doEval(str.substring(begin+2, end));
                end += 2;
            } else if (m[2]) {
                end = str.indexOf(']}', begin+2);
                me.doExpr(str.substring(begin+2, end));
                end += 2;
            } else if (m[3]) { // if ('{' token)
                me.doTag(m[3]);
            } else if (m[4]) { // content of a &lt;tpl xxxxxx xxx&gt; tag
                actions = null;
                while ((subMatch = actionsRe.exec(m[4])) !== null) {
                    s = subMatch[2] || subMatch[3];
                    if (s) {
                        s = Ext.String.htmlDecode(s); // decode attr value
                        t = subMatch[1];
                        t = aliases[t] || t;
                        actions = actions || {};
                        prev = actions[t];

                        if (typeof prev == 'string') {
                            actions[t] = [prev, s];
                        } else if (prev) {
                            actions[t].push(s);
                        } else {
                            actions[t] = s;
                        }
                    }
                }

                if (!actions) {
                    if (me.elseRe.test(m[4])) {
                        me.doElse();
                    } else if (me.defaultRe.test(m[4])) {
                        me.doDefault();
                    } else {
                        me.doTpl();
                        stack.push({ type: 'tpl' });
                    }
                }
                else if (actions['if']) {
                    me.doIf(actions['if'], actions);
                    stack.push({ type: 'if' });
                }
                else if (actions['switch']) {
                    me.doSwitch(actions['switch'], actions);
                    stack.push({ type: 'switch' });
                }
                else if (actions['case']) {
                    me.doCase(actions['case'], actions);
                }
                else if (actions['elif']) {
                    me.doElseIf(actions['elif'], actions);
                }
                else if (actions['for']) {
                    ++me.level;

                    // Extract property name to use from indexed item
                    if (prop = me.propRe.exec(m[4])) {
                        actions.propName = prop[1] || prop[2];
                    }
                    me.doFor(actions['for'], actions);
                    stack.push({ type: 'for', actions: actions });
                }
                else if (actions['foreach']) {
                    ++me.level;

                    // Extract property name to use from indexed item
                    if (prop = me.propRe.exec(m[4])) {
                        actions.propName = prop[1] || prop[2];
                    }
                    me.doForEach(actions['foreach'], actions);
                    stack.push({ type: 'foreach', actions: actions });
                }
                else if (actions.exec) {
                    me.doExec(actions.exec, actions);
                    stack.push({ type: 'exec', actions: actions });
                }
                /*
                else {
                    // todo - error
                }
                */
            } else if (m[0].length === 5) {
                // if the length of m[0] is 5, assume that we're dealing with an opening tpl tag with no attributes (e.g. &lt;tpl&gt;...&lt;/tpl&gt;)
                // in this case no action is needed other than pushing it on to the stack
                stack.push({ type: 'tpl' });
            } else {
                frame = stack.pop();
                me.doEnd(frame.type, frame.actions);
                if (frame.type == 'for' || frame.type == 'foreach') {
                    --me.level;
                }
            }
        }
    },

<span id='Ext-XTemplateParser-property-topRe'>    // Internal regexes
</span>    
    topRe:     /(?:(\{\%)|(\{\[)|\{([^{}]+)\})|(?:&lt;tpl([^&gt;]*)\&gt;)|(?:&lt;\/tpl&gt;)/g,
<span id='Ext-XTemplateParser-property-actionsRe'>    actionsRe: /\s*(elif|elseif|if|for|foreach|exec|switch|case|eval|between)\s*\=\s*(?:(?:&quot;([^&quot;]*)&quot;)|(?:'([^']*)'))\s*/g,
</span><span id='Ext-XTemplateParser-property-propRe'>    propRe:    /prop=(?:(?:&quot;([^&quot;]*)&quot;)|(?:'([^']*)'))/,
</span><span id='Ext-XTemplateParser-property-defaultRe'>    defaultRe: /^\s*default\s*$/,
</span><span id='Ext-XTemplateParser-property-elseRe'>    elseRe:    /^\s*else\s*$/
</span>});
</pre>
</body>
</html>
