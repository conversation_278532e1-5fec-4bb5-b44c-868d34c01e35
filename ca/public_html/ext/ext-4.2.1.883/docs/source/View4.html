<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-view-View'>/**
</span> * A mechanism for displaying data using custom layout templates and formatting.
 *
 * The View uses an {@link Ext.XTemplate} as its internal templating mechanism, and is bound to an
 * {@link Ext.data.Store} so that as the data in the store changes the view is automatically updated
 * to reflect the changes. The view also provides built-in behavior for many common events that can
 * occur for its contained items including click, doubleclick, mouseover, mouseout, etc. as well as a
 * built-in selection model. **In order to use these features, an {@link #itemSelector} config must
 * be provided for the View to determine what nodes it will be working with.**
 *
 * The example below binds a View to a {@link Ext.data.Store} and renders it into an {@link Ext.panel.Panel}.
 *
 *     @example
 *     Ext.define('Image', {
 *         extend: 'Ext.data.Model',
 *         fields: [
 *             { name:'src', type:'string' },
 *             { name:'caption', type:'string' }
 *         ]
 *     });
 *
 *     Ext.create('Ext.data.Store', {
 *         id:'imagesStore',
 *         model: 'Image',
 *         data: [
 *             { src:'http://www.sencha.com/img/20110215-feat-drawing.png', caption:'Drawing &amp; Charts' },
 *             { src:'http://www.sencha.com/img/20110215-feat-data.png', caption:'Advanced Data' },
 *             { src:'http://www.sencha.com/img/20110215-feat-html5.png', caption:'Overhauled Theme' },
 *             { src:'http://www.sencha.com/img/20110215-feat-perf.png', caption:'Performance Tuned' }
 *         ]
 *     });
 *
 *     var imageTpl = new Ext.XTemplate(
 *         '&lt;tpl for=&quot;.&quot;&gt;',
 *             '&lt;div style=&quot;margin-bottom: 10px;&quot; class=&quot;thumb-wrap&quot;&gt;',
 *               '&lt;img src=&quot;{src}&quot; /&gt;',
 *               '&lt;br/&gt;&lt;span&gt;{caption}&lt;/span&gt;',
 *             '&lt;/div&gt;',
 *         '&lt;/tpl&gt;'
 *     );
 *
 *     Ext.create('Ext.view.View', {
 *         store: Ext.data.StoreManager.lookup('imagesStore'),
 *         tpl: imageTpl,
 *         itemSelector: 'div.thumb-wrap',
 *         emptyText: 'No images available',
 *         renderTo: Ext.getBody()
 *     });
 */
Ext.define('Ext.view.View', {
    extend: 'Ext.view.AbstractView',
    alternateClassName: 'Ext.DataView',
    alias: 'widget.dataview',

<span id='Ext-view-View-property-deferHighlight'>    // private delay to buffer row highlighting/unhighlighting on mouse move.
</span>    // This is ignored if the public mouseOverOutBuffer remains a non-zero value
    deferHighlight: Ext.isIE7m ? 100 : 0,

<span id='Ext-view-View-cfg-mouseOverOutBuffer'>    /**
</span>     * @cfg {Number} [mouseOverOutBuffer=20]
     * The number of milliseconds to buffer mouseover and mouseout event handling on view items.
     * 
     * Configure this as `false` to process mouseover and mouseout events immediately.
     */
    mouseOverOutBuffer: 20,

<span id='Ext-view-View-property-inputTagRe'>    inputTagRe: /^textarea$|^input$/i,
</span>
    inheritableStatics: {
<span id='Ext-view-View-property-EventMap'>        EventMap: {
</span>            mousedown: 'MouseDown',
            mouseup: 'MouseUp',
            click: 'Click',
            dblclick: 'DblClick',
            contextmenu: 'ContextMenu',
            mouseover: 'MouseOver',
            mouseout: 'MouseOut',
            mouseenter: 'MouseEnter',
            mouseleave: 'MouseLeave',
            keydown: 'KeyDown',
            focus: 'Focus'
        }
    },

<span id='Ext-view-View-method-initComponent'>    initComponent: function() {
</span>        var me = this;
        me.callParent();
        
        // 
        if (me.mouseOverOutBuffer) {
            me.handleMouseOverOrOut = 
                Ext.Function.createBuffered(me.handleMouseOverOrOut, me.mouseOverOutBuffer, me);
            me.lastMouseOverOutEvent = new Ext.EventObjectImpl();
        }
        
        // Not buffering mouse over/out handling - buffer item highlighting.
        else if (me.deferHighlight){
            me.setHighlightedItem =
                Ext.Function.createBuffered(me.setHighlightedItem, me.deferHighlight, me);
        }
    },

<span id='Ext-view-View-method-addCmpEvents'>    addCmpEvents: function() {
</span>        this.addEvents(
<span id='Ext-view-View-event-beforeitemmousedown'>            /**
</span>             * @event beforeitemmousedown
             * Fires before the mousedown event on an item is processed. Returns false to cancel the default action.
             * @param {Ext.view.View} this
             * @param {Ext.data.Model} record The record that belongs to the item
             * @param {HTMLElement} item The item's element
             * @param {Number} index The item's index
             * @param {Ext.EventObject} e The raw event object
             */
            'beforeitemmousedown',
<span id='Ext-view-View-event-beforeitemmouseup'>            /**
</span>             * @event beforeitemmouseup
             * Fires before the mouseup event on an item is processed. Returns false to cancel the default action.
             * @param {Ext.view.View} this
             * @param {Ext.data.Model} record The record that belongs to the item
             * @param {HTMLElement} item The item's element
             * @param {Number} index The item's index
             * @param {Ext.EventObject} e The raw event object
             */
            'beforeitemmouseup',
<span id='Ext-view-View-event-beforeitemmouseenter'>            /**
</span>             * @event beforeitemmouseenter
             * Fires before the mouseenter event on an item is processed. Returns false to cancel the default action.
             * @param {Ext.view.View} this
             * @param {Ext.data.Model} record The record that belongs to the item
             * @param {HTMLElement} item The item's element
             * @param {Number} index The item's index
             * @param {Ext.EventObject} e The raw event object
             */
            'beforeitemmouseenter',
<span id='Ext-view-View-event-beforeitemmouseleave'>            /**
</span>             * @event beforeitemmouseleave
             * Fires before the mouseleave event on an item is processed. Returns false to cancel the default action.
             * @param {Ext.view.View} this
             * @param {Ext.data.Model} record The record that belongs to the item
             * @param {HTMLElement} item The item's element
             * @param {Number} index The item's index
             * @param {Ext.EventObject} e The raw event object
             */
            'beforeitemmouseleave',
<span id='Ext-view-View-event-beforeitemclick'>            /**
</span>             * @event beforeitemclick
             * Fires before the click event on an item is processed. Returns false to cancel the default action.
             * @param {Ext.view.View} this
             * @param {Ext.data.Model} record The record that belongs to the item
             * @param {HTMLElement} item The item's element
             * @param {Number} index The item's index
             * @param {Ext.EventObject} e The raw event object
             */
            'beforeitemclick',
<span id='Ext-view-View-event-beforeitemdblclick'>            /**
</span>             * @event beforeitemdblclick
             * Fires before the dblclick event on an item is processed. Returns false to cancel the default action.
             * @param {Ext.view.View} this
             * @param {Ext.data.Model} record The record that belongs to the item
             * @param {HTMLElement} item The item's element
             * @param {Number} index The item's index
             * @param {Ext.EventObject} e The raw event object
             */
            'beforeitemdblclick',
<span id='Ext-view-View-event-beforeitemcontextmenu'>            /**
</span>             * @event beforeitemcontextmenu
             * Fires before the contextmenu event on an item is processed. Returns false to cancel the default action.
             * @param {Ext.view.View} this
             * @param {Ext.data.Model} record The record that belongs to the item
             * @param {HTMLElement} item The item's element
             * @param {Number} index The item's index
             * @param {Ext.EventObject} e The raw event object
             */
            'beforeitemcontextmenu',
<span id='Ext-view-View-event-beforeitemkeydown'>            /**
</span>             * @event beforeitemkeydown
             * Fires before the keydown event on an item is processed. Returns false to cancel the default action.
             * @param {Ext.view.View} this
             * @param {Ext.data.Model} record The record that belongs to the item
             * @param {HTMLElement} item The item's element
             * @param {Number} index The item's index
             * @param {Ext.EventObject} e The raw event object. Use {@link Ext.EventObject#getKey getKey()} to retrieve the key that was pressed.
             */
            'beforeitemkeydown',
<span id='Ext-view-View-event-itemmousedown'>            /**
</span>             * @event itemmousedown
             * Fires when there is a mouse down on an item
             * @param {Ext.view.View} this
             * @param {Ext.data.Model} record The record that belongs to the item
             * @param {HTMLElement} item The item's element
             * @param {Number} index The item's index
             * @param {Ext.EventObject} e The raw event object
             */
            'itemmousedown',
<span id='Ext-view-View-event-itemmouseup'>            /**
</span>             * @event itemmouseup
             * Fires when there is a mouse up on an item
             * @param {Ext.view.View} this
             * @param {Ext.data.Model} record The record that belongs to the item
             * @param {HTMLElement} item The item's element
             * @param {Number} index The item's index
             * @param {Ext.EventObject} e The raw event object
             */
            'itemmouseup',
<span id='Ext-view-View-event-itemmouseenter'>            /**
</span>             * @event itemmouseenter
             * Fires when the mouse enters an item.
             * @param {Ext.view.View} this
             * @param {Ext.data.Model} record The record that belongs to the item
             * @param {HTMLElement} item The item's element
             * @param {Number} index The item's index
             * @param {Ext.EventObject} e The raw event object
             */
            'itemmouseenter',
<span id='Ext-view-View-event-itemmouseleave'>            /**
</span>             * @event itemmouseleave
             * Fires when the mouse leaves an item.
             * @param {Ext.view.View} this
             * @param {Ext.data.Model} record The record that belongs to the item
             * @param {HTMLElement} item The item's element
             * @param {Number} index The item's index
             * @param {Ext.EventObject} e The raw event object
             */
            'itemmouseleave',
<span id='Ext-view-View-event-itemclick'>            /**
</span>             * @event itemclick
             * Fires when an item is clicked.
             * @param {Ext.view.View} this
             * @param {Ext.data.Model} record The record that belongs to the item
             * @param {HTMLElement} item The item's element
             * @param {Number} index The item's index
             * @param {Ext.EventObject} e The raw event object
             */
            'itemclick',
<span id='Ext-view-View-event-itemdblclick'>            /**
</span>             * @event itemdblclick
             * Fires when an item is double clicked.
             * @param {Ext.view.View} this
             * @param {Ext.data.Model} record The record that belongs to the item
             * @param {HTMLElement} item The item's element
             * @param {Number} index The item's index
             * @param {Ext.EventObject} e The raw event object
             */
            'itemdblclick',
<span id='Ext-view-View-event-itemcontextmenu'>            /**
</span>             * @event itemcontextmenu
             * Fires when an item is right clicked.
             * @param {Ext.view.View} this
             * @param {Ext.data.Model} record The record that belongs to the item
             * @param {HTMLElement} item The item's element
             * @param {Number} index The item's index
             * @param {Ext.EventObject} e The raw event object
             */
            'itemcontextmenu',
<span id='Ext-view-View-event-itemkeydown'>            /**
</span>             * @event itemkeydown
             * Fires when a key is pressed while an item is currently selected.
             * @param {Ext.view.View} this
             * @param {Ext.data.Model} record The record that belongs to the item
             * @param {HTMLElement} item The item's element
             * @param {Number} index The item's index
             * @param {Ext.EventObject} e The raw event object. Use {@link Ext.EventObject#getKey getKey()} to retrieve the key that was pressed.
             */
            'itemkeydown',
<span id='Ext-view-View-event-beforecontainermousedown'>            /**
</span>             * @event beforecontainermousedown
             * Fires before the mousedown event on the container is processed. Returns false to cancel the default action.
             * @param {Ext.view.View} this
             * @param {Ext.EventObject} e The raw event object
             */
            'beforecontainermousedown',
<span id='Ext-view-View-event-beforecontainermouseup'>            /**
</span>             * @event beforecontainermouseup
             * Fires before the mouseup event on the container is processed. Returns false to cancel the default action.
             * @param {Ext.view.View} this
             * @param {Ext.EventObject} e The raw event object
             */
            'beforecontainermouseup',
<span id='Ext-view-View-event-beforecontainermouseover'>            /**
</span>             * @event beforecontainermouseover
             * Fires before the mouseover event on the container is processed. Returns false to cancel the default action.
             * @param {Ext.view.View} this
             * @param {Ext.EventObject} e The raw event object
             */
            'beforecontainermouseover',
<span id='Ext-view-View-event-beforecontainermouseout'>            /**
</span>             * @event beforecontainermouseout
             * Fires before the mouseout event on the container is processed. Returns false to cancel the default action.
             * @param {Ext.view.View} this
             * @param {Ext.EventObject} e The raw event object
             */
            'beforecontainermouseout',
<span id='Ext-view-View-event-beforecontainerclick'>            /**
</span>             * @event beforecontainerclick
             * Fires before the click event on the container is processed. Returns false to cancel the default action.
             * @param {Ext.view.View} this
             * @param {Ext.EventObject} e The raw event object
             */
            'beforecontainerclick',
<span id='Ext-view-View-event-beforecontainerdblclick'>            /**
</span>             * @event beforecontainerdblclick
             * Fires before the dblclick event on the container is processed. Returns false to cancel the default action.
             * @param {Ext.view.View} this
             * @param {Ext.EventObject} e The raw event object
             */
            'beforecontainerdblclick',
<span id='Ext-view-View-event-beforecontainercontextmenu'>            /**
</span>             * @event beforecontainercontextmenu
             * Fires before the contextmenu event on the container is processed. Returns false to cancel the default action.
             * @param {Ext.view.View} this
             * @param {Ext.EventObject} e The raw event object
             */
            'beforecontainercontextmenu',
<span id='Ext-view-View-event-beforecontainerkeydown'>            /**
</span>             * @event beforecontainerkeydown
             * Fires before the keydown event on the container is processed. Returns false to cancel the default action.
             * @param {Ext.view.View} this
             * @param {Ext.EventObject} e The raw event object. Use {@link Ext.EventObject#getKey getKey()} to retrieve the key that was pressed.
             */
            'beforecontainerkeydown',
<span id='Ext-view-View-event-containermouseup'>            /**
</span>             * @event containermouseup
             * Fires when there is a mouse up on the container
             * @param {Ext.view.View} this
             * @param {Ext.EventObject} e The raw event object
             */
            'containermouseup',
<span id='Ext-view-View-event-containermouseover'>            /**
</span>             * @event containermouseover
             * Fires when you move the mouse over the container.
             * @param {Ext.view.View} this
             * @param {Ext.EventObject} e The raw event object
             */
            'containermouseover',
<span id='Ext-view-View-event-containermouseout'>            /**
</span>             * @event containermouseout
             * Fires when you move the mouse out of the container.
             * @param {Ext.view.View} this
             * @param {Ext.EventObject} e The raw event object
             */
            'containermouseout',
<span id='Ext-view-View-event-containerclick'>            /**
</span>             * @event containerclick
             * Fires when the container is clicked.
             * @param {Ext.view.View} this
             * @param {Ext.EventObject} e The raw event object
             */
            'containerclick',
<span id='Ext-view-View-event-containerdblclick'>            /**
</span>             * @event containerdblclick
             * Fires when the container is double clicked.
             * @param {Ext.view.View} this
             * @param {Ext.EventObject} e The raw event object
             */
            'containerdblclick',
<span id='Ext-view-View-event-containercontextmenu'>            /**
</span>             * @event containercontextmenu
             * Fires when the container is right clicked.
             * @param {Ext.view.View} this
             * @param {Ext.EventObject} e The raw event object
             */
            'containercontextmenu',
<span id='Ext-view-View-event-containerkeydown'>            /**
</span>             * @event containerkeydown
             * Fires when a key is pressed while the container is focused, and no item is currently selected.
             * @param {Ext.view.View} this
             * @param {Ext.EventObject} e The raw event object. Use {@link Ext.EventObject#getKey getKey()} to retrieve the key that was pressed.
             */
            'containerkeydown',

<span id='Ext-view-View-event-selectionchange'>            /**
</span>             * @event
             * @inheritdoc Ext.selection.DataViewModel#selectionchange
             */
            'selectionchange',
<span id='Ext-view-View-event-beforeselect'>            /**
</span>             * @event
             * @inheritdoc Ext.selection.DataViewModel#beforeselect
             */
            'beforeselect',
<span id='Ext-view-View-event-beforedeselect'>            /**
</span>             * @event
             * @inheritdoc Ext.selection.DataViewModel#beforedeselect
             */
            'beforedeselect',
<span id='Ext-view-View-event-select'>            /**
</span>             * @event
             * @inheritdoc Ext.selection.DataViewModel#select
             */
            'select',
<span id='Ext-view-View-event-deselect'>            /**
</span>             * @event
             * @inheritdoc Ext.selection.DataViewModel#deselect
             */
            'deselect',
<span id='Ext-view-View-event-focuschange'>            /**
</span>             * @event
             * @inheritdoc Ext.selection.DataViewModel#focuschange
             */
            'focuschange',
            
<span id='Ext-view-View-event-highlightitem'>            /**
</span>             * @event highlightitem
             * Fires when a node is highlighted using keyboard navigation, or mouseover.
             * @param {Ext.view.View} view This View Component.
             * @param {Ext.Element} node The highlighted node.
             */
            'highlightitem',
            
<span id='Ext-view-View-event-unhighlightitem'>            /**
</span>             * @event unhighlightitem
             * Fires when a node is unhighlighted using keyboard navigation, or mouseout.
             * @param {Ext.view.View} view This View Component.
             * @param {Ext.Element} node The previously highlighted node.
             */
            'unhighlightitem'
        );
    },

<span id='Ext-view-View-method-getFocusEl'>    getFocusEl: function() {
</span>        return this.getTargetEl();
    },

<span id='Ext-view-View-method-afterRender'>    // @private
</span>    afterRender: function(){
        var me = this,
            onMouseOverOut = me.mouseOverOutBuffer ? me.onMouseOverOut : me.handleMouseOverOrOut;

        me.callParent();
        me.mon(me.getTargetEl(), {
            scope: me,
            /*
             * We need to make copies of this since some of the events fired here will end up triggering
             * a new event to be called and the shared event object will be mutated. In future we should
             * investigate if there are any issues with creating a new event object for each event that
             * is fired.
             */
            freezeEvent: true,
            click: me.handleEvent,
            mousedown: me.handleEvent,
            mouseup: me.handleEvent,
            dblclick: me.handleEvent,
            contextmenu: me.handleEvent,
            keydown: me.handleEvent,
            mouseover: onMouseOverOut,
            mouseout:  onMouseOverOut
        });
    },

<span id='Ext-view-View-method-onMouseOverOut'>    onMouseOverOut: function(e) {
</span>        var me = this;

        // Determining if we are entering or leaving view items is deferred until
        // mouse move churn settles down.
        me.lastMouseOverOutEvent.setEvent(e.browserEvent, true);
        me.handleMouseOverOrOut(me.lastMouseOverOutEvent);
    },

<span id='Ext-view-View-method-handleMouseOverOrOut'>    handleMouseOverOrOut: function(e) {
</span>        var me = this,
            isMouseout = e.type === 'mouseout',
            method = isMouseout ? e.getRelatedTarget : e.getTarget,
            nowOverItem = method.call(e, me.itemSelector) || method.call(e, me.dataRowSelector);

        // If the mouse event of whatever type tells use that we are no longer over the current mouseOverItem...
        if (!me.mouseOverItem || nowOverItem !== me.mouseOverItem) {

            // First fire mouseleave for the item we just left
            if (me.mouseOverItem) {
                e.item = me.mouseOverItem;
                e.newType = 'mouseleave';
                me.handleEvent(e);
            }

            // If we are over an item, fire the mouseenter
            me.mouseOverItem = nowOverItem;
            if (me.mouseOverItem) {
                e.item = me.mouseOverItem;
                e.newType = 'mouseenter';
                me.handleEvent(e);
            }
        }
    },

<span id='Ext-view-View-method-handleEvent'>    handleEvent: function(e) {
</span>        var me = this,
            key = e.type == 'keydown' &amp;&amp; e.getKey();

        if (me.processUIEvent(e) !== false) {
            me.processSpecialEvent(e);
        }

        // After all listeners have processed the event, then unless the user is typing into an input field,
        // prevent browser's default action on SPACE which is to focus the event's target element.
        // Focusing causes the browser to attempt to scroll the element into view.
        if (key === e.SPACE) {
            if (!me.inputTagRe.test(e.getTarget().tagName)) {
                e.stopEvent();
            }
        }
    },

<span id='Ext-view-View-method-processItemEvent'>    // Private template method
</span>    processItemEvent: Ext.emptyFn,
<span id='Ext-view-View-method-processContainerEvent'>    processContainerEvent: Ext.emptyFn,
</span><span id='Ext-view-View-method-processSpecialEvent'>    processSpecialEvent: Ext.emptyFn,
</span>
<span id='Ext-view-View-method-processUIEvent'>    processUIEvent: function(e) {
</span>
        // If the target event has been removed from the body (data update causing view DOM to be updated),
        // do not process. isAncestor uses native methods to check.
        if (!Ext.getBody().isAncestor(e.target)) {
            return;
        }

        var me = this,
            item = e.getTarget(me.getItemSelector(), me.getTargetEl()),
            map = this.statics().EventMap,
            index, record,
            type = e.type,
            newType = e.type,
            sm;

        // If the event is a mouseover/mouseout event converted to a mouseenter/mouseleave,
        // use that event type and ensure that the item is correct.
        if (e.newType) {
            newType = e.newType;
            item = e.item;
        }

        // For keydown events, try to get either the last focused item or the selected item.
        // If we have not focused an item, we'll just fire a container keydown event.
        if (!item &amp;&amp; type == 'keydown') {
            sm = me.getSelectionModel();
            record = sm.lastFocused || sm.getLastSelected();
            if (record) {
                item = me.getNode(record, true);
            }
        }

        if (item) {
            if (!record) {
                record = me.getRecord(item);
            }
            index = me.indexInStore ? me.indexInStore(record) : me.indexOf(item);

            // It is possible for an event to arrive for which there is no record... this
            // can happen with dblclick where the clicks are on removal actions (think a
            // grid w/&quot;delete row&quot; action column)
            if (!record || me.processItemEvent(record, item, index, e) === false) {
                return false;
            }

            if (
                (me['onBeforeItem' + map[newType]](record, item, index, e) === false) ||
                (me.fireEvent('beforeitem' + newType, me, record, item, index, e) === false) ||
                (me['onItem' + map[newType]](record, item, index, e) === false)
            ) {
                return false;
            }

            me.fireEvent('item' + newType, me, record, item, index, e);
        }
        else {
            if (
                (me.processContainerEvent(e) === false) ||
                (me['onBeforeContainer' + map[type]](e) === false) ||
                (me.fireEvent('beforecontainer' + type, me, e) === false) ||
                (me['onContainer' + map[type]](e) === false)
            ) {
                return false;
            }

            me.fireEvent('container' + type, me, e);
        }

        return true;
    },

<span id='Ext-view-View-method-onItemMouseEnter'>    // @private
</span>    onItemMouseEnter: function(record, item, index, e) {
        if (this.trackOver) {
            this.highlightItem(item);
        }
    },

<span id='Ext-view-View-method-onItemMouseLeave'>    // @private
</span>    onItemMouseLeave : function(record, item, index, e) {
        if (this.trackOver) {
            this.clearHighlight();
        }
    },

<span id='Ext-view-View-method-onItemMouseDown'>    // private, template methods
</span>    onItemMouseDown: Ext.emptyFn,
<span id='Ext-view-View-method-onItemMouseUp'>    onItemMouseUp: Ext.emptyFn,
</span><span id='Ext-view-View-method-onItemFocus'>    onItemFocus: Ext.emptyFn,
</span><span id='Ext-view-View-method-onItemClick'>    onItemClick: Ext.emptyFn,
</span><span id='Ext-view-View-method-onItemDblClick'>    onItemDblClick: Ext.emptyFn,
</span><span id='Ext-view-View-method-onItemContextMenu'>    onItemContextMenu: Ext.emptyFn,
</span><span id='Ext-view-View-method-onItemKeyDown'>    onItemKeyDown: Ext.emptyFn,
</span><span id='Ext-view-View-method-onBeforeItemMouseDown'>    onBeforeItemMouseDown: Ext.emptyFn,
</span><span id='Ext-view-View-method-onBeforeItemMouseUp'>    onBeforeItemMouseUp: Ext.emptyFn,
</span><span id='Ext-view-View-method-onBeforeItemFocus'>    onBeforeItemFocus: Ext.emptyFn,
</span><span id='Ext-view-View-method-onBeforeItemMouseEnter'>    onBeforeItemMouseEnter: Ext.emptyFn,
</span><span id='Ext-view-View-method-onBeforeItemMouseLeave'>    onBeforeItemMouseLeave: Ext.emptyFn,
</span><span id='Ext-view-View-method-onBeforeItemClick'>    onBeforeItemClick: Ext.emptyFn,
</span><span id='Ext-view-View-method-onBeforeItemDblClick'>    onBeforeItemDblClick: Ext.emptyFn,
</span><span id='Ext-view-View-method-onBeforeItemContextMenu'>    onBeforeItemContextMenu: Ext.emptyFn,
</span><span id='Ext-view-View-method-onBeforeItemKeyDown'>    onBeforeItemKeyDown: Ext.emptyFn,
</span>
<span id='Ext-view-View-method-onContainerMouseDown'>    // private, template methods
</span>    onContainerMouseDown: Ext.emptyFn,
<span id='Ext-view-View-method-onContainerMouseUp'>    onContainerMouseUp: Ext.emptyFn,
</span><span id='Ext-view-View-method-onContainerMouseOver'>    onContainerMouseOver: Ext.emptyFn,
</span><span id='Ext-view-View-method-onContainerMouseOut'>    onContainerMouseOut: Ext.emptyFn,
</span><span id='Ext-view-View-method-onContainerClick'>    onContainerClick: Ext.emptyFn,
</span><span id='Ext-view-View-method-onContainerDblClick'>    onContainerDblClick: Ext.emptyFn,
</span><span id='Ext-view-View-method-onContainerContextMenu'>    onContainerContextMenu: Ext.emptyFn,
</span><span id='Ext-view-View-method-onContainerKeyDown'>    onContainerKeyDown: Ext.emptyFn,
</span><span id='Ext-view-View-method-onBeforeContainerMouseDown'>    onBeforeContainerMouseDown: Ext.emptyFn,
</span><span id='Ext-view-View-method-onBeforeContainerMouseUp'>    onBeforeContainerMouseUp: Ext.emptyFn,
</span><span id='Ext-view-View-method-onBeforeContainerMouseOver'>    onBeforeContainerMouseOver: Ext.emptyFn,
</span><span id='Ext-view-View-method-onBeforeContainerMouseOut'>    onBeforeContainerMouseOut: Ext.emptyFn,
</span><span id='Ext-view-View-method-onBeforeContainerClick'>    onBeforeContainerClick: Ext.emptyFn,
</span><span id='Ext-view-View-method-onBeforeContainerDblClick'>    onBeforeContainerDblClick: Ext.emptyFn,
</span><span id='Ext-view-View-method-onBeforeContainerContextMenu'>    onBeforeContainerContextMenu: Ext.emptyFn,
</span><span id='Ext-view-View-method-onBeforeContainerKeyDown'>    onBeforeContainerKeyDown: Ext.emptyFn,
</span>
<span id='Ext-view-View-method-setHighlightedItem'>    // @private
</span>    setHighlightedItem: function(item){
        var me = this,
            highlighted = me.highlightedItem,
            overItemCls = me.overItemCls,
            beforeOverItemCls = me.beforeOverItemCls,
            previous;

        if (highlighted != item){
            if (highlighted) {
                Ext.fly(highlighted).removeCls(overItemCls);
                previous = highlighted.previousSibling;
                if (beforeOverItemCls &amp;&amp; previous) {
                    Ext.fly(previous).removeCls(beforeOverItemCls);
                }
                me.fireEvent('unhighlightitem', me, highlighted);
            }

            me.highlightedItem = item;

            if (item) {
                Ext.fly(item).addCls(me.overItemCls);
                previous = item.previousSibling;
                if (beforeOverItemCls &amp;&amp; previous) {
                    Ext.fly(previous).addCls(beforeOverItemCls);
                }
                me.fireEvent('highlightitem', me, item);
            }
        }
    },

<span id='Ext-view-View-method-highlightItem'>    /**
</span>     * Highlights a given item in the View. This is called by the mouseover handler if {@link #overItemCls}
     * and {@link #trackOver} are configured, but can also be called manually by other code, for instance to
     * handle stepping through the list via keyboard navigation.
     * @param {HTMLElement} item The item to highlight
     */
    highlightItem: function(item) {
        this.setHighlightedItem(item);
    },

<span id='Ext-view-View-method-clearHighlight'>    /**
</span>     * Un-highlights the currently highlighted item, if any.
     */
    clearHighlight: function() {
        this.setHighlightedItem(undefined);
    },
    
<span id='Ext-view-View-method-onUpdate'>    onUpdate: function(store, record){
</span>        var me = this,
            node,
            newNode,
            highlighted;
        
        if (me.viewReady) {
            node = me.getNode(record);
            newNode = me.callParent(arguments);
            highlighted = me.highlightedItem;
            
            if (highlighted &amp;&amp; highlighted === node) {
                delete me.highlightedItem;
                if (newNode) {
                    me.highlightItem(newNode);
                }
            }
        }
    },

<span id='Ext-view-View-method-refresh'>    refresh: function() {
</span>        this.clearHighlight();
        this.callParent(arguments);
    },
    
<span id='Ext-view-View-method-focusNode'>    /**
</span>     * Focuses a node in the view.
     * @param {Ext.data.Model} rec The record associated to the node that is to be focused.
     */
    focusNode: function(rec){
        var me          = this,
            node        = me.getNode(rec, true),
            el          = me.el,
            adjustmentY = 0,
            adjustmentX = 0,
            elRegion    = el.getRegion(),
            nodeRegion;

        // Viewable region must not include scrollbars, so use
        // DOM client dimensions
        elRegion.bottom = elRegion.top + el.dom.clientHeight;
        elRegion.right = elRegion.left + el.dom.clientWidth;
        if (node) {
            nodeRegion = Ext.fly(node).getRegion();
            // node is above
            if (nodeRegion.top &lt; elRegion.top) {
                adjustmentY = nodeRegion.top - elRegion.top;
            // node is below
            } else if (nodeRegion.bottom &gt; elRegion.bottom) {
                adjustmentY = nodeRegion.bottom - elRegion.bottom;
            }

            // node is left
            if (nodeRegion.left &lt; elRegion.left) {
                adjustmentX = nodeRegion.left - elRegion.left;
            // node is right
            } else if (nodeRegion.right &gt; elRegion.right) {
                adjustmentX = nodeRegion.right - elRegion.right;
            }

            if (adjustmentX || adjustmentY) {
                me.scrollBy(adjustmentX, adjustmentY, false);
            }
            el.focus();
        }
    }
});</pre>
</body>
</html>
