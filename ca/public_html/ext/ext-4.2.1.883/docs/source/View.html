<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>The source code</title>
  <link href="../resources/prettify/prettify.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="../resources/prettify/prettify.js"></script>
  <style type="text/css">
    .highlight { display: block; background-color: #ddd; }
  </style>
  <script type="text/javascript">
    function highlight() {
      document.getElementById(location.hash.replace(/#/, "")).className = "highlight";
    }
  </script>
</head>
<body onload="prettyPrint(); highlight();">
  <pre class="prettyprint lang-js"><span id='Ext-grid-locking-View'>/**
</span> * This class is used internally to provide a single interface when using
 * a locking grid. Internally, the locking grid creates two separate grids,
 * so this class is used to map calls appropriately.
 * @private
 */
Ext.define('Ext.grid.locking.View', {
    alternateClassName: 'Ext.grid.LockingView',

    mixins: {
        observable: 'Ext.util.Observable'
    },

<span id='Ext-grid-locking-View-property-isLockingView'>    /**
</span>     * @property {Boolean} isLockingView
     * `true` in this class to identify an object as an instantiated LockingView, or subclass thereof.
     */
    isLockingView: true,

<span id='Ext-grid-locking-View-property-eventRelayRe'>    eventRelayRe: /^(beforeitem|beforecontainer|item|container|cell|refresh)/,
</span>
<span id='Ext-grid-locking-View-method-constructor'>    constructor: function(config){
</span>        var me = this,
            eventNames = [],
            eventRe = me.eventRelayRe,
            locked = config.locked.getView(),
            normal = config.normal.getView(),
            events,
            event;

        Ext.apply(me, {
            lockedView: locked,
            normalView: normal,
            lockedGrid: config.locked,
            normalGrid: config.normal,
            panel: config.panel
        });
        me.mixins.observable.constructor.call(me, config);

        // relay events
        events = locked.events;
        for (event in events) {
            if (events.hasOwnProperty(event) &amp;&amp; eventRe.test(event)) {
                eventNames.push(event);
            }
        }
        me.relayEvents(locked, eventNames);
        me.relayEvents(normal, eventNames);

        normal.on({
            scope: me,
            itemmouseleave: me.onItemMouseLeave,
            itemmouseenter: me.onItemMouseEnter
        });

        locked.on({
            scope: me,
            itemmouseleave: me.onItemMouseLeave,
            itemmouseenter: me.onItemMouseEnter
        });
        
        me.panel.on({
            render: me.onPanelRender,
            scope: me
        });
    },

<span id='Ext-grid-locking-View-method-onPanelRender'>    onPanelRender: function() {
</span>        var me = this,
            mask = me.loadMask,
            cfg = {
                target: me.panel,
                msg: me.loadingText,
                msgCls: me.loadingCls,
                useMsg: me.loadingUseMsg,
                store: me.panel.store
            };

        // Because this is used as a View, it should have an el. Use the owning Lockable's body.
        // It also has to fire a render event so that Editing plugins can attach listeners
        me.el = me.panel.body;
        me.fireEvent('render', me);

        if (mask) {
            // either a config object 
            if (Ext.isObject(mask)) {
                cfg = Ext.apply(cfg, mask);
            }
            // Attach the LoadMask to a *Component* so that it can be sensitive to resizing during long loads.
            // If this DataView is floating, then mask this DataView.
            // Otherwise, mask its owning Container (or this, if there *is* no owning Container).
            // LoadMask captures the element upon render.
            me.loadMask = new Ext.LoadMask(cfg);
        }
    },

<span id='Ext-grid-locking-View-method-getGridColumns'>    getGridColumns: function() {
</span>        var cols = this.lockedGrid.headerCt.getVisibleGridColumns();
        return cols.concat(this.normalGrid.headerCt.getVisibleGridColumns());
    },

<span id='Ext-grid-locking-View-method-getEl'>    getEl: function(column){
</span>        return this.getViewForColumn(column).getEl();
    },

<span id='Ext-grid-locking-View-method-getViewForColumn'>    getViewForColumn: function(column) {
</span>        var view = this.lockedView,
            inLocked;

        view.headerCt.cascade(function(col){
            if (col === column) {
                inLocked = true;
                return false;
            }
        });

        return inLocked ? view : this.normalView;
    },

<span id='Ext-grid-locking-View-method-onItemMouseEnter'>    onItemMouseEnter: function(view, record){
</span>        var me = this,
            locked = me.lockedView,
            other = me.normalView,
            item;

        if (view.trackOver) {
            if (view !== locked) {
                other = locked;
            }
            item = other.getNode(record, false);
            other.highlightItem(item);
        }
    },

<span id='Ext-grid-locking-View-method-onItemMouseLeave'>    onItemMouseLeave: function(view, record){
</span>        var me = this,
            locked = me.lockedView,
            other = me.normalView;

        if (view.trackOver) {
            if (view !== locked) {
                other = locked;
            }
            other.clearHighlight();
        }
    },

<span id='Ext-grid-locking-View-method-relayFn'>    relayFn: function(name, args){
</span>        args = args || [];

        var view = this.lockedView;
        view[name].apply(view, args);
        view = this.normalView;
        view[name].apply(view, args);
    },

<span id='Ext-grid-locking-View-method-getSelectionModel'>    getSelectionModel: function(){
</span>        return this.panel.getSelectionModel();
    },

<span id='Ext-grid-locking-View-method-getStore'>    getStore: function(){
</span>        return this.panel.store;
    },

<span id='Ext-grid-locking-View-method-getNode'>    getNode: function(nodeInfo, dataRow) {
</span>        // default to the normal view
        return this.normalView.getNode(nodeInfo, dataRow);
    },

<span id='Ext-grid-locking-View-method-getCell'>    getCell: function(record, column) {
</span>        var view = this.getViewForColumn(column),
            row = view.getNode(record, true);
            
        return Ext.fly(row).down(column.getCellSelector());
    },

<span id='Ext-grid-locking-View-method-indexOf'>    indexOf: function(record) {
</span>        var result = this.lockedView.indexOf(record);
        if (!result) {
            result = this.normalView.indexOf(record);
        }
        return result;
    },

<span id='Ext-grid-locking-View-method-focus'>    focus: function() {
</span>        var p = this.getSelectionModel().getCurrentPosition(),
            v = p ? p.view : this.normalView;

        v.focus();
    },
    
<span id='Ext-grid-locking-View-method-focusRow'>    focusRow: function(row) {
</span>        this.normalView.focusRow(row);
    },

<span id='Ext-grid-locking-View-method-focusCell'>    focusCell: function(position) {
</span>        position.view.focusCell(position);
    },

<span id='Ext-grid-locking-View-method-isVisible'>    isVisible: function(deep) {
</span>        return this.panel.isVisible(deep);
    },

<span id='Ext-grid-locking-View-method-getRecord'>    getRecord: function(node) {
</span>        var result = this.lockedView.getRecord(node);
        if (!result) {
            result = this.normalView.getRecord(node);
        }
        return result;
    },
    
<span id='Ext-grid-locking-View-method-scrollBy'>    scrollBy: function(){
</span>        var normal = this.normalView;
        normal.scrollBy.apply(normal, arguments);
    },

<span id='Ext-grid-locking-View-method-addElListener'>    addElListener: function(eventName, fn, scope){
</span>        this.relayFn('addElListener', arguments);
    },

<span id='Ext-grid-locking-View-method-refreshNode'>    refreshNode: function(){
</span>        this.relayFn('refreshNode', arguments);
    },

<span id='Ext-grid-locking-View-method-refresh'>    refresh: function(){
</span>        this.relayFn('refresh', arguments);
    },

<span id='Ext-grid-locking-View-method-bindStore'>    bindStore: function(){
</span>        this.relayFn('bindStore', arguments);
    },

<span id='Ext-grid-locking-View-method-addRowCls'>    addRowCls: function(){
</span>        this.relayFn('addRowCls', arguments);
    },

<span id='Ext-grid-locking-View-method-removeRowCls'>    removeRowCls: function(){
</span>        this.relayFn('removeRowCls', arguments);
    },
    
<span id='Ext-grid-locking-View-method-destroy'>    destroy: function(){
</span>        var me = this,
            mask = me.loadMask;
            
        // Typically the mask unbinding is handled by the view, but
        // we aren't a normal view, so clear it out here
        me.clearListeners();
        if (mask &amp;&amp; mask.bindStore) {
            mask.bindStore(null);
        }
    }

});</pre>
</body>
</html>
