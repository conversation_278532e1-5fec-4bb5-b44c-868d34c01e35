Ext.data.JsonP.command_reference({"title":"Sencha Cmd Reference","guide":"<h1>sencha</h1>\n<div class='toc'>\n<p><strong>Contents</strong></p>\n<ol>\n<li><a href='#!/guide/command_reference-section-1'>sencha ant</a></li>\n<li><a href='#!/guide/command_reference-section-2'>sencha app</a></li>\n<li><a href='#!/guide/command_reference-section-3'>sencha app build</a></li>\n<li><a href='#!/guide/command_reference-section-4'>sencha app package</a></li>\n<li><a href='#!/guide/command_reference-section-5'>sencha app package build</a></li>\n<li><a href='#!/guide/command_reference-section-6'>sencha app package generate</a></li>\n<li><a href='#!/guide/command_reference-section-7'>sencha app package run</a></li>\n<li><a href='#!/guide/command_reference-section-8'>sencha app refresh</a></li>\n<li><a href='#!/guide/command_reference-section-9'>sencha app resolve</a></li>\n<li><a href='#!/guide/command_reference-section-10'>sencha app upgrade</a></li>\n<li><a href='#!/guide/command_reference-section-11'>sencha build</a></li>\n<li><a href='#!/guide/command_reference-section-12'>sencha compass</a></li>\n<li><a href='#!/guide/command_reference-section-13'>sencha compile</a></li>\n<li><a href='#!/guide/command_reference-section-14'>sencha compile concatenate</a></li>\n<li><a href='#!/guide/command_reference-section-15'>sencha compile exclude</a></li>\n<li><a href='#!/guide/command_reference-section-16'>sencha compile include</a></li>\n<li><a href='#!/guide/command_reference-section-17'>sencha compile intersect</a></li>\n<li><a href='#!/guide/command_reference-section-18'>sencha compile metadata</a></li>\n<li><a href='#!/guide/command_reference-section-19'>sencha compile page</a></li>\n<li><a href='#!/guide/command_reference-section-20'>sencha compile pop</a></li>\n<li><a href='#!/guide/command_reference-section-21'>sencha compile push</a></li>\n<li><a href='#!/guide/command_reference-section-22'>sencha compile restore</a></li>\n<li><a href='#!/guide/command_reference-section-23'>sencha compile save</a></li>\n<li><a href='#!/guide/command_reference-section-24'>sencha compile show-ignored</a></li>\n<li><a href='#!/guide/command_reference-section-25'>sencha compile union</a></li>\n<li><a href='#!/guide/command_reference-section-26'>sencha config</a></li>\n<li><a href='#!/guide/command_reference-section-27'>sencha fs</a></li>\n<li><a href='#!/guide/command_reference-section-28'>sencha fs concatenate</a></li>\n<li><a href='#!/guide/command_reference-section-29'>sencha fs difference</a></li>\n<li><a href='#!/guide/command_reference-section-30'>sencha fs minify</a></li>\n<li><a href='#!/guide/command_reference-section-31'>sencha fs mirror</a></li>\n<li><a href='#!/guide/command_reference-section-32'>sencha fs mirror all</a></li>\n<li><a href='#!/guide/command_reference-section-33'>sencha fs mirror image</a></li>\n<li><a href='#!/guide/command_reference-section-34'>sencha fs mirror sprite</a></li>\n<li><a href='#!/guide/command_reference-section-35'>sencha fs slice</a></li>\n<li><a href='#!/guide/command_reference-section-36'>sencha fs web</a></li>\n<li><a href='#!/guide/command_reference-section-37'>sencha fs web start</a></li>\n<li><a href='#!/guide/command_reference-section-38'>sencha fs web stop</a></li>\n<li><a href='#!/guide/command_reference-section-39'>sencha generate</a></li>\n<li><a href='#!/guide/command_reference-section-40'>sencha generate app</a></li>\n<li><a href='#!/guide/command_reference-section-41'>sencha generate controller</a></li>\n<li><a href='#!/guide/command_reference-section-42'>sencha generate form</a></li>\n<li><a href='#!/guide/command_reference-section-43'>sencha generate model</a></li>\n<li><a href='#!/guide/command_reference-section-44'>sencha generate package</a></li>\n<li><a href='#!/guide/command_reference-section-45'>sencha generate profile</a></li>\n<li><a href='#!/guide/command_reference-section-46'>sencha generate theme</a></li>\n<li><a href='#!/guide/command_reference-section-47'>sencha generate view</a></li>\n<li><a href='#!/guide/command_reference-section-48'>sencha generate workspace</a></li>\n<li><a href='#!/guide/command_reference-section-49'>sencha help</a></li>\n<li><a href='#!/guide/command_reference-section-50'>sencha io</a></li>\n<li><a href='#!/guide/command_reference-section-51'>sencha io app-set-group</a></li>\n<li><a href='#!/guide/command_reference-section-52'>sencha io create-app</a></li>\n<li><a href='#!/guide/command_reference-section-53'>sencha io create-auth-group</a></li>\n<li><a href='#!/guide/command_reference-section-54'>sencha io create-version</a></li>\n<li><a href='#!/guide/command_reference-section-55'>sencha io deploy</a></li>\n<li><a href='#!/guide/command_reference-section-56'>sencha io list-apps</a></li>\n<li><a href='#!/guide/command_reference-section-57'>sencha io list-groups</a></li>\n<li><a href='#!/guide/command_reference-section-58'>sencha io list-versions</a></li>\n<li><a href='#!/guide/command_reference-section-59'>sencha io set-group-auth</a></li>\n<li><a href='#!/guide/command_reference-section-60'>sencha io undeploy</a></li>\n<li><a href='#!/guide/command_reference-section-61'>sencha iofs</a></li>\n<li><a href='#!/guide/command_reference-section-62'>sencha iofs get</a></li>\n<li><a href='#!/guide/command_reference-section-63'>sencha iofs ls</a></li>\n<li><a href='#!/guide/command_reference-section-64'>sencha iofs put</a></li>\n<li><a href='#!/guide/command_reference-section-65'>sencha iofs rm</a></li>\n<li><a href='#!/guide/command_reference-section-66'>sencha js</a></li>\n<li><a href='#!/guide/command_reference-section-67'>sencha manifest</a></li>\n<li><a href='#!/guide/command_reference-section-68'>sencha manifest create</a></li>\n<li><a href='#!/guide/command_reference-section-69'>sencha package</a></li>\n<li><a href='#!/guide/command_reference-section-70'>sencha package add</a></li>\n<li><a href='#!/guide/command_reference-section-71'>sencha package build</a></li>\n<li><a href='#!/guide/command_reference-section-72'>sencha package extract</a></li>\n<li><a href='#!/guide/command_reference-section-73'>sencha package get</a></li>\n<li><a href='#!/guide/command_reference-section-74'>sencha package list</a></li>\n<li><a href='#!/guide/command_reference-section-75'>sencha package remove</a></li>\n<li><a href='#!/guide/command_reference-section-76'>sencha package repository</a></li>\n<li><a href='#!/guide/command_reference-section-77'>sencha package repository add</a></li>\n<li><a href='#!/guide/command_reference-section-78'>sencha package repository init</a></li>\n<li><a href='#!/guide/command_reference-section-79'>sencha package repository list</a></li>\n<li><a href='#!/guide/command_reference-section-80'>sencha package repository remove</a></li>\n<li><a href='#!/guide/command_reference-section-81'>sencha package repository show</a></li>\n<li><a href='#!/guide/command_reference-section-82'>sencha package repository sync</a></li>\n<li><a href='#!/guide/command_reference-section-83'>sencha package upgrade</a></li>\n<li><a href='#!/guide/command_reference-section-84'>sencha repository</a></li>\n<li><a href='#!/guide/command_reference-section-85'>sencha repository add</a></li>\n<li><a href='#!/guide/command_reference-section-86'>sencha repository init</a></li>\n<li><a href='#!/guide/command_reference-section-87'>sencha repository list</a></li>\n<li><a href='#!/guide/command_reference-section-88'>sencha repository remove</a></li>\n<li><a href='#!/guide/command_reference-section-89'>sencha repository show</a></li>\n<li><a href='#!/guide/command_reference-section-90'>sencha repository sync</a></li>\n<li><a href='#!/guide/command_reference-section-91'>sencha theme</a></li>\n<li><a href='#!/guide/command_reference-section-92'>sencha theme build</a></li>\n<li><a href='#!/guide/command_reference-section-93'>sencha theme capture</a></li>\n<li><a href='#!/guide/command_reference-section-94'>sencha theme slice</a></li>\n<li><a href='#!/guide/command_reference-section-95'>sencha upgrade</a></li>\n<li><a href='#!/guide/command_reference-section-96'>sencha which</a></li>\n</ol>\n</div>\n\n<h2 id='command_reference-section-1'>sencha ant</h2>\n\n<p>Invokes the embedded version of Apache Ant providing the <code>cmd.dir</code> property to\naccess Sencha Cmd using the following <code>taskdef</code>:</p>\n\n<pre><code>&lt;taskdef resource=\"com/sencha/ant/antlib.xml\"\n         classpath=\"${cmd.dir}/sencha.jar\"/&gt;\n</code></pre>\n\n<p>This command recognizes the <code>-Dproperty=value</code> syntax for properties used by\nAnt, even though this does not conform to normal Sencha Cmd parameter syntax.\nSimilar to directly invoking Ant, this command defaults to <code>\"build.xml\"</code> for\nthe script file basing its search on the current directory or the value of the\n<code>-cwd</code> switch passed to <code>sencha</code>.</p>\n\n<p>For example, the following command uses <code>\"../build.xml\"</code> as the script and\npasses in the <code>foo</code> property as \"42\" when executing the default target (since\nno target was specified).</p>\n\n<pre><code>sencha -cwd=.. ant -Dfoo=42\n</code></pre>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--debug</code>, <code>-d</code> - Enables Ant debug level messages</li>\n<li><code>--file</code>, <code>-f</code> - The Ant file to execute (default is build.xml)</li>\n<li><code>--props</code>, <code>-p</code> - One or more properties for the Ant script (name=value,...)</li>\n<li><code>--target</code>, <code>-t</code> - The target(s) to execute from the Ant script (comma separated)</li>\n<li><code>--verbose</code>, <code>-v</code> - Enables Ant verbose level messages</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha ant [options] targets...\n</code></pre>\n\n<h3>Where:</h3>\n\n<ul>\n<li><code>targets</code> - The Ant script targets to execute</li>\n</ul>\n\n\n<h2 id='command_reference-section-2'>sencha app</h2>\n\n<p>This category contains various commands for application management.</p>\n\n<h3>Categories</h3>\n\n<ul>\n<li><code>package</code> - Packages a Sencha Touch application for native app stores</li>\n</ul>\n\n\n<h3>Commands</h3>\n\n<ul>\n<li><code>build</code> - Executes the build process for an application</li>\n<li><code>refresh</code> - Updates the application metadata (aka \"bootstrap\") file</li>\n<li><code>resolve</code> - Generate dependencies in the loading order for the given app.</li>\n<li><code>upgrade</code> - Upgrade the current application to the specified SDK</li>\n</ul>\n\n\n<h2 id='command_reference-section-3'>sencha app build</h2>\n\n<p>This command builds the current application.</p>\n\n<pre><code>sencha app build [production|testing|native|package]\n</code></pre>\n\n<p>This will build your application in its current configuration and generate the\nbuild output in the <code>\"build/&lt;environment&gt;\"</code> folder. This location and many\nother properties can be configured in your application's configuration file\n<code>\".sencha/app/sencha.cfg\"</code> or the provided build script <code>\"build.xml\"</code>.</p>\n\n<p>For locally overriding build properties, the build script loads an optional\nproperties file called <code>\"local.properties\"</code> if present in your app folder. The\npurpose of this file is to define build properties that are in some way special\nto the local environment (that is, the local machine). As such, this file is\nnot intended to be tracked in source control.</p>\n\n<h4>Using Ant</h4>\n\n<p>This command is equivalent to running the provided Ant script directly using\nthe following command:</p>\n\n<pre><code>sencha ant [production|testing|native|package] build\n</code></pre>\n\n<p>To tune the process, start by looking at the generated <code>\"build.xml\"</code> in your\napp folder. The actual build logic is located in <code>\".sencha/app/build-impl.xml\"</code>.</p>\n\n<p>The <code>\"build.xml\"</code> script can be used by many Continuous Integration (CI) systems\nif they understand Apache Ant (most do). If not, the Sencha Cmd command line\ncan be used as you would during development. If the CI system understands Ant,\nhowever, it is often more convenient to use that integration rather than using\na command line invocation.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--archive</code>, <code>-a</code> - The directory path where all previous builds were stored.</li>\n<li><code>--clean</code>, <code>-c</code> - Remove previous build output prior to executing build</li>\n<li><code>--destination</code>, <code>-d</code> - The directory to which the build output is written</li>\n<li><code>--environment</code>, <code>-e</code> - The build environment, either 'testing', 'production', 'package' (Touch Specific), or 'native' (Touch Specific).</li>\n<li><code>--run</code>, <code>-r</code> - Enables automatically running builds with the native packager</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha app build [options] [environment] \\\n                           [destination] \\\n                           [archive]\n</code></pre>\n\n<h2 id='command_reference-section-4'>sencha app package</h2>\n\n<p>This category contains various commands for packing application for deployment\non phones using the Sencha Mobile Packager.</p>\n\n<h3>Commands</h3>\n\n<ul>\n<li><code>build</code> - Packages an app with the given configuration file</li>\n<li><code>generate</code> - Generates a Packager configuration JSON file</li>\n<li><code>run</code> - Packages and tries to run the application for the given configuration JSON file</li>\n</ul>\n\n\n<h2 id='command_reference-section-5'>sencha app package build</h2>\n\n<p>This command creates a native package of the current application.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--path</code>, <code>-p</code> - the path to the configuration file</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha app package build [options] path\n</code></pre>\n\n<h2 id='command_reference-section-6'>sencha app package generate</h2>\n\n<p>This command generates a Packager configuration JSON file.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--path</code>, <code>-p</code> - the path to the configuration file</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha app package generate [options] path\n</code></pre>\n\n<h2 id='command_reference-section-7'>sencha app package run</h2>\n\n<p>This command packages and runs the current application.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--path</code>, <code>-p</code> - the path to the configuration file</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha app package run [options] path\n</code></pre>\n\n<h2 id='command_reference-section-8'>sencha app refresh</h2>\n\n<p>This command regenerates the metadata file containing \"bootstrap\" data for the\ndynamic loader and class system.</p>\n\n<p>This must be done any time a class is added, renamed or removed.</p>\n\n<p>This command can also update any required packages if you have added package\nrequirements to your application. To refresh required packages (which may\ndownload those packages from remote repositories), do this:</p>\n\n<pre><code>sencha app refresh --packages\n</code></pre>\n\n<p>The additional parameters are seldom used.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--base-path</code>, <code>-b</code> - The base path to use to calculate relative path information. Defaults to index.html directory</li>\n<li><code>--metadata-file</code>, <code>-m</code> - The output filename for the js file containing the manifest metadata</li>\n<li><code>--packages</code>, <code>-pac</code> - Update required packages from remote repositories</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha app refresh [options] [metadata-file] \\\n                             [base-path]\n</code></pre>\n\n<h2 id='command_reference-section-9'>sencha app resolve</h2>\n\n<p>Generate a list of dependencies in the exact loading order for the current\napplication.</p>\n\n<p>NOTE: the resolved paths are relative to the current application's HTML file.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--output-file</code>, <code>-o</code> - The file path to write the results to in JSON format.</li>\n<li><code>--uri</code>, <code>-u</code> - The URI to the application\\'s HTML document</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha app resolve [options] uri \\\n                             output-file\n</code></pre>\n\n<h2 id='command_reference-section-10'>sencha app upgrade</h2>\n\n<p>This command upgrades the current application (based on current directory) to a\nspecified new framework.</p>\n\n<pre><code>sencha app upgrade /path/to/sdk\n</code></pre>\n\n<p>NOTE: This will upgrade the framework used by the current application in the\ncurrent workspace. This will effect any other applications in this workspace\nusing the same framework (i.e., \"ext\" or \"touch\").</p>\n\n<p>To upgrade just the generate scaffolding of your application to a new version\nof Sencha Cmd and not the framework in use, do this:</p>\n\n<pre><code>sencha app upgrade --noframework\n</code></pre>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--noappjs</code>, <code>-noa</code> - Disable upgrade of app.js</li>\n<li><code>--noframework</code>, <code>-nof</code> - Upgrade only the Sencha Cmd scaffolding and not the SDK</li>\n<li><code>--path</code>, <code>-p</code> - The path to the framework to which to upgrade</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha app upgrade [options] [path]\n</code></pre>\n\n<h2 id='command_reference-section-11'>sencha build</h2>\n\n<p>This command is used to process a legacy JSBuilder (\"jsb\") file.</p>\n\n<p>DEPRECATED: This command is provided for backwards compatibility with previous\nreleases. It is highly recommended to migrate applications to the new <code>compile</code>\ncommand and discontinue use of this command.</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha build \n</code></pre>\n\n<h2 id='command_reference-section-12'>sencha compass</h2>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--native</code>, <code>-n</code> - Enables / disables calls to system installed MRI compass</li>\n<li><code>--ruby-path</code>, <code>-r</code> - set the path to MRI ruby executable</li>\n</ul>\n\n\n<h2 id='command_reference-section-13'>sencha compile</h2>\n\n<p>This command category provides JavaScript compilation commands. The <code>compile</code>\ncategory maintains compilation state across its sub-commands so using <code>and</code> to\nconnect sub-commands can provide significant time savings compared to making\nrepeated calls.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--classpath</code>, <code>-cl</code> - Add one or more folders to the classpath</li>\n<li><code>--debug</code>, <code>-d</code> - Enable the debug option for the js directive parser</li>\n<li><code>--ignore</code>, <code>-ig</code> - Ignore files in the classpath with names containing substrings (comma separated)</li>\n<li><code>--options</code>, <code>-o</code> - Sets options for the js directive parser (name:value,...)</li>\n<li><code>--prefix</code>, <code>-p</code> - The file with header or license prefix to remove from source files</li>\n</ul>\n\n\n<h3>Commands</h3>\n\n<ul>\n<li><code>concatenate</code> - Produce output file by concatenating the files in the current set</li>\n<li><code>exclude</code> - Exclude files from the current set matching given criteria</li>\n<li><code>include</code> - Add files to the current set matching given criteria</li>\n<li><code>intersect</code> - Intersect specified saved sets to produce a new set</li>\n<li><code>metadata</code> - Generates information about the classes and files in the classpath</li>\n<li><code>page</code> - Compiles the content of a page of markup (html, jsp, php, etc)</li>\n<li><code>pop</code> - Pops the current set back to the most recently pushed set from the stack</li>\n<li><code>push</code> - Pushes the current set on to a stack for later pop to restore the current set</li>\n<li><code>restore</code> - Restores the enabled set of files from a previously saved set</li>\n<li><code>save</code> - Stores the currently enabled set of files by a given name</li>\n<li><code>show-ignored</code> - Shows any files being ignored in the classpath</li>\n<li><code>union</code> - Similar to include but selects only the files that match the given criteria</li>\n</ul>\n\n\n<h2 id='command_reference-section-14'>sencha compile concatenate</h2>\n\n<p>This command writes the current set to the specified output file.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--append</code>, <code>-a</code> - Appends output to output file instead of overwriting output file</li>\n<li><code>--beautify</code>, <code>-b</code> - enables / disables beautification of sources after compilation</li>\n<li><code>--closure</code>, <code>-cl</code> - Compress generate file using Closure Compiler</li>\n<li><code>--compress</code>, <code>-co</code> - Compress generated file using default compressor (YUI)</li>\n<li><code>--output-file</code>, <code>-o</code> - The output file name (or $ for stdout)</li>\n<li><code>--remove-text-references</code>, <code>-r</code> - enables / disables reference optimization by converting string classnames to static references</li>\n<li><code>--strip-comments</code>, <code>-st</code> - Strip comments from the generated file</li>\n<li><code>--uglify</code>, <code>-u</code> - Compress generate file using uglify-js</li>\n<li><code>--yui</code>, <code>-y</code> - Compress generated file using YUI Compressor</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha compile concatenate [options] output-file\n</code></pre>\n\n<h2 id='command_reference-section-15'>sencha compile exclude</h2>\n\n<p>This command removes from the current set any files matching the criteria.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--all</code>, <code>-a</code> - Select all files in global cache (ignores other options)</li>\n<li><code>--class</code>, <code>-c</code> - Selects files according to the specified class names</li>\n<li><code>--file</code>, <code>-f</code> - Selects the specified file names (supports glob patterns)</li>\n<li><code>--namespace</code>, <code>-na</code> - Selects all files with class definitions in the given namespace(s)</li>\n<li><code>--not</code>, <code>-no</code> - Inverts the matching criteria</li>\n<li><code>--recursive</code>, <code>-r</code> - Enable traversal of dependency relationships when selecting files</li>\n<li><code>--set</code>, <code>-s</code> - Selects files from on a previously saved set (ignores other options)</li>\n<li><code>--tag</code>, <code>-t</code> - Selects all files with the specified '//@tag' values</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha compile exclude [options] \n</code></pre>\n\n<h2 id='command_reference-section-16'>sencha compile include</h2>\n\n<p>This command adds the files matching the criteria to the current set.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--all</code>, <code>-a</code> - Select all files in global cache (ignores other options)</li>\n<li><code>--class</code>, <code>-c</code> - Selects files according to the specified class names</li>\n<li><code>--file</code>, <code>-f</code> - Selects the specified file names (supports glob patterns)</li>\n<li><code>--namespace</code>, <code>-na</code> - Selects all files with class definitions in the given namespace(s)</li>\n<li><code>--not</code>, <code>-no</code> - Inverts the matching criteria</li>\n<li><code>--recursive</code>, <code>-r</code> - Enable traversal of dependency relationships when selecting files</li>\n<li><code>--set</code>, <code>-s</code> - Selects files from on a previously saved set (ignores other options)</li>\n<li><code>--tag</code>, <code>-t</code> - Selects all files with the specified '//@tag' values</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha compile include [options] \n</code></pre>\n\n<h2 id='command_reference-section-17'>sencha compile intersect</h2>\n\n<p>This command produces as in the current set the files that are contained in all\nof the specified input sets. Alternatively, this command can include files that\nare present in a present in a certain minimum number of sets.</p>\n\n<p>This command only operates on saved sets (unlike most other set operations).</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--min-match</code>, <code>-m</code> - The minimum number of sets containing a file to cause a match (-1 = all)</li>\n<li><code>--name</code>, <code>-n</code> - The name by which to save the intersection as a set</li>\n<li><code>--sets</code>, <code>-s</code> - The sets to include in the intersection</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha compile intersect [options] \n</code></pre>\n\n<h2 id='command_reference-section-18'>sencha compile metadata</h2>\n\n<p>This command generates various forms of metadata extracted from the current set\nof files. This data can be exported in various formats (e.g., JSON or JSONP).</p>\n\n<h3>Options</h3>\n\n<h4>Data Type</h4>\n\n<p>Choose one of the following options</p>\n\n<ul>\n<li><code>--alias</code>, <code>-ali</code> - Generate class name to alias information</li>\n<li><code>--alternates</code>, <code>-alt</code> - Generate class alternate name information</li>\n<li><code>--definitions</code>, <code>-d</code> - Generate symbol information</li>\n<li><code>--filenames</code>, <code>-f</code> - Generate source file name information</li>\n<li><code>--loader-paths</code>, <code>-l</code> - Generate dynamic loader path information</li>\n<li><code>--manifest</code>, <code>-m</code> - Generate a class definition manifest file</li>\n</ul>\n\n\n<h4>Format</h4>\n\n<p>Choose one of the following options</p>\n\n<ul>\n<li><code>--json</code>, <code>-json</code> - Generate data in JSON format</li>\n<li><code>--jsonp</code>, <code>-jsonp</code> - Generate data in JSONP format using the given function</li>\n<li><code>--tpl</code>, <code>-t</code> - The line template for generating filenames as text (e.g. <script src=\"{0}\"></script>)</li>\n</ul>\n\n\n<h4>Misc</h4>\n\n<ul>\n<li><code>--append</code>, <code>-ap</code> - Appends output to output file instead of overwriting output file</li>\n<li><code>--base-path</code>, <code>-b</code> - Set the base path for relative path references</li>\n<li><code>--output-file</code>, <code>-o</code> - The output file name (or $ for stdout)</li>\n<li><code>--separator</code>, <code>-s</code> - The delimiter character used to separate multiple templates</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha compile metadata [options] \n</code></pre>\n\n<h2 id='command_reference-section-19'>sencha compile page</h2>\n\n<p>This command processes a markup file as input and generates an output file with\ncertain sections rewritten.</p>\n\n<p>If the <code>-name</code> option is specified, the dependency graph of all required files\nis saved as a file set with that name (see also the <code>save</code> command).</p>\n\n<p>If the <code>-name</code> option is not specified, all required files are instead written\nto the \"all-classes.js\" file.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--append</code>, <code>-ap</code> - Appends output to output file instead of overwriting output file</li>\n<li><code>--beautify</code>, <code>-b</code> - enables / disables beautification of sources after compilation</li>\n<li><code>--classes-file</code>, <code>-cla</code> - the name of the js file containing the concatenated output</li>\n<li><code>--closure</code>, <code>-clo</code> - Compress generate file using Closure Compiler</li>\n<li><code>--compress</code>, <code>-co</code> - Compress generated file using default compressor (YUI)</li>\n<li><code>--input-file</code>, <code>-i</code> - the html page to process</li>\n<li><code>--name</code>, <code>-n</code> - sets a reference name for the page</li>\n<li><code>--output-page</code>, <code>-o</code> - the output html page</li>\n<li><code>--remove-text-references</code>, <code>-r</code> - enables / disables reference optimization by converting string classnames to static references</li>\n<li><code>--scripts</code>, <code>-sc</code> - inject the given script path into the generated markup ahead of the all classes file</li>\n<li><code>--strip-comments</code>, <code>-str</code> - Strip comments from the generated file</li>\n<li><code>--uglify</code>, <code>-u</code> - Compress generate file using uglify-js</li>\n<li><code>--yui</code>, <code>-y</code> - Compress generated file using YUI Compressor</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha compile page [options] output-page\n</code></pre>\n\n<h2 id='command_reference-section-20'>sencha compile pop</h2>\n\n<p>This command restores the current set of files from the \"stack\". This state was\npreviously put on the \"stack\" using the <code>push</code> command.</p>\n\n<pre><code>sencha compile ... \\\n            and push \\\n            and ... \\\n            and pop\n            and ...\n</code></pre>\n\n<p>Between the <code>push</code> and <code>pop</code> commands the current file set can be adjusted as\nneeded and then restored for subsequent commands.</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha compile pop \n</code></pre>\n\n<h2 id='command_reference-section-21'>sencha compile push</h2>\n\n<p>This command saves the current set of files on a \"stack\" to easily save and\nrestore state.</p>\n\n<pre><code>sencha compile ... \\\n            and push \\\n            and ... \\\n            and pop\n            and ...\n</code></pre>\n\n<p>Between the <code>push</code> and <code>pop</code> commands the current file set can be adjusted as\nneeded and then restored for subsequent commands.</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha compile push \n</code></pre>\n\n<h2 id='command_reference-section-22'>sencha compile restore</h2>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha compile restore String\n</code></pre>\n\n<h2 id='command_reference-section-23'>sencha compile save</h2>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha compile save String\n</code></pre>\n\n<h2 id='command_reference-section-24'>sencha compile show-ignored</h2>\n\n<p>Displays a list of all files found in the <code>classpath</code> but matching an <code>-ignore</code>\ncriteria.</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha compile show-ignored \n</code></pre>\n\n<h2 id='command_reference-section-25'>sencha compile union</h2>\n\n<p>This command adds files matching the criteria to the current set. This is\nsimilar to the <code>include</code> command except that this command first removes all\nfiles from the current set. In other words, this command makes the current set\nequal to only those files that match the criteria.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--all</code>, <code>-a</code> - Select all files in global cache (ignores other options)</li>\n<li><code>--class</code>, <code>-c</code> - Selects files according to the specified class names</li>\n<li><code>--file</code>, <code>-f</code> - Selects the specified file names (supports glob patterns)</li>\n<li><code>--namespace</code>, <code>-na</code> - Selects all files with class definitions in the given namespace(s)</li>\n<li><code>--not</code>, <code>-no</code> - Inverts the matching criteria</li>\n<li><code>--recursive</code>, <code>-r</code> - Enable traversal of dependency relationships when selecting files</li>\n<li><code>--set</code>, <code>-s</code> - Selects files from on a previously saved set (ignores other options)</li>\n<li><code>--tag</code>, <code>-t</code> - Selects all files with the specified '//@tag' values</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha compile union [options] \n</code></pre>\n\n<h2 id='command_reference-section-26'>sencha config</h2>\n\n<p>This command can be used to either set configuration options singly or load a\nconfiguration file to set multiple options.</p>\n\n<p>Because these configuration options are only held for the current execution of\nSencha Cmd, you will almost always use <code>then</code> to chain commands that will now\nbe executed with the modified configuration.</p>\n\n<p>For example, to change the theme of an Ext JS application for a build:</p>\n\n<pre><code>sencha config -prop app.theme=ext-theme-neptune then app build\n</code></pre>\n\n<p>Multiple properties can be loaded from a properties file:</p>\n\n<pre><code>sencha config -file neptune.properties then app build\n</code></pre>\n\n<p>The content of <code>\"neptune.properties\"</code> might be something like this:</p>\n\n<pre><code>app.theme=ext-theme-neptune\napp.build.dir=${app.dir}/build/neptune\n</code></pre>\n\n<p>In this case, an alternative would be to set <code>app.dir</code> in the applications's\n<code>\"sencha.cfg\"</code> file like so:</p>\n\n<pre><code>app.build.dir=${app.dir}/build/${app.theme}\n</code></pre>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--file</code>, <code>-f</code> - The properties file to load</li>\n<li><code>--prop</code>, <code>-p</code> - One or more property names and values to set</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha config [options] \n</code></pre>\n\n<h2 id='command_reference-section-27'>sencha fs</h2>\n\n<p>This category provides commands for manipulating files.</p>\n\n<h3>Categories</h3>\n\n<ul>\n<li><code>mirror</code> - Commands for making mirror images for RTL languages</li>\n<li><code>web</code> - Manages a simple HTTP file server</li>\n</ul>\n\n\n<h3>Commands</h3>\n\n<ul>\n<li><code>concatenate</code> - Concatenate multiple files into one</li>\n<li><code>difference</code> - Generates deltas between two files in JSON format</li>\n<li><code>minify</code> - Minify a JavaScript file</li>\n<li><code>slice</code> - Generates image slices from a given image directed by a JSON manifest</li>\n</ul>\n\n\n<h2 id='command_reference-section-28'>sencha fs concatenate</h2>\n\n<p>This command combines multiple input files into a single output file.</p>\n\n<pre><code>sencha fs concat -to=output.js input1.js input2.js input3.js\n</code></pre>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--from</code>, <code>-f</code> - List of files to concatenate, comma-separated</li>\n<li><code>--to</code>, <code>-t</code> - The destination file to write concatenated content</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha fs concatenate [options] files...\n</code></pre>\n\n<h2 id='command_reference-section-29'>sencha fs difference</h2>\n\n<p>This command produces a delta (or \"patch\") file between input files.</p>\n\n<pre><code>sencha fs diff -from=base.js -to=modified.js -delta=patch\n</code></pre>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha fs difference \n</code></pre>\n\n<h2 id='command_reference-section-30'>sencha fs minify</h2>\n\n<p>This command produced minified files using various back-end compressors.</p>\n\n<pre><code>sencha fs minify -yui -from=in.js -to=out.js\n\nsencha fs minify -closure -from=in.js -to=out.js\n</code></pre>\n\n<p>The legacy syntax is also supported:</p>\n\n<pre><code>sencha fs minify -compressor=yuicompressor -from=in.js -to=out.js\n\nsencha fs minify -compressor=closurecompiler -from=in.js -to=out.js\n</code></pre>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--closure</code>, <code>-cl</code> - Enable the Google Closure Compiler</li>\n<li><code>--from</code>, <code>-f</code> - The input js file to minify</li>\n<li><code>--to</code>, <code>-t</code> - The destination filename for minified output.</li>\n<li><code>--yui</code>, <code>-y</code> - Enable the YUI Compressor</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha fs minify [options] \n</code></pre>\n\n<h2 id='command_reference-section-31'>sencha fs mirror</h2>\n\n<p>Commands for create horizontal mirror of images and sprites for RTL locales.</p>\n\n<p>This family of commands is intended for automated production of \"derivative\"\nimages based on hand maintained and designed image assets authored in the more\nfamiliar, left-to-right (LTR) form.</p>\n\n<h3>Commands</h3>\n\n<ul>\n<li><code>all</code> - Horizontally flip a folder of images and sprites based on naming convention</li>\n<li><code>image</code> - Horizontally flip an image</li>\n<li><code>sprite</code> - Horizontally flip a \"sprite\" (multi-cell image)</li>\n</ul>\n\n\n<h2 id='command_reference-section-32'>sencha fs mirror all</h2>\n\n<p>This command creates horizontal mirror images of a folder of images and/or\nsprites. This command requires some name consistency in order to differentiate\noutput files from input files and the geometry of sprites.</p>\n\n<p>Sprites must have a name segment that looks like \"4x3\" to define its geometry.\nThis is understood as \"columns\" x \"rows\" or, in this example, 4 columns and 3\nrows.</p>\n\n<p>The following examples all fit this pattern:</p>\n\n<ul>\n<li>tools-2x12.png</li>\n<li>sprite_12x3.gif</li>\n<li>some-3x5-sprite.png</li>\n</ul>\n\n\n<p>The input files and output files are separated by a suffix that must be given.\nTHe output files will be produced from the input files applying the suffix. By\ndefault, the output files are written to the same folder as the input files.\nThis can be changed by specifying \"-out\".</p>\n\n<p>For example:</p>\n\n<pre><code>sencha fs mirror all -r -suffix=-rtl /path/to/images\n</code></pre>\n\n<p>The above command performs the following:</p>\n\n<ul>\n<li>Scans <code>\"/path/to/images\"</code> (and all sub folders due to <code>-r</code>) for images.</li>\n<li>Any image not ending in <code>\"-rtl\"</code> is considered an input file.</li>\n<li>Any input image with sprite geometry in its name has its cells flipped.</li>\n<li>Other input images are entirely flipped.</li>\n<li>The input files are written using their original name plus the suffix.</li>\n<li>Up-to-date checks are made but can be skipped by passing <code>-overwrite</code>.</li>\n<li>Files are written to <code>\"/path/to/images\"</code>.</li>\n</ul>\n\n\n<p>By passing the <code>-format</code> switch, the format of the output images can be set\nto be other than the same format as the original file. For example, one could\nconvert PNG files to GIF by passing <code>-format=gif</code>. This does only basic image\nconversion and no advanced image processing. Simple color quantization can be\nenabled using <code>-quantize</code>.</p>\n\n<p>For example:</p>\n\n<pre><code>sencha fs mirror all all -format=gif -ext=png -quantize -out=/out/dir \\\n     -suffix=-rtl /some/pngs\n</code></pre>\n\n<p>The above command will process all <code>\"png\"</code> images and will write out their\n<code>\"gif\"</code> versions (using color quantization) to a different folder.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--dry-run</code>, <code>-d</code> - When set no images will be saved but all normal work is still done</li>\n<li><code>--extensions</code>, <code>-e</code> - Comma-separated list of image extensions (default is \"gif,png\")</li>\n<li><code>--format</code>, <code>-f</code> - The image format to write all output files (e.g., \"-f=png\")</li>\n<li><code>--output-dir</code>, <code>-ou</code> - The output folder for generated images (defaults to input folder)</li>\n<li><code>--overwrite</code>, <code>-ov</code> - Disable up-to-date check and always generate output file</li>\n<li><code>--quantize</code>, <code>-q</code> - Enable basic color quantization (useful with -format=gif)</li>\n<li><code>--recurse</code>, <code>-r</code> - Process the input folder recursively (i.e., include sub-folders)</li>\n<li><code>--suffix</code>, <code>-s</code> - The suffix of output files (e.g., \"-rtl\")</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha fs mirror all [options] File\n</code></pre>\n\n<h3>Where:</h3>\n\n<ul>\n<li><code>File</code> - The input folder to process</li>\n</ul>\n\n\n<h2 id='command_reference-section-33'>sencha fs mirror image</h2>\n\n<p>This command create a horizontal mirror image of a given input file.</p>\n\n<p>For example</p>\n\n<pre><code>sencha fs mirror image foo.png foo-rtl.png\n</code></pre>\n\n<p>The above command creates <code>\"foo-rtl.png\"</code> from <code>\"foo.png\"</code>.</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha fs mirror image File \\\n                       File\n</code></pre>\n\n<h2 id='command_reference-section-34'>sencha fs mirror sprite</h2>\n\n<p>This command create a horizontal mirror image of the cells in a given sprite.</p>\n\n<p>For example</p>\n\n<pre><code>sencha fs mirror sprite -rows=2 -cols=4 sprite.png sprite-rtl.png\n</code></pre>\n\n<p>The above command horizontally flips each cell in the 2x4 sprite <code>\"sprite.png\"</code>\nand produces <code>\"sprite-rtl.png\"</code>.</p>\n\n<p><code>NOTE</code>: The number of rows and columns are required.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--columns</code>, <code>-c</code> - The number of columns in the sprite.</li>\n<li><code>--rows</code>, <code>-r</code> - The number of rows in the sprite.</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha fs mirror sprite [options] File \\\n                                  File\n</code></pre>\n\n<h2 id='command_reference-section-35'>sencha fs slice</h2>\n\n<p>This command performs image slicing and manipulation driven by the contents of\na JSON manifest file. The manifest file contains an array of image area\ndefinitions that further contain a set of \"slices\" to make.</p>\n\n<p>This file and the corresponding image are typically produced for a Theme as\npart of the theme package build. For details on this process, consult this\nguide:</p>\n\n<p>http://docs.sencha.com/ext-js/4-2/#!/guide/command_slice</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--format</code>, <code>-f</code> - The image format to save - either \"png\" or \"gif\" (the default)</li>\n<li><code>--image</code>, <code>-i</code> - The image to slice</li>\n<li><code>--manifest</code>, <code>-m</code> - The slicer manifest (JSON) file</li>\n<li><code>--out-dir</code>, <code>-o</code> - The root folder to which sliced images are written</li>\n<li><code>--quantized</code>, <code>-q</code> - Enables image quantization (default is true)</li>\n<li><code>--tolerate-conflicts</code>, <code>-t</code> - Tolerate conflicts in slice manifest</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha fs slice [options] \n</code></pre>\n\n<h2 id='command_reference-section-36'>sencha fs web</h2>\n\n<p>This category provides commands to manage a simple HTTP file server.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--port</code>, <code>-p</code> - Set the port for the web server</li>\n</ul>\n\n\n<h3>Commands</h3>\n\n<ul>\n<li><code>start</code> - Starts a static file Web Server on a port</li>\n<li><code>stop</code> - Stops the local web server on the specific port</li>\n</ul>\n\n\n<h2 id='command_reference-section-37'>sencha fs web start</h2>\n\n<p>This command starts the Web server and routes requests to the specified files.\nFor example:</p>\n\n<pre><code>sencha fs web -port 8000 start -map foo=/path/to/foo,bar=/another/path\n</code></pre>\n\n<p>Given the above, the following URL entered in a browser will display the files\nin <code>\"/path/to/foo\"</code>:</p>\n\n<pre><code>http://localhost:8000/foo\n</code></pre>\n\n<p>And this URL will display the files in <code>\"/another/path\"</code>:</p>\n\n<pre><code>http://localhost:8000/bar\n</code></pre>\n\n<p>To stop the server, press CTRL+C or run the <code>sencha fs web stop</code> command:</p>\n\n<pre><code>sencha fs web -port 8000 stop\n</code></pre>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--mappings</code>, <code>-m</code> - List of local folders (ex: [sub=]/path/to/folder)</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha fs web start [options] \n</code></pre>\n\n<h2 id='command_reference-section-38'>sencha fs web stop</h2>\n\n<p>This command stops the Web server previously started by <code>sencha fs web start</code>.</p>\n\n<p>For example:</p>\n\n<pre><code>sencha fs web -port 8000 start -map foo=/path/to/foo,bar=/another/path\n</code></pre>\n\n<p>From another terminal or console, this will stop the server:</p>\n\n<pre><code>sencha fs web -port 8000 stop\n</code></pre>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha fs web stop \n</code></pre>\n\n<h2 id='command_reference-section-39'>sencha generate</h2>\n\n<p>This category contains code generators used to generate applications as well\nas add new classes to the application.</p>\n\n<h3>Commands</h3>\n\n<ul>\n<li><code>app</code> - Generates a starter application</li>\n<li><code>controller</code> - Generates a Controller for the current application</li>\n<li><code>form</code> - Generates a Form for the current application (Sencha Touch Specific)</li>\n<li><code>model</code> - Generates a Model for the current application</li>\n<li><code>package</code> - Generates a starter package</li>\n<li><code>profile</code> - Generates a Profile for the current application (Sencha Touch Specific)</li>\n<li><code>theme</code> - Generates a theme page for slice operations (Ext JS Specific)</li>\n<li><code>view</code> - Generates a View for the current application (Ext JS Specific)</li>\n<li><code>workspace</code> - Initializes a multi-app workspace</li>\n</ul>\n\n\n<h2 id='command_reference-section-40'>sencha generate app</h2>\n\n<p>This command generates an empty application given a name and target folder.</p>\n\n<p>The application can be extended using other <code>sencha generate</code> commands (e.g.,\n<code>sencha generate model</code>).</p>\n\n<p>Other application actions are provided in the <code>sencha app</code> category (e.g.,\n<code>sencha app build</code>).</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--controller-name</code>, <code>-c</code> - The name of the default Controller</li>\n<li><code>--library</code>, <code>-l</code> - the pre-built library to use (core or all). Default: core</li>\n<li><code>--name</code>, <code>-n</code> - The name of the application to generate</li>\n<li><code>--path</code>, <code>-p</code> - The path for the generated application</li>\n<li><code>--starter</code>, <code>-s</code> - Overrides the default Starter App template directory</li>\n<li><code>--theme-name</code>, <code>-t</code> - The name of the defualt Theme</li>\n<li><code>--view-name</code>, <code>-v</code> - The name of the defalut View</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha generate app [options] name \\\n                              path\n</code></pre>\n\n<h2 id='command_reference-section-41'>sencha generate controller</h2>\n\n<p>This command generates a new Controller and adds it to the current application.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--name</code>, <code>-n</code> - The name of the Controller to generate</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha generate controller [options] name\n</code></pre>\n\n<h2 id='command_reference-section-42'>sencha generate form</h2>\n\n<p>This command generates a new form and adds it to the current application.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--fields</code>, <code>-f</code> - Comma separated list of \"name:type\" field pairs</li>\n<li><code>--name</code>, <code>-n</code> - The name of the Form to generate</li>\n<li><code>--xtype</code>, <code>-x</code> - The xtype for the form. Defaults to the lowercase form of the name.</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha generate form [options] name \\\n                               fields \\\n                               [xtype]\n</code></pre>\n\n<h2 id='command_reference-section-43'>sencha generate model</h2>\n\n<p>This command generates a new Model class and adds it to the current application.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--base</code>, <code>-b</code> - The base class of the Model (default: <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>)</li>\n<li><code>--fields</code>, <code>-f</code> - Comma separated list of \"name:type\" field pairs</li>\n<li><code>--name</code>, <code>-n</code> - The name of the Model</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha generate model [options] name \\\n                                fields\n</code></pre>\n\n<h2 id='command_reference-section-44'>sencha generate package</h2>\n\n<p>This command generates a new Sencha Cmd Package. A package is in many ways like\nan application in that it contains any of the following pieces:</p>\n\n<ul>\n<li>JavaScript source</li>\n<li>SASS styles</li>\n<li>Arbitrary resources</li>\n</ul>\n\n\n<p>All of these are integrated by a build process using <code>sencha package build</code>.</p>\n\n<p>For example:</p>\n\n<pre><code>sencha generate package foo\n</code></pre>\n\n<p>To use this package in applications (or other packages), you just add the name\nof the package to the requires array in the <code>\"app.json\"</code> or <code>\"package.json\"</code>\nfile:</p>\n\n<pre><code>requires: [\n    'foo'\n]\n</code></pre>\n\n<p>All packages reside in the <code>\"./packages\"</code> folder of the workspace (which is\noften the same folder as your application).</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--name</code>, <code>-n</code> - The name of the package to generate</li>\n<li><code>--type</code>, <code>-t</code> - The type of the package to generate (i.e., \"code\" or \"theme\")</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha generate package [options] name\n</code></pre>\n\n<h2 id='command_reference-section-45'>sencha generate profile</h2>\n\n<p>This command generates a new Profile and adds it to the current application.</p>\n\n<p>NOTE: Sencha Touch only.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--name</code>, <code>-n</code> - The name of the Profile to generate</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha generate profile [options] name\n</code></pre>\n\n<h2 id='command_reference-section-46'>sencha generate theme</h2>\n\n<p>This command generates a new Theme. For Ext JS 4.1, themes are \"owned\" by an\napplication. In Ext JS 4.2 and beyond, themes are Packages.</p>\n\n<p>In Ext JS 4.2, theme packages can extend other themes. By default, generated\nthemes extend \"ext-theme-classic\". This can be overridden using <code>--extend</code>.</p>\n\n<p>To generate a stand-alone Theme in Ext JS 4.2, follow these steps. Generate a\nworkspace (with <code>\"ext\"</code> folder) using Ext JS 4.2 SDK unzipped on your system:</p>\n\n<pre><code>sencha -sdk /path/to/ext-4.2.0 generate workspace MyWorkspace\ncd MyWorkspace\n</code></pre>\n\n<p>From inside the workspace, use the <code>\"ext\"</code> folder to generate the theme package:</p>\n\n<pre><code>sencha -sdk ext generate theme --extend ext-theme-neptune MyTheme\n</code></pre>\n\n<p>The above could equivalently have used the SDK used to create the workspace.</p>\n\n<p>The <code>-sdk</code> switch is used here to indicate the framework on which the theme is\nbased. This is not needed if the command is run from an Ext JS application\nfolder.</p>\n\n<p><code>NOTE:</code> Does not apply to Sencha Touch.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--extend</code>, <code>-e</code> - The name of the theme package to extend (Ext JS 4.2+ only)</li>\n<li><code>--name</code>, <code>-n</code> - The name of the Theme to generate</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha generate theme [options] name\n</code></pre>\n\n<h2 id='command_reference-section-47'>sencha generate view</h2>\n\n<p>This command generates a new View class and adds it to the current application.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--name</code>, <code>-n</code> - The name of the View to generate</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha generate view [options] name\n</code></pre>\n\n<h2 id='command_reference-section-48'>sencha generate workspace</h2>\n\n<p>This command generates a workspace for managing shared code across pages or\napplications.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--path</code>, <code>-p</code> - Sets the target path for the workspace</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha generate workspace [options] [path]\n</code></pre>\n\n<h2 id='command_reference-section-49'>sencha help</h2>\n\n<p>This command displays help for other commands.</p>\n\n<h3>Example</h3>\n\n<pre><code>sencha help generate app\n</code></pre>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha help command...\n</code></pre>\n\n<h3>Where:</h3>\n\n<ul>\n<li><code>command</code> - The command path for which to display help (e.g., \"generate app\"</li>\n</ul>\n\n\n<h2 id='command_reference-section-50'>sencha io</h2>\n\n<p>These commands give you access to all the tools required to create, manage and\ndeploy web applications in the io cloud.</p>\n\n<p>Sencha.io provides a backend-as-a-service that enables developers with a set of\nAPIs that helps them build and run their applications. It allows you to build\nweb applications using services provided through our client side SDKs, Ext JS\nand Sencha Touch.</p>\n\n<h3>Commands</h3>\n\n<ul>\n<li><code>app-set-group</code> - Sets the Auth Group associated with an app</li>\n<li><code>create-app</code> - Creates an app in Sencha.io</li>\n<li><code>create-auth-group</code> - Creates an auth group in Sencha.io</li>\n<li><code>create-version</code> - Creates a version of an app in Sencha.io</li>\n<li><code>deploy</code> - Deploys a version of an application in Sencha.io</li>\n<li><code>list-apps</code> - Lists all the apps for this developer in Sencha.io</li>\n<li><code>list-groups</code> - Lists Authentication Groups for a Developer</li>\n<li><code>list-versions</code> - Lists all the versions for this app in Sencha.io</li>\n<li><code>set-group-auth</code> - Sets an Auth Group's authentication mechanism</li>\n<li><code>undeploy</code> - Undeploys a version of an application in Sencha.io</li>\n</ul>\n\n\n<h2 id='command_reference-section-51'>sencha io app-set-group</h2>\n\n<p>Sets the Authentication Group which is associated with this Application.</p>\n\n<p>Only one Authentication Group may be associated with an Application at any\ngiven time.</p>\n\n<p>An Authentication Group controls which methods of authentication are available\nto users of your application. Users are members of Authentication Groups.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--group-name</code>, <code>-g</code> - The Auth Group name to associate with this app</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha io app-set-group [options] [appName] \\\n                                  [groupName] \\\n                                  [username] \\\n                                  [password] \\\n                                  [teamName]\n</code></pre>\n\n<h2 id='command_reference-section-52'>sencha io create-app</h2>\n\n<p>Creates a new Application in the context of Sencha Io.</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha io create-app [appName] \\\n                     [username] \\\n                     [password] \\\n                     [teamName]\n</code></pre>\n\n<h2 id='command_reference-section-53'>sencha io create-auth-group</h2>\n\n<p>Creates a new Authentication Group in Sencha Io.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--group-name</code>, <code>-g</code> - The Auth Group name to associate with this app</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha io create-auth-group [options] [groupName] \\\n                                      [username] \\\n                                      [password] \\\n                                      [teamName]\n</code></pre>\n\n<h2 id='command_reference-section-54'>sencha io create-version</h2>\n\n<p>Creates a new Version of an Application. In doing so, an application <code>\".zip\"</code>\nfile is uploaded to the cloud, along with a version tag and a description of\nthis version.  Once a version has been successfully created, it must be\ndeployed before it can be accessed.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--package-path</code>, <code>-pac</code> - The path to the package to be used in a new application version.</li>\n<li><code>--version-description</code>, <code>-version-d</code> - The version description for this new application version</li>\n<li><code>--version-tag</code>, <code>-version-t</code> - The version tag for this new application version</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha io create-version [options] [appName] \\\n                                   [packagePath] \\\n                                   [versionTag] \\\n                                   [versionDescription] \\\n                                   [username] \\\n                                   [password] \\\n                                   [teamName]\n</code></pre>\n\n<h2 id='command_reference-section-55'>sencha io deploy</h2>\n\n<p>Deploys a previously created version of an Application to the cloud.</p>\n\n<p>Once an application has been deployed, it becomes publicly accessible.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--version-tag</code>, <code>-v</code> - The version tag to deploy</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha io deploy [options] [appName] \\\n                           [versionTag] \\\n                           [username] \\\n                           [password] \\\n                           [teamName]\n</code></pre>\n\n<h2 id='command_reference-section-56'>sencha io list-apps</h2>\n\n<p>Lists all Applications which are accessible by this Developer (in the current\nTeam context).</p>\n\n<p>If the Developer is a member of multiple teams, they may have to switch Team\ncontexts to see all of the Applications to which they have access.</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha io list-apps [username] \\\n                    [password] \\\n                    [teamName]\n</code></pre>\n\n<h2 id='command_reference-section-57'>sencha io list-groups</h2>\n\n<p>Lists all the Authentication Groups to which this Developer currently has\naccess.</p>\n\n<p>If the Developer is a member of multiple teams, they may have to switch Team\ncontexts to see all of the Authentication Groups to which they have access.</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha io list-groups [username] \\\n                      [password]\n</code></pre>\n\n<h2 id='command_reference-section-58'>sencha io list-versions</h2>\n\n<p>Lists all of the currently uploaded versions of an Application in the system.</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha io list-versions [appName] \\\n                        [username] \\\n                        [password] \\\n                        [teamName]\n</code></pre>\n\n<h2 id='command_reference-section-59'>sencha io set-group-auth</h2>\n\n<p>Sets an Authentication Group's configuration.</p>\n\n<p>Acceptible values for Authentication Method are senchaio (which is on by\ndefault), facebook, and twitter.</p>\n\n<p>In order to use facebook or twitter, Application Keys and Secrets must be\nobtained for you application with those services, and then be provided as the\nfinal parameters of this command.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--auth-method</code>, <code>-au</code> - The auth method to set</li>\n<li><code>--enabled</code>, <code>-e</code> - true/false, to enable the auth method</li>\n<li><code>--group-name</code>, <code>-g</code> - The group name to set</li>\n<li><code>--key</code>, <code>-k</code> - key for twitter/facebook auth</li>\n<li><code>--secret</code>, <code>-s</code> - secret for twitter/facebook auth</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha io set-group-auth [options] [groupName] \\\n                                   [authMethod] \\\n                                   [enabled] \\\n                                   [username] \\\n                                   [password] \\\n                                   [key] \\\n                                   [secret]\n</code></pre>\n\n<h2 id='command_reference-section-60'>sencha io undeploy</h2>\n\n<p>Undeploys a previously deployed version of an Application.</p>\n\n<p>Once this has been done, the application will no longer be publicly available.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--version-tag</code>, <code>-v</code> - The version tag to undeploy</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha io undeploy [options] [appName] \\\n                             [versionTag] \\\n                             [username] \\\n                             [password] \\\n                             [teamName]\n</code></pre>\n\n<h2 id='command_reference-section-61'>sencha iofs</h2>\n\n<p>These commands gives you low level access to an io application's cloud hosting\nthrough an interface which ressembles a file system shell.</p>\n\n<h3>Commands</h3>\n\n<ul>\n<li><code>get</code> - Retrieve a remote file from the file system</li>\n<li><code>ls</code> - List all files in the file system for the supplied path</li>\n<li><code>put</code> - Upload a file to the remote filesystem</li>\n<li><code>rm</code> - Remove a file or directory from the file system</li>\n</ul>\n\n\n<h2 id='command_reference-section-62'>sencha iofs get</h2>\n\n<p>Retrieves a remote file from an Application's filesystem in Sencha io.</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha iofs get [appName] \\\n                [path] \\\n                [username] \\\n                [password] \\\n                [teamName]\n</code></pre>\n\n<h2 id='command_reference-section-63'>sencha iofs ls</h2>\n\n<p>Lists the contents of a remote path in an Application's Sencha Io hosting.</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha iofs ls [appName] \\\n               [path] \\\n               [username] \\\n               [password] \\\n               [teamName]\n</code></pre>\n\n<h2 id='command_reference-section-64'>sencha iofs put</h2>\n\n<p>Uploads a local file to a remote path in an Application's Sencha Io hosting.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--content-type</code>, <code>-c</code> - The MIME content type of the file to be uploaded</li>\n<li><code>--local-path</code>, <code>-l</code> - The local path of the file to be uploaded</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha iofs put [options] [appName] \\\n                          [path] \\\n                          [localPath] \\\n                          [contentType] \\\n                          [username] \\\n                          [password] \\\n                          [teamName]\n</code></pre>\n\n<h2 id='command_reference-section-65'>sencha iofs rm</h2>\n\n<p>Removes a remote directory or file from an Application's remote Sencha Io\nhosting.</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha iofs rm [appName] \\\n               [path] \\\n               [username] \\\n               [password] \\\n               [teamName]\n</code></pre>\n\n<h2 id='command_reference-section-66'>sencha js</h2>\n\n<p>This command loads and executes the specified JavaScript source file or files.</p>\n\n<pre><code>sencha js file.js[,file2.js,...] [arg1 [arg2 ...] ]\n</code></pre>\n\n<h4>Files</h4>\n\n<p>The first argument to this command is the file or files to execute. If there\nare multiple files, separate them with commas. In addition to the command line\ntechnique of specifying files, this command also recognizes the following\ndirective:</p>\n\n<pre><code>//@require ../path/to/source.js\n</code></pre>\n\n<p>This form of <code>require</code> directive uses a relative path based on the file that\ncontains the directive. Any given file will only be executed once, in much the\nsame manner as the compiler.</p>\n\n<h4>Context</h4>\n\n<p>A primitive <code>console</code> object with the following methods is provided to the\nJavaScript execution context:</p>\n\n<ul>\n<li><code>log</code></li>\n<li><code>debug</code></li>\n<li><code>info</code></li>\n<li><code>warn</code></li>\n<li><code>error</code></li>\n<li><code>dir</code></li>\n<li><code>trace</code></li>\n<li><code>time</code> / <code>timeEnd</code></li>\n</ul>\n\n\n<p>Arguments beyond the first can be accessed in JavaScript with the global <code>$args</code>\narray. The current directory can be accessed with <code>$curdir</code>.</p>\n\n<p>The Sencha Cmd object can be accessed with <code>sencha</code>. This object has a <code>version</code>\nproperty and a <code>dispatch</code> method.</p>\n\n<pre><code>if (sencha.version.compareTo('*********') &lt; 0) {\n    console.warn('Some message');\n} else {\n    // dispatch any command provided by Cmd:\n    sencha.dispatch([ 'app', 'build', $args[1] ]);\n}\n</code></pre>\n\n<p>Beyond the above, the executing JavaScript has full access to the JRE using\nthe <code>importPackage</code> and <code>importClass</code> methods.</p>\n\n<p>For example:</p>\n\n<pre><code>importPackage(java.io);\n\nvar f = new File('somefile.txt');  // create a java.io.File object\n</code></pre>\n\n<p>For further details on the JavaScript engine provided by Java, consult the\nJava Scripting guide:</p>\n\n<p>http://docs.oracle.com/javase/6/docs/technotes/guides/scripting/programmer_guide/index.html</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha js String \\\n          String[]...\n</code></pre>\n\n<h2 id='command_reference-section-67'>sencha manifest</h2>\n\n<p>This category provides commands to manage application manifests.</p>\n\n<h3>Commands</h3>\n\n<ul>\n<li><code>create</code> - Generate a list of metadata for all classes found in the given directories</li>\n</ul>\n\n\n<h2 id='command_reference-section-68'>sencha manifest create</h2>\n\n<p>This command generates a list of metadata for all classes.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--output-path</code>, <code>-o</code> - The file path to write the results to in JSON format.</li>\n<li><code>--path</code>, <code>-p</code> - The directory path(s) that contains all classes</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha manifest create [options] output-path\n</code></pre>\n\n<h2 id='command_reference-section-69'>sencha package</h2>\n\n<p>These commands manage packages in the local repository.</p>\n\n<p>These commands are not typically used directly because application requirements\nare automatically used by <code>sencha app build</code> and <code>sencha app refresh --packages</code>\nto handle these details.</p>\n\n<h4>Using Packages</h4>\n\n<p>The most common commands needed to use packages are those that connect your\nlocal package repository to remote repositories. By default, the local repo has\none remote repository defined that points at Sencha's package repository.</p>\n\n<p>To add, remove or display these connections see:</p>\n\n<pre><code>sencha help package repo\n</code></pre>\n\n<h4>Authoring Packages</h4>\n\n<p>When authoring packages for others to use in their applications, however, these\ncommands are involved. In particular, you must first initialize your local\npackage repository. This is because the local repository is automatically\ninitialized \"anonymously\". In this state, the local repository can only be used\nto retrieve and cache other packages. To create and publish packages, the local\nrepository must be initialized with a name and an optional email address.</p>\n\n<p>This name is not required to be globally unique, but it is a good idea to use a\nname that is unique and meaningful as a package author.</p>\n\n<pre><code>sencha repository init -name \"My Company, Inc.\"\n\nsencha repository init -name mySenchaForumId\n</code></pre>\n\n<p>For details see:</p>\n\n<pre><code>sencha help repository init\n</code></pre>\n\n<h3>Categories</h3>\n\n<ul>\n<li><code>repository</code> - Manage local repository and remote repository connections</li>\n</ul>\n\n\n<h3>Commands</h3>\n\n<ul>\n<li><code>add</code> - Adds a package file (.pkg) to the local repository</li>\n<li><code>build</code> - Builds the current package</li>\n<li><code>extract</code> - Extracts the contents of a package to an output folder</li>\n<li><code>get</code> - Get a package from a remote repository</li>\n<li><code>list</code> - Lists packages in the repository</li>\n<li><code>remove</code> - Removes a package from the local repository</li>\n<li><code>upgrade</code> - Upgrades the current pacakge</li>\n</ul>\n\n\n<h2 id='command_reference-section-70'>sencha package add</h2>\n\n<p>Adds a new package file (<code>\"*.pkg\"</code>) to the local repository. These packages will\nbe signed automatically if their <code>creator</code> property matches the <code>name</code> associated\nwith the local repository.</p>\n\n<p>Once a package is added to the local repository, any repository that points to\nthis repository as a remote repository will be able to download the package.</p>\n\n<p>The <code>sencha package build</code> process generates an appropriate <code>\".pkg\"</code> file in the\n<code>workspace.build.dir</code>.</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha package add pkgFile\n</code></pre>\n\n<h3>Where:</h3>\n\n<ul>\n<li><code>pkgFile</code> - The path to the package file (e.g., path/file.pkg)</li>\n</ul>\n\n\n<h2 id='command_reference-section-71'>sencha package build</h2>\n\n<p>This command invokes the build process for the current package. Similar to an\napplication and <code>sencha app build</code>, this command builds the current package (as\ndefined by the current folder).</p>\n\n<pre><code>sencha package build\n</code></pre>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--clean</code>, <code>-c</code> - Remove previous build output prior to executing build</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha package build [options] \n</code></pre>\n\n<h2 id='command_reference-section-72'>sencha package extract</h2>\n\n<p>This command extracts a package or packages from the repository. If necessary\nthe packages will be downloaded from remote repositories and cached locally for\nfuture use.</p>\n\n<p><code>NOTE:</code> This is <code>not</code> typically executed manually but is handle automatically\nas part of the build process based on the \"requires\" found in <code>\"app.json\"</code> and/or\n<code>\"package.json\"</code>.</p>\n\n<p>To extract a package named \"foo\" at version \"1.2\" to a specified location:</p>\n\n<pre><code>sencha package extract -todir=/some/path foo@1.2\n</code></pre>\n\n<p>This will create <code>\"/some/path/foo\"</code>. To recursively extract packages required\nby \"foo\", you would do this:</p>\n\n<pre><code>sencha package extract -recurse -todir=/some/path foo@1.2\n</code></pre>\n\n<p>When complete, \"foo\" and all of its required packages will reside in the folder\nspecified.</p>\n\n<p><code>NOTE:</code> It is recommended to use <code>-todir</code> and allow the package name to be used\nas the immediate subdirectory of that folder. The <code>-outdir</code> option allows you to\nstrip off this directory but prevents recursive extraction as a result.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--clean</code>, <code>-c</code> - Delete any files in the output folder before extracting</li>\n<li><code>--force</code>, <code>-f</code> - Ignore local copy and fetch from remote repository</li>\n<li><code>--outdir</code>, <code>-o</code> - The output folder for the extracted package contents</li>\n<li><code>--recurse</code>, <code>-r</code> - Also get all required packages recursively</li>\n<li><code>--todir</code>, <code>-t</code> - The output folder for the extracted package folder</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha package extract [options] packages...\n</code></pre>\n\n<h3>Where:</h3>\n\n<ul>\n<li><code>packages</code> - The names/versions of the packages to extract</li>\n</ul>\n\n\n<h2 id='command_reference-section-73'>sencha package get</h2>\n\n<p>This command ensures that a specified package is locally available. This does\n<code>not</code> extract the package to a particular location, but rather, enables apps or\nother packages to get the package from the local repository (that is, without\ndownloading it).</p>\n\n<p>For example, to ensure that <code>\"foo\"</code> and <code>\"bar\"</code> are available locally:</p>\n\n<pre><code>sencha package get foo bar\n</code></pre>\n\n<p>To get all packages required by those specified packages:</p>\n\n<pre><code>sencha package get -recursive foo bar\n</code></pre>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--force</code>, <code>-f</code> - Ignore local copy and (re)fetch from remote repository</li>\n<li><code>--recurse</code>, <code>-r</code> - Also get all required packages recursively</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha package get [options] packages...\n</code></pre>\n\n<h3>Where:</h3>\n\n<ul>\n<li><code>packages</code> - One or more packages/versions to fetch locally</li>\n</ul>\n\n\n<h2 id='command_reference-section-74'>sencha package list</h2>\n\n<p>This command lists packages in the repository. To list available packages\nsimply execute:</p>\n\n<pre><code>sencha package list\n</code></pre>\n\n<p>To list locally available packages (no download required), do this:</p>\n\n<pre><code>sencha package list .\n</code></pre>\n\n<p>Otherwise, you can specify the names of package repositories to list:</p>\n\n<pre><code>sencha package list sencha\n</code></pre>\n\n<p>The above will list the contents of the Sencha Cmd Package Repository.</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha package list names...\n</code></pre>\n\n<h3>Where:</h3>\n\n<ul>\n<li><code>names</code> - The repos to list (blank for all, or remote names or \".\" for local)</li>\n</ul>\n\n\n<h2 id='command_reference-section-75'>sencha package remove</h2>\n\n<p>Removes one or more packages from the local repository.</p>\n\n<p>Removes version 1.2 of the package \"foo\":</p>\n\n<pre><code>sencha package remove foo@1.2\n</code></pre>\n\n<p>Remove all versions of \"foo\"</p>\n\n<pre><code>sencha package remove foo@...\n</code></pre>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha package remove packageNames...\n</code></pre>\n\n<h3>Where:</h3>\n\n<ul>\n<li><code>packageNames</code> - One or more packages/versions to remove</li>\n</ul>\n\n\n<h2 id='command_reference-section-76'>sencha package repository</h2>\n\n<p>These commands manage the local repository and its connections to remote\nrepositories.</p>\n\n<h4>Remote Repositories</h4>\n\n<p>The primary role of the local repository is as a cache of packages that it\ndownloads from one or more specified remote repositories. By default, Sencha\nCmd adds the Sencha package repository as a remote repository. Using these\ncommands you can manage these connections.</p>\n\n<p>This command adds a remote repository connection named <code>\"foo\"</code>:</p>\n\n<pre><code>sencha repo add foo http://coolstuff.foo/packages\n</code></pre>\n\n<p>Following this command, any packages contained in this repository will be\navailable. Typically these packages are used by adding their name (and possibly\nversion) to your application's <code>\"app.json\"</code> in its <code>requires</code> array.</p>\n\n<pre><code>{\n    requires: [\n        'cool-package@2.1'\n    ]\n}\n</code></pre>\n\n<p>Then:</p>\n\n<pre><code>sencha app build\n</code></pre>\n\n<p>The above addition will require version 2.1 of <code>\"cool-package\"</code>. The remote\nrepository added above will be checked for this package, and if found, it is\ndownloaded to the local repository and cached there as well as extracted to\nyour app's<code>\"packages/cool-package\"</code> folder and automatically integrated in to\nyour build.</p>\n\n<h4>Authoring Packages</h4>\n\n<p>To author packages for others to use in their applications, you will need to\ninitialize your local repository with your name:</p>\n\n<pre><code>sencha repo init -name \"My Company, Inc.\"\n</code></pre>\n\n<p>See these for more details:</p>\n\n<pre><code>sencha help package\nsencha help repo init\n</code></pre>\n\n<h3>Commands</h3>\n\n<ul>\n<li><code>add</code> - Add a remote repository connection</li>\n<li><code>init</code> - Initializes the local package repository</li>\n<li><code>list</code> - List remote repository connections</li>\n<li><code>remove</code> - Remove a remote repository connection</li>\n<li><code>show</code> - Show details for a repository</li>\n<li><code>sync</code> - Clears caches to force refetching for a remote repository</li>\n</ul>\n\n\n<h2 id='command_reference-section-77'>sencha package repository add</h2>\n\n<p>Adds a remote repository connection. Once added, packages from that repository\nwill be available to applications for use.</p>\n\n<pre><code>sencha repo add foo http://foo.bar/pkgs\n</code></pre>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--address</code>, <code>-a</code> - The address (or URL) for the remote repository</li>\n<li><code>--name</code>, <code>-n</code> - The name for the remote connection</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha package repository add [options] name \\\n                                        address\n</code></pre>\n\n<h2 id='command_reference-section-78'>sencha package repository init</h2>\n\n<p>Initializes the local repository. The local repository is used to cache local\ncopies of packages (potentially for multiple versions).</p>\n\n<p><code>NOTE:</code> This step is not typically necessary because the local repository is\nautomatically initialized in \"anonymous\" mode. This command is needed only if\nyou want to publish packages for others to use in their application. To publish\npackages you must initial the local repository and provide a name:</p>\n\n<pre><code>sencha repository init -name \"My Company, Inc.\" -email <EMAIL>\n</code></pre>\n\n<p>Beyond initializing the repository file structures, this command also generates\na public/private key pair and stores these in the local repository. The private\nkey is used to sign packages added to this local repository.</p>\n\n<p>For details on adding packages:</p>\n\n<pre><code>sencha help package add\n</code></pre>\n\n<h4>Private Key</h4>\n\n<p>Packages added to the local repository with a <code>creator</code> property equal to the\nname given to <code>sencha repository init</code> will be signed using the private key\nstored in the local repository.</p>\n\n<p>In this release of Sencha Cmd, these signatures are only used to test package\nintegrity. You can backup this key if desired, but a new key can be regenerated\nby running <code>sencha repo init</code> at any time. In future versions it may be more\nimportant to backup your private key.</p>\n\n<h4>Remote Access</h4>\n\n<p>Making the local package repository available as a remote repository for others\nto access requires some knowledge of the disk structure of the repository. By\ndefault, Sencha Cmd creates the local repository adjacent to its installation\nfolder. For example, given the following location of Sencha Cmd:</p>\n\n<pre><code>/Users/<USER>/bin/Sencha/Cmd/3.1.0.200/\n</code></pre>\n\n<p>The local respository is located at:</p>\n\n<pre><code>/Users/<USER>/bin/Sencha/Cmd/repo\n</code></pre>\n\n<p>This is to allow your local repository to be used by newer versions of Sencha\nCmd. The folder to publish to others as an HTTP URL is:</p>\n\n<pre><code>/Users/<USER>/bin/Sencha/Cmd/repo/pkgs\n</code></pre>\n\n<p><code>IMPORTANT:</code> Do <code>NOT</code> expose the parent folder of <code>\"pkgs\"</code> - that folder holds\nprivate information (such as your private key). Further, Sencha Cmd will not\nrecognize the structure as a valid remote repository.</p>\n\n<p>If you want to host the repository on a public server, simply copy the <code>\"pkgs\"</code>\nfolder to a web server and share the HTTP address.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--email</code>, <code>-em</code> - The email address for the owner of the local repository</li>\n<li><code>--expiration</code>, <code>-ex</code> - The number of years before the key pair becomes invalid</li>\n<li><code>--keybits</code>, <code>-k</code> - The number of bits for the public/private key pair</li>\n<li><code>--name</code>, <code>-n</code> - The name for the owner of the local repository</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha package repository init [options] \n</code></pre>\n\n<h2 id='command_reference-section-79'>sencha package repository list</h2>\n\n<p>Lists all remote repository connections.</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha package repository list \n</code></pre>\n\n<h2 id='command_reference-section-80'>sencha package repository remove</h2>\n\n<p>Remove a remote repository from the local repository's list of remote\nrepositories. For example, if a remote was previously added:</p>\n\n<pre><code>sencha repo add foo http://foo.bar/pkgs\n</code></pre>\n\n<p>This command will remove it:</p>\n\n<pre><code>sencha repo remove foo\n</code></pre>\n\n<p><code>NOTE:</code> This command does not remove packages that you may have downloaded from\nthis remote as they are now cached in the local repository.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--name</code>, <code>-n</code> - The name for the remote connection</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha package repository remove [options] name\n</code></pre>\n\n<h2 id='command_reference-section-81'>sencha package repository show</h2>\n\n<p>Shows information about a remote repository.</p>\n\n<p>To show information about the local repository:</p>\n\n<pre><code>sencha repo show .\n</code></pre>\n\n<p>To show information about a specific remote repository:</p>\n\n<pre><code>sencha repo show some-remote\n</code></pre>\n\n<p>The name given should match the name previously given to:</p>\n\n<pre><code>sencha repo add some-remote ...\n</code></pre>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--all</code>, <code>-a</code> - Include all details about the repository</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha package repository show [options] names...\n</code></pre>\n\n<h3>Where:</h3>\n\n<ul>\n<li><code>names</code> - The name(s) of remote repositories (or \".\" for local)</li>\n</ul>\n\n\n<h2 id='command_reference-section-82'>sencha package repository sync</h2>\n\n<p>Forces (re)synchronization with a remote repository catalog. Normally this is\ndone periodically and does not need to be manually synchronized. This command\nmay be needed if there something known to have been added to a remote repo but\nhas not yet shown up in the catalog on this machine.</p>\n\n<pre><code>sencha repo sync someremote\n</code></pre>\n\n<p>To resynchronize with all remote repositories:</p>\n\n<pre><code>sencha repo sync\n</code></pre>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--name</code>, <code>-n</code> - The name for the remote connection (blank for all)</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha package repository sync [options] [name]\n</code></pre>\n\n<h2 id='command_reference-section-83'>sencha package upgrade</h2>\n\n<p>Upgrades the current package to a newer SDK or Sencha Cmd version.</p>\n\n<p>This command must be run from the desired package's folder.</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha package upgrade \n</code></pre>\n\n<h2 id='command_reference-section-84'>sencha repository</h2>\n\n<p>These commands manage the local repository and its connections to remote\nrepositories.</p>\n\n<h4>Remote Repositories</h4>\n\n<p>The primary role of the local repository is as a cache of packages that it\ndownloads from one or more specified remote repositories. By default, Sencha\nCmd adds the Sencha package repository as a remote repository. Using these\ncommands you can manage these connections.</p>\n\n<p>This command adds a remote repository connection named <code>\"foo\"</code>:</p>\n\n<pre><code>sencha repo add foo http://coolstuff.foo/packages\n</code></pre>\n\n<p>Following this command, any packages contained in this repository will be\navailable. Typically these packages are used by adding their name (and possibly\nversion) to your application's <code>\"app.json\"</code> in its <code>requires</code> array.</p>\n\n<pre><code>{\n    requires: [\n        'cool-package@2.1'\n    ]\n}\n</code></pre>\n\n<p>Then:</p>\n\n<pre><code>sencha app build\n</code></pre>\n\n<p>The above addition will require version 2.1 of <code>\"cool-package\"</code>. The remote\nrepository added above will be checked for this package, and if found, it is\ndownloaded to the local repository and cached there as well as extracted to\nyour app's<code>\"packages/cool-package\"</code> folder and automatically integrated in to\nyour build.</p>\n\n<h4>Authoring Packages</h4>\n\n<p>To author packages for others to use in their applications, you will need to\ninitialize your local repository with your name:</p>\n\n<pre><code>sencha repo init -name \"My Company, Inc.\"\n</code></pre>\n\n<p>See these for more details:</p>\n\n<pre><code>sencha help package\nsencha help repo init\n</code></pre>\n\n<h3>Commands</h3>\n\n<ul>\n<li><code>add</code> - Add a remote repository connection</li>\n<li><code>init</code> - Initializes the local package repository</li>\n<li><code>list</code> - List remote repository connections</li>\n<li><code>remove</code> - Remove a remote repository connection</li>\n<li><code>show</code> - Show details for a repository</li>\n<li><code>sync</code> - Clears caches to force refetching for a remote repository</li>\n</ul>\n\n\n<h2 id='command_reference-section-85'>sencha repository add</h2>\n\n<p>Adds a remote repository connection. Once added, packages from that repository\nwill be available to applications for use.</p>\n\n<pre><code>sencha repo add foo http://foo.bar/pkgs\n</code></pre>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--address</code>, <code>-a</code> - The address (or URL) for the remote repository</li>\n<li><code>--name</code>, <code>-n</code> - The name for the remote connection</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha repository add [options] name \\\n                                address\n</code></pre>\n\n<h2 id='command_reference-section-86'>sencha repository init</h2>\n\n<p>Initializes the local repository. The local repository is used to cache local\ncopies of packages (potentially for multiple versions).</p>\n\n<p><code>NOTE:</code> This step is not typically necessary because the local repository is\nautomatically initialized in \"anonymous\" mode. This command is needed only if\nyou want to publish packages for others to use in their application. To publish\npackages you must initial the local repository and provide a name:</p>\n\n<pre><code>sencha repository init -name \"My Company, Inc.\" -email <EMAIL>\n</code></pre>\n\n<p>Beyond initializing the repository file structures, this command also generates\na public/private key pair and stores these in the local repository. The private\nkey is used to sign packages added to this local repository.</p>\n\n<p>For details on adding packages:</p>\n\n<pre><code>sencha help package add\n</code></pre>\n\n<h4>Private Key</h4>\n\n<p>Packages added to the local repository with a <code>creator</code> property equal to the\nname given to <code>sencha repository init</code> will be signed using the private key\nstored in the local repository.</p>\n\n<p>In this release of Sencha Cmd, these signatures are only used to test package\nintegrity. You can backup this key if desired, but a new key can be regenerated\nby running <code>sencha repo init</code> at any time. In future versions it may be more\nimportant to backup your private key.</p>\n\n<h4>Remote Access</h4>\n\n<p>Making the local package repository available as a remote repository for others\nto access requires some knowledge of the disk structure of the repository. By\ndefault, Sencha Cmd creates the local repository adjacent to its installation\nfolder. For example, given the following location of Sencha Cmd:</p>\n\n<pre><code>/Users/<USER>/bin/Sencha/Cmd/3.1.0.200/\n</code></pre>\n\n<p>The local respository is located at:</p>\n\n<pre><code>/Users/<USER>/bin/Sencha/Cmd/repo\n</code></pre>\n\n<p>This is to allow your local repository to be used by newer versions of Sencha\nCmd. The folder to publish to others as an HTTP URL is:</p>\n\n<pre><code>/Users/<USER>/bin/Sencha/Cmd/repo/pkgs\n</code></pre>\n\n<p><code>IMPORTANT:</code> Do <code>NOT</code> expose the parent folder of <code>\"pkgs\"</code> - that folder holds\nprivate information (such as your private key). Further, Sencha Cmd will not\nrecognize the structure as a valid remote repository.</p>\n\n<p>If you want to host the repository on a public server, simply copy the <code>\"pkgs\"</code>\nfolder to a web server and share the HTTP address.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--email</code>, <code>-em</code> - The email address for the owner of the local repository</li>\n<li><code>--expiration</code>, <code>-ex</code> - The number of years before the key pair becomes invalid</li>\n<li><code>--keybits</code>, <code>-k</code> - The number of bits for the public/private key pair</li>\n<li><code>--name</code>, <code>-n</code> - The name for the owner of the local repository</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha repository init [options] \n</code></pre>\n\n<h2 id='command_reference-section-87'>sencha repository list</h2>\n\n<p>Lists all remote repository connections.</p>\n\n<h3>Syntax</h3>\n\n<pre><code>sencha repository list \n</code></pre>\n\n<h2 id='command_reference-section-88'>sencha repository remove</h2>\n\n<p>Remove a remote repository from the local repository's list of remote\nrepositories. For example, if a remote was previously added:</p>\n\n<pre><code>sencha repo add foo http://foo.bar/pkgs\n</code></pre>\n\n<p>This command will remove it:</p>\n\n<pre><code>sencha repo remove foo\n</code></pre>\n\n<p><code>NOTE:</code> This command does not remove packages that you may have downloaded from\nthis remote as they are now cached in the local repository.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--name</code>, <code>-n</code> - The name for the remote connection</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha repository remove [options] name\n</code></pre>\n\n<h2 id='command_reference-section-89'>sencha repository show</h2>\n\n<p>Shows information about a remote repository.</p>\n\n<p>To show information about the local repository:</p>\n\n<pre><code>sencha repo show .\n</code></pre>\n\n<p>To show information about a specific remote repository:</p>\n\n<pre><code>sencha repo show some-remote\n</code></pre>\n\n<p>The name given should match the name previously given to:</p>\n\n<pre><code>sencha repo add some-remote ...\n</code></pre>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--all</code>, <code>-a</code> - Include all details about the repository</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha repository show [options] names...\n</code></pre>\n\n<h3>Where:</h3>\n\n<ul>\n<li><code>names</code> - The name(s) of remote repositories (or \".\" for local)</li>\n</ul>\n\n\n<h2 id='command_reference-section-90'>sencha repository sync</h2>\n\n<p>Forces (re)synchronization with a remote repository catalog. Normally this is\ndone periodically and does not need to be manually synchronized. This command\nmay be needed if there something known to have been added to a remote repo but\nhas not yet shown up in the catalog on this machine.</p>\n\n<pre><code>sencha repo sync someremote\n</code></pre>\n\n<p>To resynchronize with all remote repositories:</p>\n\n<pre><code>sencha repo sync\n</code></pre>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--name</code>, <code>-n</code> - The name for the remote connection (blank for all)</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha repository sync [options] [name]\n</code></pre>\n\n<h2 id='command_reference-section-91'>sencha theme</h2>\n\n<p>This category contains low-level commands for managing themes. Typically these\noperations are handled by <code>sencha app build</code> and/or <code>sencha package build</code>.</p>\n\n<h3>Commands</h3>\n\n<ul>\n<li><code>build</code> - Builds a custom theme from a given page</li>\n<li><code>capture</code> - Capture an image and slicer manfiest for a theme</li>\n<li><code>slice</code> - Generates image slices from a given image directed by a JSON manifest</li>\n</ul>\n\n\n<h2 id='command_reference-section-92'>sencha theme build</h2>\n\n<p>This command will build the specified theme's image sprites.</p>\n\n<p><code>IMPORTANT</code>: This command should only be used for Ext JS 4.1 applications. For\nExt JS 4.2 applications, themes are now <code>packages</code> and should be managed using\nthe <code>sencha package build</code> process.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--environment</code>, <code>-en</code> - The build environment (e.g., production or testing)</li>\n<li><code>--output-path</code>, <code>-o</code> - The destination path for the sliced images</li>\n<li><code>--page</code>, <code>-p</code> - The page to slice</li>\n<li><code>--theme-name</code>, <code>-t</code> - The name of the theme to build</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha theme build [options] [theme-name] \\\n                             [environment]\n</code></pre>\n\n<h2 id='command_reference-section-93'>sencha theme capture</h2>\n\n<p>This command will capture an image and slice manifest for a specified page.</p>\n\n<p>It is rarely necessary to call this command directly as it is part of the theme\nbuild process. In Ext JS 4.2 applications or theme packages, this command is\ncalled by the build script's <code>slice</code> step. In Ext JS 4.1 applications this is\ncalled for each application theme or directly by the 'sencha theme build`\ncommand.</p>\n\n<p>For example, this is roughly the command performed by the <code>slice</code> step for a\ntheme package:</p>\n\n<pre><code>sencha theme capture -page sass/example/theme.html \\\n                     -image build/theme-capture.png \\\n                     -manifest build/theme-capture.json\n</code></pre>\n\n<p>Once the image and slicer manifest are produced, the <code>sencha fs slice</code> command\nextracts the background images and sprites required for Internet Explorer.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--image-file</code>, <code>-i</code> - The output image (e.g. \"theme-capture.png\")</li>\n<li><code>--manifest</code>, <code>-m</code> - The output slice manifest (e.g. \"theme-capture.json\")</li>\n<li><code>--page</code>, <code>-p</code> - The page to load for capturing theme contents</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha theme capture [options] \n</code></pre>\n\n<h2 id='command_reference-section-94'>sencha theme slice</h2>\n\n<p>This command performs image slicing and manipulation driven by the contents of\na JSON manifest file. The manifest file contains an array of image area\ndefinitions that further contain a set of \"slices\" to make.</p>\n\n<p>This file and the corresponding image are typically produced for a Theme as\npart of the theme package build. For details on this process, consult this\nguide:</p>\n\n<p>http://docs.sencha.com/ext-js/4-2/#!/guide/command_slice</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--format</code>, <code>-f</code> - The image format to save - either \"png\" or \"gif\" (the default)</li>\n<li><code>--image</code>, <code>-i</code> - The image to slice</li>\n<li><code>--manifest</code>, <code>-m</code> - The slicer manifest (JSON) file</li>\n<li><code>--out-dir</code>, <code>-o</code> - The root folder to which sliced images are written</li>\n<li><code>--quantized</code>, <code>-q</code> - Enables image quantization (default is true)</li>\n<li><code>--tolerate-conflicts</code>, <code>-t</code> - Tolerate conflicts in slice manifest</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha theme slice [options] \n</code></pre>\n\n<h2 id='command_reference-section-95'>sencha upgrade</h2>\n\n<p>This command downloads and installs the current version of Sencha Cmd. Or you\ncan specify the version you want to install as part of the command.</p>\n\n<p>The following command downloads and installs the current version of Sencha Cmd:</p>\n\n<pre><code>sencha upgrade\n</code></pre>\n\n<p>This command downloads a particular version:</p>\n\n<pre><code>sencha upgrade 3.0.2.288\n</code></pre>\n\n<p>If the version requested is the already installed then this command will, by\ndefault, do nothing. This can be forced using <code>--force</code>:</p>\n\n<pre><code>sencha upgrade --force\n</code></pre>\n\n<p>If the version requested is the version in the <code>PATH</code>, the command will exit\nwith a message saying that the current version cannot be upgraded. In this case\nthe <code>--force</code> option is ignored.</p>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--beta</code>, <code>-b</code> - Check for the latest beta or RC version (vs stable release)</li>\n<li><code>--check</code>, <code>-c</code> - Only check for an upgrade but do not install it</li>\n<li><code>--force</code>, <code>-f</code> - Force a (re)install even if the version is already installed</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha upgrade [options] [version=\"\"]\n</code></pre>\n\n<h2 id='command_reference-section-96'>sencha which</h2>\n\n<p>This command display the location of Sencha Cmd.</p>\n\n<pre><code>sencha which\nC:\\Users\\<USER>\\bin\\Sencha\\Cmd\\3.1.0.220\n</code></pre>\n\n<h3>Options</h3>\n\n<ul>\n<li><code>--output</code>, <code>-o</code> - Name of an output property file to write the location as a property</li>\n<li><code>--property</code>, <code>-p</code> - Name of the property to write to the output property file for the location</li>\n</ul>\n\n\n<h3>Syntax</h3>\n\n<pre><code>sencha which [options] \n</code></pre>\n"});