Ext.data.JsonP.drawing({"title":"Drawing","guide":"<h1>Drawing</h1>\n<div class='toc'>\n<p><strong>Contents</strong></p>\n<ol>\n<li><a href='#!/guide/drawing-section-1'>Creating a Drawing Surface</a></li>\n<li><a href='#!/guide/drawing-section-2'>Interacting with a Sprite</a></li>\n</ol>\n</div>\n\n<p>The drawing package enables you to draw\ngeneral purpose graphics and animations\nthat can be used with the\n<a href=\"#!/guide/charting\">charting classes</a>\nand other interfaces\nto create graphics that work\non all browsers and mobile devices.\nThis document guides you through\nthe overall design and implementation details\nof the Drawing package.</p>\n\n<p>The draw package provides a versatile tool\nthat creates custom graphics in a cross-browser/device manner\nand also performs rich animations with them.</p>\n\n<p>The Draw package contains a Surface class\nthat abstracts the underlying graphics implementation\nand enables the developer to create arbitrarily shaped Sprites or SpriteGroups\nthat respond to interactions like mouse events\nand also provide rich animations on all attributes like shape, color, size, etc.</p>\n\n<p>The underlying/concrete implementations for the Surface class\nare SVG (for SVG capable browsers) and\nVML (for the version 9 and earlier of the Internet Explorer family).\nSurface can be considered as an interface\nfor the SVG and VML rendering engines\nthat is agnostic to its underlying implementations.\nMost of the methods and ways to create sprites\nare heavily inspired by the <a href=\"http://www.w3.org/TR/SVG/\">SVG standard</a>.</p>\n\n<h2 id='drawing-section-1'>Creating a Drawing Surface</h2>\n\n<p>You can create a simple drawing surface\nwithout loading the Charting package at all.\nThis enables you to create arbitrary graphics\nthat work on all browsers/devices and animate well.\nFor example, you could create an interactive map of the United States\nwhere each state is a sprite,\nor an infographic where each element is also a sprite.\nWhat's interesting about making sprites and not images\nis that the document acquires a new level of interactivity\nbut also that, because the images are VML and SVG based,\nthey will never loose quality and can be printed correctly.</p>\n\n<p>In order to use the Draw package directly\nyou can create a Draw Component and\n(for example) append it to an <code><a href=\"#!/api/Ext.window.Window\" rel=\"Ext.window.Window\" class=\"docClass\">Ext.Window</a></code>:</p>\n\n<pre class='inline-example '><code>var drawComponent = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.draw.Component\" rel=\"Ext.draw.Component\" class=\"docClass\">Ext.draw.Component</a>', {\n    viewBox: false,\n    items: [{\n        type: 'circle',\n        fill: '#ffc',\n        radius: 100,\n        x: 100,\n        y: 100\n    }]\n});\n\n<a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.window.Window\" rel=\"Ext.window.Window\" class=\"docClass\">Ext.Window</a>', {\n    width: 230,\n    height: 230,\n    layout: 'fit',\n    items: [drawComponent]\n}).show();\n</code></pre>\n\n<p>In this case, we created a draw component\nand added a sprite to it.\nThe <em>type</em> of the sprite is <em>circle</em>\nso if you run this code you'll see a yellow-ish circle in a Window.\nWhen setting <code>viewBox</code> to <code>false</code>\nwe are responsible for setting\nthe object's position and dimensions accordingly.</p>\n\n<p>Sprites can have different types. Some of them are:</p>\n\n<ul>\n<li><em>circle</em> - To draw circles.\nYou can set the radius by using the <em>radius</em> parameter in the sprite configuration.</li>\n<li><em>rect</em> - To render rectangles.\nYou can set the width and height of the rectangle by using the <em>width</em> and <em>height</em> parameters\nin the sprite configuration.</li>\n<li><em>text</em> - To render text as a sprite.\nYou can set the font/font-size by using the <em>font</em> parameter.</li>\n<li><em>path</em> - The most powerful sprite type.\nWith it you can create arbitrary shapes by using\nthe <a href=\"http://www.w3.org/TR/SVG/paths.html\">SVG path syntax</a>.\nYou can find a quick tutorial on to how to get started\nwith the path syntax\n<a href=\"https://developer.mozilla.org/en/SVG/Tutorial/Paths\">here</a>.</li>\n</ul>\n\n\n<p>A Sprite is an object rendered in a Drawing surface.\nThere are different options and types of sprites.\nThe configuration of a Sprite is an object with the following properties:</p>\n\n<ul>\n<li><strong>type</strong> - (String) The type of the sprite.\nPossible options are 'circle', 'path', 'rect', 'text', 'square'.</li>\n<li><strong>width</strong> - (Number) Used in rectangle sprites,\nthe width of the rectangle.</li>\n<li><strong>height</strong> - (Number) Used in rectangle sprites,\nthe height of the rectangle.</li>\n<li><strong>size</strong> - (Number) Used in square sprites,\nthe dimension of the square.</li>\n<li><strong>radius</strong> - (Number) Used in circle sprites, the radius of the circle.</li>\n<li><strong>x</strong> - (Number) The position along the x-axis.</li>\n<li><strong>y</strong> - (Number) The position along the y-axis.</li>\n<li><strong>path</strong> - (Array) Used in path sprites,\nthe path of the sprite written in SVG-like path syntax.</li>\n<li><strong>opacity</strong> - (Number) The opacity of the sprite.</li>\n<li><strong>fill</strong> - (String) The fill color.</li>\n<li><strong>stroke</strong> - (String) The stroke color.</li>\n<li><strong>stroke-width</strong> - (Number) The width of the stroke.</li>\n<li><strong>font</strong> - (String) Used with text type sprites.\nThe full font description.\nUses the same syntax as the CSS <code>font</code> parameter.</li>\n<li><strong>text</strong> - (String) Used with text type sprites.\nThe text itself.</li>\n</ul>\n\n\n<p>Additionally there are three transform objects\nthat can be set with <code>setAttributes</code>\nwhich are <code>translate</code>, <code>rotate</code> and <code>scale</code>.</p>\n\n<p>For translate, the configuration object\ncontains x and y attributes for the translation. For example:</p>\n\n<pre><code>sprite.setAttributes({\n  translate: {\n   x: 10,\n   y: 10\n  }\n}, true);\n</code></pre>\n\n<p>For rotate, the configuration object\ncontains x and y attributes\nfor the center of the rotation (which are optional),\nand a degrees attribute\nthat specifies the rotation in degrees. For example:</p>\n\n<pre><code>sprite.setAttributes({\n  rotate: {\n   degrees: 90\n  }\n}, true);\n</code></pre>\n\n<p>For scale, the configuration object contains x and y attributes\nfor the x-axis and y-axis scaling. For example:</p>\n\n<pre><code>sprite.setAttributes({\n  scale: {\n   x: 10,\n   y: 3\n  }\n}, true);\n</code></pre>\n\n<h2 id='drawing-section-2'>Interacting with a Sprite</h2>\n\n<p>Now that we've created a draw surface with a sprite in it,\nlet's dive into how to interact with the sprite.\nWe can get a handle to the sprite we want to modify\nby adding that sprite imperatively to the surface:</p>\n\n<pre class='inline-example '><code>// Create a draw component\nvar drawComponent = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.draw.Component\" rel=\"Ext.draw.Component\" class=\"docClass\">Ext.draw.Component</a>', {\n    viewBox: false\n});\n\n// Create a window to place the draw component in\n<a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.window.Window\" rel=\"Ext.window.Window\" class=\"docClass\">Ext.Window</a>', {\n    width: 220,\n    height: 230,\n    layout: 'fit',\n    items: [drawComponent]\n}).show();\n\n// Add a circle sprite\nvar myCircle = drawComponent.surface.add({\n    type: 'circle',\n    x: 100,\n    y: 100,\n    radius: 100,\n    fill: '#cc5'\n});\n\n// Now do stuff with the sprite, like changing its properties:\nmyCircle.setAttributes({\n    fill: '#ccc'\n}, true);\n\n// or animate an attribute on the sprite\nmyCircle.animate({\n    to: {\n        fill: '#555'\n    },\n    duration: 2000\n});\n\n// Add a mouseup listener to the sprite\nmyCircle.addListener('mouseup', function() {\n    alert('mouse upped!');\n});\n</code></pre>\n\n<p>In this example we've seen how we can add events,\nset sprite attributes and animate these attributes\nusing the draw package.\nAs you can see this package is a versatile abstraction layer\nover the graphics we can do.\nWhat's most interesting about this class\nis that we aren't tied to a specific shape or structure;\nalso all elements support events,\nsetting attributes and creating animations.\nMost important of all, all of this is compatible in all browsers and devices.</p>\n"});