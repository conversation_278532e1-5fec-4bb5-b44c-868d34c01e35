Ext.data.JsonP.command_compiler_meta({"title":"Generating Metadata","guide":"<h1>Generating Metadata</h1>\n<div class='toc'>\n<p><strong>Contents</strong></p>\n<ol>\n<li><a href='#!/guide/command_compiler_meta-section-1'>Generating Output with meta</a></li>\n<li><a href='#!/guide/command_compiler_meta-section-2'>Generating a Custom Bootstrap</a></li>\n<li><a href='#!/guide/command_compiler_meta-section-3'>Changing the Format</a></li>\n<li><a href='#!/guide/command_compiler_meta-section-4'>Exporting Filenames</a></li>\n<li><a href='#!/guide/command_compiler_meta-section-5'>Exporting Definitions</a></li>\n</ol>\n</div>\n\n<p>The metadata tracked by the Sencha Cmd compiler has a variety of uses, some of which we'll\nexamine in this guide. To support these uses, the compiler can export and format this\nmetadata in several different ways, which we'll cover here, as well.</p>\n\n<p><p><img src=\"guides/command_compiler_meta/../command/sencha-command-128.png\" alt=\"\"></p></p>\n\n<h2 id='command_compiler_meta-section-1'>Generating Output with meta</h2>\n\n<p>One of the major new dimensions provided by the compiler is its ability to export metadata\nin various formats. This feature is used to produce the <code>ext.js</code> \"bootstrap\" file that\ncontains various classes and a block of metadata about all of the files in the\nframework.</p>\n\n<p>There are several forms of metadata that the compiler can export using the <code>meta</code> command:</p>\n\n<ul>\n<li>Class aliases</li>\n<li>Alternate class names</li>\n<li>Loader paths</li>\n<li>Filenames</li>\n<li>Definitions</li>\n</ul>\n\n\n<h2 id='command_compiler_meta-section-2'>Generating a Custom Bootstrap</h2>\n\n<p><strong>Note.</strong> This process is handled automatically for applications generated by Sencha Cmd. If\nyou are not using Sencha Cmd generated application, this section describes how to achieve\nthe same results manually.</p>\n\n<p>The primary use for the <code>meta</code> command is to create your own \"bootstrap\" file. This file\ngives the framework the same level of awareness of your application code that it has of\nthe framework code itself.</p>\n\n<p>The simplest way to manage your bootstrap file is to store it alongside your markup file.\nIf that won't work for you, read on to see how to manage relative paths. If you have your\nmarkup file in a source folder in your classpath, you need to tell the compiler to ignore\nthe bootstrap file. Do this using the <code>-ignore</code> switch:</p>\n\n<pre><code>sencha compile -classpath=sdk/src,app -ignore bootstrap.js \\\n    ...\n</code></pre>\n\n<h3>Enabling Wildcard Support in <code>requires</code></h3>\n\n<p>The end of <code>\"ext-debug.js\"</code> contains these two function calls:</p>\n\n<pre><code><a href=\"#!/api/Ext.ClassManager-method-addNameAlternateMappings\" rel=\"Ext.ClassManager-method-addNameAlternateMappings\" class=\"docClass\">Ext.ClassManager.addNameAlternateMappings</a>({\n    \"<a href=\"#!/api/Ext.draw.engine.ImageExporter\" rel=\"Ext.draw.engine.ImageExporter\" class=\"docClass\">Ext.draw.engine.ImageExporter</a>\": [],\n    \"<a href=\"#!/api/Ext.layout.component.Auto\" rel=\"Ext.layout.component.Auto\" class=\"docClass\">Ext.layout.component.Auto</a>\": [],\n    ...\n]);\n\n<a href=\"#!/api/Ext.ClassManager-method-addNameAliasMappings\" rel=\"Ext.ClassManager-method-addNameAliasMappings\" class=\"docClass\">Ext.ClassManager.addNameAliasMappings</a>({\n    \"<a href=\"#!/api/Ext.draw.engine.ImageExporter\" rel=\"Ext.draw.engine.ImageExporter\" class=\"docClass\">Ext.draw.engine.ImageExporter</a>\": [],\n    \"<a href=\"#!/api/Ext.layout.component.Auto\" rel=\"Ext.layout.component.Auto\" class=\"docClass\">Ext.layout.component.Auto</a>\": [\n        \"layout.autocomponent\"\n    ],\n    ...\n]);\n</code></pre>\n\n<p>It is the presence of these two pieces of metadata that allow wildcards to be used in\n<code>requires</code> statements. That is:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('MyApp.App', {\n    requires: [\n        'Ext.grid.*'\n    ],\n    ...\n});\n</code></pre>\n\n<p>All that is required to use wildcards in your own code is to provide the same bootstrap\ndata for your app.</p>\n\n<p>This command will produce a file that does just that:</p>\n\n<pre><code>sencha compile -classpath=app \\\n    meta -alias -out bootstrap.js and \\\n    meta -alt -append -out bootstrap.js\n</code></pre>\n\n<p>The above command line tells the compiler to read in the source in the <code>app</code> folder and\ngenerate two pieces of metadata. The second piece of metadata is written to the same\noutput file as the first, but using the <code>-append</code> option to append to the file and not\nreplace it.</p>\n\n<p>Once you have the <code>\"bootstrap.js\"</code> file, change your page like so to add it to the\n<code>x-bootstrap</code> section:</p>\n\n<pre><code>&lt;html&gt;\n    &lt;head&gt;\n        &lt;!-- &lt;x-compile&gt; --&gt;\n            &lt;!-- &lt;x-bootstrap&gt; --&gt;\n                &lt;script src=\"../sdk/ext-dev.js\" type=\"text/javascript\"&gt;&lt;/script&gt;\n\n                &lt;script src=\"bootstrap.js\" type=\"text/javascript\"&gt;&lt;/script&gt;\n            &lt;!-- &lt;/x-bootstrap&gt; --&gt;\n\n            &lt;script src=\"app/app.js\" type=\"text/javascript\"&gt;&lt;/script&gt;\n        &lt;!-- &lt;/x-compile&gt; --&gt;\n    &lt;/head&gt;\n    &lt;body&gt;&lt;/body&gt;\n&lt;/html&gt;\n</code></pre>\n\n<p>The <code>\"bootstrap.js\"</code> file needs to be regenerated if you do any of the following:</p>\n\n<ul>\n<li>Add a class</li>\n<li>Remove a class</li>\n<li>Change class aliases</li>\n<li>Change class alternate names</li>\n</ul>\n\n\n<p>This rebuild of the bootstrap data can be handled in a variety of ways, but the fundamental\nquestion is whether to keep these files in source control or require developers to\ngenerate them locally. Both approaches work and can be automated to some degree.</p>\n\n<p><strong>Note.</strong> For applications generated by Sencha Cmd, this is handled as part of the build\nprocess of <code>sencha app build</code>. Alternatively, refreshing just the bootstrap instead of\nperforming a full build is accomplished by the <code>sencha app refresh</code> command.</p>\n\n<h3>Exporting Loader Paths</h3>\n\n<p>In large applications it can be helpful to organize your namespace using multiple source\ntrees. In fact, Ext JS itself uses three source trees. This approach, however, has always\npresented problems for the dynamic loader requiring loader paths to be configured by hand\nto work around the issue. The compiler, however, has complete knowledge of class-to-file\nrelationships given all of the source in the classpath. And the <code>meta</code> command can export\nthat data for use in your application.</p>\n\n<p>If you are already sold on the above to create a <code>\"bootstrap.js\"</code>, this data can be added by\nadding one more <code>meta</code> command (of course, the classpath will contain multiple folders in\nthis case):</p>\n\n<pre><code>sencha compile -classpath=src1,src2,src3 \\\n    meta -alias -out bootstrap.js and \\\n    meta -alt -append -out bootstrap.js and \\\n    meta -loader -append -out bootstrap.js\n</code></pre>\n\n<p>Now the <code>\"bootstrap.js\"</code> file solves both problems. With this approach, the following things\nwill also require you to rebuild <code>\"bootstrap.js\"</code>:</p>\n\n<ul>\n<li>Rename a file or folder</li>\n<li>Reorganize the classpath</li>\n<li>Reorganize the content of any of the source trees</li>\n</ul>\n\n\n<p><strong>Note.</strong> This part is also handled automatically for applications generated by Sencha Cmd.</p>\n\n<h3>Resolving Relative Paths with <code>-base-path</code></h3>\n\n<p>For many good reasons, paths need to be relative. Whenever you deal with relative paths,\nhowever, you need to solve the problem of where those relative paths are based. In the\nabove examples we cheated a bit and placed the <code>\"bootstrap.js\"</code> file next to the markup\nfile. This leverages the fact that the <code>meta</code> command defaults the base folder to the\nlocation of the output file.</p>\n\n<p>In the above examples we cheated a bit and placed the <code>\"bootstrap.js\"</code> file next to\nthe markup file. This leverages the fact that the <code>meta</code> command defaults the base folder\nto the location of the output file.</p>\n\n<p>When this is not the case, you need to tell the <code>meta</code> command the base for determining\nrelative paths. Let's say we want to move the <code>\"bootstrap.js\"</code> file in to the <code>\"build\"</code> folder\n(perhaps because we are not keeping it in source control). Since our page is in the current\nfolder and our source is in the <code>\"app\"</code> folder, this will generate the proper relative paths:</p>\n\n<pre><code>sencha compile -classpath=src1,src2,src3 \\\n    meta -alias -out build/bootstrap.js and \\\n    meta -alt -append -out build/bootstrap.js and \\\n    meta -loader -append -base-path . -out build/bootstrap.js\n</code></pre>\n\n<p>Since the <code>-alias</code> and <code>-alt</code> modes do not deal in paths, the <code>-base-path</code> option is only\nneeded on the <code>-loader</code> use of the <code>meta</code> command.</p>\n\n<h2 id='command_compiler_meta-section-3'>Changing the Format</h2>\n\n<p>By default, the <code>meta</code> command exports metadata in JSONP format using a function call\nwrapper appropriate for the type of metadata requested. If a different function call is\ndesired or you want the data in JSON format, you can request this in the <code>meta</code> command.</p>\n\n<p>In the example below, the <code>aliases.json</code> file will contain the alias data in JSON format.\nYou cannot use <code>-append</code> in this case because JSON format requires a single, top-level\nobject or array.</p>\n\n<pre><code>sencha compile -classpath=src1,src2,src3 \\\n    meta -alias -json -out aliases.json\n</code></pre>\n\n<p>In this next example, we customize the JSONP wrapping by supplying the function to call:</p>\n\n<pre><code>sencha compile -classpath=src1,src2,src3 \\\n    meta -alias -jsonp Foo.bar.doAliases -out aliases.js\n</code></pre>\n\n<p>This form can work with <code>-append</code> because it produces JavaScript code. The output of the\nabove looks roughly like this:</p>\n\n<pre><code>Foo.bar.doAliases(\n    // ... the JSON data ...\n);\n</code></pre>\n\n<h2 id='command_compiler_meta-section-4'>Exporting Filenames</h2>\n\n<p>An occasionally useful form of metadata supported by the <code>meta</code> command is filename data.\nThat is, the list of a files in the proper, dependency order. In many ways this is the same\nas the other meta data forms in that this data can be exported in JSON or JSONP format, and\ncan be combined using <code>-append</code>.</p>\n\n<p>The first difference with <code>-filenames</code> is that the default format is text. To produce JSON\nor JSONP, you must specify one of the <code>-json</code> or <code>-jsonp</code> options.</p>\n\n<p>In the default mode of text, the filenames are written as lines of text, one filename per\nline. The following command will create <code>\"filenames.txt\"</code>:</p>\n\n<pre><code>sencha compile -classpath=src1,src2,src3 \\\n    meta -filenames -out filenames.txt\n</code></pre>\n\n<p>Each line of the file can be decorated using the <code>-tpl</code> option. Because of the special\ncharacters needed for this example, we use a response file to hold the template. We put\nthis in <code>\"template.txt\"</code>, like this:</p>\n\n<pre><code>&lt;script src=\"{0}\" type=\"text/javascript\"&gt;&lt;/script&gt;\n</code></pre>\n\n<p>Then run the following command.</p>\n\n<pre><code>sencha compile -classpath=src1,src2,src3 \\\n    meta -filenames -tpl @template.txt -out filenames.txt\n</code></pre>\n\n<p>We now have a chunk of markup that will script-tag in all of the files in their correct order.</p>\n\n<h2 id='command_compiler_meta-section-5'>Exporting Definitions</h2>\n\n<p>The compiler normally reads metadata such as classes, namespaces and dependencies by parsing\nsource code. In situations where this is hidden, for example, when obfuscating a library,\nthe compiler will be unaware of any defined classes or their dependencies.</p>\n\n<p>This form of metadata export can be used to provide the \"symbols\" for such libraries so\nthat users can still compile their application using Sencha Cmd.</p>\n\n<pre><code>sencha compile -classpath=src1,src2,src3 \\\n    meta -definitions -out symbols.js\n</code></pre>\n\n<p>The above creates a file that contains directives like this:</p>\n\n<pre><code>//@define Foo.bar.Thing\n//@require <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>\n//@uses <a href=\"#!/api/Ext.layout.container.HBox\" rel=\"Ext.layout.container.HBox\" class=\"docClass\">Ext.layout.container.HBox</a>\n</code></pre>\n\n<p>These directives are recognized by the compiler and introduce the symbolic names needed for\nuser code to compile. These symbols should be added to the obfuscated library file to ensure\nthat the library code is concatenated in the right order.</p>\n"});