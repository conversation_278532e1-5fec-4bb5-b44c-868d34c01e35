A Theme defines the style of the shapes, color, font,
axes and background of a chart.
The theming configuration can be very rich and complex:


    {
        axis: {
            fill: '#000',
            'stroke-width': 1
        },
        axisLabelTop: {
            fill: '#000',
            font: '11px Arial'
        },
        axisLabelLeft: {
            fill: '#000',
            font: '11px Arial'
        },
        axisLabelRight: {
            fill: '#000',
            font: '11px Arial'
        },
        axisLabelBottom: {
            fill: '#000',
            font: '11px Arial'
        },
        axisTitleTop: {
            fill: '#000',
            font: '11px Arial'
        },
        axisTitleLeft: {
            fill: '#000',
            font: '11px Arial'
        },
        axisTitleRight: {
            fill: '#000',
            font: '11px Arial'
        },
        axisTitleBottom: {
            fill: '#000',
            font: '11px Arial'
        },
        series: {
            'stroke-width': 1
        },
        seriesLabel: {
            font: '12px Arial',
            fill: '#333'
        },
        marker: {
            stroke: '#555',
            fill: '#000',
            radius: 3,
            size: 3
        },
        seriesThemes: [{
            fill: '#C6DBEF'
        }, {
            fill: '#9ECAE1'
        }, {
            fill: '#6BAED6'
        }, {
            fill: '#4292C6'
        }, {
            fill: '#2171B5'
        }, {
            fill: '#084594'
        }],
        markerThemes: [{
            fill: '#084594',
            type: 'circle'
        }, {
            fill: '#2171B5',
            type: 'cross'
        }, {
            fill: '#4292C6',
            type: 'plus'
        }]
    }

We can also create a seed of colors
that is a base for the entire theme
just by creating a simple array of colors
in the configuration object like:

    {
      colors: ['#aaa', '#bcd', '#eee']
    }

When setting a base color, the theme generates
an array of colors that match the base color:

    {
      baseColor: '#bce'
    }

You can create a custom theme by extending from the base theme.
For example, to create a custom `Fancy` theme we can do:

    var colors = ['#555',
                  '#666',
                  '#777',
                  '#888',
                  '#999'];

    var baseColor = '#eee';

    Ext.define('Ext.chart.theme.Fancy', {
        extend: 'Ext.chart.theme.Base',

        constructor: function(config) {
            this.callParent([Ext.apply({
                axis: {
                    fill: baseColor,
                    stroke: baseColor
                },
                axisLabelLeft: {
                    fill: baseColor
                },
                axisLabelBottom: {
                    fill: baseColor
                },
                axisTitleLeft: {
                    fill: baseColor
                },
                axisTitleBottom: {
                    fill: baseColor
                },
                colors: colors
            }, config)]);
        }
    });

    var chart = Ext.create('Ext.chart.Chart', {
        theme: 'Fancy',

        /* Other options here... */
    });

