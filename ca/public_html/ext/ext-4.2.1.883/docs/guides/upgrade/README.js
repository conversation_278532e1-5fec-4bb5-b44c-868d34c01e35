Ext.data.JsonP.upgrade({"title":"Upgrade Guide","guide":"<h1>Ext JS 4 Upgrade Guide</h1>\n<div class='toc'>\n<p><strong>Contents</strong></p>\n<ol>\n<li><a href='#!/guide/upgrade-section-1'>General</a></li>\n<li><a href='#!/guide/upgrade-section-2'>Ext Core</a></li>\n<li><a href='#!/guide/upgrade-section-3'>Ext JS</a></li>\n<li><a href='#!/guide/upgrade-section-4'>Theming</a></li>\n<li><a href='#!/guide/upgrade-section-5'>Documentation</a></li>\n</ol>\n</div>\n\n<p>Ext 4 is a revolutionary step forward in web app development. Almost every\nmajor component within the framework has been improved, in many cases\ndrastically so. There are also many components and subsystems that are brand\nnew since Ext 3. This guide will provide an introduction to all major changes\nbetween Ext 3 and 4.</p>\n\n<p>If you find any issues in this document or would like to provide feedback,\nplease visit the <a href=\"http://www.sencha.com/forum/showthread.php?124015-Ext-3-to-4-Migration\" title=\"Ext 3 to 4 Migration\">Ext 3 to 4 Migration</a> thread in the Sencha forums.</p>\n\n<h2 id='upgrade-section-1'>General</h2>\n\n<p>These changes apply across both Ext Core and Ext JS.</p>\n\n<h3>Ext 3 Compatibility</h3>\n\n<p>Ext 4 is by far the most comprehensive update to Ext JS that we've ever\nproduced, and many changes were required that are not compatible with Ext 3.\nHowever, we've gone to great lengths to provide as much backwards-\ncompatibility as possible.</p>\n\n<h4>JS Compatibility Layer</h4>\n\n<p>This is an optional JavaScript file that can be referenced after Ext 4 is\nloaded that will provide the aliases and overrides necessary to make the vast\nmajority of Ext 3 code run correctly under Ext 4 as-is.</p>\n\n<p>You can download the Ext JS 3 to 4 Migration Pack from the 'Related Downloads'\nsection of the <a href=\"http://www.sencha.com/products/extjs/download/\">Ext JS Download page</a></p>\n\n<h4>Sandbox Mode</h4>\n\n<p>Ext 4 has introduced changes that now make it possible to completely sandbox\nprevious versions of the framework alongside Ext 4 within the same page. On\nthe JavaScript side, this is possible with the removal of all prototypal\nenhancements to core JavaScript objects in Ext 4. Now you can simply alias the\nglobal Ext object to a different name and be completely isolated from previous\nExt versions.</p>\n\n<p>On the markup/CSS side of things, Ext 4 now uses CSS generated from templates\nusing Compass &amp; SASS, so it is trivial to customize the prefix of all CSS\nrules defined by the framework. Combined with the new Ext.baseCSSPrefix\nproperty, it is now extremely easy to isolate markup generated from multiple\nversions of Ext on a single page.</p>\n\n<h3>Package &amp; Namespace Updates</h3>\n\n<p>All classes and packages have been restructured according to a strict naming\nconvention. This makes classes more consistent and easier to find. For\nexample, in Ext 3 classes like Button, CycleButton and SplitButton are simply\ngrouped under /widgets (along with many other classes). The classes are also\ndefined directly on the global Ext object.</p>\n\n<p>In Ext 4, every class has been consistently placed into packages and\nnamespaces based on related functionality. This is not entirely new, but was\nnever consistently enforced in Ext 3. Back to the Button example above, in Ext\n4 these classes are now:</p>\n\n<ul>\n<li>Grouped into a single src/button/ package at the file level</li>\n<li>Grouped into a new Ext.button namespace in code</li>\n<li>Renamed based on the new namspaces (e.g., <a href=\"#!/api/Ext.button.Split\" rel=\"Ext.button.Split\" class=\"docClass\">Ext.SplitButton</a> -> <a href=\"#!/api/Ext.button.Split\" rel=\"Ext.button.Split\" class=\"docClass\">Ext.button.Split</a>)</li>\n</ul>\n\n\n<p>All classes that were reorganized are still available via the new\nalternateClassName property, so Ext 3 classnames will still work under Ext 4\nif necessary (e.g. alternateClassName: '<a href=\"#!/api/Ext.button.Split\" rel=\"Ext.button.Split\" class=\"docClass\">Ext.SplitButton</a>'). <em>Going forward with\nExt 4 development it's recommended that you migrate to the new conventions in\nyour own code</em>.</p>\n\n<h2 id='upgrade-section-2'>Ext Core</h2>\n\n<h3>New Class System</h3>\n\n<h4>Resources</h4>\n\n<p><a href=\"http://www.sencha.com/blog/countdown-to-ext-js-4-dynamic-loading-and-new-class-system\">Overview blog post &amp; demo</a></p>\n\n<p>Ext of course is already built on a powerful prototype-based class system,\nwhich approximates a traditional OO-based class system. In Ext 4 we've taken\nour class system to a new level of flexibility with the addition of many key\nfeatures, and best of all the changes are 100% backwards-compatible with the\nExt 3 class system. The main new features are:</p>\n\n<ul>\n<li><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> to create class definitions within the new system</li>\n<li>Automatic dependency management and dynamic class loading</li>\n<li>Mixins</li>\n<li>Statics</li>\n<li>New config option for Automatic setter / getter generation</li>\n</ul>\n\n\n<p><p><img src=\"guides/upgrade/ext4-class-system.jpg\" alt=\"Ext 4 Class System\"></p></p>\n\n<h4>Class Definition</h4>\n\n<p>You can still use the tried and true new ClassName() syntax in Ext 4, but the\npreferred syntax going forward is to use the new <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> function to define\nnew classes. By defining classes explicitly within the Ext class system all of\nthe additional class features in the following sections become enabled. The\nnew keyword will still work, but without those benefits.</p>\n\n<p>An additional benefit of using <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> is that it will automatically detect\nand create new namespaces as needed, without the need for explicitly defining\nthe namespaces separately from declaring the classes that use them.</p>\n\n<p>Here's a simple example of the old and new syntax for how you would define a\ncustom class that extends an existing Ext component:</p>\n\n<pre><code>// Ext 3:\n<a href=\"#!/api/Ext-method-ns\" rel=\"Ext-method-ns\" class=\"docClass\">Ext.ns</a>('MyApp'); // required in Ext 3\nMyApp.CustomerPanel = <a href=\"#!/api/Ext-method-extend\" rel=\"Ext-method-extend\" class=\"docClass\">Ext.extend</a>(<a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.Panel</a>, {\n    // etc.\n});\n\n// Ext 4\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('MyApp.CustomerPanel', {\n    extend: '<a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>',\n    // etc.\n});\n</code></pre>\n\n<h4>Class Loading</h4>\n\n<p>Ext 4 now includes a robust dynamic class loading system that also provides\nintegrated dependency management. Again, this is an optional feature, but\nprovides a couple of key benefits:</p>\n\n<ul>\n<li><p><strong>Dynamic loading</strong>:\nClasses can be loaded dynamically and asynchronously at runtime simply\nbased on the dependency tree that Ext manages internally. This means that\nyou can stop managing brittle blocks of manual include tags in HTML pages\nand simply let the class loader figure out what your page needs to run.\nThis is mostly useful in the development environment when dependency\nflexibility is more important than page load speed.</p></li>\n<li><p><strong>Dynamic build generation</strong>:\nFor production, it's usually preferable still to compile down to a single\n(or few) build files. Since Ext now understands the entire dependency\ngraph at runtime, it can easily output that graph as needed (for example,\nin the form of a custom build configuration). And even more importantly,\nthis capability is not restricted to the Ext namespace. You can include any\nnumber of custom classes and namespaces in addition to Ext and as long as\neverything is defined using the class system Ext will be able to figure out\nhow they all relate. In the very near future, any Ext developer will have\none-click custom build capability that automatically manages the entire\nExt + custom application class graph, even as it changes from one minute\nto the next, and only builds <em>exactly</em> what's actually needed by the app.</p></li>\n</ul>\n\n\n<p>This dynamic loading is enabled simply by using <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> and by defining the\ndependencies in code using two new class properties:</p>\n\n<ul>\n<li>requires: The class dependencies required for a class to work. These are guaranteed to be loaded prior to the current class being instantiated.</li>\n<li>uses: Optional class dependencies that are used by, but not required by, a class. These can be loaded asynchronously and do not have to be available for the class to be instantiated.</li>\n</ul>\n\n\n<h4>Mixins</h4>\n\n<p>This is a new approach to plugging custom functionality into existing classes.\nThe new mixins config, included during instantiation of a class, allows you to\n\"mix in\" new capabilities to the class prototype. This is similar to the\nexisting <a href=\"#!/api/Ext-method-override\" rel=\"Ext-method-override\" class=\"docClass\">Ext.override</a> function, although it does not replace existing methods\nlike override does, it simply augments the prototype with whatever is new.\nExample syntax:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Sample.Musician', {\n    extend: 'Sample.Person',\n\n    mixins: {\n        guitar: 'Sample.ability.CanPlayGuitar',\n        compose: 'Sample.ability.CanComposeSongs',\n        sing: 'Sample.ability.CanSing'\n    }\n});\n</code></pre>\n\n<h4>Statics</h4>\n\n<p>Any class can now define static methods simply by defining them in its statics\nclass property.</p>\n\n<h4>Config</h4>\n\n<p>In Ext we currently use the term <strong>config</strong> to mean any properties on a class\nprototype that, by convention, make up its configurable API and can be set by\ncalling code. While this is still true in Ext 4, there is also a new optional\nconfig that's actually called config. When configuration properties are added\nto the config property, it will automatically generate a getter, setter, reset\nand apply (template method for custom setter logic) methods matching that\nproperty. For example, given this code:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('MyClass', {\n    config: {\n        title: 'Default Title'\n    }\n});\n</code></pre>\n\n<p>...the following methods will get generated automatically behind the scenes:</p>\n\n<pre><code>title: 'Default Title',\n\ngetTitle: function() {\n    return this.title;\n},\n\nresetTitle: function() {\n    this.setTitle('Default Title');\n},\n\nsetTitle: function(newTitle) {\n   this.title = this.applyTitle(newTitle) || newTitle;\n},\n\napplyTitle: function(newTitle) {\n    // custom code here\n    // e.g. <a href=\"#!/api/Ext-method-get\" rel=\"Ext-method-get\" class=\"docClass\">Ext.get</a>('titleEl').update(newTitle);\n}\n</code></pre>\n\n<h3>Env Namespace</h3>\n\n<p>Provides access to comprehensive information about the host browser and\noperating system, as well as a complete list of modern browser feature\ndetection.</p>\n\n<h4>Ext.env.Browser</h4>\n\n<p>This class provides all of the browser metadata (name, engine, version,\nisStrict, etc.) that used to exist directly on the global Ext object.</p>\n\n<h4>Ext.env.FeatureDetector</h4>\n\n<p>This is a brand new class whose functionality did not exist in Ext 3. It\nprovides properties detailing the feature capabilities of the running host\nenvironment, primarily to detect the availability of modern HTML5 and CSS3\nfeatures, as well as mobile features, including:</p>\n\n<ul>\n<li>CSS transforms / animations / transitions</li>\n<li>SVG / VML</li>\n<li>Touch availability / Orientation</li>\n<li>Geolocation</li>\n<li>SqlDatabase</li>\n<li>Websockets</li>\n<li>History</li>\n<li>Audio</li>\n<li>Video</li>\n</ul>\n\n\n<h4>Ext.env.OS</h4>\n\n<p>Identifies the current host operating system, and includes a more\ncomprehensive list than Ext 3 did (adding various mobile OS'es).</p>\n\n<h3>Lang Package</h3>\n\n<p>Much of the functionality within this new package existed under Ext 3, but as\nmodifications to core JavaScript object prototypes. While this was convenient,\nit made it very challenging for Ext to coexist with other frameworks that\nmodified the same objects. It also meant that Ext was directly exposed to any\nchanges that might occur in the ECMAscript specification going forward. To\nremedy these problems we've completely removed all core JavaScript object\nmodifications and placed them all into appropriately namespaced static classes\nunder the Ext object.</p>\n\n<p>All of the Prototype enhancements to the following objects have been moved\naccordingly (note that there is not actually a lang <em>namespace</em> -- it's just\nthe package that holds these new files in the source tree):</p>\n\n<ul>\n<li>Array -> <a href=\"#!/api/Ext.Array\" rel=\"Ext.Array\" class=\"docClass\">Ext.Array</a></li>\n<li>Date -> <a href=\"#!/api/Ext.Date\" rel=\"Ext.Date\" class=\"docClass\">Ext.Date</a></li>\n<li>Function -> <a href=\"#!/api/Ext.Function\" rel=\"Ext.Function\" class=\"docClass\">Ext.Function</a></li>\n<li>Number -> <a href=\"#!/api/Ext.Number\" rel=\"Ext.Number\" class=\"docClass\">Ext.Number</a></li>\n<li>Object -> <a href=\"#!/api/Ext.Object\" rel=\"Ext.Object\" class=\"docClass\">Ext.Object</a></li>\n<li>String -> <a href=\"#!/api/Ext.String\" rel=\"Ext.String\" class=\"docClass\">Ext.String</a></li>\n</ul>\n\n\n<p>Note that these will all be aliased back to the core object prototypes in the\ncompatibility file, but in order to fully move to Ext 4 and remove the extra\ncompatibility layer the usage of any of the Ext 3 versions of these functions\nwill ultimately have to be updated to the new namespaced versions.</p>\n\n<h4><a href=\"#!/api/Ext.Function\" rel=\"Ext.Function\" class=\"docClass\">Ext.Function</a></h4>\n\n<p>There are a few noteworthy changes to how the base Function prototype methods\nhave been implemented in Ext 4. Most importantly, the Function.createDelegate\nand Function.createCallback methods are now named <a href=\"#!/api/Ext.Function-method-bind\" rel=\"Ext.Function-method-bind\" class=\"docClass\">Ext.Function.bind</a> and\n<a href=\"#!/api/Ext.Function-method-pass\" rel=\"Ext.Function-method-pass\" class=\"docClass\">Ext.Function.pass</a> respectively. Likewise, Function.defer is now available as\n<a href=\"#!/api/Ext.Function-method-defer\" rel=\"Ext.Function-method-defer\" class=\"docClass\">Ext.Function.defer</a>. Because these three functions are so commonly used, they\nare also aliased directly onto the Ext object as <a href=\"#!/api/Ext-method-bind\" rel=\"Ext-method-bind\" class=\"docClass\">Ext.bind</a>, <a href=\"#!/api/Ext-method-pass\" rel=\"Ext-method-pass\" class=\"docClass\">Ext.pass</a> and\n<a href=\"#!/api/Ext-method-defer\" rel=\"Ext-method-defer\" class=\"docClass\">Ext.defer</a>.</p>\n\n<p>The other Function methods like createSequence and createInterceptor have also\nbeen moved to <a href=\"#!/api/Ext.Function\" rel=\"Ext.Function\" class=\"docClass\">Ext.Function</a>, and there are also a few new useful additions like\ncreateBuffered and createThrottled.</p>\n\n<p>Here are some examples of how the invocation syntax has changed:</p>\n\n<pre><code>// Ext 3:\nmyFunction.createDelegate(this, [arg1, arg2]);\nmyFunction.defer(1000, this);\n\n// Ext 4:\n<a href=\"#!/api/Ext-method-bind\" rel=\"Ext-method-bind\" class=\"docClass\">Ext.bind</a>(myFunction, this, [arg1, arg2];\n<a href=\"#!/api/Ext-method-defer\" rel=\"Ext-method-defer\" class=\"docClass\">Ext.defer</a>(myFunction, 1000, this);\n</code></pre>\n\n<h2 id='upgrade-section-3'>Ext JS</h2>\n\n<h4>Resources</h4>\n\n<ul>\n<li><a href=\"http://vimeo.com/17666102\">Intro to Ext 4 (video)</a></li>\n<li><a href=\"http://vimeo.com/17733892\">Ext 4 Architecture (video)</a></li>\n</ul>\n\n\n<h3>General</h3>\n\n<h4>Adapters</h4>\n\n<p>In previous versions of Ext through version 3 we supported the use of third-\nparty base libraries via adapters. As of Ext 4 this support has been\ndiscontinued. Moving forward, Ext Core will be the required foundation for all\nExt JS applications. Of course third party libraries may still be optionally\nused <em>in addition to</em> Ext Core (and in fact this will even work better now\nthat JavaScript object modifications have been removed from Ext), but\nthey will no longer be supported as base library <em>dependencies</em> for Ext.</p>\n\n<h4>ARIA</h4>\n\n<p>Ext 4 features a new level of accessibility with full ARIA support baked right\ninto every component in the framework. <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a> now has a config property\ncalled ariaRole which defaults to \"presentation\" (meaning the role is simply\nvisual and provides no user interaction functionality) but can be easily\noverridden as needed. For example, <a href=\"#!/api/Ext.button.Button\" rel=\"Ext.button.Button\" class=\"docClass\">Ext.button.Button</a> overrides the default\nwith ariaRole:'button'. This renders the standard role HTML attribute into the\nwrapping markup tag and tells screen readers that the contained markup (no\nmatter how complex) functionally represents an action button. This is hugely\nimportant for accessibility and is now implemented throughout Ext.</p>\n\n<p>Another important ARIA hint that is built into <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a> is automatic\nadding/removing of the standard aria-disabled HTML attribute to signify the\nenabled state of functional UI elements to screen readers.</p>\n\n<h3>Data</h3>\n\n<p>The data package has seen a significant upgrade since 3.x. Data is one of the\nmany packages that Ext 4 now shares with Sencha Touch, and there is already a\nhuge amount of information about it available today. Here are a few of the\narticles that give some background on the new capabilities -- we strongly\nsuggest reading at least the first one before diving into code:</p>\n\n<h4>Resources</h4>\n\n<ul>\n<li><a href=\"http://www.sencha.com/blog/countdown-to-ext-js-4-data-package/\">Ext 4 Data Overview (blog post)</a></li>\n<li><a href=\"http://www.sencha.com/blog/ext-js-4-anatomy-of-a-model/\">Models (blog post)</a></li>\n<li><a href=\"http://edspencer.net/2011/02/proxies-extjs-4.html\">Proxies (blog post)</a></li>\n<li><a href=\"http://www.sencha.com/blog/using-validations-and-associations-in-sencha-touch/\">Validations &amp; Associations (blog post)</a></li>\n</ul>\n\n\n<h4>Overview</h4>\n\n<p>Most of the changes in the data package are non-breaking when including the\nExt 3 compatibility file. The biggest updates have come to Store, Proxy and\nModel, which is the new name for Record:</p>\n\n<ul>\n<li><p><strong>Store</strong> no longer cares about the format of the data (e.g. no need for\nJsonStore, XmlStore and other store subclasses). It now delegates all of\nits loading and saving to the attached Proxy, which now receives the\nReader and Writer instances. Store can also perform multi sorting,\nfiltering and grouping dynamically on the client side, and can\nsynchronize itself with your server</p></li>\n<li><p><strong>Proxy</strong> can now be attached to either a Store or <a href=\"http://www.sencha.com/blog/ext-js-4-anatomy-of-a-model\">directly to a Model</a>,\nwhich makes it easy to manipulate data without a Store. Proxies can be\nconfigured with Readers and Writers which decode and encode communications\nwith your server.</p></li>\n<li><p><strong>Model</strong> is a much more capable version of Record, adding support for\n<a href=\"http://www.sencha.com/blog/ext-js-4-anatomy-of-a-model\">associations, validations and much more</a>.</p></li>\n</ul>\n\n\n<p>Ext JS 4 also features the brand new LocalStorageProxy which seamlessly\npersists your Model data into HTML5 localStorage.</p>\n\n<h3>Draw</h3>\n\n<p>This package is entirely new and has no Ext 3 analog. This is an entire\nlibrary that provides custom drawing capabilities by abstracting the standard\nSVG and VML engines, choosing the best one available at runtime based on the\ncurrent browser. This package provides the foundation for the new\ncharting package in Ext 4, but can also easily be used for any type of\ncustom drawing and graphics needs. Here are the major features:</p>\n\n<ul>\n<li>HTML5 standards based</li>\n<li>Rendering of primitive shapes, text, images, gradients</li>\n<li>Library classes for manipulating color, matrix transformations, etc.</li>\n<li><p>Ext.draw.DrawComponent</p>\n\n<ul>\n<li>Extends <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a></li>\n<li>Engine preference via feature detection</li>\n<li>Customizable engine preference order</li>\n<li>Layout participation</li>\n</ul>\n</li>\n<li><p>Built-in sprite management via <a href=\"#!/api/Ext.draw.Sprite\" rel=\"Ext.draw.Sprite\" class=\"docClass\">Ext.draw.Sprite</a></p>\n\n<ul>\n<li>Abstracted drawing elements</li>\n<li>Normalizes differences between engine API syntax</li>\n<li>Attributes</li>\n<li>Event support (extends Observable)</li>\n<li>Transforms (Rotate, Translate, Scale)</li>\n<li>Animation supported through Ext.fx</li>\n<li>SpriteComposites</li>\n</ul>\n</li>\n</ul>\n\n\n<h3>Charting &amp; Visualization</h3>\n\n<p>Charting in Ext 4 is a huge step forward from Ext 3, which did support\ncharting, but only Flash-based. In Ext 4 we've completely removed Flash from\nthe picture, yet retained complete best-of-breed browser support (all the way\nback to IE6) using Canvas, SVG and VML. The charting engine automatically\nselects the best engine for the current platform at runtime, and everything is\nrendered uniformly and with the best possible performance across all supported\nbrowsers. More importantly, all charting functions share a single uniform API,\nregardless of the underlying charting engine in use.</p>\n\n<h4>Resources</h4>\n\n<ul>\n<li><a href=\"http://vimeo.com/17673342\">SenchaCon Overview (video)</a></li>\n<li><a href=\"http://www.slideshare.net/senchainc/charts-5971878\">SenchaCon Overview (slides)</a></li>\n<li><p><img src=\"guides/upgrade/working-with-charts.gif\" alt=\"Working with Charts\"></p></li>\n</ul>\n\n\n<h3>Fx</h3>\n\n<p>In Ext 3, the Fx class mainly provided a set of useful Element effects (slide,\nhighlight, fade, etc.). In Ext 4 this basic functionality has been retained,\nbut there is an entire Fx package that goes far beyond simple element effects.</p>\n\n<p>There's now an underlying Animator class that coordinates multiple animations\nconcurrently running on the page.</p>\n\n<p>Components may now be animated in size and position via new Fx \"target\"\nclasses (Ext.fx.target.*), which opens up many new possibilities for creating\ndynamic UI's.</p>\n\n<h3>Layout</h3>\n\n<h4>Resources</h4>\n\n<ul>\n<li><a href=\"http://vimeo.com/17917111\">Ext 4 Layouts (video)</a></li>\n<li><a href=\"http://www.slideshare.net/senchainc/layouts-5971880\">Ext 4 Layouts (slides)</a></li>\n</ul>\n\n\n<h4>ComponentLayout</h4>\n\n<p>Complex components use the new ComponentLayout (as opposed to ContainerLayout,\nthe new name for the traditional Container-based layouts carried over from Ext\n3) to perform sizing of internal elements in response to resize calls. An\nexample of this is the FieldLayout which manages the size and position of both\nan associated label and input element within the containing Field Component.</p>\n\n<h4>FormLayout</h4>\n\n<p>As of Ext 4, FormLayout is no longer available. See Layout in the Form\nsection later in this document for details.</p>\n\n<h4>BorderLayout</h4>\n\n<p>Border layout in Ext 4 is fully backwards-compatible to Ext 3.</p>\n\n<p>Panel Headers now may be orientated vertically, so east and west regions are\nreplaced on collapse by a vertical header (a full-fledged <a href=\"#!/api/Ext.panel.Header\" rel=\"Ext.panel.Header\" class=\"docClass\">Ext.panel.Header</a>\ninstance, unlike the simple Element in Ext 3) containing the title rotated by\n90 degrees (courtesy of Ext 4's new Draw package). The placeholder\nHeader is accessible using the layout's new getPlaceholder method. The\nplaceholder Component is not rendered until the Panel is first collapsed. All\nthe methods of the <a href=\"#!/api/Ext.panel.Header\" rel=\"Ext.panel.Header\" class=\"docClass\">Ext.panel.Header</a> class are available on it. As a Container\nsubclass, it also inherits all the methods of Container.</p>\n\n<p>Another small difference is that the cmargins config is no longer supported.\nThe placeholder Header now has the same margins that the expanded Panel did.\nThis makes it easier to create layouts which look good in both collapsed and\nexpanded states by default.</p>\n\n<p>Internally, the BorderLayout class now uses nested HBox and VBox layouts to\ncreate the bordered arrangement, making the layout much more flexible and\ncustomizable than it has ever been.</p>\n\n<h3>Component</h3>\n\n<p>Any Component subclass may now be configured as floatable using the\nfloating:true config to specify that it floats above the document flow.\nFloating components may be configured as draggable and/or resizable using the\nstandard configs. They may also be added as child items to any container, in\nwhich case they will not participate in the container's layout, but will float\nabove it.</p>\n\n<p>All floating Components now have their z-index managed by a ZIndexManager.\nThis is the successor to the Ext 3 WindowGroup class. By default, floating\ncomponents (such as Windows) which are rendered into the document body by\nhaving their show method called are managed by the singleton\n<a href=\"#!/api/Ext.WindowManager\" rel=\"Ext.WindowManager\" class=\"docClass\">Ext.WindowManager</a>. All floating components therefore have a <code>toFront</code> and\ntoBack method which invokes the ZIndexManager.</p>\n\n<p>Floating components which are added to a specific Container acquire a\nZIndexManager reference at render time. They search for an ancestor Container\nfrom which to request z-index management. Usually, this will be provided by\nthe singleton <a href=\"#!/api/Ext.WindowManager\" rel=\"Ext.WindowManager\" class=\"docClass\">Ext.WindowManager</a>, but if the floating component is the\ndescendant of a floating <strong>Container</strong> such as a Window, that Window will\ncreate its own ZIndexManager instance, and all floating Components within will\nbe managed relative to the Window's z-index.</p>\n\n<p>This is a huge improvement over previous versions of Ext, in which complex\narrangements of floated containers could quickly lead to z-index conflicts\namong different components. Now, for example, the dropdowns of combo boxes\nthat are contained in a window will <em>always</em> be correctly managed above that\nwindow.</p>\n\n<h3>Form</h3>\n\n<h4>Layout</h4>\n\n<p>In Ext 3 the FormPanel used the FormLayout class for positioning field labels,\ninput boxes, error indicators, etc. In Ext 4 FormLayout has been removed\nentirely, in favor of using standard layouts for overall arrangement and the\nnew FieldLayout (one of the new ComponentLayout classes) for the field\nlabels etc. This makes it now possible to use any kind of standard layouts\nwithin any form, making the creation of custom form layouts exceedingly simple\ncompared to Ext 3.</p>\n\n<p>The default container layout for FormPanel is now anchor, although any\nstandard layout may now be used within forms.</p>\n\n<h4>FieldContainer</h4>\n\n<p>Ext4 also introduces a new class for managing form layouts:\n<a href=\"#!/api/Ext.form.FieldContainer\" rel=\"Ext.form.FieldContainer\" class=\"docClass\">Ext.form.FieldContainer</a>. This is a standard <a href=\"#!/api/Ext.container.Container\" rel=\"Ext.container.Container\" class=\"docClass\">Ext.Container</a> which has been\nenhanced to allow addition of a label and/or an error message, using all the\nsame configurations regarding label and error placement that are supported by\n<a href=\"#!/api/Ext.form.Labelable\" rel=\"Ext.form.Labelable\" class=\"docClass\">Ext.form.Labelable</a>. The container's children will be confined to the same\nspace as the field input would occupy in a real Field. This makes it easy to\nadd any group of arbitrary components to a form and have them line up exactly\nwith the form's fields.</p>\n\n<h4>Field as Mixin</h4>\n\n<p>One longstanding difficulty with Ext has been adding non-Field components into\na form with field-like capabilities like layout, validation, value management,\netc. The only way to easily inherit field behavior in Ext 3 was to subclass\n<a href=\"#!/api/Ext.form.field.Base\" rel=\"Ext.form.field.Base\" class=\"docClass\">Ext.form.Field</a> directly, but in reality many components must subclass\nsomething outside of the Field class hiearchy. Since Ext (and JavaScript) has\nno support for true multiple inheritance, the result is normally copying most\nif Field's code directly into the custom class, which is non-optimal to say\nthe least.</p>\n\n<p>Now that Ext 4 supports mixins as part of its updated class system, this\nis no longer an issue. Field can now be used as a mixin to any other\ncomponent, making it simple to provide full Field API support to almost\nanything. And of course you can easily override methods as needed if the\ndefault Field behavior needs to be customized (it is common to override\ngetValue and setValue for example).</p>\n\n<h4>Validation</h4>\n\n<p>A common desire when building forms is to give the user feedback on the\nvalidity of fields as immediately as possible, as the user is entering data.\nWhile this could be done in Ext 3, it was clumsy and required using the\nmonitorValid option on FormPanel which was non-optimal for performance because\nit invoked full-form validation several times per second.</p>\n\n<p>In Ext 4 we have reworked form validation to make immediate feedback more\nelegant and performant. This is enabled mainly by implementing a new event\nflow:</p>\n\n<ul>\n<li>Input fields now listen to a set of modern browser events that allow us\nto detect changes immediately as the user makes them via any possible\nmethod, including typing, cutting/pasting, and dragging text into the\nfield. We handle these events and roll them up together, firing the\nfield's changeevent when a change in its value is observed (discussed\nfurther below).</li>\n<li>Each field validates itself when its value changes. When its validity\nchanges from valid to invalid or vice-versa, it fires a new\nvaliditychange event.</li>\n<li>When any of the validitychange events fired by the fields causes the\noverall validity of the enclosing form to change, the BasicForm fires\na validitychange event of its own.</li>\n</ul>\n\n\n<p>This new event flow allows for immediate feedback to users regarding the\nvalidity of the form, and because it is based on events rather than polling,\nperformance is excellent. Therefore, this is now the default behavior (it had\nto be enabled explicitly in Ext 3), though it can of course be easily\ndisabled.</p>\n\n<p>There are still situations, however, in which polling may still be useful.\nSome browsers (notably Opera and older versions of Safari) do not always fire\nevents when fields are edited in certain ways such as cutting/pasting via the\ncontext menu. To handle these edge cases, you can use the new pollForChanges\nconfig on FormPanel to set up interval polling for changes to field values;\nthis triggers the same event flow above. While polling is still not great for\nperformance, this is less intensive than Ext 3's polling because it only\nchecks for value changes, rather than forcing validation (and therefore value\nparsing, conversion, and pattern matching) each time.</p>\n\n<p>The validation of each field is now triggered when a change in the field's\nvalue is detected (assuming the validateOnChange config is left enabled). The\nbrowser events used to detect value changes are defined by the\ncheckChangeEvents config, and are set to the following by default:</p>\n\n<ul>\n<li>change and propertychange for Internet Explorer</li>\n<li>change, input, textInput, keyup, and dragdrop for all other browsers</li>\n</ul>\n\n\n<p>This set of events successfully detects nearly all user-initiated changes\nacross the set of supported browsers. The only known exceptions at the time of\nwriting are:</p>\n\n<ul>\n<li>Safari 3.2 and older: cut/paste in textareas via the context menu, and\ndragging text into textareas</li>\n<li>Opera 10 and 11: dragging text into text fields and textareas, and cut\nvia the context menu in text fields and textareas</li>\n<li>Opera 9: Same as Opera 10 and 11, plus paste from context menu in text\nfields and textareas</li>\n</ul>\n\n\n<p>If you must absolutely ensure that changes triggered by the above actions also\ntrigger validation, you can use FormPanel's pollForChanges and pollInterval\nconfigs, and/or startPolling and stopPolling methods to check for changes on a\ntimer.</p>\n\n<p>Ext 3 supported the formBind property on buttons added to the form via the\nbuttons property, which allows them to be automatically enabled/disabled as\nthe form's validity changes. In Ext 4, this has been expanded so that formBind\nmay be specified on <em>any component</em> that is added anywhere in the FormPanel.</p>\n\n<h4>FieldDefaults</h4>\n\n<p>One time-saving feature of the old Ext 3 FormLayout was that it allowed\ndevelopers to configure the layout of all fields within the form from a single\nconfig, using the hideLabels, labelAlign, etc. config properties of FormPanel.\nSince Ext 4 now uses the standard layouts for form containers, it would be\nmuch less convenient to have to specify common field layout properties again\nand again for each field or container. Therefore FormPanel now has a\nfieldDefaults config object, which specifies values that will be used as\ndefault configs for all fields at any depth within the FormPanel.</p>\n\n<h4>BasicForm</h4>\n\n<p>While BasicForm was most often limited to internal use by FormPanel in Ext 3,\nit has been refactored in Ext 4 to make it more flexible and more easily\nextended. It no longer requires its associated container to provide a <code>&lt;form&gt;</code>\nelement around all the fields, and does not require its container to manually\nnotify the BasicForm when adding or removing fields. It is therefore now\ncompletely decoupled from FormPanel, and can potentially be used to manage\nform fields within any Container.</p>\n\n<h3>Grid</h3>\n\n<p>The grid components have been rewritten from the ground up for Ext 4. While\nthis was for good reason and the benefits will be well worth it, unfortunately\nthis is one of the areas in Ext 4 where full backwards compatibility could not\nbe practically maintained. A complete migration guide will be provided to\nassist with the transition from Ext 3 to 4 grids.</p>\n\n<h4>Intelligent Rendering</h4>\n\n<p>Ext 3's grid works fantastically well, but takes the \"least common\ndenominator\" approach to its rich feature support by always generating the\nfull markup needed by every grid feature (which is overly-heavy in most\ncases). Ext 4 takes a much more intelligent approach to this problem. The\ndefault basic grid has very lightweight markup, and only as developers enable\ndifferent features will the additional feature-specific markup be rendered.\nThis is a huge boost both for page rendering speed and overall grid\nperformance.</p>\n\n<h4>Standardized Layout</h4>\n\n<p>Along with a smarter rendering pipeline, many portions of the new grid have\nbeen made into proper Components and integrated into the standard layout\nmanagement system rather than relying on custom internal markup and CSS. This\nenables us to unify grid's rendering process with the rest of the framework,\nwhile still retaining a pixel-perfect UI experience.</p>\n\n<p>One useful example of this is the new HeaderContainer class. In Ext 3 column\nheaders were baked into the grid and not very customizable. In Ext 4 the\ncolumn headers are true Containers and now use a standard HBox layout,\nallowing you to do things like provide custom flex values per column.</p>\n\n<h4>Feature Support</h4>\n\n<p>In Ext 3, it was easy to add new functionality to grids, but there was no\nsingle strategy for doing so. Many added features were provided as plugins,\nbut then some were provided via subclassing. This made it very difficult (if\nnot impossible) to combine certain features easily.</p>\n\n<p>Ext 4 includes a new grid base class called Ext.grid.Feature which provides\nthe basis for creating extremely flexible optional grid features. The\nunderlying templates can now be modified by any Feature classes in order to\ndecorate or mutate the markup that the GridView generates. Features provide a\npowerful alternative to subclassing the old GridView because it makes it easy\nto mix and match compatible features.</p>\n\n<p>Some examples of the features now available in the Ext 4 grid are:</p>\n\n<ul>\n<li>RowWrap</li>\n<li>RowBody</li>\n<li>Grouping</li>\n<li>Chunking/Buffering</li>\n</ul>\n\n\n<h4>Virtual Scrolling</h4>\n\n<p>The Ext 4 grid now natively supports buffering its data during rendering,\nproviding a virtual, load-on-demand view of its data. Grids will now easily\nsupport hundreds or even thousands of records without paging, which will be a\nmassive improvement over Ext 3's grid capabilities.</p>\n\n<h4>Editing Improvements</h4>\n\n<p>In Ext 3 you had to use the specialized EditorGrid class to provide an\neditable grid, which limited its flexibility. In Ext 4 there is now an Editing\nplugin that can be applied to any grid instance, making it completely reusable\nacross all grids.</p>\n\n<p>As part of the editing improvements, the popular RowEditor extension from Ext\n3 has been promoted to a first-class and fully-supported framework component\nin Ext 4.</p>\n\n<h4>DataView</h4>\n\n<p>The new GridView in Ext 4 extends the standard DataView class now. This not\nonly minimizes duplicated code internally, it also makes the new grid even\neasier to customize. Because it extends DataView the new grid is also able to\nleverage the same selection models as any view, including discontinguous\nselection via keyboard navigation. The Home, End, PageDown and PageUp keys are\nalso fully supported.</p>\n\n<h3>Panel</h3>\n\n<h4>Docking Support</h4>\n\n<p>Panel now uses a panel-specific ComponentLayout class to manage a set of items\ndocked to its borders. The Panel's <code>body</code> element is then sized to occupy any\nremaining space. Any components may be docked to any side of a panel via the\nnew dockedItems config property, and docked items must be configured with a\n<code>dock</code> property to specify which border to dock to. This allows for amazingly\nflexible Panel layouts now -- things that were impossible in Ext 3 like\nvertically-docked side toolbars are now a breeze to implement in Ext 4.</p>\n\n<h4>Header Improvements</h4>\n\n<p>Header is now a first-class Container subclass, inheriting capabilities like\nchild component management and layout. Headers may also be configured with a\nheaderPosition of 'top', 'right', 'bottom' or 'left' via the new docking\nsupport.</p>\n\n<p>Tools (the small header buttons that perform actions like minimize, close,\netc.) are also now proper Components making them even more flexible than they\nwere in Ext 3.</p>\n\n<h3>Resizer</h3>\n\n<p>Ext has had handle-based resizing built-in since the earliest days of the\nframework, but through Ext 3 it was limited to resizing DOM elements only. Now\nin Ext 4 any Component can be resized via the new <a href=\"#!/api/Ext.resizer.Resizer\" rel=\"Ext.resizer.Resizer\" class=\"docClass\">Ext.resizer.Resizer</a> and its\nassociated components. This is mainly useful when applied to floating\ncomponents, or components programmatically rendered outside of Ext's Container\nsystem.</p>\n\n<p>By configuring a component with resizable:true, resize handles are\nautomatically added to the Component's edges. By default a proxy element is\nresized using the mouse, and the Component is resized on mouse up. Behavior of\nthe resizer can be modified by specifying the <code>resizable</code> property as a config\nobject for the Resizer class.</p>\n\n<h3>ComponentDragger</h3>\n\n<p>Ext has always had extensive drag-and-drop support, but primarily at the DOM\nelement level. In Ext 4, any Component can now easily be made draggable as\nwell via the new <a href=\"#!/api/Ext.util.ComponentDragger\" rel=\"Ext.util.ComponentDragger\" class=\"docClass\">Ext.util.ComponentDragger</a> class. This is mainly useful for\nfloating components, or components programmatically rendered outside of Ext's\nContainer system.</p>\n\n<p>By configuring a component with draggable:true, it automatically becomes\ndraggable with the mouse. Panels that are made draggable by this method\n(Windows configure themselves as draggable by default) show an empty ghost\nduring the drag operation and the Panel or Window is shown in the new position\non mouse up. Behavior of the dragger can be modified by specifying the\ndraggable property as a config object for the ComponentDragger class.</p>\n\n<h3>Splitter</h3>\n\n<p>Both the HBox and VBox layouts may contain <a href=\"#!/api/Ext.resizer.Splitter\" rel=\"Ext.resizer.Splitter\" class=\"docClass\">Ext.resizer.Splitter</a> components\nwhich are used to manage the size of their adjacent siblings. Minimum and\nmaximum size settings are supported. By default, resizing a flexed item of a\nbox layout sets its size in pixels and deletes the flex value. <strong>One</strong> of the\nsplitter's two adjacent siblings may be configured with <code>maintainFlex:true</code> to\npreserve its flex value after the resize operation.</p>\n\n<h3>TabPanel</h3>\n\n<p>As with many other components in Ext 4, the main pieces that make up a\nTabPanel have now been made into first-class Components in Ext 4. The Tab\nitself, just a DOM element in Ext 3, is now a Button subclass. The TabBar that\ncontains the tabs is now a Container. These changes provide much more\nflexibility over Ext 3.</p>\n\n<p>Because tabs are now separate components from the contained child panels that\nhold the tab content, individual tab panels can now optionally show their own\ntitle header bars separately from their tab titles. This was impossible in Ext\n3.</p>\n\n<h3>Toolbar</h3>\n\n<p>Toolbar is now a first-class Container, which will make adding new components\nand customizing its layout much easier than in Ext 3.</p>\n\n<h2 id='upgrade-section-4'>Theming</h2>\n\n<p>The theming in Ext has always been amazing, but not as easy to customize as it\ncould be. Ext 3 made strides in this area by separating structural and visual\nstylesheets, but there is still quite a bit of duplication, browser-specific\noverride rules, and many other issues that are simply weaknesses of the CSS\nlanguage itself.</p>\n\n<h4>Resources</h4>\n\n<ul>\n<li><a href=\"http://vimeo.com/19159630\">Ext 4 Theming (video)</a></li>\n<li><a href=\"http://www.slideshare.net/senchainc/slides-5971886\">Ext 4 Theming (slides)</a></li>\n</ul>\n\n\n<h3>Compass &amp; SASS</h3>\n\n<p>For Ext 4 we've completely retooled the theming system, switching to\n<a href=\"http://compass-style.org/\" title=\"Compass\">Compass</a> and <a href=\"http://sass-lang.com/\" title=\"Sass: Syntactically Awesome Stylesheets\">SASS</a> as the primary CSS authoring tools internally.\nSASS is a superset of standard CSS that adds support for many advanced\nfeatures:</p>\n\n<ul>\n<li>Nested selectors</li>\n<li>Variables</li>\n<li>Mixins</li>\n<li>Selector inheritance</li>\n<li>Compiled and easily compressed</li>\n<li>Ability to completely remove CSS for components not needed by your specific application</li>\n</ul>\n\n\n<h3>Markup Changes</h3>\n\n<p>Ext now supports browser/version-specific markup for most components, which is\na great advance from Ext 3.</p>\n\n<h2 id='upgrade-section-5'>Documentation</h2>\n\n<ul>\n<li>Support for new class system constructs (requires, mixins, etc.)</li>\n<li>Support for @deprected members</li>\n<li>History navigation support</li>\n<li>Embedded examples, videos, etc.</li>\n<li>Favorite topics</li>\n<li>Multiple framework / version support</li>\n<li>Local search</li>\n</ul>\n\n"});