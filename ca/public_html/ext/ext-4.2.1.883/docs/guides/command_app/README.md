# Using Sencha Cmd with Ext JS 4.2+

{@img ../command/sencha-command-128.png}

This guide walks through the process of using Sencha Cmd with Ext JS 4.2 applications
starting with the `sencha generate app` command and ending with a running application.

This guide applies mostly to *new* single-page Ext JS applications. If you have an *existing*
single-page application you might consider using Sencha Cmd to build an application
skeleton based on this guide and then migrate your application code to this preferred
structure. This will provide you with the maximum leverage from Sencha Cmd. If this option
is not right for your app, you can still use Sencha Cmd to help with many aspects of your
development. For developing single-page applications with a custom folder structure, see
[Single-Page Ext JS Apps](#/guide/command_app_single).

If you work with applications that have multiple pages, it will be helpful to start by
learning about the simple uses of Sencha Cmd described in this and the
[Single-Page Ext JS Apps](#/guide/command_app_single) guide. For details on developing
more complex, multipage Ext JS applications, refer to
[Multi-Page and Mixed Apps](#/guide/command_app_multi).

## Generating Your Application

Our starting point is to generate an application skeleton. This is done using
the following command:

    sencha generate app MyApp /path/to/MyApp

**Important.** The above command must be able to determine the appropriate SDK. This can
be satisfied by either executing this command from a folder containing an extracted SDK
distribution or by using the `-sdk` switch like so:

    sencha -sdk /path/to/SDK generate app MyApp /path/to/MyApp

The application files generated by this command should have the following structure:

    .sencha/                    # Sencha-specific files (e.g. configuration)
        app/                    # Application-specific content
            sencha.cfg          # Application configuration file for Sencha Cmd
            build-impl.xml      # Standard application build script
            plugin.xml          # Application-level plugin for Sencha Cmd
        workspace/              # Workspace-specific content (see below)
            sencha.cfg          # Workspace configuration file for Sencha Cmd
            plugin.xml          # Workspace-level plugin for Sencha Cmd

    ext/                        # A copy of the Ext JS SDK
        cmd/                    # Framework-specific content for Sencha Cmd
            sencha.cfg          # Framework configuration file for Sencha Cmd
        packages/               # Framework supplied packages
            ext-theme-classic/  # Ext JS Theme Package for Classic
            ext-theme-neptune/  # Ext JS Theme Package for Neptune
            ...                 # Other theme and locale packages
        src/                    # The Ext JS source
        ext-*.js                # Pre-compiled and bootstrap files
        ...

    index.html                  # The entry point to your application
    app.json                    # Application configuration
    app.js                      # Your application's initialization logic
    app/                        # Your application's source code in MVC structure
        model/                  # Folder for application model classes.
        store/                  # Folder for application stores
        view/                   # Folder for application view classes.
            Main.js             # The initial default View
        controller/             # Folder for application controllers.
            Main.js             # The initial default Controller

    packages/                   # Sencha Cmd packages

    build/                      # The folder where build output is placed.

There is no distinction between workspace and app content in a single-page application.
This distinction becomes important for multi-page applications as described in
[Multi-page and Mixed Apps](#/guide/command_app_multi).

## Building Your Application

All that is required to build your application is to run the following command:

    sencha app build

**Important.** In order to execute this command, the current directory **must** be the
top-level folder of your application (in this case, `"/path/to/MyApp"`).

This command will build your markup page, JavaScript code, SASS and themes into the `"build"`
folder.

## Extending Your Application

The `sencha generate` command helps you quickly generate common MVC components such as
controllers or models:

    sencha help generate

You should see this:

    Sencha Cmd v3.1.0.xxx
    sencha generate

    This category contains code generators used to generate applications as well
    as add new classes to the application.

    Commands
      * app - Generates a starter application
      * controller - Generates a Controller for the current application
      * form - Generates a Form for the current application (Sencha Touch Specific)
      * model - Generates a Model for the current application
      * package - Generates a starter package
      * profile - Generates a Profile for the current application (Sencha Touch Specific)
      * theme - Generates a theme page for slice operations (Ext JS Specific)
      * view - Generates a View for the current application (Ext JS Specific)
      * workspace - Initializes a multi-app workspace

**Important.** In order to execute the commands discussed below, the current directory on
the console **must** be inside your application (in this case, "/path/to/MyApp").

### Adding New Models

Adding a model to your application is done by making the `"/path/to/MyApp"` your current
directory and running Sencha Cmd, like this:

    cd /path/to/MyApp
    sencha generate model User id:int,name,email

This command adds a model class called `User` with the given 3 fields.

### Adding New Controllers

Adding a controller is similar to adding a model:

    cd /path/to/MyApp
    sencha generate controller Central

There are no other parameters in this case beyond the controller name.

### Adding New Views

Adding a view to your application is also similar:

    cd /path/to/MyApp
    sencha generate view SomeView

There are no other parameters in this case beyond the view name.

## Customizing The Build

There are various configuration options available in the `".sencha/app/sencha.cfg"` file. In
the case of a single-page application, it is best to ignore the `".sencha/workspace"`
folder, which also has a config file.

When configuration options cannot accomplish the task, the next level of customization is
to extend the generated "build.xml" [Ant](http://ant.apache.org/) script. All that the
`sencha app build` command does inside Sencha Cmd itself is some basic validations before
calling in to the `"build"` target of this build script. This means the entire build process
can be examined, extended and if necessary, even modified.

### The classpath

The `sencha app build` command knows where to find the source of your application due to
the `app.classpath` configuration value stored in `".sencha/app/sencha.cfg"`. By default,
this value is:

    app.classpath=${app.dir}/app,${app.dir}/app.js

Adding directories to this comma-separated list informs the compiler where to find the
source code required to build the application.

### Build Properties

Many aspects of the build performed by `sencha app build` are controlled by properties. To
see the current values of these you can run this command from your app folder:

    sencha ant .props

To provide fine-grained control of your application, many different properties are used. In
most cases you can tell where this property comes from by its prefix:

  * `app.`  -- Check the `".sencha/app/sencha.cfg"` file
  * `build.`  -- Check the `".sencha/app/build.properties"` file
  * `workspace.` -- Check the `".sencha/workspace/sencha.cfg"` file
  * `framework.` -- Check the `"cmd/sencha.cfg"` file in the Ext JS SDK.
  * `cmd.` -- Check the Sencha Cmd install folder's `"sencha.cfg"` file.

It is not required that these properties be set in these files, but it is the default and
following this convention will help you manage these settings. That said, there are times
when a Workspace setting may need to be overridden by an application. To do this, the
`workspace.foo` property must be set in `".sencha/app/sencha.cfg"` because that is the
application-level configuration file. When deviating from the convention, leaving behind
a comment would be advised.

### Ant Extension Points

The generated `"build.xml"` [Ant](http://ant.apache.org/) script is a minimal Ant script that
uses an Ant `import` task to import `".sencha/app/build-impl.xml"`. The `"build.xml"` file
is intended to be edited after it is generated. The `"build-impl.xml"` file, however, will
be replaced by the `sencha app upgrade` command described below and is best left alone.

In addition to the `import` task, `"build.xml"` contains a comment block describing all of
the various extension points it provides. These are in the form of optional Ant targets and
are typically named after their build process step but with prefixes of `"-before-"` and
`"-after-"`. In other words, the `"sass"` build step is wrapped by targets that will be invoked
before and after the `"sass"` target named `"-before-sass"` and `"-after-sass"`.

To perform additional processing before or after any build step, add an appropriately named
target to `"build.xml"`. These targets will be invoked by `sencha app build`. These will also
be invoked if you use Ant to directly invoke a particular target.

For example, build properties are loaded by the `init` target. This means that they can be
overridden by setting them prior to that time (since this follows the Ant convention of
first-write-wins). The most common place to do this is in the "-before-init" target of the
application's `"build.xml"`:

    <target name="-before-init">
        <property name="foo" value="42"/>
    </target>

**Note.** Because `sencha app build` simply invokes the `"build"` target of the Ant `"build.xml"`
file, you can equivalently invoke a build directly from Ant. This can be useful in IDE's
like Eclipse and NetBeans for invoking your builds but also in a Continuous Integration
server that understands Ant (which is just about all of them).

## Upgrading Your Application

Generate applications include two basic kinds of content relevant to Sencha Cmd: build
scripts and the important content of the used Sencha SDK's. As such, you will occasionally
need to upgrade these pieces. You can do this with the `sencha app upgrade` command.

### Preparing to Upgrade

When performing any bulk operation on your application source code, it is highly advisable
to start with a "clean" state with respect to version control. That is, it is best to have
no uncommitted changes before performing the upgrade. This way, you can easily review and
possibly discard changes made by `sencha app upgrade` with minimum trouble.

### Upgrading Just The Sencha Cmd Scaffold

To bring up a new version of Sencha Cmd with your application produced by a previous
version, you can run this command from inside your application:

    sencha app upgrade --noframework

This will replace the content of `".sencha"` but will also regenerate `"app.js"` to pick
up changes there. As well as a handful of other files.

### Upgrading Frameworks

Since generated applications include their own copies of the SDK from which they were
originally generated, applications need to be updated to use a new version of the SDK.
The `sencha app upgrade` command will replace the old SDK copy with the new one:

    sencha app upgrade ../downloads/ext-4.2.0

The above command points to the path to a downloaded and extracted SDK.

**Important.** Do not use the `-sdk` switch for this command as you would for the
`sencha generate app` command. Instead use the command shown above.

### Dealing With Merge Conflicts

In Sencha Cmd v3.1, the code generator incorporates a 3-way merge to best reconcile the
code it generates with the code it generated last time and the current state of the code
as you may have edited it. This approach allows you to edit files (like `"spp.js"`) in
many ways so long as your changes don't overlap those that Sencha Cmd wants to make.

The merge process follows this pseudo-code for `"app.js"` (as an example):

    mv app.js app.js.$old
    regenerate last version to app.js.$base
    generate new version to app.js
    diff3 app.js.$base app.js.$old app.js

In the ideal scenario, you won't notice this mechanism at work. There are situations,
however, in which you may receive an error message telling you there was a "Merge conflict"
and that you need to resolve this manually.

In cases where the base version cannot be recreated, the `".$old"` file is left on disk
and you can compare it to the current version. Or you can use your source control system
to compare the current file to what was last committed.

When the base version could be produced (the majority case), the `".$old"` file is deleted
because the conflicts are annotated in the target file in the standard way:

    <<<<<<< Generated
        // stuff that Sencha Cmd thinks belongs here
    =======
        // stuff that you have changed which conflicts
    >>>>>>> Custom

### Alternative Strategies

If you have heavily customized your application, it is sometimes simpler to just generate
a new application in a temporary location and start by copying its `".sencha"` folder to
replace your own.

If you are using a workspace, you may need to copy the `".sencha/workspace"` folder from
the generated app to your workspace's `".sencha"` folder to replace the old version there.

`NOTE`: be careful to preserve any edits to `"sencha.cfg"` files you may still need.
