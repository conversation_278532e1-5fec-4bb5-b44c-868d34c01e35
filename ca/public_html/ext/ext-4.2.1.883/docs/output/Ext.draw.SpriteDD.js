Ext.data.JsonP.Ext_draw_SpriteDD({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":"Ext.dd.DragSource","uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='docClass'>Ext.dd.DragDrop</a><div class='subclass '><a href='#!/api/Ext.dd.DD' rel='Ext.dd.DD' class='docClass'>Ext.dd.DD</a><div class='subclass '><a href='#!/api/Ext.dd.DDProxy' rel='Ext.dd.DDProxy' class='docClass'>Ext.dd.DDProxy</a><div class='subclass '><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='docClass'>Ext.dd.DragSource</a><div class='subclass '><strong>Ext.draw.SpriteDD</strong></div></div></div></div></div></div><h4>Files</h4><div class='dependency'><a href='source/SpriteDD.html#Ext-draw-SpriteDD' target='_blank'>SpriteDD.js</a></div></pre><div class='doc-contents'><p class='private'><strong>NOTE</strong> This is a private utility class for internal use by the framework. Don't rely on its existence.</p><p>DD implementation for Panels.</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-cfg'>Config options</h3><div class='subsection'><div id='cfg-animRepair' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-cfg-animRepair' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-cfg-animRepair' class='name expandable'>animRepair</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>If true, animates the proxy element back to the position of the handle element used to trigger the drag. ...</div><div class='long'><p>If true, animates the proxy element back to the position of the handle element used to trigger the drag.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-ddGroup' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-cfg-ddGroup' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-cfg-ddGroup' class='name expandable'>ddGroup</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>A named drag drop group to which this object belongs. ...</div><div class='long'><p>A named drag drop group to which this object belongs.  If a group is specified, then this object will only\ninteract with other drag drop objects in the same group.</p>\n</div></div></div><div id='cfg-dropAllowed' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-cfg-dropAllowed' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-cfg-dropAllowed' class='name expandable'>dropAllowed</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The CSS class returned to the drag source when drop is allowed. ...</div><div class='long'><p>The CSS class returned to the drag source when drop is allowed.</p>\n<p>Defaults to: <code>Ext.baseCSSPrefix + 'dd-drop-ok'</code></p></div></div></div><div id='cfg-dropNotAllowed' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-cfg-dropNotAllowed' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-cfg-dropNotAllowed' class='name expandable'>dropNotAllowed</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The CSS class returned to the drag source when drop is not allowed. ...</div><div class='long'><p>The CSS class returned to the drag source when drop is not allowed.</p>\n<p>Defaults to: <code>Ext.baseCSSPrefix + 'dd-drop-nodrop'</code></p></div></div></div><div id='cfg-repairHighlightColor' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-cfg-repairHighlightColor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-cfg-repairHighlightColor' class='name expandable'>repairHighlightColor</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The color to use when visually highlighting the drag source in the afterRepair\nmethod after a failed drop (defaults t...</div><div class='long'><p>The color to use when visually highlighting the drag source in the afterRepair\nmethod after a failed drop (defaults to light blue). The color must be a 6 digit hex value, without\na preceding '#'.</p>\n<p>Defaults to: <code>'c3daf9'</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-__ygDragDrop' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-__ygDragDrop' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-__ygDragDrop' class='name expandable'>__ygDragDrop</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Internal typeof flag ...</div><div class='long'><p>Internal typeof flag</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-_domRef' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-_domRef' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-_domRef' class='name not-expandable'>_domRef</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'><p>Cached reference to the linked element</p>\n</div><div class='long'><p>Cached reference to the linked element</p>\n</div></div></div><div id='property-available' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-available' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-available' class='name expandable'>available</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>The available property is false until the linked dom element is accessible. ...</div><div class='long'><p>The available property is false until the linked dom element is accessible.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-centerFrame' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DDProxy' rel='Ext.dd.DDProxy' class='defined-in docClass'>Ext.dd.DDProxy</a><br/><a href='source/DDProxy.html#Ext-dd-DDProxy-property-centerFrame' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DDProxy-property-centerFrame' class='name expandable'>centerFrame</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>By default the frame is positioned exactly where the drag element is, so\nwe use the cursor offset provided by Ext.dd.DD. ...</div><div class='long'><p>By default the frame is positioned exactly where the drag element is, so\nwe use the cursor offset provided by <a href=\"#!/api/Ext.dd.DD\" rel=\"Ext.dd.DD\" class=\"docClass\">Ext.dd.DD</a>.  Another option that works only if\nyou do not have constraints on the obj is to have the drag frame centered\naround the cursor.  Set centerFrame to true for this effect.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-config' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-config' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-config' class='name not-expandable'>config</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'><p>Configuration attributes passed into the constructor</p>\n</div><div class='long'><p>Configuration attributes passed into the constructor</p>\n</div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-constrainX' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-constrainX' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-constrainX' class='name expandable'>constrainX</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Set to true when horizontal contraints are applied ...</div><div class='long'><p>Set to true when horizontal contraints are applied</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-constrainY' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-constrainY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-constrainY' class='name expandable'>constrainY</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Set to true when vertical contraints are applied ...</div><div class='long'><p>Set to true when vertical contraints are applied</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-defaultPadding' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-defaultPadding' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-defaultPadding' class='name expandable'>defaultPadding</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>Provides default constraint padding to \"constrainTo\" elements. ...</div><div class='long'><p>Provides default constraint padding to \"constrainTo\" elements.</p>\n<p>Defaults to: <code>{left: 0, right: 0, top: 0, bottom: 0}</code></p></div></div></div><div id='property-dragData' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-property-dragData' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-property-dragData' class='name expandable'>dragData</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>This property contains the data representing the dragged object. ...</div><div class='long'><p>This property contains the data representing the dragged object. This data is set up by the implementation of the\n<a href=\"#!/api/Ext.dd.DragSource-method-getDragData\" rel=\"Ext.dd.DragSource-method-getDragData\" class=\"docClass\">getDragData</a> method. It must contain a ddel property, but can contain any other data according to the\napplication's needs.</p>\n</div></div></div><div id='property-dragElId' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-dragElId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-dragElId' class='name expandable'>dragElId</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>The id of the element that will be dragged. ...</div><div class='long'><p>The id of the element that will be dragged.  By default this is same\nas the linked element, but could be changed to another element. Ex:\n<a href=\"#!/api/Ext.dd.DDProxy\" rel=\"Ext.dd.DDProxy\" class=\"docClass\">Ext.dd.DDProxy</a></p>\n</div></div></div><div id='property-groups' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-groups' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-groups' class='name expandable'>groups</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>The group defines a logical collection of DragDrop objects that are\nrelated. ...</div><div class='long'><p>The group defines a logical collection of DragDrop objects that are\nrelated.  Instances only get events when interacting with other\nDragDrop object in the same group.  This lets us define multiple\ngroups using a single DragDrop subclass if we want.</p>\n\n<p>An object in the format {'group1':true, 'group2':true}</p>\n</div></div></div><div id='property-handleElId' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-handleElId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-handleElId' class='name expandable'>handleElId</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>The ID of the element that initiates the drag operation. ...</div><div class='long'><p>The ID of the element that initiates the drag operation.  By default\nthis is the linked element, but could be changed to be a child of this\nelement.  This lets us do things like only starting the drag when the\nheader element within the linked html element is clicked.</p>\n</div></div></div><div id='property-hasOuterHandles' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-hasOuterHandles' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-hasOuterHandles' class='name expandable'>hasOuterHandles</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>By default, drags can only be initiated if the mousedown occurs in the\nregion the linked element is. ...</div><div class='long'><p>By default, drags can only be initiated if the mousedown occurs in the\nregion the linked element is.  This is done in part to work around a\nbug in some browsers that mis-report the mousedown if the previous\nmouseup happened outside of the window.  This property is set to true\nif outer handles are defined. Defaults to false.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-id' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-id' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-id' class='name expandable'>id</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The id of the element associated with this object. ...</div><div class='long'><p>The id of the element associated with this object.  This is what we\nrefer to as the \"linked element\" because the size and position of\nthis element is used to determine when the drag and drop objects have\ninteracted.</p>\n</div></div></div><div id='property-ignoreSelf' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-ignoreSelf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-ignoreSelf' class='name expandable'>ignoreSelf</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Set to false to enable a DragDrop object to fire drag events while dragging\nover its own Element. ...</div><div class='long'><p>Set to false to enable a DragDrop object to fire drag events while dragging\nover its own Element. Defaults to true - DragDrop objects do not by default\nfire drag events to themselves.</p>\n</div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-invalidHandleClasses' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-invalidHandleClasses' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-invalidHandleClasses' class='name not-expandable'>invalidHandleClasses</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</span></div><div class='description'><div class='short'><p>An Array of CSS class names for elements to be considered in valid as drag handles.</p>\n</div><div class='long'><p>An Array of CSS class names for elements to be considered in valid as drag handles.</p>\n</div></div></div><div id='property-invalidHandleIds' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-invalidHandleIds' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-invalidHandleIds' class='name expandable'>invalidHandleIds</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>An object who's property names identify the IDs of elements to be considered invalid as drag handles. ...</div><div class='long'><p>An object who's property names identify the IDs of elements to be considered invalid as drag handles.\nA non-null property value identifies the ID as invalid. For example, to prevent\ndragging from being initiated on element ID \"foo\", use:</p>\n\n<pre><code>{\n    foo: true\n}\n</code></pre>\n</div></div></div><div id='property-invalidHandleTypes' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-invalidHandleTypes' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-invalidHandleTypes' class='name expandable'>invalidHandleTypes</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>An object who's property names identify HTML tags to be considered invalid as drag handles. ...</div><div class='long'><p>An object who's property names identify HTML tags to be considered invalid as drag handles.\nA non-null property value identifies the tag as invalid. Defaults to the\nfollowing value which prevents drag operations from being initiated by <code>&lt;a&gt;</code> elements:</p>\n\n<pre><code>{\n    A: \"A\"\n}\n</code></pre>\n</div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isTarget' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-isTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-isTarget' class='name expandable'>isTarget</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>By default, all instances can be a drop target. ...</div><div class='long'><p>By default, all instances can be a drop target.  This can be disabled by\nsetting isTarget to false.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-locked' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-locked' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-locked' class='name expandable'>locked</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Individual drag/drop instances can be locked. ...</div><div class='long'><p>Individual drag/drop instances can be locked.  This will prevent\nonmousedown start drag.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-maintainOffset' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-maintainOffset' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-maintainOffset' class='name expandable'>maintainOffset</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Maintain offsets when we resetconstraints. ...</div><div class='long'><p>Maintain offsets when we resetconstraints.  Set to true when you want\nthe position of the element relative to its parent to stay the same\nwhen the page changes</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-maxX' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-maxX' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-maxX' class='name expandable'>maxX</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>The right constraint ...</div><div class='long'><p>The right constraint</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-maxY' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-maxY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-maxY' class='name expandable'>maxY</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>The down constraint ...</div><div class='long'><p>The down constraint</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-minX' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-minX' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-minX' class='name expandable'>minX</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>The left constraint ...</div><div class='long'><p>The left constraint</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-minY' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-minY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-minY' class='name expandable'>minY</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>The up constraint ...</div><div class='long'><p>The up constraint</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-moveOnly' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-moveOnly' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-moveOnly' class='name expandable'>moveOnly</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>When set to true, other DD objects in cooperating DDGroups do not receive\nnotification events when this DD object is ...</div><div class='long'><p>When set to true, other DD objects in cooperating DDGroups do not receive\nnotification events when this DD object is dragged over them.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-padding' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-padding' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-padding' class='name expandable'>padding</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</span></div><div class='description'><div class='short'>The padding configured for this drag and drop object for calculating\nthe drop zone intersection with this object. ...</div><div class='long'><p>The padding configured for this drag and drop object for calculating\nthe drop zone intersection with this object.\nAn array containing the 4 padding values: [top, right, bottom, left]</p>\n</div></div></div><div id='property-primaryButtonOnly' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-primaryButtonOnly' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-primaryButtonOnly' class='name expandable'>primaryButtonOnly</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>By default the drag and drop instance will only respond to the primary\nbutton click (left button for a right-handed m...</div><div class='long'><p>By default the drag and drop instance will only respond to the primary\nbutton click (left button for a right-handed mouse).  Set to true to\nallow drag and drop to start with any mouse click that is propogated\nby the browser</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-resizeFrame' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DDProxy' rel='Ext.dd.DDProxy' class='defined-in docClass'>Ext.dd.DDProxy</a><br/><a href='source/DDProxy.html#Ext-dd-DDProxy-property-resizeFrame' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DDProxy-property-resizeFrame' class='name expandable'>resizeFrame</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>By default we resize the drag frame to be the same size as the element\nwe want to drag (this is to get the frame effe...</div><div class='long'><p>By default we resize the drag frame to be the same size as the element\nwe want to drag (this is to get the frame effect).  We can turn it off\nif we want a different behavior.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-scroll' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DD' rel='Ext.dd.DD' class='defined-in docClass'>Ext.dd.DD</a><br/><a href='source/DD.html#Ext-dd-DD-property-scroll' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DD-property-scroll' class='name expandable'>scroll</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>When set to true, the utility automatically tries to scroll the browser\nwindow when a drag and drop element is dragge...</div><div class='long'><p>When set to true, the utility automatically tries to scroll the browser\nwindow when a drag and drop element is dragged near the viewport boundary.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div><div id='property-startPageX' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-startPageX' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-startPageX' class='name expandable'>startPageX</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>The linked element's absolute X position at the time the drag was\nstarted ...</div><div class='long'><p>The linked element's absolute X position at the time the drag was\nstarted</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-startPageY' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-startPageY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-startPageY' class='name expandable'>startPageY</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>The linked element's absolute X position at the time the drag was\nstarted ...</div><div class='long'><p>The linked element's absolute X position at the time the drag was\nstarted</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-xTicks' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-xTicks' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-xTicks' class='name expandable'>xTicks</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</span></div><div class='description'><div class='short'>Array of pixel locations the element will snap to if we specified a\nhorizontal graduation/interval. ...</div><div class='long'><p>Array of pixel locations the element will snap to if we specified a\nhorizontal graduation/interval.  This array is generated automatically\nwhen you define a tick interval.</p>\n</div></div></div><div id='property-yTicks' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-property-yTicks' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-property-yTicks' class='name expandable'>yTicks</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</span></div><div class='description'><div class='short'>Array of pixel locations the element will snap to if we specified a\nvertical graduation/interval. ...</div><div class='long'><p>Array of pixel locations the element will snap to if we specified a\nvertical graduation/interval.  This array is generated automatically\nwhen you define a tick interval.</p>\n</div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.draw.SpriteDD'>Ext.draw.SpriteDD</span><br/><a href='source/SpriteDD.html#Ext-draw-SpriteDD-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/Ext.draw.SpriteDD-method-constructor' class='name expandable'>Ext.draw.SpriteDD</a>( <span class='pre'>el, [config]</span> ) : <a href=\"#!/api/Ext.draw.SpriteDD\" rel=\"Ext.draw.SpriteDD\" class=\"docClass\">Ext.draw.SpriteDD</a></div><div class='description'><div class='short'>Creates new drag-source. ...</div><div class='long'><p>Creates new drag-source.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The container element or ID of it.</p>\n</div></li><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Config object.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.draw.SpriteDD\" rel=\"Ext.draw.SpriteDD\" class=\"docClass\">Ext.draw.SpriteDD</a></span><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DragSource-method-constructor' rel='Ext.dd.DragSource-method-constructor' class='docClass'>Ext.dd.DragSource.constructor</a></p></div></div></div><div id='method-_resizeProxy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DDProxy' rel='Ext.dd.DDProxy' class='defined-in docClass'>Ext.dd.DDProxy</a><br/><a href='source/DDProxy.html#Ext-dd-DDProxy-method-_resizeProxy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DDProxy-method-_resizeProxy' class='name expandable'>_resizeProxy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>The proxy is automatically resized to the dimensions of the linked\nelement when a drag is initiated, unless resizeFra...</div><div class='long'><p>The proxy is automatically resized to the dimensions of the linked\nelement when a drag is initiated, unless resizeFrame is set to false</p>\n</div></div></div><div id='method-addInvalidHandleClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-addInvalidHandleClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-addInvalidHandleClass' class='name expandable'>addInvalidHandleClass</a>( <span class='pre'>cssClass</span> )</div><div class='description'><div class='short'>Lets you specify a css class of elements that will not initiate a drag ...</div><div class='long'><p>Lets you specify a css class of elements that will not initiate a drag</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>cssClass</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the class of the elements you wish to ignore</p>\n</div></li></ul></div></div></div><div id='method-addInvalidHandleId' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-addInvalidHandleId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-addInvalidHandleId' class='name expandable'>addInvalidHandleId</a>( <span class='pre'>id</span> )</div><div class='description'><div class='short'>Lets you to specify an element id for a child of a drag handle\nthat should not initiate a drag ...</div><div class='long'><p>Lets you to specify an element id for a child of a drag handle\nthat should not initiate a drag</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the element id of the element you wish to ignore</p>\n</div></li></ul></div></div></div><div id='method-addInvalidHandleType' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-addInvalidHandleType' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-addInvalidHandleType' class='name expandable'>addInvalidHandleType</a>( <span class='pre'>tagName</span> )</div><div class='description'><div class='short'>Allows you to specify a tag name that should not start a drag operation\nwhen clicked. ...</div><div class='long'><p>Allows you to specify a tag name that should not start a drag operation\nwhen clicked.  This is designed to facilitate embedding links within a\ndrag handle that do something other than start the drag.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>tagName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the type of element to exclude</p>\n</div></li></ul></div></div></div><div id='method-addToGroup' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-addToGroup' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-addToGroup' class='name expandable'>addToGroup</a>( <span class='pre'>sGroup</span> )</div><div class='description'><div class='short'>Adds this instance to a group of related drag/drop objects. ...</div><div class='long'><p>Adds this instance to a group of related drag/drop objects.  All\ninstances belong to at least one group, and can belong to as many\ngroups as needed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>sGroup</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the name of the group</p>\n</div></li></ul></div></div></div><div id='method-afterDrag' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DDProxy' rel='Ext.dd.DDProxy' class='defined-in docClass'>Ext.dd.DDProxy</a><br/><a href='source/DDProxy.html#Ext-dd-DDProxy-method-afterDrag' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DDProxy-method-afterDrag' class='name expandable'>afterDrag</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-afterDragDrop' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-afterDragDrop' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-afterDragDrop' class='name expandable'>afterDragDrop</a>( <span class='pre'>target, e, id</span> )</div><div class='description'><div class='short'>An empty function by default, but provided so that you can perform a custom action\nafter a valid drag drop has occurr...</div><div class='long'><p>An empty function by default, but provided so that you can perform a custom action\nafter a valid drag drop has occurred by providing an implementation.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a><div class='sub-desc'><p>The drop target</p>\n</div></li><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>The event object</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The id of the dropped element</p>\n</div></li></ul></div></div></div><div id='method-afterDragEnter' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-afterDragEnter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-afterDragEnter' class='name expandable'>afterDragEnter</a>( <span class='pre'>target, e, id</span> )</div><div class='description'><div class='short'>An empty function by default, but provided so that you can perform a custom action\nwhen the dragged item enters the d...</div><div class='long'><p>An empty function by default, but provided so that you can perform a custom action\nwhen the dragged item enters the drop target by providing an implementation.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a><div class='sub-desc'><p>The drop target</p>\n</div></li><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>The event object</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The id of the dragged element</p>\n</div></li></ul></div></div></div><div id='method-afterDragOut' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-afterDragOut' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-afterDragOut' class='name expandable'>afterDragOut</a>( <span class='pre'>target, e, id</span> )</div><div class='description'><div class='short'>An empty function by default, but provided so that you can perform a custom action\nafter the dragged item is dragged ...</div><div class='long'><p>An empty function by default, but provided so that you can perform a custom action\nafter the dragged item is dragged out of the target without dropping.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a><div class='sub-desc'><p>The drop target</p>\n</div></li><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>The event object</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The id of the dragged element</p>\n</div></li></ul></div></div></div><div id='method-afterDragOver' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-afterDragOver' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-afterDragOver' class='name expandable'>afterDragOver</a>( <span class='pre'>target, e, id</span> )</div><div class='description'><div class='short'>An empty function by default, but provided so that you can perform a custom action\nwhile the dragged item is over the...</div><div class='long'><p>An empty function by default, but provided so that you can perform a custom action\nwhile the dragged item is over the drop target by providing an implementation.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a><div class='sub-desc'><p>The drop target</p>\n</div></li><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>The event object</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The id of the dragged element</p>\n</div></li></ul></div></div></div><div id='method-afterInvalidDrop' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-afterInvalidDrop' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-afterInvalidDrop' class='name expandable'>afterInvalidDrop</a>( <span class='pre'>e, id</span> )</div><div class='description'><div class='short'>An empty function by default, but provided so that you can perform a custom action\nafter an invalid drop has occurred...</div><div class='long'><p>An empty function by default, but provided so that you can perform a custom action\nafter an invalid drop has occurred by providing an implementation.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>The event object</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The id of the dropped element</p>\n</div></li></ul></div></div></div><div id='method-afterRepair' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-afterRepair' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-afterRepair' class='name expandable'>afterRepair</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-afterValidDrop' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-afterValidDrop' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-afterValidDrop' class='name expandable'>afterValidDrop</a>( <span class='pre'>target, e, id</span> )</div><div class='description'><div class='short'>An empty function by default, but provided so that you can perform a custom action\nafter a valid drop has occurred by...</div><div class='long'><p>An empty function by default, but provided so that you can perform a custom action\nafter a valid drop has occurred by providing an implementation.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The target DD</p>\n</div></li><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>The event object</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The id of the dropped element</p>\n</div></li></ul></div></div></div><div id='method-alignElWithMouse' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-alignElWithMouse' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-alignElWithMouse' class='name expandable'>alignElWithMouse</a>( <span class='pre'>el, iPageX, iPageY</span> )</div><div class='description'><div class='short'>Sets the element to the location of the mousedown or click event,\nmaintaining the cursor location relative to the loc...</div><div class='long'><p>Sets the element to the location of the mousedown or click event,\nmaintaining the cursor location relative to the location on the element\nthat was clicked.  Override this if you want to place the element in a\nlocation other than where the cursor is.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : HTMLElement<div class='sub-desc'><p>the element to move</p>\n</div></li><li><span class='pre'>iPageX</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the X coordinate of the mousedown or drag event</p>\n</div></li><li><span class='pre'>iPageY</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the Y coordinate of the mousedown or drag event</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DD-method-alignElWithMouse' rel='Ext.dd.DD-method-alignElWithMouse' class='docClass'>Ext.dd.DD.alignElWithMouse</a></p></div></div></div><div id='method-applyConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DDProxy' rel='Ext.dd.DDProxy' class='defined-in docClass'>Ext.dd.DDProxy</a><br/><a href='source/DDProxy.html#Ext-dd-DDProxy-method-applyConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DDProxy-method-applyConfig' class='name expandable'>applyConfig</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Sets up config options specific to this class. ...</div><div class='long'><p>Sets up config options specific to this class. Overrides\n<a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a>, but all versions of this method through the\ninheritance chain are called</p>\n<p>Overrides: <a href='#!/api/Ext.dd.DD-method-applyConfig' rel='Ext.dd.DD-method-applyConfig' class='docClass'>Ext.dd.DD.applyConfig</a></p></div></div></div><div id='method-autoOffset' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-autoOffset' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-autoOffset' class='name expandable'>autoOffset</a>( <span class='pre'>iPageX, iPageY</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Sets the pointer offset to the distance between the linked element's top\nleft corner and the location the element was...</div><div class='long'><p>Sets the pointer offset to the distance between the linked element's top\nleft corner and the location the element was clicked.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>iPageX</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the X coordinate of the click</p>\n</div></li><li><span class='pre'>iPageY</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the Y coordinate of the click</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DD-method-autoOffset' rel='Ext.dd.DD-method-autoOffset' class='docClass'>Ext.dd.DD.autoOffset</a></p></div></div></div><div id='method-autoScroll' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DD' rel='Ext.dd.DD' class='defined-in docClass'>Ext.dd.DD</a><br/><a href='source/DD.html#Ext-dd-DD-method-autoScroll' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DD-method-autoScroll' class='name expandable'>autoScroll</a>( <span class='pre'>x, y, h, w</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Auto-scroll the window if the dragged object has been moved beyond the\nvisible window boundary. ...</div><div class='long'><p>Auto-scroll the window if the dragged object has been moved beyond the\nvisible window boundary.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>x</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the drag element's x position</p>\n</div></li><li><span class='pre'>y</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the drag element's y position</p>\n</div></li><li><span class='pre'>h</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the height of the drag element</p>\n</div></li><li><span class='pre'>w</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the width of the drag element</p>\n</div></li></ul></div></div></div><div id='method-b4Drag' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DD' rel='Ext.dd.DD' class='defined-in docClass'>Ext.dd.DD</a><br/><a href='source/DD.html#Ext-dd-DD-method-b4Drag' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DD-method-b4Drag' class='name expandable'>b4Drag</a>( <span class='pre'>e</span> )</div><div class='description'><div class='short'>Event that fires prior to the onDrag event. ...</div><div class='long'><p>Event that fires prior to the onDrag event.  Overrides\n<a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DragDrop-method-b4Drag' rel='Ext.dd.DragDrop-method-b4Drag' class='docClass'>Ext.dd.DragDrop.b4Drag</a></p></div></div></div><div id='method-b4DragDrop' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-b4DragDrop' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-b4DragDrop' class='name expandable'>b4DragDrop</a>( <span class='pre'>e</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Code that executes immediately before the onDragDrop event ...</div><div class='long'><p>Code that executes immediately before the onDragDrop event</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-b4DragOut' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-b4DragOut' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-b4DragOut' class='name expandable'>b4DragOut</a>( <span class='pre'>e</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Code that executes immediately before the onDragOut event ...</div><div class='long'><p>Code that executes immediately before the onDragOut event</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-b4DragOver' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-b4DragOver' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-b4DragOver' class='name expandable'>b4DragOver</a>( <span class='pre'>e</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Code that executes immediately before the onDragOver event ...</div><div class='long'><p>Code that executes immediately before the onDragOver event</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-b4EndDrag' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-b4EndDrag' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-b4EndDrag' class='name expandable'>b4EndDrag</a>( <span class='pre'>e</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Code that executes immediately before the endDrag event ...</div><div class='long'><p>Code that executes immediately before the endDrag event</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DDProxy-method-b4EndDrag' rel='Ext.dd.DDProxy-method-b4EndDrag' class='docClass'>Ext.dd.DDProxy.b4EndDrag</a></p></div></div></div><div id='method-b4MouseDown' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DDProxy' rel='Ext.dd.DDProxy' class='defined-in docClass'>Ext.dd.DDProxy</a><br/><a href='source/DDProxy.html#Ext-dd-DDProxy-method-b4MouseDown' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DDProxy-method-b4MouseDown' class='name expandable'>b4MouseDown</a>( <span class='pre'>e</span> )</div><div class='description'><div class='short'>overrides Ext.dd.DragDrop\n\nEvent that fires prior to the onMouseDown event. ...</div><div class='long'><p>overrides <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a></p>\n\n<p>Event that fires prior to the onMouseDown event.  Overrides\n<a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DD-method-b4MouseDown' rel='Ext.dd.DD-method-b4MouseDown' class='docClass'>Ext.dd.DD.b4MouseDown</a></p></div></div></div><div id='method-b4StartDrag' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DDProxy' rel='Ext.dd.DDProxy' class='defined-in docClass'>Ext.dd.DDProxy</a><br/><a href='source/DDProxy.html#Ext-dd-DDProxy-method-b4StartDrag' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DDProxy-method-b4StartDrag' class='name expandable'>b4StartDrag</a>( <span class='pre'>x, y</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>overrides Ext.dd.DragDrop\n\nCode that executes immediately before the startDrag event ...</div><div class='long'><p>overrides <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a></p>\n\n<p>Code that executes immediately before the startDrag event</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>x</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>y</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DragDrop-method-b4StartDrag' rel='Ext.dd.DragDrop-method-b4StartDrag' class='docClass'>Ext.dd.DragDrop.b4StartDrag</a></p></div></div></div><div id='method-beforeDragDrop' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-beforeDragDrop' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-beforeDragDrop' class='name expandable'>beforeDragDrop</a>( <span class='pre'>target, e, id</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><strong class='template signature' >template</strong></div><div class='description'><div class='short'>An empty function by default, but provided so that you can perform a custom action before the dragged\nitem is dropped...</div><div class='long'><p>An empty function by default, but provided so that you can perform a custom action before the dragged\nitem is dropped onto the target and optionally cancel the onDragDrop.</p>\n      <div class='signature-box template'>\n      <p>This is a <a href=\"#!/guide/components\">template method</a>.\n         a hook into the functionality of this class.\n         Feel free to override it in child classes.</p>\n      </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a><div class='sub-desc'><p>The drop target</p>\n</div></li><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>The event object</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The id of the dragged element</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>isValid True if the drag drop event is valid, else false to cancel</p>\n</div></li></ul></div></div></div><div id='method-beforeDragEnter' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-beforeDragEnter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-beforeDragEnter' class='name expandable'>beforeDragEnter</a>( <span class='pre'>target, e, id</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><strong class='template signature' >template</strong></div><div class='description'><div class='short'>An empty function by default, but provided so that you can perform a custom action\nbefore the dragged item enters the...</div><div class='long'><p>An empty function by default, but provided so that you can perform a custom action\nbefore the dragged item enters the drop target and optionally cancel the onDragEnter.</p>\n      <div class='signature-box template'>\n      <p>This is a <a href=\"#!/guide/components\">template method</a>.\n         a hook into the functionality of this class.\n         Feel free to override it in child classes.</p>\n      </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a><div class='sub-desc'><p>The drop target</p>\n</div></li><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>The event object</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The id of the dragged element</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>isValid True if the drag event is valid, else false to cancel</p>\n</div></li></ul></div></div></div><div id='method-beforeDragOut' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-beforeDragOut' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-beforeDragOut' class='name expandable'>beforeDragOut</a>( <span class='pre'>target, e, id</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><strong class='template signature' >template</strong></div><div class='description'><div class='short'>An empty function by default, but provided so that you can perform a custom action before the dragged\nitem is dragged...</div><div class='long'><p>An empty function by default, but provided so that you can perform a custom action before the dragged\nitem is dragged out of the target without dropping, and optionally cancel the onDragOut.</p>\n      <div class='signature-box template'>\n      <p>This is a <a href=\"#!/guide/components\">template method</a>.\n         a hook into the functionality of this class.\n         Feel free to override it in child classes.</p>\n      </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a><div class='sub-desc'><p>The drop target</p>\n</div></li><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>The event object</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The id of the dragged element</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>isValid True if the drag event is valid, else false to cancel</p>\n</div></li></ul></div></div></div><div id='method-beforeDragOver' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-beforeDragOver' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-beforeDragOver' class='name expandable'>beforeDragOver</a>( <span class='pre'>target, e, id</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><strong class='template signature' >template</strong></div><div class='description'><div class='short'>An empty function by default, but provided so that you can perform a custom action\nwhile the dragged item is over the...</div><div class='long'><p>An empty function by default, but provided so that you can perform a custom action\nwhile the dragged item is over the drop target and optionally cancel the onDragOver.</p>\n      <div class='signature-box template'>\n      <p>This is a <a href=\"#!/guide/components\">template method</a>.\n         a hook into the functionality of this class.\n         Feel free to override it in child classes.</p>\n      </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a><div class='sub-desc'><p>The drop target</p>\n</div></li><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>The event object</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The id of the dragged element</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>isValid True if the drag event is valid, else false to cancel</p>\n</div></li></ul></div></div></div><div id='method-beforeInvalidDrop' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-beforeInvalidDrop' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-beforeInvalidDrop' class='name expandable'>beforeInvalidDrop</a>( <span class='pre'>target, e, id</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><strong class='template signature' >template</strong></div><div class='description'><div class='short'>An empty function by default, but provided so that you can perform a custom action after an invalid\ndrop has occurred. ...</div><div class='long'><p>An empty function by default, but provided so that you can perform a custom action after an invalid\ndrop has occurred.</p>\n      <div class='signature-box template'>\n      <p>This is a <a href=\"#!/guide/components\">template method</a>.\n         a hook into the functionality of this class.\n         Feel free to override it in child classes.</p>\n      </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a><div class='sub-desc'><p>The drop target</p>\n</div></li><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>The event object</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The id of the dragged element</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>isValid True if the invalid drop should proceed, else false to cancel</p>\n</div></li></ul></div></div></div><div id='method-beforeMove' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DDProxy' rel='Ext.dd.DDProxy' class='defined-in docClass'>Ext.dd.DDProxy</a><br/><a href='source/DDProxy.html#Ext-dd-DDProxy-method-beforeMove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DDProxy-method-beforeMove' class='name expandable'>beforeMove</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-cachePosition' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DD' rel='Ext.dd.DD' class='defined-in docClass'>Ext.dd.DD</a><br/><a href='source/DD.html#Ext-dd-DD-method-cachePosition' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DD-method-cachePosition' class='name expandable'>cachePosition</a>( <span class='pre'>[iPageX], [iPageY]</span> )</div><div class='description'><div class='short'>Saves the most recent position so that we can reset the constraints and\ntick marks on-demand. ...</div><div class='long'><p>Saves the most recent position so that we can reset the constraints and\ntick marks on-demand.  We need to know this so that we can calculate the\nnumber of pixels the element is offset from its original position.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>iPageX</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>the current x position (this just makes it so we\ndon't have to look it up again)</p>\n</div></li><li><span class='pre'>iPageY</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>the current y position (this just makes it so we\ndon't have to look it up again)</p>\n</div></li></ul></div></div></div><div id='method-callOverridden' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-clearConstraints' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-clearConstraints' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-clearConstraints' class='name expandable'>clearConstraints</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Clears any constraints applied to this instance. ...</div><div class='long'><p>Clears any constraints applied to this instance.  Also clears ticks\nsince they can't exist independent of a constraint at this time.</p>\n</div></div></div><div id='method-clearTicks' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-clearTicks' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-clearTicks' class='name expandable'>clearTicks</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Clears any tick interval defined for this instance ...</div><div class='long'><p>Clears any tick interval defined for this instance</p>\n</div></div></div><div id='method-clickValidator' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-clickValidator' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-clickValidator' class='name expandable'>clickValidator</a>( <span class='pre'>e</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-constrainTo' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-constrainTo' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-constrainTo' class='name expandable'>constrainTo</a>( <span class='pre'>constrainTo, [pad], [inContent]</span> )</div><div class='description'><div class='short'>Initializes the drag drop object's constraints to restrict movement to a certain element. ...</div><div class='long'><p>Initializes the drag drop object's constraints to restrict movement to a certain element.</p>\n\n<p>Usage:</p>\n\n<pre><code>var dd = new <a href=\"#!/api/Ext.dd.DDProxy\" rel=\"Ext.dd.DDProxy\" class=\"docClass\">Ext.dd.DDProxy</a>(\"dragDiv1\", \"proxytest\",\n               { dragElId: \"existingProxyDiv\" });\ndd.startDrag = function(){\n    this.constrainTo(\"parent-id\");\n};\n</code></pre>\n\n<p>Or you can initalize it using the <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a> object:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-get\" rel=\"Ext-method-get\" class=\"docClass\">Ext.get</a>(\"dragDiv1\").initDDProxy(\"proxytest\", {dragElId: \"existingProxyDiv\"}, {\n    startDrag : function(){\n        this.constrainTo(\"parent-id\");\n    }\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>constrainTo</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The element or element ID to constrain to.</p>\n</div></li><li><span class='pre'>pad</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>Pad provides a way to specify \"padding\" of the constraints,\nand can be either a number for symmetrical padding (4 would be equal to <code>{left:4, right:4, top:4, bottom:4}</code>) or\nan object containing the sides to pad. For example: <code>{right:10, bottom:10}</code></p>\n</div></li><li><span class='pre'>inContent</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>Constrain the draggable in the content box of the element (inside padding and borders)</p>\n</div></li></ul></div></div></div><div id='method-createFrame' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.draw.SpriteDD'>Ext.draw.SpriteDD</span><br/><a href='source/SpriteDD.html#Ext-draw-SpriteDD-method-createFrame' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.draw.SpriteDD-method-createFrame' class='name expandable'>createFrame</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Creates the proxy element if it does not yet exist ...</div><div class='long'><p>Creates the proxy element if it does not yet exist</p>\n<p>Overrides: <a href='#!/api/Ext.dd.DDProxy-method-createFrame' rel='Ext.dd.DDProxy-method-createFrame' class='docClass'>Ext.dd.DDProxy.createFrame</a></p></div></div></div><div id='method-destroy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-destroy' class='name expandable'>destroy</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Destroy this DragDrop instance ...</div><div class='long'><p>Destroy this DragDrop instance</p>\n<p>Overrides: <a href='#!/api/Ext.dd.DragDrop-method-destroy' rel='Ext.dd.DragDrop-method-destroy' class='docClass'>Ext.dd.DragDrop.destroy</a></p></div></div></div><div id='method-endDrag' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-endDrag' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-endDrag' class='name expandable'>endDrag</a>( <span class='pre'>e</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Called when we are done dragging the object ...</div><div class='long'><p>Called when we are done dragging the object</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>the mouseup event</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DDProxy-method-endDrag' rel='Ext.dd.DDProxy-method-endDrag' class='docClass'>Ext.dd.DDProxy.endDrag</a></p></div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getDragData' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-getDragData' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-getDragData' class='name expandable'>getDragData</a>( <span class='pre'>e</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Returns the data object associated with this drag source ...</div><div class='long'><p>Returns the data object associated with this drag source</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>data An object containing arbitrary data</p>\n</div></li></ul></div></div></div><div id='method-getDragEl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.draw.SpriteDD'>Ext.draw.SpriteDD</span><br/><a href='source/SpriteDD.html#Ext-draw-SpriteDD-method-getDragEl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.draw.SpriteDD-method-getDragEl' class='name expandable'>getDragEl</a>( <span class='pre'></span> ) : HTMLElement</div><div class='description'><div class='short'>Returns a reference to the actual element to drag. ...</div><div class='long'><p>Returns a reference to the actual element to drag.  By default this is\nthe same as the html element, but it can be assigned to another\nelement. An example of this can be found in <a href=\"#!/api/Ext.dd.DDProxy\" rel=\"Ext.dd.DDProxy\" class=\"docClass\">Ext.dd.DDProxy</a></p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'>HTMLElement</span><div class='sub-desc'><p>the html element</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DragDrop-method-getDragEl' rel='Ext.dd.DragDrop-method-getDragEl' class='docClass'>Ext.dd.DragDrop.getDragEl</a></p></div></div></div><div id='method-getEl' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-getEl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-getEl' class='name expandable'>getEl</a>( <span class='pre'></span> ) : HTMLElement</div><div class='description'><div class='short'>Returns a reference to the linked element ...</div><div class='long'><p>Returns a reference to the linked element</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'>HTMLElement</span><div class='sub-desc'><p>the html element</p>\n</div></li></ul></div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-getLocalX' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DD' rel='Ext.dd.DD' class='defined-in docClass'>Ext.dd.DD</a><br/><a href='source/DD.html#Ext-dd-DD-method-getLocalX' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DD-method-getLocalX' class='name expandable'>getLocalX</a>( <span class='pre'>el</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getProxy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-getProxy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-getProxy' class='name expandable'>getProxy</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.dd.StatusProxy\" rel=\"Ext.dd.StatusProxy\" class=\"docClass\">Ext.dd.StatusProxy</a></div><div class='description'><div class='short'>Returns the drag source's underlying Ext.dd.StatusProxy ...</div><div class='long'><p>Returns the drag source's underlying <a href=\"#!/api/Ext.dd.StatusProxy\" rel=\"Ext.dd.StatusProxy\" class=\"docClass\">Ext.dd.StatusProxy</a></p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dd.StatusProxy\" rel=\"Ext.dd.StatusProxy\" class=\"docClass\">Ext.dd.StatusProxy</a></span><div class='sub-desc'><p>proxy The StatusProxy</p>\n</div></li></ul></div></div></div><div id='method-getRegion' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.draw.SpriteDD'>Ext.draw.SpriteDD</span><br/><a href='source/SpriteDD.html#Ext-draw-SpriteDD-method-getRegion' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.draw.SpriteDD-method-getRegion' class='name expandable'>getRegion</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getRepairXY' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-getRepairXY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-getRepairXY' class='name expandable'>getRepairXY</a>( <span class='pre'>e, data</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>data</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getTargetCoord' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DD' rel='Ext.dd.DD' class='defined-in docClass'>Ext.dd.DD</a><br/><a href='source/DD.html#Ext-dd-DD-method-getTargetCoord' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DD-method-getTargetCoord' class='name expandable'>getTargetCoord</a>( <span class='pre'>iPageX, iPageY</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Finds the location the element should be placed if we want to move\nit to where the mouse location less the click offs...</div><div class='long'><p>Finds the location the element should be placed if we want to move\nit to where the mouse location less the click offset would place us.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>iPageX</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the X coordinate of the click</p>\n</div></li><li><span class='pre'>iPageY</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the Y coordinate of the click</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>An object that contains the coordinates (Object.x and Object.y)</p>\n<ul><li><span class='pre'>x</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'></div></li><li><span class='pre'>y</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'></div></li></ul></div></li></ul></div></div></div><div id='method-getTick' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-getTick' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-getTick' class='name expandable'>getTick</a>( <span class='pre'>val, tickArray</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Normally the drag element is moved pixel by pixel, but we can specify\nthat it move a number of pixels at a time. ...</div><div class='long'><p>Normally the drag element is moved pixel by pixel, but we can specify\nthat it move a number of pixels at a time.  This method resolves the\nlocation when we have it set up like this.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>val</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>where we want to place the object</p>\n</div></li><li><span class='pre'>tickArray</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]<div class='sub-desc'><p>sorted array of valid points</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>the closest tick</p>\n</div></li></ul></div></div></div><div id='method-handleMouseDown' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-handleMouseDown' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-handleMouseDown' class='name expandable'>handleMouseDown</a>( <span class='pre'>e, oDD</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Called when this object is clicked ...</div><div class='long'><p>Called when this object is clicked</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : Event<div class='sub-desc'>\n</div></li><li><span class='pre'>oDD</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a><div class='sub-desc'><p>the clicked dd object (this dd obj)</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DragDrop-method-handleMouseDown' rel='Ext.dd.DragDrop-method-handleMouseDown' class='docClass'>Ext.dd.DragDrop.handleMouseDown</a></p></div></div></div><div id='method-handleOnAvailable' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-handleOnAvailable' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-handleOnAvailable' class='name expandable'>handleOnAvailable</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Executed when the linked element is available ...</div><div class='long'><p>Executed when the linked element is available</p>\n</div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-hideProxy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-hideProxy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-hideProxy' class='name expandable'>hideProxy</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Hides the drag source's Ext.dd.StatusProxy ...</div><div class='long'><p>Hides the drag source's <a href=\"#!/api/Ext.dd.StatusProxy\" rel=\"Ext.dd.StatusProxy\" class=\"docClass\">Ext.dd.StatusProxy</a></p>\n</div></div></div><div id='method-init' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-init' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-init' class='name expandable'>init</a>( <span class='pre'>id, sGroup, config</span> )</div><div class='description'><div class='short'>Sets up the DragDrop object. ...</div><div class='long'><p>Sets up the DragDrop object.  Must be called in the constructor of any\n<a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a> subclass</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the id of the linked element</p>\n</div></li><li><span class='pre'>sGroup</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the group of related items</p>\n</div></li><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>configuration attributes</p>\n</div></li></ul></div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-initFrame' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DDProxy' rel='Ext.dd.DDProxy' class='defined-in docClass'>Ext.dd.DDProxy</a><br/><a href='source/DDProxy.html#Ext-dd-DDProxy-method-initFrame' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DDProxy-method-initFrame' class='name expandable'>initFrame</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Initialization for the drag frame element. ...</div><div class='long'><p>Initialization for the drag frame element.  Must be called in the\nconstructor of all subclasses</p>\n</div></div></div><div id='method-initTarget' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-initTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-initTarget' class='name expandable'>initTarget</a>( <span class='pre'>id, sGroup, config</span> )</div><div class='description'><div class='short'>Initializes Targeting functionality only... ...</div><div class='long'><p>Initializes Targeting functionality only... the object does not\nget a mousedown handler.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the id of the linked element</p>\n</div></li><li><span class='pre'>sGroup</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the group of related items</p>\n</div></li><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>configuration attributes</p>\n</div></li></ul></div></div></div><div id='method-isLocked' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-isLocked' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-isLocked' class='name expandable'>isLocked</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if this instance is locked, or the drag drop mgr is locked\n(meaning that all drag/drop is disabled on th...</div><div class='long'><p>Returns true if this instance is locked, or the drag drop mgr is locked\n(meaning that all drag/drop is disabled on the page.)</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>true if this obj or all drag/drop is locked, else\nfalse</p>\n</div></li></ul></div></div></div><div id='method-isValidHandleChild' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-isValidHandleChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-isValidHandleChild' class='name expandable'>isValidHandleChild</a>( <span class='pre'>node</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks the tag exclusion list to see if this click should be ignored ...</div><div class='long'><p>Checks the tag exclusion list to see if this click should be ignored</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>node</span> : HTMLElement<div class='sub-desc'><p>the HTMLElement to evaluate</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>true if this is a valid tag type, false if not</p>\n</div></li></ul></div></div></div><div id='method-lock' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-lock' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-lock' class='name expandable'>lock</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Locks this instance ...</div><div class='long'><p>Locks this instance</p>\n</div></div></div><div id='method-onAvailable' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-onAvailable' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-onAvailable' class='name expandable'>onAvailable</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Override the onAvailable method to do what is needed after the initial\nposition was determined. ...</div><div class='long'><p>Override the onAvailable method to do what is needed after the initial\nposition was determined.</p>\n</div></div></div><div id='method-onBeforeDrag' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-onBeforeDrag' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-onBeforeDrag' class='name expandable'>onBeforeDrag</a>( <span class='pre'>data, e</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><strong class='template signature' >template</strong></div><div class='description'><div class='short'>An empty function by default, but provided so that you can perform a custom action before the initial\ndrag event begi...</div><div class='long'><p>An empty function by default, but provided so that you can perform a custom action before the initial\ndrag event begins and optionally cancel it.</p>\n      <div class='signature-box template'>\n      <p>This is a <a href=\"#!/guide/components\">template method</a>.\n         a hook into the functionality of this class.\n         Feel free to override it in child classes.</p>\n      </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>data</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>An object containing arbitrary data to be shared with drop targets</p>\n</div></li><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>The event object</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>isValid True if the drag event is valid, else false to cancel</p>\n</div></li></ul></div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onDrag' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.draw.SpriteDD'>Ext.draw.SpriteDD</span><br/><a href='source/SpriteDD.html#Ext-draw-SpriteDD-method-onDrag' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.draw.SpriteDD-method-onDrag' class='name expandable'>onDrag</a>( <span class='pre'>e</span> )</div><div class='description'><div class='short'>Abstract method called during the onMouseMove event while dragging an\nobject. ...</div><div class='long'><p>Abstract method called during the onMouseMove event while dragging an\nobject.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>the mousemove event</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DragDrop-method-onDrag' rel='Ext.dd.DragDrop-method-onDrag' class='docClass'>Ext.dd.DragDrop.onDrag</a></p></div></div></div><div id='method-onDragDrop' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-onDragDrop' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-onDragDrop' class='name expandable'>onDragDrop</a>( <span class='pre'>e, id</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Abstract method called when this item is dropped on another DragDrop\nobj ...</div><div class='long'><p>Abstract method called when this item is dropped on another DragDrop\nobj</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>the mouseup event</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a>[]<div class='sub-desc'><p>In POINT mode, the element\nid this was dropped on.  In INTERSECT mode, an array of dd items this\nwas dropped on.</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DragDrop-method-onDragDrop' rel='Ext.dd.DragDrop-method-onDragDrop' class='docClass'>Ext.dd.DragDrop.onDragDrop</a></p></div></div></div><div id='method-onDragEnter' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-onDragEnter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-onDragEnter' class='name expandable'>onDragEnter</a>( <span class='pre'>e, id</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Abstract method called when this element fist begins hovering over\nanother DragDrop obj ...</div><div class='long'><p>Abstract method called when this element fist begins hovering over\nanother DragDrop obj</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>the mousemove event</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a>[]<div class='sub-desc'><p>In POINT mode, the element\nid this is hovering over.  In INTERSECT mode, an array of one or more\ndragdrop items being hovered over.</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DragDrop-method-onDragEnter' rel='Ext.dd.DragDrop-method-onDragEnter' class='docClass'>Ext.dd.DragDrop.onDragEnter</a></p></div></div></div><div id='method-onDragOut' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-onDragOut' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-onDragOut' class='name expandable'>onDragOut</a>( <span class='pre'>e, id</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Abstract method called when we are no longer hovering over an element ...</div><div class='long'><p>Abstract method called when we are no longer hovering over an element</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>the mousemove event</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a>[]<div class='sub-desc'><p>In POINT mode, the element\nid this was hovering over.  In INTERSECT mode, an array of dd items\nthat the mouse is no longer over.</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DragDrop-method-onDragOut' rel='Ext.dd.DragDrop-method-onDragOut' class='docClass'>Ext.dd.DragDrop.onDragOut</a></p></div></div></div><div id='method-onDragOver' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-onDragOver' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-onDragOver' class='name expandable'>onDragOver</a>( <span class='pre'>e, id</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Abstract method called when this element is hovering over another\nDragDrop obj ...</div><div class='long'><p>Abstract method called when this element is hovering over another\nDragDrop obj</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>the mousemove event</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a>[]<div class='sub-desc'><p>In POINT mode, the element\nid this is hovering over.  In INTERSECT mode, an array of dd items\nbeing hovered over.</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DragDrop-method-onDragOver' rel='Ext.dd.DragDrop-method-onDragOver' class='docClass'>Ext.dd.DragDrop.onDragOver</a></p></div></div></div><div id='method-onEndDrag' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-onEndDrag' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-onEndDrag' class='name expandable'>onEndDrag</a>( <span class='pre'>data, e</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>data</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>e</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onInitDrag' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-onInitDrag' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-onInitDrag' class='name expandable'>onInitDrag</a>( <span class='pre'>x, y</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>x</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>y</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onInvalidDrop' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-onInvalidDrop' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-onInvalidDrop' class='name expandable'>onInvalidDrop</a>( <span class='pre'>e</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Abstract method called when this item is dropped on an area with no\ndrop target ...</div><div class='long'><p>Abstract method called when this item is dropped on an area with no\ndrop target</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>the mouseup event</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DragDrop-method-onInvalidDrop' rel='Ext.dd.DragDrop-method-onInvalidDrop' class='docClass'>Ext.dd.DragDrop.onInvalidDrop</a></p></div></div></div><div id='method-onMouseDown' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-onMouseDown' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-onMouseDown' class='name expandable'>onMouseDown</a>( <span class='pre'>e</span> )</div><div class='description'><div class='short'>Called when a drag/drop obj gets a mousedown ...</div><div class='long'><p>Called when a drag/drop obj gets a mousedown</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>the mousedown event</p>\n</div></li></ul></div></div></div><div id='method-onMouseUp' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-onMouseUp' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-onMouseUp' class='name expandable'>onMouseUp</a>( <span class='pre'>e</span> )</div><div class='description'><div class='short'>Called when a drag/drop obj gets a mouseup ...</div><div class='long'><p>Called when a drag/drop obj gets a mouseup</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>the mouseup event</p>\n</div></li></ul></div></div></div><div id='method-onStartDrag' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-onStartDrag' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-onStartDrag' class='name expandable'>onStartDrag</a>( <span class='pre'>x, y</span> )<strong class='template signature' >template</strong></div><div class='description'><div class='short'>An empty function by default, but provided so that you can perform a custom action once the initial\ndrag event has be...</div><div class='long'><p>An empty function by default, but provided so that you can perform a custom action once the initial\ndrag event has begun.  The drag cannot be canceled from this function.</p>\n      <div class='signature-box template'>\n      <p>This is a <a href=\"#!/guide/components\">template method</a>.\n         a hook into the functionality of this class.\n         Feel free to override it in child classes.</p>\n      </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>x</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The x position of the click on the dragged object</p>\n</div></li><li><span class='pre'>y</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The y position of the click on the dragged object</p>\n</div></li></ul></div></div></div><div id='method-onValidDrop' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-onValidDrop' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-onValidDrop' class='name expandable'>onValidDrop</a>( <span class='pre'>target, e, id</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>e</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-removeFromGroup' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-removeFromGroup' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-removeFromGroup' class='name expandable'>removeFromGroup</a>( <span class='pre'>sGroup</span> )</div><div class='description'><div class='short'>Removes this instance from the supplied interaction group ...</div><div class='long'><p>Removes this instance from the supplied interaction group</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>sGroup</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The group to drop</p>\n</div></li></ul></div></div></div><div id='method-removeInvalidHandleClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-removeInvalidHandleClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-removeInvalidHandleClass' class='name expandable'>removeInvalidHandleClass</a>( <span class='pre'>cssClass</span> )</div><div class='description'><div class='short'>Unsets an invalid css class ...</div><div class='long'><p>Unsets an invalid css class</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>cssClass</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the class of the element(s) you wish to\nre-enable</p>\n</div></li></ul></div></div></div><div id='method-removeInvalidHandleId' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-removeInvalidHandleId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-removeInvalidHandleId' class='name expandable'>removeInvalidHandleId</a>( <span class='pre'>id</span> )</div><div class='description'><div class='short'>Unsets an invalid handle id ...</div><div class='long'><p>Unsets an invalid handle id</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the id of the element to re-enable</p>\n</div></li></ul></div></div></div><div id='method-removeInvalidHandleType' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-removeInvalidHandleType' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-removeInvalidHandleType' class='name expandable'>removeInvalidHandleType</a>( <span class='pre'>tagName</span> )</div><div class='description'><div class='short'>Unsets an excluded tag name set by addInvalidHandleType ...</div><div class='long'><p>Unsets an excluded tag name set by addInvalidHandleType</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>tagName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the type of element to unexclude</p>\n</div></li></ul></div></div></div><div id='method-resetConstraints' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-resetConstraints' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-resetConstraints' class='name expandable'>resetConstraints</a>( <span class='pre'>maintainOffset</span> )</div><div class='description'><div class='short'>Must be called if you manually reposition a dd element. ...</div><div class='long'><p>Must be called if you manually reposition a dd element.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>maintainOffset</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setDelta' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DD' rel='Ext.dd.DD' class='defined-in docClass'>Ext.dd.DD</a><br/><a href='source/DD.html#Ext-dd-DD-method-setDelta' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DD-method-setDelta' class='name expandable'>setDelta</a>( <span class='pre'>iDeltaX, iDeltaY</span> )</div><div class='description'><div class='short'>Sets the pointer offset. ...</div><div class='long'><p>Sets the pointer offset.  You can call this directly to force the\noffset to be in a particular location (e.g., pass in 0,0 to set it\nto the center of the object)</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>iDeltaX</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the distance from the left</p>\n</div></li><li><span class='pre'>iDeltaY</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the distance from the top</p>\n</div></li></ul></div></div></div><div id='method-setDragElId' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-setDragElId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-setDragElId' class='name expandable'>setDragElId</a>( <span class='pre'>id</span> )</div><div class='description'><div class='short'>Allows you to specify that an element other than the linked element\nwill be moved with the cursor during a drag ...</div><div class='long'><p>Allows you to specify that an element other than the linked element\nwill be moved with the cursor during a drag</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the id of the element that will be used to initiate the drag</p>\n</div></li></ul></div></div></div><div id='method-setDragElPos' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.draw.SpriteDD'>Ext.draw.SpriteDD</span><br/><a href='source/SpriteDD.html#Ext-draw-SpriteDD-method-setDragElPos' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.draw.SpriteDD-method-setDragElPos' class='name expandable'>setDragElPos</a>( <span class='pre'>iPageX, iPageY</span> )</div><div class='description'><div class='short'>Sets the drag element to the location of the mousedown or click event,\nmaintaining the cursor location relative to th...</div><div class='long'><p>Sets the drag element to the location of the mousedown or click event,\nmaintaining the cursor location relative to the location on the element\nthat was clicked.  Override this if you want to place the element in a\nlocation other than where the cursor is.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>iPageX</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the X coordinate of the mousedown or drag event</p>\n</div></li><li><span class='pre'>iPageY</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the Y coordinate of the mousedown or drag event</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DD-method-setDragElPos' rel='Ext.dd.DD-method-setDragElPos' class='docClass'>Ext.dd.DD.setDragElPos</a></p></div></div></div><div id='method-setHandleElId' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-setHandleElId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-setHandleElId' class='name expandable'>setHandleElId</a>( <span class='pre'>id</span> )</div><div class='description'><div class='short'>Allows you to specify a child of the linked element that should be\nused to initiate the drag operation. ...</div><div class='long'><p>Allows you to specify a child of the linked element that should be\nused to initiate the drag operation.  An example of this would be if\nyou have a content div with text and links.  Clicking anywhere in the\ncontent area would normally start the drag operation.  Use this method\nto specify that an element inside of the content div is the element\nthat starts the drag operation.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the id of the element that will be used to\ninitiate the drag.</p>\n</div></li></ul></div></div></div><div id='method-setInitPosition' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-setInitPosition' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-setInitPosition' class='name expandable'>setInitPosition</a>( <span class='pre'>diffX, diffY</span> )</div><div class='description'><div class='short'>Stores the initial placement of the linked element. ...</div><div class='long'><p>Stores the initial placement of the linked element.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>diffX</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the X offset, default 0</p>\n</div></li><li><span class='pre'>diffY</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the Y offset, default 0</p>\n</div></li></ul></div></div></div><div id='method-setLocalXY' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DD' rel='Ext.dd.DD' class='defined-in docClass'>Ext.dd.DD</a><br/><a href='source/DD.html#Ext-dd-DD-method-setLocalXY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DD-method-setLocalXY' class='name expandable'>setLocalXY</a>( <span class='pre'>el, x, y</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>x</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>y</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setOuterHandleElId' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-setOuterHandleElId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-setOuterHandleElId' class='name expandable'>setOuterHandleElId</a>( <span class='pre'>id</span> )</div><div class='description'><div class='short'>Allows you to set an element outside of the linked element as a drag\nhandle ...</div><div class='long'><p>Allows you to set an element outside of the linked element as a drag\nhandle</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the id of the element that will be used to initiate the drag</p>\n</div></li></ul></div></div></div><div id='method-setPadding' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-setPadding' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-setPadding' class='name expandable'>setPadding</a>( <span class='pre'>iTop, iRight, iBot, iLeft</span> )</div><div class='description'><div class='short'>Configures the padding for the target zone in px. ...</div><div class='long'><p>Configures the padding for the target zone in px.  Effectively expands\n(or reduces) the virtual object size for targeting calculations.\nSupports css-style shorthand; if only one parameter is passed, all sides\nwill have that padding, and if only two are passed, the top and bottom\nwill have the first param, the left and right the second.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>iTop</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Top pad</p>\n</div></li><li><span class='pre'>iRight</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Right pad</p>\n</div></li><li><span class='pre'>iBot</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Bot pad</p>\n</div></li><li><span class='pre'>iLeft</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Left pad</p>\n</div></li></ul></div></div></div><div id='method-setStartPosition' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-setStartPosition' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-setStartPosition' class='name expandable'>setStartPosition</a>( <span class='pre'>pos</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Sets the start position of the element. ...</div><div class='long'><p>Sets the start position of the element.  This is set when the obj\nis initialized, the reset when a drag is started.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>pos</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>current position (from previous lookup)</p>\n</div></li></ul></div></div></div><div id='method-setXConstraint' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-setXConstraint' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-setXConstraint' class='name expandable'>setXConstraint</a>( <span class='pre'>iLeft, iRight, [iTickSize]</span> )</div><div class='description'><div class='short'>By default, the element can be dragged any place on the screen. ...</div><div class='long'><p>By default, the element can be dragged any place on the screen.  Use\nthis method to limit the horizontal travel of the element.  Pass in\n0,0 for the parameters if you want to lock the drag to the y axis.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>iLeft</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the number of pixels the element can move to the left</p>\n</div></li><li><span class='pre'>iRight</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the number of pixels the element can move to the\nright</p>\n</div></li><li><span class='pre'>iTickSize</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>parameter for specifying that the\nelement should move iTickSize pixels at a time.</p>\n</div></li></ul></div></div></div><div id='method-setXTicks' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-setXTicks' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-setXTicks' class='name expandable'>setXTicks</a>( <span class='pre'>iStartX, iTickSize</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Creates the array of horizontal tick marks if an interval was specified\nin setXConstraint(). ...</div><div class='long'><p>Creates the array of horizontal tick marks if an interval was specified\nin setXConstraint().</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>iStartX</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>iTickSize</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setYConstraint' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-setYConstraint' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-setYConstraint' class='name expandable'>setYConstraint</a>( <span class='pre'>iUp, iDown, [iTickSize]</span> )</div><div class='description'><div class='short'>By default, the element can be dragged any place on the screen. ...</div><div class='long'><p>By default, the element can be dragged any place on the screen.  Set\nthis to limit the vertical travel of the element.  Pass in 0,0 for the\nparameters if you want to lock the drag to the x axis.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>iUp</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the number of pixels the element can move up</p>\n</div></li><li><span class='pre'>iDown</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the number of pixels the element can move down</p>\n</div></li><li><span class='pre'>iTickSize</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>parameter for specifying that the\nelement should move iTickSize pixels at a time.</p>\n</div></li></ul></div></div></div><div id='method-setYTicks' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-setYTicks' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-setYTicks' class='name expandable'>setYTicks</a>( <span class='pre'>iStartY, iTickSize</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Creates the array of vertical tick marks if an interval was specified in\nsetYConstraint(). ...</div><div class='long'><p>Creates the array of vertical tick marks if an interval was specified in\nsetYConstraint().</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>iStartY</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>iTickSize</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-showFrame' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.draw.SpriteDD'>Ext.draw.SpriteDD</span><br/><a href='source/SpriteDD.html#Ext-draw-SpriteDD-method-showFrame' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.draw.SpriteDD-method-showFrame' class='name expandable'>showFrame</a>( <span class='pre'>iPageX, iPageY</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Resizes the drag frame to the dimensions of the clicked object, positions\nit over the object, and finally displays it ...</div><div class='long'><p>Resizes the drag frame to the dimensions of the clicked object, positions\nit over the object, and finally displays it</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>iPageX</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>X click position</p>\n</div></li><li><span class='pre'>iPageY</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Y click position</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DDProxy-method-showFrame' rel='Ext.dd.DDProxy-method-showFrame' class='docClass'>Ext.dd.DDProxy.showFrame</a></p></div></div></div><div id='method-startDrag' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.draw.SpriteDD'>Ext.draw.SpriteDD</span><br/><a href='source/SpriteDD.html#Ext-draw-SpriteDD-method-startDrag' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.draw.SpriteDD-method-startDrag' class='name expandable'>startDrag</a>( <span class='pre'>x, y</span> )</div><div class='description'><div class='short'>TODO(nico): Cumulative translations in VML are handled\ndifferently than in SVG. ...</div><div class='long'><p>TODO(nico): Cumulative translations in VML are handled\ndifferently than in SVG. While in SVG we specify the translation\nrelative to the original x, y position attributes, in VML the translation\nis a delta between the last position of the object (modified by the last\ntranslation) and the new one.</p>\n\n<p>In VML the translation alters the position\nof the object, we should change that or alter the SVG impl.</p>\n\n<p>Abstract method called after a drag/drop object is clicked\nand the drag or mousedown time thresholds have beeen met.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>x</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>X click location</p>\n</div></li><li><span class='pre'>y</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Y click location</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DragSource-method-startDrag' rel='Ext.dd.DragSource-method-startDrag' class='docClass'>Ext.dd.DragSource.startDrag</a></p></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-toString' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DDProxy' rel='Ext.dd.DDProxy' class='defined-in docClass'>Ext.dd.DDProxy</a><br/><a href='source/DDProxy.html#Ext-dd-DDProxy-method-toString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DDProxy-method-toString' class='name expandable'>toString</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>toString method ...</div><div class='long'><p>toString method</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>string representation of the dd obj</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.dd.DD-method-toString' rel='Ext.dd.DD-method-toString' class='docClass'>Ext.dd.DD.toString</a></p></div></div></div><div id='method-triggerCacheRefresh' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragSource' rel='Ext.dd.DragSource' class='defined-in docClass'>Ext.dd.DragSource</a><br/><a href='source/DragSource.html#Ext-dd-DragSource-method-triggerCacheRefresh' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragSource-method-triggerCacheRefresh' class='name expandable'>triggerCacheRefresh</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-unlock' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-unlock' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-unlock' class='name expandable'>unlock</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Unlocks this instace ...</div><div class='long'><p>Unlocks this instace</p>\n</div></div></div><div id='method-unreg' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='defined-in docClass'>Ext.dd.DragDrop</a><br/><a href='source/DragDrop.html#Ext-dd-DragDrop-method-unreg' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDrop-method-unreg' class='name expandable'>unreg</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Removes all drag and drop hooks for this element ...</div><div class='long'><p>Removes all drag and drop hooks for this element</p>\n</div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-create' class='name expandable'>create</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create a new instance of this Class. ...</div><div class='long'><p>Create a new instance of this Class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.create({\n    someConfig: true\n});\n</code></pre>\n\n<p>All parameters are passed to the constructor of the class.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the created instance.</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div></div></div>","superclasses":["Ext.Base","Ext.dd.DragDrop","Ext.dd.DD","Ext.dd.DDProxy","Ext.dd.DragSource"],"meta":{"private":true},"code_type":"ext_define","requires":[],"html_meta":{"private":null},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"SpriteDD.html#Ext-draw-SpriteDD","filename":"SpriteDD.js"}],"linenr":1,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"__ygDragDrop","id":"property-__ygDragDrop"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"_domRef","id":"property-_domRef"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{},"name":"available","id":"property-available"},{"tagname":"property","owner":"Ext.dd.DDProxy","meta":{},"name":"centerFrame","id":"property-centerFrame"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{},"name":"config","id":"property-config"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"constrainX","id":"property-constrainX"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"constrainY","id":"property-constrainY"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{},"name":"defaultPadding","id":"property-defaultPadding"},{"tagname":"property","owner":"Ext.dd.DragSource","meta":{},"name":"dragData","id":"property-dragData"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"dragElId","id":"property-dragElId"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{},"name":"groups","id":"property-groups"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"handleElId","id":"property-handleElId"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{},"name":"hasOuterHandles","id":"property-hasOuterHandles"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{},"name":"id","id":"property-id"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{},"name":"ignoreSelf","id":"property-ignoreSelf"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{},"name":"invalidHandleClasses","id":"property-invalidHandleClasses"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{},"name":"invalidHandleIds","id":"property-invalidHandleIds"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{},"name":"invalidHandleTypes","id":"property-invalidHandleTypes"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{},"name":"isTarget","id":"property-isTarget"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"locked","id":"property-locked"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{},"name":"maintainOffset","id":"property-maintainOffset"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"maxX","id":"property-maxX"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"maxY","id":"property-maxY"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"minX","id":"property-minX"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"minY","id":"property-minY"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{},"name":"moveOnly","id":"property-moveOnly"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{},"name":"padding","id":"property-padding"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{},"name":"primaryButtonOnly","id":"property-primaryButtonOnly"},{"tagname":"property","owner":"Ext.dd.DDProxy","meta":{},"name":"resizeFrame","id":"property-resizeFrame"},{"tagname":"property","owner":"Ext.dd.DD","meta":{},"name":"scroll","id":"property-scroll"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"startPageX","id":"property-startPageX"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"startPageY","id":"property-startPageY"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{},"name":"xTicks","id":"property-xTicks"},{"tagname":"property","owner":"Ext.dd.DragDrop","meta":{},"name":"yTicks","id":"property-yTicks"}],"cfg":[{"tagname":"cfg","owner":"Ext.dd.DragSource","meta":{},"name":"animRepair","id":"cfg-animRepair"},{"tagname":"cfg","owner":"Ext.dd.DragSource","meta":{},"name":"ddGroup","id":"cfg-ddGroup"},{"tagname":"cfg","owner":"Ext.dd.DragSource","meta":{},"name":"dropAllowed","id":"cfg-dropAllowed"},{"tagname":"cfg","owner":"Ext.dd.DragSource","meta":{},"name":"dropNotAllowed","id":"cfg-dropNotAllowed"},{"tagname":"cfg","owner":"Ext.dd.DragSource","meta":{},"name":"repairHighlightColor","id":"cfg-repairHighlightColor"}],"css_var":[],"method":[{"tagname":"method","owner":"Ext.draw.SpriteDD","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"Ext.dd.DDProxy","meta":{"private":true},"name":"_resizeProxy","id":"method-_resizeProxy"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"addInvalidHandleClass","id":"method-addInvalidHandleClass"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"addInvalidHandleId","id":"method-addInvalidHandleId"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"addInvalidHandleType","id":"method-addInvalidHandleType"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"addToGroup","id":"method-addToGroup"},{"tagname":"method","owner":"Ext.dd.DDProxy","meta":{"private":true},"name":"afterDrag","id":"method-afterDrag"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{},"name":"afterDragDrop","id":"method-afterDragDrop"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{},"name":"afterDragEnter","id":"method-afterDragEnter"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{},"name":"afterDragOut","id":"method-afterDragOut"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{},"name":"afterDragOver","id":"method-afterDragOver"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{},"name":"afterInvalidDrop","id":"method-afterInvalidDrop"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"private":true},"name":"afterRepair","id":"method-afterRepair"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{},"name":"afterValidDrop","id":"method-afterValidDrop"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{},"name":"alignElWithMouse","id":"method-alignElWithMouse"},{"tagname":"method","owner":"Ext.dd.DDProxy","meta":{},"name":"applyConfig","id":"method-applyConfig"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"private":true},"name":"autoOffset","id":"method-autoOffset"},{"tagname":"method","owner":"Ext.dd.DD","meta":{"private":true},"name":"autoScroll","id":"method-autoScroll"},{"tagname":"method","owner":"Ext.dd.DD","meta":{},"name":"b4Drag","id":"method-b4Drag"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"b4DragDrop","id":"method-b4DragDrop"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"b4DragOut","id":"method-b4DragOut"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"b4DragOver","id":"method-b4DragOver"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"private":true},"name":"b4EndDrag","id":"method-b4EndDrag"},{"tagname":"method","owner":"Ext.dd.DDProxy","meta":{},"name":"b4MouseDown","id":"method-b4MouseDown"},{"tagname":"method","owner":"Ext.dd.DDProxy","meta":{"private":true},"name":"b4StartDrag","id":"method-b4StartDrag"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"template":true},"name":"beforeDragDrop","id":"method-beforeDragDrop"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"template":true},"name":"beforeDragEnter","id":"method-beforeDragEnter"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"template":true},"name":"beforeDragOut","id":"method-beforeDragOut"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"template":true},"name":"beforeDragOver","id":"method-beforeDragOver"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"template":true},"name":"beforeInvalidDrop","id":"method-beforeInvalidDrop"},{"tagname":"method","owner":"Ext.dd.DDProxy","meta":{"private":true},"name":"beforeMove","id":"method-beforeMove"},{"tagname":"method","owner":"Ext.dd.DD","meta":{},"name":"cachePosition","id":"method-cachePosition"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"clearConstraints","id":"method-clearConstraints"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"clearTicks","id":"method-clearTicks"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"clickValidator","id":"method-clickValidator"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"constrainTo","id":"method-constrainTo"},{"tagname":"method","owner":"Ext.draw.SpriteDD","meta":{},"name":"createFrame","id":"method-createFrame"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"private":true},"name":"endDrag","id":"method-endDrag"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{},"name":"getDragData","id":"method-getDragData"},{"tagname":"method","owner":"Ext.draw.SpriteDD","meta":{},"name":"getDragEl","id":"method-getDragEl"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"getEl","id":"method-getEl"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.dd.DD","meta":{"private":true},"name":"getLocalX","id":"method-getLocalX"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{},"name":"getProxy","id":"method-getProxy"},{"tagname":"method","owner":"Ext.draw.SpriteDD","meta":{"private":true},"name":"getRegion","id":"method-getRegion"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"private":true},"name":"getRepairXY","id":"method-getRepairXY"},{"tagname":"method","owner":"Ext.dd.DD","meta":{"private":true},"name":"getTargetCoord","id":"method-getTargetCoord"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"getTick","id":"method-getTick"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"private":true},"name":"handleMouseDown","id":"method-handleMouseDown"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"handleOnAvailable","id":"method-handleOnAvailable"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{},"name":"hideProxy","id":"method-hideProxy"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"init","id":"method-init"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.dd.DDProxy","meta":{},"name":"initFrame","id":"method-initFrame"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"initTarget","id":"method-initTarget"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"isLocked","id":"method-isLocked"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"isValidHandleChild","id":"method-isValidHandleChild"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"lock","id":"method-lock"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"onAvailable","id":"method-onAvailable"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"template":true},"name":"onBeforeDrag","id":"method-onBeforeDrag"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.draw.SpriteDD","meta":{},"name":"onDrag","id":"method-onDrag"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"private":true},"name":"onDragDrop","id":"method-onDragDrop"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"private":true},"name":"onDragEnter","id":"method-onDragEnter"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"private":true},"name":"onDragOut","id":"method-onDragOut"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"private":true},"name":"onDragOver","id":"method-onDragOver"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"private":true},"name":"onEndDrag","id":"method-onEndDrag"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"private":true},"name":"onInitDrag","id":"method-onInitDrag"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"private":true},"name":"onInvalidDrop","id":"method-onInvalidDrop"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"onMouseDown","id":"method-onMouseDown"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"onMouseUp","id":"method-onMouseUp"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"template":true},"name":"onStartDrag","id":"method-onStartDrag"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"private":true},"name":"onValidDrop","id":"method-onValidDrop"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"removeFromGroup","id":"method-removeFromGroup"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"removeInvalidHandleClass","id":"method-removeInvalidHandleClass"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"removeInvalidHandleId","id":"method-removeInvalidHandleId"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"removeInvalidHandleType","id":"method-removeInvalidHandleType"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"resetConstraints","id":"method-resetConstraints"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.dd.DD","meta":{},"name":"setDelta","id":"method-setDelta"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"setDragElId","id":"method-setDragElId"},{"tagname":"method","owner":"Ext.draw.SpriteDD","meta":{},"name":"setDragElPos","id":"method-setDragElPos"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"setHandleElId","id":"method-setHandleElId"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"setInitPosition","id":"method-setInitPosition"},{"tagname":"method","owner":"Ext.dd.DD","meta":{"private":true},"name":"setLocalXY","id":"method-setLocalXY"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"setOuterHandleElId","id":"method-setOuterHandleElId"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"setPadding","id":"method-setPadding"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"setStartPosition","id":"method-setStartPosition"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"setXConstraint","id":"method-setXConstraint"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"setXTicks","id":"method-setXTicks"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"setYConstraint","id":"method-setYConstraint"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{"private":true},"name":"setYTicks","id":"method-setYTicks"},{"tagname":"method","owner":"Ext.draw.SpriteDD","meta":{"private":true},"name":"showFrame","id":"method-showFrame"},{"tagname":"method","owner":"Ext.draw.SpriteDD","meta":{},"name":"startDrag","id":"method-startDrag"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"},{"tagname":"method","owner":"Ext.dd.DDProxy","meta":{},"name":"toString","id":"method-toString"},{"tagname":"method","owner":"Ext.dd.DragSource","meta":{"private":true},"name":"triggerCacheRefresh","id":"method-triggerCacheRefresh"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"unlock","id":"method-unlock"},{"tagname":"method","owner":"Ext.dd.DragDrop","meta":{},"name":"unreg","id":"method-unreg"}],"event":[],"css_mixin":[]},"inheritable":null,"private":true,"component":false,"name":"Ext.draw.SpriteDD","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.draw.SpriteDD","mixins":[],"mixedInto":[]});