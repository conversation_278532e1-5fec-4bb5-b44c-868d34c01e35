Ext.data.JsonP.Ext_data_proxy_JsonP({"alternateClassNames":["Ext.data.ScriptTagProxy"],"aliases":{"proxy":["jsonp","scripttag"]},"enum":null,"parentMixins":["Ext.util.Observable"],"tagname":"class","subclasses":[],"extends":"Ext.data.proxy.Server","uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Alternate names</h4><div class='alternate-class-name'>Ext.data.ScriptTagProxy</div><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='docClass'>Ext.data.proxy.Proxy</a><div class='subclass '><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='docClass'>Ext.data.proxy.Server</a><div class='subclass '><strong>Ext.data.proxy.JsonP</strong></div></div></div></div><h4>Inherited mixins</h4><div class='dependency'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='docClass'>Ext.util.Observable</a></div><h4>Requires</h4><div class='dependency'><a href='#!/api/Ext.data.JsonP' rel='Ext.data.JsonP' class='docClass'>Ext.data.JsonP</a></div><h4>Files</h4><div class='dependency'><a href='source/JsonP2.html#Ext-data-proxy-JsonP' target='_blank'>JsonP.js</a></div></pre><div class='doc-contents'><p>The JsonP proxy is useful when you need to load data from a domain other than the one your application is running on. If\nyour application is running on http://domainA.com it cannot use <a href=\"#!/api/Ext.data.proxy.Ajax\" rel=\"Ext.data.proxy.Ajax\" class=\"docClass\">Ajax</a> to load its data\nfrom http://domainB.com because cross-domain ajax requests are prohibited by the browser.</p>\n\n<p>We can get around this using a JsonP proxy. JsonP proxy injects a <code>&lt;script&gt;</code> tag into the DOM whenever an AJAX request\nwould usually be made. Let's say we want to load data from http://domainB.com/users - the script tag that would be\ninjected might look like this:</p>\n\n<pre><code>&lt;script src=\"http://domainB.com/users?callback=someCallback\"&gt;&lt;/script&gt;\n</code></pre>\n\n<p>When we inject the tag above, the browser makes a request to that url and includes the response as if it was any\nother type of JavaScript include. By passing a callback in the url above, we're telling domainB's server that we want\nto be notified when the result comes in and that it should call our callback function with the data it sends back. So\nlong as the server formats the response to look like this, everything will work:</p>\n\n<pre><code>someCallback({\n    users: [\n        {\n            id: 1,\n            name: \"Ed Spencer\",\n            email: \"<EMAIL>\"\n        }\n    ]\n});\n</code></pre>\n\n<p>As soon as the script finishes loading, the 'someCallback' function that we passed in the url is called with the JSON\nobject that the server returned.</p>\n\n<p>JsonP proxy takes care of all of this automatically. It formats the url you pass, adding the callback parameter\nautomatically. It even creates a temporary callback function, waits for it to be called and then puts the data into\nthe Proxy making it look just like you loaded it through a normal <a href=\"#!/api/Ext.data.proxy.Ajax\" rel=\"Ext.data.proxy.Ajax\" class=\"docClass\">AjaxProxy</a>. Here's how\nwe might set that up:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('User', {\n    extend: '<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>',\n    fields: ['id', 'name', 'email']\n});\n\nvar store = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a>', {\n    model: 'User',\n    proxy: {\n        type: 'jsonp',\n        url : 'http://domainB.com/users'\n    }\n});\n\nstore.load();\n</code></pre>\n\n<p>That's all we need to do - JsonP proxy takes care of the rest. In this case the Proxy will have injected a script tag\nlike this:</p>\n\n<pre><code>&lt;script src=\"http://domainB.com/users?callback=callback1\"&gt;&lt;/script&gt;\n</code></pre>\n\n<h1>Customization</h1>\n\n<p>This script tag can be customized using the <a href=\"#!/api/Ext.data.proxy.JsonP-cfg-callbackKey\" rel=\"Ext.data.proxy.JsonP-cfg-callbackKey\" class=\"docClass\">callbackKey</a> configuration. For example:</p>\n\n<pre><code>var store = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a>', {\n    model: 'User',\n    proxy: {\n        type: 'jsonp',\n        url : 'http://domainB.com/users',\n        callbackKey: 'theCallbackFunction'\n    }\n});\n\nstore.load();\n</code></pre>\n\n<p>Would inject a script tag like this:</p>\n\n<pre><code>&lt;script src=\"http://domainB.com/users?theCallbackFunction=callback1\"&gt;&lt;/script&gt;\n</code></pre>\n\n<h1>Implementing on the server side</h1>\n\n<p>The remote server side needs to be configured to return data in this format. Here are suggestions for how you might\nachieve this using Java, PHP and ASP.net:</p>\n\n<p>Java:</p>\n\n<pre><code>boolean jsonP = false;\nString cb = request.getParameter(\"callback\");\nif (cb != null) {\n    jsonP = true;\n    response.setContentType(\"text/javascript\");\n} else {\n    response.setContentType(\"application/x-json\");\n}\nWriter out = response.getWriter();\nif (jsonP) {\n    out.write(cb + \"(\");\n}\nout.print(dataBlock.toJsonString());\nif (jsonP) {\n    out.write(\");\");\n}\n</code></pre>\n\n<p>PHP:</p>\n\n<pre><code>$callback = $_REQUEST['callback'];\n\n// Create the output object.\n$output = array('a' =&gt; 'Apple', 'b' =&gt; 'Banana');\n\n//start output\nif ($callback) {\n    header('Content-Type: text/javascript');\n    echo $callback . '(' . json_encode($output) . ');';\n} else {\n    header('Content-Type: application/x-json');\n    echo json_encode($output);\n}\n</code></pre>\n\n<p>ASP.net:</p>\n\n<pre><code>String jsonString = \"{success: true}\";\nString cb = Request.Params.Get(\"callback\");\nString responseString = \"\";\nif (!String.IsNullOrEmpty(cb)) {\n    responseString = cb + \"(\" + jsonString + \")\";\n} else {\n    responseString = jsonString;\n}\nResponse.Write(responseString);\n</code></pre>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-cfg'>Config options</h3><div class='subsection'><div id='cfg-api' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-cfg-api' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-cfg-api' class='name expandable'>api</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>Specific urls to call on CRUD action methods \"create\", \"read\", \"update\" and \"destroy\". ...</div><div class='long'><p>Specific urls to call on CRUD action methods \"create\", \"read\", \"update\" and \"destroy\". Defaults to:</p>\n\n<pre><code>api: {\n    create  : undefined,\n    read    : undefined,\n    update  : undefined,\n    destroy : undefined\n}\n</code></pre>\n\n<p>The url is built based upon the action being executed [create|read|update|destroy] using the commensurate\n<a href=\"#!/api/Ext.data.proxy.Server-cfg-api\" rel=\"Ext.data.proxy.Server-cfg-api\" class=\"docClass\">api</a> property, or if undefined default to the configured\n<a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a>.<a href=\"#!/api/Ext.data.proxy.Server-cfg-url\" rel=\"Ext.data.proxy.Server-cfg-url\" class=\"docClass\">url</a>.</p>\n\n<p>For example:</p>\n\n<pre><code>api: {\n    create  : '/controller/new',\n    read    : '/controller/load',\n    update  : '/controller/update',\n    destroy : '/controller/destroy_action'\n}\n</code></pre>\n\n<p>If the specific URL for a given CRUD action is undefined, the CRUD action request will be directed to the\nconfigured <a href=\"#!/api/Ext.data.proxy.Server-cfg-url\" rel=\"Ext.data.proxy.Server-cfg-url\" class=\"docClass\">url</a>.</p>\n</div></div></div><div id='cfg-autoAppendParams' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.proxy.JsonP'>Ext.data.proxy.JsonP</span><br/><a href='source/JsonP2.html#Ext-data-proxy-JsonP-cfg-autoAppendParams' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.JsonP-cfg-autoAppendParams' class='name expandable'>autoAppendParams</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True to automatically append the request's params to the generated url. ...</div><div class='long'><p>True to automatically append the request's params to the generated url. Defaults to true</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-batchActions' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-cfg-batchActions' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-cfg-batchActions' class='name expandable'>batchActions</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True to batch actions of a particular type when synchronizing the store. ...</div><div class='long'><p>True to batch actions of a particular type when synchronizing the store. Defaults to true.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-batchOrder' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-cfg-batchOrder' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-cfg-batchOrder' class='name expandable'>batchOrder</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Comma-separated ordering 'create', 'update' and 'destroy' actions when batching. ...</div><div class='long'><p>Comma-separated ordering 'create', 'update' and 'destroy' actions when batching. Override this to set a different\norder for the batched CRUD actions to be executed in. Defaults to 'create,update,destroy'.</p>\n<p>Defaults to: <code>'create,update,destroy'</code></p></div></div></div><div id='cfg-cacheString' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-cfg-cacheString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-cfg-cacheString' class='name expandable'>cacheString</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The name of the cache param added to the url when using noCache. ...</div><div class='long'><p>The name of the cache param added to the url when using noCache. Defaults to \"_dc\".</p>\n<p>Defaults to: <code>&quot;_dc&quot;</code></p></div></div></div><div id='cfg-callbackKey' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.proxy.JsonP'>Ext.data.proxy.JsonP</span><br/><a href='source/JsonP2.html#Ext-data-proxy-JsonP-cfg-callbackKey' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.JsonP-cfg-callbackKey' class='name expandable'>callbackKey</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>See Ext.data.JsonP.callbackKey. ...</div><div class='long'><p>See <a href=\"#!/api/Ext.data.JsonP-property-callbackKey\" rel=\"Ext.data.JsonP-property-callbackKey\" class=\"docClass\">Ext.data.JsonP.callbackKey</a>.</p>\n<p>Defaults to: <code>'callback'</code></p></div></div></div><div id='cfg-defaultReaderType' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-cfg-defaultReaderType' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-cfg-defaultReaderType' class='name expandable'>defaultReaderType</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>The default registered reader type. ...</div><div class='long'><p>The default registered reader type. Defaults to 'json'.</p>\n<p>Defaults to: <code>'json'</code></p></div></div></div><div id='cfg-defaultWriterType' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.proxy.JsonP'>Ext.data.proxy.JsonP</span><br/><a href='source/JsonP2.html#Ext-data-proxy-JsonP-cfg-defaultWriterType' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.JsonP-cfg-defaultWriterType' class='name expandable'>defaultWriterType</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>The default registered writer type. ...</div><div class='long'><p>The default registered writer type. Defaults to 'json'.</p>\n<p>Defaults to: <code>'base'</code></p><p>Overrides: <a href='#!/api/Ext.data.proxy.Proxy-cfg-defaultWriterType' rel='Ext.data.proxy.Proxy-cfg-defaultWriterType' class='docClass'>Ext.data.proxy.Proxy.defaultWriterType</a></p></div></div></div><div id='cfg-directionParam' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-cfg-directionParam' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-cfg-directionParam' class='name expandable'>directionParam</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The name of the direction parameter to send in a request. ...</div><div class='long'><p>The name of the direction parameter to send in a request. <strong>This is only used when simpleSortMode is set to\ntrue.</strong></p>\n<p>Defaults to: <code>&quot;dir&quot;</code></p></div></div></div><div id='cfg-extraParams' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-cfg-extraParams' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-cfg-extraParams' class='name expandable'>extraParams</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>Extra parameters that will be included on every request. ...</div><div class='long'><p>Extra parameters that will be included on every request. Individual requests with params of the same name\nwill override these params when they are in conflict.</p>\n</div></div></div><div id='cfg-filterParam' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-cfg-filterParam' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-cfg-filterParam' class='name expandable'>filterParam</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The name of the 'filter' parameter to send in a request. ...</div><div class='long'><p>The name of the 'filter' parameter to send in a request. Defaults to 'filter'. Set this to undefined if you don't\nwant to send a filter parameter.</p>\n<p>Defaults to: <code>&quot;filter&quot;</code></p></div></div></div><div id='cfg-groupDirectionParam' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-cfg-groupDirectionParam' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-cfg-groupDirectionParam' class='name expandable'>groupDirectionParam</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The name of the direction parameter to send in a request. ...</div><div class='long'><p>The name of the direction parameter to send in a request. <strong>This is only used when simpleGroupMode is set to\ntrue.</strong></p>\n<p>Defaults to: <code>&quot;groupDir&quot;</code></p></div></div></div><div id='cfg-groupParam' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-cfg-groupParam' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-cfg-groupParam' class='name expandable'>groupParam</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The name of the 'group' parameter to send in a request. ...</div><div class='long'><p>The name of the 'group' parameter to send in a request. Defaults to 'group'. Set this to undefined if you don't\nwant to send a group parameter.</p>\n<p>Defaults to: <code>&quot;group&quot;</code></p></div></div></div><div id='cfg-idParam' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-cfg-idParam' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-cfg-idParam' class='name expandable'>idParam</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The name of the parameter which carries the id of the entity being operated upon. ...</div><div class='long'><p>The name of the parameter which carries the id of the entity being operated upon.</p>\n<p>Defaults to: <code>&quot;id&quot;</code></p></div></div></div><div id='cfg-limitParam' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-cfg-limitParam' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-cfg-limitParam' class='name expandable'>limitParam</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The name of the 'limit' parameter to send in a request. ...</div><div class='long'><p>The name of the 'limit' parameter to send in a request. Defaults to 'limit'. Set this to undefined if you don't\nwant to send a limit parameter.</p>\n<p>Defaults to: <code>&quot;limit&quot;</code></p></div></div></div><div id='cfg-listeners' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-cfg-listeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-cfg-listeners' class='name expandable'>listeners</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>A config object containing one or more event handlers to be added to this object during initialization. ...</div><div class='long'><p>A config object containing one or more event handlers to be added to this object during initialization. This\nshould be a valid listeners config object as specified in the <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> example for attaching multiple\nhandlers at once.</p>\n\n<p><strong>DOM events from Ext JS <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a></strong></p>\n\n<p>While <em>some</em> Ext JS Component classes export selected DOM events (e.g. \"click\", \"mouseover\" etc), this is usually\nonly done when extra value can be added. For example the <a href=\"#!/api/Ext.view.View\" rel=\"Ext.view.View\" class=\"docClass\">DataView</a>'s <strong><code><a href=\"#!/api/Ext.view.View-event-itemclick\" rel=\"Ext.view.View-event-itemclick\" class=\"docClass\">itemclick</a></code></strong> event passing the node clicked on. To access DOM events directly from a\nchild element of a Component, we need to specify the <code>element</code> option to identify the Component property to add a\nDOM listener to:</p>\n\n<pre><code>new <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>({\n    width: 400,\n    height: 200,\n    dockedItems: [{\n        xtype: 'toolbar'\n    }],\n    listeners: {\n        click: {\n            element: 'el', //bind to the underlying el property on the panel\n            fn: function(){ console.log('click el'); }\n        },\n        dblclick: {\n            element: 'body', //bind to the underlying body property on the panel\n            fn: function(){ console.log('dblclick body'); }\n        }\n    }\n});\n</code></pre>\n</div></div></div><div id='cfg-model' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-cfg-model' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-cfg-model' class='name expandable'>model</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></span></div><div class='description'><div class='short'>The name of the Model to tie to this Proxy. ...</div><div class='long'><p>The name of the Model to tie to this Proxy. Can be either the string name of the Model, or a reference to the\nModel constructor. Required.</p>\n</div></div></div><div id='cfg-noCache' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-cfg-noCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-cfg-noCache' class='name expandable'>noCache</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Disable caching by adding a unique parameter name to the request. ...</div><div class='long'><p>Disable caching by adding a unique parameter name to the request. Set to false to allow caching. Defaults to true.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-pageParam' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-cfg-pageParam' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-cfg-pageParam' class='name expandable'>pageParam</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The name of the 'page' parameter to send in a request. ...</div><div class='long'><p>The name of the 'page' parameter to send in a request. Defaults to 'page'. Set this to undefined if you don't\nwant to send a page parameter.</p>\n<p>Defaults to: <code>&quot;page&quot;</code></p></div></div></div><div id='cfg-reader' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-cfg-reader' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-cfg-reader' class='name expandable'>reader</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.data.reader.Reader\" rel=\"Ext.data.reader.Reader\" class=\"docClass\">Ext.data.reader.Reader</a></span></div><div class='description'><div class='short'>The Ext.data.reader.Reader to use to decode the server's response or data read from client. ...</div><div class='long'><p>The <a href=\"#!/api/Ext.data.reader.Reader\" rel=\"Ext.data.reader.Reader\" class=\"docClass\">Ext.data.reader.Reader</a> to use to decode the server's response or data read from client. This can either be a\nReader instance, a config object or just a valid Reader type name (e.g. 'json', 'xml').</p>\n</div></div></div><div id='cfg-recordParam' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.proxy.JsonP'>Ext.data.proxy.JsonP</span><br/><a href='source/JsonP2.html#Ext-data-proxy-JsonP-cfg-recordParam' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.JsonP-cfg-recordParam' class='name expandable'>recordParam</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The HTTP parameter name to use when passing records to the server and the Json writer is not configured\nto encode rec...</div><div class='long'><p>The HTTP parameter name to use when passing records to the server and the <a href=\"#!/api/Ext.data.proxy.JsonP-cfg-writer\" rel=\"Ext.data.proxy.JsonP-cfg-writer\" class=\"docClass\">Json writer</a> is not configured\nto <a href=\"#!/api/Ext.data.writer.Json-cfg-encode\" rel=\"Ext.data.writer.Json-cfg-encode\" class=\"docClass\">encode</a> records into a parameter.</p>\n\n<p>The <a href=\"#!/api/Ext.data.proxy.JsonP-method-encodeRecords\" rel=\"Ext.data.proxy.JsonP-method-encodeRecords\" class=\"docClass\">encodeRecords</a> method is used to encode the records to create this parameter's value.</p>\n<p>Defaults to: <code>records</code></p></div></div></div><div id='cfg-simpleGroupMode' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-cfg-simpleGroupMode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-cfg-simpleGroupMode' class='name expandable'>simpleGroupMode</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Enabling simpleGroupMode in conjunction with remoteGroup will only send one group property and a direction when a\nrem...</div><div class='long'><p>Enabling simpleGroupMode in conjunction with remoteGroup will only send one group property and a direction when a\nremote group is requested. The <a href=\"#!/api/Ext.data.proxy.Server-cfg-groupDirectionParam\" rel=\"Ext.data.proxy.Server-cfg-groupDirectionParam\" class=\"docClass\">groupDirectionParam</a> and <a href=\"#!/api/Ext.data.proxy.Server-cfg-groupParam\" rel=\"Ext.data.proxy.Server-cfg-groupParam\" class=\"docClass\">groupParam</a> will be sent with the property name and either 'ASC'\nor 'DESC'.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-simpleSortMode' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-cfg-simpleSortMode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-cfg-simpleSortMode' class='name expandable'>simpleSortMode</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Enabling simpleSortMode in conjunction with remoteSort will only send one sort property and a direction when a\nremote...</div><div class='long'><p>Enabling simpleSortMode in conjunction with remoteSort will only send one sort property and a direction when a\nremote sort is requested. The <a href=\"#!/api/Ext.data.proxy.Server-cfg-directionParam\" rel=\"Ext.data.proxy.Server-cfg-directionParam\" class=\"docClass\">directionParam</a> and <a href=\"#!/api/Ext.data.proxy.Server-cfg-sortParam\" rel=\"Ext.data.proxy.Server-cfg-sortParam\" class=\"docClass\">sortParam</a> will be sent with the property name\nand either 'ASC' or 'DESC'.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-sortParam' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-cfg-sortParam' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-cfg-sortParam' class='name expandable'>sortParam</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The name of the 'sort' parameter to send in a request. ...</div><div class='long'><p>The name of the 'sort' parameter to send in a request. Defaults to 'sort'. Set this to undefined if you don't\nwant to send a sort parameter.</p>\n<p>Defaults to: <code>&quot;sort&quot;</code></p></div></div></div><div id='cfg-startParam' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-cfg-startParam' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-cfg-startParam' class='name expandable'>startParam</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The name of the 'start' parameter to send in a request. ...</div><div class='long'><p>The name of the 'start' parameter to send in a request. Defaults to 'start'. Set this to undefined if you don't\nwant to send a start parameter.</p>\n<p>Defaults to: <code>&quot;start&quot;</code></p></div></div></div><div id='cfg-timeout' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-cfg-timeout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-cfg-timeout' class='name expandable'>timeout</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The number of milliseconds to wait for a response. ...</div><div class='long'><p>The number of milliseconds to wait for a response. Defaults to 30000 milliseconds (30 seconds).</p>\n<p>Defaults to: <code>30000</code></p></div></div></div><div id='cfg-url' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-cfg-url' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-cfg-url' class='name not-expandable'>url</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'><p>The URL from which to request the data object.</p>\n</div><div class='long'><p>The URL from which to request the data object.</p>\n</div></div></div><div id='cfg-writer' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-cfg-writer' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-cfg-writer' class='name expandable'>writer</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.data.writer.Writer\" rel=\"Ext.data.writer.Writer\" class=\"docClass\">Ext.data.writer.Writer</a></span></div><div class='description'><div class='short'>The Ext.data.writer.Writer to use to encode any request sent to the server or saved to client. ...</div><div class='long'><p>The <a href=\"#!/api/Ext.data.writer.Writer\" rel=\"Ext.data.writer.Writer\" class=\"docClass\">Ext.data.writer.Writer</a> to use to encode any request sent to the server or saved to client. This can either be\na Writer instance, a config object or just a valid Writer type name (e.g. 'json', 'xml').</p>\n</div></div></div></div></div><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-eventsSuspended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-property-eventsSuspended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-property-eventsSuspended' class='name expandable'>eventsSuspended</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Initial suspended call count. ...</div><div class='long'><p>Initial suspended call count. Incremented when <a href=\"#!/api/Ext.util.Observable-method-suspendEvents\" rel=\"Ext.util.Observable-method-suspendEvents\" class=\"docClass\">suspendEvents</a> is called, decremented when <a href=\"#!/api/Ext.util.Observable-method-resumeEvents\" rel=\"Ext.util.Observable-method-resumeEvents\" class=\"docClass\">resumeEvents</a> is called.</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-hasListeners' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-property-hasListeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-property-hasListeners' class='name expandable'>hasListeners</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='readonly signature' >readonly</strong></div><div class='description'><div class='short'>This object holds a key for any event that has a listener. ...</div><div class='long'><p>This object holds a key for any event that has a listener. The listener may be set\ndirectly on the instance, or on its class or a super class (via <a href=\"#!/api/Ext.util.Observable-static-method-observe\" rel=\"Ext.util.Observable-static-method-observe\" class=\"docClass\">observe</a>) or\non the <a href=\"#!/api/Ext.app.EventBus\" rel=\"Ext.app.EventBus\" class=\"docClass\">MVC EventBus</a>. The values of this object are truthy\n(a non-zero number) and falsy (0 or undefined). They do not represent an exact count\nof listeners. The value for an event is truthy if the event must be fired and is\nfalsy if there is no need to fire the event.</p>\n\n<p>The intended use of this property is to avoid the expense of fireEvent calls when\nthere are no listeners. This can be particularly helpful when one would otherwise\nhave to call fireEvent hundreds or thousands of times. It is used like this:</p>\n\n<pre><code> if (this.hasListeners.foo) {\n     this.fireEvent('foo', this, arg1);\n }\n</code></pre>\n</div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isObservable' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-property-isObservable' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-property-isObservable' class='name expandable'>isObservable</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true in this class to identify an object as an instantiated Observable, or subclass thereof. ...</div><div class='long'><p><code>true</code> in this class to identify an object as an instantiated Observable, or subclass thereof.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isProxy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-property-isProxy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-property-isProxy' class='name expandable'>isProxy</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true in this class to identify an object as an instantiated Proxy, or subclass thereof. ...</div><div class='long'><p><code>true</code> in this class to identify an object as an instantiated Proxy, or subclass thereof.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isSynchronous' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-property-isSynchronous' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-property-isSynchronous' class='name expandable'>isSynchronous</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Identifies the proxy as (a)synchronous. ...</div><div class='long'><p>Identifies the proxy as (a)synchronous.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.proxy.JsonP'>Ext.data.proxy.JsonP</span><br/><a href='source/JsonP2.html#Ext-data-proxy-JsonP-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/Ext.data.proxy.JsonP-method-constructor' class='name expandable'>Ext.data.proxy.JsonP</a>( <span class='pre'>[config]</span> ) : <a href=\"#!/api/Ext.data.proxy.JsonP\" rel=\"Ext.data.proxy.JsonP\" class=\"docClass\">Ext.data.proxy.JsonP</a></div><div class='description'><div class='short'>Creates the Proxy ...</div><div class='long'><p>Creates the Proxy</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Config object.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.proxy.JsonP\" rel=\"Ext.data.proxy.JsonP\" class=\"docClass\">Ext.data.proxy.JsonP</a></span><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.proxy.Proxy-method-constructor' rel='Ext.data.proxy.Proxy-method-constructor' class='docClass'>Ext.data.proxy.Proxy.constructor</a></p></div></div></div><div id='method-abort' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.proxy.JsonP'>Ext.data.proxy.JsonP</span><br/><a href='source/JsonP2.html#Ext-data-proxy-JsonP-method-abort' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.JsonP-method-abort' class='name expandable'>abort</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Aborts the current server request if one is currently running ...</div><div class='long'><p>Aborts the current server request if one is currently running</p>\n</div></div></div><div id='method-addEvents' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-addEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-addEvents' class='name expandable'>addEvents</a>( <span class='pre'>eventNames</span> )</div><div class='description'><div class='short'>Adds the specified events to the list of events which this Observable may fire. ...</div><div class='long'><p>Adds the specified events to the list of events which this Observable may fire.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventNames</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>...<div class='sub-desc'><p>Either an object with event names as properties with\na value of <code>true</code>. For example:</p>\n\n<pre><code>this.addEvents({\n    storeloaded: true,\n    storecleared: true\n});\n</code></pre>\n\n<p>Or any number of event names as separate parameters. For example:</p>\n\n<pre><code>this.addEvents('storeloaded', 'storecleared');\n</code></pre>\n</div></li></ul></div></div></div><div id='method-addListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-addListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-addListener' class='name expandable'>addListener</a>( <span class='pre'>eventName, [fn], [scope], [options]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Appends an event handler to this object. ...</div><div class='long'><p>Appends an event handler to this object.  For example:</p>\n\n<pre><code>myGridPanel.on(\"mouseover\", this.onMouseOver, this);\n</code></pre>\n\n<p>The method also allows for a single argument to be passed which is a config object\ncontaining properties which specify multiple events. For example:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: this.onCellClick,\n    mouseover: this.onMouseOver,\n    mouseout: this.onMouseOut,\n    scope: this // Important. Ensure \"this\" is correct during handler execution\n});\n</code></pre>\n\n<p>One can also specify options for each event handler separately:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: this.onCellClick, scope: this, single: true},\n    mouseover: {fn: panel.onMouseOver, scope: panel}\n});\n</code></pre>\n\n<p><em>Names</em> of methods in a specified scope may also be used. Note that\n<code>scope</code> MUST be specified to use this option:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: 'onCellClick', scope: this, single: true},\n    mouseover: {fn: 'onMouseOver', scope: panel}\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The name of the event to listen for.\nMay also be an object who's property names are event names.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The method the event invokes, or <em>if <code>scope</code> is specified, the </em>name* of the method within\nthe specified <code>scope</code>.  Will be called with arguments\ngiven to <a href=\"#!/api/Ext.util.Observable-method-fireEvent\" rel=\"Ext.util.Observable-method-fireEvent\" class=\"docClass\">fireEvent</a> plus the <code>options</code> parameter described below.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is\nexecuted. <strong>If omitted, defaults to the object which fired the event.</strong></p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>An object containing handler configuration.</p>\n\n\n\n\n<p><strong>Note:</strong> Unlike in ExtJS 3.x, the options object will also be passed as the last\nargument to every event handler.</p>\n\n\n\n\n<p>This object may contain any of the following properties:</p>\n\n<ul><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is executed. <strong>If omitted,\n  defaults to the object which fired the event.</strong></p>\n\n</div></li><li><span class='pre'>delay</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of milliseconds to delay the invocation of the handler after the event fires.</p>\n\n</div></li><li><span class='pre'>single</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to add a handler to handle just the next firing of the event, and then remove itself.</p>\n\n</div></li><li><span class='pre'>buffer</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Causes the handler to be scheduled to run in an <a href=\"#!/api/Ext.util.DelayedTask\" rel=\"Ext.util.DelayedTask\" class=\"docClass\">Ext.util.DelayedTask</a> delayed\n  by the specified number of milliseconds. If the event fires again within that time,\n  the original handler is <em>not</em> invoked, but the new handler is scheduled in its place.</p>\n\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a><div class='sub-desc'><p>Only call the handler if the event was fired on the target Observable, <em>not</em> if the event\n  was bubbled up from a child Observable.</p>\n\n</div></li><li><span class='pre'>element</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p><strong>This option is only valid for listeners bound to <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a>.</strong>\n  The name of a Component property which references an element to add a listener to.</p>\n\n\n\n\n<p>  This option is useful during Component construction to add DOM event listeners to elements of\n  <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a> which will exist only after the Component is rendered.\n  For example, to add a click listener to a Panel's body:</p>\n\n\n\n\n<pre><code>  new <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>({\n      title: 'The title',\n      listeners: {\n          click: this.handlePanelClick,\n          element: 'body'\n      }\n  });\n</code></pre>\n\n</div></li><li><span class='pre'>destroyable</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>When specified as <code>true</code>, the function returns A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call.</p>\n\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>priority</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>An optional numeric priority that determines the order in which event handlers\n  are run. Event handlers with no priority will be run as if they had a priority\n  of 0. Handlers with a higher priority will be prioritized to run sooner than\n  those with a lower priority.  Negative numbers can be used to set a priority\n  lower than the default. Internally, the framework uses a range of 1000 or\n  greater, and -1000 or lesser for handers that are intended to run before or\n  after all others, so it is recommended to stay within the range of -999 to 999\n  when setting the priority of event handlers in application-level code.</p>\n\n\n\n\n<p><strong>Combining Options</strong></p>\n\n\n\n\n<p>Using the options argument, it is possible to combine different types of listeners:</p>\n\n\n\n\n<p>A delayed, one-time listener.</p>\n\n\n\n\n<pre><code>myPanel.on('hide', this.handleClick, this, {\n    single: true,\n    delay: 100\n});\n</code></pre>\n\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><strong>Only when the <code>destroyable</code> option is specified. </strong></p>\n\n\n\n\n<p> A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call. For example:</p>\n\n\n\n\n<pre><code>this.btnListeners =  = myButton.on({\n    destroyable: true\n    mouseover:   function() { console.log('mouseover'); },\n    mouseout:    function() { console.log('mouseout'); },\n    click:       function() { console.log('click'); }\n});\n</code></pre>\n\n\n\n\n<p>And when those listeners need to be removed:</p>\n\n\n\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.btnListeners);\n</code></pre>\n\n\n\n\n<p>or</p>\n\n\n\n\n<pre><code>this.btnListeners.destroy();\n</code></pre>\n\n</div></li></ul></div></div></div><div id='method-addManagedListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-addManagedListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-addManagedListener' class='name expandable'>addManagedListener</a>( <span class='pre'>item, ename, [fn], [scope], [options]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Adds listeners to any Observable object (or Ext.Element) which are automatically removed when this Component is\ndestr...</div><div class='long'><p>Adds listeners to any Observable object (or <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>) which are automatically removed when this Component is\ndestroyed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The item to which to add a listener/listeners.</p>\n\n</div></li><li><span class='pre'>ename</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The event name, or an object containing event name properties.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the handler function.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the scope (<code>this</code> reference)\nin which the handler function is executed.</p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the\n<a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> options.</p>\n\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><strong>Only when the <code>destroyable</code> option is specified. </strong></p>\n\n\n\n\n<p> A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call. For example:</p>\n\n\n\n\n<pre><code>this.btnListeners =  = myButton.mon({\n    destroyable: true\n    mouseover:   function() { console.log('mouseover'); },\n    mouseout:    function() { console.log('mouseout'); },\n    click:       function() { console.log('click'); }\n});\n</code></pre>\n\n\n\n\n<p>And when those listeners need to be removed:</p>\n\n\n\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.btnListeners);\n</code></pre>\n\n\n\n\n<p>or</p>\n\n\n\n\n<pre><code>this.btnListeners.destroy();\n</code></pre>\n\n</div></li></ul></div></div></div><div id='method-afterRequest' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-method-afterRequest' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-method-afterRequest' class='name expandable'>afterRequest</a>( <span class='pre'>request, success</span> )<strong class='protected signature' >protected</strong><strong class='template signature' >template</strong></div><div class='description'><div class='short'>Optional callback function which can be used to clean up after a request has been completed. ...</div><div class='long'><p>Optional callback function which can be used to clean up after a request has been completed.</p>\n      <div class='signature-box template'>\n      <p>This is a <a href=\"#!/guide/components\">template method</a>.\n         a hook into the functionality of this class.\n         Feel free to override it in child classes.</p>\n      </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>request</span> : <a href=\"#!/api/Ext.data.Request\" rel=\"Ext.data.Request\" class=\"docClass\">Ext.data.Request</a><div class='sub-desc'><p>The Request object</p>\n</div></li><li><span class='pre'>success</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True if the request was successful</p>\n</div></li></ul></div></div></div><div id='method-applyEncoding' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-method-applyEncoding' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-method-applyEncoding' class='name expandable'>applyEncoding</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Encode any values being sent to the server. ...</div><div class='long'><p>Encode any values being sent to the server. Can be overridden in subclasses.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>An array of sorters/filters.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The encoded value</p>\n</div></li></ul></div></div></div><div id='method-batch' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-method-batch' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-method-batch' class='name expandable'>batch</a>( <span class='pre'>options</span> ) : <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a></div><div class='description'><div class='short'>Performs a batch of Operations, in the order specified by batchOrder. ...</div><div class='long'><p>Performs a batch of <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Operations</a>, in the order specified by <a href=\"#!/api/Ext.data.proxy.Proxy-cfg-batchOrder\" rel=\"Ext.data.proxy.Proxy-cfg-batchOrder\" class=\"docClass\">batchOrder</a>. Used\ninternally by <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a>'s <a href=\"#!/api/Ext.data.Store-method-sync\" rel=\"Ext.data.Store-method-sync\" class=\"docClass\">sync</a> method. Example usage:</p>\n\n<pre><code>myProxy.batch({\n    create : [myModel1, myModel2],\n    update : [myModel3],\n    destroy: [myModel4, myModel5]\n});\n</code></pre>\n\n<p>Where the myModel* above are <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Model</a> instances - in this case 1 and 2 are new instances and\nhave not been saved before, 3 has been saved previously but needs to be updated, and 4 and 5 have already been\nsaved but should now be destroyed.</p>\n\n<p>Note that the previous version of this method took 2 arguments (operations and listeners). While this is still\nsupported for now, the current signature is now a single <code>options</code> argument that can contain both operations and\nlisteners, in addition to other options. The multi-argument signature will likely be deprecated in a future release.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>Object containing one or more properties supported by the batch method:</p>\n<ul><li><span class='pre'>operations</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>Object containing the Model instances to act upon, keyed by action name</p>\n</div></li><li><span class='pre'>listeners</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Event listeners object passed straight through to the Batch -\nsee <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a> for details</p>\n</div></li><li><span class='pre'>batch</span> : <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>A <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a> object (or batch config to apply\nto the created batch). If unspecified a default batch will be auto-created.</p>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The function to be called upon completion of processing the batch.\nThe callback is called regardless of success or failure and is passed the following parameters:</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>batch</span> : <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">batch</a> that was processed,\ncontaining all operations in their current state after processing</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options argument that was originally passed into batch</p>\n</div></li></ul></div></li><li><span class='pre'>success</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The function to be called upon successful completion of the batch. The\nsuccess function is called only if no exceptions were reported in any operations. If one or more exceptions\noccurred then the <code>failure</code> function will be called instead. The success function is called\nwith the following parameters:</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>batch</span> : <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">batch</a> that was processed,\ncontaining all operations in their current state after processing</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options argument that was originally passed into batch</p>\n</div></li></ul></div></li><li><span class='pre'>failure</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The function to be called upon unsuccessful completion of the batch. The\nfailure function is called when one or more operations returns an exception during processing (even if some\noperations were also successful). In this case you can check the batch's <a href=\"#!/api/Ext.data.Batch-property-exceptions\" rel=\"Ext.data.Batch-property-exceptions\" class=\"docClass\">exceptions</a> array to see exactly which operations had exceptions. The failure function is called with the\nfollowing parameters:</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>batch</span> : <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">batch</a> that was processed,\ncontaining all operations in their current state after processing</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options argument that was originally passed into batch</p>\n</div></li></ul></div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope in which to execute any callbacks (i.e. the <code>this</code> object inside\nthe callback, success and/or failure functions). Defaults to the proxy.</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a></span><div class='sub-desc'><p>The newly created Batch</p>\n</div></li></ul></div></div></div><div id='method-buildRequest' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-method-buildRequest' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-method-buildRequest' class='name expandable'>buildRequest</a>( <span class='pre'>operation</span> ) : <a href=\"#!/api/Ext.data.Request\" rel=\"Ext.data.Request\" class=\"docClass\">Ext.data.Request</a></div><div class='description'><div class='short'>Creates an Request object from Operation. ...</div><div class='long'><p>Creates an <a href=\"#!/api/Ext.data.Request\" rel=\"Ext.data.Request\" class=\"docClass\">Request</a> object from <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Operation</a>.</p>\n\n<p>This gets called from doRequest methods in subclasses of Server proxy.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The operation to execute</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Request\" rel=\"Ext.data.Request\" class=\"docClass\">Ext.data.Request</a></span><div class='sub-desc'><p>The request object</p>\n</div></li></ul></div></div></div><div id='method-buildUrl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.proxy.JsonP'>Ext.data.proxy.JsonP</span><br/><a href='source/JsonP2.html#Ext-data-proxy-JsonP-method-buildUrl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.JsonP-method-buildUrl' class='name expandable'>buildUrl</a>( <span class='pre'>request</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Generates a url based on a given Ext.data.Request object. ...</div><div class='long'><p>Generates a url based on a given <a href=\"#!/api/Ext.data.Request\" rel=\"Ext.data.Request\" class=\"docClass\">Ext.data.Request</a> object. Adds the params and callback function name to the url</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>request</span> : <a href=\"#!/api/Ext.data.Request\" rel=\"Ext.data.Request\" class=\"docClass\">Ext.data.Request</a><div class='sub-desc'><p>The request object</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The url</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.proxy.Server-method-buildUrl' rel='Ext.data.proxy.Server-method-buildUrl' class='docClass'>Ext.data.proxy.Server.buildUrl</a></p></div></div></div><div id='method-callOverridden' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-captureArgs' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-captureArgs' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-captureArgs' class='name expandable'>captureArgs</a>( <span class='pre'>o, fn, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>o</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-clearListeners' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-clearListeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-clearListeners' class='name expandable'>clearListeners</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Removes all listeners for this object including the managed listeners ...</div><div class='long'><p>Removes all listeners for this object including the managed listeners</p>\n</div></div></div><div id='method-clearManagedListeners' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-clearManagedListeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-clearManagedListeners' class='name expandable'>clearManagedListeners</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Removes all managed listeners for this object. ...</div><div class='long'><p>Removes all managed listeners for this object.</p>\n</div></div></div><div id='method-clone' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-method-clone' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-method-clone' class='name expandable'>clone</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-continueFireEvent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-continueFireEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-continueFireEvent' class='name expandable'>continueFireEvent</a>( <span class='pre'>eventName, args, bubbles</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Continue to fire event. ...</div><div class='long'><p>Continue to fire event.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li><li><span class='pre'>bubbles</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-method-create' class='name expandable'>create</a>( <span class='pre'>operation, callback, scope</span> )</div><div class='description'><div class='short'>in a ServerProxy all four CRUD operations are executed in the same manner, so we delegate to doRequest in each case\n\n...</div><div class='long'><p>in a ServerProxy all four CRUD operations are executed in the same manner, so we delegate to doRequest in each case</p>\n\n<p>Performs the given create operation.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The Operation to perform</p>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Callback function to be called when the Operation has completed (whether\nsuccessful or not)</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>Scope to execute the callback function in</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.proxy.Proxy-method-create' rel='Ext.data.proxy.Proxy-method-create' class='docClass'>Ext.data.proxy.Proxy.create</a></p></div></div></div><div id='method-createRelayer' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-createRelayer' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-createRelayer' class='name expandable'>createRelayer</a>( <span class='pre'>newName, [beginEnd]</span> ) : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Creates an event handling function which refires the event from this object as the passed event name. ...</div><div class='long'><p>Creates an event handling function which refires the event from this object as the passed event name.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>newName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name under which to refire the passed parameters.</p>\n</div></li><li><span class='pre'>beginEnd</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> (optional)<div class='sub-desc'><p>The caller can specify on which indices to slice.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createRequestCallback' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.proxy.JsonP'>Ext.data.proxy.JsonP</span><br/><a href='source/JsonP2.html#Ext-data-proxy-JsonP-method-createRequestCallback' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.JsonP-method-createRequestCallback' class='name expandable'>createRequestCallback</a>( <span class='pre'>request, operation, callback, scope</span> ) : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Creates and returns the function that is called when the request has completed. ...</div><div class='long'><p>Creates and returns the function that is called when the request has completed. The returned function\nshould accept a Response object, which contains the response to be read by the configured Reader.\nThe third argument is the callback that should be called after the request has been completed and the Reader has decoded\nthe response. This callback will typically be the callback passed by a store, e.g. in proxy.read(operation, theCallback, scope)\ntheCallback refers to the callback argument received by this function.\nSee <a href=\"#!/api/Ext.data.proxy.JsonP-method-doRequest\" rel=\"Ext.data.proxy.JsonP-method-doRequest\" class=\"docClass\">doRequest</a> for details.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>request</span> : <a href=\"#!/api/Ext.data.Request\" rel=\"Ext.data.Request\" class=\"docClass\">Ext.data.Request</a><div class='sub-desc'><p>The Request object</p>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The Operation being executed</p>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The callback function to be called when the request completes. This is usually the callback\npassed to doRequest</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope in which to execute the callback function</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></span><div class='sub-desc'><p>The callback function</p>\n</div></li></ul></div></div></div><div id='method-destroy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-method-destroy' class='name expandable'>destroy</a>( <span class='pre'>operation, callback, scope</span> )</div><div class='description'><div class='short'>Performs the given destroy operation. ...</div><div class='long'><p>Performs the given destroy operation.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The Operation to perform</p>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Callback function to be called when the Operation has completed (whether\nsuccessful or not)</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>Scope to execute the callback function in</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.proxy.Proxy-method-destroy' rel='Ext.data.proxy.Proxy-method-destroy' class='docClass'>Ext.data.proxy.Proxy.destroy</a></p></div></div></div><div id='method-doRequest' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.proxy.JsonP'>Ext.data.proxy.JsonP</span><br/><a href='source/JsonP2.html#Ext-data-proxy-JsonP-method-doRequest' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.JsonP-method-doRequest' class='name expandable'>doRequest</a>( <span class='pre'>operation, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Performs the read request to the remote domain. ...</div><div class='long'><p>Performs the read request to the remote domain. JsonP proxy does not actually create an Ajax request,\ninstead we write out a <code>&lt;script&gt;</code> tag based on the configuration of the internal <a href=\"#!/api/Ext.data.Request\" rel=\"Ext.data.Request\" class=\"docClass\">Ext.data.Request</a> object</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Operation</a> object to execute</p>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>A callback function to execute when the Operation has been completed</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope to execute the callback in</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.proxy.Server-method-doRequest' rel='Ext.data.proxy.Server-method-doRequest' class='docClass'>Ext.data.proxy.Server.doRequest</a></p></div></div></div><div id='method-enableBubble' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-enableBubble' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-enableBubble' class='name expandable'>enableBubble</a>( <span class='pre'>eventNames</span> )</div><div class='description'><div class='short'>Enables events fired by this Observable to bubble up an owner hierarchy by calling this.getBubbleTarget() if\npresent. ...</div><div class='long'><p>Enables events fired by this Observable to bubble up an owner hierarchy by calling <code>this.getBubbleTarget()</code> if\npresent. There is no implementation in the Observable base class.</p>\n\n<p>This is commonly used by Ext.Components to bubble events to owner Containers.\nSee <a href=\"#!/api/Ext.Component-method-getBubbleTarget\" rel=\"Ext.Component-method-getBubbleTarget\" class=\"docClass\">Ext.Component.getBubbleTarget</a>. The default implementation in <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a> returns the\nComponent's immediate owner. But if a known target is required, this can be overridden to access the\nrequired target more quickly.</p>\n\n<p>Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.overrides.form.field.Base', {\n    override: '<a href=\"#!/api/Ext.form.field.Base\" rel=\"Ext.form.field.Base\" class=\"docClass\">Ext.form.field.Base</a>',\n\n    //  Add functionality to Field's initComponent to enable the change event to bubble\n    initComponent: function () {\n        this.callParent();\n        this.enableBubble('change');\n    }\n});\n\nvar myForm = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.form.Panel\" rel=\"Ext.form.Panel\" class=\"docClass\">Ext.form.Panel</a>', {\n    title: 'User Details',\n    items: [{\n        ...\n    }],\n    listeners: {\n        change: function() {\n            // Title goes red if form has been modified.\n            myForm.header.setStyle('color', 'red');\n        }\n    }\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventNames</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>The event name to bubble, or an Array of event names.</p>\n</div></li></ul></div></div></div><div id='method-encodeFilters' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-method-encodeFilters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-method-encodeFilters' class='name expandable'>encodeFilters</a>( <span class='pre'>filters</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Encodes the array of Ext.util.Filter objects into a string to be sent in the request url. ...</div><div class='long'><p>Encodes the array of <a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Ext.util.Filter</a> objects into a string to be sent in the request url. By default,\nthis simply JSON-encodes the filter data</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>filters</span> : <a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Ext.util.Filter</a>[]<div class='sub-desc'><p>The array of <a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Filter</a> objects</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The encoded filters</p>\n</div></li></ul></div></div></div><div id='method-encodeRecords' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.proxy.JsonP'>Ext.data.proxy.JsonP</span><br/><a href='source/JsonP2.html#Ext-data-proxy-JsonP-method-encodeRecords' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.JsonP-method-encodeRecords' class='name expandable'>encodeRecords</a>( <span class='pre'>records</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Encodes an array of records into a value suitable to be added to the request params as the recordParam parameter. ...</div><div class='long'><p>Encodes an array of records into a value suitable to be added to the request <code>params</code> as the <a href=\"#!/api/Ext.data.proxy.JsonP-cfg-recordParam\" rel=\"Ext.data.proxy.JsonP-cfg-recordParam\" class=\"docClass\">recordParam</a> parameter.\nThis is broken out into its own function so that it can be easily overridden.</p>\n\n<p>The default implementation</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>records</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]<div class='sub-desc'><p>The records array</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>An array of record data objects</p>\n</div></li></ul></div></div></div><div id='method-encodeSorters' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-method-encodeSorters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-method-encodeSorters' class='name expandable'>encodeSorters</a>( <span class='pre'>sorters</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Encodes the array of Ext.util.Sorter objects into a string to be sent in the request url. ...</div><div class='long'><p>Encodes the array of <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a> objects into a string to be sent in the request url. By default,\nthis simply JSON-encodes the sorter data</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>sorters</span> : <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a>[]<div class='sub-desc'><p>The array of <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Sorter</a> objects</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The encoded sorters</p>\n</div></li></ul></div></div></div><div id='method-extractResponseData' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-method-extractResponseData' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-method-extractResponseData' class='name expandable'>extractResponseData</a>( <span class='pre'>response</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='private signature' >private</strong><strong class='template signature' >template</strong></div><div class='description'><div class='short'>Template method to allow subclasses to specify how to get the response for the reader. ...</div><div class='long'><p>Template method to allow subclasses to specify how to get the response for the reader.</p>\n      <div class='signature-box template'>\n      <p>This is a <a href=\"#!/guide/components\">template method</a>.\n         a hook into the functionality of this class.\n         Feel free to override it in child classes.</p>\n      </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>response</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The server response</p>\n\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The response data to be used by the reader</p>\n\n</div></li></ul></div></div></div><div id='method-fireEvent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-fireEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-fireEvent' class='name expandable'>fireEvent</a>( <span class='pre'>eventName, args</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Fires the specified event with the passed parameters (minus the event name, plus the options object passed\nto addList...</div><div class='long'><p>Fires the specified event with the passed parameters (minus the event name, plus the <code>options</code> object passed\nto <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a>).</p>\n\n<p>An event may be set to bubble up an Observable parent hierarchy (See <a href=\"#!/api/Ext.Component-method-getBubbleTarget\" rel=\"Ext.Component-method-getBubbleTarget\" class=\"docClass\">Ext.Component.getBubbleTarget</a>) by\ncalling <a href=\"#!/api/Ext.util.Observable-method-enableBubble\" rel=\"Ext.util.Observable-method-enableBubble\" class=\"docClass\">enableBubble</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the event to fire.</p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>...<div class='sub-desc'><p>Variable number of parameters are passed to handlers.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>returns false if any of the handlers return false otherwise it returns true.</p>\n</div></li></ul></div></div></div><div id='method-fireEventArgs' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-fireEventArgs' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-fireEventArgs' class='name expandable'>fireEventArgs</a>( <span class='pre'>eventName, args</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Fires the specified event with the passed parameter list. ...</div><div class='long'><p>Fires the specified event with the passed parameter list.</p>\n\n<p>An event may be set to bubble up an Observable parent hierarchy (See <a href=\"#!/api/Ext.Component-method-getBubbleTarget\" rel=\"Ext.Component-method-getBubbleTarget\" class=\"docClass\">Ext.Component.getBubbleTarget</a>) by\ncalling <a href=\"#!/api/Ext.util.Observable-method-enableBubble\" rel=\"Ext.util.Observable-method-enableBubble\" class=\"docClass\">enableBubble</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the event to fire.</p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]<div class='sub-desc'><p>An array of parameters which are passed to handlers.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>returns false if any of the handlers return false otherwise it returns true.</p>\n</div></li></ul></div></div></div><div id='method-getBubbleParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-getBubbleParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-getBubbleParent' class='name expandable'>getBubbleParent</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Gets the bubbling parent for an Observable ...</div><div class='long'><p>Gets the bubbling parent for an Observable</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a></span><div class='sub-desc'><p>The bubble parent. null is returned if no bubble target exists</p>\n</div></li></ul></div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-getModel' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-method-getModel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-method-getModel' class='name expandable'>getModel</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></div><div class='description'><div class='short'>Returns the model attached to this Proxy ...</div><div class='long'><p>Returns the model attached to this Proxy</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></span><div class='sub-desc'><p>The model</p>\n</div></li></ul></div></div></div><div id='method-getParams' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-method-getParams' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-method-getParams' class='name expandable'>getParams</a>( <span class='pre'>operation</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Copy any sorters, filters etc into the params so they can be sent over the wire ...</div><div class='long'><p>Copy any sorters, filters etc into the params so they can be sent over the wire</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>operation</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getReader' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-method-getReader' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-method-getReader' class='name expandable'>getReader</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.data.reader.Reader\" rel=\"Ext.data.reader.Reader\" class=\"docClass\">Ext.data.reader.Reader</a></div><div class='description'><div class='short'>Returns the reader currently attached to this proxy instance ...</div><div class='long'><p>Returns the reader currently attached to this proxy instance</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.reader.Reader\" rel=\"Ext.data.reader.Reader\" class=\"docClass\">Ext.data.reader.Reader</a></span><div class='sub-desc'><p>The Reader instance</p>\n</div></li></ul></div></div></div><div id='method-getUrl' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-method-getUrl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-method-getUrl' class='name expandable'>getUrl</a>( <span class='pre'>request</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Get the url for the request taking into account the order of priority,\n- The request\n- The api\n- The url ...</div><div class='long'><p>Get the url for the request taking into account the order of priority,\n- The request\n- The api\n- The url</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>request</span> : <a href=\"#!/api/Ext.data.Request\" rel=\"Ext.data.Request\" class=\"docClass\">Ext.data.Request</a><div class='sub-desc'><p>The request</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The url</p>\n</div></li></ul></div></div></div><div id='method-getWriter' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-method-getWriter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-method-getWriter' class='name expandable'>getWriter</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.data.writer.Writer\" rel=\"Ext.data.writer.Writer\" class=\"docClass\">Ext.data.writer.Writer</a></div><div class='description'><div class='short'>Returns the writer currently attached to this proxy instance ...</div><div class='long'><p>Returns the writer currently attached to this proxy instance</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.writer.Writer\" rel=\"Ext.data.writer.Writer\" class=\"docClass\">Ext.data.writer.Writer</a></span><div class='sub-desc'><p>The Writer instance</p>\n</div></li></ul></div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-hasListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-hasListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-hasListener' class='name expandable'>hasListener</a>( <span class='pre'>eventName</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks to see if this object has any listeners for a specified event, or whether the event bubbles. ...</div><div class='long'><p>Checks to see if this object has any listeners for a specified event, or whether the event bubbles. The answer\nindicates whether the event needs firing or not.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the event to check for</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p><code>true</code> if the event is being listened for or bubbles, else <code>false</code></p>\n</div></li></ul></div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-mon' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-mon' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-mon' class='name expandable'>mon</a>( <span class='pre'>item, ename, [fn], [scope], [options]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Shorthand for addManagedListener. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.util.Observable-method-addManagedListener\" rel=\"Ext.util.Observable-method-addManagedListener\" class=\"docClass\">addManagedListener</a>.</p>\n\n<p>Adds listeners to any Observable object (or <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>) which are automatically removed when this Component is\ndestroyed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The item to which to add a listener/listeners.</p>\n\n</div></li><li><span class='pre'>ename</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The event name, or an object containing event name properties.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the handler function.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the scope (<code>this</code> reference)\nin which the handler function is executed.</p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the\n<a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> options.</p>\n\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><strong>Only when the <code>destroyable</code> option is specified. </strong></p>\n\n\n\n\n<p> A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call. For example:</p>\n\n\n\n\n<pre><code>this.btnListeners =  = myButton.mon({\n    destroyable: true\n    mouseover:   function() { console.log('mouseover'); },\n    mouseout:    function() { console.log('mouseout'); },\n    click:       function() { console.log('click'); }\n});\n</code></pre>\n\n\n\n\n<p>And when those listeners need to be removed:</p>\n\n\n\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.btnListeners);\n</code></pre>\n\n\n\n\n<p>or</p>\n\n\n\n\n<pre><code>this.btnListeners.destroy();\n</code></pre>\n\n</div></li></ul></div></div></div><div id='method-mun' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-mun' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-mun' class='name expandable'>mun</a>( <span class='pre'>item, ename, [fn], [scope]</span> )</div><div class='description'><div class='short'>Shorthand for removeManagedListener. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.util.Observable-method-removeManagedListener\" rel=\"Ext.util.Observable-method-removeManagedListener\" class=\"docClass\">removeManagedListener</a>.</p>\n\n<p>Removes listeners that were added by the <a href=\"#!/api/Ext.util.Observable-method-mon\" rel=\"Ext.util.Observable-method-mon\" class=\"docClass\">mon</a> method.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The item from which to remove a listener/listeners.</p>\n\n</div></li><li><span class='pre'>ename</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The event name, or an object containing event name properties.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the handler function.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the scope (<code>this</code> reference)\nin which the handler function is executed.</p>\n\n</div></li></ul></div></div></div><div id='method-on' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-on' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-on' class='name expandable'>on</a>( <span class='pre'>eventName, [fn], [scope], [options]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Shorthand for addListener. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a>.</p>\n\n<p>Appends an event handler to this object.  For example:</p>\n\n<pre><code>myGridPanel.on(\"mouseover\", this.onMouseOver, this);\n</code></pre>\n\n<p>The method also allows for a single argument to be passed which is a config object\ncontaining properties which specify multiple events. For example:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: this.onCellClick,\n    mouseover: this.onMouseOver,\n    mouseout: this.onMouseOut,\n    scope: this // Important. Ensure \"this\" is correct during handler execution\n});\n</code></pre>\n\n<p>One can also specify options for each event handler separately:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: this.onCellClick, scope: this, single: true},\n    mouseover: {fn: panel.onMouseOver, scope: panel}\n});\n</code></pre>\n\n<p><em>Names</em> of methods in a specified scope may also be used. Note that\n<code>scope</code> MUST be specified to use this option:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: 'onCellClick', scope: this, single: true},\n    mouseover: {fn: 'onMouseOver', scope: panel}\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The name of the event to listen for.\nMay also be an object who's property names are event names.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The method the event invokes, or <em>if <code>scope</code> is specified, the </em>name* of the method within\nthe specified <code>scope</code>.  Will be called with arguments\ngiven to <a href=\"#!/api/Ext.util.Observable-method-fireEvent\" rel=\"Ext.util.Observable-method-fireEvent\" class=\"docClass\">fireEvent</a> plus the <code>options</code> parameter described below.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is\nexecuted. <strong>If omitted, defaults to the object which fired the event.</strong></p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>An object containing handler configuration.</p>\n\n\n\n\n<p><strong>Note:</strong> Unlike in ExtJS 3.x, the options object will also be passed as the last\nargument to every event handler.</p>\n\n\n\n\n<p>This object may contain any of the following properties:</p>\n\n<ul><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is executed. <strong>If omitted,\n  defaults to the object which fired the event.</strong></p>\n\n</div></li><li><span class='pre'>delay</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of milliseconds to delay the invocation of the handler after the event fires.</p>\n\n</div></li><li><span class='pre'>single</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to add a handler to handle just the next firing of the event, and then remove itself.</p>\n\n</div></li><li><span class='pre'>buffer</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Causes the handler to be scheduled to run in an <a href=\"#!/api/Ext.util.DelayedTask\" rel=\"Ext.util.DelayedTask\" class=\"docClass\">Ext.util.DelayedTask</a> delayed\n  by the specified number of milliseconds. If the event fires again within that time,\n  the original handler is <em>not</em> invoked, but the new handler is scheduled in its place.</p>\n\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a><div class='sub-desc'><p>Only call the handler if the event was fired on the target Observable, <em>not</em> if the event\n  was bubbled up from a child Observable.</p>\n\n</div></li><li><span class='pre'>element</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p><strong>This option is only valid for listeners bound to <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a>.</strong>\n  The name of a Component property which references an element to add a listener to.</p>\n\n\n\n\n<p>  This option is useful during Component construction to add DOM event listeners to elements of\n  <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a> which will exist only after the Component is rendered.\n  For example, to add a click listener to a Panel's body:</p>\n\n\n\n\n<pre><code>  new <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>({\n      title: 'The title',\n      listeners: {\n          click: this.handlePanelClick,\n          element: 'body'\n      }\n  });\n</code></pre>\n\n</div></li><li><span class='pre'>destroyable</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>When specified as <code>true</code>, the function returns A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call.</p>\n\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>priority</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>An optional numeric priority that determines the order in which event handlers\n  are run. Event handlers with no priority will be run as if they had a priority\n  of 0. Handlers with a higher priority will be prioritized to run sooner than\n  those with a lower priority.  Negative numbers can be used to set a priority\n  lower than the default. Internally, the framework uses a range of 1000 or\n  greater, and -1000 or lesser for handers that are intended to run before or\n  after all others, so it is recommended to stay within the range of -999 to 999\n  when setting the priority of event handlers in application-level code.</p>\n\n\n\n\n<p><strong>Combining Options</strong></p>\n\n\n\n\n<p>Using the options argument, it is possible to combine different types of listeners:</p>\n\n\n\n\n<p>A delayed, one-time listener.</p>\n\n\n\n\n<pre><code>myPanel.on('hide', this.handleClick, this, {\n    single: true,\n    delay: 100\n});\n</code></pre>\n\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><strong>Only when the <code>destroyable</code> option is specified. </strong></p>\n\n\n\n\n<p> A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call. For example:</p>\n\n\n\n\n<pre><code>this.btnListeners =  = myButton.on({\n    destroyable: true\n    mouseover:   function() { console.log('mouseover'); },\n    mouseout:    function() { console.log('mouseout'); },\n    click:       function() { console.log('click'); }\n});\n</code></pre>\n\n\n\n\n<p>And when those listeners need to be removed:</p>\n\n\n\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.btnListeners);\n</code></pre>\n\n\n\n\n<p>or</p>\n\n\n\n\n<pre><code>this.btnListeners.destroy();\n</code></pre>\n\n</div></li></ul></div></div></div><div id='method-onBatchComplete' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-method-onBatchComplete' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-method-onBatchComplete' class='name expandable'>onBatchComplete</a>( <span class='pre'>batchOptions, batch</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>The internal callback that the proxy uses to call any specified user callbacks after completion of a batch ...</div><div class='long'><p>The internal callback that the proxy uses to call any specified user callbacks after completion of a batch</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>batchOptions</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>batch</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onDestroy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-method-onDestroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-method-onDestroy' class='name expandable'>onDestroy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-onMetaChange' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-method-onMetaChange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-method-onMetaChange' class='name expandable'>onMetaChange</a>( <span class='pre'>meta</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Called each time the reader's onMetaChange is called so that the proxy can fire the metachange event ...</div><div class='long'><p>Called each time the reader's onMetaChange is called so that the proxy can fire the metachange event</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>meta</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-prepareClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-prepareClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-prepareClass' class='name expandable'>prepareClass</a>( <span class='pre'>T</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Prepares a given class for observable instances. ...</div><div class='long'><p>Prepares a given class for observable instances. This method is called when a\nclass derives from this class or uses this class as a mixin.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>T</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The class constructor to prepare.</p>\n</div></li></ul></div></div></div><div id='method-processResponse' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-method-processResponse' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-method-processResponse' class='name expandable'>processResponse</a>( <span class='pre'>success, operation, request, response, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Should this be documented as protected method? ...</div><div class='long'><p>Should this be documented as protected method?</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>success</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>request</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>response</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-read' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-method-read' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-method-read' class='name expandable'>read</a>( <span class='pre'>operation, callback, scope</span> )</div><div class='description'><div class='short'>Performs the given read operation. ...</div><div class='long'><p>Performs the given read operation.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The Operation to perform</p>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Callback function to be called when the Operation has completed (whether\nsuccessful or not)</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>Scope to execute the callback function in</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.proxy.Proxy-method-read' rel='Ext.data.proxy.Proxy-method-read' class='docClass'>Ext.data.proxy.Proxy.read</a></p></div></div></div><div id='method-relayEvents' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-relayEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-relayEvents' class='name expandable'>relayEvents</a>( <span class='pre'>origin, events, [prefix]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Relays selected events from the specified Observable as if the events were fired by this. ...</div><div class='long'><p>Relays selected events from the specified Observable as if the events were fired by <code>this</code>.</p>\n\n<p>For example if you are extending Grid, you might decide to forward some events from store.\nSo you can do this inside your initComponent:</p>\n\n<pre><code>this.relayEvents(this.getStore(), ['load']);\n</code></pre>\n\n<p>The grid instance will then have an observable 'load' event which will be passed the\nparameters of the store's load event and any function fired with the grid's load event\nwould have access to the grid using the <code>this</code> keyword.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>origin</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The Observable whose events this object is to relay.</p>\n</div></li><li><span class='pre'>events</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>Array of event names to relay.</p>\n</div></li><li><span class='pre'>prefix</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>A common prefix to prepend to the event names. For example:</p>\n\n<pre><code>this.relayEvents(this.getStore(), ['load', 'clear'], 'store');\n</code></pre>\n\n<p>Now the grid will forward 'load' and 'clear' events of store as 'storeload' and 'storeclear'.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which, when destroyed, removes all relayers. For example:</p>\n\n<pre><code>this.storeRelayers = this.relayEvents(this.getStore(), ['load', 'clear'], 'store');\n</code></pre>\n\n<p>Can be undone by calling</p>\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.storeRelayers);\n</code></pre>\n\n<p>or</p>\n\n<pre><code>this.store.relayers.destroy();\n</code></pre>\n</div></li></ul></div></div></div><div id='method-removeListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-removeListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-removeListener' class='name expandable'>removeListener</a>( <span class='pre'>eventName, fn, [scope]</span> )</div><div class='description'><div class='short'>Removes an event handler. ...</div><div class='long'><p>Removes an event handler.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The type of event the handler was associated with.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The handler to remove. <strong>This must be a reference to the function passed into the\n<a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> call.</strong></p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope originally specified for the handler. It must be the same as the\nscope argument specified in the original call to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> or the listener will not be removed.</p>\n\n</div></li></ul></div></div></div><div id='method-removeManagedListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-removeManagedListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-removeManagedListener' class='name expandable'>removeManagedListener</a>( <span class='pre'>item, ename, [fn], [scope]</span> )</div><div class='description'><div class='short'>Removes listeners that were added by the mon method. ...</div><div class='long'><p>Removes listeners that were added by the <a href=\"#!/api/Ext.util.Observable-method-mon\" rel=\"Ext.util.Observable-method-mon\" class=\"docClass\">mon</a> method.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The item from which to remove a listener/listeners.</p>\n\n</div></li><li><span class='pre'>ename</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The event name, or an object containing event name properties.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the handler function.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the scope (<code>this</code> reference)\nin which the handler function is executed.</p>\n\n</div></li></ul></div></div></div><div id='method-removeManagedListenerItem' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-removeManagedListenerItem' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-removeManagedListenerItem' class='name expandable'>removeManagedListenerItem</a>( <span class='pre'>isClear, managedListener</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Remove a single managed listener item ...</div><div class='long'><p>Remove a single managed listener item</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>isClear</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True if this is being called during a clear</p>\n</div></li><li><span class='pre'>managedListener</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The managed listener item\nSee removeManagedListener for other args</p>\n</div></li></ul></div></div></div><div id='method-resumeEvent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-resumeEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-resumeEvent' class='name expandable'>resumeEvent</a>( <span class='pre'>eventName</span> )</div><div class='description'><div class='short'>Resumes firing of the named event(s). ...</div><div class='long'><p>Resumes firing of the named event(s).</p>\n\n<p>After calling this method to resume events, the events will fire when requested to fire.</p>\n\n<p><strong>Note that if the <a href=\"#!/api/Ext.util.Observable-method-suspendEvent\" rel=\"Ext.util.Observable-method-suspendEvent\" class=\"docClass\">suspendEvent</a> method is called multiple times for a certain event,\nthis converse method will have to be called the same number of times for it to resume firing.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>...<div class='sub-desc'><p>Multiple event names to resume.</p>\n</div></li></ul></div></div></div><div id='method-resumeEvents' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-resumeEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-resumeEvents' class='name expandable'>resumeEvents</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Resumes firing events (see suspendEvents). ...</div><div class='long'><p>Resumes firing events (see <a href=\"#!/api/Ext.util.Observable-method-suspendEvents\" rel=\"Ext.util.Observable-method-suspendEvents\" class=\"docClass\">suspendEvents</a>).</p>\n\n<p>If events were suspended using the <code>queueSuspended</code> parameter, then all events fired\nduring event suspension will be sent to any listeners now.</p>\n</div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setException' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.proxy.JsonP'>Ext.data.proxy.JsonP</span><br/><a href='source/JsonP2.html#Ext-data-proxy-JsonP-method-setException' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.JsonP-method-setException' class='name expandable'>setException</a>( <span class='pre'>operation, response</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>inherit docs\n\nSets up an exception on the operation ...</div><div class='long'><p>inherit docs</p>\n\n<p>Sets up an exception on the operation</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The operation</p>\n</div></li><li><span class='pre'>response</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The response</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.proxy.Server-method-setException' rel='Ext.data.proxy.Server-method-setException' class='docClass'>Ext.data.proxy.Server.setException</a></p></div></div></div><div id='method-setExtraParam' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-method-setExtraParam' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-method-setExtraParam' class='name expandable'>setExtraParam</a>( <span class='pre'>name, value</span> )</div><div class='description'><div class='short'>Sets a value in the underlying extraParams. ...</div><div class='long'><p>Sets a value in the underlying <a href=\"#!/api/Ext.data.proxy.Server-cfg-extraParams\" rel=\"Ext.data.proxy.Server-cfg-extraParams\" class=\"docClass\">extraParams</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The key for the new value</p>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value</p>\n</div></li></ul></div></div></div><div id='method-setModel' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-method-setModel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-method-setModel' class='name expandable'>setModel</a>( <span class='pre'>model, setOnStore</span> )</div><div class='description'><div class='short'>Sets the model associated with this proxy. ...</div><div class='long'><p>Sets the model associated with this proxy. This will only usually be called by a Store</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>model</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The new model. Can be either the model name string,\nor a reference to the model's constructor</p>\n</div></li><li><span class='pre'>setOnStore</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>Sets the new model on the associated Store, if one is present</p>\n</div></li></ul></div></div></div><div id='method-setReader' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-method-setReader' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-method-setReader' class='name expandable'>setReader</a>( <span class='pre'>reader</span> ) : <a href=\"#!/api/Ext.data.reader.Reader\" rel=\"Ext.data.reader.Reader\" class=\"docClass\">Ext.data.reader.Reader</a></div><div class='description'><div class='short'>Sets the Proxy's Reader by string, config object or Reader instance ...</div><div class='long'><p>Sets the Proxy's Reader by string, config object or Reader instance</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>reader</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Ext.data.reader.Reader\" rel=\"Ext.data.reader.Reader\" class=\"docClass\">Ext.data.reader.Reader</a><div class='sub-desc'><p>The new Reader, which can be either a type string,\na configuration object or an <a href=\"#!/api/Ext.data.reader.Reader\" rel=\"Ext.data.reader.Reader\" class=\"docClass\">Ext.data.reader.Reader</a> instance</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.reader.Reader\" rel=\"Ext.data.reader.Reader\" class=\"docClass\">Ext.data.reader.Reader</a></span><div class='sub-desc'><p>The attached Reader object</p>\n</div></li></ul></div></div></div><div id='method-setWriter' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-method-setWriter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-method-setWriter' class='name expandable'>setWriter</a>( <span class='pre'>writer</span> ) : <a href=\"#!/api/Ext.data.writer.Writer\" rel=\"Ext.data.writer.Writer\" class=\"docClass\">Ext.data.writer.Writer</a></div><div class='description'><div class='short'>Sets the Proxy's Writer by string, config object or Writer instance ...</div><div class='long'><p>Sets the Proxy's Writer by string, config object or Writer instance</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>writer</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Ext.data.writer.Writer\" rel=\"Ext.data.writer.Writer\" class=\"docClass\">Ext.data.writer.Writer</a><div class='sub-desc'><p>The new Writer, which can be either a type string,\na configuration object or an <a href=\"#!/api/Ext.data.writer.Writer\" rel=\"Ext.data.writer.Writer\" class=\"docClass\">Ext.data.writer.Writer</a> instance</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.writer.Writer\" rel=\"Ext.data.writer.Writer\" class=\"docClass\">Ext.data.writer.Writer</a></span><div class='sub-desc'><p>The attached Writer object</p>\n</div></li></ul></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-suspendEvent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-suspendEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-suspendEvent' class='name expandable'>suspendEvent</a>( <span class='pre'>eventName</span> )</div><div class='description'><div class='short'>Suspends firing of the named event(s). ...</div><div class='long'><p>Suspends firing of the named event(s).</p>\n\n<p>After calling this method to suspend events, the events will no longer fire when requested to fire.</p>\n\n<p><strong>Note that if this is called multiple times for a certain event, the converse method\n<a href=\"#!/api/Ext.util.Observable-method-resumeEvent\" rel=\"Ext.util.Observable-method-resumeEvent\" class=\"docClass\">resumeEvent</a> will have to be called the same number of times for it to resume firing.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>...<div class='sub-desc'><p>Multiple event names to suspend.</p>\n</div></li></ul></div></div></div><div id='method-suspendEvents' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-suspendEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-suspendEvents' class='name expandable'>suspendEvents</a>( <span class='pre'>queueSuspended</span> )</div><div class='description'><div class='short'>Suspends the firing of all events. ...</div><div class='long'><p>Suspends the firing of all events. (see <a href=\"#!/api/Ext.util.Observable-method-resumeEvents\" rel=\"Ext.util.Observable-method-resumeEvents\" class=\"docClass\">resumeEvents</a>)</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>queueSuspended</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>Pass as true to queue up suspended events to be fired\nafter the <a href=\"#!/api/Ext.util.Observable-method-resumeEvents\" rel=\"Ext.util.Observable-method-resumeEvents\" class=\"docClass\">resumeEvents</a> call instead of discarding all suspended events.</p>\n</div></li></ul></div></div></div><div id='method-un' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-un' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-un' class='name expandable'>un</a>( <span class='pre'>eventName, fn, [scope]</span> )</div><div class='description'><div class='short'>Shorthand for removeListener. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.util.Observable-method-removeListener\" rel=\"Ext.util.Observable-method-removeListener\" class=\"docClass\">removeListener</a>.</p>\n\n<p>Removes an event handler.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The type of event the handler was associated with.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The handler to remove. <strong>This must be a reference to the function passed into the\n<a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> call.</strong></p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope originally specified for the handler. It must be the same as the\nscope argument specified in the original call to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> or the listener will not be removed.</p>\n\n</div></li></ul></div></div></div><div id='method-update' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Server' rel='Ext.data.proxy.Server' class='defined-in docClass'>Ext.data.proxy.Server</a><br/><a href='source/Server.html#Ext-data-proxy-Server-method-update' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Server-method-update' class='name expandable'>update</a>( <span class='pre'>operation, callback, scope</span> )</div><div class='description'><div class='short'>Performs the given update operation. ...</div><div class='long'><p>Performs the given update operation.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The Operation to perform</p>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Callback function to be called when the Operation has completed (whether\nsuccessful or not)</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>Scope to execute the callback function in</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.proxy.Proxy-method-update' rel='Ext.data.proxy.Proxy-method-update' class='docClass'>Ext.data.proxy.Proxy.update</a></p></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-create' class='name expandable'>create</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create a new instance of this Class. ...</div><div class='long'><p>Create a new instance of this Class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.create({\n    someConfig: true\n});\n</code></pre>\n\n<p>All parameters are passed to the constructor of the class.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the created instance.</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-event'>Events</h3><div class='subsection'><div id='event-exception' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.proxy.JsonP'>Ext.data.proxy.JsonP</span><br/><a href='source/JsonP2.html#Ext-data-proxy-JsonP-event-exception' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.JsonP-event-exception' class='name expandable'>exception</a>( <span class='pre'>this, request, operation, eOpts</span> )</div><div class='description'><div class='short'>Fires when the server returns an exception. ...</div><div class='long'><p>Fires when the server returns an exception. This event may also be listened\nto in the event that a request has timed out or has been aborted.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a><div class='sub-desc'>\n</div></li><li><span class='pre'>request</span> : <a href=\"#!/api/Ext.data.Request\" rel=\"Ext.data.Request\" class=\"docClass\">Ext.data.Request</a><div class='sub-desc'><p>The request that was sent</p>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The operation that triggered the request</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.proxy.Server-event-exception' rel='Ext.data.proxy.Server-event-exception' class='docClass'>Ext.data.proxy.Server.exception</a></p></div></div></div><div id='event-metachange' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='defined-in docClass'>Ext.data.proxy.Proxy</a><br/><a href='source/Proxy2.html#Ext-data-proxy-Proxy-event-metachange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.proxy.Proxy-event-metachange' class='name expandable'>metachange</a>( <span class='pre'>this, meta, eOpts</span> )</div><div class='description'><div class='short'>Fires when this proxy's reader provides new metadata. ...</div><div class='long'><p>Fires when this proxy's reader provides new metadata. Metadata usually consists\nof new field definitions, but can include any configuration data required by an\napplication, and can be processed as needed in the event handler.\nThis event is currently only fired for JsonReaders. Note that this event is also\npropagated by <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a>, which is typically where it would be handled.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a><div class='sub-desc'>\n</div></li><li><span class='pre'>meta</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The JSON metadata</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div></div></div></div></div>","superclasses":["Ext.Base","Ext.data.proxy.Proxy","Ext.data.proxy.Server"],"meta":{"author":["Ed Spencer"]},"code_type":"ext_define","requires":["Ext.data.JsonP"],"html_meta":{"author":null},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"JsonP2.html#Ext-data-proxy-JsonP","filename":"JsonP.js"}],"linenr":1,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.util.Observable","meta":{"private":true},"name":"eventsSuspended","id":"property-eventsSuspended"},{"tagname":"property","owner":"Ext.util.Observable","meta":{"readonly":true},"name":"hasListeners","id":"property-hasListeners"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.util.Observable","meta":{},"name":"isObservable","id":"property-isObservable"},{"tagname":"property","owner":"Ext.data.proxy.Proxy","meta":{},"name":"isProxy","id":"property-isProxy"},{"tagname":"property","owner":"Ext.data.proxy.Proxy","meta":{},"name":"isSynchronous","id":"property-isSynchronous"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"}],"cfg":[{"tagname":"cfg","owner":"Ext.data.proxy.Server","meta":{},"name":"api","id":"cfg-api"},{"tagname":"cfg","owner":"Ext.data.proxy.JsonP","meta":{},"name":"autoAppendParams","id":"cfg-autoAppendParams"},{"tagname":"cfg","owner":"Ext.data.proxy.Proxy","meta":{},"name":"batchActions","id":"cfg-batchActions"},{"tagname":"cfg","owner":"Ext.data.proxy.Proxy","meta":{},"name":"batchOrder","id":"cfg-batchOrder"},{"tagname":"cfg","owner":"Ext.data.proxy.Server","meta":{},"name":"cacheString","id":"cfg-cacheString"},{"tagname":"cfg","owner":"Ext.data.proxy.JsonP","meta":{},"name":"callbackKey","id":"cfg-callbackKey"},{"tagname":"cfg","owner":"Ext.data.proxy.Proxy","meta":{"private":true},"name":"defaultReaderType","id":"cfg-defaultReaderType"},{"tagname":"cfg","owner":"Ext.data.proxy.JsonP","meta":{"private":true},"name":"defaultWriterType","id":"cfg-defaultWriterType"},{"tagname":"cfg","owner":"Ext.data.proxy.Server","meta":{},"name":"directionParam","id":"cfg-directionParam"},{"tagname":"cfg","owner":"Ext.data.proxy.Server","meta":{},"name":"extraParams","id":"cfg-extraParams"},{"tagname":"cfg","owner":"Ext.data.proxy.Server","meta":{},"name":"filterParam","id":"cfg-filterParam"},{"tagname":"cfg","owner":"Ext.data.proxy.Server","meta":{},"name":"groupDirectionParam","id":"cfg-groupDirectionParam"},{"tagname":"cfg","owner":"Ext.data.proxy.Server","meta":{},"name":"groupParam","id":"cfg-groupParam"},{"tagname":"cfg","owner":"Ext.data.proxy.Server","meta":{},"name":"idParam","id":"cfg-idParam"},{"tagname":"cfg","owner":"Ext.data.proxy.Server","meta":{},"name":"limitParam","id":"cfg-limitParam"},{"tagname":"cfg","owner":"Ext.util.Observable","meta":{},"name":"listeners","id":"cfg-listeners"},{"tagname":"cfg","owner":"Ext.data.proxy.Proxy","meta":{},"name":"model","id":"cfg-model"},{"tagname":"cfg","owner":"Ext.data.proxy.Server","meta":{},"name":"noCache","id":"cfg-noCache"},{"tagname":"cfg","owner":"Ext.data.proxy.Server","meta":{},"name":"pageParam","id":"cfg-pageParam"},{"tagname":"cfg","owner":"Ext.data.proxy.Proxy","meta":{},"name":"reader","id":"cfg-reader"},{"tagname":"cfg","owner":"Ext.data.proxy.JsonP","meta":{},"name":"recordParam","id":"cfg-recordParam"},{"tagname":"cfg","owner":"Ext.data.proxy.Server","meta":{},"name":"simpleGroupMode","id":"cfg-simpleGroupMode"},{"tagname":"cfg","owner":"Ext.data.proxy.Server","meta":{},"name":"simpleSortMode","id":"cfg-simpleSortMode"},{"tagname":"cfg","owner":"Ext.data.proxy.Server","meta":{},"name":"sortParam","id":"cfg-sortParam"},{"tagname":"cfg","owner":"Ext.data.proxy.Server","meta":{},"name":"startParam","id":"cfg-startParam"},{"tagname":"cfg","owner":"Ext.data.proxy.Server","meta":{},"name":"timeout","id":"cfg-timeout"},{"tagname":"cfg","owner":"Ext.data.proxy.Server","meta":{},"name":"url","id":"cfg-url"},{"tagname":"cfg","owner":"Ext.data.proxy.Proxy","meta":{},"name":"writer","id":"cfg-writer"}],"css_var":[],"method":[{"tagname":"method","owner":"Ext.data.proxy.JsonP","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"Ext.data.proxy.JsonP","meta":{},"name":"abort","id":"method-abort"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"addEvents","id":"method-addEvents"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"addListener","id":"method-addListener"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"addManagedListener","id":"method-addManagedListener"},{"tagname":"method","owner":"Ext.data.proxy.Server","meta":{"template":true,"protected":true},"name":"afterRequest","id":"method-afterRequest"},{"tagname":"method","owner":"Ext.data.proxy.Server","meta":{"private":true},"name":"applyEncoding","id":"method-applyEncoding"},{"tagname":"method","owner":"Ext.data.proxy.Proxy","meta":{},"name":"batch","id":"method-batch"},{"tagname":"method","owner":"Ext.data.proxy.Server","meta":{},"name":"buildRequest","id":"method-buildRequest"},{"tagname":"method","owner":"Ext.data.proxy.JsonP","meta":{},"name":"buildUrl","id":"method-buildUrl"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"captureArgs","id":"method-captureArgs"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"clearListeners","id":"method-clearListeners"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"clearManagedListeners","id":"method-clearManagedListeners"},{"tagname":"method","owner":"Ext.data.proxy.Proxy","meta":{"private":true},"name":"clone","id":"method-clone"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"continueFireEvent","id":"method-continueFireEvent"},{"tagname":"method","owner":"Ext.data.proxy.Server","meta":{},"name":"create","id":"method-create"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"createRelayer","id":"method-createRelayer"},{"tagname":"method","owner":"Ext.data.proxy.JsonP","meta":{"private":true},"name":"createRequestCallback","id":"method-createRequestCallback"},{"tagname":"method","owner":"Ext.data.proxy.Server","meta":{},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.data.proxy.JsonP","meta":{"private":true},"name":"doRequest","id":"method-doRequest"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"enableBubble","id":"method-enableBubble"},{"tagname":"method","owner":"Ext.data.proxy.Server","meta":{},"name":"encodeFilters","id":"method-encodeFilters"},{"tagname":"method","owner":"Ext.data.proxy.JsonP","meta":{},"name":"encodeRecords","id":"method-encodeRecords"},{"tagname":"method","owner":"Ext.data.proxy.Server","meta":{},"name":"encodeSorters","id":"method-encodeSorters"},{"tagname":"method","owner":"Ext.data.proxy.Server","meta":{"template":true,"private":true},"name":"extractResponseData","id":"method-extractResponseData"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"fireEvent","id":"method-fireEvent"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"fireEventArgs","id":"method-fireEventArgs"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"getBubbleParent","id":"method-getBubbleParent"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.data.proxy.Proxy","meta":{},"name":"getModel","id":"method-getModel"},{"tagname":"method","owner":"Ext.data.proxy.Server","meta":{"private":true},"name":"getParams","id":"method-getParams"},{"tagname":"method","owner":"Ext.data.proxy.Proxy","meta":{},"name":"getReader","id":"method-getReader"},{"tagname":"method","owner":"Ext.data.proxy.Server","meta":{"private":true},"name":"getUrl","id":"method-getUrl"},{"tagname":"method","owner":"Ext.data.proxy.Proxy","meta":{},"name":"getWriter","id":"method-getWriter"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"hasListener","id":"method-hasListener"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"mon","id":"method-mon"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"mun","id":"method-mun"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"on","id":"method-on"},{"tagname":"method","owner":"Ext.data.proxy.Proxy","meta":{"private":true},"name":"onBatchComplete","id":"method-onBatchComplete"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.data.proxy.Server","meta":{"private":true},"name":"onDestroy","id":"method-onDestroy"},{"tagname":"method","owner":"Ext.data.proxy.Proxy","meta":{"private":true},"name":"onMetaChange","id":"method-onMetaChange"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"prepareClass","id":"method-prepareClass"},{"tagname":"method","owner":"Ext.data.proxy.Server","meta":{"private":true},"name":"processResponse","id":"method-processResponse"},{"tagname":"method","owner":"Ext.data.proxy.Server","meta":{},"name":"read","id":"method-read"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"relayEvents","id":"method-relayEvents"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"removeListener","id":"method-removeListener"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"removeManagedListener","id":"method-removeManagedListener"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"removeManagedListenerItem","id":"method-removeManagedListenerItem"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"resumeEvent","id":"method-resumeEvent"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"resumeEvents","id":"method-resumeEvents"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.data.proxy.JsonP","meta":{"private":true},"name":"setException","id":"method-setException"},{"tagname":"method","owner":"Ext.data.proxy.Server","meta":{},"name":"setExtraParam","id":"method-setExtraParam"},{"tagname":"method","owner":"Ext.data.proxy.Proxy","meta":{},"name":"setModel","id":"method-setModel"},{"tagname":"method","owner":"Ext.data.proxy.Proxy","meta":{},"name":"setReader","id":"method-setReader"},{"tagname":"method","owner":"Ext.data.proxy.Proxy","meta":{},"name":"setWriter","id":"method-setWriter"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"suspendEvent","id":"method-suspendEvent"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"suspendEvents","id":"method-suspendEvents"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"un","id":"method-un"},{"tagname":"method","owner":"Ext.data.proxy.Server","meta":{},"name":"update","id":"method-update"}],"event":[{"tagname":"event","owner":"Ext.data.proxy.JsonP","meta":{},"name":"exception","id":"event-exception"},{"tagname":"event","owner":"Ext.data.proxy.Proxy","meta":{},"name":"metachange","id":"event-metachange"}],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.data.proxy.JsonP","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.data.proxy.JsonP","mixins":[],"mixedInto":[]});