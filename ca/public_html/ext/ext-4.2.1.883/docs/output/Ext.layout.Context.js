Ext.data.JsonP.Ext_layout_Context({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":"Ext.Base","uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><strong>Ext.layout.Context</strong></div></div><h4>Requires</h4><div class='dependency'><a href='#!/api/Ext.fx.Anim' rel='Ext.fx.Anim' class='docClass'>Ext.fx.Anim</a></div><div class='dependency'><a href='#!/api/Ext.fx.Manager' rel='Ext.fx.Manager' class='docClass'>Ext.fx.Manager</a></div><div class='dependency'><a href='#!/api/Ext.layout.ContextItem' rel='Ext.layout.ContextItem' class='docClass'>Ext.layout.ContextItem</a></div><div class='dependency'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='docClass'>Ext.layout.Layout</a></div><div class='dependency'><a href='#!/api/Ext.util.Queue' rel='Ext.util.Queue' class='docClass'>Ext.util.Queue</a></div><h4>Files</h4><div class='dependency'><a href='source/Context2.html#Ext-layout-Context' target='_blank'>Context.js</a></div><div class='dependency'><a href='source/Context.html#Ext-diag-layout-Context' target='_blank'>Context.js</a></div></pre><div class='doc-contents'><p class='private'><strong>NOTE</strong> This is a private utility class for internal use by the framework. Don't rely on its existence.</p><p>Manages context information during a layout.</p>\n\n<h1>Algorithm</h1>\n\n<p>This class performs the following jobs:</p>\n\n<ul>\n<li>Cache DOM reads to avoid reading the same values repeatedly.</li>\n<li>Buffer DOM writes and flush them as a block to avoid read/write interleaving.</li>\n<li>Track layout dependencies so each layout does not have to figure out the source of\nits dependent values.</li>\n<li>Intelligently run layouts when the values on which they depend change (a trigger).</li>\n<li>Allow layouts to avoid processing when required values are unavailable (a block).</li>\n</ul>\n\n\n<p>Work done during layout falls into either a \"read phase\" or a \"write phase\" and it is\nessential to always be aware of the current phase. Most methods in\n<a href=\"#!/api/Ext.layout.Layout\" rel=\"Ext.layout.Layout\" class=\"docClass\">Layout</a> are called during a read phase:\n<a href=\"#!/api/Ext.layout.Layout-method-calculate\" rel=\"Ext.layout.Layout-method-calculate\" class=\"docClass\">calculate</a>,\n<a href=\"#!/api/Ext.layout.Layout-method-completeLayout\" rel=\"Ext.layout.Layout-method-completeLayout\" class=\"docClass\">completeLayout</a> and\n<a href=\"#!/api/Ext.layout.Layout-method-finalizeLayout\" rel=\"Ext.layout.Layout-method-finalizeLayout\" class=\"docClass\">finalizeLayout</a>. The exceptions to this are\n<a href=\"#!/api/Ext.layout.Layout-method-beginLayout\" rel=\"Ext.layout.Layout-method-beginLayout\" class=\"docClass\">beginLayout</a>,\n<a href=\"#!/api/Ext.layout.Layout-method-beginLayoutCycle\" rel=\"Ext.layout.Layout-method-beginLayoutCycle\" class=\"docClass\">beginLayoutCycle</a> and\n<a href=\"#!/api/Ext.layout.Layout-method-finishedLayout\" rel=\"Ext.layout.Layout-method-finishedLayout\" class=\"docClass\">finishedLayout</a> which are called during\na write phase. While <a href=\"#!/api/Ext.layout.Layout-method-finishedLayout\" rel=\"Ext.layout.Layout-method-finishedLayout\" class=\"docClass\">finishedLayout</a> is called\na write phase, it is really intended to be a catch-all for post-processing after a\nlayout run.</p>\n\n<p>In a read phase, it is OK to read the DOM but this should be done using the appropriate\n<a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">ContextItem</a> where possible since that provides a cache\nto avoid redundant reads. No writes should be made to the DOM in a read phase! Instead,\nthe values should be written to the proper ContextItem for later write-back.</p>\n\n<p>The rules flip-flop in a write phase. The only difference is that ContextItem methods\nlike <a href=\"#!/api/Ext.layout.ContextItem-method-getStyle\" rel=\"Ext.layout.ContextItem-method-getStyle\" class=\"docClass\">getStyle</a> will still read the DOM unless the\nvalue was previously read. This detail is unknowable from the outside of ContextItem, so\nread calls to ContextItem should also be avoided in a write phase.</p>\n\n<p>Calculating interdependent layouts requires a certain amount of iteration. In a given\ncycle, some layouts will contribute results that allow other layouts to proceed. The\ngeneral flow then is to gather all of the layouts (both component and container) in a\ncomponent tree and queue them all for processing. The initial queue order is bottom-up\nand component layout first, then container layout (if applicable) for each component.</p>\n\n<p>This initial step also calls the beginLayout method on all layouts to clear any values\nfrom the DOM that might interfere with calculations and measurements. In other words,\nthis is a \"write phase\" and reads from the DOM should be strictly avoided.</p>\n\n<p>Next the layout enters into its iterations or \"cycles\". Each cycle consists of calling\nthe <a href=\"#!/api/Ext.layout.Layout-method-calculate\" rel=\"Ext.layout.Layout-method-calculate\" class=\"docClass\">calculate</a> method on all layouts in the\n<a href=\"#!/api/Ext.layout.Context-property-layoutQueue\" rel=\"Ext.layout.Context-property-layoutQueue\" class=\"docClass\">layoutQueue</a>. These calls are part of a \"read phase\" and writes to the DOM should\nbe strictly avoided.</p>\n\n<h1>Considerations</h1>\n\n<p><strong>RULE 1</strong>: Respect the read/write cycles. Always use the <a href=\"#!/api/Ext.layout.ContextItem-method-getProp\" rel=\"Ext.layout.ContextItem-method-getProp\" class=\"docClass\">getProp</a>\nor <a href=\"#!/api/Ext.layout.ContextItem-method-getDomProp\" rel=\"Ext.layout.ContextItem-method-getDomProp\" class=\"docClass\">getDomProp</a> methods to get calculated values;\nonly use the <a href=\"#!/api/Ext.layout.ContextItem-method-getStyle\" rel=\"Ext.layout.ContextItem-method-getStyle\" class=\"docClass\">getStyle</a> method to read styles; use\n<a href=\"#!/api/Ext.layout.ContextItem-method-setProp\" rel=\"Ext.layout.ContextItem-method-setProp\" class=\"docClass\">setProp</a> to set DOM values. Some reads will, of\ncourse, still go directly to the DOM, but if there is a method in\n<a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">ContextItem</a> to do a certain job, it should be used instead\nof a lower-level equivalent.</p>\n\n<p>The basic logic flow in <a href=\"#!/api/Ext.layout.Layout-method-calculate\" rel=\"Ext.layout.Layout-method-calculate\" class=\"docClass\">calculate</a> consists of gathering\nvalues by calling <a href=\"#!/api/Ext.layout.ContextItem-method-getProp\" rel=\"Ext.layout.ContextItem-method-getProp\" class=\"docClass\">getProp</a> or\n<a href=\"#!/api/Ext.layout.ContextItem-method-getDomProp\" rel=\"Ext.layout.ContextItem-method-getDomProp\" class=\"docClass\">getDomProp</a>, calculating results and publishing\nthem by calling <a href=\"#!/api/Ext.layout.ContextItem-method-setProp\" rel=\"Ext.layout.ContextItem-method-setProp\" class=\"docClass\">setProp</a>. It is important to realize\nthat <a href=\"#!/api/Ext.layout.ContextItem-method-getProp\" rel=\"Ext.layout.ContextItem-method-getProp\" class=\"docClass\">getProp</a> will return <code>undefined</code> if the value\nis not yet known. But the act of calling the method is enough to track the fact that the\ncalling layout depends (in some way) on this value. In other words, the calling layout is\n\"triggered\" by the properties it requests.</p>\n\n<p><strong>RULE 2</strong>: Avoid calling <a href=\"#!/api/Ext.layout.ContextItem-method-getProp\" rel=\"Ext.layout.ContextItem-method-getProp\" class=\"docClass\">getProp</a> unless the value\nis needed. Gratuitous calls cause inefficiency because the layout will appear to depend on\nvalues that it never actually uses. This applies equally to\n<a href=\"#!/api/Ext.layout.ContextItem-method-getDomProp\" rel=\"Ext.layout.ContextItem-method-getDomProp\" class=\"docClass\">getDomProp</a> and the test-only methods\n<a href=\"#!/api/Ext.layout.ContextItem-method-hasProp\" rel=\"Ext.layout.ContextItem-method-hasProp\" class=\"docClass\">hasProp</a> and <a href=\"#!/api/Ext.layout.ContextItem-method-hasDomProp\" rel=\"Ext.layout.ContextItem-method-hasDomProp\" class=\"docClass\">hasDomProp</a>.</p>\n\n<p>Because <a href=\"#!/api/Ext.layout.ContextItem-method-getProp\" rel=\"Ext.layout.ContextItem-method-getProp\" class=\"docClass\">getProp</a> can return <code>undefined</code>, it is often\nthe case that subsequent math will produce NaN's. This is usually not a problem as the\nNaN's simply propagate along and result in final results that are NaN. Both <code>undefined</code>\nand NaN are ignored by <a href=\"#!/api/Ext.layout.ContextItem-method-setProp\" rel=\"Ext.layout.ContextItem-method-setProp\" class=\"docClass\">Ext.layout.ContextItem.setProp</a>, so it is often not necessary\nto even know that this is happening. It does become important for determining if a layout\nis not done or if it might lead to publishing an incorrect (but not NaN or <code>undefined</code>)\nvalue.</p>\n\n<p><strong>RULE 3</strong>: If a layout has not calculated all the values it is required to calculate, it\nmust set <a href=\"#!/api/Ext.layout.Layout-property-done\" rel=\"Ext.layout.Layout-property-done\" class=\"docClass\">done</a> to <code>false</code> before returning from\n<a href=\"#!/api/Ext.layout.Layout-method-calculate\" rel=\"Ext.layout.Layout-method-calculate\" class=\"docClass\">calculate</a>. This value is always <code>true</code> on entry because\nit is simpler to detect the incomplete state rather than the complete state (especially up\nand down a class hierarchy).</p>\n\n<p><strong>RULE 4</strong>: A layout must never publish an incomplete (wrong) result. Doing so would cause\ndependent layouts to run their calculations on those wrong values, producing more wrong\nvalues and some layouts may even incorrectly flag themselves as <a href=\"#!/api/Ext.layout.Layout-property-done\" rel=\"Ext.layout.Layout-property-done\" class=\"docClass\">done</a>\nbefore the correct values are determined and republished. Doing this will poison the\ncalculations.</p>\n\n<p><strong>RULE 5</strong>: Each value should only be published by one layout. If multiple layouts attempt\nto publish the same values, it would be nearly impossible to avoid breaking <strong>RULE 4</strong>. To\nhelp detect this problem, the layout diagnostics will trap on an attempt to set a value\nfrom different layouts.</p>\n\n<p>Complex layouts can produce many results as part of their calculations. These values are\nimportant for other layouts to proceed and need to be published by the earliest possible\ncall to <a href=\"#!/api/Ext.layout.Layout-method-calculate\" rel=\"Ext.layout.Layout-method-calculate\" class=\"docClass\">Ext.layout.Layout.calculate</a> to avoid unnecessary cycles and poor performance. It is\nalso possible, however, for some results to be related in a way such that publishing them\nmay be an all-or-none proposition (typically to avoid breaking <em>RULE 4</em>).</p>\n\n<p><strong>RULE 6</strong>: Publish results as soon as they are known to be correct rather than wait for\nall values to be calculated. Waiting for everything to be complete can lead to deadlock.\nThe key here is not to forget <strong>RULE 4</strong> in the process.</p>\n\n<p>Some layouts depend on certain critical values as part of their calculations. For example,\nHBox depends on width and cannot do anything until the width is known. In these cases, it\nis best to use <a href=\"#!/api/Ext.layout.ContextItem-method-block\" rel=\"Ext.layout.ContextItem-method-block\" class=\"docClass\">block</a> or\n<a href=\"#!/api/Ext.layout.ContextItem-method-domBlock\" rel=\"Ext.layout.ContextItem-method-domBlock\" class=\"docClass\">domBlock</a> and thereby avoid processing the layout\nuntil the needed value is available.</p>\n\n<p><strong>RULE 7</strong>: Use <a href=\"#!/api/Ext.layout.ContextItem-method-block\" rel=\"Ext.layout.ContextItem-method-block\" class=\"docClass\">block</a> or\n<a href=\"#!/api/Ext.layout.ContextItem-method-domBlock\" rel=\"Ext.layout.ContextItem-method-domBlock\" class=\"docClass\">domBlock</a> when values are required to make progress.\nThis will mimize wasted recalculations.</p>\n\n<p><strong>RULE 8</strong>: Blocks should only be used when no forward progress can be made. If even one\nvalue could still be calculated, a block could result in a deadlock.</p>\n\n<p>Historically, layouts have been invoked directly by component code, sometimes in places\nlike an <code>afterLayout</code> method for a child component. With the flexibility now available\nto solve complex, iterative issues, such things should be done in a responsible layout\n(be it component or container).</p>\n\n<p><strong>RULE 9</strong>: Use layouts to solve layout issues and don't wait for the layout to finish to\nperform further layouts. This is especially important now that layouts process entire\ncomponent trees and not each layout in isolation.</p>\n\n<h1>Sequence Diagram</h1>\n\n<p>The simplest sequence diagram for a layout run looks roughly like this:</p>\n\n<pre><code>  Context         Layout 1     Item 1     Layout 2     Item 2\n     |               |           |           |           |\n----&gt;X--------------&gt;X           |           |           |\nrun  X---------------|-----------|----------&gt;X           |\n     X beginLayout   |           |           |           |\n     X               |           |           |           |\n   A X--------------&gt;X           |           |           |\n     X  calculate    X----------&gt;X           |           |\n     X             C X  getProp  |           |           |\n   B X               X----------&gt;X           |           |\n     X               |  setProp  |           |           |\n     X               |           |           |           |\n   D X---------------|-----------|----------&gt;X           |\n     X  calculate    |           |           X----------&gt;X\n     X               |           |           |  setProp  |\n   E X               |           |           |           |\n     X---------------|-----------|----------&gt;X           |\n     X completeLayout|           |         F |           |\n     X               |           |           |           |\n   G X               |           |           |           |\n   H X--------------&gt;X           |           |           |\n     X  calculate    X----------&gt;X           |           |\n     X             I X  getProp  |           |           |\n     X               X----------&gt;X           |           |\n     X               |  setProp  |           |           |\n   J X--------------&gt;X           |           |           |\n     X completeLayout|           |           |           |\n     X               |           |           |           |\n   K X--------------&gt;X           |           |           |\n     X---------------|-----------|----------&gt;X           |\n     X finalizeLayout|           |           |           |\n     X               |           |           |           |\n   L X--------------&gt;X           |           |           |\n     X---------------|-----------|----------&gt;X           |\n     X finishedLayout|           |           |           |\n     X               |           |           |           |\n   M X--------------&gt;X           |           |           |\n     X---------------|-----------|----------&gt;X           |\n     X notifyOwner   |           |           |           |\n   N |               |           |           |           |\n     -               -           -           -           -\n</code></pre>\n\n<p>Notes:</p>\n\n<p><strong>A.</strong> This is a call from the <a href=\"#!/api/Ext.layout.Context-method-run\" rel=\"Ext.layout.Context-method-run\" class=\"docClass\">run</a> method to the <a href=\"#!/api/Ext.layout.Context-method-runCycle\" rel=\"Ext.layout.Context-method-runCycle\" class=\"docClass\">runCycle</a> method.\nEach layout in the queue will have its <a href=\"#!/api/Ext.layout.Layout-method-calculate\" rel=\"Ext.layout.Layout-method-calculate\" class=\"docClass\">calculate</a>\nmethod called.</p>\n\n<p><strong>B.</strong> After each <a href=\"#!/api/Ext.layout.Layout-method-calculate\" rel=\"Ext.layout.Layout-method-calculate\" class=\"docClass\">calculate</a> method is called the\n<a href=\"#!/api/Ext.layout.Layout-property-done\" rel=\"Ext.layout.Layout-property-done\" class=\"docClass\">done</a> flag is checked to see if the Layout has completed.\nIf it has completed and that layout object implements a\n<a href=\"#!/api/Ext.layout.Layout-method-completeLayout\" rel=\"Ext.layout.Layout-method-completeLayout\" class=\"docClass\">completeLayout</a> method, this layout is queued to\nreceive its call. Otherwise, the layout will be queued again unless there are blocks or\ntriggers that govern its requeueing.</p>\n\n<p><strong>C.</strong> The call to <a href=\"#!/api/Ext.layout.ContextItem-method-getProp\" rel=\"Ext.layout.ContextItem-method-getProp\" class=\"docClass\">getProp</a> is made to the Item\nand that will be tracked as a trigger (keyed by the name of the property being requested).\nChanges to this property will cause this layout to be requeued. The call to\n<a href=\"#!/api/Ext.layout.ContextItem-method-setProp\" rel=\"Ext.layout.ContextItem-method-setProp\" class=\"docClass\">setProp</a> will place a value in the item and not\ndirectly into the DOM.</p>\n\n<p><strong>D.</strong> Call the other layouts now in the first cycle (repeat <strong>B</strong> and <strong>C</strong> for each\nlayout).</p>\n\n<p><strong>E.</strong> After completing a cycle, if progress was made (new properties were written to\nthe context) and if the <a href=\"#!/api/Ext.layout.Context-property-layoutQueue\" rel=\"Ext.layout.Context-property-layoutQueue\" class=\"docClass\">layoutQueue</a> is not empty, the next cycle is run. If no\nprogress was made or no layouts are ready to run, all buffered values are written to\nthe DOM (a flush).</p>\n\n<p><strong>F.</strong> After flushing, any layouts that were marked as <a href=\"#!/api/Ext.layout.Layout-property-done\" rel=\"Ext.layout.Layout-property-done\" class=\"docClass\">done</a>\nthat also have a <a href=\"#!/api/Ext.layout.Layout-method-completeLayout\" rel=\"Ext.layout.Layout-method-completeLayout\" class=\"docClass\">completeLayout</a> method are called.\nThis can cause them to become no longer done (see <a href=\"#!/api/Ext.layout.Context-method-invalidate\" rel=\"Ext.layout.Context-method-invalidate\" class=\"docClass\">invalidate</a>). As with\n<a href=\"#!/api/Ext.layout.Layout-method-calculate\" rel=\"Ext.layout.Layout-method-calculate\" class=\"docClass\">calculate</a>, this is considered a \"read phase\" and\ndirect DOM writes should be avoided.</p>\n\n<p><strong>G.</strong> Flushing and calling any pending <a href=\"#!/api/Ext.layout.Layout-method-completeLayout\" rel=\"Ext.layout.Layout-method-completeLayout\" class=\"docClass\">completeLayout</a>\nmethods will likely trigger layouts that called <a href=\"#!/api/Ext.layout.ContextItem-method-getDomProp\" rel=\"Ext.layout.ContextItem-method-getDomProp\" class=\"docClass\">getDomProp</a>\nand unblock layouts that have called <a href=\"#!/api/Ext.layout.ContextItem-method-domBlock\" rel=\"Ext.layout.ContextItem-method-domBlock\" class=\"docClass\">domBlock</a>.\nThese variants are used when a layout needs the value to be correct in the DOM and not\nsimply known. If this does not cause at least one layout to enter the queue, we have a\nlayout FAILURE. Otherwise, we continue with the next cycle.</p>\n\n<p><strong>H.</strong> Call <a href=\"#!/api/Ext.layout.Layout-method-calculate\" rel=\"Ext.layout.Layout-method-calculate\" class=\"docClass\">calculate</a> on any layouts in the queue\nat the start of this cycle. Just a repeat of <strong>B</strong> through <strong>G</strong>.</p>\n\n<p><strong>I.</strong> Once the layout has calculated all that it is resposible for, it can leave itself\nin the <a href=\"#!/api/Ext.layout.Layout-property-done\" rel=\"Ext.layout.Layout-property-done\" class=\"docClass\">done</a> state. This is the value on entry to\n<a href=\"#!/api/Ext.layout.Layout-method-calculate\" rel=\"Ext.layout.Layout-method-calculate\" class=\"docClass\">calculate</a> and must be cleared in that call if the\nlayout has more work to do.</p>\n\n<p><strong>J.</strong> Now that all layouts are done, flush any DOM values and\n<a href=\"#!/api/Ext.layout.Layout-method-completeLayout\" rel=\"Ext.layout.Layout-method-completeLayout\" class=\"docClass\">completeLayout</a> calls. This can again cause\nlayouts to become not done, and so we will be back on another cycle if that happens.</p>\n\n<p><strong>K.</strong> After all layouts are done, call the <a href=\"#!/api/Ext.layout.Layout-method-finalizeLayout\" rel=\"Ext.layout.Layout-method-finalizeLayout\" class=\"docClass\">finalizeLayout</a>\nmethod on any layouts that have one. As with <a href=\"#!/api/Ext.layout.Layout-method-completeLayout\" rel=\"Ext.layout.Layout-method-completeLayout\" class=\"docClass\">completeLayout</a>,\nthis can cause layouts to become no longer done. This is less desirable than using\n<a href=\"#!/api/Ext.layout.Layout-method-completeLayout\" rel=\"Ext.layout.Layout-method-completeLayout\" class=\"docClass\">completeLayout</a> because it will cause all\n<a href=\"#!/api/Ext.layout.Layout-method-finalizeLayout\" rel=\"Ext.layout.Layout-method-finalizeLayout\" class=\"docClass\">finalizeLayout</a> methods to be called again\nwhen we think things are all wrapped up.</p>\n\n<p><strong>L.</strong> After finishing the last iteration, layouts that have a\n<a href=\"#!/api/Ext.layout.Layout-method-finishedLayout\" rel=\"Ext.layout.Layout-method-finishedLayout\" class=\"docClass\">finishedLayout</a> method will be called. This\ncall will only happen once per run and cannot cause layouts to be run further.</p>\n\n<p><strong>M.</strong> After calling finahedLayout, layouts that have a\n<a href=\"#!/api/Ext.layout.Layout-method-notifyOwner\" rel=\"Ext.layout.Layout-method-notifyOwner\" class=\"docClass\">notifyOwner</a> method will be called. This\ncall will only happen once per run and cannot cause layouts to be run further.</p>\n\n<p><strong>N.</strong> One last flush to make sure everything has been written to the DOM.</p>\n\n<h1>Inter-Layout Collaboration</h1>\n\n<p>Many layout problems require collaboration between multiple layouts. In some cases, this\nis as simple as a component's container layout providing results used by its component\nlayout or vise-versa. A slightly more distant collaboration occurs in a box layout when\nstretchmax is used: the child item's component layout provides results that are consumed\nby the ownerCt's box layout to determine the size of the children.</p>\n\n<p>The various forms of interdependence between a container and its children are described by\neach components' <a href=\"#!/api/Ext.AbstractComponent-method-getSizeModel\" rel=\"Ext.AbstractComponent-method-getSizeModel\" class=\"docClass\">size model</a>.</p>\n\n<p>To facilitate this collaboration, the following pairs of properties are published to the\ncomponent's <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">ContextItem</a>:</p>\n\n<ul>\n<li>width/height: These hold the final size of the component. The layout indicated by the\n<a href=\"#!/api/Ext.AbstractComponent-method-getSizeModel\" rel=\"Ext.AbstractComponent-method-getSizeModel\" class=\"docClass\">size model</a> is responsible for setting these.</li>\n<li>contentWidth/contentHeight: These hold size information published by the container\nlayout or from DOM measurement. These describe the content only. These values are\nused by the component layout to determine the outer width/height when that component\nis <a href=\"#!/api/Ext.AbstractComponent-cfg-shrinkWrap\" rel=\"Ext.AbstractComponent-cfg-shrinkWrap\" class=\"docClass\">shrink-wrapped</a>. They are also used to\ndetermine overflow. All container layouts must publish these values for dimensions\nthat are shrink-wrapped. If a component has raw content (not container items), the\ncomponentLayout must publish these values instead.</li>\n</ul>\n\n</div><div class='members'><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-layoutQueue' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-property-layoutQueue' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-property-layoutQueue' class='name not-expandable'>layoutQueue</a><span> : <a href=\"#!/api/Ext.util.Queue\" rel=\"Ext.util.Queue\" class=\"docClass\">Ext.util.Queue</a></span></div><div class='description'><div class='short'><p>List of layouts to perform.</p>\n</div><div class='long'><p>List of layouts to perform.</p>\n</div></div></div><div id='property-logOn' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context.html#Ext-layout-Context-property-logOn' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-property-logOn' class='name expandable'>logOn</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Defined in override Ext.diag.layout.Context. ...</div><div class='long'><p><strong>Defined in override Ext.diag.layout.Context.</strong></p>\n<p>Defaults to: <code>{0: 0}</code></p></div></div></div><div id='property-remainingLayouts' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-property-remainingLayouts' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-property-remainingLayouts' class='name expandable'>remainingLayouts</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div><div id='property-state' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-property-state' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-property-state' class='name expandable'>state</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>One of these values:\n\n\n0 - Before run\n1 - Running\n2 - Run complete ...</div><div class='long'><p>One of these values:</p>\n\n<ul>\n<li>0 - Before run</li>\n<li>1 - Running</li>\n<li>2 - Run complete</li>\n</ul>\n\n<p>Defaults to: <code>0</code></p></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/Ext.layout.Context-method-constructor' class='name expandable'>Ext.layout.Context</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.layout.Context\" rel=\"Ext.layout.Context\" class=\"docClass\">Ext.layout.Context</a></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.layout.Context\" rel=\"Ext.layout.Context\" class=\"docClass\">Ext.layout.Context</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-callLayout' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-callLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-callLayout' class='name expandable'>callLayout</a>( <span class='pre'>layout, methodName</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Overridden in Ext.diag.layout.Context. ...</div><div class='long'><p><strong>Overridden in Ext.diag.layout.Context.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>layout</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>methodName</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-callOverridden' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-cancelComponent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-cancelComponent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-cancelComponent' class='name expandable'>cancelComponent</a>( <span class='pre'>comp, isChild, isDestroying</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>From override Ext.diag.layout.Context: reportOnSuccess: true, ...</div><div class='long'><p><strong>From override Ext.diag.layout.Context:</strong> reportOnSuccess: true,</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>comp</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>isChild</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>isDestroying</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-cancelLayout' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-cancelLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-cancelLayout' class='name expandable'>cancelLayout</a>( <span class='pre'>layout</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Overridden in Ext.diag.layout.Context. ...</div><div class='long'><p><strong>Overridden in Ext.diag.layout.Context.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>layout</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-chainFns' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-chainFns' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-chainFns' class='name expandable'>chainFns</a>( <span class='pre'>oldOptions, newOptions, funcName</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>oldOptions</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>newOptions</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>funcName</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-checkRemainingLayouts' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context.html#Ext-layout-Context-method-checkRemainingLayouts' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-checkRemainingLayouts' class='name expandable'>checkRemainingLayouts</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Defined in override Ext.diag.layout.Context. ...</div><div class='long'><p><strong>Defined in override Ext.diag.layout.Context.</strong></p>\n</div></div></div><div id='method-clearTriggers' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-clearTriggers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-clearTriggers' class='name expandable'>clearTriggers</a>( <span class='pre'>layout, inDom</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>layout</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>inDom</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-destroy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-destroy' class='name expandable'>destroy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.util.ElementContainer-method-destroy' rel='Ext.util.ElementContainer-method-destroy' class='docClass'>Ext.util.ElementContainer.destroy</a>, <a href='#!/api/Ext.AbstractComponent-method-destroy' rel='Ext.AbstractComponent-method-destroy' class='docClass'>Ext.AbstractComponent.destroy</a>, <a href='#!/api/Ext.AbstractPlugin-method-destroy' rel='Ext.AbstractPlugin-method-destroy' class='docClass'>Ext.AbstractPlugin.destroy</a></p></div></div></div><div id='method-flush' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-flush' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-flush' class='name expandable'>flush</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Flushes any pending writes to the DOM by calling each ContextItem in the flushQueue. ...</div><div class='long'><p>Flushes any pending writes to the DOM by calling each ContextItem in the flushQueue.</p>\n\n<p><strong>Overridden in Ext.diag.layout.Context.</strong></p>\n</div></div></div><div id='method-flushAnimations' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-flushAnimations' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-flushAnimations' class='name expandable'>flushAnimations</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-flushInvalidates' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-flushInvalidates' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-flushInvalidates' class='name expandable'>flushInvalidates</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Overridden in Ext.diag.layout.Context. ...</div><div class='long'><p><strong>Overridden in Ext.diag.layout.Context.</strong></p>\n</div></div></div><div id='method-flushLayouts' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-flushLayouts' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-flushLayouts' class='name expandable'>flushLayouts</a>( <span class='pre'>queueName, methodName, dontClear</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>queueName</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>methodName</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>dontClear</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getCmp' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-getCmp' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-getCmp' class='name expandable'>getCmp</a>( <span class='pre'>cmp</span> )</div><div class='description'><div class='short'>Returns the ContextItem for a component. ...</div><div class='long'><p>Returns the ContextItem for a component.</p>\n\n<p><strong>Overridden in Ext.diag.layout.Context.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>cmp</span> : <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getEl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-getEl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-getEl' class='name expandable'>getEl</a>( <span class='pre'>parent, el</span> )</div><div class='description'><div class='short'>Returns the ContextItem for an element. ...</div><div class='long'><p>Returns the ContextItem for an element.</p>\n\n<p><strong>Overridden in Ext.diag.layout.Context.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>parent</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'>\n</div></li><li><span class='pre'>el</span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-getItem' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-getItem' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-getItem' class='name expandable'>getItem</a>( <span class='pre'>target, el</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>el</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getLayoutName' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context.html#Ext-layout-Context-method-getLayoutName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-getLayoutName' class='name expandable'>getLayoutName</a>( <span class='pre'>layout</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Defined in override Ext.diag.layout.Context. ...</div><div class='long'><p><strong>Defined in override Ext.diag.layout.Context.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>layout</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-handleFailure' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-handleFailure' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-handleFailure' class='name expandable'>handleFailure</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-invalidate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-invalidate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-invalidate' class='name expandable'>invalidate</a>( <span class='pre'>components, full</span> )</div><div class='description'><div class='short'>Invalidates one or more components' layouts (component and container). ...</div><div class='long'><p>Invalidates one or more components' layouts (component and container). This can be\ncalled before run to identify the components that need layout or during the run to\nrestart the layout of a component. This is called internally to flush any queued\ninvalidations at the start of a cycle. If called during a run, it is not expected\nthat new components will be introduced to the layout.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>components</span> : <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>An array of Components or a single Component.</p>\n</div></li><li><span class='pre'>full</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True if all properties should be invalidated, otherwise only\n those calculated by the component should be invalidated.</p>\n</div></li></ul></div></div></div><div id='method-layoutDone' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-layoutDone' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-layoutDone' class='name expandable'>layoutDone</a>( <span class='pre'>layout</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Overridden in Ext.diag.layout.Context. ...</div><div class='long'><p><strong>Overridden in Ext.diag.layout.Context.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>layout</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-layoutTreeHasFailures' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context.html#Ext-layout-Context-method-layoutTreeHasFailures' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-layoutTreeHasFailures' class='name expandable'>layoutTreeHasFailures</a>( <span class='pre'>layout, reported</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Defined in override Ext.diag.layout.Context. ...</div><div class='long'><p><strong>Defined in override Ext.diag.layout.Context.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>layout</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>reported</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-newQueue' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-newQueue' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-newQueue' class='name expandable'>newQueue</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-processInvalidate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-processInvalidate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-processInvalidate' class='name expandable'>processInvalidate</a>( <span class='pre'>options, item, name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-queueAnimation' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-queueAnimation' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-queueAnimation' class='name expandable'>queueAnimation</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Queues a ContextItem to have its Ext.layout.ContextItem.flushAnimations method called. ...</div><div class='long'><p>Queues a ContextItem to have its <a href=\"#!/api/Ext.layout.ContextItem-method-flushAnimations\" rel=\"Ext.layout.ContextItem-method-flushAnimations\" class=\"docClass\">Ext.layout.ContextItem.flushAnimations</a> method called.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-queueCompletion' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-queueCompletion' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-queueCompletion' class='name expandable'>queueCompletion</a>( <span class='pre'>layout</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Queues a layout to have its Ext.layout.Layout.completeLayout method called. ...</div><div class='long'><p>Queues a layout to have its <a href=\"#!/api/Ext.layout.Layout-method-completeLayout\" rel=\"Ext.layout.Layout-method-completeLayout\" class=\"docClass\">Ext.layout.Layout.completeLayout</a> method called.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>layout</span> : <a href=\"#!/api/Ext.layout.Layout\" rel=\"Ext.layout.Layout\" class=\"docClass\">Ext.layout.Layout</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-queueFinalize' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-queueFinalize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-queueFinalize' class='name expandable'>queueFinalize</a>( <span class='pre'>layout</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Queues a layout to have its Ext.layout.Layout.finalizeLayout method called. ...</div><div class='long'><p>Queues a layout to have its <a href=\"#!/api/Ext.layout.Layout-method-finalizeLayout\" rel=\"Ext.layout.Layout-method-finalizeLayout\" class=\"docClass\">Ext.layout.Layout.finalizeLayout</a> method called.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>layout</span> : <a href=\"#!/api/Ext.layout.Layout\" rel=\"Ext.layout.Layout\" class=\"docClass\">Ext.layout.Layout</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-queueFlush' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-queueFlush' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-queueFlush' class='name expandable'>queueFlush</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Queues a ContextItem for the next flush to the DOM. ...</div><div class='long'><p>Queues a ContextItem for the next flush to the DOM. This should only be called by\nthe <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a> class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-queueInvalidate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-queueInvalidate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-queueInvalidate' class='name expandable'>queueInvalidate</a>( <span class='pre'>item, options</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Queue a component (and its tree) to be invalidated on the next cycle. ...</div><div class='long'><p>Queue a component (and its tree) to be invalidated on the next cycle.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>/<a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'><p>The component or ContextItem to invalidate.</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>An object describing how to handle the invalidation (see\n <a href=\"#!/api/Ext.layout.ContextItem-method-invalidate\" rel=\"Ext.layout.ContextItem-method-invalidate\" class=\"docClass\">Ext.layout.ContextItem.invalidate</a> for details).</p>\n</div></li></ul></div></div></div><div id='method-queueItemLayouts' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-queueItemLayouts' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-queueItemLayouts' class='name expandable'>queueItemLayouts</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-queueLayout' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-queueLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-queueLayout' class='name expandable'>queueLayout</a>( <span class='pre'>layout</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Queues a layout for the next calculation cycle. ...</div><div class='long'><p>Queues a layout for the next calculation cycle. This should not be called if the\nlayout is done, blocked or already in the queue. The only classes that should call\nthis method are this class and <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a>.</p>\n\n<p><strong>Overridden in Ext.diag.layout.Context.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>layout</span> : <a href=\"#!/api/Ext.layout.Layout\" rel=\"Ext.layout.Layout\" class=\"docClass\">Ext.layout.Layout</a><div class='sub-desc'><p>The layout to add to the queue.</p>\n</div></li></ul></div></div></div><div id='method-removeEl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-removeEl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-removeEl' class='name expandable'>removeEl</a>( <span class='pre'>parent, el</span> )</div><div class='description'><div class='short'>Removes the ContextItem for an element from the cache and from the parent's\n\"children\" array. ...</div><div class='long'><p>Removes the ContextItem for an element from the cache and from the parent's\n\"children\" array.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>parent</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'>\n</div></li><li><span class='pre'>el</span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-reportLayoutResult' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context.html#Ext-layout-Context-method-reportLayoutResult' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-reportLayoutResult' class='name expandable'>reportLayoutResult</a>( <span class='pre'>layout, reported</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Defined in override Ext.diag.layout.Context. ...</div><div class='long'><p><strong>Defined in override Ext.diag.layout.Context.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>layout</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>reported</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-resetLayout' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-resetLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-resetLayout' class='name expandable'>resetLayout</a>( <span class='pre'>layout, ownerContext, firstTime</span> )</div><div class='description'><div class='short'>Resets the given layout object. ...</div><div class='long'><p>Resets the given layout object. This is called at the start of the run and can also\nbe called during the run by calling <a href=\"#!/api/Ext.layout.Context-method-invalidate\" rel=\"Ext.layout.Context-method-invalidate\" class=\"docClass\">invalidate</a>.</p>\n\n<p><strong>Overridden in Ext.diag.layout.Context.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>layout</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>firstTime</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-round' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context.html#Ext-layout-Context-method-round' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-round' class='name expandable'>round</a>( <span class='pre'>t</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Defined in override Ext.diag.layout.Context. ...</div><div class='long'><p><strong>Defined in override Ext.diag.layout.Context.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>t</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-run' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-run' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-run' class='name expandable'>run</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Runs the layout calculations. ...</div><div class='long'><p>Runs the layout calculations. This can be called only once on this object.</p>\n\n<p><strong>Overridden in Ext.diag.layout.Context.</strong></p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if all layouts were completed, false if not.</p>\n</div></li></ul></div></div></div><div id='method-runComplete' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-runComplete' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-runComplete' class='name expandable'>runComplete</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-runCycle' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-runCycle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-runCycle' class='name expandable'>runCycle</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Performs one layout cycle by calling each layout in the layout queue. ...</div><div class='long'><p>Performs one layout cycle by calling each layout in the layout queue.</p>\n\n<p><strong>Overridden in Ext.diag.layout.Context.</strong></p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if some progress was made, false if not.</p>\n</div></li></ul></div></div></div><div id='method-runLayout' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-runLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-runLayout' class='name expandable'>runLayout</a>( <span class='pre'>layout</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Runs one layout as part of a cycle. ...</div><div class='long'><p>Runs one layout as part of a cycle.</p>\n\n<p><strong>Overridden in Ext.diag.layout.Context.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>layout</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setItemSize' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.Context'>Ext.layout.Context</span><br/><a href='source/Context2.html#Ext-layout-Context-method-setItemSize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Context-method-setItemSize' class='name expandable'>setItemSize</a>( <span class='pre'>items, width, height</span> )</div><div class='description'><div class='short'>Set the size of a component, element or composite or an array of components or elements. ...</div><div class='long'><p>Set the size of a component, element or composite or an array of components or elements.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>items</span> : <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>/<a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>[]/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>[]/<a href=\"#!/api/Ext.dom.CompositeElement\" rel=\"Ext.dom.CompositeElement\" class=\"docClass\">Ext.dom.CompositeElement</a><div class='sub-desc'><p>The item(s) to size.</p>\n</div></li><li><span class='pre'>width</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The new width to set (ignored if undefined or NaN).</p>\n</div></li><li><span class='pre'>height</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The new height to set (ignored if undefined or NaN).</p>\n</div></li></ul></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-create' class='name expandable'>create</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create a new instance of this Class. ...</div><div class='long'><p>Create a new instance of this Class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.create({\n    someConfig: true\n});\n</code></pre>\n\n<p>All parameters are passed to the constructor of the class.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the created instance.</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div></div></div>","superclasses":["Ext.Base"],"meta":{"private":true},"code_type":"ext_define","requires":["Ext.fx.Anim","Ext.fx.Manager","Ext.layout.ContextItem","Ext.layout.Layout","Ext.util.Queue"],"html_meta":{"private":null},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"Context2.html#Ext-layout-Context","filename":"Context.js"},{"href":"Context.html#Ext-diag-layout-Context","filename":"Context.js"}],"linenr":1,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.layout.Context","meta":{},"name":"layoutQueue","id":"property-layoutQueue"},{"tagname":"property","owner":"Ext.layout.Context","meta":{"private":true},"name":"logOn","id":"property-logOn"},{"tagname":"property","owner":"Ext.layout.Context","meta":{"private":true},"name":"remainingLayouts","id":"property-remainingLayouts"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"},{"tagname":"property","owner":"Ext.layout.Context","meta":{},"name":"state","id":"property-state"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.layout.Context","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"callLayout","id":"method-callLayout"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"cancelComponent","id":"method-cancelComponent"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"cancelLayout","id":"method-cancelLayout"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"chainFns","id":"method-chainFns"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"checkRemainingLayouts","id":"method-checkRemainingLayouts"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"clearTriggers","id":"method-clearTriggers"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.layout.Context","meta":{},"name":"flush","id":"method-flush"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"flushAnimations","id":"method-flushAnimations"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"flushInvalidates","id":"method-flushInvalidates"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"flushLayouts","id":"method-flushLayouts"},{"tagname":"method","owner":"Ext.layout.Context","meta":{},"name":"getCmp","id":"method-getCmp"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.layout.Context","meta":{},"name":"getEl","id":"method-getEl"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"getItem","id":"method-getItem"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"getLayoutName","id":"method-getLayoutName"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"handleFailure","id":"method-handleFailure"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.layout.Context","meta":{},"name":"invalidate","id":"method-invalidate"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"layoutDone","id":"method-layoutDone"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"layoutTreeHasFailures","id":"method-layoutTreeHasFailures"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"newQueue","id":"method-newQueue"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"processInvalidate","id":"method-processInvalidate"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"queueAnimation","id":"method-queueAnimation"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"queueCompletion","id":"method-queueCompletion"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"queueFinalize","id":"method-queueFinalize"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"queueFlush","id":"method-queueFlush"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"queueInvalidate","id":"method-queueInvalidate"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"queueItemLayouts","id":"method-queueItemLayouts"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"queueLayout","id":"method-queueLayout"},{"tagname":"method","owner":"Ext.layout.Context","meta":{},"name":"removeEl","id":"method-removeEl"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"reportLayoutResult","id":"method-reportLayoutResult"},{"tagname":"method","owner":"Ext.layout.Context","meta":{},"name":"resetLayout","id":"method-resetLayout"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"round","id":"method-round"},{"tagname":"method","owner":"Ext.layout.Context","meta":{},"name":"run","id":"method-run"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"runComplete","id":"method-runComplete"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"protected":true},"name":"runCycle","id":"method-runCycle"},{"tagname":"method","owner":"Ext.layout.Context","meta":{"private":true},"name":"runLayout","id":"method-runLayout"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.layout.Context","meta":{},"name":"setItemSize","id":"method-setItemSize"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"}],"event":[],"css_mixin":[]},"inheritable":null,"private":true,"component":false,"name":"Ext.layout.Context","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.layout.Context","mixins":[],"mixedInto":[]});