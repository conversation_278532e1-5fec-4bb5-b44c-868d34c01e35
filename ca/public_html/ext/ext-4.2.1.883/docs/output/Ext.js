Ext.data.JsonP.Ext({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Ext-more.html#Ext' target='_blank'>Ext-more.js</a></div><div class='dependency'><a href='source/Ext-more.html#Ext' target='_blank'>Ext-more.js</a></div><div class='dependency'><a href='source/Ext.html#Ext' target='_blank'>Ext.js</a></div><div class='dependency'><a href='source/Version.html#Ext' target='_blank'>Version.js</a></div></pre><div class='doc-contents'><p>The Ext namespace (global object) encapsulates all classes, singletons, and\nutility methods provided by Sencha's libraries.</p>\n\n<p>Most user interface Components are at a lower level of nesting in the namespace,\nbut many common utility functions are provided as direct properties of the Ext namespace.</p>\n\n<p>Also many frequently used methods from other classes are provided as shortcuts\nwithin the Ext namespace. For example <a href=\"#!/api/Ext-method-getCmp\" rel=\"Ext-method-getCmp\" class=\"docClass\">Ext.getCmp</a> aliases\n<a href=\"#!/api/Ext.ComponentManager-method-get\" rel=\"Ext.ComponentManager-method-get\" class=\"docClass\">Ext.ComponentManager.get</a>.</p>\n\n<p>Many applications are initiated with <a href=\"#!/api/Ext-method-onReady\" rel=\"Ext-method-onReady\" class=\"docClass\">Ext.onReady</a> which is\ncalled once the DOM is ready. This ensures all scripts have been loaded,\npreventing dependency issues. For example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-onReady\" rel=\"Ext-method-onReady\" class=\"docClass\">Ext.onReady</a>(function(){\n    new <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>({\n        renderTo: document.body,\n        html: 'DOM ready!'\n    });\n});\n</code></pre>\n\n<p>For more information about how to use the Ext classes, see:</p>\n\n<ul>\n<li><a href=\"http://www.sencha.com/learn/\">The Learning Center</a></li>\n<li><a href=\"http://www.sencha.com/learn/Ext_FAQ\">The FAQ</a></li>\n<li><a href=\"http://www.sencha.com/forum/\">The forums</a></li>\n</ul>\n\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-BLANK_IMAGE_URL' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-BLANK_IMAGE_URL' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-BLANK_IMAGE_URL' class='name expandable'>BLANK_IMAGE_URL</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>URL to a 1x1 transparent gif image used by Ext to create inline icons with\nCSS background images. ...</div><div class='long'><p>URL to a 1x1 transparent gif image used by Ext to create inline icons with\nCSS background images. In older versions of IE, this defaults to\n\"http://sencha.com/s.gif\" and you should change this to a URL on your server.\nFor other browsers it uses an inline data URL.</p>\n</div></div></div><div id='property-Logger' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-property-Logger' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-Logger' class='name not-expandable'>Logger</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-SSL_SECURE_URL' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-SSL_SECURE_URL' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-SSL_SECURE_URL' class='name expandable'>SSL_SECURE_URL</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>URL to a blank file used by Ext when in secure mode for iframe src and onReady src\nto prevent the IE insecure content...</div><div class='long'><p>URL to a blank file used by Ext when in secure mode for iframe src and onReady src\nto prevent the IE insecure content warning (<code>'about:blank'</code>, except for IE\nin secure mode, which is <code>'javascript:\"\"'</code>).</p>\n</div></div></div><div id='property-USE_NATIVE_JSON' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-USE_NATIVE_JSON' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-USE_NATIVE_JSON' class='name expandable'>USE_NATIVE_JSON</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Indicates whether to use native browser parsing for JSON methods. ...</div><div class='long'><p>Indicates whether to use native browser parsing for JSON methods.\nThis option is ignored if the browser does not support native JSON methods.</p>\n\n<p><strong>Note:</strong> Native JSON methods will not work with objects that have functions.\nAlso, property names must be quoted, otherwise the data will not parse.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-chromeVersion' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-chromeVersion' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-chromeVersion' class='name not-expandable'>chromeVersion</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'><p>The current version of Chrome (0 if the browser is not Chrome).</p>\n</div><div class='long'><p>The current version of Chrome (0 if the browser is not Chrome).</p>\n</div></div></div><div id='property-emptyFn' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-property-emptyFn' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-emptyFn' class='name not-expandable'>emptyFn</a><span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></span></div><div class='description'><div class='short'><p>A reusable empty function</p>\n</div><div class='long'><p>A reusable empty function</p>\n</div></div></div><div id='property-emptyString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-property-emptyString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-emptyString' class='name expandable'>emptyString</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>A zero length string which will pass a truth test. ...</div><div class='long'><p>A zero length string which will pass a truth test. Useful for passing to methods\nwhich use a truth test to reject <i>falsy</i> values where a string value must be cleared.</p>\n</div></div></div><div id='property-enableFx' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-enableFx' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-enableFx' class='name not-expandable'>enableFx</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the <a href=\"#!/api/Ext.fx.Anim\" rel=\"Ext.fx.Anim\" class=\"docClass\">Ext.fx.Anim</a> Class is available.</p>\n</div><div class='long'><p>True if the <a href=\"#!/api/Ext.fx.Anim\" rel=\"Ext.fx.Anim\" class=\"docClass\">Ext.fx.Anim</a> Class is available.</p>\n</div></div></div><div id='property-enableGarbageCollector' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-enableGarbageCollector' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-enableGarbageCollector' class='name expandable'>enableGarbageCollector</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True to automatically uncache orphaned Ext.Elements periodically ...</div><div class='long'><p>True to automatically uncache orphaned Ext.Elements periodically</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-enableListenerCollection' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-enableListenerCollection' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-enableListenerCollection' class='name expandable'>enableListenerCollection</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True to automatically purge event listeners during garbageCollection. ...</div><div class='long'><p>True to automatically purge event listeners during garbageCollection.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-enableNestedListenerRemoval' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-enableNestedListenerRemoval' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-enableNestedListenerRemoval' class='name expandable'>enableNestedListenerRemoval</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Experimental. ...</div><div class='long'><p><strong>Experimental.</strong> True to cascade listener removal to child elements when an element\nis removed. Currently not optimized for performance.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-enumerables' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-property-enumerables' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-enumerables' class='name not-expandable'>enumerables</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</span></div><div class='description'><div class='short'><p>An array containing extra enumerables for old browsers</p>\n</div><div class='long'><p>An array containing extra enumerables for old browsers</p>\n</div></div></div><div id='property-firefoxVersion' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-firefoxVersion' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-firefoxVersion' class='name not-expandable'>firefoxVersion</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'><p>The current version of Firefox (0 if the browser is not Firefox).</p>\n</div><div class='long'><p>The current version of Firefox (0 if the browser is not Firefox).</p>\n</div></div></div><div id='property-functionFactoryCache' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-property-functionFactoryCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-functionFactoryCache' class='name expandable'>functionFactoryCache</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-globalEvents' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Observable.html#Ext-property-globalEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-globalEvents' class='name expandable'>globalEvents</a><span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a></span></div><div class='description'><div class='short'>An instance of Ext.util.Observable through which Ext fires global events. ...</div><div class='long'><p>An instance of <code><a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a></code> through which Ext fires global events.</p>\n\n<p>This Observable instance fires the following events:</p>\n\n<ul>\n<li><p><strong><code>idle</code></strong></p>\n\n<p>Fires when an event handler finishes its run, just before returning to browser control.</p>\n\n<p>This includes DOM event handlers, Ajax (including JSONP) event handlers, and <a href=\"#!/api/Ext.util.TaskRunner\" rel=\"Ext.util.TaskRunner\" class=\"docClass\">TaskRunners</a></p>\n\n<p>This can be useful for performing cleanup, or update tasks which need to happen only\nafter all code in an event handler has been run, but which should not be executed in a timer\ndue to the intervening browser reflow/repaint which would take place.</p></li>\n<li><p><strong><code>ready</code></strong></p>\n\n<p> Fires when the DOM is ready, and all required classes have been loaded. Functionally\n the same as <a href=\"#!/api/Ext-method-onReady\" rel=\"Ext-method-onReady\" class=\"docClass\">onReady</a>, but must be called with the <code>single</code> option:</p>\n\n<pre><code>  <a href=\"#!/api/Ext-method-on\" rel=\"Ext-method-on\" class=\"docClass\">Ext.on</a>({\n      ready: function() {\n          console.log('document is ready!');\n      },\n      single: true\n  }); \n</code></pre></li>\n<li><p><strong><code>resumelayouts</code></strong></p>\n\n<p> Fires after global layout processing has been resumed in <a href=\"#!/api/Ext.AbstractComponent-method-resumeLayouts\" rel=\"Ext.AbstractComponent-method-resumeLayouts\" class=\"docClass\">Ext.AbstractComponent.resumeLayouts</a>.</p></li>\n</ul>\n\n</div></div></div><div id='property-ieVersion' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-ieVersion' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-ieVersion' class='name expandable'>ieVersion</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The current version of IE (0 if the browser is not IE). ...</div><div class='long'><p>The current version of IE (0 if the browser is not IE). This does not account\nfor the documentMode of the current page, which is factored into <a href=\"#!/api/Ext-property-isIE7\" rel=\"Ext-property-isIE7\" class=\"docClass\">isIE7</a>,\n<a href=\"#!/api/Ext-property-isIE8\" rel=\"Ext-property-isIE8\" class=\"docClass\">isIE8</a> and <a href=\"#!/api/Ext-property-isIE9\" rel=\"Ext-property-isIE9\" class=\"docClass\">isIE9</a>. Thus this is not always true:</p>\n\n<pre><code><a href=\"#!/api/Ext-property-isIE8\" rel=\"Ext-property-isIE8\" class=\"docClass\">Ext.isIE8</a> == (<a href=\"#!/api/Ext-property-ieVersion\" rel=\"Ext-property-ieVersion\" class=\"docClass\">Ext.ieVersion</a> == 8)\n</code></pre>\n</div></div></div><div id='property-isChrome' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isChrome' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isChrome' class='name not-expandable'>isChrome</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Chrome.</p>\n</div><div class='long'><p>True if the detected browser is Chrome.</p>\n</div></div></div><div id='property-isFF10' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isFF10' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isFF10' class='name not-expandable'>isFF10</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser uses FireFox 10</p>\n</div><div class='long'><p>True if the detected browser uses FireFox 10</p>\n</div></div></div><div id='property-isFF3_0' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isFF3_0' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isFF3_0' class='name not-expandable'>isFF3_0</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser uses FireFox 3.0</p>\n</div><div class='long'><p>True if the detected browser uses FireFox 3.0</p>\n</div></div></div><div id='property-isFF3_5' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isFF3_5' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isFF3_5' class='name not-expandable'>isFF3_5</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser uses FireFox 3.5</p>\n</div><div class='long'><p>True if the detected browser uses FireFox 3.5</p>\n</div></div></div><div id='property-isFF3_6' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isFF3_6' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isFF3_6' class='name not-expandable'>isFF3_6</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser uses FireFox 3.6</p>\n</div><div class='long'><p>True if the detected browser uses FireFox 3.6</p>\n</div></div></div><div id='property-isFF4' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isFF4' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isFF4' class='name not-expandable'>isFF4</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser uses FireFox 4</p>\n</div><div class='long'><p>True if the detected browser uses FireFox 4</p>\n</div></div></div><div id='property-isFF5' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isFF5' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isFF5' class='name not-expandable'>isFF5</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser uses FireFox 5</p>\n</div><div class='long'><p>True if the detected browser uses FireFox 5</p>\n</div></div></div><div id='property-isGecko' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isGecko' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isGecko' class='name expandable'>isGecko</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True if the detected browser uses the Gecko layout engine (e.g. ...</div><div class='long'><p>True if the detected browser uses the Gecko layout engine (e.g. Mozilla, Firefox).</p>\n</div></div></div><div id='property-isGecko10' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isGecko10' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isGecko10' class='name expandable'>isGecko10</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True if the detected browser uses a Gecko 5.0+ layout engine (e.g. ...</div><div class='long'><p>True if the detected browser uses a Gecko 5.0+ layout engine (e.g. Firefox 5.x).</p>\n</div></div></div><div id='property-isGecko3' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isGecko3' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isGecko3' class='name expandable'>isGecko3</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True if the detected browser uses a Gecko 1.9+ layout engine (e.g. ...</div><div class='long'><p>True if the detected browser uses a Gecko 1.9+ layout engine (e.g. Firefox 3.x).</p>\n</div></div></div><div id='property-isGecko4' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isGecko4' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isGecko4' class='name expandable'>isGecko4</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True if the detected browser uses a Gecko 2.0+ layout engine (e.g. ...</div><div class='long'><p>True if the detected browser uses a Gecko 2.0+ layout engine (e.g. Firefox 4.x).</p>\n</div></div></div><div id='property-isGecko5' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isGecko5' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isGecko5' class='name expandable'>isGecko5</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True if the detected browser uses a Gecko 5.0+ layout engine (e.g. ...</div><div class='long'><p>True if the detected browser uses a Gecko 5.0+ layout engine (e.g. Firefox 5.x).</p>\n</div></div></div><div id='property-isIE' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isIE' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isIE' class='name not-expandable'>isIE</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Internet Explorer.</p>\n</div><div class='long'><p>True if the detected browser is Internet Explorer.</p>\n</div></div></div><div id='property-isIE10' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isIE10' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isIE10' class='name not-expandable'>isIE10</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Internet Explorer 10.x.</p>\n</div><div class='long'><p>True if the detected browser is Internet Explorer 10.x.</p>\n</div></div></div><div id='property-isIE10m' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isIE10m' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isIE10m' class='name not-expandable'>isIE10m</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Internet Explorer 10.x or lower.</p>\n</div><div class='long'><p>True if the detected browser is Internet Explorer 10.x or lower.</p>\n</div></div></div><div id='property-isIE10p' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isIE10p' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isIE10p' class='name not-expandable'>isIE10p</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Internet Explorer 10.x or higher.</p>\n</div><div class='long'><p>True if the detected browser is Internet Explorer 10.x or higher.</p>\n</div></div></div><div id='property-isIE6' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isIE6' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isIE6' class='name not-expandable'>isIE6</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Internet Explorer 6.x.</p>\n</div><div class='long'><p>True if the detected browser is Internet Explorer 6.x.</p>\n</div></div></div><div id='property-isIE7' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isIE7' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isIE7' class='name not-expandable'>isIE7</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Internet Explorer 7.x.</p>\n</div><div class='long'><p>True if the detected browser is Internet Explorer 7.x.</p>\n</div></div></div><div id='property-isIE7m' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isIE7m' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isIE7m' class='name not-expandable'>isIE7m</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Internet Explorer 7.x or lower.</p>\n</div><div class='long'><p>True if the detected browser is Internet Explorer 7.x or lower.</p>\n</div></div></div><div id='property-isIE7p' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isIE7p' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isIE7p' class='name not-expandable'>isIE7p</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Internet Explorer 7.x or higher.</p>\n</div><div class='long'><p>True if the detected browser is Internet Explorer 7.x or higher.</p>\n</div></div></div><div id='property-isIE8' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isIE8' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isIE8' class='name not-expandable'>isIE8</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Internet Explorer 8.x.</p>\n</div><div class='long'><p>True if the detected browser is Internet Explorer 8.x.</p>\n</div></div></div><div id='property-isIE8m' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isIE8m' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isIE8m' class='name not-expandable'>isIE8m</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Internet Explorer 8.x or lower.</p>\n</div><div class='long'><p>True if the detected browser is Internet Explorer 8.x or lower.</p>\n</div></div></div><div id='property-isIE8p' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isIE8p' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isIE8p' class='name not-expandable'>isIE8p</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Internet Explorer 8.x or higher.</p>\n</div><div class='long'><p>True if the detected browser is Internet Explorer 8.x or higher.</p>\n</div></div></div><div id='property-isIE9' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isIE9' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isIE9' class='name not-expandable'>isIE9</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Internet Explorer 9.x.</p>\n</div><div class='long'><p>True if the detected browser is Internet Explorer 9.x.</p>\n</div></div></div><div id='property-isIE9m' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isIE9m' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isIE9m' class='name not-expandable'>isIE9m</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Internet Explorer 9.x or lower.</p>\n</div><div class='long'><p>True if the detected browser is Internet Explorer 9.x or lower.</p>\n</div></div></div><div id='property-isIE9p' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isIE9p' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isIE9p' class='name not-expandable'>isIE9p</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Internet Explorer 9.x or higher.</p>\n</div><div class='long'><p>True if the detected browser is Internet Explorer 9.x or higher.</p>\n</div></div></div><div id='property-isLinux' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isLinux' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isLinux' class='name not-expandable'>isLinux</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected platform is Linux.</p>\n</div><div class='long'><p>True if the detected platform is Linux.</p>\n</div></div></div><div id='property-isMac' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isMac' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isMac' class='name not-expandable'>isMac</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected platform is Mac OS.</p>\n</div><div class='long'><p>True if the detected platform is Mac OS.</p>\n</div></div></div><div id='property-isOpera' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isOpera' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isOpera' class='name not-expandable'>isOpera</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Opera.</p>\n</div><div class='long'><p>True if the detected browser is Opera.</p>\n</div></div></div><div id='property-isOpera10_5' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isOpera10_5' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isOpera10_5' class='name not-expandable'>isOpera10_5</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Opera 10.5x.</p>\n</div><div class='long'><p>True if the detected browser is Opera 10.5x.</p>\n</div></div></div><div id='property-isReady' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isReady' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isReady' class='name expandable'>isReady</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True when the document is fully initialized and ready for action ...</div><div class='long'><p>True when the document is fully initialized and ready for action</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-isSafari' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isSafari' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isSafari' class='name not-expandable'>isSafari</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Safari.</p>\n</div><div class='long'><p>True if the detected browser is Safari.</p>\n</div></div></div><div id='property-isSafari2' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isSafari2' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isSafari2' class='name not-expandable'>isSafari2</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Safari 2.x.</p>\n</div><div class='long'><p>True if the detected browser is Safari 2.x.</p>\n</div></div></div><div id='property-isSafari3' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isSafari3' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isSafari3' class='name not-expandable'>isSafari3</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Safari 3.x.</p>\n</div><div class='long'><p>True if the detected browser is Safari 3.x.</p>\n</div></div></div><div id='property-isSafari4' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isSafari4' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isSafari4' class='name not-expandable'>isSafari4</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Safari 4.x.</p>\n</div><div class='long'><p>True if the detected browser is Safari 4.x.</p>\n</div></div></div><div id='property-isSafari5' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isSafari5' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isSafari5' class='name not-expandable'>isSafari5</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Safari 5.x.</p>\n</div><div class='long'><p>True if the detected browser is Safari 5.x.</p>\n</div></div></div><div id='property-isSafari5_0' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isSafari5_0' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isSafari5_0' class='name not-expandable'>isSafari5_0</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser is Safari 5.0.x.</p>\n</div><div class='long'><p>True if the detected browser is Safari 5.0.x.</p>\n</div></div></div><div id='property-isSecure' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isSecure' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isSecure' class='name not-expandable'>isSecure</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the page is running over SSL</p>\n</div><div class='long'><p>True if the page is running over SSL</p>\n</div></div></div><div id='property-isWebKit' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isWebKit' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isWebKit' class='name not-expandable'>isWebKit</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected browser uses WebKit.</p>\n</div><div class='long'><p>True if the detected browser uses WebKit.</p>\n</div></div></div><div id='property-isWindows' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-isWindows' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-isWindows' class='name not-expandable'>isWindows</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the detected platform is Windows.</p>\n</div><div class='long'><p>True if the detected platform is Windows.</p>\n</div></div></div><div id='property-lastRegisteredVersion' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Version.html#Ext-property-lastRegisteredVersion' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-lastRegisteredVersion' class='name not-expandable'>lastRegisteredVersion</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-mergeIf' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Object2.html#Ext-property-mergeIf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-mergeIf' class='name not-expandable'>mergeIf</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-name' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-property-name' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-name' class='name expandable'>name</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The name of the property in the global namespace (The window in browser environments) which refers to the current ins...</div><div class='long'><p>The name of the property in the global namespace (The <code>window</code> in browser environments) which refers to the current instance of Ext.</p>\n\n\n<p>This is usually <code>\"Ext\"</code>, but if a sandboxed build of ExtJS is being used, this will be an alternative name.</p>\n\n\n<p>If code is being generated for use by <code>eval</code> or to create a <code>new Function</code>, and the global instance\nof Ext must be referenced, this is the name that should be built into the code.</p>\n\n<p>Defaults to: <code>'Ext'</code></p></div></div></div><div id='property-operaVersion' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-operaVersion' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-operaVersion' class='name not-expandable'>operaVersion</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'><p>The current version of Opera (0 if the browser is not Opera).</p>\n</div><div class='long'><p>The current version of Opera (0 if the browser is not Opera).</p>\n</div></div></div><div id='property-rootHierarchyState' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-rootHierarchyState' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-rootHierarchyState' class='name expandable'>rootHierarchyState</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>the top level hierarchy state to which\nall other hierarchy states are chained. ...</div><div class='long'><p>the top level hierarchy state to which\nall other hierarchy states are chained.  If there is a viewport instance,\nthis object becomes the viewport's heirarchyState. See also\n<a href=\"#!/api/Ext.AbstractComponent-method-getHierarchyState\" rel=\"Ext.AbstractComponent-method-getHierarchyState\" class=\"docClass\">Ext.AbstractComponent.getHierarchyState</a></p>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-safariVersion' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-safariVersion' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-safariVersion' class='name not-expandable'>safariVersion</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'><p>The current version of Safari (0 if the browser is not Safari).</p>\n</div><div class='long'><p>The current version of Safari (0 if the browser is not Safari).</p>\n</div></div></div><div id='property-useShims' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-useShims' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-useShims' class='name expandable'>useShims</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>By default, Ext intelligently decides whether floating elements should be shimmed. ...</div><div class='long'><p>By default, Ext intelligently decides whether floating elements should be shimmed.\nIf you are using flash, you may want to set this to true.</p>\n</div></div></div><div id='property-versions' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Version.html#Ext-property-versions' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-versions' class='name expandable'>versions</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-webKitVersion' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-property-webKitVersion' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-property-webKitVersion' class='name not-expandable'>webKitVersion</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'><p>The current version of WebKit (0 if the browser does not use WebKit).</p>\n</div><div class='long'><p>The current version of WebKit (0 if the browser does not use WebKit).</p>\n</div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-addBehaviors' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-addBehaviors' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-addBehaviors' class='name expandable'>addBehaviors</a>( <span class='pre'>obj</span> )</div><div class='description'><div class='short'>Applies event listeners to elements by selectors when the document is ready. ...</div><div class='long'><p>Applies event listeners to elements by selectors when the document is ready.\nThe event name is specified with an <code>@</code> suffix.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-addBehaviors\" rel=\"Ext-method-addBehaviors\" class=\"docClass\">Ext.addBehaviors</a>({\n    // add a listener for click on all anchors in element with id foo\n    '#foo a@click' : function(e, t){\n        // do something\n    },\n\n    // add the same listener to multiple selectors (separated by comma BEFORE the @)\n    '#foo a, #bar span.some-class@mouseover' : function(){\n        // do something\n    }\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>obj</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The list of behaviors to apply</p>\n</div></li></ul></div></div></div><div id='method-addNamespaces' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-addNamespaces' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-addNamespaces' class='name expandable'>addNamespaces</a>( <span class='pre'>namespace</span> )</div><div class='description'><div class='short'>Adds namespace(s) to known list. ...</div><div class='long'><p>Adds namespace(s) to known list.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>namespace</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-application' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-application' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-application' class='name expandable'>application</a>( <span class='pre'>config</span> )</div><div class='description'><div class='short'>Loads Ext.app.Application class and starts it up with given configuration after the\npage is ready. ...</div><div class='long'><p>Loads <a href=\"#!/api/Ext.app.Application\" rel=\"Ext.app.Application\" class=\"docClass\">Ext.app.Application</a> class and starts it up with given configuration after the\npage is ready.</p>\n\n<p>See <code><a href=\"#!/api/Ext.app.Application\" rel=\"Ext.app.Application\" class=\"docClass\">Ext.app.Application</a></code> for details.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>Application config object or name of a class derived from <a href=\"#!/api/Ext.app.Application\" rel=\"Ext.app.Application\" class=\"docClass\">Ext.app.Application</a>.</p>\n</div></li></ul></div></div></div><div id='method-apply' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-apply' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-apply' class='name expandable'>apply</a>( <span class='pre'>object, config, [defaults]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Copies all the properties of config to the specified object. ...</div><div class='long'><p>Copies all the properties of config to the specified object.\nNote that if recursive merging and cloning without referencing the original objects / arrays is needed, use\n<a href=\"#!/api/Ext.Object-method-merge\" rel=\"Ext.Object-method-merge\" class=\"docClass\">Ext.Object.merge</a> instead.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>object</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The receiver of the properties</p>\n</div></li><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The source of the properties</p>\n</div></li><li><span class='pre'>defaults</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>A different object that will also be applied for default values</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>returns obj</p>\n</div></li></ul></div></div></div><div id='method-applyIf' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-applyIf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-applyIf' class='name expandable'>applyIf</a>( <span class='pre'>object, config</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Copies all the properties of config to object if they don't already exist. ...</div><div class='long'><p>Copies all the properties of config to object if they don't already exist.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>object</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The receiver of the properties</p>\n</div></li><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The source of the properties</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>returns obj</p>\n</div></li></ul></div></div></div><div id='method-batchLayouts' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/AbstractComponent.html#Ext-method-batchLayouts' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-batchLayouts' class='name expandable'>batchLayouts</a>( <span class='pre'>fn, [scope]</span> )</div><div class='description'><div class='short'>Utility wrapper that suspends layouts of all components for the duration of a given function. ...</div><div class='long'><p>Utility wrapper that suspends layouts of all components for the duration of a given function.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The function to execute.</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the specified function is executed.</p>\n</div></li></ul></div></div></div><div id='method-bind' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Function2.html#Ext-method-bind' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-bind' class='name expandable'>bind</a>( <span class='pre'>fn, [scope], [args], [appendArgs]</span> ) : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></div><div class='description'><div class='short'>Create a new function from the provided fn, change this to the provided scope, optionally\noverrides arguments for the...</div><div class='long'><p>Create a new function from the provided <code>fn</code>, change <code>this</code> to the provided scope, optionally\noverrides arguments for the call. (Defaults to the arguments passed by the caller)</p>\n\n<p><a href=\"#!/api/Ext-method-bind\" rel=\"Ext-method-bind\" class=\"docClass\">Ext.bind</a> is alias for <a href=\"#!/api/Ext.Function-method-bind\" rel=\"Ext.Function-method-bind\" class=\"docClass\">Ext.Function.bind</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The function to delegate.</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the function is executed.\n<strong>If omitted, defaults to the default global environment object (usually the browser window).</strong></p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> (optional)<div class='sub-desc'><p>Overrides arguments for the call. (Defaults to the arguments passed by the caller)</p>\n</div></li><li><span class='pre'>appendArgs</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a>/<a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>if True args are appended to call args instead of overriding,\nif a number the args are inserted at the specified position</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></span><div class='sub-desc'><p>The new function</p>\n</div></li></ul></div></div></div><div id='method-callback' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-callback' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-callback' class='name expandable'>callback</a>( <span class='pre'>callback, [scope], [args], [delay]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Execute a callback function in a particular scope. ...</div><div class='long'><p>Execute a callback function in a particular scope. If <code>callback</code> argument is a\nfunction reference, that is called. If it is a string, the string is assumed to\nbe the name of a method on the given <code>scope</code>. If no function is passed the call\nis ignored.</p>\n\n<p>For example, these calls are equivalent:</p>\n\n<pre><code> var myFunc = this.myFunc;\n\n <a href=\"#!/api/Ext-method-callback\" rel=\"Ext-method-callback\" class=\"docClass\">Ext.callback</a>('myFunc', this, [arg1, arg2]);\n <a href=\"#!/api/Ext-method-callback\" rel=\"Ext-method-callback\" class=\"docClass\">Ext.callback</a>(myFunc, this, [arg1, arg2]);\n\n <a href=\"#!/api/Ext-method-isFunction\" rel=\"Ext-method-isFunction\" class=\"docClass\">Ext.isFunction</a>(myFunc) &amp;&amp; this.myFunc(arg1, arg2);\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The callback to execute</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope to execute in</p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> (optional)<div class='sub-desc'><p>The arguments to pass to the function</p>\n</div></li><li><span class='pre'>delay</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>Pass a number to delay the call by a number of milliseconds.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The value returned by the callback or <code>undefined</code> (if there is a <code>delay</code>\nor if the <code>callback</code> is not a function).</p>\n</div></li></ul></div></div></div><div id='method-clean' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Array2.html#Ext-method-clean' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-clean' class='name expandable'>clean</a>( <span class='pre'>array</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Old alias to Ext.Array.clean\n\nFilter through an array and remove empty item as defined in Ext.isEmpty\n\nSee Ext.Array....</div><div class='long'><p>Old alias to <a href=\"#!/api/Ext.Array-method-clean\" rel=\"Ext.Array-method-clean\" class=\"docClass\">Ext.Array.clean</a></p>\n\n<p>Filter through an array and remove empty item as defined in <a href=\"#!/api/Ext-method-isEmpty\" rel=\"Ext-method-isEmpty\" class=\"docClass\">Ext.isEmpty</a></p>\n\n<p>See <a href=\"#!/api/Ext.Array-method-filter\" rel=\"Ext.Array-method-filter\" class=\"docClass\">Ext.Array.filter</a></p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Use <a href=\"#!/api/Ext.Array-method-clean\" rel=\"Ext.Array-method-clean\" class=\"docClass\">Ext.Array.clean</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>results</p>\n</div></li></ul></div></div></div><div id='method-clearNamespaces' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-clearNamespaces' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-clearNamespaces' class='name expandable'>clearNamespaces</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Clear all namespaces from known list. ...</div><div class='long'><p>Clear all namespaces from known list.</p>\n</div></div></div><div id='method-clone' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-clone' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-clone' class='name expandable'>clone</a>( <span class='pre'>item</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Clone simple variables including array, {}-like objects, DOM nodes and Date without keeping the old reference. ...</div><div class='long'><p>Clone simple variables including array, {}-like objects, DOM nodes and Date without keeping the old reference.\nA reference for the object itself is returned if it's not a direct decendant of Object. For model cloning,\nsee <a href=\"#!/api/Ext.data.Model-method-copy\" rel=\"Ext.data.Model-method-copy\" class=\"docClass\">Model.copy</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The variable to clone</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>clone</p>\n</div></li></ul></div></div></div><div id='method-coerce' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-coerce' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-coerce' class='name expandable'>coerce</a>( <span class='pre'>from, to</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Coerces the first value if possible so that it is comparable to the second value. ...</div><div class='long'><p>Coerces the first value if possible so that it is comparable to the second value.</p>\n\n<p>Coercion only works between the basic atomic data types String, Boolean, Number, Date, null and undefined.</p>\n\n<p>Numbers and numeric strings are coerced to Dates using the value as the millisecond era value.</p>\n\n<p>Strings are coerced to Dates by parsing using the <a href=\"#!/api/Ext.Date-property-defaultFormat\" rel=\"Ext.Date-property-defaultFormat\" class=\"docClass\">defaultFormat</a>.</p>\n\n<p>For example</p>\n\n<pre><code><a href=\"#!/api/Ext-method-coerce\" rel=\"Ext-method-coerce\" class=\"docClass\">Ext.coerce</a>('false', true);\n</code></pre>\n\n<p>returns the boolean value <code>false</code> because the second parameter is of type <code>Boolean</code>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>from</span> : Mixed<div class='sub-desc'><p>The value to coerce</p>\n</div></li><li><span class='pre'>to</span> : Mixed<div class='sub-desc'><p>The value it must be compared against</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The coerced value.</p>\n</div></li></ul></div></div></div><div id='method-collectNamespaces' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-collectNamespaces' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-collectNamespaces' class='name expandable'>collectNamespaces</a>( <span class='pre'>paths</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>paths</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-copyTo' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-copyTo' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-copyTo' class='name expandable'>copyTo</a>( <span class='pre'>dest, source, names, [usePrototypeKeys]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Copies a set of named properties fom the source object to the destination object. ...</div><div class='long'><p>Copies a set of named properties fom the source object to the destination object.</p>\n\n<p>Example:</p>\n\n<pre><code>ImageComponent = <a href=\"#!/api/Ext-method-extend\" rel=\"Ext-method-extend\" class=\"docClass\">Ext.extend</a>(<a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>, {\n    initComponent: function() {\n        this.autoEl = { tag: 'img' };\n        MyComponent.superclass.initComponent.apply(this, arguments);\n        this.initialBox = <a href=\"#!/api/Ext-method-copyTo\" rel=\"Ext-method-copyTo\" class=\"docClass\">Ext.copyTo</a>({}, this.initialConfig, 'x,y,width,height');\n    }\n});\n</code></pre>\n\n<p>Important note: To borrow class prototype methods, use <a href=\"#!/api/Ext.Base-static-method-borrow\" rel=\"Ext.Base-static-method-borrow\" class=\"docClass\">Ext.Base.borrow</a> instead.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>dest</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The destination object.</p>\n</div></li><li><span class='pre'>source</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The source object.</p>\n</div></li><li><span class='pre'>names</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>Either an Array of property names, or a comma-delimited list\nof property names to copy.</p>\n</div></li><li><span class='pre'>usePrototypeKeys</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>Defaults to false. Pass true to copy keys off of the\nprototype as well as the instance.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The modified object.</p>\n</div></li></ul></div></div></div><div id='method-create' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/ClassManager.html#Ext-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-create' class='name expandable'>create</a>( <span class='pre'>[name], [args]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Instantiate a class by either full name, alias or alternate name. ...</div><div class='long'><p>Instantiate a class by either full name, alias or alternate name.</p>\n\n<p>If <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a> is <a href=\"#!/api/Ext.Loader-method-setConfig\" rel=\"Ext.Loader-method-setConfig\" class=\"docClass\">enabled</a> and the class has\nnot been defined yet, it will attempt to load the class via synchronous loading.</p>\n\n<p>For example, all these three lines return the same result:</p>\n\n<pre><code> // alias\n var window = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('widget.window', {\n     width: 600,\n     height: 800,\n     ...\n });\n\n // alternate name\n var window = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.window.Window\" rel=\"Ext.window.Window\" class=\"docClass\">Ext.Window</a>', {\n     width: 600,\n     height: 800,\n     ...\n });\n\n // full class name\n var window = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.window.Window\" rel=\"Ext.window.Window\" class=\"docClass\">Ext.window.Window</a>', {\n     width: 600,\n     height: 800,\n     ...\n });\n\n // single object with xclass property:\n var window = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>({\n     xclass: '<a href=\"#!/api/Ext.window.Window\" rel=\"Ext.window.Window\" class=\"docClass\">Ext.window.Window</a>', // any valid value for 'name' (above)\n     width: 600,\n     height: 800,\n     ...\n });\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The class name or alias. Can be specified as <code>xclass</code>\nproperty if only one object parameter is specified.</p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>... (optional)<div class='sub-desc'><p>Additional arguments after the name will be passed to\nthe class' constructor.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>instance</p>\n</div></li></ul></div></div></div><div id='method-createByAlias' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/ClassManager.html#Ext-method-createByAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-createByAlias' class='name expandable'>createByAlias</a>( <span class='pre'>alias, args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Instantiate a class by its alias. ...</div><div class='long'><p>Instantiate a class by its alias.</p>\n\n<p><a href=\"#!/api/Ext.ClassManager-method-instantiateByAlias\" rel=\"Ext.ClassManager-method-instantiateByAlias\" class=\"docClass\">Ext.ClassManager.instantiateByAlias</a> is usually invoked by the shorthand <a href=\"#!/api/Ext-method-createByAlias\" rel=\"Ext-method-createByAlias\" class=\"docClass\">createByAlias</a>.</p>\n\n<p>If <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a> is <a href=\"#!/api/Ext.Loader-method-setConfig\" rel=\"Ext.Loader-method-setConfig\" class=\"docClass\">enabled</a> and the class has not been defined yet, it will\nattempt to load the class via synchronous loading.</p>\n\n<pre><code>var window = <a href=\"#!/api/Ext-method-createByAlias\" rel=\"Ext-method-createByAlias\" class=\"docClass\">Ext.createByAlias</a>('widget.window', { width: 600, height: 800, ... });\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>...<div class='sub-desc'><p>Additional arguments after the alias will be passed to the\nclass constructor.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>instance</p>\n</div></li></ul></div></div></div><div id='method-createWidget' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/ClassManager.html#Ext-method-createWidget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-createWidget' class='name expandable'>createWidget</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Old name for widget. ...</div><div class='long'><p>Old name for <a href=\"#!/api/Ext-method-widget\" rel=\"Ext-method-widget\" class=\"docClass\">widget</a>.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-widget\" rel=\"Ext-method-widget\" class=\"docClass\">widget</a> instead.</p>\n\n        </div>\n</div></div></div><div id='method-decode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/JSON.html#Ext-method-decode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-decode' class='name expandable'>decode</a>( <span class='pre'>json, [safe]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Shorthand for Ext.JSON.decode\n\nDecodes (parses) a JSON string to an object. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.JSON-method-decode\" rel=\"Ext.JSON-method-decode\" class=\"docClass\">Ext.JSON.decode</a></p>\n\n<p>Decodes (parses) a JSON string to an object. If the JSON is invalid, this function throws\na SyntaxError unless the safe option is set.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>json</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The JSON string</p>\n</div></li><li><span class='pre'>safe</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return null, false to throw an exception if the JSON is invalid.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The resulting object</p>\n</div></li></ul></div></div></div><div id='method-defer' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Function2.html#Ext-method-defer' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-defer' class='name expandable'>defer</a>( <span class='pre'>fn, millis, [scope], [args], [appendArgs]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Calls this function after the number of millseconds specified, optionally in a specific scope. ...</div><div class='long'><p>Calls this function after the number of millseconds specified, optionally in a specific scope. Example usage:</p>\n\n<pre><code>var sayHi = function(name){\n    alert('Hi, ' + name);\n}\n\n// executes immediately:\nsayHi('Fred');\n\n// executes after 2 seconds:\n<a href=\"#!/api/Ext.Function-method-defer\" rel=\"Ext.Function-method-defer\" class=\"docClass\">Ext.Function.defer</a>(sayHi, 2000, this, ['Fred']);\n\n// this syntax is sometimes useful for deferring\n// execution of an anonymous function:\n<a href=\"#!/api/Ext.Function-method-defer\" rel=\"Ext.Function-method-defer\" class=\"docClass\">Ext.Function.defer</a>(function(){\n    alert('Anonymous');\n}, 100);\n</code></pre>\n\n<p><a href=\"#!/api/Ext-method-defer\" rel=\"Ext-method-defer\" class=\"docClass\">Ext.defer</a> is alias for <a href=\"#!/api/Ext.Function-method-defer\" rel=\"Ext.Function-method-defer\" class=\"docClass\">Ext.Function.defer</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The function to defer.</p>\n</div></li><li><span class='pre'>millis</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of milliseconds for the setTimeout call\n(if less than or equal to 0 the function is executed immediately)</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the function is executed.\n<strong>If omitted, defaults to the browser window.</strong></p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> (optional)<div class='sub-desc'><p>Overrides arguments for the call. (Defaults to the arguments passed by the caller)</p>\n</div></li><li><span class='pre'>appendArgs</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a>/<a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>if True args are appended to call args instead of overriding,\nif a number the args are inserted at the specified position</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The timeout id that can be used with clearTimeout</p>\n</div></li></ul></div></div></div><div id='method-define' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/ClassManager.html#Ext-method-define' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-define' class='name expandable'>define</a>( <span class='pre'>className, data, createdFn</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></div><div class='description'><div class='short'>Defines a class or override. ...</div><div class='long'><p>Defines a class or override. A basic class is defined like this:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n     someProperty: 'something',\n\n     someMethod: function(s) {\n         alert(s + this.someProperty);\n     }\n\n     ...\n });\n\n var obj = new My.awesome.Class();\n\n obj.someMethod('Say '); // alerts 'Say something'\n</code></pre>\n\n<p>To create an anonymous class, pass <code>null</code> for the <code>className</code>:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>(null, {\n     constructor: function () {\n         // ...\n     }\n });\n</code></pre>\n\n<p>In some cases, it is helpful to create a nested scope to contain some private\nproperties. The best way to do this is to pass a function instead of an object\nas the second parameter. This function will be called to produce the class\nbody:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('MyApp.foo.Bar', function () {\n     var id = 0;\n\n     return {\n         nextId: function () {\n             return ++id;\n         }\n     };\n });\n</code></pre>\n\n<p><em>Note</em> that when using override, the above syntax will not override successfully, because\nthe passed function would need to be executed first to determine whether or not the result\nis an override or defining a new object. As such, an alternative syntax that immediately\ninvokes the function can be used:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('MyApp.override.BaseOverride', function () {\n     var counter = 0;\n\n     return {\n         override: '<a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>',\n         logId: function () {\n             console.log(++counter, this.id);\n         }\n     };\n }());\n</code></pre>\n\n<p>When using this form of <code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a></code>, the function is passed a reference to its\nclass. This can be used as an efficient way to access any static properties you\nmay have:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('MyApp.foo.Bar', function (Bar) {\n     return {\n         statics: {\n             staticMethod: function () {\n                 // ...\n             }\n         },\n\n         method: function () {\n             return Bar.staticMethod();\n         }\n     };\n });\n</code></pre>\n\n<p>To define an override, include the <code>override</code> property. The content of an\noverride is aggregated with the specified class in order to extend or modify\nthat class. This can be as simple as setting default property values or it can\nextend and/or replace methods. This can also extend the statics of the class.</p>\n\n<p>One use for an override is to break a large class into manageable pieces.</p>\n\n<pre><code> // File: /src/app/Panel.js\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.app.Panel', {\n     extend: '<a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>',\n     requires: [\n         'My.app.PanelPart2',\n         'My.app.PanelPart3'\n     ]\n\n     constructor: function (config) {\n         this.callParent(arguments); // calls <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>'s constructor\n         //...\n     },\n\n     statics: {\n         method: function () {\n             return 'abc';\n         }\n     }\n });\n\n // File: /src/app/PanelPart2.js\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.app.PanelPart2', {\n     override: 'My.app.Panel',\n\n     constructor: function (config) {\n         this.callParent(arguments); // calls My.app.Panel's constructor\n         //...\n     }\n });\n</code></pre>\n\n<p>Another use of overrides is to provide optional parts of classes that can be\nindependently required. In this case, the class may even be unaware of the\noverride altogether.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.ux.CoolTip', {\n     override: '<a href=\"#!/api/Ext.tip.ToolTip\" rel=\"Ext.tip.ToolTip\" class=\"docClass\">Ext.tip.ToolTip</a>',\n\n     constructor: function (config) {\n         this.callParent(arguments); // calls <a href=\"#!/api/Ext.tip.ToolTip\" rel=\"Ext.tip.ToolTip\" class=\"docClass\">Ext.tip.ToolTip</a>'s constructor\n         //...\n     }\n });\n</code></pre>\n\n<p>The above override can now be required as normal.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.app.App', {\n     requires: [\n         'My.ux.CoolTip'\n     ]\n });\n</code></pre>\n\n<p>Overrides can also contain statics:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.app.BarMod', {\n     override: 'Ext.foo.Bar',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x * 2]); // call Ext.foo.Bar.method\n         }\n     }\n });\n</code></pre>\n\n<p>IMPORTANT: An override is only included in a build if the class it overrides is\nrequired. Otherwise, the override, like the target class, is not included.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>className</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The class name to create in string dot-namespaced format, for example:\n'My.very.awesome.Class', 'FeedViewer.plugin.CoolPager'\nIt is highly recommended to follow this simple convention:\n - The root and the class name are 'CamelCased'\n - Everything else is lower-cased\nPass <code>null</code> to create an anonymous class.</p>\n</div></li><li><span class='pre'>data</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The key - value pairs of properties to apply to this class. Property names can be of any valid\nstrings, except those in the reserved listed below:\n - <code>mixins</code>\n - <code>statics</code>\n - <code>config</code>\n - <code>alias</code>\n - <code>self</code>\n - <code>singleton</code>\n - <code>alternateClassName</code>\n - <code>override</code></p>\n</div></li><li><span class='pre'>createdFn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Optional callback to execute after the class is created, the execution scope of which\n(<code>this</code>) will be the newly created class itself.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-deprecate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Version.html#Ext-method-deprecate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-deprecate' class='name expandable'>deprecate</a>( <span class='pre'>packageName, since, closure, scope</span> )</div><div class='description'><div class='short'>Create a closure for deprecated code. ...</div><div class='long'><p>Create a closure for deprecated code.</p>\n\n<pre><code>// This means Ext.oldMethod is only supported in 4.0.0beta and older.\n// If <a href=\"#!/api/Ext-method-getVersion\" rel=\"Ext-method-getVersion\" class=\"docClass\">Ext.getVersion</a>('extjs') returns a version that is later than '4.0.0beta', for example '4.0.0RC',\n// the closure will not be invoked\n<a href=\"#!/api/Ext-method-deprecate\" rel=\"Ext-method-deprecate\" class=\"docClass\">Ext.deprecate</a>('extjs', '4.0.0beta', function() {\n    Ext.oldMethod = Ext.newMethod;\n\n    ...\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>packageName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The package name</p>\n</div></li><li><span class='pre'>since</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The last version before it's deprecated</p>\n</div></li><li><span class='pre'>closure</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The callback function to be executed with the specified version is less than the current version</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The execution scope (<code>this</code>) if the closure</p>\n</div></li></ul></div></div></div><div id='method-destroy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-destroy' class='name expandable'>destroy</a>( <span class='pre'>args</span> )</div><div class='description'><div class='short'>Attempts to destroy any objects passed to it by removing all event listeners, removing them from the\nDOM (if applicab...</div><div class='long'><p>Attempts to destroy any objects passed to it by removing all event listeners, removing them from the\nDOM (if applicable) and calling their destroy functions (if available).  This method is primarily\nintended for arguments of type <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a> and <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>, but any subclass of\n<a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a> can be passed in.  Any number of elements and/or components can be\npassed into this function in a single call as separate arguments.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>/<a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>[]/<a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>[]...<div class='sub-desc'><p>Any number of elements or components, or an Array of either of these to destroy.</p>\n</div></li></ul></div></div></div><div id='method-destroyMembers' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-destroyMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-destroyMembers' class='name expandable'>destroyMembers</a>( <span class='pre'>o, args</span> )</div><div class='description'><div class='short'>Attempts to destroy and then remove a set of named properties of the passed object. ...</div><div class='long'><p>Attempts to destroy and then remove a set of named properties of the passed object.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>o</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object (most likely a Component) who's properties you wish to destroy.</p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>...<div class='sub-desc'><p>One or more names of the properties to destroy and remove from the object.</p>\n</div></li></ul></div></div></div><div id='method-each' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Array2.html#Ext-method-each' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-each' class='name expandable'>each</a>( <span class='pre'>iterable, fn, [scope], [reverse]</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Iterates an array or an iterable value and invoke the given callback function for each item. ...</div><div class='long'><p>Iterates an array or an iterable value and invoke the given callback function for each item.</p>\n\n<pre><code>var countries = ['Vietnam', 'Singapore', 'United States', 'Russia'];\n\n<a href=\"#!/api/Ext.Array-method-each\" rel=\"Ext.Array-method-each\" class=\"docClass\">Ext.Array.each</a>(countries, function(name, index, countriesItSelf) {\n    console.log(name);\n});\n\nvar sum = function() {\n    var sum = 0;\n\n    <a href=\"#!/api/Ext.Array-method-each\" rel=\"Ext.Array-method-each\" class=\"docClass\">Ext.Array.each</a>(arguments, function(value) {\n        sum += value;\n    });\n\n    return sum;\n};\n\nsum(1, 2, 3); // returns 6\n</code></pre>\n\n<p>The iteration can be stopped by returning false in the function callback.</p>\n\n<pre><code><a href=\"#!/api/Ext.Array-method-each\" rel=\"Ext.Array-method-each\" class=\"docClass\">Ext.Array.each</a>(countries, function(name, index, countriesItSelf) {\n    if (name === 'Singapore') {\n        return false; // break here\n    }\n});\n</code></pre>\n\n<p><a href=\"#!/api/Ext-method-each\" rel=\"Ext-method-each\" class=\"docClass\">Ext.each</a> is alias for <a href=\"#!/api/Ext.Array-method-each\" rel=\"Ext.Array-method-each\" class=\"docClass\">Ext.Array.each</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>iterable</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/NodeList/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to be iterated. If this\nargument is not iterable, the callback function is called once.</p>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The callback function. If it returns false, the iteration stops and this method returns\nthe current <code>index</code>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The item at the current <code>index</code> in the passed <code>array</code></p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The current <code>index</code> within the <code>array</code></p>\n</div></li><li><span class='pre'>allItems</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The <code>array</code> itself which was passed as the first argument</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>Return false to stop iteration.</p>\n</div></li></ul></div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the specified function is executed.</p>\n</div></li><li><span class='pre'>reverse</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>Reverse the iteration order (loop from the end to the beginning)\nDefaults false</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>See description for the <code>fn</code> parameter.</p>\n</div></li></ul></div></div></div><div id='method-encode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/JSON.html#Ext-method-encode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-encode' class='name expandable'>encode</a>( <span class='pre'>o</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Shorthand for Ext.JSON.encode\n\nEncodes an Object, Array or other value. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.JSON-method-encode\" rel=\"Ext.JSON-method-encode\" class=\"docClass\">Ext.JSON.encode</a></p>\n\n<p>Encodes an Object, Array or other value.</p>\n\n<p>If the environment's native JSON encoding is not being used (<a href=\"#!/api/Ext-property-USE_NATIVE_JSON\" rel=\"Ext-property-USE_NATIVE_JSON\" class=\"docClass\">USE_NATIVE_JSON</a> is not set,\nor the environment does not support it), then ExtJS's encoding will be used. This allows the developer\nto add a <code>toJSON</code> method to their classes which need serializing to return a valid JSON representation\nof the object.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>o</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The variable to encode</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The JSON string</p>\n</div></li></ul></div></div></div><div id='method-escapeRe' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-escapeRe' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-escapeRe' class='name expandable'>escapeRe</a>( <span class='pre'>str</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Escapes the passed string for use in a regular expression. ...</div><div class='long'><p>Escapes the passed string for use in a regular expression.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Use <a href=\"#!/api/Ext.String-method-escapeRegex\" rel=\"Ext.String-method-escapeRegex\" class=\"docClass\">Ext.String.escapeRegex</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>str</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-exclude' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Loader.html#Ext-method-exclude' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-exclude' class='name expandable'>exclude</a>( <span class='pre'>excludes</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Convenient shortcut to Ext.Loader.exclude\n\nExplicitly exclude files from being loaded. ...</div><div class='long'><p>Convenient shortcut to <a href=\"#!/api/Ext.Loader-method-exclude\" rel=\"Ext.Loader-method-exclude\" class=\"docClass\">Ext.Loader.exclude</a></p>\n\n<p>Explicitly exclude files from being loaded. Useful when used in conjunction with a broad include expression.\nCan be chained with more <code>require</code> and <code>exclude</code> methods, eg:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-exclude\" rel=\"Ext-method-exclude\" class=\"docClass\">Ext.exclude</a>('Ext.data.*').require('*');\n\n<a href=\"#!/api/Ext-method-exclude\" rel=\"Ext-method-exclude\" class=\"docClass\">Ext.exclude</a>('widget.button*').require('widget.*');\n</code></pre>\n\n<p><a href=\"#!/api/Ext-method-exclude\" rel=\"Ext-method-exclude\" class=\"docClass\">exclude</a> is alias for <a href=\"#!/api/Ext.Loader-method-exclude\" rel=\"Ext.Loader-method-exclude\" class=\"docClass\">Ext.Loader.exclude</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>excludes</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>object contains <code>require</code> method for chaining</p>\n\n</div></li></ul></div></div></div><div id='method-extend' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-extend' class='name expandable'>extend</a>( <span class='pre'>superclass, overrides</span> ) : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>This method deprecated. ...</div><div class='long'><p>This method deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>superclass</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'>\n</div></li><li><span class='pre'>overrides</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></span><div class='sub-desc'><p>The subclass constructor from the <tt>overrides</tt> parameter, or a generated one if not provided.</p>\n</div></li></ul></div></div></div><div id='method-flatten' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Array2.html#Ext-method-flatten' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-flatten' class='name expandable'>flatten</a>( <span class='pre'>array</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Old alias to Ext.Array.flatten\n\nRecursively flattens into 1-d Array. ...</div><div class='long'><p>Old alias to <a href=\"#!/api/Ext.Array-method-flatten\" rel=\"Ext.Array-method-flatten\" class=\"docClass\">Ext.Array.flatten</a></p>\n\n<p>Recursively flattens into 1-d Array. Injects Arrays inline.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Use <a href=\"#!/api/Ext.Array-method-flatten\" rel=\"Ext.Array-method-flatten\" class=\"docClass\">Ext.Array.flatten</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array to flatten</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>The 1-d array.</p>\n</div></li></ul></div></div></div><div id='method-fly' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/AbstractElement.html#Ext-method-fly' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-fly' class='name expandable'>fly</a>( <span class='pre'>dom, [named]</span> ) : <a href=\"#!/api/Ext.dom.Element.Fly\" rel=\"Ext.dom.Element.Fly\" class=\"docClass\">Ext.dom.Element.Fly</a></div><div class='description'><div class='short'>Gets the singleton flyweight element, with the passed node as the active element. ...</div><div class='long'><p>Gets the singleton <a href=\"#!/api/Ext.dom.Element.Fly\" rel=\"Ext.dom.Element.Fly\" class=\"docClass\">flyweight</a> element, with the passed node as the active element.</p>\n\n<p>Because it is a singleton, this Flyweight does not have an ID, and must be used and discarded in a single line.\nYou may not keep and use the reference to this singleton over multiple lines because methods that you call\nmay themselves make use of <a href=\"#!/api/Ext-method-fly\" rel=\"Ext-method-fly\" class=\"docClass\">fly</a> and may change the DOM element to which the instance refers.</p>\n\n<p><a href=\"#!/api/Ext-method-fly\" rel=\"Ext-method-fly\" class=\"docClass\">fly</a> is alias for <a href=\"#!/api/Ext.dom.AbstractElement-static-method-fly\" rel=\"Ext.dom.AbstractElement-static-method-fly\" class=\"docClass\">Ext.dom.AbstractElement.fly</a>.</p>\n\n<p>Use this to make one-time references to DOM elements which are not going to be accessed again either by\napplication code, or by Ext's classes. If accessing an element which will be processed regularly, then <a href=\"#!/api/Ext-method-get\" rel=\"Ext-method-get\" class=\"docClass\">Ext.get</a> will be more appropriate to take advantage of the caching provided by the <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>\nclass.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>dom</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement<div class='sub-desc'><p>The dom node or id</p>\n</div></li><li><span class='pre'>named</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Allows for creation of named reusable flyweights to prevent conflicts (e.g.\ninternally Ext uses \"_global\")</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element.Fly\" rel=\"Ext.dom.Element.Fly\" class=\"docClass\">Ext.dom.Element.Fly</a></span><div class='sub-desc'><p>The singleton flyweight object (or null if no matching element was found)</p>\n</div></li></ul></div></div></div><div id='method-get' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/AbstractElement.html#Ext-method-get' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-get' class='name expandable'>get</a>( <span class='pre'>el</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></div><div class='description'><div class='short'>Retrieves Ext.dom.Element objects. ...</div><div class='long'><p>Retrieves <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a> objects. <a href=\"#!/api/Ext-method-get\" rel=\"Ext-method-get\" class=\"docClass\">get</a> is alias for <a href=\"#!/api/Ext.dom.Element-static-method-get\" rel=\"Ext.dom.Element-static-method-get\" class=\"docClass\">Ext.dom.Element.get</a>.</p>\n\n<p><strong>This method does not retrieve <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Component</a>s.</strong> This method retrieves <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>\nobjects which encapsulate DOM elements. To retrieve a Component by its ID, use <a href=\"#!/api/Ext.ComponentManager-method-get\" rel=\"Ext.ComponentManager-method-get\" class=\"docClass\">Ext.ComponentManager.get</a>.</p>\n\n<p>When passing an id, it should not include the <code>#</code> character that is used for a css selector.</p>\n\n<pre><code>// For an element with id 'foo'\n<a href=\"#!/api/Ext-method-get\" rel=\"Ext-method-get\" class=\"docClass\">Ext.get</a>('foo'); // Correct\n<a href=\"#!/api/Ext-method-get\" rel=\"Ext-method-get\" class=\"docClass\">Ext.get</a>('#foo'); // Incorrect\n</code></pre>\n\n<p>Uses simple caching to consistently return the same object. Automatically fixes if an object was recreated with\nthe same id via AJAX or DOM.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The id of the node, a DOM Node or an existing Element.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></span><div class='sub-desc'><p>The Element object (or null if no matching element was found)</p>\n</div></li></ul></div></div></div><div id='method-getBody' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-getBody' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-getBody' class='name expandable'>getBody</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></div><div class='description'><div class='short'>Returns the current document body as an Ext.Element. ...</div><div class='long'><p>Returns the current document body as an <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span><div class='sub-desc'><p>The document body</p>\n</div></li></ul></div></div></div><div id='method-getClass' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/ClassManager.html#Ext-method-getClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-getClass' class='name expandable'>getClass</a>( <span class='pre'>object</span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></div><div class='description'><div class='short'>Get the class of the provided object; returns null if it's not an instance\nof any class created with Ext.define. ...</div><div class='long'><p>Get the class of the provided object; returns null if it's not an instance\nof any class created with <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>.</p>\n\n<p><a href=\"#!/api/Ext.ClassManager-method-getClass\" rel=\"Ext.ClassManager-method-getClass\" class=\"docClass\">Ext.ClassManager.getClass</a> is usually invoked by the shorthand <a href=\"#!/api/Ext-method-getClass\" rel=\"Ext-method-getClass\" class=\"docClass\">getClass</a>.</p>\n\n<pre><code>var component = new <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>();\n\n<a href=\"#!/api/Ext-method-getClass\" rel=\"Ext-method-getClass\" class=\"docClass\">Ext.getClass</a>(component); // returns <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>object</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'><p>class</p>\n</div></li></ul></div></div></div><div id='method-getClassName' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/ClassManager.html#Ext-method-getClassName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-getClassName' class='name expandable'>getClassName</a>( <span class='pre'>object</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Get the name of the class by its reference or its instance;\n\nExt.ClassManager.getName is usually invoked by the short...</div><div class='long'><p>Get the name of the class by its reference or its instance;</p>\n\n<p><a href=\"#!/api/Ext.ClassManager-method-getName\" rel=\"Ext.ClassManager-method-getName\" class=\"docClass\">Ext.ClassManager.getName</a> is usually invoked by the shorthand <a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">getClassName</a>.</p>\n\n<pre><code>Ext.getName(<a href=\"#!/api/Ext.Action\" rel=\"Ext.Action\" class=\"docClass\">Ext.Action</a>); // returns \"<a href=\"#!/api/Ext.Action\" rel=\"Ext.Action\" class=\"docClass\">Ext.Action</a>\"\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>object</span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='method-getCmp' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/ComponentManager.html#Ext-method-getCmp' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-getCmp' class='name expandable'>getCmp</a>( <span class='pre'>id</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>This is shorthand reference to Ext.ComponentManager.get. ...</div><div class='long'><p>This is shorthand reference to <a href=\"#!/api/Ext.ComponentManager-method-get\" rel=\"Ext.ComponentManager-method-get\" class=\"docClass\">Ext.ComponentManager.get</a>.\nLooks up an existing <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Component</a> by <a href=\"#!/api/Ext.Component-cfg-id\" rel=\"Ext.Component-cfg-id\" class=\"docClass\">id</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The component <a href=\"#!/api/Ext.Component-cfg-id\" rel=\"Ext.Component-cfg-id\" class=\"docClass\">id</a></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a> The Component, <code>undefined</code> if not found, or <code>null</code> if a\nClass was found.</p>\n</div></li></ul></div></div></div><div id='method-getDetachedBody' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/AbstractElement.html#Ext-method-getDetachedBody' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-getDetachedBody' class='name expandable'>getDetachedBody</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getDoc' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-getDoc' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-getDoc' class='name expandable'>getDoc</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></div><div class='description'><div class='short'>Returns the current HTML document object as an Ext.Element. ...</div><div class='long'><p>Returns the current HTML document object as an <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span><div class='sub-desc'><p>The document</p>\n</div></li></ul></div></div></div><div id='method-getDom' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-getDom' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-getDom' class='name expandable'>getDom</a>( <span class='pre'>el</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Returns the dom node for the passed String (id), dom node, or Ext.Element. ...</div><div class='long'><p>Returns the dom node for the passed String (id), dom node, or <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>.\nOptional 'strict' flag is needed for IE since it can return 'name' and\n'id' elements by using getElementById.</p>\n\n<p>Here are some examples:</p>\n\n<pre><code>// gets dom node based on id\nvar elDom = <a href=\"#!/api/Ext-method-getDom\" rel=\"Ext-method-getDom\" class=\"docClass\">Ext.getDom</a>('elId');\n// gets dom node based on the dom node\nvar elDom1 = <a href=\"#!/api/Ext-method-getDom\" rel=\"Ext-method-getDom\" class=\"docClass\">Ext.getDom</a>(elDom);\n\n// If we don&amp;#39;t know if we are working with an\n// <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a> or a dom node use <a href=\"#!/api/Ext-method-getDom\" rel=\"Ext-method-getDom\" class=\"docClass\">Ext.getDom</a>\nfunction(el){\n    var dom = <a href=\"#!/api/Ext-method-getDom\" rel=\"Ext-method-getDom\" class=\"docClass\">Ext.getDom</a>(el);\n    // do something with the dom node\n}\n</code></pre>\n\n<p><strong>Note:</strong> the dom node to be found actually needs to exist (be rendered, etc)\nwhen this method is called to be successful.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>HTMLElement</p>\n</div></li></ul></div></div></div><div id='method-getElementById' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/AbstractElement.html#Ext-method-getElementById' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-getElementById' class='name expandable'>getElementById</a>( <span class='pre'>id</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getHead' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-getHead' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-getHead' class='name expandable'>getHead</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></div><div class='description'><div class='short'>Returns the current document head as an Ext.Element. ...</div><div class='long'><p>Returns the current document head as an <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span><div class='sub-desc'><p>The document head</p>\n</div></li></ul></div></div></div><div id='method-getNamespace' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-getNamespace' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-getNamespace' class='name expandable'>getNamespace</a>( <span class='pre'>className</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Get namespace prefix for a class name. ...</div><div class='long'><p>Get namespace prefix for a class name.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>className</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Namespace prefix if it's known, otherwise undefined</p>\n</div></li></ul></div></div></div><div id='method-getOrientation' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-getOrientation' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-getOrientation' class='name expandable'>getOrientation</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns the current orientation of the mobile device ...</div><div class='long'><p>Returns the current orientation of the mobile device</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Either 'portrait' or 'landscape'</p>\n</div></li></ul></div></div></div><div id='method-getScrollBarWidth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-getScrollBarWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-getScrollBarWidth' class='name expandable'>getScrollBarWidth</a>( <span class='pre'>[force]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Utility method for getting the width of the browser's vertical scrollbar. ...</div><div class='long'><p>Utility method for getting the width of the browser's vertical scrollbar. This\ncan differ depending on operating system settings, such as the theme or font size.</p>\n\n<p>This method is deprected in favor of <a href=\"#!/api/Ext-method-getScrollbarSize\" rel=\"Ext-method-getScrollbarSize\" class=\"docClass\">getScrollbarSize</a>.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        \n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>force</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>true to force a recalculation of the value.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The width of a vertical scrollbar.</p>\n</div></li></ul></div></div></div><div id='method-getScrollbarSize' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-getScrollbarSize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-getScrollbarSize' class='name expandable'>getScrollbarSize</a>( <span class='pre'>[force]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Returns the size of the browser scrollbars. ...</div><div class='long'><p>Returns the size of the browser scrollbars. This can differ depending on\noperating system settings, such as the theme or font size.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>force</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>true to force a recalculation of the value.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>An object containing scrollbar sizes.</p>\n<ul><li><span class='pre'>width</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The width of the vertical scrollbar.</p>\n</div></li><li><span class='pre'>height</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The height of the horizontal scrollbar.</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-getStore' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/StoreManager.html#Ext-method-getStore' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-getStore' class='name expandable'>getStore</a>( <span class='pre'>store</span> ) : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a></div><div class='description'><div class='short'>Shortcut to Ext.data.StoreManager.lookup. ...</div><div class='long'><p>Shortcut to <a href=\"#!/api/Ext.data.StoreManager-method-lookup\" rel=\"Ext.data.StoreManager-method-lookup\" class=\"docClass\">Ext.data.StoreManager.lookup</a>.</p>\n\n<p>Gets a registered Store by id</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>store</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The id of the Store, or a Store instance, or a store configuration</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getUniqueGlobalNamespace' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-getUniqueGlobalNamespace' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-getUniqueGlobalNamespace' class='name expandable'>getUniqueGlobalNamespace</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Generate a unique reference of Ext in the global scope, useful for sandboxing ...</div><div class='long'><p>Generate a unique reference of Ext in the global scope, useful for sandboxing</p>\n</div></div></div><div id='method-getVersion' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Version.html#Ext-method-getVersion' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-getVersion' class='name expandable'>getVersion</a>( <span class='pre'>[packageName]</span> ) : <a href=\"#!/api/Ext.Version\" rel=\"Ext.Version\" class=\"docClass\">Ext.Version</a></div><div class='description'><div class='short'>Get the version number of the supplied package name; will return the last registered version\n(last Ext.setVersion cal...</div><div class='long'><p>Get the version number of the supplied package name; will return the last registered version\n(last <a href=\"#!/api/Ext-method-setVersion\" rel=\"Ext-method-setVersion\" class=\"docClass\">Ext.setVersion</a> call) if there's no package name given.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>packageName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The package name, for example: 'core', 'touch', 'extjs'</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Version\" rel=\"Ext.Version\" class=\"docClass\">Ext.Version</a></span><div class='sub-desc'><p>The version</p>\n</div></li></ul></div></div></div><div id='method-htmlDecode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/String2.html#Ext-method-htmlDecode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-htmlDecode' class='name expandable'>htmlDecode</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Old alias to Ext.String.htmlDecode\n\nConvert certain characters (&amp;, &lt;, >, ', and \") from their HTML character e...</div><div class='long'><p>Old alias to <a href=\"#!/api/Ext.String-method-htmlDecode\" rel=\"Ext.String-method-htmlDecode\" class=\"docClass\">Ext.String.htmlDecode</a></p>\n\n<p>Convert certain characters (&amp;, &lt;, >, ', and \") from their HTML character equivalents.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>Use <a href=\"#!/api/Ext.String-method-htmlDecode\" rel=\"Ext.String-method-htmlDecode\" class=\"docClass\">Ext.String.htmlDecode</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The string to decode.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The decoded text.</p>\n</div></li></ul></div></div></div><div id='method-htmlEncode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/String2.html#Ext-method-htmlEncode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-htmlEncode' class='name expandable'>htmlEncode</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Old alias to Ext.String.htmlEncode\n\nConvert certain characters (&amp;, &lt;, >, ', and \") to their HTML character equ...</div><div class='long'><p>Old alias to <a href=\"#!/api/Ext.String-method-htmlEncode\" rel=\"Ext.String-method-htmlEncode\" class=\"docClass\">Ext.String.htmlEncode</a></p>\n\n<p>Convert certain characters (&amp;, &lt;, >, ', and \") to their HTML character equivalents for literal display in web pages.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>Use <a href=\"#!/api/Ext.String-method-htmlEncode\" rel=\"Ext.String-method-htmlEncode\" class=\"docClass\">Ext.String.htmlEncode</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The string to encode.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The encoded text.</p>\n</div></li></ul></div></div></div><div id='method-id' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-id' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-id' class='name expandable'>id</a>( <span class='pre'>[el], [prefix]</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Generates unique ids. ...</div><div class='long'><p>Generates unique ids. If the element already has an id, it is unchanged</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : HTMLElement/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a> (optional)<div class='sub-desc'><p>The element to generate an id for</p>\n</div></li><li><span class='pre'>prefix</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Id prefix (defaults \"ext-gen\")</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The generated Id.</p>\n</div></li></ul></div></div></div><div id='method-identityFn' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-identityFn' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-identityFn' class='name expandable'>identityFn</a>( <span class='pre'>o</span> )</div><div class='description'><div class='short'>A reusable identity function. ...</div><div class='long'><p>A reusable identity function. The function will always return the first argument, unchanged.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>o</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-invoke' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-invoke' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-invoke' class='name expandable'>invoke</a>( <span class='pre'>arr, methodName, args</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Invokes a method on each item in an Array. ...</div><div class='long'><p>Invokes a method on each item in an Array.</p>\n\n<p>Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-invoke\" rel=\"Ext-method-invoke\" class=\"docClass\">Ext.invoke</a>(<a href=\"#!/api/Ext-method-query\" rel=\"Ext-method-query\" class=\"docClass\">Ext.query</a>(\"p\"), \"getAttribute\", \"id\");\n// [el1.getAttribute(\"id\"), el2.getAttribute(\"id\"), ..., elN.getAttribute(\"id\")]\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Will be removed in the next major version</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>arr</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/NodeList<div class='sub-desc'><p>The Array of items to invoke the method on.</p>\n</div></li><li><span class='pre'>methodName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The method name to invoke.</p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>...<div class='sub-desc'><p>Arguments to send into the method invocation.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>The results of invoking the method on each item in the array.</p>\n</div></li></ul></div></div></div><div id='method-isArray' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-isArray' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-isArray' class='name expandable'>isArray</a>( <span class='pre'>target</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the passed value is a JavaScript Array, false otherwise. ...</div><div class='long'><p>Returns true if the passed value is a JavaScript Array, false otherwise.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The target to test</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isBoolean' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-isBoolean' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-isBoolean' class='name expandable'>isBoolean</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the passed value is a boolean. ...</div><div class='long'><p>Returns true if the passed value is a boolean.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to test</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isDate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-isDate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-isDate' class='name expandable'>isDate</a>( <span class='pre'>object</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the passed value is a JavaScript Date object, false otherwise. ...</div><div class='long'><p>Returns true if the passed value is a JavaScript Date object, false otherwise.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>object</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object to test</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isDefined' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-isDefined' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-isDefined' class='name expandable'>isDefined</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the passed value is defined. ...</div><div class='long'><p>Returns true if the passed value is defined.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to test</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isElement' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-isElement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-isElement' class='name expandable'>isElement</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the passed value is an HTMLElement ...</div><div class='long'><p>Returns true if the passed value is an HTMLElement</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to test</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isEmpty' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-isEmpty' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-isEmpty' class='name expandable'>isEmpty</a>( <span class='pre'>value, [allowEmptyString]</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the passed value is empty, false otherwise. ...</div><div class='long'><p>Returns true if the passed value is empty, false otherwise. The value is deemed to be empty if it is either:</p>\n\n<ul>\n<li><code>null</code></li>\n<li><code>undefined</code></li>\n<li>a zero-length array</li>\n<li>a zero-length string (Unless the <code>allowEmptyString</code> parameter is set to <code>true</code>)</li>\n</ul>\n\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to test</p>\n</div></li><li><span class='pre'>allowEmptyString</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>true to allow empty strings (defaults to false)</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isFunction' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-isFunction' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-isFunction' class='name expandable'>isFunction</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the passed value is a JavaScript Function, false otherwise. ...</div><div class='long'><p>Returns true if the passed value is a JavaScript Function, false otherwise.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to test</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isIterable' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-isIterable' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-isIterable' class='name expandable'>isIterable</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the passed value is iterable, that is, if elements of it are addressable using array\nnotation with nu...</div><div class='long'><p>Returns <code>true</code> if the passed value is iterable, that is, if elements of it are addressable using array\nnotation with numeric indices, <code>false</code> otherwise.</p>\n\n<p>Arrays and function <code>arguments</code> objects are iterable. Also HTML collections such as <code>NodeList</code> and `HTMLCollection'\nare iterable.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to test</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isNumber' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-isNumber' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-isNumber' class='name expandable'>isNumber</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the passed value is a number. ...</div><div class='long'><p>Returns true if the passed value is a number. Returns false for non-finite numbers.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to test</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isNumeric' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-isNumeric' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-isNumeric' class='name expandable'>isNumeric</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Validates that a value is numeric. ...</div><div class='long'><p>Validates that a value is numeric.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>Examples: 1, '1', '2.34'</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if numeric, false otherwise</p>\n</div></li></ul></div></div></div><div id='method-isObject' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-isObject' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-isObject' class='name expandable'>isObject</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the passed value is a JavaScript Object, false otherwise. ...</div><div class='long'><p>Returns true if the passed value is a JavaScript Object, false otherwise.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to test</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isPrimitive' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-isPrimitive' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-isPrimitive' class='name expandable'>isPrimitive</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the passed value is a JavaScript 'primitive', a string, number or boolean. ...</div><div class='long'><p>Returns true if the passed value is a JavaScript 'primitive', a string, number or boolean.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to test</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isSimpleObject' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-isSimpleObject' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-isSimpleObject' class='name expandable'>isSimpleObject</a>( <span class='pre'>value</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-isString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-isString' class='name expandable'>isString</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the passed value is a string. ...</div><div class='long'><p>Returns true if the passed value is a string.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to test</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isTextNode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-isTextNode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-isTextNode' class='name expandable'>isTextNode</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the passed value is a TextNode ...</div><div class='long'><p>Returns true if the passed value is a TextNode</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to test</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-iterate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-iterate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-iterate' class='name expandable'>iterate</a>( <span class='pre'>object, fn, [scope]</span> )</div><div class='description'><div class='short'>Iterates either an array or an object. ...</div><div class='long'><p>Iterates either an array or an object. This method delegates to\n<a href=\"#!/api/Ext.Array-method-each\" rel=\"Ext.Array-method-each\" class=\"docClass\">Ext.Array.each</a> if the given value is iterable, and <a href=\"#!/api/Ext.Object-method-each\" rel=\"Ext.Object-method-each\" class=\"docClass\">Ext.Object.each</a> otherwise.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>object</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The object or array to be iterated.</p>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The function to be called for each iteration. See and <a href=\"#!/api/Ext.Array-method-each\" rel=\"Ext.Array-method-each\" class=\"docClass\">Ext.Array.each</a> and\n<a href=\"#!/api/Ext.Object-method-each\" rel=\"Ext.Object-method-each\" class=\"docClass\">Ext.Object.each</a> for detailed lists of arguments passed to this function depending on the given object\ntype that is being iterated.</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the specified function is executed.\nDefaults to the object being iterated itself.</p>\n</div></li></ul></div></div></div><div id='method-log' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-log' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-log' class='name expandable'>log</a>( <span class='pre'>[options], [message]</span> )</div><div class='description'><div class='short'>Logs a message. ...</div><div class='long'><p>Logs a message. If a console is present it will be used. On Opera, the method\n\"opera.postError\" is called. In other cases, the message is logged to an array\n\"Ext.log.out\". An attached debugger can watch this array and view the log. The\nlog buffer is limited to a maximum of \"Ext.log.max\" entries (defaults to 250).\nThe <code>Ext.log.out</code> array can also be written to a popup window by entering the\nfollowing in the URL bar (a \"bookmarklet\"):</p>\n\n<pre><code>javascript:void(Ext.log.show());\n</code></pre>\n\n<p>If additional parameters are passed, they are joined and appended to the message.\nA technique for tracing entry and exit of a function is this:</p>\n\n<pre><code>function foo () {\n    <a href=\"#!/api/Ext-method-log\" rel=\"Ext-method-log\" class=\"docClass\">Ext.log</a>({ indent: 1 }, '&gt;&gt; foo');\n\n    // log statements in here or methods called from here will be indented\n    // by one step\n\n    <a href=\"#!/api/Ext-method-log\" rel=\"Ext-method-log\" class=\"docClass\">Ext.log</a>({ outdent: 1 }, '&lt;&lt; foo');\n}\n</code></pre>\n\n<p>This method does nothing in a release build.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The message to log or an options object with any\nof the following properties:</p>\n\n<ul>\n<li><code>msg</code>: The message to log (required).</li>\n<li><code>level</code>: One of: \"error\", \"warn\", \"info\" or \"log\" (the default is \"log\").</li>\n<li><code>dump</code>: An object to dump to the log as part of the message.</li>\n<li><code>stack</code>: True to include a stack trace in the log.</li>\n<li><code>indent</code>: Cause subsequent log statements to be indented one step.</li>\n<li><code>outdent</code>: Cause this and following statements to be one step less indented.</li>\n</ul>\n\n</div></li><li><span class='pre'>message</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>... (optional)<div class='sub-desc'><p>The message to log (required unless specified in\noptions object).</p>\n</div></li></ul></div></div></div><div id='method-max' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Array2.html#Ext-method-max' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-max' class='name expandable'>max</a>( <span class='pre'>array, [comparisonFn]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Old alias to Ext.Array.max\n\nReturns the maximum value in the Array. ...</div><div class='long'><p>Old alias to <a href=\"#!/api/Ext.Array-method-max\" rel=\"Ext.Array-method-max\" class=\"docClass\">Ext.Array.max</a></p>\n\n<p>Returns the maximum value in the Array.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Use <a href=\"#!/api/Ext.Array-method-max\" rel=\"Ext.Array-method-max\" class=\"docClass\">Ext.Array.max</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/NodeList<div class='sub-desc'><p>The Array from which to select the maximum value.</p>\n</div></li><li><span class='pre'>comparisonFn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>a function to perform the comparision which determines maximization.\nIf omitted the \">\" operator will be used. Note: gt = 1; eq = 0; lt = -1</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>max</span> : Mixed<div class='sub-desc'><p>Current maximum value.</p>\n</div></li><li><span class='pre'>item</span> : Mixed<div class='sub-desc'><p>The value to compare with the current maximum.</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>maxValue The maximum value</p>\n</div></li></ul></div></div></div><div id='method-mean' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Array2.html#Ext-method-mean' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-mean' class='name expandable'>mean</a>( <span class='pre'>array</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Old alias to Ext.Array.mean\n\nCalculates the mean of all items in the array. ...</div><div class='long'><p>Old alias to <a href=\"#!/api/Ext.Array-method-mean\" rel=\"Ext.Array-method-mean\" class=\"docClass\">Ext.Array.mean</a></p>\n\n<p>Calculates the mean of all items in the array.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Use <a href=\"#!/api/Ext.Array-method-mean\" rel=\"Ext.Array-method-mean\" class=\"docClass\">Ext.Array.mean</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The Array to calculate the mean value of.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The mean.</p>\n</div></li></ul></div></div></div><div id='method-merge' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Object2.html#Ext-method-merge' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-merge' class='name expandable'>merge</a>( <span class='pre'>destination, object</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>A convenient alias method for Ext.Object.merge. ...</div><div class='long'><p>A convenient alias method for <a href=\"#!/api/Ext.Object-method-merge\" rel=\"Ext.Object-method-merge\" class=\"docClass\">Ext.Object.merge</a>.</p>\n\n<p>Merges any number of objects recursively without referencing them or their children.</p>\n\n<pre><code>var extjs = {\n    companyName: 'Ext JS',\n    products: ['Ext JS', 'Ext GWT', 'Ext Designer'],\n    isSuperCool: true,\n    office: {\n        size: 2000,\n        location: 'Palo Alto',\n        isFun: true\n    }\n};\n\nvar newStuff = {\n    companyName: 'Sencha Inc.',\n    products: ['Ext JS', 'Ext GWT', 'Ext Designer', 'Sencha Touch', 'Sencha Animator'],\n    office: {\n        size: 40000,\n        location: 'Redwood City'\n    }\n};\n\nvar sencha = <a href=\"#!/api/Ext.Object-method-merge\" rel=\"Ext.Object-method-merge\" class=\"docClass\">Ext.Object.merge</a>(extjs, newStuff);\n\n// extjs and sencha then equals to\n{\n    companyName: 'Sencha Inc.',\n    products: ['Ext JS', 'Ext GWT', 'Ext Designer', 'Sencha Touch', 'Sencha Animator'],\n    isSuperCool: true,\n    office: {\n        size: 40000,\n        location: 'Redwood City',\n        isFun: true\n    }\n}\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>destination</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object into which all subsequent objects are merged.</p>\n</div></li><li><span class='pre'>object</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>...<div class='sub-desc'><p>Any number of objects to merge into the destination.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>merged The destination object with all passed objects merged in.</p>\n</div></li></ul></div></div></div><div id='method-min' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Array2.html#Ext-method-min' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-min' class='name expandable'>min</a>( <span class='pre'>array, [comparisonFn]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Old alias to Ext.Array.min\n\nReturns the minimum value in the Array. ...</div><div class='long'><p>Old alias to <a href=\"#!/api/Ext.Array-method-min\" rel=\"Ext.Array-method-min\" class=\"docClass\">Ext.Array.min</a></p>\n\n<p>Returns the minimum value in the Array.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Use <a href=\"#!/api/Ext.Array-method-min\" rel=\"Ext.Array-method-min\" class=\"docClass\">Ext.Array.min</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/NodeList<div class='sub-desc'><p>The Array from which to select the minimum value.</p>\n</div></li><li><span class='pre'>comparisonFn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>a function to perform the comparision which determines minimization.\nIf omitted the \"&lt;\" operator will be used. Note: gt = 1; eq = 0; lt = -1</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>min</span> : Mixed<div class='sub-desc'><p>Current minimum value.</p>\n</div></li><li><span class='pre'>item</span> : Mixed<div class='sub-desc'><p>The value to compare with the current minimum.</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>minValue The minimum value</p>\n</div></li></ul></div></div></div><div id='method-namespace' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/ClassManager.html#Ext-method-namespace' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-namespace' class='name expandable'>namespace</a>( <span class='pre'>namespaces</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Creates namespaces to be used for scoping variables and classes so that they are not global. ...</div><div class='long'><p>Creates namespaces to be used for scoping variables and classes so that they are not global.\nSpecifying the last node of a namespace implicitly creates all other nodes. Usage:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-namespace\" rel=\"Ext-method-namespace\" class=\"docClass\">Ext.namespace</a>('Company', 'Company.data');\n\n// equivalent and preferable to the above syntax\n<a href=\"#!/api/Ext-method-ns\" rel=\"Ext-method-ns\" class=\"docClass\">Ext.ns</a>('Company.data');\n\nCompany.Widget = function() { ... };\n\nCompany.data.CustomStore = function(config) { ... };\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>namespaces</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>...<div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The namespace object.\n(If multiple arguments are passed, this will be the last namespace created)</p>\n\n</div></li></ul></div></div></div><div id='method-ns' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/ClassManager.html#Ext-method-ns' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-ns' class='name expandable'>ns</a>( <span class='pre'>namespaces</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Convenient alias for Ext.namespace. ...</div><div class='long'><p>Convenient alias for <a href=\"#!/api/Ext-method-namespace\" rel=\"Ext-method-namespace\" class=\"docClass\">Ext.namespace</a>.</p>\n\n<p>Creates namespaces to be used for scoping variables and classes so that they are not global.\nSpecifying the last node of a namespace implicitly creates all other nodes. Usage:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-namespace\" rel=\"Ext-method-namespace\" class=\"docClass\">Ext.namespace</a>('Company', 'Company.data');\n\n// equivalent and preferable to the above syntax\n<a href=\"#!/api/Ext-method-ns\" rel=\"Ext-method-ns\" class=\"docClass\">Ext.ns</a>('Company.data');\n\nCompany.Widget = function() { ... };\n\nCompany.data.CustomStore = function(config) { ... };\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>namespaces</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>...<div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The namespace object.\n(If multiple arguments are passed, this will be the last namespace created)</p>\n\n</div></li></ul></div></div></div><div id='method-num' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Number2.html#Ext-method-num' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-num' class='name expandable'>num</a>( <span class='pre'>value, defaultValue</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Validate that a value is numeric and convert it to a number if necessary. ...</div><div class='long'><p>Validate that a value is numeric and convert it to a number if necessary. Returns the specified default value if\nit is not.</p>\n\n<pre><code><a href=\"#!/api/Ext.Number-method-from\" rel=\"Ext.Number-method-from\" class=\"docClass\">Ext.Number.from</a>('1.23', 1); // returns 1.23\n<a href=\"#!/api/Ext.Number-method-from\" rel=\"Ext.Number-method-from\" class=\"docClass\">Ext.Number.from</a>('abc', 1); // returns 1\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Please use <a href=\"#!/api/Ext.Number-method-from\" rel=\"Ext.Number-method-from\" class=\"docClass\">Ext.Number.from</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>defaultValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The value to return if the original value is non-numeric</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>value, if numeric, defaultValue otherwise</p>\n</div></li></ul></div></div></div><div id='method-on' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Observable.html#Ext-method-on' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-on' class='name expandable'>on</a>( <span class='pre'>eventName, [fn], [scope], [options]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Shorthand for the Ext.util.Observable.addListener method of the\nglobalEvents Observable instance. ...</div><div class='long'><p>Shorthand for the <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a> method of the\n<a href=\"#!/api/Ext-property-globalEvents\" rel=\"Ext-property-globalEvents\" class=\"docClass\">globalEvents</a> Observable instance.</p>\n\n<p>Appends an event handler to this object.  For example:</p>\n\n<pre><code>myGridPanel.on(\"mouseover\", this.onMouseOver, this);\n</code></pre>\n\n<p>The method also allows for a single argument to be passed which is a config object\ncontaining properties which specify multiple events. For example:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: this.onCellClick,\n    mouseover: this.onMouseOver,\n    mouseout: this.onMouseOut,\n    scope: this // Important. Ensure \"this\" is correct during handler execution\n});\n</code></pre>\n\n<p>One can also specify options for each event handler separately:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: this.onCellClick, scope: this, single: true},\n    mouseover: {fn: panel.onMouseOver, scope: panel}\n});\n</code></pre>\n\n<p><em>Names</em> of methods in a specified scope may also be used. Note that\n<code>scope</code> MUST be specified to use this option:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: 'onCellClick', scope: this, single: true},\n    mouseover: {fn: 'onMouseOver', scope: panel}\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The name of the event to listen for.\nMay also be an object who's property names are event names.</p>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The method the event invokes, or <em>if <code>scope</code> is specified, the </em>name* of the method within\nthe specified <code>scope</code>.  Will be called with arguments\ngiven to <a href=\"#!/api/Ext.util.Observable-method-fireEvent\" rel=\"Ext.util.Observable-method-fireEvent\" class=\"docClass\">Ext.util.Observable.fireEvent</a> plus the <code>options</code> parameter described below.</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is\nexecuted. <strong>If omitted, defaults to the object which fired the event.</strong></p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>An object containing handler configuration.</p>\n\n<p><strong>Note:</strong> Unlike in ExtJS 3.x, the options object will also be passed as the last\nargument to every event handler.</p>\n\n<p>This object may contain any of the following properties:</p>\n<ul><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is executed. <strong>If omitted,\n  defaults to the object which fired the event.</strong></p>\n</div></li><li><span class='pre'>delay</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of milliseconds to delay the invocation of the handler after the event fires.</p>\n</div></li><li><span class='pre'>single</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to add a handler to handle just the next firing of the event, and then remove itself.</p>\n</div></li><li><span class='pre'>buffer</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Causes the handler to be scheduled to run in an <a href=\"#!/api/Ext.util.DelayedTask\" rel=\"Ext.util.DelayedTask\" class=\"docClass\">Ext.util.DelayedTask</a> delayed\n  by the specified number of milliseconds. If the event fires again within that time,\n  the original handler is <em>not</em> invoked, but the new handler is scheduled in its place.</p>\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a><div class='sub-desc'><p>Only call the handler if the event was fired on the target Observable, <em>not</em> if the event\n  was bubbled up from a child Observable.</p>\n</div></li><li><span class='pre'>element</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p><strong>This option is only valid for listeners bound to <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a>.</strong>\n  The name of a Component property which references an element to add a listener to.</p>\n\n<p>  This option is useful during Component construction to add DOM event listeners to elements of\n  <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a> which will exist only after the Component is rendered.\n  For example, to add a click listener to a Panel's body:</p>\n\n<pre><code>  new <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>({\n      title: 'The title',\n      listeners: {\n          click: this.handlePanelClick,\n          element: 'body'\n      }\n  });\n</code></pre>\n</div></li><li><span class='pre'>destroyable</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>When specified as <code>true</code>, the function returns A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call.</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>priority</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>An optional numeric priority that determines the order in which event handlers\n  are run. Event handlers with no priority will be run as if they had a priority\n  of 0. Handlers with a higher priority will be prioritized to run sooner than\n  those with a lower priority.  Negative numbers can be used to set a priority\n  lower than the default. Internally, the framework uses a range of 1000 or\n  greater, and -1000 or lesser for handers that are intended to run before or\n  after all others, so it is recommended to stay within the range of -999 to 999\n  when setting the priority of event handlers in application-level code.</p>\n\n<p><strong>Combining Options</strong></p>\n\n<p>Using the options argument, it is possible to combine different types of listeners:</p>\n\n<p>A delayed, one-time listener.</p>\n\n<pre><code>myPanel.on('hide', this.handleClick, this, {\n    single: true,\n    delay: 100\n});\n</code></pre>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><strong>Only when the <code>destroyable</code> option is specified. </strong></p>\n\n<p> A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call. For example:</p>\n\n<pre><code>this.btnListeners =  = myButton.on({\n    destroyable: true\n    mouseover:   function() { console.log('mouseover'); },\n    mouseout:    function() { console.log('mouseout'); },\n    click:       function() { console.log('click'); }\n});\n</code></pre>\n\n<p>And when those listeners need to be removed:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.btnListeners);\n</code></pre>\n\n<p>or</p>\n\n<pre><code>this.btnListeners.destroy();\n</code></pre>\n</div></li></ul></div></div></div><div id='method-onDocumentReady' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/EventManager.html#Ext-method-onDocumentReady' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-onDocumentReady' class='name expandable'>onDocumentReady</a>( <span class='pre'>fn, [scope], [options]</span> )</div><div class='description'><div class='short'>Adds a listener to be notified when the document is ready (before onload and before images are loaded). ...</div><div class='long'><p>Adds a listener to be notified when the document is ready (before onload and before images are loaded).</p>\n\n<p><a href=\"#!/api/Ext-method-onDocumentReady\" rel=\"Ext-method-onDocumentReady\" class=\"docClass\">onDocumentReady</a> is an alias for <a href=\"#!/api/Ext.EventManager-method-onDocumentReady\" rel=\"Ext.EventManager-method-onDocumentReady\" class=\"docClass\">Ext.EventManager.onDocumentReady</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The method the event invokes.</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function executes.\nDefaults to the browser window.</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Options object as passed to <a href=\"#!/api/Ext.dom.Element-method-addListener\" rel=\"Ext.dom.Element-method-addListener\" class=\"docClass\">Ext.Element.addListener</a>.</p>\n</div></li></ul></div></div></div><div id='method-onReady' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/EventManager.html#Ext-method-onReady' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-onReady' class='name expandable'>onReady</a>( <span class='pre'>fn, scope, options</span> )</div><div class='description'><div class='short'>Adds a function to be called when the DOM is ready, and all required classes have been loaded. ...</div><div class='long'><p>Adds a function to be called when the DOM is ready, and all required classes have been loaded.</p>\n\n<p>If the DOM is ready and all classes are loaded, the passed function is executed immediately.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The function callback to be executed</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The execution scope (<code>this</code> reference) of the callback function</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options to modify the listener as passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a>.</p>\n</div></li></ul></div></div></div><div id='method-override' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-override' class='name expandable'>override</a>( <span class='pre'>target, overrides</span> )</div><div class='description'><div class='short'>Overrides members of the specified target with the given values. ...</div><div class='long'><p>Overrides members of the specified <code>target</code> with the given values.</p>\n\n<p>If the <code>target</code> is a class declared using <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>, the\n<code>override</code> method of that class is called (see <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">Ext.Base.override</a>) given\nthe <code>overrides</code>.</p>\n\n<p>If the <code>target</code> is a function, it is assumed to be a constructor and the contents\nof <code>overrides</code> are applied to its <code>prototype</code> using <a href=\"#!/api/Ext-method-apply\" rel=\"Ext-method-apply\" class=\"docClass\">Ext.apply</a>.</p>\n\n<p>If the <code>target</code> is an instance of a class declared using <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>,\nthe <code>overrides</code> are applied to only that instance. In this case, methods are\nspecially processed to allow them to use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">Ext.Base.callParent</a>.</p>\n\n<pre><code> var panel = new <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.Panel</a>({ ... });\n\n <a href=\"#!/api/Ext-method-override\" rel=\"Ext-method-override\" class=\"docClass\">Ext.override</a>(panel, {\n     initComponent: function () {\n         // extra processing...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>If the <code>target</code> is none of these, the <code>overrides</code> are applied to the <code>target</code>\nusing <a href=\"#!/api/Ext-method-apply\" rel=\"Ext-method-apply\" class=\"docClass\">Ext.apply</a>.</p>\n\n<p>Please refer to <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> and <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">Ext.Base.override</a> for\nfurther details.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The target to override.</p>\n</div></li><li><span class='pre'>overrides</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add or replace on <code>target</code>.</p>\n</div></li></ul></div></div></div><div id='method-partition' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-partition' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-partition' class='name expandable'>partition</a>( <span class='pre'>arr, [truth]</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Partitions the set into two sets: a true set and a false set. ...</div><div class='long'><p>Partitions the set into two sets: a true set and a false set.</p>\n\n<p>Example 1:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-partition\" rel=\"Ext-method-partition\" class=\"docClass\">Ext.partition</a>([true, false, true, true, false]);\n// returns [[true, true, true], [false, false]]\n</code></pre>\n\n<p>Example 2:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-partition\" rel=\"Ext-method-partition\" class=\"docClass\">Ext.partition</a>(\n    <a href=\"#!/api/Ext-method-query\" rel=\"Ext-method-query\" class=\"docClass\">Ext.query</a>(\"p\"),\n    function(val){\n        return val.className == \"class1\"\n    }\n);\n// true are those paragraph elements with a className of \"class1\",\n// false set are those that do not have that className.\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Will be removed in the next major version</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>arr</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/NodeList<div class='sub-desc'><p>The array to partition</p>\n</div></li><li><span class='pre'>truth</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>a function to determine truth.\nIf this is omitted the element itself must be able to be evaluated for its truthfulness.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>[array of truish values, array of falsy values]</p>\n</div></li></ul></div></div></div><div id='method-pass' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Function2.html#Ext-method-pass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-pass' class='name expandable'>pass</a>( <span class='pre'>fn, args, [scope]</span> ) : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></div><div class='description'><div class='short'>Create a new function from the provided fn, the arguments of which are pre-set to args. ...</div><div class='long'><p>Create a new function from the provided <code>fn</code>, the arguments of which are pre-set to <code>args</code>.\nNew arguments passed to the newly created callback when it's invoked are appended after the pre-set ones.\nThis is especially useful when creating callbacks.</p>\n\n<p>For example:</p>\n\n<pre><code>var originalFunction = function(){\n    alert(<a href=\"#!/api/Ext.Array-method-from\" rel=\"Ext.Array-method-from\" class=\"docClass\">Ext.Array.from</a>(arguments).join(' '));\n};\n\nvar callback = <a href=\"#!/api/Ext.Function-method-pass\" rel=\"Ext.Function-method-pass\" class=\"docClass\">Ext.Function.pass</a>(originalFunction, ['Hello', 'World']);\n\ncallback(); // alerts 'Hello World'\ncallback('by Me'); // alerts 'Hello World by Me'\n</code></pre>\n\n<p><a href=\"#!/api/Ext-method-pass\" rel=\"Ext-method-pass\" class=\"docClass\">Ext.pass</a> is alias for <a href=\"#!/api/Ext.Function-method-pass\" rel=\"Ext.Function-method-pass\" class=\"docClass\">Ext.Function.pass</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The original function</p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The arguments to pass to new callback</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the function is executed.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></span><div class='sub-desc'><p>The new callback function</p>\n</div></li></ul></div></div></div><div id='method-pluck' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Array2.html#Ext-method-pluck' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-pluck' class='name expandable'>pluck</a>( <span class='pre'>array, propertyName</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Old alias to Ext.Array.pluck\n\nPlucks the value of a property from each item in the Array. ...</div><div class='long'><p>Old alias to <a href=\"#!/api/Ext.Array-method-pluck\" rel=\"Ext.Array-method-pluck\" class=\"docClass\">Ext.Array.pluck</a></p>\n\n<p>Plucks the value of a property from each item in the Array. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext.Array-method-pluck\" rel=\"Ext.Array-method-pluck\" class=\"docClass\">Ext.Array.pluck</a>(<a href=\"#!/api/Ext-method-query\" rel=\"Ext-method-query\" class=\"docClass\">Ext.query</a>(\"p\"), \"className\"); // [el1.className, el2.className, ..., elN.className]\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Use <a href=\"#!/api/Ext.Array-method-pluck\" rel=\"Ext.Array-method-pluck\" class=\"docClass\">Ext.Array.pluck</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/NodeList<div class='sub-desc'><p>The Array of items to pluck the value from.</p>\n</div></li><li><span class='pre'>propertyName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The property name to pluck from each element.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>The value from each item in the Array.</p>\n</div></li></ul></div></div></div><div id='method-preg' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/PluginManager.html#Ext-method-preg' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-preg' class='name expandable'>preg</a>( <span class='pre'>ptype, cls</span> )</div><div class='description'><div class='short'>Shorthand for Ext.PluginManager.registerType ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.PluginManager-method-registerType\" rel=\"Ext.PluginManager-method-registerType\" class=\"docClass\">Ext.PluginManager.registerType</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ptype</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The ptype mnemonic string by which the Plugin class\nmay be looked up.</p>\n</div></li><li><span class='pre'>cls</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The new Plugin class.</p>\n</div></li></ul></div></div></div><div id='method-query' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Query.html#Ext-method-query' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-query' class='name expandable'>query</a>( <span class='pre'>path, [root], [type], [single]</span> ) : HTMLElement[]</div><div class='description'><div class='short'>Shorthand of Ext.dom.Query.select\n\nSelects an array of DOM nodes by CSS/XPath selector. ...</div><div class='long'><p>Shorthand of <a href=\"#!/api/Ext.dom.Query-method-select\" rel=\"Ext.dom.Query-method-select\" class=\"docClass\">Ext.dom.Query.select</a></p>\n\n<p>Selects an array of DOM nodes by CSS/XPath selector.</p>\n\n<p>Uses <a href=\"https://developer.mozilla.org/en/DOM/document.querySelectorAll\">document.querySelectorAll</a> if browser supports that, otherwise falls back to\n<a href=\"#!/api/Ext.dom.Query-method-jsSelect\" rel=\"Ext.dom.Query-method-jsSelect\" class=\"docClass\">Ext.dom.Query.jsSelect</a> to do the work.</p>\n\n<p>Aliased as <a href=\"#!/api/Ext-method-query\" rel=\"Ext-method-query\" class=\"docClass\">query</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>path</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The selector/xpath query</p>\n</div></li><li><span class='pre'>root</span> : HTMLElement (optional)<div class='sub-desc'><p>The start of the query.</p>\n<p>Defaults to: <code>document</code></p></div></li><li><span class='pre'>type</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Either \"select\" or \"simple\" for a simple selector match (only valid when\nused when the call is deferred to the jsSelect method)</p>\n<p>Defaults to: <code>&quot;select&quot;</code></p></div></li><li><span class='pre'>single</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>Pass <code>true</code> to select only the first matching node using <code>document.querySelector</code> (where available)</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>HTMLElement[]</span><div class='sub-desc'><p>An array of DOM elements (not a NodeList as returned by <code>querySelectorAll</code>).</p>\n</div></li></ul></div></div></div><div id='method-regModel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/ModelManager.html#Ext-method-regModel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-regModel' class='name expandable'>regModel</a>( <span class='pre'>name, config</span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Old way for creating Model classes. ...</div><div class='long'><p>Old way for creating Model classes.  Instead use:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>(\"MyModel\", {\n    extend: \"<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>\",\n    fields: []\n});\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">define</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>Name of the Model class.</p>\n</div></li><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>A configuration object for the Model you wish to create.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></span><div class='sub-desc'><p>The newly registered Model</p>\n</div></li></ul></div></div></div><div id='method-regStore' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/StoreManager.html#Ext-method-regStore' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-regStore' class='name expandable'>regStore</a>( <span class='pre'>id, config</span> )</div><div class='description'><div class='short'>Creates a new store for the given id and config, then registers it with the Store Manager. ...</div><div class='long'><p>Creates a new store for the given id and config, then registers it with the <a href=\"#!/api/Ext.data.StoreManager\" rel=\"Ext.data.StoreManager\" class=\"docClass\">Store Manager</a>.\nSample usage:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-regStore\" rel=\"Ext-method-regStore\" class=\"docClass\">Ext.regStore</a>('AllUsers', {\n    model: 'User'\n});\n\n// the store can now easily be used throughout the application\nnew Ext.List({\n    store: 'AllUsers',\n    ... other config\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The id to set on the new store</p>\n</div></li><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The store config</p>\n</div></li></ul></div></div></div><div id='method-removeNode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-removeNode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-removeNode' class='name expandable'>removeNode</a>( <span class='pre'>node</span> )</div><div class='description'><div class='short'>Removes a DOM node from the document. ...</div><div class='long'><p>Removes a DOM node from the document.</p>\n\n<p>Removes this element from the document, removes all DOM event listeners, and\ndeletes the cache reference. All DOM event listeners are removed from this element.\nIf <a href=\"#!/api/Ext-property-enableNestedListenerRemoval\" rel=\"Ext-property-enableNestedListenerRemoval\" class=\"docClass\">Ext.enableNestedListenerRemoval</a> is\n<code>true</code>, then DOM event listeners are also removed from all child nodes.\nThe body node will be ignored if passed in.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>node</span> : HTMLElement<div class='sub-desc'><p>The node to remove</p>\n</div></li></ul></div></div></div><div id='method-require' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Loader.html#Ext-method-require' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-require' class='name expandable'>require</a>( <span class='pre'>expressions, [fn], [scope], [excludes]</span> )</div><div class='description'><div class='short'>Loads all classes by the given names and all their direct dependencies; optionally executes\nthe given callback functi...</div><div class='long'><p>Loads all classes by the given names and all their direct dependencies; optionally executes\nthe given callback function when finishes, within the optional scope.</p>\n\n<p><a href=\"#!/api/Ext-method-require\" rel=\"Ext-method-require\" class=\"docClass\">require</a> is alias for <a href=\"#!/api/Ext.Loader-method-require\" rel=\"Ext.Loader-method-require\" class=\"docClass\">Ext.Loader.require</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>expressions</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>Can either be a string or an array of string</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The callback function</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The execution scope (<code>this</code>) of the callback function</p>\n\n</div></li><li><span class='pre'>excludes</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> (optional)<div class='sub-desc'><p>Classes to be excluded, useful when being used with expressions</p>\n\n</div></li></ul></div></div></div><div id='method-resolveMethod' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-resolveMethod' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-resolveMethod' class='name expandable'>resolveMethod</a>( <span class='pre'>fn, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-resumeLayouts' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/AbstractComponent.html#Ext-method-resumeLayouts' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-resumeLayouts' class='name expandable'>resumeLayouts</a>( <span class='pre'>[flush]</span> )</div><div class='description'><div class='short'>Resumes layout activity in the whole framework. ...</div><div class='long'><p>Resumes layout activity in the whole framework.</p>\n\n<p><a href=\"#!/api/Ext-method-suspendLayouts\" rel=\"Ext-method-suspendLayouts\" class=\"docClass\">suspendLayouts</a> is alias of <a href=\"#!/api/Ext.AbstractComponent-method-suspendLayouts\" rel=\"Ext.AbstractComponent-method-suspendLayouts\" class=\"docClass\">Ext.AbstractComponent.suspendLayouts</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>flush</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p><code>true</code> to perform all the pending layouts. This can also be\nachieved by calling <a href=\"#!/api/Ext.AbstractComponent-static-method-flushLayouts\" rel=\"Ext.AbstractComponent-static-method-flushLayouts\" class=\"docClass\">flushLayouts</a> directly.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul></div></div></div><div id='method-select' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/CompositeElement.html#Ext-method-select' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-select' class='name expandable'>select</a>( <span class='pre'>selector, [unique]</span> ) : <a href=\"#!/api/Ext.dom.CompositeElement\" rel=\"Ext.dom.CompositeElement\" class=\"docClass\">Ext.CompositeElement</a></div><div class='description'><div class='short'>Shorthand of Ext.Element.select. ...</div><div class='long'><p>Shorthand of <a href=\"#!/api/Ext.dom.Element-method-select\" rel=\"Ext.dom.Element-method-select\" class=\"docClass\">Ext.Element.select</a>.</p>\n\n<p>Creates a <a href=\"#!/api/Ext.dom.CompositeElement\" rel=\"Ext.dom.CompositeElement\" class=\"docClass\">Ext.CompositeElement</a> for child nodes based on the passed CSS selector (the selector should not contain an id).</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_traversal.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>selector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The CSS selector</p>\n</div></li><li><span class='pre'>unique</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to create a unique <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a> for each element. Defaults to a shared flyweight object.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.CompositeElement\" rel=\"Ext.dom.CompositeElement\" class=\"docClass\">Ext.CompositeElement</a></span><div class='sub-desc'><p>The composite element</p>\n</div></li></ul></div></div></div><div id='method-setGlyphFontFamily' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-setGlyphFontFamily' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-setGlyphFontFamily' class='name expandable'>setGlyphFontFamily</a>( <span class='pre'>fontFamily</span> )</div><div class='description'><div class='short'>Sets the default font-family to use for components that support a glyph config. ...</div><div class='long'><p>Sets the default font-family to use for components that support a <code>glyph</code> config.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fontFamily</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the font-family</p>\n</div></li></ul></div></div></div><div id='method-setVersion' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Version.html#Ext-method-setVersion' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-setVersion' class='name expandable'>setVersion</a>( <span class='pre'>packageName, version</span> ) : <a href=\"#!/api/Ext\" rel=\"Ext\" class=\"docClass\">Ext</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Set version number for the given package name. ...</div><div class='long'><p>Set version number for the given package name.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>packageName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The package name, for example: 'core', 'touch', 'extjs'</p>\n</div></li><li><span class='pre'>version</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.Version\" rel=\"Ext.Version\" class=\"docClass\">Ext.Version</a><div class='sub-desc'><p>The version, for example: '1.2.3alpha', '2.4.0-dev'</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext\" rel=\"Ext\" class=\"docClass\">Ext</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-sum' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Array2.html#Ext-method-sum' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-sum' class='name expandable'>sum</a>( <span class='pre'>array</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Old alias to Ext.Array.sum\n\nCalculates the sum of all items in the given array. ...</div><div class='long'><p>Old alias to <a href=\"#!/api/Ext.Array-method-sum\" rel=\"Ext.Array-method-sum\" class=\"docClass\">Ext.Array.sum</a></p>\n\n<p>Calculates the sum of all items in the given array.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Use <a href=\"#!/api/Ext.Array-method-sum\" rel=\"Ext.Array-method-sum\" class=\"docClass\">Ext.Array.sum</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The Array to calculate the sum value of.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The sum.</p>\n</div></li></ul></div></div></div><div id='method-suspendLayouts' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/AbstractComponent.html#Ext-method-suspendLayouts' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-suspendLayouts' class='name expandable'>suspendLayouts</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Stops layouts from happening in the whole framework. ...</div><div class='long'><p>Stops layouts from happening in the whole framework.</p>\n\n<p>It's useful to suspend the layout activity while updating multiple components and\ncontainers:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-suspendLayouts\" rel=\"Ext-method-suspendLayouts\" class=\"docClass\">Ext.suspendLayouts</a>();\n// batch of updates...\n<a href=\"#!/api/Ext-method-resumeLayouts\" rel=\"Ext-method-resumeLayouts\" class=\"docClass\">Ext.resumeLayouts</a>(true);\n</code></pre>\n\n<p><a href=\"#!/api/Ext-method-suspendLayouts\" rel=\"Ext-method-suspendLayouts\" class=\"docClass\">suspendLayouts</a> is alias of <a href=\"#!/api/Ext.AbstractComponent-method-suspendLayouts\" rel=\"Ext.AbstractComponent-method-suspendLayouts\" class=\"docClass\">Ext.AbstractComponent.suspendLayouts</a>.</p>\n\n<p>See also <a href=\"#!/api/Ext-method-batchLayouts\" rel=\"Ext-method-batchLayouts\" class=\"docClass\">batchLayouts</a> for more abstract way of doing this.</p>\n</div></div></div><div id='method-syncRequire' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Loader.html#Ext-method-syncRequire' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-syncRequire' class='name expandable'>syncRequire</a>( <span class='pre'>expressions, [fn], [scope], [excludes]</span> )</div><div class='description'><div class='short'>Synchronously loads all classes by the given names and all their direct dependencies; optionally\nexecutes the given c...</div><div class='long'><p>Synchronously loads all classes by the given names and all their direct dependencies; optionally\nexecutes the given callback function when finishes, within the optional scope.</p>\n\n<p><a href=\"#!/api/Ext-method-syncRequire\" rel=\"Ext-method-syncRequire\" class=\"docClass\">syncRequire</a> is alias for <a href=\"#!/api/Ext.Loader-method-syncRequire\" rel=\"Ext.Loader-method-syncRequire\" class=\"docClass\">Ext.Loader.syncRequire</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>expressions</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>Can either be a string or an array of string</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The callback function</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The execution scope (<code>this</code>) of the callback function</p>\n\n</div></li><li><span class='pre'>excludes</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> (optional)<div class='sub-desc'><p>Classes to be excluded, useful when being used with expressions</p>\n\n</div></li></ul></div></div></div><div id='method-toArray' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Array2.html#Ext-method-toArray' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-toArray' class='name expandable'>toArray</a>( <span class='pre'>iterable, [start], [end]</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Converts any iterable (numeric indices and a length property) into a true array. ...</div><div class='long'><p>Converts any iterable (numeric indices and a length property) into a true array.</p>\n\n<pre><code>function test() {\n    var args = <a href=\"#!/api/Ext.Array-method-toArray\" rel=\"Ext.Array-method-toArray\" class=\"docClass\">Ext.Array.toArray</a>(arguments),\n        fromSecondToLastArgs = <a href=\"#!/api/Ext.Array-method-toArray\" rel=\"Ext.Array-method-toArray\" class=\"docClass\">Ext.Array.toArray</a>(arguments, 1);\n\n    alert(args.join(' '));\n    alert(fromSecondToLastArgs.join(' '));\n}\n\ntest('just', 'testing', 'here'); // alerts 'just testing here';\n                                 // alerts 'testing here';\n\n<a href=\"#!/api/Ext.Array-method-toArray\" rel=\"Ext.Array-method-toArray\" class=\"docClass\">Ext.Array.toArray</a>(document.getElementsByTagName('div')); // will convert the NodeList into an array\n<a href=\"#!/api/Ext.Array-method-toArray\" rel=\"Ext.Array-method-toArray\" class=\"docClass\">Ext.Array.toArray</a>('splitted'); // returns ['s', 'p', 'l', 'i', 't', 't', 'e', 'd']\n<a href=\"#!/api/Ext.Array-method-toArray\" rel=\"Ext.Array-method-toArray\" class=\"docClass\">Ext.Array.toArray</a>('splitted', 0, 3); // returns ['s', 'p', 'l']\n</code></pre>\n\n<p><a href=\"#!/api/Ext-method-toArray\" rel=\"Ext-method-toArray\" class=\"docClass\">Ext.toArray</a> is alias for <a href=\"#!/api/Ext.Array-method-toArray\" rel=\"Ext.Array-method-toArray\" class=\"docClass\">Ext.Array.toArray</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>iterable</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>the iterable object to be turned into a true Array.</p>\n</div></li><li><span class='pre'>start</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>a zero-based index that specifies the start of extraction. Defaults to 0</p>\n</div></li><li><span class='pre'>end</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>a 1-based index that specifies the end of extraction. Defaults to the last\nindex of the iterable value</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>array</p>\n</div></li></ul></div></div></div><div id='method-toSentence' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-toSentence' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-toSentence' class='name expandable'>toSentence</a>( <span class='pre'>items, connector</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Turns an array into a sentence, joined by a specified connector - e.g.:\n\nExt.toSentence(['Adama', 'Tigh', 'Roslin']);...</div><div class='long'><p>Turns an array into a sentence, joined by a specified connector - e.g.:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-toSentence\" rel=\"Ext-method-toSentence\" class=\"docClass\">Ext.toSentence</a>(['Adama', 'Tigh', 'Roslin']); //'Adama, Tigh and Roslin'\n<a href=\"#!/api/Ext-method-toSentence\" rel=\"Ext-method-toSentence\" class=\"docClass\">Ext.toSentence</a>(['Adama', 'Tigh', 'Roslin'], 'or'); //'Adama, Tigh or Roslin'\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Will be removed in the next major version</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>items</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>The array to create a sentence from</p>\n</div></li><li><span class='pre'>connector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The string to use to connect the last two words.\nUsually 'and' or 'or' - defaults to 'and'.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The sentence string</p>\n</div></li></ul></div></div></div><div id='method-type' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-type' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-type' class='name expandable'>type</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Old alias to typeOf\n\nReturns the type of the given variable in string format. ...</div><div class='long'><p>Old alias to <a href=\"#!/api/Ext-method-typeOf\" rel=\"Ext-method-typeOf\" class=\"docClass\">typeOf</a></p>\n\n<p>Returns the type of the given variable in string format. List of possible values are:</p>\n\n<ul>\n<li><code>undefined</code>: If the given value is <code>undefined</code></li>\n<li><code>null</code>: If the given value is <code>null</code></li>\n<li><code>string</code>: If the given value is a string</li>\n<li><code>number</code>: If the given value is a number</li>\n<li><code>boolean</code>: If the given value is a boolean value</li>\n<li><code>date</code>: If the given value is a <code>Date</code> object</li>\n<li><code>function</code>: If the given value is a function reference</li>\n<li><code>object</code>: If the given value is an object</li>\n<li><code>array</code>: If the given value is an array</li>\n<li><code>regexp</code>: If the given value is a regular expression</li>\n<li><code>element</code>: If the given value is a DOM Element</li>\n<li><code>textnode</code>: If the given value is a DOM text node and contains something other than whitespace</li>\n<li><code>whitespace</code>: If the given value is a DOM text node and contains only whitespace</li>\n</ul>\n\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-typeOf\" rel=\"Ext-method-typeOf\" class=\"docClass\">typeOf</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-typeOf' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-typeOf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-typeOf' class='name expandable'>typeOf</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns the type of the given variable in string format. ...</div><div class='long'><p>Returns the type of the given variable in string format. List of possible values are:</p>\n\n<ul>\n<li><code>undefined</code>: If the given value is <code>undefined</code></li>\n<li><code>null</code>: If the given value is <code>null</code></li>\n<li><code>string</code>: If the given value is a string</li>\n<li><code>number</code>: If the given value is a number</li>\n<li><code>boolean</code>: If the given value is a boolean value</li>\n<li><code>date</code>: If the given value is a <code>Date</code> object</li>\n<li><code>function</code>: If the given value is a function reference</li>\n<li><code>object</code>: If the given value is an object</li>\n<li><code>array</code>: If the given value is an array</li>\n<li><code>regexp</code>: If the given value is a regular expression</li>\n<li><code>element</code>: If the given value is a DOM Element</li>\n<li><code>textnode</code>: If the given value is a DOM text node and contains something other than whitespace</li>\n<li><code>whitespace</code>: If the given value is a DOM text node and contains only whitespace</li>\n</ul>\n\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-un' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Observable.html#Ext-method-un' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-un' class='name expandable'>un</a>( <span class='pre'>eventName, fn, [scope]</span> )</div><div class='description'><div class='short'>Shorthand for the Ext.util.Observable.removeListener method of the\nglobalEvents Observable instance. ...</div><div class='long'><p>Shorthand for the <a href=\"#!/api/Ext.util.Observable-method-removeListener\" rel=\"Ext.util.Observable-method-removeListener\" class=\"docClass\">Ext.util.Observable.removeListener</a> method of the\n<a href=\"#!/api/Ext-property-globalEvents\" rel=\"Ext-property-globalEvents\" class=\"docClass\">globalEvents</a> Observable instance.</p>\n\n<p>Removes an event handler.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The type of event the handler was associated with.</p>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The handler to remove. <strong>This must be a reference to the function passed into the\n<a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a> call.</strong></p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope originally specified for the handler. It must be the same as the\nscope argument specified in the original call to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a> or the listener will not be removed.</p>\n</div></li></ul></div></div></div><div id='method-unique' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Array2.html#Ext-method-unique' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-unique' class='name expandable'>unique</a>( <span class='pre'>array</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Old alias to Ext.Array.unique\n\nReturns a new array with unique items ...</div><div class='long'><p>Old alias to <a href=\"#!/api/Ext.Array-method-unique\" rel=\"Ext.Array-method-unique\" class=\"docClass\">Ext.Array.unique</a></p>\n\n<p>Returns a new array with unique items</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Use <a href=\"#!/api/Ext.Array-method-unique\" rel=\"Ext.Array-method-unique\" class=\"docClass\">Ext.Array.unique</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>results</p>\n</div></li></ul></div></div></div><div id='method-urlAppend' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/String2.html#Ext-method-urlAppend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-urlAppend' class='name expandable'>urlAppend</a>( <span class='pre'>url, string</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Old alias to Ext.String.urlAppend\n\nAppends content to the query string of a URL, handling logic for whether to place\n...</div><div class='long'><p>Old alias to <a href=\"#!/api/Ext.String-method-urlAppend\" rel=\"Ext.String-method-urlAppend\" class=\"docClass\">Ext.String.urlAppend</a></p>\n\n<p>Appends content to the query string of a URL, handling logic for whether to place\na question mark or ampersand.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>Use <a href=\"#!/api/Ext.String-method-urlAppend\" rel=\"Ext.String-method-urlAppend\" class=\"docClass\">Ext.String.urlAppend</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>url</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The URL to append to.</p>\n</div></li><li><span class='pre'>string</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The content to append to the URL.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The resulting URL</p>\n</div></li></ul></div></div></div><div id='method-urlDecode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Object2.html#Ext-method-urlDecode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-urlDecode' class='name expandable'>urlDecode</a>( <span class='pre'>queryString, [recursive]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Alias for Ext.Object.fromQueryString. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.Object-method-fromQueryString\" rel=\"Ext.Object-method-fromQueryString\" class=\"docClass\">Ext.Object.fromQueryString</a>.</p>\n\n<p>Converts a query string back into an object.</p>\n\n<p>Non-recursive:</p>\n\n<pre><code><a href=\"#!/api/Ext.Object-method-fromQueryString\" rel=\"Ext.Object-method-fromQueryString\" class=\"docClass\">Ext.Object.fromQueryString</a>(\"foo=1&amp;bar=2\"); // returns {foo: '1', bar: '2'}\n<a href=\"#!/api/Ext.Object-method-fromQueryString\" rel=\"Ext.Object-method-fromQueryString\" class=\"docClass\">Ext.Object.fromQueryString</a>(\"foo=&amp;bar=2\"); // returns {foo: null, bar: '2'}\n<a href=\"#!/api/Ext.Object-method-fromQueryString\" rel=\"Ext.Object-method-fromQueryString\" class=\"docClass\">Ext.Object.fromQueryString</a>(\"some%20price=%24300\"); // returns {'some price': '$300'}\n<a href=\"#!/api/Ext.Object-method-fromQueryString\" rel=\"Ext.Object-method-fromQueryString\" class=\"docClass\">Ext.Object.fromQueryString</a>(\"colors=red&amp;colors=green&amp;colors=blue\"); // returns {colors: ['red', 'green', 'blue']}\n</code></pre>\n\n<p>Recursive:</p>\n\n<pre><code><a href=\"#!/api/Ext.Object-method-fromQueryString\" rel=\"Ext.Object-method-fromQueryString\" class=\"docClass\">Ext.Object.fromQueryString</a>(\n    \"username=Jacky&amp;\"+\n    \"dateOfBirth[day]=1&amp;dateOfBirth[month]=2&amp;dateOfBirth[year]=1911&amp;\"+\n    \"hobbies[0]=coding&amp;hobbies[1]=eating&amp;hobbies[2]=sleeping&amp;\"+\n    \"hobbies[3][0]=nested&amp;hobbies[3][1]=stuff\", true);\n\n// returns\n{\n    username: 'Jacky',\n    dateOfBirth: {\n        day: '1',\n        month: '2',\n        year: '1911'\n    },\n    hobbies: ['coding', 'eating', 'sleeping', ['nested', 'stuff']]\n}\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Use <a href=\"#!/api/Ext.Object-method-fromQueryString\" rel=\"Ext.Object-method-fromQueryString\" class=\"docClass\">Ext.Object.fromQueryString</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>queryString</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The query string to decode</p>\n</div></li><li><span class='pre'>recursive</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>Whether or not to recursively decode the string. This format is supported by\nPHP / Ruby on Rails servers and similar.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-urlEncode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Object2.html#Ext-method-urlEncode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-urlEncode' class='name expandable'>urlEncode</a>( <span class='pre'>object, [recursive]</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Takes an object and converts it to an encoded query string. ...</div><div class='long'><p>Takes an object and converts it to an encoded query string.</p>\n\n<p>Non-recursive:</p>\n\n<pre><code><a href=\"#!/api/Ext.Object-method-toQueryString\" rel=\"Ext.Object-method-toQueryString\" class=\"docClass\">Ext.Object.toQueryString</a>({foo: 1, bar: 2}); // returns \"foo=1&amp;bar=2\"\n<a href=\"#!/api/Ext.Object-method-toQueryString\" rel=\"Ext.Object-method-toQueryString\" class=\"docClass\">Ext.Object.toQueryString</a>({foo: null, bar: 2}); // returns \"foo=&amp;bar=2\"\n<a href=\"#!/api/Ext.Object-method-toQueryString\" rel=\"Ext.Object-method-toQueryString\" class=\"docClass\">Ext.Object.toQueryString</a>({'some price': '$300'}); // returns \"some%20price=%24300\"\n<a href=\"#!/api/Ext.Object-method-toQueryString\" rel=\"Ext.Object-method-toQueryString\" class=\"docClass\">Ext.Object.toQueryString</a>({date: new Date(2011, 0, 1)}); // returns \"date=%222011-01-01T00%3A00%3A00%22\"\n<a href=\"#!/api/Ext.Object-method-toQueryString\" rel=\"Ext.Object-method-toQueryString\" class=\"docClass\">Ext.Object.toQueryString</a>({colors: ['red', 'green', 'blue']}); // returns \"colors=red&amp;colors=green&amp;colors=blue\"\n</code></pre>\n\n<p>Recursive:</p>\n\n<pre><code><a href=\"#!/api/Ext.Object-method-toQueryString\" rel=\"Ext.Object-method-toQueryString\" class=\"docClass\">Ext.Object.toQueryString</a>({\n    username: 'Jacky',\n    dateOfBirth: {\n        day: 1,\n        month: 2,\n        year: 1911\n    },\n    hobbies: ['coding', 'eating', 'sleeping', ['nested', 'stuff']]\n}, true); // returns the following string (broken down and url-decoded for ease of reading purpose):\n// username=Jacky\n//    &amp;dateOfBirth[day]=1&amp;dateOfBirth[month]=2&amp;dateOfBirth[year]=1911\n//    &amp;hobbies[0]=coding&amp;hobbies[1]=eating&amp;hobbies[2]=sleeping&amp;hobbies[3][0]=nested&amp;hobbies[3][1]=stuff\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Use <a href=\"#!/api/Ext.Object-method-toQueryString\" rel=\"Ext.Object-method-toQueryString\" class=\"docClass\">Ext.Object.toQueryString</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>object</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object to encode</p>\n</div></li><li><span class='pre'>recursive</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>Whether or not to interpret the object in recursive format.\n(PHP / Ruby on Rails servers and similar).</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>queryString</p>\n</div></li></ul></div></div></div><div id='method-value' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-value' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-value' class='name expandable'>value</a>( <span class='pre'>value, defaultValue, [allowBlank]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Utility method for returning a default value if the passed value is empty. ...</div><div class='long'><p>Utility method for returning a default value if the passed value is empty.</p>\n\n<p>The value is deemed to be empty if it is:</p>\n\n<ul>\n<li>null</li>\n<li>undefined</li>\n<li>an empty array</li>\n<li>a zero length string (Unless the <code>allowBlank</code> parameter is <code>true</code>)</li>\n</ul>\n\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-valueFrom\" rel=\"Ext-method-valueFrom\" class=\"docClass\">valueFrom</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to test</p>\n</div></li><li><span class='pre'>defaultValue</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to return if the original value is empty</p>\n</div></li><li><span class='pre'>allowBlank</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>true to allow zero length strings to qualify as non-empty.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>value, if non-empty, else defaultValue</p>\n</div></li></ul></div></div></div><div id='method-valueFrom' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext.html#Ext-method-valueFrom' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-valueFrom' class='name expandable'>valueFrom</a>( <span class='pre'>value, defaultValue, [allowBlank]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Returns the given value itself if it's not empty, as described in isEmpty; returns the default\nvalue (second argument...</div><div class='long'><p>Returns the given value itself if it's not empty, as described in <a href=\"#!/api/Ext-method-isEmpty\" rel=\"Ext-method-isEmpty\" class=\"docClass\">isEmpty</a>; returns the default\nvalue (second argument) otherwise.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to test</p>\n</div></li><li><span class='pre'>defaultValue</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to return if the original value is empty</p>\n</div></li><li><span class='pre'>allowBlank</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>true to allow zero length strings to qualify as non-empty (defaults to false)</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>value, if non-empty, else defaultValue</p>\n</div></li></ul></div></div></div><div id='method-widget' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/ClassManager.html#Ext-method-widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-widget' class='name expandable'>widget</a>( <span class='pre'>[name], [config]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Convenient shorthand to create a widget by its xtype or a config object. ...</div><div class='long'><p>Convenient shorthand to create a widget by its xtype or a config object.\nSee also <a href=\"#!/api/Ext.ClassManager-method-instantiateByAlias\" rel=\"Ext.ClassManager-method-instantiateByAlias\" class=\"docClass\">Ext.ClassManager.instantiateByAlias</a>.</p>\n\n<pre><code> var button = <a href=\"#!/api/Ext-method-widget\" rel=\"Ext-method-widget\" class=\"docClass\">Ext.widget</a>('button'); // Equivalent to <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('widget.button');\n\n var panel = <a href=\"#!/api/Ext-method-widget\" rel=\"Ext-method-widget\" class=\"docClass\">Ext.widget</a>('panel', { // Equivalent to <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('widget.panel')\n     title: 'Panel'\n });\n\n var grid = <a href=\"#!/api/Ext-method-widget\" rel=\"Ext-method-widget\" class=\"docClass\">Ext.widget</a>({\n     xtype: 'grid',\n     ...\n });\n</code></pre>\n\n<p>If a <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">component</a> instance is passed, it is simply returned.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The xtype of the widget to create.</p>\n</div></li><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The configuration object for the widget constructor.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The widget instance</p>\n</div></li></ul></div></div></div><div id='method-zip' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext'>Ext</span><br/><a href='source/Ext-more.html#Ext-method-zip' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext-method-zip' class='name expandable'>zip</a>( <span class='pre'>arr, [zipper]</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Zips N sets together. ...</div><div class='long'><p>Zips N sets together.</p>\n\n<p>Example 1:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-zip\" rel=\"Ext-method-zip\" class=\"docClass\">Ext.zip</a>([1,2,3],[4,5,6]); // [[1,4],[2,5],[3,6]]\n</code></pre>\n\n<p>Example 2:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-zip\" rel=\"Ext-method-zip\" class=\"docClass\">Ext.zip</a>(\n    [ \"+\", \"-\", \"+\"],\n    [  12,  10,  22],\n    [  43,  15,  96],\n    function(a, b, c){\n        return \"$\" + a + \"\" + b + \".\" + c\n    }\n); // [\"$+12.43\", \"$-10.15\", \"$+22.96\"]\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Will be removed in the next major version</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>arr</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/NodeList...<div class='sub-desc'><p>This argument may be repeated. Array(s)\nto contribute values.</p>\n</div></li><li><span class='pre'>zipper</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The last item in the argument list.\nThis will drive how the items are zipped together.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>The zipped set.</p>\n</div></li></ul></div></div></div></div></div></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"Ext-more.html#Ext","filename":"Ext-more.js"},{"href":"Ext-more.html#Ext","filename":"Ext-more.js"},{"href":"Ext.html#Ext","filename":"Ext.js"},{"href":"Version.html#Ext","filename":"Version.js"}],"linenr":5,"members":{"property":[{"tagname":"property","owner":"Ext","meta":{},"name":"BLANK_IMAGE_URL","id":"property-BLANK_IMAGE_URL"},{"tagname":"property","owner":"Ext","meta":{"private":true},"name":"Logger","id":"property-Logger"},{"tagname":"property","owner":"Ext","meta":{},"name":"SSL_SECURE_URL","id":"property-SSL_SECURE_URL"},{"tagname":"property","owner":"Ext","meta":{},"name":"USE_NATIVE_JSON","id":"property-USE_NATIVE_JSON"},{"tagname":"property","owner":"Ext","meta":{},"name":"chromeVersion","id":"property-chromeVersion"},{"tagname":"property","owner":"Ext","meta":{},"name":"emptyFn","id":"property-emptyFn"},{"tagname":"property","owner":"Ext","meta":{},"name":"emptyString","id":"property-emptyString"},{"tagname":"property","owner":"Ext","meta":{},"name":"enableFx","id":"property-enableFx"},{"tagname":"property","owner":"Ext","meta":{},"name":"enableGarbageCollector","id":"property-enableGarbageCollector"},{"tagname":"property","owner":"Ext","meta":{},"name":"enableListenerCollection","id":"property-enableListenerCollection"},{"tagname":"property","owner":"Ext","meta":{},"name":"enableNestedListenerRemoval","id":"property-enableNestedListenerRemoval"},{"tagname":"property","owner":"Ext","meta":{},"name":"enumerables","id":"property-enumerables"},{"tagname":"property","owner":"Ext","meta":{},"name":"firefoxVersion","id":"property-firefoxVersion"},{"tagname":"property","owner":"Ext","meta":{"private":true},"name":"functionFactoryCache","id":"property-functionFactoryCache"},{"tagname":"property","owner":"Ext","meta":{},"name":"globalEvents","id":"property-globalEvents"},{"tagname":"property","owner":"Ext","meta":{},"name":"ieVersion","id":"property-ieVersion"},{"tagname":"property","owner":"Ext","meta":{},"name":"isChrome","id":"property-isChrome"},{"tagname":"property","owner":"Ext","meta":{},"name":"isFF10","id":"property-isFF10"},{"tagname":"property","owner":"Ext","meta":{},"name":"isFF3_0","id":"property-isFF3_0"},{"tagname":"property","owner":"Ext","meta":{},"name":"isFF3_5","id":"property-isFF3_5"},{"tagname":"property","owner":"Ext","meta":{},"name":"isFF3_6","id":"property-isFF3_6"},{"tagname":"property","owner":"Ext","meta":{},"name":"isFF4","id":"property-isFF4"},{"tagname":"property","owner":"Ext","meta":{},"name":"isFF5","id":"property-isFF5"},{"tagname":"property","owner":"Ext","meta":{},"name":"isGecko","id":"property-isGecko"},{"tagname":"property","owner":"Ext","meta":{},"name":"isGecko10","id":"property-isGecko10"},{"tagname":"property","owner":"Ext","meta":{},"name":"isGecko3","id":"property-isGecko3"},{"tagname":"property","owner":"Ext","meta":{},"name":"isGecko4","id":"property-isGecko4"},{"tagname":"property","owner":"Ext","meta":{},"name":"isGecko5","id":"property-isGecko5"},{"tagname":"property","owner":"Ext","meta":{},"name":"isIE","id":"property-isIE"},{"tagname":"property","owner":"Ext","meta":{},"name":"isIE10","id":"property-isIE10"},{"tagname":"property","owner":"Ext","meta":{},"name":"isIE10m","id":"property-isIE10m"},{"tagname":"property","owner":"Ext","meta":{},"name":"isIE10p","id":"property-isIE10p"},{"tagname":"property","owner":"Ext","meta":{},"name":"isIE6","id":"property-isIE6"},{"tagname":"property","owner":"Ext","meta":{},"name":"isIE7","id":"property-isIE7"},{"tagname":"property","owner":"Ext","meta":{},"name":"isIE7m","id":"property-isIE7m"},{"tagname":"property","owner":"Ext","meta":{},"name":"isIE7p","id":"property-isIE7p"},{"tagname":"property","owner":"Ext","meta":{},"name":"isIE8","id":"property-isIE8"},{"tagname":"property","owner":"Ext","meta":{},"name":"isIE8m","id":"property-isIE8m"},{"tagname":"property","owner":"Ext","meta":{},"name":"isIE8p","id":"property-isIE8p"},{"tagname":"property","owner":"Ext","meta":{},"name":"isIE9","id":"property-isIE9"},{"tagname":"property","owner":"Ext","meta":{},"name":"isIE9m","id":"property-isIE9m"},{"tagname":"property","owner":"Ext","meta":{},"name":"isIE9p","id":"property-isIE9p"},{"tagname":"property","owner":"Ext","meta":{},"name":"isLinux","id":"property-isLinux"},{"tagname":"property","owner":"Ext","meta":{},"name":"isMac","id":"property-isMac"},{"tagname":"property","owner":"Ext","meta":{},"name":"isOpera","id":"property-isOpera"},{"tagname":"property","owner":"Ext","meta":{},"name":"isOpera10_5","id":"property-isOpera10_5"},{"tagname":"property","owner":"Ext","meta":{},"name":"isReady","id":"property-isReady"},{"tagname":"property","owner":"Ext","meta":{},"name":"isSafari","id":"property-isSafari"},{"tagname":"property","owner":"Ext","meta":{},"name":"isSafari2","id":"property-isSafari2"},{"tagname":"property","owner":"Ext","meta":{},"name":"isSafari3","id":"property-isSafari3"},{"tagname":"property","owner":"Ext","meta":{},"name":"isSafari4","id":"property-isSafari4"},{"tagname":"property","owner":"Ext","meta":{},"name":"isSafari5","id":"property-isSafari5"},{"tagname":"property","owner":"Ext","meta":{},"name":"isSafari5_0","id":"property-isSafari5_0"},{"tagname":"property","owner":"Ext","meta":{},"name":"isSecure","id":"property-isSecure"},{"tagname":"property","owner":"Ext","meta":{},"name":"isWebKit","id":"property-isWebKit"},{"tagname":"property","owner":"Ext","meta":{},"name":"isWindows","id":"property-isWindows"},{"tagname":"property","owner":"Ext","meta":{"private":true},"name":"lastRegisteredVersion","id":"property-lastRegisteredVersion"},{"tagname":"property","owner":"Ext","meta":{"private":true},"name":"mergeIf","id":"property-mergeIf"},{"tagname":"property","owner":"Ext","meta":{},"name":"name","id":"property-name"},{"tagname":"property","owner":"Ext","meta":{},"name":"operaVersion","id":"property-operaVersion"},{"tagname":"property","owner":"Ext","meta":{"private":true},"name":"rootHierarchyState","id":"property-rootHierarchyState"},{"tagname":"property","owner":"Ext","meta":{},"name":"safariVersion","id":"property-safariVersion"},{"tagname":"property","owner":"Ext","meta":{},"name":"useShims","id":"property-useShims"},{"tagname":"property","owner":"Ext","meta":{"private":true},"name":"versions","id":"property-versions"},{"tagname":"property","owner":"Ext","meta":{},"name":"webKitVersion","id":"property-webKitVersion"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext","meta":{},"name":"addBehaviors","id":"method-addBehaviors"},{"tagname":"method","owner":"Ext","meta":{},"name":"addNamespaces","id":"method-addNamespaces"},{"tagname":"method","owner":"Ext","meta":{},"name":"application","id":"method-application"},{"tagname":"method","owner":"Ext","meta":{},"name":"apply","id":"method-apply"},{"tagname":"method","owner":"Ext","meta":{},"name":"applyIf","id":"method-applyIf"},{"tagname":"method","owner":"Ext","meta":{},"name":"batchLayouts","id":"method-batchLayouts"},{"tagname":"method","owner":"Ext","meta":{},"name":"bind","id":"method-bind"},{"tagname":"method","owner":"Ext","meta":{},"name":"callback","id":"method-callback"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext.Array#clean} instead","version":"4.0.0"}},"name":"clean","id":"method-clean"},{"tagname":"method","owner":"Ext","meta":{"private":true},"name":"clearNamespaces","id":"method-clearNamespaces"},{"tagname":"method","owner":"Ext","meta":{},"name":"clone","id":"method-clone"},{"tagname":"method","owner":"Ext","meta":{},"name":"coerce","id":"method-coerce"},{"tagname":"method","owner":"Ext","meta":{"private":true},"name":"collectNamespaces","id":"method-collectNamespaces"},{"tagname":"method","owner":"Ext","meta":{},"name":"copyTo","id":"method-copyTo"},{"tagname":"method","owner":"Ext","meta":{},"name":"create","id":"method-create"},{"tagname":"method","owner":"Ext","meta":{},"name":"createByAlias","id":"method-createByAlias"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext#widget} instead.","version":"4.0.0"}},"name":"createWidget","id":"method-createWidget"},{"tagname":"method","owner":"Ext","meta":{},"name":"decode","id":"method-decode"},{"tagname":"method","owner":"Ext","meta":{},"name":"defer","id":"method-defer"},{"tagname":"method","owner":"Ext","meta":{},"name":"define","id":"method-define"},{"tagname":"method","owner":"Ext","meta":{},"name":"deprecate","id":"method-deprecate"},{"tagname":"method","owner":"Ext","meta":{},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext","meta":{},"name":"destroyMembers","id":"method-destroyMembers"},{"tagname":"method","owner":"Ext","meta":{},"name":"each","id":"method-each"},{"tagname":"method","owner":"Ext","meta":{},"name":"encode","id":"method-encode"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext.String#escapeRegex} instead","version":"4.0.0"}},"name":"escapeRe","id":"method-escapeRe"},{"tagname":"method","owner":"Ext","meta":{},"name":"exclude","id":"method-exclude"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.0.0"}},"name":"extend","id":"method-extend"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext.Array#flatten} instead","version":"4.0.0"}},"name":"flatten","id":"method-flatten"},{"tagname":"method","owner":"Ext","meta":{},"name":"fly","id":"method-fly"},{"tagname":"method","owner":"Ext","meta":{},"name":"get","id":"method-get"},{"tagname":"method","owner":"Ext","meta":{},"name":"getBody","id":"method-getBody"},{"tagname":"method","owner":"Ext","meta":{},"name":"getClass","id":"method-getClass"},{"tagname":"method","owner":"Ext","meta":{},"name":"getClassName","id":"method-getClassName"},{"tagname":"method","owner":"Ext","meta":{},"name":"getCmp","id":"method-getCmp"},{"tagname":"method","owner":"Ext","meta":{"private":true},"name":"getDetachedBody","id":"method-getDetachedBody"},{"tagname":"method","owner":"Ext","meta":{},"name":"getDoc","id":"method-getDoc"},{"tagname":"method","owner":"Ext","meta":{},"name":"getDom","id":"method-getDom"},{"tagname":"method","owner":"Ext","meta":{"private":true},"name":"getElementById","id":"method-getElementById"},{"tagname":"method","owner":"Ext","meta":{},"name":"getHead","id":"method-getHead"},{"tagname":"method","owner":"Ext","meta":{},"name":"getNamespace","id":"method-getNamespace"},{"tagname":"method","owner":"Ext","meta":{},"name":"getOrientation","id":"method-getOrientation"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":""}},"name":"getScrollBarWidth","id":"method-getScrollBarWidth"},{"tagname":"method","owner":"Ext","meta":{},"name":"getScrollbarSize","id":"method-getScrollbarSize"},{"tagname":"method","owner":"Ext","meta":{},"name":"getStore","id":"method-getStore"},{"tagname":"method","owner":"Ext","meta":{"private":true},"name":"getUniqueGlobalNamespace","id":"method-getUniqueGlobalNamespace"},{"tagname":"method","owner":"Ext","meta":{},"name":"getVersion","id":"method-getVersion"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext.String#htmlDecode} instead"}},"name":"htmlDecode","id":"method-htmlDecode"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext.String#htmlEncode} instead"}},"name":"htmlEncode","id":"method-htmlEncode"},{"tagname":"method","owner":"Ext","meta":{},"name":"id","id":"method-id"},{"tagname":"method","owner":"Ext","meta":{},"name":"identityFn","id":"method-identityFn"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Will be removed in the next major version","version":"4.0.0"}},"name":"invoke","id":"method-invoke"},{"tagname":"method","owner":"Ext","meta":{},"name":"isArray","id":"method-isArray"},{"tagname":"method","owner":"Ext","meta":{},"name":"isBoolean","id":"method-isBoolean"},{"tagname":"method","owner":"Ext","meta":{},"name":"isDate","id":"method-isDate"},{"tagname":"method","owner":"Ext","meta":{},"name":"isDefined","id":"method-isDefined"},{"tagname":"method","owner":"Ext","meta":{},"name":"isElement","id":"method-isElement"},{"tagname":"method","owner":"Ext","meta":{"markdown":true},"name":"isEmpty","id":"method-isEmpty"},{"tagname":"method","owner":"Ext","meta":{},"name":"isFunction","id":"method-isFunction"},{"tagname":"method","owner":"Ext","meta":{},"name":"isIterable","id":"method-isIterable"},{"tagname":"method","owner":"Ext","meta":{},"name":"isNumber","id":"method-isNumber"},{"tagname":"method","owner":"Ext","meta":{},"name":"isNumeric","id":"method-isNumeric"},{"tagname":"method","owner":"Ext","meta":{},"name":"isObject","id":"method-isObject"},{"tagname":"method","owner":"Ext","meta":{},"name":"isPrimitive","id":"method-isPrimitive"},{"tagname":"method","owner":"Ext","meta":{"private":true},"name":"isSimpleObject","id":"method-isSimpleObject"},{"tagname":"method","owner":"Ext","meta":{},"name":"isString","id":"method-isString"},{"tagname":"method","owner":"Ext","meta":{},"name":"isTextNode","id":"method-isTextNode"},{"tagname":"method","owner":"Ext","meta":{"markdown":true},"name":"iterate","id":"method-iterate"},{"tagname":"method","owner":"Ext","meta":{},"name":"log","id":"method-log"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext.Array#max} instead","version":"4.0.0"}},"name":"max","id":"method-max"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext.Array#mean} instead","version":"4.0.0"}},"name":"mean","id":"method-mean"},{"tagname":"method","owner":"Ext","meta":{},"name":"merge","id":"method-merge"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext.Array#min} instead","version":"4.0.0"}},"name":"min","id":"method-min"},{"tagname":"method","owner":"Ext","meta":{},"name":"namespace","id":"method-namespace"},{"tagname":"method","owner":"Ext","meta":{},"name":"ns","id":"method-ns"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Please use {@link Ext.Number#from} instead.","version":"4.0.0"}},"name":"num","id":"method-num"},{"tagname":"method","owner":"Ext","meta":{},"name":"on","id":"method-on"},{"tagname":"method","owner":"Ext","meta":{},"name":"onDocumentReady","id":"method-onDocumentReady"},{"tagname":"method","owner":"Ext","meta":{},"name":"onReady","id":"method-onReady"},{"tagname":"method","owner":"Ext","meta":{},"name":"override","id":"method-override"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Will be removed in the next major version","version":"4.0.0"}},"name":"partition","id":"method-partition"},{"tagname":"method","owner":"Ext","meta":{},"name":"pass","id":"method-pass"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext.Array#pluck Ext.Array.pluck} instead","version":"4.0.0"}},"name":"pluck","id":"method-pluck"},{"tagname":"method","owner":"Ext","meta":{},"name":"preg","id":"method-preg"},{"tagname":"method","owner":"Ext","meta":{},"name":"query","id":"method-query"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext#define} instead.","version":"4.0.0"}},"name":"regModel","id":"method-regModel"},{"tagname":"method","owner":"Ext","meta":{},"name":"regStore","id":"method-regStore"},{"tagname":"method","owner":"Ext","meta":{},"name":"removeNode","id":"method-removeNode"},{"tagname":"method","owner":"Ext","meta":{},"name":"require","id":"method-require"},{"tagname":"method","owner":"Ext","meta":{"private":true},"name":"resolveMethod","id":"method-resolveMethod"},{"tagname":"method","owner":"Ext","meta":{},"name":"resumeLayouts","id":"method-resumeLayouts"},{"tagname":"method","owner":"Ext","meta":{},"name":"select","id":"method-select"},{"tagname":"method","owner":"Ext","meta":{},"name":"setGlyphFontFamily","id":"method-setGlyphFontFamily"},{"tagname":"method","owner":"Ext","meta":{"chainable":true},"name":"setVersion","id":"method-setVersion"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext.Array#sum} instead","version":"4.0.0"}},"name":"sum","id":"method-sum"},{"tagname":"method","owner":"Ext","meta":{},"name":"suspendLayouts","id":"method-suspendLayouts"},{"tagname":"method","owner":"Ext","meta":{},"name":"syncRequire","id":"method-syncRequire"},{"tagname":"method","owner":"Ext","meta":{},"name":"toArray","id":"method-toArray"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Will be removed in the next major version","version":"4.0.0"}},"name":"toSentence","id":"method-toSentence"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext#typeOf} instead","version":"4.0.0"}},"name":"type","id":"method-type"},{"tagname":"method","owner":"Ext","meta":{"markdown":true},"name":"typeOf","id":"method-typeOf"},{"tagname":"method","owner":"Ext","meta":{},"name":"un","id":"method-un"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext.Array#unique} instead","version":"4.0.0"}},"name":"unique","id":"method-unique"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext.String#urlAppend} instead"}},"name":"urlAppend","id":"method-urlAppend"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext.Object#fromQueryString} instead","version":"4.0.0"}},"name":"urlDecode","id":"method-urlDecode"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext.Object#toQueryString} instead","version":"4.0.0"}},"name":"urlEncode","id":"method-urlEncode"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Use {@link Ext#valueFrom} instead","version":"4.0.0"}},"name":"value","id":"method-value"},{"tagname":"method","owner":"Ext","meta":{},"name":"valueFrom","id":"method-valueFrom"},{"tagname":"method","owner":"Ext","meta":{},"name":"widget","id":"method-widget"},{"tagname":"method","owner":"Ext","meta":{"deprecated":{"text":"Will be removed in the next major version","version":"4.0.0"}},"name":"zip","id":"method-zip"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext","singleton":true,"override":null,"inheritdoc":null,"id":"class-Ext","mixins":[],"mixedInto":[]});