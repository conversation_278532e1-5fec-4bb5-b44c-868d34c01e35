Ext.data.JsonP.Ext_data_AbstractStore({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":["Ext.data.Store","Ext.data.TreeStore"],"extends":"Ext.Base","uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><strong>Ext.data.AbstractStore</strong></div></div><h4>Mixins</h4><div class='dependency'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='docClass'>Ext.util.Observable</a></div><div class='dependency'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='docClass'>Ext.util.Sortable</a></div><h4>Requires</h4><div class='dependency'><a href='#!/api/Ext.data.Operation' rel='Ext.data.Operation' class='docClass'>Ext.data.Operation</a></div><div class='dependency'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='docClass'>Ext.data.proxy.Proxy</a></div><div class='dependency'><a href='#!/api/Ext.util.Filter' rel='Ext.util.Filter' class='docClass'>Ext.util.Filter</a></div><div class='dependency'><a href='#!/api/Ext.util.MixedCollection' rel='Ext.util.MixedCollection' class='docClass'>Ext.util.MixedCollection</a></div><h4>Subclasses</h4><div class='dependency'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='docClass'>Ext.data.Store</a></div><div class='dependency'><a href='#!/api/Ext.data.TreeStore' rel='Ext.data.TreeStore' class='docClass'>Ext.data.TreeStore</a></div><h4>Files</h4><div class='dependency'><a href='source/AbstractStore.html#Ext-data-AbstractStore' target='_blank'>AbstractStore.js</a></div></pre><div class='doc-contents'><p>AbstractStore is a superclass of <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a> and <a href=\"#!/api/Ext.data.TreeStore\" rel=\"Ext.data.TreeStore\" class=\"docClass\">Ext.data.TreeStore</a>. It's never used directly,\nbut offers a set of methods used by both of those subclasses.</p>\n\n<p>We've left it here in the docs for reference purposes, but unless you need to make a whole new type of Store, what\nyou're probably looking for is <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a>. If you're still interested, here's a brief description of what\nAbstractStore is and is not.</p>\n\n<p>AbstractStore provides the basic configuration for anything that can be considered a Store. It expects to be\ngiven a <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Model</a> that represents the type of data in the Store. It also expects to be given a\n<a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Proxy</a> that handles the loading of data into the Store.</p>\n\n<p>AbstractStore provides a few helpful methods such as <a href=\"#!/api/Ext.data.AbstractStore-method-load\" rel=\"Ext.data.AbstractStore-method-load\" class=\"docClass\">load</a> and <a href=\"#!/api/Ext.data.AbstractStore-method-sync\" rel=\"Ext.data.AbstractStore-method-sync\" class=\"docClass\">sync</a>, which load and save data\nrespectively, passing the requests through the configured <a href=\"#!/api/Ext.data.AbstractStore-cfg-proxy\" rel=\"Ext.data.AbstractStore-cfg-proxy\" class=\"docClass\">proxy</a>. Both built-in Store subclasses add extra\nbehavior to each of these functions. Note also that each AbstractStore subclass has its own way of storing data -\nin <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a> the data is saved as a flat <a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">MixedCollection</a>, whereas in\n<a href=\"#!/api/Ext.data.TreeStore\" rel=\"Ext.data.TreeStore\" class=\"docClass\">TreeStore</a> we use a <a href=\"#!/api/Ext.data.Tree\" rel=\"Ext.data.Tree\" class=\"docClass\">Ext.data.Tree</a> to maintain the data's hierarchy.</p>\n\n<p>The store provides filtering and sorting support. This sorting/filtering can happen on the client side\nor can be completed on the server. This is controlled by the <a href=\"#!/api/Ext.data.Store-cfg-remoteSort\" rel=\"Ext.data.Store-cfg-remoteSort\" class=\"docClass\">remoteSort</a> and\n<a href=\"#!/api/Ext.data.Store-cfg-remoteFilter\" rel=\"Ext.data.Store-cfg-remoteFilter\" class=\"docClass\">remoteFilter</a> config options. For more information see the <a href=\"#!/api/Ext.data.AbstractStore-method-sort\" rel=\"Ext.data.AbstractStore-method-sort\" class=\"docClass\">sort</a> and\n<a href=\"#!/api/Ext.data.Store-method-filter\" rel=\"Ext.data.Store-method-filter\" class=\"docClass\">filter</a> methods.</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-cfg'>Config options</h3><div class='subsection'><div id='cfg-autoLoad' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-autoLoad' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-autoLoad' class='name expandable'>autoLoad</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>If data is not specified, and if autoLoad is true or an Object, this store's load method is automatically called\nafte...</div><div class='long'><p>If data is not specified, and if autoLoad is true or an Object, this store's load method is automatically called\nafter creation. If the value of autoLoad is an Object, this Object will be passed to the store's load method.</p>\n        <p>Available since: <b>2.3.0</b></p>\n</div></div></div><div id='cfg-autoSync' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-autoSync' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-autoSync' class='name expandable'>autoSync</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True to automatically sync the Store with its Proxy after every edit to one of its Records. ...</div><div class='long'><p>True to automatically sync the Store with its Proxy after every edit to one of its Records. Defaults to false.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-batchUpdateMode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-batchUpdateMode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-batchUpdateMode' class='name expandable'>batchUpdateMode</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Sets the updating behavior based on batch synchronization. ...</div><div class='long'><p>Sets the updating behavior based on batch synchronization. 'operation' (the default) will update the Store's\ninternal representation of the data after each operation of the batch has completed, 'complete' will wait until\nthe entire batch has been completed before updating the Store's data. 'complete' is a good choice for local\nstorage proxies, 'operation' is better for remote proxies, where there is a comparatively high latency.</p>\n<p>Defaults to: <code>'operation'</code></p></div></div></div><div id='cfg-defaultSortDirection' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-cfg-defaultSortDirection' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-cfg-defaultSortDirection' class='name expandable'>defaultSortDirection</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The default sort direction to use if one is not specified. ...</div><div class='long'><p>The default sort direction to use if one is not specified.</p>\n<p>Defaults to: <code>&quot;ASC&quot;</code></p></div></div></div><div id='cfg-fields' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-fields' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-fields' class='name expandable'>fields</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]</span></div><div class='description'><div class='short'>This may be used in place of specifying a model configuration. ...</div><div class='long'><p>This may be used in place of specifying a <a href=\"#!/api/Ext.data.AbstractStore-cfg-model\" rel=\"Ext.data.AbstractStore-cfg-model\" class=\"docClass\">model</a> configuration. The fields should be a\nset of <a href=\"#!/api/Ext.data.Field\" rel=\"Ext.data.Field\" class=\"docClass\">Ext.data.Field</a> configuration objects. The store will automatically create a <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>\nwith these fields. In general this configuration option should only be used for simple stores like\na two-field store of ComboBox. For anything more complicated, such as specifying a particular id property or\nassociations, a <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a> should be defined and specified for the <a href=\"#!/api/Ext.data.AbstractStore-cfg-model\" rel=\"Ext.data.AbstractStore-cfg-model\" class=\"docClass\">model</a>\nconfig.</p>\n        <p>Available since: <b>2.3.0</b></p>\n</div></div></div><div id='cfg-filterOnLoad' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-filterOnLoad' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-filterOnLoad' class='name expandable'>filterOnLoad</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>If true, any filters attached to this Store will be run after loading data, before the datachanged event is fired. ...</div><div class='long'><p>If true, any filters attached to this Store will be run after loading data, before the datachanged event is fired.\nDefaults to true, ignored if <a href=\"#!/api/Ext.data.Store-cfg-remoteFilter\" rel=\"Ext.data.Store-cfg-remoteFilter\" class=\"docClass\">remoteFilter</a> is true</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-filters' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-filters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-filters' class='name expandable'>filters</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]/<a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a>[]</span></div><div class='description'><div class='short'>Array of Filters for this store. ...</div><div class='long'><p>Array of <a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Filters</a> for this store. Can also be passed array of\nfunctions which will be used as the <a href=\"#!/api/Ext.util.Filter-cfg-filterFn\" rel=\"Ext.util.Filter-cfg-filterFn\" class=\"docClass\">filterFn</a> config\nfor filters:</p>\n\n<pre><code>filters: [\n    function(item) {\n        return item.weight &gt; 0;\n    }\n]\n</code></pre>\n\n<p>To filter after the grid is loaded use the <a href=\"#!/api/Ext.data.Store-method-filterBy\" rel=\"Ext.data.Store-method-filterBy\" class=\"docClass\">filterBy</a> function.</p>\n</div></div></div><div id='cfg-listeners' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-cfg-listeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-cfg-listeners' class='name expandable'>listeners</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>A config object containing one or more event handlers to be added to this object during initialization. ...</div><div class='long'><p>A config object containing one or more event handlers to be added to this object during initialization. This\nshould be a valid listeners config object as specified in the <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> example for attaching multiple\nhandlers at once.</p>\n\n<p><strong>DOM events from Ext JS <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a></strong></p>\n\n<p>While <em>some</em> Ext JS Component classes export selected DOM events (e.g. \"click\", \"mouseover\" etc), this is usually\nonly done when extra value can be added. For example the <a href=\"#!/api/Ext.view.View\" rel=\"Ext.view.View\" class=\"docClass\">DataView</a>'s <strong><code><a href=\"#!/api/Ext.view.View-event-itemclick\" rel=\"Ext.view.View-event-itemclick\" class=\"docClass\">itemclick</a></code></strong> event passing the node clicked on. To access DOM events directly from a\nchild element of a Component, we need to specify the <code>element</code> option to identify the Component property to add a\nDOM listener to:</p>\n\n<pre><code>new <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>({\n    width: 400,\n    height: 200,\n    dockedItems: [{\n        xtype: 'toolbar'\n    }],\n    listeners: {\n        click: {\n            element: 'el', //bind to the underlying el property on the panel\n            fn: function(){ console.log('click el'); }\n        },\n        dblclick: {\n            element: 'body', //bind to the underlying body property on the panel\n            fn: function(){ console.log('dblclick body'); }\n        }\n    }\n});\n</code></pre>\n</div></div></div><div id='cfg-model' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-model' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-model' class='name expandable'>model</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Name of the Model associated with this store. ...</div><div class='long'><p>Name of the <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Model</a> associated with this store.\nThe string is used as an argument for <a href=\"#!/api/Ext.ModelManager-method-getModel\" rel=\"Ext.ModelManager-method-getModel\" class=\"docClass\">Ext.ModelManager.getModel</a>.</p>\n</div></div></div><div id='cfg-proxy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-proxy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-proxy' class='name expandable'>proxy</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>The Proxy to use for this Store. ...</div><div class='long'><p>The Proxy to use for this Store. This can be either a string, a config object or a Proxy instance -\nsee <a href=\"#!/api/Ext.data.AbstractStore-method-setProxy\" rel=\"Ext.data.AbstractStore-method-setProxy\" class=\"docClass\">setProxy</a> for details.</p>\n        <p>Available since: <b>1.1.0</b></p>\n</div></div></div><div id='cfg-remoteFilter' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-remoteFilter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-remoteFilter' class='name expandable'>remoteFilter</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True to defer any filtering operation to the server. ...</div><div class='long'><p>True to defer any filtering operation to the server. If false, filtering is done locally on the client.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-remoteSort' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-remoteSort' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-remoteSort' class='name expandable'>remoteSort</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True to defer any sorting operation to the server. ...</div><div class='long'><p>True to defer any sorting operation to the server. If false, sorting is done locally on the client.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-sortOnLoad' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-sortOnLoad' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-sortOnLoad' class='name expandable'>sortOnLoad</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>If true, any sorters attached to this Store will be run after loading data, before the datachanged event is fired. ...</div><div class='long'><p>If true, any sorters attached to this Store will be run after loading data, before the datachanged event is fired.\nDefaults to true, igored if <a href=\"#!/api/Ext.data.Store-cfg-remoteSort\" rel=\"Ext.data.Store-cfg-remoteSort\" class=\"docClass\">remoteSort</a> is true</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-sortRoot' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-cfg-sortRoot' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-cfg-sortRoot' class='name not-expandable'>sortRoot</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'><p>The property in each item that contains the data to sort.</p>\n</div><div class='long'><p>The property in each item that contains the data to sort.</p>\n</div></div></div><div id='cfg-sorters' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-cfg-sorters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-cfg-sorters' class='name not-expandable'>sorters</a><span> : <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a>[]/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]</span></div><div class='description'><div class='short'><p>The initial set of <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Sorters</a></p>\n</div><div class='long'><p>The initial set of <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Sorters</a></p>\n</div></div></div><div id='cfg-statefulFilters' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-statefulFilters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-statefulFilters' class='name expandable'>statefulFilters</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Configure as true to have the filters saved when a client grid saves its state. ...</div><div class='long'><p>Configure as <code>true</code> to have the filters saved when a client <a href=\"#!/api/Ext.grid.Panel\" rel=\"Ext.grid.Panel\" class=\"docClass\">grid</a> saves its state.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-storeId' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-storeId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-storeId' class='name expandable'>storeId</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Unique identifier for this store. ...</div><div class='long'><p>Unique identifier for this store. If present, this Store will be registered with the <a href=\"#!/api/Ext.data.StoreManager\" rel=\"Ext.data.StoreManager\" class=\"docClass\">Ext.data.StoreManager</a>,\nmaking it easy to reuse elsewhere.</p>\n\n<p>Note that when store is instatiated by Controller, the storeId will be overridden by the name of the store.</p>\n</div></div></div></div></div><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-defaultProxyType' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-property-defaultProxyType' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-property-defaultProxyType' class='name expandable'>defaultProxyType</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The string type of the Proxy to create if none is specified. ...</div><div class='long'><p>The string type of the Proxy to create if none is specified. This defaults to creating a\n<a href=\"#!/api/Ext.data.proxy.Memory\" rel=\"Ext.data.proxy.Memory\" class=\"docClass\">memory proxy</a>.</p>\n<p>Defaults to: <code>'memory'</code></p></div></div></div><div id='property-eventsSuspended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-property-eventsSuspended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-property-eventsSuspended' class='name expandable'>eventsSuspended</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Initial suspended call count. ...</div><div class='long'><p>Initial suspended call count. Incremented when <a href=\"#!/api/Ext.util.Observable-method-suspendEvents\" rel=\"Ext.util.Observable-method-suspendEvents\" class=\"docClass\">suspendEvents</a> is called, decremented when <a href=\"#!/api/Ext.util.Observable-method-resumeEvents\" rel=\"Ext.util.Observable-method-resumeEvents\" class=\"docClass\">resumeEvents</a> is called.</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-filters' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-property-filters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-property-filters' class='name not-expandable'>filters</a><span> : <a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">Ext.util.MixedCollection</a></span></div><div class='description'><div class='short'><p>The collection of <a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Filters</a> currently applied to this Store</p>\n</div><div class='long'><p>The collection of <a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Filters</a> currently applied to this Store</p>\n</div></div></div><div id='property-hasListeners' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-property-hasListeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-property-hasListeners' class='name expandable'>hasListeners</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='readonly signature' >readonly</strong></div><div class='description'><div class='short'>This object holds a key for any event that has a listener. ...</div><div class='long'><p>This object holds a key for any event that has a listener. The listener may be set\ndirectly on the instance, or on its class or a super class (via <a href=\"#!/api/Ext.util.Observable-static-method-observe\" rel=\"Ext.util.Observable-static-method-observe\" class=\"docClass\">observe</a>) or\non the <a href=\"#!/api/Ext.app.EventBus\" rel=\"Ext.app.EventBus\" class=\"docClass\">MVC EventBus</a>. The values of this object are truthy\n(a non-zero number) and falsy (0 or undefined). They do not represent an exact count\nof listeners. The value for an event is truthy if the event must be fired and is\nfalsy if there is no need to fire the event.</p>\n\n<p>The intended use of this property is to avoid the expense of fireEvent calls when\nthere are no listeners. This can be particularly helpful when one would otherwise\nhave to call fireEvent hundreds or thousands of times. It is used like this:</p>\n\n<pre><code> if (this.hasListeners.foo) {\n     this.fireEvent('foo', this, arg1);\n }\n</code></pre>\n</div></div></div><div id='property-implicitModel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-property-implicitModel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-property-implicitModel' class='name expandable'>implicitModel</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>True if a model was created implicitly for this Store. ...</div><div class='long'><p>True if a model was created implicitly for this Store. This happens if a fields array is passed to the Store's\nconstructor instead of a model constructor or name.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-isDestroyed' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-property-isDestroyed' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-property-isDestroyed' class='name expandable'>isDestroyed</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True if the Store has already been destroyed. ...</div><div class='long'><p>True if the Store has already been destroyed. If this is true, the reference to Store should be deleted\nas it will not function correctly any more.</p>\n<p>Defaults to: <code>false</code></p>        <p>Available since: <b>3.4.0</b></p>\n</div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isObservable' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-property-isObservable' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-property-isObservable' class='name expandable'>isObservable</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true in this class to identify an object as an instantiated Observable, or subclass thereof. ...</div><div class='long'><p><code>true</code> in this class to identify an object as an instantiated Observable, or subclass thereof.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isSortable' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-property-isSortable' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-property-isSortable' class='name expandable'>isSortable</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true in this class to identify an object as an instantiated Sortable, or subclass thereof. ...</div><div class='long'><p><code>true</code> in this class to identify an object as an instantiated Sortable, or subclass thereof.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isStore' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-property-isStore' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-property-isStore' class='name expandable'>isStore</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true in this class to identify an object as an instantiated Store, or subclass thereof. ...</div><div class='long'><p><code>true</code> in this class to identify an object as an instantiated Store, or subclass thereof.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-modelDefaults' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-property-modelDefaults' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-property-modelDefaults' class='name expandable'>modelDefaults</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>A set of default values to be applied to every model instance added via insert or created\nvia createModel. ...</div><div class='long'><p>A set of default values to be applied to every model instance added via <a href=\"#!/api/Ext.data.Store-method-insert\" rel=\"Ext.data.Store-method-insert\" class=\"docClass\">insert</a> or created\nvia <a href=\"#!/api/Ext.data.Store-method-createModel\" rel=\"Ext.data.Store-method-createModel\" class=\"docClass\">createModel</a>. This is used internally by associations to set foreign keys and\nother fields. See the Association classes source code for examples. This should not need to be used by application developers.</p>\n</div></div></div><div id='property-removed' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-property-removed' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-property-removed' class='name expandable'>removed</a><span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Temporary cache in which removed model instances are kept until successfully synchronised with a Proxy,\nat which poin...</div><div class='long'><p>Temporary cache in which removed model instances are kept until successfully synchronised with a Proxy,\nat which point this is cleared.</p>\n</div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div><div id='property-sorters' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-property-sorters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-property-sorters' class='name not-expandable'>sorters</a><span> : <a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">Ext.util.MixedCollection</a></span></div><div class='description'><div class='short'><p>The collection of <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Sorters</a> currently applied to this Store</p>\n</div><div class='long'><p>The collection of <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Sorters</a> currently applied to this Store</p>\n</div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/Ext.data.AbstractStore-method-constructor' class='name expandable'>Ext.data.AbstractStore</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.data.AbstractStore\" rel=\"Ext.data.AbstractStore\" class=\"docClass\">Ext.data.AbstractStore</a></div><div class='description'><div class='short'>documented above ...</div><div class='long'><p>documented above</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.AbstractStore\" rel=\"Ext.data.AbstractStore\" class=\"docClass\">Ext.data.AbstractStore</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-addEvents' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-addEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-addEvents' class='name expandable'>addEvents</a>( <span class='pre'>eventNames</span> )</div><div class='description'><div class='short'>Adds the specified events to the list of events which this Observable may fire. ...</div><div class='long'><p>Adds the specified events to the list of events which this Observable may fire.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventNames</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>...<div class='sub-desc'><p>Either an object with event names as properties with\na value of <code>true</code>. For example:</p>\n\n<pre><code>this.addEvents({\n    storeloaded: true,\n    storecleared: true\n});\n</code></pre>\n\n<p>Or any number of event names as separate parameters. For example:</p>\n\n<pre><code>this.addEvents('storeloaded', 'storecleared');\n</code></pre>\n</div></li></ul></div></div></div><div id='method-addListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-addListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-addListener' class='name expandable'>addListener</a>( <span class='pre'>eventName, [fn], [scope], [options]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Appends an event handler to this object. ...</div><div class='long'><p>Appends an event handler to this object.  For example:</p>\n\n<pre><code>myGridPanel.on(\"mouseover\", this.onMouseOver, this);\n</code></pre>\n\n<p>The method also allows for a single argument to be passed which is a config object\ncontaining properties which specify multiple events. For example:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: this.onCellClick,\n    mouseover: this.onMouseOver,\n    mouseout: this.onMouseOut,\n    scope: this // Important. Ensure \"this\" is correct during handler execution\n});\n</code></pre>\n\n<p>One can also specify options for each event handler separately:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: this.onCellClick, scope: this, single: true},\n    mouseover: {fn: panel.onMouseOver, scope: panel}\n});\n</code></pre>\n\n<p><em>Names</em> of methods in a specified scope may also be used. Note that\n<code>scope</code> MUST be specified to use this option:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: 'onCellClick', scope: this, single: true},\n    mouseover: {fn: 'onMouseOver', scope: panel}\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The name of the event to listen for.\nMay also be an object who's property names are event names.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The method the event invokes, or <em>if <code>scope</code> is specified, the </em>name* of the method within\nthe specified <code>scope</code>.  Will be called with arguments\ngiven to <a href=\"#!/api/Ext.util.Observable-method-fireEvent\" rel=\"Ext.util.Observable-method-fireEvent\" class=\"docClass\">fireEvent</a> plus the <code>options</code> parameter described below.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is\nexecuted. <strong>If omitted, defaults to the object which fired the event.</strong></p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>An object containing handler configuration.</p>\n\n\n\n\n<p><strong>Note:</strong> Unlike in ExtJS 3.x, the options object will also be passed as the last\nargument to every event handler.</p>\n\n\n\n\n<p>This object may contain any of the following properties:</p>\n\n<ul><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is executed. <strong>If omitted,\n  defaults to the object which fired the event.</strong></p>\n\n</div></li><li><span class='pre'>delay</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of milliseconds to delay the invocation of the handler after the event fires.</p>\n\n</div></li><li><span class='pre'>single</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to add a handler to handle just the next firing of the event, and then remove itself.</p>\n\n</div></li><li><span class='pre'>buffer</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Causes the handler to be scheduled to run in an <a href=\"#!/api/Ext.util.DelayedTask\" rel=\"Ext.util.DelayedTask\" class=\"docClass\">Ext.util.DelayedTask</a> delayed\n  by the specified number of milliseconds. If the event fires again within that time,\n  the original handler is <em>not</em> invoked, but the new handler is scheduled in its place.</p>\n\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a><div class='sub-desc'><p>Only call the handler if the event was fired on the target Observable, <em>not</em> if the event\n  was bubbled up from a child Observable.</p>\n\n</div></li><li><span class='pre'>element</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p><strong>This option is only valid for listeners bound to <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a>.</strong>\n  The name of a Component property which references an element to add a listener to.</p>\n\n\n\n\n<p>  This option is useful during Component construction to add DOM event listeners to elements of\n  <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a> which will exist only after the Component is rendered.\n  For example, to add a click listener to a Panel's body:</p>\n\n\n\n\n<pre><code>  new <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>({\n      title: 'The title',\n      listeners: {\n          click: this.handlePanelClick,\n          element: 'body'\n      }\n  });\n</code></pre>\n\n</div></li><li><span class='pre'>destroyable</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>When specified as <code>true</code>, the function returns A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call.</p>\n\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>priority</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>An optional numeric priority that determines the order in which event handlers\n  are run. Event handlers with no priority will be run as if they had a priority\n  of 0. Handlers with a higher priority will be prioritized to run sooner than\n  those with a lower priority.  Negative numbers can be used to set a priority\n  lower than the default. Internally, the framework uses a range of 1000 or\n  greater, and -1000 or lesser for handers that are intended to run before or\n  after all others, so it is recommended to stay within the range of -999 to 999\n  when setting the priority of event handlers in application-level code.</p>\n\n\n\n\n<p><strong>Combining Options</strong></p>\n\n\n\n\n<p>Using the options argument, it is possible to combine different types of listeners:</p>\n\n\n\n\n<p>A delayed, one-time listener.</p>\n\n\n\n\n<pre><code>myPanel.on('hide', this.handleClick, this, {\n    single: true,\n    delay: 100\n});\n</code></pre>\n\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><strong>Only when the <code>destroyable</code> option is specified. </strong></p>\n\n\n\n\n<p> A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call. For example:</p>\n\n\n\n\n<pre><code>this.btnListeners =  = myButton.on({\n    destroyable: true\n    mouseover:   function() { console.log('mouseover'); },\n    mouseout:    function() { console.log('mouseout'); },\n    click:       function() { console.log('click'); }\n});\n</code></pre>\n\n\n\n\n<p>And when those listeners need to be removed:</p>\n\n\n\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.btnListeners);\n</code></pre>\n\n\n\n\n<p>or</p>\n\n\n\n\n<pre><code>this.btnListeners.destroy();\n</code></pre>\n\n</div></li></ul></div></div></div><div id='method-addManagedListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-addManagedListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-addManagedListener' class='name expandable'>addManagedListener</a>( <span class='pre'>item, ename, [fn], [scope], [options]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Adds listeners to any Observable object (or Ext.Element) which are automatically removed when this Component is\ndestr...</div><div class='long'><p>Adds listeners to any Observable object (or <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>) which are automatically removed when this Component is\ndestroyed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The item to which to add a listener/listeners.</p>\n\n</div></li><li><span class='pre'>ename</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The event name, or an object containing event name properties.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the handler function.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the scope (<code>this</code> reference)\nin which the handler function is executed.</p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the\n<a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> options.</p>\n\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><strong>Only when the <code>destroyable</code> option is specified. </strong></p>\n\n\n\n\n<p> A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call. For example:</p>\n\n\n\n\n<pre><code>this.btnListeners =  = myButton.mon({\n    destroyable: true\n    mouseover:   function() { console.log('mouseover'); },\n    mouseout:    function() { console.log('mouseout'); },\n    click:       function() { console.log('click'); }\n});\n</code></pre>\n\n\n\n\n<p>And when those listeners need to be removed:</p>\n\n\n\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.btnListeners);\n</code></pre>\n\n\n\n\n<p>or</p>\n\n\n\n\n<pre><code>this.btnListeners.destroy();\n</code></pre>\n\n</div></li></ul></div></div></div><div id='method-afterCommit' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-afterCommit' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-afterCommit' class='name expandable'>afterCommit</a>( <span class='pre'>record</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>A model instance should call this method on the Store it has been joined to. ...</div><div class='long'><p>A model instance should call this method on the Store it has been <a href=\"#!/api/Ext.data.Model-method-join\" rel=\"Ext.data.Model-method-join\" class=\"docClass\">joined</a> to.</p>\n        <p>Available since: <b>3.4.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>record</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The model instance that was edited</p>\n</div></li></ul></div></div></div><div id='method-afterEdit' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-afterEdit' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-afterEdit' class='name expandable'>afterEdit</a>( <span class='pre'>record, modifiedFieldNames</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>A model instance should call this method on the Store it has been joined to. ...</div><div class='long'><p>A model instance should call this method on the Store it has been <a href=\"#!/api/Ext.data.Model-method-join\" rel=\"Ext.data.Model-method-join\" class=\"docClass\">joined</a> to.</p>\n        <p>Available since: <b>3.4.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>record</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The model instance that was edited</p>\n</div></li><li><span class='pre'>modifiedFieldNames</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>Array of field names changed during edit.</p>\n</div></li></ul></div></div></div><div id='method-afterReject' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-afterReject' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-afterReject' class='name expandable'>afterReject</a>( <span class='pre'>record</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>A model instance should call this method on the Store it has been joined to.. ...</div><div class='long'><p>A model instance should call this method on the Store it has been <a href=\"#!/api/Ext.data.Model-method-join\" rel=\"Ext.data.Model-method-join\" class=\"docClass\">joined</a> to..</p>\n        <p>Available since: <b>3.4.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>record</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The model instance that was edited</p>\n</div></li></ul></div></div></div><div id='method-applyState' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-applyState' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-applyState' class='name expandable'>applyState</a>( <span class='pre'>state</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Restores state to the passed state ...</div><div class='long'><p>Restores state to the passed state</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>state</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-callOverridden' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-captureArgs' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-captureArgs' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-captureArgs' class='name expandable'>captureArgs</a>( <span class='pre'>o, fn, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>o</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-clearData' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-clearData' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-clearData' class='name expandable'>clearData</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>to be implemented by subclasses ...</div><div class='long'><p>to be implemented by subclasses</p>\n</div></div></div><div id='method-clearFilter' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-clearFilter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-clearFilter' class='name expandable'>clearFilter</a>( <span class='pre'>supressEvent</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>supressEvent</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-clearListeners' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-clearListeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-clearListeners' class='name expandable'>clearListeners</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Removes all listeners for this object including the managed listeners ...</div><div class='long'><p>Removes all listeners for this object including the managed listeners</p>\n</div></div></div><div id='method-clearManagedListeners' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-clearManagedListeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-clearManagedListeners' class='name expandable'>clearManagedListeners</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Removes all managed listeners for this object. ...</div><div class='long'><p>Removes all managed listeners for this object.</p>\n</div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-continueFireEvent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-continueFireEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-continueFireEvent' class='name expandable'>continueFireEvent</a>( <span class='pre'>eventName, args, bubbles</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Continue to fire event. ...</div><div class='long'><p>Continue to fire event.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li><li><span class='pre'>bubbles</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-create' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-create' class='name expandable'>create</a>( <span class='pre'>data, options</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>saves any phantom records ...</div><div class='long'><p>saves any phantom records</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>data</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createRelayer' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-createRelayer' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-createRelayer' class='name expandable'>createRelayer</a>( <span class='pre'>newName, [beginEnd]</span> ) : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Creates an event handling function which refires the event from this object as the passed event name. ...</div><div class='long'><p>Creates an event handling function which refires the event from this object as the passed event name.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>newName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name under which to refire the passed parameters.</p>\n</div></li><li><span class='pre'>beginEnd</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> (optional)<div class='sub-desc'><p>The caller can specify on which indices to slice.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-decodeFilters' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-decodeFilters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-decodeFilters' class='name expandable'>decodeFilters</a>( <span class='pre'>filters</span> ) : <a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Ext.util.Filter</a>[]<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Normalizes an array of filter objects, ensuring that they are all Ext.util.Filter instances ...</div><div class='long'><p>Normalizes an array of filter objects, ensuring that they are all <a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Ext.util.Filter</a> instances</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>filters</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]<div class='sub-desc'><p>The filters array</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Ext.util.Filter</a>[]</span><div class='sub-desc'><p>Array of <a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Ext.util.Filter</a> objects</p>\n</div></li></ul></div></div></div><div id='method-decodeSorters' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-method-decodeSorters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-method-decodeSorters' class='name expandable'>decodeSorters</a>( <span class='pre'>sorters</span> ) : <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a>[]<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Normalizes an array of sorter objects, ensuring that they are all Ext.util.Sorter instances ...</div><div class='long'><p>Normalizes an array of sorter objects, ensuring that they are all <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a> instances</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>sorters</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]<div class='sub-desc'><p>The sorters array</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a>[]</span><div class='sub-desc'><p>Array of <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a> objects</p>\n</div></li></ul></div></div></div><div id='method-destroy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-destroy' class='name expandable'>destroy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>tells the attached proxy to destroy the given records ...</div><div class='long'><p>tells the attached proxy to destroy the given records</p>\n        <p>Available since: <b>3.4.0</b></p>\n<p>Overrides: <a href='#!/api/Ext.Base-method-destroy' rel='Ext.Base-method-destroy' class='docClass'>Ext.Base.destroy</a></p></div></div></div><div id='method-destroyStore' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-destroyStore' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-destroyStore' class='name expandable'>destroyStore</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>private ...</div><div class='long'><p>private</p>\n</div></div></div><div id='method-doSort' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-doSort' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-doSort' class='name expandable'>doSort</a>( <span class='pre'>sorterFn</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>private ...</div><div class='long'><p>private</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>sorterFn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-emptyComparator' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-method-emptyComparator' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-method-emptyComparator' class='name expandable'>emptyComparator</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-enableBubble' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-enableBubble' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-enableBubble' class='name expandable'>enableBubble</a>( <span class='pre'>eventNames</span> )</div><div class='description'><div class='short'>Enables events fired by this Observable to bubble up an owner hierarchy by calling this.getBubbleTarget() if\npresent. ...</div><div class='long'><p>Enables events fired by this Observable to bubble up an owner hierarchy by calling <code>this.getBubbleTarget()</code> if\npresent. There is no implementation in the Observable base class.</p>\n\n<p>This is commonly used by Ext.Components to bubble events to owner Containers.\nSee <a href=\"#!/api/Ext.Component-method-getBubbleTarget\" rel=\"Ext.Component-method-getBubbleTarget\" class=\"docClass\">Ext.Component.getBubbleTarget</a>. The default implementation in <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a> returns the\nComponent's immediate owner. But if a known target is required, this can be overridden to access the\nrequired target more quickly.</p>\n\n<p>Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.overrides.form.field.Base', {\n    override: '<a href=\"#!/api/Ext.form.field.Base\" rel=\"Ext.form.field.Base\" class=\"docClass\">Ext.form.field.Base</a>',\n\n    //  Add functionality to Field's initComponent to enable the change event to bubble\n    initComponent: function () {\n        this.callParent();\n        this.enableBubble('change');\n    }\n});\n\nvar myForm = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.form.Panel\" rel=\"Ext.form.Panel\" class=\"docClass\">Ext.form.Panel</a>', {\n    title: 'User Details',\n    items: [{\n        ...\n    }],\n    listeners: {\n        change: function() {\n            // Title goes red if form has been modified.\n            myForm.header.setStyle('color', 'red');\n        }\n    }\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventNames</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>The event name to bubble, or an Array of event names.</p>\n</div></li></ul></div></div></div><div id='method-filter' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-filter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-filter' class='name expandable'>filter</a>( <span class='pre'>filters, value</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>filters</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-filterBy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-filterBy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-filterBy' class='name expandable'>filterBy</a>( <span class='pre'>fn, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-filterNew' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-filterNew' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-filterNew' class='name expandable'>filterNew</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Filter function for new records. ...</div><div class='long'><p>Filter function for new records.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-filterUpdated' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-filterUpdated' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-filterUpdated' class='name expandable'>filterUpdated</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Filter function for updated records. ...</div><div class='long'><p>Filter function for updated records.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-fireEvent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-fireEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-fireEvent' class='name expandable'>fireEvent</a>( <span class='pre'>eventName, args</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Fires the specified event with the passed parameters (minus the event name, plus the options object passed\nto addList...</div><div class='long'><p>Fires the specified event with the passed parameters (minus the event name, plus the <code>options</code> object passed\nto <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a>).</p>\n\n<p>An event may be set to bubble up an Observable parent hierarchy (See <a href=\"#!/api/Ext.Component-method-getBubbleTarget\" rel=\"Ext.Component-method-getBubbleTarget\" class=\"docClass\">Ext.Component.getBubbleTarget</a>) by\ncalling <a href=\"#!/api/Ext.util.Observable-method-enableBubble\" rel=\"Ext.util.Observable-method-enableBubble\" class=\"docClass\">enableBubble</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the event to fire.</p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>...<div class='sub-desc'><p>Variable number of parameters are passed to handlers.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>returns false if any of the handlers return false otherwise it returns true.</p>\n</div></li></ul></div></div></div><div id='method-fireEventArgs' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-fireEventArgs' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-fireEventArgs' class='name expandable'>fireEventArgs</a>( <span class='pre'>eventName, args</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Fires the specified event with the passed parameter list. ...</div><div class='long'><p>Fires the specified event with the passed parameter list.</p>\n\n<p>An event may be set to bubble up an Observable parent hierarchy (See <a href=\"#!/api/Ext.Component-method-getBubbleTarget\" rel=\"Ext.Component-method-getBubbleTarget\" class=\"docClass\">Ext.Component.getBubbleTarget</a>) by\ncalling <a href=\"#!/api/Ext.util.Observable-method-enableBubble\" rel=\"Ext.util.Observable-method-enableBubble\" class=\"docClass\">enableBubble</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the event to fire.</p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]<div class='sub-desc'><p>An array of parameters which are passed to handlers.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>returns false if any of the handlers return false otherwise it returns true.</p>\n</div></li></ul></div></div></div><div id='method-generateComparator' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-method-generateComparator' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-method-generateComparator' class='name expandable'>generateComparator</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Returns a comparator function which compares two items and returns -1, 0, or 1 depending\non the currently defined set...</div><div class='long'><p>Returns a comparator function which compares two items and returns -1, 0, or 1 depending\non the currently defined set of <a href=\"#!/api/Ext.util.Sortable-cfg-sorters\" rel=\"Ext.util.Sortable-cfg-sorters\" class=\"docClass\">sorters</a>.</p>\n\n<p>If there are no <a href=\"#!/api/Ext.util.Sortable-cfg-sorters\" rel=\"Ext.util.Sortable-cfg-sorters\" class=\"docClass\">sorters</a> defined, it returns a function which returns <code>0</code> meaning\nthat no sorting will occur.</p>\n</div></div></div><div id='method-getBatchListeners' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-getBatchListeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-getBatchListeners' class='name expandable'>getBatchListeners</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Returns an object which is passed in as the listeners argument to proxy.batch inside this.sync. ...</div><div class='long'><p>Returns an object which is passed in as the listeners argument to proxy.batch inside this.sync.\nThis is broken out into a separate function to allow for customisation of the listeners</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The listeners object</p>\n</div></li></ul></div></div></div><div id='method-getBubbleParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-getBubbleParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-getBubbleParent' class='name expandable'>getBubbleParent</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Gets the bubbling parent for an Observable ...</div><div class='long'><p>Gets the bubbling parent for an Observable</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a></span><div class='sub-desc'><p>The bubble parent. null is returned if no bubble target exists</p>\n</div></li></ul></div></div></div><div id='method-getById' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-getById' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-getById' class='name expandable'>getById</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>to be implemented by subclasses ...</div><div class='long'><p>to be implemented by subclasses</p>\n</div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getCount' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-getCount' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-getCount' class='name expandable'>getCount</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>to be implemented by subclasses ...</div><div class='long'><p>to be implemented by subclasses</p>\n</div></div></div><div id='method-getFirstSorter' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-method-getFirstSorter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-method-getFirstSorter' class='name expandable'>getFirstSorter</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Gets the first sorter from the sorters collection, excluding\nany groupers that may be in place ...</div><div class='long'><p>Gets the first sorter from the sorters collection, excluding\nany groupers that may be in place</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a></span><div class='sub-desc'><p>The sorter, null if none exist</p>\n</div></li></ul></div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-getModifiedRecords' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-getModifiedRecords' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-getModifiedRecords' class='name expandable'>getModifiedRecords</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</div><div class='description'><div class='short'>Gets all records added or updated since the last commit. ...</div><div class='long'><p>Gets all <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">records</a> added or updated since the last commit. Note that the order of records\nreturned is not deterministic and does not indicate the order in which records were modified. Note also that\nremoved records are not included (use <a href=\"#!/api/Ext.data.AbstractStore-method-getRemovedRecords\" rel=\"Ext.data.AbstractStore-method-getRemovedRecords\" class=\"docClass\">getRemovedRecords</a> for that).</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</span><div class='sub-desc'><p>The added and updated Model instances</p>\n</div></li></ul></div></div></div><div id='method-getNewRecords' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-getNewRecords' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-getNewRecords' class='name expandable'>getNewRecords</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</div><div class='description'><div class='short'>Returns all Model instances that are either currently a phantom (e.g. ...</div><div class='long'><p>Returns all Model instances that are either currently a phantom (e.g. have no id), or have an ID but have not\nyet been saved on this Store (this happens when adding a non-phantom record from another Store into this one)</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</span><div class='sub-desc'><p>The Model instances</p>\n</div></li></ul></div></div></div><div id='method-getProxy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-getProxy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-getProxy' class='name expandable'>getProxy</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a></div><div class='description'><div class='short'>Returns the proxy currently attached to this proxy instance ...</div><div class='long'><p>Returns the proxy currently attached to this proxy instance</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a></span><div class='sub-desc'><p>The Proxy instance</p>\n</div></li></ul></div></div></div><div id='method-getRemovedRecords' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-getRemovedRecords' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-getRemovedRecords' class='name expandable'>getRemovedRecords</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</div><div class='description'><div class='short'>Returns any records that have been removed from the store but not yet destroyed on the proxy. ...</div><div class='long'><p>Returns any records that have been removed from the store but not yet destroyed on the proxy.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</span><div class='sub-desc'><p>The removed Model instances</p>\n</div></li></ul></div></div></div><div id='method-getSorters' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-method-getSorters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-method-getSorters' class='name expandable'>getSorters</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getState' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-getState' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-getState' class='name expandable'>getState</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Returns the grouping, sorting and filtered state of this Store. ...</div><div class='long'><p>Returns the grouping, sorting and filtered state of this Store.</p>\n</div></div></div><div id='method-getUpdatedRecords' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-getUpdatedRecords' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-getUpdatedRecords' class='name expandable'>getUpdatedRecords</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</div><div class='description'><div class='short'>Returns all Model instances that have been updated in the Store but not yet synchronized with the Proxy ...</div><div class='long'><p>Returns all Model instances that have been updated in the Store but not yet synchronized with the Proxy</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</span><div class='sub-desc'><p>The updated Model instances</p>\n</div></li></ul></div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-hasListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-hasListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-hasListener' class='name expandable'>hasListener</a>( <span class='pre'>eventName</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks to see if this object has any listeners for a specified event, or whether the event bubbles. ...</div><div class='long'><p>Checks to see if this object has any listeners for a specified event, or whether the event bubbles. The answer\nindicates whether the event needs firing or not.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the event to check for</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p><code>true</code> if the event is being listened for or bubbles, else <code>false</code></p>\n</div></li></ul></div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-initSortable' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-method-initSortable' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-method-initSortable' class='name expandable'>initSortable</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Performs initialization of this mixin. ...</div><div class='long'><p>Performs initialization of this mixin. Component classes using this mixin should call this method during their\nown initialization.</p>\n</div></div></div><div id='method-isFiltered' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-isFiltered' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-isFiltered' class='name expandable'>isFiltered</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-isLoading' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-isLoading' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-isLoading' class='name expandable'>isLoading</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the Store is currently performing a load operation ...</div><div class='long'><p>Returns true if the Store is currently performing a load operation</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if the Store is currently loading</p>\n</div></li></ul></div></div></div><div id='method-load' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-load' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-load' class='name expandable'>load</a>( <span class='pre'>[options]</span> ) : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a></div><div class='description'><div class='short'>Loads the Store using its configured proxy. ...</div><div class='long'><p>Loads the Store using its configured <a href=\"#!/api/Ext.data.AbstractStore-cfg-proxy\" rel=\"Ext.data.AbstractStore-cfg-proxy\" class=\"docClass\">proxy</a>.</p>\n        <p>Available since: <b>1.1.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>config object. This is passed into the <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Operation</a>\nobject that is created and then sent to the proxy's <a href=\"#!/api/Ext.data.proxy.Proxy-method-read\" rel=\"Ext.data.proxy.Proxy-method-read\" class=\"docClass\">Ext.data.proxy.Proxy.read</a> function</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-mon' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-mon' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-mon' class='name expandable'>mon</a>( <span class='pre'>item, ename, [fn], [scope], [options]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Shorthand for addManagedListener. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.util.Observable-method-addManagedListener\" rel=\"Ext.util.Observable-method-addManagedListener\" class=\"docClass\">addManagedListener</a>.</p>\n\n<p>Adds listeners to any Observable object (or <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>) which are automatically removed when this Component is\ndestroyed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The item to which to add a listener/listeners.</p>\n\n</div></li><li><span class='pre'>ename</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The event name, or an object containing event name properties.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the handler function.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the scope (<code>this</code> reference)\nin which the handler function is executed.</p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the\n<a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> options.</p>\n\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><strong>Only when the <code>destroyable</code> option is specified. </strong></p>\n\n\n\n\n<p> A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call. For example:</p>\n\n\n\n\n<pre><code>this.btnListeners =  = myButton.mon({\n    destroyable: true\n    mouseover:   function() { console.log('mouseover'); },\n    mouseout:    function() { console.log('mouseout'); },\n    click:       function() { console.log('click'); }\n});\n</code></pre>\n\n\n\n\n<p>And when those listeners need to be removed:</p>\n\n\n\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.btnListeners);\n</code></pre>\n\n\n\n\n<p>or</p>\n\n\n\n\n<pre><code>this.btnListeners.destroy();\n</code></pre>\n\n</div></li></ul></div></div></div><div id='method-mun' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-mun' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-mun' class='name expandable'>mun</a>( <span class='pre'>item, ename, [fn], [scope]</span> )</div><div class='description'><div class='short'>Shorthand for removeManagedListener. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.util.Observable-method-removeManagedListener\" rel=\"Ext.util.Observable-method-removeManagedListener\" class=\"docClass\">removeManagedListener</a>.</p>\n\n<p>Removes listeners that were added by the <a href=\"#!/api/Ext.util.Observable-method-mon\" rel=\"Ext.util.Observable-method-mon\" class=\"docClass\">mon</a> method.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The item from which to remove a listener/listeners.</p>\n\n</div></li><li><span class='pre'>ename</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The event name, or an object containing event name properties.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the handler function.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the scope (<code>this</code> reference)\nin which the handler function is executed.</p>\n\n</div></li></ul></div></div></div><div id='method-on' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-on' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-on' class='name expandable'>on</a>( <span class='pre'>eventName, [fn], [scope], [options]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Shorthand for addListener. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a>.</p>\n\n<p>Appends an event handler to this object.  For example:</p>\n\n<pre><code>myGridPanel.on(\"mouseover\", this.onMouseOver, this);\n</code></pre>\n\n<p>The method also allows for a single argument to be passed which is a config object\ncontaining properties which specify multiple events. For example:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: this.onCellClick,\n    mouseover: this.onMouseOver,\n    mouseout: this.onMouseOut,\n    scope: this // Important. Ensure \"this\" is correct during handler execution\n});\n</code></pre>\n\n<p>One can also specify options for each event handler separately:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: this.onCellClick, scope: this, single: true},\n    mouseover: {fn: panel.onMouseOver, scope: panel}\n});\n</code></pre>\n\n<p><em>Names</em> of methods in a specified scope may also be used. Note that\n<code>scope</code> MUST be specified to use this option:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: 'onCellClick', scope: this, single: true},\n    mouseover: {fn: 'onMouseOver', scope: panel}\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The name of the event to listen for.\nMay also be an object who's property names are event names.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The method the event invokes, or <em>if <code>scope</code> is specified, the </em>name* of the method within\nthe specified <code>scope</code>.  Will be called with arguments\ngiven to <a href=\"#!/api/Ext.util.Observable-method-fireEvent\" rel=\"Ext.util.Observable-method-fireEvent\" class=\"docClass\">fireEvent</a> plus the <code>options</code> parameter described below.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is\nexecuted. <strong>If omitted, defaults to the object which fired the event.</strong></p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>An object containing handler configuration.</p>\n\n\n\n\n<p><strong>Note:</strong> Unlike in ExtJS 3.x, the options object will also be passed as the last\nargument to every event handler.</p>\n\n\n\n\n<p>This object may contain any of the following properties:</p>\n\n<ul><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is executed. <strong>If omitted,\n  defaults to the object which fired the event.</strong></p>\n\n</div></li><li><span class='pre'>delay</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of milliseconds to delay the invocation of the handler after the event fires.</p>\n\n</div></li><li><span class='pre'>single</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to add a handler to handle just the next firing of the event, and then remove itself.</p>\n\n</div></li><li><span class='pre'>buffer</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Causes the handler to be scheduled to run in an <a href=\"#!/api/Ext.util.DelayedTask\" rel=\"Ext.util.DelayedTask\" class=\"docClass\">Ext.util.DelayedTask</a> delayed\n  by the specified number of milliseconds. If the event fires again within that time,\n  the original handler is <em>not</em> invoked, but the new handler is scheduled in its place.</p>\n\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a><div class='sub-desc'><p>Only call the handler if the event was fired on the target Observable, <em>not</em> if the event\n  was bubbled up from a child Observable.</p>\n\n</div></li><li><span class='pre'>element</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p><strong>This option is only valid for listeners bound to <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a>.</strong>\n  The name of a Component property which references an element to add a listener to.</p>\n\n\n\n\n<p>  This option is useful during Component construction to add DOM event listeners to elements of\n  <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a> which will exist only after the Component is rendered.\n  For example, to add a click listener to a Panel's body:</p>\n\n\n\n\n<pre><code>  new <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>({\n      title: 'The title',\n      listeners: {\n          click: this.handlePanelClick,\n          element: 'body'\n      }\n  });\n</code></pre>\n\n</div></li><li><span class='pre'>destroyable</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>When specified as <code>true</code>, the function returns A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call.</p>\n\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>priority</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>An optional numeric priority that determines the order in which event handlers\n  are run. Event handlers with no priority will be run as if they had a priority\n  of 0. Handlers with a higher priority will be prioritized to run sooner than\n  those with a lower priority.  Negative numbers can be used to set a priority\n  lower than the default. Internally, the framework uses a range of 1000 or\n  greater, and -1000 or lesser for handers that are intended to run before or\n  after all others, so it is recommended to stay within the range of -999 to 999\n  when setting the priority of event handlers in application-level code.</p>\n\n\n\n\n<p><strong>Combining Options</strong></p>\n\n\n\n\n<p>Using the options argument, it is possible to combine different types of listeners:</p>\n\n\n\n\n<p>A delayed, one-time listener.</p>\n\n\n\n\n<pre><code>myPanel.on('hide', this.handleClick, this, {\n    single: true,\n    delay: 100\n});\n</code></pre>\n\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><strong>Only when the <code>destroyable</code> option is specified. </strong></p>\n\n\n\n\n<p> A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call. For example:</p>\n\n\n\n\n<pre><code>this.btnListeners =  = myButton.on({\n    destroyable: true\n    mouseover:   function() { console.log('mouseover'); },\n    mouseout:    function() { console.log('mouseout'); },\n    click:       function() { console.log('click'); }\n});\n</code></pre>\n\n\n\n\n<p>And when those listeners need to be removed:</p>\n\n\n\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.btnListeners);\n</code></pre>\n\n\n\n\n<p>or</p>\n\n\n\n\n<pre><code>this.btnListeners.destroy();\n</code></pre>\n\n</div></li></ul></div></div></div><div id='method-onBatchComplete' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onBatchComplete' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onBatchComplete' class='name expandable'>onBatchComplete</a>( <span class='pre'>batch, operation</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Attached as the 'complete' event listener to a proxy's Batch object. ...</div><div class='long'><p>Attached as the 'complete' event listener to a proxy's Batch object. Iterates over the batch operations\nand updates the Store's internal data MixedCollection.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>batch</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onBatchException' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onBatchException' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onBatchException' class='name expandable'>onBatchException</a>( <span class='pre'>batch, operation</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>batch</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onBatchOperationComplete' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onBatchOperationComplete' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onBatchOperationComplete' class='name expandable'>onBatchOperationComplete</a>( <span class='pre'>batch, operation</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Attached as the 'operationcomplete' event listener to a proxy's Batch object. ...</div><div class='long'><p>Attached as the 'operationcomplete' event listener to a proxy's Batch object. By default just calls through\nto onProxyWrite.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>batch</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onBeforeSort' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-method-onBeforeSort' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-method-onBeforeSort' class='name expandable'>onBeforeSort</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-onClassExtended' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onClassExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onClassExtended' class='name expandable'>onClassExtended</a>( <span class='pre'>cls, data, hooks</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>cls</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>data</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>hooks</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onCreateRecords' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onCreateRecords' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onCreateRecords' class='name expandable'>onCreateRecords</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>may be implemented by store subclasses ...</div><div class='long'><p>may be implemented by store subclasses</p>\n</div></div></div><div id='method-onDestroyRecords' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onDestroyRecords' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onDestroyRecords' class='name expandable'>onDestroyRecords</a>( <span class='pre'>records, operation, success</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Removes any records when a write is returned from the server. ...</div><div class='long'><p>Removes any records when a write is returned from the server.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>records</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]<div class='sub-desc'><p>The array of removed records</p>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The operation that just completed</p>\n</div></li><li><span class='pre'>success</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True if the operation was successful</p>\n</div></li></ul></div></div></div><div id='method-onIdChanged' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onIdChanged' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onIdChanged' class='name expandable'>onIdChanged</a>( <span class='pre'>model, oldId, newId, oldInternalId</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>model</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>oldId</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>newId</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>oldInternalId</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onMetaChange' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onMetaChange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onMetaChange' class='name expandable'>onMetaChange</a>( <span class='pre'>proxy, meta</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>private ...</div><div class='long'><p>private</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>proxy</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>meta</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onProxyWrite' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onProxyWrite' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onProxyWrite' class='name expandable'>onProxyWrite</a>( <span class='pre'>operation</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Callback for any write Operation over the Proxy. ...</div><div class='long'><p>Callback for any write Operation over the Proxy. Updates the Store's MixedCollection to reflect\nthe updates provided by the Proxy</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>operation</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onUpdate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onUpdate' class='name expandable'>onUpdate</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-onUpdateRecords' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onUpdateRecords' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onUpdateRecords' class='name expandable'>onUpdateRecords</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>may be implemented by store subclasses ...</div><div class='long'><p>may be implemented by store subclasses</p>\n</div></div></div><div id='method-prepareClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-prepareClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-prepareClass' class='name expandable'>prepareClass</a>( <span class='pre'>T</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Prepares a given class for observable instances. ...</div><div class='long'><p>Prepares a given class for observable instances. This method is called when a\nclass derives from this class or uses this class as a mixin.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>T</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The class constructor to prepare.</p>\n</div></li></ul></div></div></div><div id='method-read' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-read' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-read' class='name expandable'>read</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-relayEvents' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-relayEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-relayEvents' class='name expandable'>relayEvents</a>( <span class='pre'>origin, events, [prefix]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Relays selected events from the specified Observable as if the events were fired by this. ...</div><div class='long'><p>Relays selected events from the specified Observable as if the events were fired by <code>this</code>.</p>\n\n<p>For example if you are extending Grid, you might decide to forward some events from store.\nSo you can do this inside your initComponent:</p>\n\n<pre><code>this.relayEvents(this.getStore(), ['load']);\n</code></pre>\n\n<p>The grid instance will then have an observable 'load' event which will be passed the\nparameters of the store's load event and any function fired with the grid's load event\nwould have access to the grid using the <code>this</code> keyword.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>origin</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The Observable whose events this object is to relay.</p>\n</div></li><li><span class='pre'>events</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>Array of event names to relay.</p>\n</div></li><li><span class='pre'>prefix</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>A common prefix to prepend to the event names. For example:</p>\n\n<pre><code>this.relayEvents(this.getStore(), ['load', 'clear'], 'store');\n</code></pre>\n\n<p>Now the grid will forward 'load' and 'clear' events of store as 'storeload' and 'storeclear'.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which, when destroyed, removes all relayers. For example:</p>\n\n<pre><code>this.storeRelayers = this.relayEvents(this.getStore(), ['load', 'clear'], 'store');\n</code></pre>\n\n<p>Can be undone by calling</p>\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.storeRelayers);\n</code></pre>\n\n<p>or</p>\n\n<pre><code>this.store.relayers.destroy();\n</code></pre>\n</div></li></ul></div></div></div><div id='method-reload' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-reload' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-reload' class='name expandable'>reload</a>( <span class='pre'>options</span> )</div><div class='description'><div class='short'>Reloads the store using the last options passed to the load method. ...</div><div class='long'><p>Reloads the store using the last options passed to the <a href=\"#!/api/Ext.data.AbstractStore-method-load\" rel=\"Ext.data.AbstractStore-method-load\" class=\"docClass\">load</a> method.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>A config object which contains options which may override the options passed to the previous load call.</p>\n</div></li></ul></div></div></div><div id='method-removeAll' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-removeAll' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-removeAll' class='name expandable'>removeAll</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Removes all records from the store. ...</div><div class='long'><p>Removes all records from the store. This method does a \"fast remove\",\nindividual remove events are not called. The <a href=\"#!/api/Ext.data.AbstractStore-event-clear\" rel=\"Ext.data.AbstractStore-event-clear\" class=\"docClass\">clear</a> event is\nfired upon completion.</p>\n        <p>Available since: <b>1.1.0</b></p>\n</div></div></div><div id='method-removeListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-removeListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-removeListener' class='name expandable'>removeListener</a>( <span class='pre'>eventName, fn, [scope]</span> )</div><div class='description'><div class='short'>Removes an event handler. ...</div><div class='long'><p>Removes an event handler.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The type of event the handler was associated with.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The handler to remove. <strong>This must be a reference to the function passed into the\n<a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> call.</strong></p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope originally specified for the handler. It must be the same as the\nscope argument specified in the original call to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> or the listener will not be removed.</p>\n\n</div></li></ul></div></div></div><div id='method-removeManagedListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-removeManagedListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-removeManagedListener' class='name expandable'>removeManagedListener</a>( <span class='pre'>item, ename, [fn], [scope]</span> )</div><div class='description'><div class='short'>Removes listeners that were added by the mon method. ...</div><div class='long'><p>Removes listeners that were added by the <a href=\"#!/api/Ext.util.Observable-method-mon\" rel=\"Ext.util.Observable-method-mon\" class=\"docClass\">mon</a> method.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The item from which to remove a listener/listeners.</p>\n\n</div></li><li><span class='pre'>ename</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The event name, or an object containing event name properties.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the handler function.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the scope (<code>this</code> reference)\nin which the handler function is executed.</p>\n\n</div></li></ul></div></div></div><div id='method-removeManagedListenerItem' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-removeManagedListenerItem' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-removeManagedListenerItem' class='name expandable'>removeManagedListenerItem</a>( <span class='pre'>isClear, managedListener</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Remove a single managed listener item ...</div><div class='long'><p>Remove a single managed listener item</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>isClear</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True if this is being called during a clear</p>\n</div></li><li><span class='pre'>managedListener</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The managed listener item\nSee removeManagedListener for other args</p>\n</div></li></ul></div></div></div><div id='method-resumeAutoSync' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-resumeAutoSync' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-resumeAutoSync' class='name expandable'>resumeAutoSync</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Resumes automatically syncing the Store with its Proxy. ...</div><div class='long'><p>Resumes automatically syncing the Store with its Proxy.  Only applicable if <a href=\"#!/api/Ext.data.AbstractStore-cfg-autoSync\" rel=\"Ext.data.AbstractStore-cfg-autoSync\" class=\"docClass\">autoSync</a> is <code>true</code></p>\n</div></div></div><div id='method-resumeEvent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-resumeEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-resumeEvent' class='name expandable'>resumeEvent</a>( <span class='pre'>eventName</span> )</div><div class='description'><div class='short'>Resumes firing of the named event(s). ...</div><div class='long'><p>Resumes firing of the named event(s).</p>\n\n<p>After calling this method to resume events, the events will fire when requested to fire.</p>\n\n<p><strong>Note that if the <a href=\"#!/api/Ext.util.Observable-method-suspendEvent\" rel=\"Ext.util.Observable-method-suspendEvent\" class=\"docClass\">suspendEvent</a> method is called multiple times for a certain event,\nthis converse method will have to be called the same number of times for it to resume firing.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>...<div class='sub-desc'><p>Multiple event names to resume.</p>\n</div></li></ul></div></div></div><div id='method-resumeEvents' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-resumeEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-resumeEvents' class='name expandable'>resumeEvents</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Resumes firing events (see suspendEvents). ...</div><div class='long'><p>Resumes firing events (see <a href=\"#!/api/Ext.util.Observable-method-suspendEvents\" rel=\"Ext.util.Observable-method-suspendEvents\" class=\"docClass\">suspendEvents</a>).</p>\n\n<p>If events were suspended using the <code>queueSuspended</code> parameter, then all events fired\nduring event suspension will be sent to any listeners now.</p>\n</div></div></div><div id='method-save' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-save' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-save' class='name expandable'>save</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Saves all pending changes via the configured proxy. ...</div><div class='long'><p>Saves all pending changes via the configured <a href=\"#!/api/Ext.data.AbstractStore-cfg-proxy\" rel=\"Ext.data.AbstractStore-cfg-proxy\" class=\"docClass\">proxy</a>. Use <a href=\"#!/api/Ext.data.AbstractStore-method-sync\" rel=\"Ext.data.AbstractStore-method-sync\" class=\"docClass\">sync</a> instead.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Will be removed in the next major version</p>\n\n        </div>\n</div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setProxy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-setProxy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-setProxy' class='name expandable'>setProxy</a>( <span class='pre'>proxy</span> ) : <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a></div><div class='description'><div class='short'>Sets the Store's Proxy by string, config object or Proxy instance ...</div><div class='long'><p>Sets the Store's Proxy by string, config object or Proxy instance</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>proxy</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a><div class='sub-desc'><p>The new Proxy, which can be either a type string, a configuration object\nor an <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a> instance</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a></span><div class='sub-desc'><p>The attached Proxy object</p>\n</div></li></ul></div></div></div><div id='method-sort' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-method-sort' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-method-sort' class='name expandable'>sort</a>( <span class='pre'>[sorters], [direction]</span> ) : <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a>[]</div><div class='description'><div class='short'>Sorts the data in the Store by one or more of its properties. ...</div><div class='long'><p>Sorts the data in the Store by one or more of its properties. Example usage:</p>\n\n<pre><code>//sort by a single field\nmyStore.sort('myField', 'DESC');\n\n//sorting by multiple fields\nmyStore.sort([\n    {\n        property : 'age',\n        direction: 'ASC'\n    },\n    {\n        property : 'name',\n        direction: 'DESC'\n    }\n]);\n</code></pre>\n\n<p>Internally, Store converts the passed arguments into an array of <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a> instances, and delegates\nthe actual sorting to its internal <a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">Ext.util.MixedCollection</a>.</p>\n\n<p>When passing a single string argument to sort, Store maintains a ASC/DESC toggler per field, so this code:</p>\n\n<pre><code>store.sort('myField');\nstore.sort('myField');\n</code></pre>\n\n<p>Is equivalent to this code, because Store handles the toggling automatically:</p>\n\n<pre><code>store.sort('myField', 'ASC');\nstore.sort('myField', 'DESC');\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>sorters</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a>[] (optional)<div class='sub-desc'><p>Either a string name of one of the fields in this Store's configured\n<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Model</a>, or an array of sorter configurations.</p>\n</div></li><li><span class='pre'>direction</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The overall direction to sort the data by.</p>\n<p>Defaults to: <code>&quot;ASC&quot;</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a>[]</span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-suspendAutoSync' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-suspendAutoSync' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-suspendAutoSync' class='name expandable'>suspendAutoSync</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Suspends automatically syncing the Store with its Proxy. ...</div><div class='long'><p>Suspends automatically syncing the Store with its Proxy.  Only applicable if <a href=\"#!/api/Ext.data.AbstractStore-cfg-autoSync\" rel=\"Ext.data.AbstractStore-cfg-autoSync\" class=\"docClass\">autoSync</a> is <code>true</code></p>\n</div></div></div><div id='method-suspendEvent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-suspendEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-suspendEvent' class='name expandable'>suspendEvent</a>( <span class='pre'>eventName</span> )</div><div class='description'><div class='short'>Suspends firing of the named event(s). ...</div><div class='long'><p>Suspends firing of the named event(s).</p>\n\n<p>After calling this method to suspend events, the events will no longer fire when requested to fire.</p>\n\n<p><strong>Note that if this is called multiple times for a certain event, the converse method\n<a href=\"#!/api/Ext.util.Observable-method-resumeEvent\" rel=\"Ext.util.Observable-method-resumeEvent\" class=\"docClass\">resumeEvent</a> will have to be called the same number of times for it to resume firing.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>...<div class='sub-desc'><p>Multiple event names to suspend.</p>\n</div></li></ul></div></div></div><div id='method-suspendEvents' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-suspendEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-suspendEvents' class='name expandable'>suspendEvents</a>( <span class='pre'>queueSuspended</span> )</div><div class='description'><div class='short'>Suspends the firing of all events. ...</div><div class='long'><p>Suspends the firing of all events. (see <a href=\"#!/api/Ext.util.Observable-method-resumeEvents\" rel=\"Ext.util.Observable-method-resumeEvents\" class=\"docClass\">resumeEvents</a>)</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>queueSuspended</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>Pass as true to queue up suspended events to be fired\nafter the <a href=\"#!/api/Ext.util.Observable-method-resumeEvents\" rel=\"Ext.util.Observable-method-resumeEvents\" class=\"docClass\">resumeEvents</a> call instead of discarding all suspended events.</p>\n</div></li></ul></div></div></div><div id='method-sync' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-sync' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-sync' class='name expandable'>sync</a>( <span class='pre'>[options]</span> ) : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a></div><div class='description'><div class='short'>Synchronizes the store with its proxy. ...</div><div class='long'><p>Synchronizes the store with its <a href=\"#!/api/Ext.data.AbstractStore-cfg-proxy\" rel=\"Ext.data.AbstractStore-cfg-proxy\" class=\"docClass\">proxy</a>. This asks the proxy to batch together any new, updated\nand deleted records in the store, updating the store's internal representation of the records\nas each operation completes.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Object containing one or more properties supported by the sync method (these get\npassed along to the underlying proxy's <a href=\"#!/api/Ext.data.proxy.Proxy-method-batch\" rel=\"Ext.data.proxy.Proxy-method-batch\" class=\"docClass\">batch</a> method):</p>\n<ul><li><span class='pre'>batch</span> : <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>A <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a> object (or batch config to apply\nto the created batch). If unspecified a default batch will be auto-created as needed.</p>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The function to be called upon completion of the sync.\nThe callback is called regardless of success or failure and is passed the following parameters:</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>batch</span> : <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">batch</a> that was processed,\ncontaining all operations in their current state after processing</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options argument that was originally passed into sync</p>\n</div></li></ul></div></li><li><span class='pre'>success</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The function to be called upon successful completion of the sync. The\nsuccess function is called only if no exceptions were reported in any operations. If one or more exceptions\noccurred then the failure function will be called instead. The success function is called\nwith the following parameters:</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>batch</span> : <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">batch</a> that was processed,\ncontaining all operations in their current state after processing</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options argument that was originally passed into sync</p>\n</div></li></ul></div></li><li><span class='pre'>failure</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The function to be called upon unsuccessful completion of the sync. The\nfailure function is called when one or more operations returns an exception during processing (even if some\noperations were also successful). In this case you can check the batch's <a href=\"#!/api/Ext.data.Batch-property-exceptions\" rel=\"Ext.data.Batch-property-exceptions\" class=\"docClass\">exceptions</a> array to see exactly which operations had exceptions. The failure function is called with the\nfollowing parameters:</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>batch</span> : <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a> that was processed, containing all\noperations in their current state after processing</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options argument that was originally passed into sync</p>\n</div></li></ul></div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope in which to execute any callbacks (i.e. the <code>this</code> object inside\nthe callback, success and/or failure functions). Defaults to the store's proxy.</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-un' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-un' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-un' class='name expandable'>un</a>( <span class='pre'>eventName, fn, [scope]</span> )</div><div class='description'><div class='short'>Shorthand for removeListener. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.util.Observable-method-removeListener\" rel=\"Ext.util.Observable-method-removeListener\" class=\"docClass\">removeListener</a>.</p>\n\n<p>Removes an event handler.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The type of event the handler was associated with.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The handler to remove. <strong>This must be a reference to the function passed into the\n<a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> call.</strong></p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope originally specified for the handler. It must be the same as the\nscope argument specified in the original call to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> or the listener will not be removed.</p>\n\n</div></li></ul></div></div></div><div id='method-update' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-update' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-update' class='name expandable'>update</a>( <span class='pre'>options</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-static-method-create' class='name expandable'>create</a>( <span class='pre'>store</span> ) : <a href=\"#!/api/Ext.data.AbstractStore\" rel=\"Ext.data.AbstractStore\" class=\"docClass\">Ext.data.AbstractStore</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Creates a store from config object. ...</div><div class='long'><p>Creates a store from config object.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>store</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Ext.data.AbstractStore\" rel=\"Ext.data.AbstractStore\" class=\"docClass\">Ext.data.AbstractStore</a><div class='sub-desc'><p>A config for\nthe store to be created.  It may contain a <code>type</code> field\nwhich defines the particular type of store to create.</p>\n\n<p>Alteratively passing an actual store to this method will\njust return it, no changes made.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.AbstractStore\" rel=\"Ext.data.AbstractStore\" class=\"docClass\">Ext.data.AbstractStore</a></span><div class='sub-desc'><p>The created store.</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.Base-static-method-create' rel='Ext.Base-static-method-create' class='docClass'>Ext.Base.create</a></p></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-event'>Events</h3><div class='subsection'><div id='event-add' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-add' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-add' class='name expandable'>add</a>( <span class='pre'>store, records, index, eOpts</span> )</div><div class='description'><div class='short'>Fired when a Model instance has been added to this Store. ...</div><div class='long'><p>Fired when a Model instance has been added to this Store.</p>\n        <p>Available since: <b>1.1.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>store</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>The store</p>\n</div></li><li><span class='pre'>records</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]<div class='sub-desc'><p>The Model instances that were added</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The index at which the instances were inserted</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-beforeload' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-beforeload' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-beforeload' class='name expandable'>beforeload</a>( <span class='pre'>store, operation, eOpts</span> )</div><div class='description'><div class='short'>Fires before a request is made for a new data object. ...</div><div class='long'><p>Fires before a request is made for a new data object. If the beforeload handler returns false the load\naction will be canceled.</p>\n        <p>Available since: <b>1.1.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>store</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>This Store</p>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a> object that will be passed to the Proxy to\nload the Store</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-beforesync' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-beforesync' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-beforesync' class='name expandable'>beforesync</a>( <span class='pre'>options, eOpts</span> )</div><div class='description'><div class='short'>Fired before a call to sync is executed. ...</div><div class='long'><p>Fired before a call to <a href=\"#!/api/Ext.data.AbstractStore-method-sync\" rel=\"Ext.data.AbstractStore-method-sync\" class=\"docClass\">sync</a> is executed. Return false from any listener to cancel the sync</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>Hash of all records to be synchronized, broken down into create, update and destroy</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-bulkremove' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-bulkremove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-bulkremove' class='name expandable'>bulkremove</a>( <span class='pre'>store, records, indexes, isMove, eOpts</span> )</div><div class='description'><div class='short'>Fired at the end of the remove method when all records in the passed array have been removed. ...</div><div class='long'><p>Fired at the <em>end</em> of the <a href=\"#!/api/Ext.data.Store-method-remove\" rel=\"Ext.data.Store-method-remove\" class=\"docClass\">remove</a> method when all records in the passed array have been removed.</p>\n\n<p>If many records may be removed in one go, then it is more efficient to listen for this event\nand perform any processing for a bulk remove than to listen for many <a href=\"#!/api/Ext.data.AbstractStore-event-remove\" rel=\"Ext.data.AbstractStore-event-remove\" class=\"docClass\">remove</a> events.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>store</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>The Store object</p>\n</div></li><li><span class='pre'>records</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]<div class='sub-desc'><p>The array of records that were removed (In the order they appear in the Store)</p>\n</div></li><li><span class='pre'>indexes</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]<div class='sub-desc'><p>The indexes of the records that were removed</p>\n</div></li><li><span class='pre'>isMove</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p><code>true</code> if the child nodes are being removed so they can be moved to another position in this Store.</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-clear' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-clear' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-clear' class='name expandable'>clear</a>( <span class='pre'>this, eOpts</span> )</div><div class='description'><div class='short'>Fired after the removeAll method is called. ...</div><div class='long'><p>Fired after the <a href=\"#!/api/Ext.data.AbstractStore-method-removeAll\" rel=\"Ext.data.AbstractStore-method-removeAll\" class=\"docClass\">removeAll</a> method is called.</p>\n        <p>Available since: <b>1.1.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-datachanged' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-datachanged' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-datachanged' class='name expandable'>datachanged</a>( <span class='pre'>this, eOpts</span> )</div><div class='description'><div class='short'>Fires whenever the records in the Store have changed in some way - this could include adding or removing\nrecords, or ...</div><div class='long'><p>Fires whenever the records in the Store have changed in some way - this could include adding or removing\nrecords, or updating the data in existing records</p>\n        <p>Available since: <b>1.1.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>The data store</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-load' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-load' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-load' class='name expandable'>load</a>( <span class='pre'>this, records, successful, eOpts</span> )</div><div class='description'><div class='short'>Fires whenever the store reads data from a remote data source. ...</div><div class='long'><p>Fires whenever the store reads data from a remote data source.</p>\n        <p>Available since: <b>1.1.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'>\n</div></li><li><span class='pre'>records</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]<div class='sub-desc'><p>An array of records</p>\n</div></li><li><span class='pre'>successful</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True if the operation was successful.</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-metachange' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-metachange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-metachange' class='name expandable'>metachange</a>( <span class='pre'>this, meta, eOpts</span> )</div><div class='description'><div class='short'>Fires when this store's underlying reader (available via the proxy) provides new metadata. ...</div><div class='long'><p>Fires when this store's underlying reader (available via the proxy) provides new metadata.\nMetadata usually consists of new field definitions, but can include any configuration data\nrequired by an application, and can be processed as needed in the event handler.\nThis event is currently only fired for JsonReaders.</p>\n        <p>Available since: <b>1.1.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'>\n</div></li><li><span class='pre'>meta</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The JSON metadata</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-refresh' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-refresh' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-refresh' class='name expandable'>refresh</a>( <span class='pre'>this, eOpts</span> )</div><div class='description'><div class='short'>Fires when the data cache has changed in a bulk manner (e.g., it has been sorted, filtered, etc.) and a\nwidget that i...</div><div class='long'><p>Fires when the data cache has changed in a bulk manner (e.g., it has been sorted, filtered, etc.) and a\nwidget that is using this Store as a Record cache should refresh its view.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>The data store</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-remove' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-remove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-remove' class='name expandable'>remove</a>( <span class='pre'>store, record, index, isMove, eOpts</span> )</div><div class='description'><div class='short'>Fired when a Model instance has been removed from this Store. ...</div><div class='long'><p>Fired when a Model instance has been removed from this Store.</p>\n\n<p><strong>If many records may be removed in one go, then it is more efficient to listen for the <a href=\"#!/api/Ext.data.AbstractStore-event-bulkremove\" rel=\"Ext.data.AbstractStore-event-bulkremove\" class=\"docClass\">bulkremove</a> event\nand perform any processing for a bulk remove than to listen for this <a href=\"#!/api/Ext.data.AbstractStore-event-remove\" rel=\"Ext.data.AbstractStore-event-remove\" class=\"docClass\">remove</a> event.</strong></p>\n        <p>Available since: <b>1.1.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>store</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>The Store object</p>\n</div></li><li><span class='pre'>record</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The record that was removed</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The index of the record that was removed</p>\n</div></li><li><span class='pre'>isMove</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p><code>true</code> if the child node is being removed so it can be moved to another position in this Store.</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-update' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-update' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-update' class='name expandable'>update</a>( <span class='pre'>this, record, operation, eOpts</span> )</div><div class='description'><div class='short'>Fires when a Model instance has been updated. ...</div><div class='long'><p>Fires when a Model instance has been updated.</p>\n        <p>Available since: <b>1.1.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'>\n</div></li><li><span class='pre'>record</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The Model instance that was updated</p>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The update operation being performed. Value may be one of:</p>\n\n<pre><code><a href=\"#!/api/Ext.data.Model-static-property-EDIT\" rel=\"Ext.data.Model-static-property-EDIT\" class=\"docClass\">Ext.data.Model.EDIT</a>\n<a href=\"#!/api/Ext.data.Model-static-property-REJECT\" rel=\"Ext.data.Model-static-property-REJECT\" class=\"docClass\">Ext.data.Model.REJECT</a>\n<a href=\"#!/api/Ext.data.Model-static-property-COMMIT\" rel=\"Ext.data.Model-static-property-COMMIT\" class=\"docClass\">Ext.data.Model.COMMIT</a>\n</code></pre>\n\n<p>@param {String[]} modifiedFieldNames Array of field names changed during edit.</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-write' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.AbstractStore'>Ext.data.AbstractStore</span><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-write' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-write' class='name expandable'>write</a>( <span class='pre'>store, operation, eOpts</span> )</div><div class='description'><div class='short'>Fires whenever a successful write has been made via the configured Proxy ...</div><div class='long'><p>Fires whenever a successful write has been made via the configured <a href=\"#!/api/Ext.data.AbstractStore-cfg-proxy\" rel=\"Ext.data.AbstractStore-cfg-proxy\" class=\"docClass\">Proxy</a></p>\n        <p>Available since: <b>3.4.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>store</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>This Store</p>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Operation</a> object that was used in\nthe write</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div></div></div></div></div>","superclasses":["Ext.Base"],"meta":{"author":["Ed Spencer"]},"code_type":"ext_define","requires":["Ext.data.Operation","Ext.data.proxy.Proxy","Ext.util.Filter","Ext.util.MixedCollection"],"html_meta":{"author":null},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"AbstractStore.html#Ext-data-AbstractStore","filename":"AbstractStore.js"}],"linenr":1,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.data.AbstractStore","meta":{},"name":"defaultProxyType","id":"property-defaultProxyType"},{"tagname":"property","owner":"Ext.util.Observable","meta":{"private":true},"name":"eventsSuspended","id":"property-eventsSuspended"},{"tagname":"property","owner":"Ext.data.AbstractStore","meta":{},"name":"filters","id":"property-filters"},{"tagname":"property","owner":"Ext.util.Observable","meta":{"readonly":true},"name":"hasListeners","id":"property-hasListeners"},{"tagname":"property","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"implicitModel","id":"property-implicitModel"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.data.AbstractStore","meta":{"since":"3.4.0"},"name":"isDestroyed","id":"property-isDestroyed"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.util.Observable","meta":{},"name":"isObservable","id":"property-isObservable"},{"tagname":"property","owner":"Ext.util.Sortable","meta":{},"name":"isSortable","id":"property-isSortable"},{"tagname":"property","owner":"Ext.data.AbstractStore","meta":{},"name":"isStore","id":"property-isStore"},{"tagname":"property","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"modelDefaults","id":"property-modelDefaults"},{"tagname":"property","owner":"Ext.data.AbstractStore","meta":{"protected":true},"name":"removed","id":"property-removed"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"},{"tagname":"property","owner":"Ext.util.Sortable","meta":{},"name":"sorters","id":"property-sorters"}],"cfg":[{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{"since":"2.3.0"},"name":"autoLoad","id":"cfg-autoLoad"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"autoSync","id":"cfg-autoSync"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"batchUpdateMode","id":"cfg-batchUpdateMode"},{"tagname":"cfg","owner":"Ext.util.Sortable","meta":{},"name":"defaultSortDirection","id":"cfg-defaultSortDirection"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{"since":"2.3.0"},"name":"fields","id":"cfg-fields"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"filterOnLoad","id":"cfg-filterOnLoad"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"filters","id":"cfg-filters"},{"tagname":"cfg","owner":"Ext.util.Observable","meta":{},"name":"listeners","id":"cfg-listeners"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"model","id":"cfg-model"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"proxy","id":"cfg-proxy"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"remoteFilter","id":"cfg-remoteFilter"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"remoteSort","id":"cfg-remoteSort"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"sortOnLoad","id":"cfg-sortOnLoad"},{"tagname":"cfg","owner":"Ext.util.Sortable","meta":{},"name":"sortRoot","id":"cfg-sortRoot"},{"tagname":"cfg","owner":"Ext.util.Sortable","meta":{},"name":"sorters","id":"cfg-sorters"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"statefulFilters","id":"cfg-statefulFilters"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"storeId","id":"cfg-storeId"}],"css_var":[],"method":[{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"addEvents","id":"method-addEvents"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"addListener","id":"method-addListener"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"addManagedListener","id":"method-addManagedListener"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"since":"3.4.0","private":true},"name":"afterCommit","id":"method-afterCommit"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"since":"3.4.0","private":true},"name":"afterEdit","id":"method-afterEdit"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"since":"3.4.0","private":true},"name":"afterReject","id":"method-afterReject"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"applyState","id":"method-applyState"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"captureArgs","id":"method-captureArgs"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"clearData","id":"method-clearData"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"clearFilter","id":"method-clearFilter"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"clearListeners","id":"method-clearListeners"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"clearManagedListeners","id":"method-clearManagedListeners"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"continueFireEvent","id":"method-continueFireEvent"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"create","id":"method-create"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"createRelayer","id":"method-createRelayer"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"decodeFilters","id":"method-decodeFilters"},{"tagname":"method","owner":"Ext.util.Sortable","meta":{"private":true},"name":"decodeSorters","id":"method-decodeSorters"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"since":"3.4.0","private":true},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"destroyStore","id":"method-destroyStore"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"doSort","id":"method-doSort"},{"tagname":"method","owner":"Ext.util.Sortable","meta":{"private":true},"name":"emptyComparator","id":"method-emptyComparator"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"enableBubble","id":"method-enableBubble"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"filter","id":"method-filter"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"filterBy","id":"method-filterBy"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"filterNew","id":"method-filterNew"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"filterUpdated","id":"method-filterUpdated"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"fireEvent","id":"method-fireEvent"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"fireEventArgs","id":"method-fireEventArgs"},{"tagname":"method","owner":"Ext.util.Sortable","meta":{},"name":"generateComparator","id":"method-generateComparator"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"getBatchListeners","id":"method-getBatchListeners"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"getBubbleParent","id":"method-getBubbleParent"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"getById","id":"method-getById"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"getCount","id":"method-getCount"},{"tagname":"method","owner":"Ext.util.Sortable","meta":{"protected":true},"name":"getFirstSorter","id":"method-getFirstSorter"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"getModifiedRecords","id":"method-getModifiedRecords"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"getNewRecords","id":"method-getNewRecords"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"getProxy","id":"method-getProxy"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"getRemovedRecords","id":"method-getRemovedRecords"},{"tagname":"method","owner":"Ext.util.Sortable","meta":{"private":true},"name":"getSorters","id":"method-getSorters"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"getState","id":"method-getState"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"getUpdatedRecords","id":"method-getUpdatedRecords"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"hasListener","id":"method-hasListener"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.util.Sortable","meta":{},"name":"initSortable","id":"method-initSortable"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"isFiltered","id":"method-isFiltered"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"isLoading","id":"method-isLoading"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"load","id":"method-load"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"mon","id":"method-mon"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"mun","id":"method-mun"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"on","id":"method-on"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onBatchComplete","id":"method-onBatchComplete"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onBatchException","id":"method-onBatchException"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onBatchOperationComplete","id":"method-onBatchOperationComplete"},{"tagname":"method","owner":"Ext.util.Sortable","meta":{"private":true},"name":"onBeforeSort","id":"method-onBeforeSort"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onClassExtended","id":"method-onClassExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onCreateRecords","id":"method-onCreateRecords"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onDestroyRecords","id":"method-onDestroyRecords"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onIdChanged","id":"method-onIdChanged"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onMetaChange","id":"method-onMetaChange"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onProxyWrite","id":"method-onProxyWrite"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onUpdate","id":"method-onUpdate"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onUpdateRecords","id":"method-onUpdateRecords"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"prepareClass","id":"method-prepareClass"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"read","id":"method-read"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"relayEvents","id":"method-relayEvents"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"reload","id":"method-reload"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"removeAll","id":"method-removeAll"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"removeListener","id":"method-removeListener"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"removeManagedListener","id":"method-removeManagedListener"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"removeManagedListenerItem","id":"method-removeManagedListenerItem"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"resumeAutoSync","id":"method-resumeAutoSync"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"resumeEvent","id":"method-resumeEvent"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"resumeEvents","id":"method-resumeEvents"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"deprecated":{"text":"Will be removed in the next major version","version":"4.0.0"}},"name":"save","id":"method-save"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"setProxy","id":"method-setProxy"},{"tagname":"method","owner":"Ext.util.Sortable","meta":{},"name":"sort","id":"method-sort"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"suspendAutoSync","id":"method-suspendAutoSync"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"suspendEvent","id":"method-suspendEvent"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"suspendEvents","id":"method-suspendEvents"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"sync","id":"method-sync"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"un","id":"method-un"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"update","id":"method-update"}],"event":[{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"add","id":"event-add"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"beforeload","id":"event-beforeload"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{},"name":"beforesync","id":"event-beforesync"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{},"name":"bulkremove","id":"event-bulkremove"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"clear","id":"event-clear"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"datachanged","id":"event-datachanged"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"load","id":"event-load"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"metachange","id":"event-metachange"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{},"name":"refresh","id":"event-refresh"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"remove","id":"event-remove"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"update","id":"event-update"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"3.4.0"},"name":"write","id":"event-write"}],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.data.AbstractStore","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.data.AbstractStore","mixins":["Ext.util.Observable","Ext.util.Sortable"],"mixedInto":[]});