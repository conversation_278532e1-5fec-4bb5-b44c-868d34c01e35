Ext.data.JsonP.Ext_data_DirectStore({"alternateClassNames":[],"aliases":{"store":["direct"]},"enum":null,"parentMixins":["Ext.util.Observable","Ext.util.Sortable"],"tagname":"class","subclasses":[],"extends":"Ext.data.Store","uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='docClass'>Ext.data.AbstractStore</a><div class='subclass '><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='docClass'>Ext.data.Store</a><div class='subclass '><strong>Ext.data.DirectStore</strong></div></div></div></div><h4>Inherited mixins</h4><div class='dependency'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='docClass'>Ext.util.Observable</a></div><div class='dependency'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='docClass'>Ext.util.Sortable</a></div><h4>Requires</h4><div class='dependency'><a href='#!/api/Ext.data.proxy.Direct' rel='Ext.data.proxy.Direct' class='docClass'>Ext.data.proxy.Direct</a></div><h4>Files</h4><div class='dependency'><a href='source/DirectStore.html#Ext-data-DirectStore' target='_blank'>DirectStore.js</a></div></pre><div class='doc-contents'><p>Small helper class to create an <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a> configured with an <a href=\"#!/api/Ext.data.proxy.Direct\" rel=\"Ext.data.proxy.Direct\" class=\"docClass\">Ext.data.proxy.Direct</a>\nand <a href=\"#!/api/Ext.data.reader.Json\" rel=\"Ext.data.reader.Json\" class=\"docClass\">Ext.data.reader.Json</a> to make interacting with an <a href=\"#!/api/Ext.direct.Manager\" rel=\"Ext.direct.Manager\" class=\"docClass\">Ext.direct.Manager</a> server-side\n<a href=\"#!/api/Ext.direct.Provider\" rel=\"Ext.direct.Provider\" class=\"docClass\">Provider</a> easier. To create a different proxy/reader combination create a basic\n<a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a> configured as needed.</p>\n\n<p><strong>Note:</strong> Although they are not listed, this class inherits all of the config options of:</p>\n\n<ul>\n<li><p><strong><a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Store</a></strong></p></li>\n<li><p><strong><a href=\"#!/api/Ext.data.reader.Json\" rel=\"Ext.data.reader.Json\" class=\"docClass\">JsonReader</a></strong></p>\n\n<ul>\n<li><strong><a href=\"#!/api/Ext.data.reader.Json-cfg-root\" rel=\"Ext.data.reader.Json-cfg-root\" class=\"docClass\">root</a></strong></li>\n<li><strong><a href=\"#!/api/Ext.data.reader.Json-cfg-idProperty\" rel=\"Ext.data.reader.Json-cfg-idProperty\" class=\"docClass\">idProperty</a></strong></li>\n<li><strong><a href=\"#!/api/Ext.data.reader.Json-cfg-totalProperty\" rel=\"Ext.data.reader.Json-cfg-totalProperty\" class=\"docClass\">totalProperty</a></strong></li>\n</ul>\n</li>\n<li><p><strong><a href=\"#!/api/Ext.data.proxy.Direct\" rel=\"Ext.data.proxy.Direct\" class=\"docClass\">DirectProxy</a></strong></p>\n\n<ul>\n<li><strong><a href=\"#!/api/Ext.data.proxy.Direct-cfg-directFn\" rel=\"Ext.data.proxy.Direct-cfg-directFn\" class=\"docClass\">directFn</a></strong></li>\n<li><strong><a href=\"#!/api/Ext.data.proxy.Direct-cfg-paramOrder\" rel=\"Ext.data.proxy.Direct-cfg-paramOrder\" class=\"docClass\">paramOrder</a></strong></li>\n<li><strong><a href=\"#!/api/Ext.data.proxy.Direct-cfg-paramsAsHash\" rel=\"Ext.data.proxy.Direct-cfg-paramsAsHash\" class=\"docClass\">paramsAsHash</a></strong></li>\n</ul>\n</li>\n</ul>\n\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-cfg'>Config options</h3><div class='subsection'><div id='cfg-autoDestroy' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-cfg-autoDestroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-cfg-autoDestroy' class='name expandable'>autoDestroy</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>When a Store is used by only one DataView, and should only exist for the lifetime of that view, then\nconfigure the au...</div><div class='long'><p>When a Store is used by only one <a href=\"#!/api/Ext.view.View\" rel=\"Ext.view.View\" class=\"docClass\">DataView</a>, and should only exist for the lifetime of that view, then\nconfigure the autoDestroy flag as <code>true</code>. This causes the destruction of the view to trigger the destruction of its Store.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-autoLoad' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-autoLoad' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-autoLoad' class='name expandable'>autoLoad</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>If data is not specified, and if autoLoad is true or an Object, this store's load method is automatically called\nafte...</div><div class='long'><p>If data is not specified, and if autoLoad is true or an Object, this store's load method is automatically called\nafter creation. If the value of autoLoad is an Object, this Object will be passed to the store's load method.</p>\n        <p>Available since: <b>2.3.0</b></p>\n</div></div></div><div id='cfg-autoSync' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-autoSync' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-autoSync' class='name expandable'>autoSync</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True to automatically sync the Store with its Proxy after every edit to one of its Records. ...</div><div class='long'><p>True to automatically sync the Store with its Proxy after every edit to one of its Records. Defaults to false.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-batchUpdateMode' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-batchUpdateMode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-batchUpdateMode' class='name expandable'>batchUpdateMode</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Sets the updating behavior based on batch synchronization. ...</div><div class='long'><p>Sets the updating behavior based on batch synchronization. 'operation' (the default) will update the Store's\ninternal representation of the data after each operation of the batch has completed, 'complete' will wait until\nthe entire batch has been completed before updating the Store's data. 'complete' is a good choice for local\nstorage proxies, 'operation' is better for remote proxies, where there is a comparatively high latency.</p>\n<p>Defaults to: <code>'operation'</code></p></div></div></div><div id='cfg-buffered' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-cfg-buffered' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-cfg-buffered' class='name expandable'>buffered</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Allows the Store to prefetch and cache in a page cache, pages of Records, and to then satisfy\nloading requirements fr...</div><div class='long'><p>Allows the Store to prefetch and cache in a <strong>page cache</strong>, pages of Records, and to then satisfy\nloading requirements from this page cache.</p>\n\n<p>To use buffered Stores, initiate the process by loading the first page. The number of rows rendered are\ndetermined automatically, and the range of pages needed to keep the cache primed for scrolling is\nrequested and cached.\nExample:</p>\n\n<pre><code>myStore.loadPage(1); // Load page 1\n</code></pre>\n\n<p>A <a href=\"#!/api/Ext.grid.plugin.BufferedRenderer\" rel=\"Ext.grid.plugin.BufferedRenderer\" class=\"docClass\">BufferedRenderer</a> is instantiated which will monitor the scrolling in the grid, and\nrefresh the view's rows from the page cache as needed. It will also pull new data into the page\ncache when scrolling of the view draws upon data near either end of the prefetched data.</p>\n\n<p>The margins which trigger view refreshing from the prefetched data are <a href=\"#!/api/Ext.grid.plugin.BufferedRenderer-cfg-numFromEdge\" rel=\"Ext.grid.plugin.BufferedRenderer-cfg-numFromEdge\" class=\"docClass\">Ext.grid.plugin.BufferedRenderer.numFromEdge</a>,\n<a href=\"#!/api/Ext.grid.plugin.BufferedRenderer-cfg-leadingBufferZone\" rel=\"Ext.grid.plugin.BufferedRenderer-cfg-leadingBufferZone\" class=\"docClass\">Ext.grid.plugin.BufferedRenderer.leadingBufferZone</a> and <a href=\"#!/api/Ext.grid.plugin.BufferedRenderer-cfg-trailingBufferZone\" rel=\"Ext.grid.plugin.BufferedRenderer-cfg-trailingBufferZone\" class=\"docClass\">Ext.grid.plugin.BufferedRenderer.trailingBufferZone</a>.</p>\n\n<p>The margins which trigger loading more data into the page cache are, <a href=\"#!/api/Ext.data.Store-cfg-leadingBufferZone\" rel=\"Ext.data.Store-cfg-leadingBufferZone\" class=\"docClass\">leadingBufferZone</a> and\n<a href=\"#!/api/Ext.data.Store-cfg-trailingBufferZone\" rel=\"Ext.data.Store-cfg-trailingBufferZone\" class=\"docClass\">trailingBufferZone</a>.</p>\n\n<p>By default, only 5 pages of data are cached in the page cache, with pages \"scrolling\" out of the buffer\nas the view moves down through the dataset.\nSetting this value to zero means that no pages are <em>ever</em> scrolled out of the page cache, and\nthat eventually the whole dataset may become present in the page cache. This is sometimes desirable\nas long as datasets do not reach astronomical proportions.</p>\n\n<p>Selection state may be maintained across page boundaries by configuring the SelectionModel not to discard\nrecords from its collection when those Records cycle out of the Store's primary collection. This is done\nby configuring the SelectionModel like this:</p>\n\n<pre><code>selModel: {\n    pruneRemoved: false\n}\n</code></pre>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-clearOnPageLoad' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-cfg-clearOnPageLoad' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-cfg-clearOnPageLoad' class='name expandable'>clearOnPageLoad</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True to empty the store when loading another page via loadPage,\nnextPage or previousPage. ...</div><div class='long'><p>True to empty the store when loading another page via <a href=\"#!/api/Ext.data.Store-method-loadPage\" rel=\"Ext.data.Store-method-loadPage\" class=\"docClass\">loadPage</a>,\n<a href=\"#!/api/Ext.data.Store-method-nextPage\" rel=\"Ext.data.Store-method-nextPage\" class=\"docClass\">nextPage</a> or <a href=\"#!/api/Ext.data.Store-method-previousPage\" rel=\"Ext.data.Store-method-previousPage\" class=\"docClass\">previousPage</a>. Setting to false keeps existing records, allowing\nlarge data sets to be loaded one page at a time but rendered all together.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-clearRemovedOnLoad' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-cfg-clearRemovedOnLoad' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-cfg-clearRemovedOnLoad' class='name expandable'>clearRemovedOnLoad</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true to clear anything in the removed record collection when the store loads. ...</div><div class='long'><p><code>true</code> to clear anything in the <a href=\"#!/api/Ext.data.Store-property-removed\" rel=\"Ext.data.Store-property-removed\" class=\"docClass\">removed</a> record collection when the store loads.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-data' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-cfg-data' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-cfg-data' class='name expandable'>data</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]/<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</span></div><div class='description'><div class='short'>Array of Model instances or data objects to load locally. ...</div><div class='long'><p>Array of Model instances or data objects to load locally. See \"Inline data\" above for details.</p>\n</div></div></div><div id='cfg-defaultSortDirection' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-cfg-defaultSortDirection' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-cfg-defaultSortDirection' class='name expandable'>defaultSortDirection</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The default sort direction to use if one is not specified. ...</div><div class='long'><p>The default sort direction to use if one is not specified.</p>\n<p>Defaults to: <code>&quot;ASC&quot;</code></p></div></div></div><div id='cfg-fields' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-fields' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-fields' class='name expandable'>fields</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]</span></div><div class='description'><div class='short'>This may be used in place of specifying a model configuration. ...</div><div class='long'><p>This may be used in place of specifying a <a href=\"#!/api/Ext.data.AbstractStore-cfg-model\" rel=\"Ext.data.AbstractStore-cfg-model\" class=\"docClass\">model</a> configuration. The fields should be a\nset of <a href=\"#!/api/Ext.data.Field\" rel=\"Ext.data.Field\" class=\"docClass\">Ext.data.Field</a> configuration objects. The store will automatically create a <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>\nwith these fields. In general this configuration option should only be used for simple stores like\na two-field store of ComboBox. For anything more complicated, such as specifying a particular id property or\nassociations, a <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a> should be defined and specified for the <a href=\"#!/api/Ext.data.AbstractStore-cfg-model\" rel=\"Ext.data.AbstractStore-cfg-model\" class=\"docClass\">model</a>\nconfig.</p>\n        <p>Available since: <b>2.3.0</b></p>\n</div></div></div><div id='cfg-filterOnLoad' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-filterOnLoad' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-filterOnLoad' class='name expandable'>filterOnLoad</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>If true, any filters attached to this Store will be run after loading data, before the datachanged event is fired. ...</div><div class='long'><p>If true, any filters attached to this Store will be run after loading data, before the datachanged event is fired.\nDefaults to true, ignored if <a href=\"#!/api/Ext.data.Store-cfg-remoteFilter\" rel=\"Ext.data.Store-cfg-remoteFilter\" class=\"docClass\">remoteFilter</a> is true</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-filters' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-filters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-filters' class='name expandable'>filters</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]/<a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a>[]</span></div><div class='description'><div class='short'>Array of Filters for this store. ...</div><div class='long'><p>Array of <a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Filters</a> for this store. Can also be passed array of\nfunctions which will be used as the <a href=\"#!/api/Ext.util.Filter-cfg-filterFn\" rel=\"Ext.util.Filter-cfg-filterFn\" class=\"docClass\">filterFn</a> config\nfor filters:</p>\n\n<pre><code>filters: [\n    function(item) {\n        return item.weight &gt; 0;\n    }\n]\n</code></pre>\n\n<p>To filter after the grid is loaded use the <a href=\"#!/api/Ext.data.Store-method-filterBy\" rel=\"Ext.data.Store-method-filterBy\" class=\"docClass\">filterBy</a> function.</p>\n</div></div></div><div id='cfg-groupDir' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-cfg-groupDir' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-cfg-groupDir' class='name expandable'>groupDir</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The direction in which sorting should be applied when grouping. ...</div><div class='long'><p>The direction in which sorting should be applied when grouping. Supported values are \"ASC\" and \"DESC\".</p>\n<p>Defaults to: <code>&quot;ASC&quot;</code></p></div></div></div><div id='cfg-groupField' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-cfg-groupField' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-cfg-groupField' class='name expandable'>groupField</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The field by which to group data in the store. ...</div><div class='long'><p>The field by which to group data in the store. Internally, grouping is very similar to sorting - the\ngroupField and <a href=\"#!/api/Ext.data.Store-cfg-groupDir\" rel=\"Ext.data.Store-cfg-groupDir\" class=\"docClass\">groupDir</a> are injected as the first sorter (see <a href=\"#!/api/Ext.data.Store-method-sort\" rel=\"Ext.data.Store-method-sort\" class=\"docClass\">sort</a>). Stores support a single\nlevel of grouping, and groups can be fetched via the <a href=\"#!/api/Ext.data.Store-method-getGroups\" rel=\"Ext.data.Store-method-getGroups\" class=\"docClass\">getGroups</a> method.</p>\n</div></div></div><div id='cfg-groupers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-cfg-groupers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-cfg-groupers' class='name not-expandable'>groupers</a><span> : <a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">Ext.util.MixedCollection</a></span></div><div class='description'><div class='short'><p>The collection of <a href=\"#!/api/Ext.util.Grouper\" rel=\"Ext.util.Grouper\" class=\"docClass\">Groupers</a> currently applied to this Store.</p>\n</div><div class='long'><p>The collection of <a href=\"#!/api/Ext.util.Grouper\" rel=\"Ext.util.Grouper\" class=\"docClass\">Groupers</a> currently applied to this Store.</p>\n</div></div></div><div id='cfg-leadingBufferZone' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-cfg-leadingBufferZone' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-cfg-leadingBufferZone' class='name expandable'>leadingBufferZone</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>When buffered, the number of extra rows to keep cached on the leading side of scrolling buffer\nas scrolling proceeds. ...</div><div class='long'><p>When <a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">buffered</a>, the number of extra rows to keep cached on the leading side of scrolling buffer\nas scrolling proceeds. A larger number means fewer replenishments from the server.</p>\n<p>Defaults to: <code>200</code></p></div></div></div><div id='cfg-listeners' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-cfg-listeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-cfg-listeners' class='name expandable'>listeners</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>A config object containing one or more event handlers to be added to this object during initialization. ...</div><div class='long'><p>A config object containing one or more event handlers to be added to this object during initialization. This\nshould be a valid listeners config object as specified in the <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> example for attaching multiple\nhandlers at once.</p>\n\n<p><strong>DOM events from Ext JS <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a></strong></p>\n\n<p>While <em>some</em> Ext JS Component classes export selected DOM events (e.g. \"click\", \"mouseover\" etc), this is usually\nonly done when extra value can be added. For example the <a href=\"#!/api/Ext.view.View\" rel=\"Ext.view.View\" class=\"docClass\">DataView</a>'s <strong><code><a href=\"#!/api/Ext.view.View-event-itemclick\" rel=\"Ext.view.View-event-itemclick\" class=\"docClass\">itemclick</a></code></strong> event passing the node clicked on. To access DOM events directly from a\nchild element of a Component, we need to specify the <code>element</code> option to identify the Component property to add a\nDOM listener to:</p>\n\n<pre><code>new <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>({\n    width: 400,\n    height: 200,\n    dockedItems: [{\n        xtype: 'toolbar'\n    }],\n    listeners: {\n        click: {\n            element: 'el', //bind to the underlying el property on the panel\n            fn: function(){ console.log('click el'); }\n        },\n        dblclick: {\n            element: 'body', //bind to the underlying body property on the panel\n            fn: function(){ console.log('dblclick body'); }\n        }\n    }\n});\n</code></pre>\n</div></div></div><div id='cfg-model' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-model' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-model' class='name expandable'>model</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Name of the Model associated with this store. ...</div><div class='long'><p>Name of the <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Model</a> associated with this store.\nThe string is used as an argument for <a href=\"#!/api/Ext.ModelManager-method-getModel\" rel=\"Ext.ModelManager-method-getModel\" class=\"docClass\">Ext.ModelManager.getModel</a>.</p>\n</div></div></div><div id='cfg-pageSize' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-cfg-pageSize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-cfg-pageSize' class='name expandable'>pageSize</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The number of records considered to form a 'page'. ...</div><div class='long'><p>The number of records considered to form a 'page'. This is used to power the built-in\npaging using the nextPage and previousPage functions when the grid is paged using a\n<a href=\"#!/api/Ext.toolbar.Paging\" rel=\"Ext.toolbar.Paging\" class=\"docClass\">PagingToolbar</a> Defaults to 25.</p>\n\n<p>If this Store is <a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">buffered</a>, pages are loaded into a page cache before the Store's\ndata is updated from the cache. The pageSize is the number of rows loaded into the cache in one request.\nThis will not affect the rendering of a buffered grid, but a larger page size will mean fewer loads.</p>\n\n<p>In a buffered grid, scrolling is monitored, and the page cache is kept primed with data ahead of the\ndirection of scroll to provide rapid access to data when scrolling causes it to be required. Several pages\nin advance may be requested depending on various parameters.</p>\n\n<p>It is recommended to tune the <a href=\"#!/api/Ext.data.Store-cfg-pageSize\" rel=\"Ext.data.Store-cfg-pageSize\" class=\"docClass\">pageSize</a>, <a href=\"#!/api/Ext.data.Store-cfg-trailingBufferZone\" rel=\"Ext.data.Store-cfg-trailingBufferZone\" class=\"docClass\">trailingBufferZone</a> and\n<a href=\"#!/api/Ext.data.Store-cfg-leadingBufferZone\" rel=\"Ext.data.Store-cfg-leadingBufferZone\" class=\"docClass\">leadingBufferZone</a> configurations based upon the conditions pertaining in your deployed application.</p>\n\n<p>The provided SDK example <code>examples/grid/infinite-scroll-grid-tuner.html</code> can be used to experiment with\ndifferent settings including simulating Ajax latency.</p>\n</div></div></div><div id='cfg-proxy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-cfg-proxy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-cfg-proxy' class='name expandable'>proxy</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>The Proxy to use for this Store. ...</div><div class='long'><p>The Proxy to use for this Store. This can be either a string, a config object or a Proxy instance -\nsee <a href=\"#!/api/Ext.data.Store-method-setProxy\" rel=\"Ext.data.Store-method-setProxy\" class=\"docClass\">setProxy</a> for details.</p>\n<p>Overrides: <a href='#!/api/Ext.data.AbstractStore-cfg-proxy' rel='Ext.data.AbstractStore-cfg-proxy' class='docClass'>Ext.data.AbstractStore.proxy</a></p></div></div></div><div id='cfg-purgePageCount' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-cfg-purgePageCount' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-cfg-purgePageCount' class='name expandable'>purgePageCount</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Valid only when used with a buffered Store. ...</div><div class='long'><p><em>Valid only when used with a <a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">buffered</a> Store.</em></p>\n\n<p>The number of pages <em>additional to the required buffered range</em> to keep in the prefetch cache before purging least recently used records.</p>\n\n<p>For example, if the height of the view area and the configured <a href=\"#!/api/Ext.data.Store-cfg-trailingBufferZone\" rel=\"Ext.data.Store-cfg-trailingBufferZone\" class=\"docClass\">trailingBufferZone</a> and <a href=\"#!/api/Ext.data.Store-cfg-leadingBufferZone\" rel=\"Ext.data.Store-cfg-leadingBufferZone\" class=\"docClass\">leadingBufferZone</a> require that there\nare three pages in the cache, then a <code>purgePageCount</code> of 5 ensures that up to 8 pages can be in the page cache any any one time.</p>\n\n<p>A value of 0 indicates to never purge the prefetched data.</p>\n<p>Defaults to: <code>5</code></p></div></div></div><div id='cfg-remoteFilter' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-cfg-remoteFilter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-cfg-remoteFilter' class='name expandable'>remoteFilter</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true if the grouping should be performed on the server side, false if it is local only. ...</div><div class='long'><p><code>true</code> if the grouping should be performed on the server side, false if it is local only.</p>\n\n<p><a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">Buffered</a> stores automatically set this to <code>true</code>. Buffered stores contain an abitrary\nsubset of the full dataset which depends upon various configurations and which pages have been requested\nfor rendering. Such <em>sparse</em> datasets are ineligible for local filtering.</p>\n<p>Defaults to: <code>false</code></p><p>Overrides: <a href='#!/api/Ext.data.AbstractStore-cfg-remoteFilter' rel='Ext.data.AbstractStore-cfg-remoteFilter' class='docClass'>Ext.data.AbstractStore.remoteFilter</a></p></div></div></div><div id='cfg-remoteGroup' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-cfg-remoteGroup' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-cfg-remoteGroup' class='name expandable'>remoteGroup</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true if the grouping should apply on the server side, false if it is local only. ...</div><div class='long'><p><code>true</code> if the grouping should apply on the server side, false if it is local only.  If the\ngrouping is local, it can be applied immediately to the data.  If it is remote, then it will simply act as a\nhelper, automatically sending the grouping information to the server.</p>\n\n<p><a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">Buffered</a> stores automatically set this to <code>true</code>. Buffered stores contain an abitrary\nsubset of the full dataset which depends upon various configurations and which pages have been requested\nfor rendering. Such <em>sparse</em> datasets are ineligible for local grouping.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-remoteSort' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-cfg-remoteSort' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-cfg-remoteSort' class='name expandable'>remoteSort</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true if the sorting should be performed on the server side, false if it is local only. ...</div><div class='long'><p><code>true</code> if the sorting should be performed on the server side, false if it is local only.</p>\n\n<p><a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">Buffered</a> stores automatically set this to <code>true</code>. Buffered stores contain an abitrary\nsubset of the full dataset which depends upon various configurations and which pages have been requested\nfor rendering. Such <em>sparse</em> datasets are ineligible for local sorting.</p>\n<p>Defaults to: <code>false</code></p><p>Overrides: <a href='#!/api/Ext.data.AbstractStore-cfg-remoteSort' rel='Ext.data.AbstractStore-cfg-remoteSort' class='docClass'>Ext.data.AbstractStore.remoteSort</a></p></div></div></div><div id='cfg-sortOnFilter' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-cfg-sortOnFilter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-cfg-sortOnFilter' class='name expandable'>sortOnFilter</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>For local filtering only, causes sort to be called whenever filter is called,\ncausing the sorters to be reapplied aft...</div><div class='long'><p>For local filtering only, causes <a href=\"#!/api/Ext.data.Store-method-sort\" rel=\"Ext.data.Store-method-sort\" class=\"docClass\">sort</a> to be called whenever <a href=\"#!/api/Ext.data.Store-method-filter\" rel=\"Ext.data.Store-method-filter\" class=\"docClass\">filter</a> is called,\ncausing the sorters to be reapplied after filtering.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-sortOnLoad' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-sortOnLoad' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-sortOnLoad' class='name expandable'>sortOnLoad</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>If true, any sorters attached to this Store will be run after loading data, before the datachanged event is fired. ...</div><div class='long'><p>If true, any sorters attached to this Store will be run after loading data, before the datachanged event is fired.\nDefaults to true, igored if <a href=\"#!/api/Ext.data.Store-cfg-remoteSort\" rel=\"Ext.data.Store-cfg-remoteSort\" class=\"docClass\">remoteSort</a> is true</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-sortRoot' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-cfg-sortRoot' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-cfg-sortRoot' class='name not-expandable'>sortRoot</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'><p>The property in each item that contains the data to sort.</p>\n</div><div class='long'><p>The property in each item that contains the data to sort.</p>\n</div></div></div><div id='cfg-sorters' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-cfg-sorters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-cfg-sorters' class='name not-expandable'>sorters</a><span> : <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a>[]/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]</span></div><div class='description'><div class='short'><p>The initial set of <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Sorters</a></p>\n</div><div class='long'><p>The initial set of <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Sorters</a></p>\n</div></div></div><div id='cfg-statefulFilters' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-statefulFilters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-statefulFilters' class='name expandable'>statefulFilters</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Configure as true to have the filters saved when a client grid saves its state. ...</div><div class='long'><p>Configure as <code>true</code> to have the filters saved when a client <a href=\"#!/api/Ext.grid.Panel\" rel=\"Ext.grid.Panel\" class=\"docClass\">grid</a> saves its state.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-storeId' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-cfg-storeId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-cfg-storeId' class='name expandable'>storeId</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Unique identifier for this store. ...</div><div class='long'><p>Unique identifier for this store. If present, this Store will be registered with the <a href=\"#!/api/Ext.data.StoreManager\" rel=\"Ext.data.StoreManager\" class=\"docClass\">Ext.data.StoreManager</a>,\nmaking it easy to reuse elsewhere.</p>\n\n<p>Note that when store is instatiated by Controller, the storeId will be overridden by the name of the store.</p>\n</div></div></div><div id='cfg-trailingBufferZone' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-cfg-trailingBufferZone' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-cfg-trailingBufferZone' class='name expandable'>trailingBufferZone</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>When buffered, the number of extra records to keep cached on the trailing side of scrolling buffer\nas scrolling proce...</div><div class='long'><p>When <a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">buffered</a>, the number of extra records to keep cached on the trailing side of scrolling buffer\nas scrolling proceeds. A larger number means fewer replenishments from the server.</p>\n<p>Defaults to: <code>25</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-addRecordsOptions' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-property-addRecordsOptions' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-property-addRecordsOptions' class='name expandable'>addRecordsOptions</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Private. ...</div><div class='long'><p>Private. Used as parameter to loadRecords</p>\n<p>Defaults to: <code>{addRecords: true}</code></p></div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-currentPage' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-property-currentPage' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-property-currentPage' class='name expandable'>currentPage</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The page that the Store has most recently loaded (see loadPage) ...</div><div class='long'><p>The page that the Store has most recently loaded (see <a href=\"#!/api/Ext.data.Store-method-loadPage\" rel=\"Ext.data.Store-method-loadPage\" class=\"docClass\">loadPage</a>)</p>\n<p>Defaults to: <code>1</code></p></div></div></div><div id='property-data' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-property-data' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-property-data' class='name expandable'>data</a><span> : Ext.util.MixedCollection/Ext.data.Store.PageMap</span></div><div class='description'><div class='short'>When this Store is not buffered, the data property is a MixedCollection which holds this store's local cache of records. ...</div><div class='long'><p>When this Store is not <a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">buffered</a>, the <code>data</code> property is a MixedCollection which holds this store's local cache of records.</p>\n\n<p>When this store <em>is</em> <a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">buffered</a>, the <code>data</code> property is a cache of <em>pages</em> of records used to satisfy load requests from the Store when the associated view\nscrolls. Depending on how the <a href=\"#!/api/Ext.data.Store-cfg-leadingBufferZone\" rel=\"Ext.data.Store-cfg-leadingBufferZone\" class=\"docClass\">buffer zone</a> and <a href=\"#!/api/Ext.data.Store-cfg-purgePageCount\" rel=\"Ext.data.Store-cfg-purgePageCount\" class=\"docClass\">purgePageCount</a> are configured,\npages which are scrolled out of view may be evicted from the cache, and need to be re-requested from the server\nwhen scrolled back into view. For this reason, if using <a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">buffered</a>, it is recommended that you configure\nyour Model definitions with a unique <a href=\"#!/api/Ext.data.Model-cfg-idProperty\" rel=\"Ext.data.Model-cfg-idProperty\" class=\"docClass\">Ext.data.Model.idProperty</a> so that records which return to the page\ncache may be matched against previously selected records.</p>\n\n<p>Pages in the direction of scroll are prefetched from the remote server and loaded into this cache <em>before</em>\nthey are needed based upon the <a href=\"#!/api/Ext.data.Store-cfg-leadingBufferZone\" rel=\"Ext.data.Store-cfg-leadingBufferZone\" class=\"docClass\">buffer zone</a> so that scrolling can proceed without visible pauses for data loading.</p>\n</div></div></div><div id='property-defaultPageSize' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-property-defaultPageSize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-property-defaultPageSize' class='name expandable'>defaultPageSize</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>25</code></p></div></div></div><div id='property-defaultProxyType' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-property-defaultProxyType' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-property-defaultProxyType' class='name expandable'>defaultProxyType</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The string type of the Proxy to create if none is specified. ...</div><div class='long'><p>The string type of the Proxy to create if none is specified. This defaults to creating a\n<a href=\"#!/api/Ext.data.proxy.Memory\" rel=\"Ext.data.proxy.Memory\" class=\"docClass\">memory proxy</a>.</p>\n<p>Defaults to: <code>'memory'</code></p></div></div></div><div id='property-defaultViewSize' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-property-defaultViewSize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-property-defaultViewSize' class='name expandable'>defaultViewSize</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Number of records to load into a buffered grid before it has been bound to a view of known size ...</div><div class='long'><p>Number of records to load into a buffered grid before it has been bound to a view of known size</p>\n<p>Defaults to: <code>100</code></p></div></div></div><div id='property-eventsSuspended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-property-eventsSuspended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-property-eventsSuspended' class='name expandable'>eventsSuspended</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Initial suspended call count. ...</div><div class='long'><p>Initial suspended call count. Incremented when <a href=\"#!/api/Ext.util.Observable-method-suspendEvents\" rel=\"Ext.util.Observable-method-suspendEvents\" class=\"docClass\">suspendEvents</a> is called, decremented when <a href=\"#!/api/Ext.util.Observable-method-resumeEvents\" rel=\"Ext.util.Observable-method-resumeEvents\" class=\"docClass\">resumeEvents</a> is called.</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-filters' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-property-filters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-property-filters' class='name not-expandable'>filters</a><span> : <a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">Ext.util.MixedCollection</a></span></div><div class='description'><div class='short'><p>The collection of <a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Filters</a> currently applied to this Store</p>\n</div><div class='long'><p>The collection of <a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Filters</a> currently applied to this Store</p>\n</div></div></div><div id='property-hasListeners' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-property-hasListeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-property-hasListeners' class='name expandable'>hasListeners</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='readonly signature' >readonly</strong></div><div class='description'><div class='short'>This object holds a key for any event that has a listener. ...</div><div class='long'><p>This object holds a key for any event that has a listener. The listener may be set\ndirectly on the instance, or on its class or a super class (via <a href=\"#!/api/Ext.util.Observable-static-method-observe\" rel=\"Ext.util.Observable-static-method-observe\" class=\"docClass\">observe</a>) or\non the <a href=\"#!/api/Ext.app.EventBus\" rel=\"Ext.app.EventBus\" class=\"docClass\">MVC EventBus</a>. The values of this object are truthy\n(a non-zero number) and falsy (0 or undefined). They do not represent an exact count\nof listeners. The value for an event is truthy if the event must be fired and is\nfalsy if there is no need to fire the event.</p>\n\n<p>The intended use of this property is to avoid the expense of fireEvent calls when\nthere are no listeners. This can be particularly helpful when one would otherwise\nhave to call fireEvent hundreds or thousands of times. It is used like this:</p>\n\n<pre><code> if (this.hasListeners.foo) {\n     this.fireEvent('foo', this, arg1);\n }\n</code></pre>\n</div></div></div><div id='property-implicitModel' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-property-implicitModel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-property-implicitModel' class='name expandable'>implicitModel</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>True if a model was created implicitly for this Store. ...</div><div class='long'><p>True if a model was created implicitly for this Store. This happens if a fields array is passed to the Store's\nconstructor instead of a model constructor or name.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-isDestroyed' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-property-isDestroyed' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-property-isDestroyed' class='name expandable'>isDestroyed</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True if the Store has already been destroyed. ...</div><div class='long'><p>True if the Store has already been destroyed. If this is true, the reference to Store should be deleted\nas it will not function correctly any more.</p>\n<p>Defaults to: <code>false</code></p>        <p>Available since: <b>3.4.0</b></p>\n</div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isObservable' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-property-isObservable' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-property-isObservable' class='name expandable'>isObservable</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true in this class to identify an object as an instantiated Observable, or subclass thereof. ...</div><div class='long'><p><code>true</code> in this class to identify an object as an instantiated Observable, or subclass thereof.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isSortable' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-property-isSortable' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-property-isSortable' class='name expandable'>isSortable</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true in this class to identify an object as an instantiated Sortable, or subclass thereof. ...</div><div class='long'><p><code>true</code> in this class to identify an object as an instantiated Sortable, or subclass thereof.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isStore' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-property-isStore' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-property-isStore' class='name expandable'>isStore</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true in this class to identify an object as an instantiated Store, or subclass thereof. ...</div><div class='long'><p><code>true</code> in this class to identify an object as an instantiated Store, or subclass thereof.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-loading' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-property-loading' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-property-loading' class='name expandable'>loading</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>true if the Store is currently loading via its Proxy. ...</div><div class='long'><p><code>true</code> if the Store is currently loading via its Proxy.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-modelDefaults' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-property-modelDefaults' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-property-modelDefaults' class='name expandable'>modelDefaults</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>A set of default values to be applied to every model instance added via insert or created\nvia createModel. ...</div><div class='long'><p>A set of default values to be applied to every model instance added via <a href=\"#!/api/Ext.data.Store-method-insert\" rel=\"Ext.data.Store-method-insert\" class=\"docClass\">insert</a> or created\nvia <a href=\"#!/api/Ext.data.Store-method-createModel\" rel=\"Ext.data.Store-method-createModel\" class=\"docClass\">createModel</a>. This is used internally by associations to set foreign keys and\nother fields. See the Association classes source code for examples. This should not need to be used by application developers.</p>\n</div></div></div><div id='property-removed' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-property-removed' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-property-removed' class='name expandable'>removed</a><span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Temporary cache in which removed model instances are kept until successfully synchronised with a Proxy,\nat which poin...</div><div class='long'><p>Temporary cache in which removed model instances are kept until successfully synchronised with a Proxy,\nat which point this is cleared.</p>\n</div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div><div id='property-snapshot' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-property-snapshot' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-property-snapshot' class='name expandable'>snapshot</a><span> : <a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">Ext.util.MixedCollection</a></span></div><div class='description'><div class='short'>A pristine (unfiltered) collection of the records in this store. ...</div><div class='long'><p>A pristine (unfiltered) collection of the records in this store. This is used to reinstate\nrecords when a filter is removed or changed</p>\n</div></div></div><div id='property-sorters' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-property-sorters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-property-sorters' class='name not-expandable'>sorters</a><span> : <a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">Ext.util.MixedCollection</a></span></div><div class='description'><div class='short'><p>The collection of <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Sorters</a> currently applied to this Store</p>\n</div><div class='long'><p>The collection of <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Sorters</a> currently applied to this Store</p>\n</div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.DirectStore'>Ext.data.DirectStore</span><br/><a href='source/DirectStore.html#Ext-data-DirectStore-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/Ext.data.DirectStore-method-constructor' class='name expandable'>Ext.data.DirectStore</a>( <span class='pre'>[config]</span> ) : <a href=\"#!/api/Ext.data.DirectStore\" rel=\"Ext.data.DirectStore\" class=\"docClass\">Ext.data.DirectStore</a></div><div class='description'><div class='short'>End Definitions\n\nCreates the store. ...</div><div class='long'><p>End Definitions</p>\n\n<p>Creates the store.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Config object.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.DirectStore\" rel=\"Ext.data.DirectStore\" class=\"docClass\">Ext.data.DirectStore</a></span><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.Store-method-constructor' rel='Ext.data.Store-method-constructor' class='docClass'>Ext.data.Store.constructor</a></p></div></div></div><div id='method-add' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-add' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-add' class='name expandable'>add</a>( <span class='pre'>model</span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</div><div class='description'><div class='short'>Adds Model instance to the Store. ...</div><div class='long'><p>Adds Model instance to the Store. This method accepts either:</p>\n\n<ul>\n<li>An array of Model instances or Model configuration objects.</li>\n<li>Any number of Model instance or Model configuration object arguments.</li>\n</ul>\n\n\n<p>The new Model instances will be added at the end of the existing collection.</p>\n\n<p>Sample usage:</p>\n\n<pre><code>myStore.add({some: 'data'}, {some: 'other data'});\n</code></pre>\n\n<p>Note that if this Store is sorted, the new Model instances will be inserted\nat the correct point in the Store to maintain the sort order.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>model</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]/<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>.../<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>...<div class='sub-desc'><p>An array of Model instances\nor Model configuration objects, or variable number of Model instance or config arguments.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</span><div class='sub-desc'><p>The model instances that were added</p>\n</div></li></ul></div></div></div><div id='method-addEvents' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-addEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-addEvents' class='name expandable'>addEvents</a>( <span class='pre'>eventNames</span> )</div><div class='description'><div class='short'>Adds the specified events to the list of events which this Observable may fire. ...</div><div class='long'><p>Adds the specified events to the list of events which this Observable may fire.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventNames</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>...<div class='sub-desc'><p>Either an object with event names as properties with\na value of <code>true</code>. For example:</p>\n\n<pre><code>this.addEvents({\n    storeloaded: true,\n    storecleared: true\n});\n</code></pre>\n\n<p>Or any number of event names as separate parameters. For example:</p>\n\n<pre><code>this.addEvents('storeloaded', 'storecleared');\n</code></pre>\n</div></li></ul></div></div></div><div id='method-addFilter' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-addFilter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-addFilter' class='name expandable'>addFilter</a>( <span class='pre'>filters, [applyFilters]</span> )</div><div class='description'><div class='short'>Adds a new Filter to this Store's filter set and\nby default, applys the updated filter set to the Store's unfiltered ...</div><div class='long'><p>Adds a new Filter to this Store's <a href=\"#!/api/Ext.data.Store-property-filters\" rel=\"Ext.data.Store-property-filters\" class=\"docClass\">filter set</a> and\nby default, applys the updated filter set to the Store's unfiltered dataset.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>filters</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]/<a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Ext.util.Filter</a>[]<div class='sub-desc'><p>The set of filters to add to the current <a href=\"#!/api/Ext.data.Store-property-filters\" rel=\"Ext.data.Store-property-filters\" class=\"docClass\">filter set</a>.</p>\n</div></li><li><span class='pre'>applyFilters</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>Pass as <code>false</code> to add the filter but not apply the updated filter set.</p>\n<p>Defaults to: <code>true</code></p></div></li></ul></div></div></div><div id='method-addListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-addListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-addListener' class='name expandable'>addListener</a>( <span class='pre'>eventName, [fn], [scope], [options]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Appends an event handler to this object. ...</div><div class='long'><p>Appends an event handler to this object.  For example:</p>\n\n<pre><code>myGridPanel.on(\"mouseover\", this.onMouseOver, this);\n</code></pre>\n\n<p>The method also allows for a single argument to be passed which is a config object\ncontaining properties which specify multiple events. For example:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: this.onCellClick,\n    mouseover: this.onMouseOver,\n    mouseout: this.onMouseOut,\n    scope: this // Important. Ensure \"this\" is correct during handler execution\n});\n</code></pre>\n\n<p>One can also specify options for each event handler separately:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: this.onCellClick, scope: this, single: true},\n    mouseover: {fn: panel.onMouseOver, scope: panel}\n});\n</code></pre>\n\n<p><em>Names</em> of methods in a specified scope may also be used. Note that\n<code>scope</code> MUST be specified to use this option:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: 'onCellClick', scope: this, single: true},\n    mouseover: {fn: 'onMouseOver', scope: panel}\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The name of the event to listen for.\nMay also be an object who's property names are event names.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The method the event invokes, or <em>if <code>scope</code> is specified, the </em>name* of the method within\nthe specified <code>scope</code>.  Will be called with arguments\ngiven to <a href=\"#!/api/Ext.util.Observable-method-fireEvent\" rel=\"Ext.util.Observable-method-fireEvent\" class=\"docClass\">fireEvent</a> plus the <code>options</code> parameter described below.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is\nexecuted. <strong>If omitted, defaults to the object which fired the event.</strong></p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>An object containing handler configuration.</p>\n\n\n\n\n<p><strong>Note:</strong> Unlike in ExtJS 3.x, the options object will also be passed as the last\nargument to every event handler.</p>\n\n\n\n\n<p>This object may contain any of the following properties:</p>\n\n<ul><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is executed. <strong>If omitted,\n  defaults to the object which fired the event.</strong></p>\n\n</div></li><li><span class='pre'>delay</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of milliseconds to delay the invocation of the handler after the event fires.</p>\n\n</div></li><li><span class='pre'>single</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to add a handler to handle just the next firing of the event, and then remove itself.</p>\n\n</div></li><li><span class='pre'>buffer</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Causes the handler to be scheduled to run in an <a href=\"#!/api/Ext.util.DelayedTask\" rel=\"Ext.util.DelayedTask\" class=\"docClass\">Ext.util.DelayedTask</a> delayed\n  by the specified number of milliseconds. If the event fires again within that time,\n  the original handler is <em>not</em> invoked, but the new handler is scheduled in its place.</p>\n\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a><div class='sub-desc'><p>Only call the handler if the event was fired on the target Observable, <em>not</em> if the event\n  was bubbled up from a child Observable.</p>\n\n</div></li><li><span class='pre'>element</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p><strong>This option is only valid for listeners bound to <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a>.</strong>\n  The name of a Component property which references an element to add a listener to.</p>\n\n\n\n\n<p>  This option is useful during Component construction to add DOM event listeners to elements of\n  <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a> which will exist only after the Component is rendered.\n  For example, to add a click listener to a Panel's body:</p>\n\n\n\n\n<pre><code>  new <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>({\n      title: 'The title',\n      listeners: {\n          click: this.handlePanelClick,\n          element: 'body'\n      }\n  });\n</code></pre>\n\n</div></li><li><span class='pre'>destroyable</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>When specified as <code>true</code>, the function returns A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call.</p>\n\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>priority</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>An optional numeric priority that determines the order in which event handlers\n  are run. Event handlers with no priority will be run as if they had a priority\n  of 0. Handlers with a higher priority will be prioritized to run sooner than\n  those with a lower priority.  Negative numbers can be used to set a priority\n  lower than the default. Internally, the framework uses a range of 1000 or\n  greater, and -1000 or lesser for handers that are intended to run before or\n  after all others, so it is recommended to stay within the range of -999 to 999\n  when setting the priority of event handlers in application-level code.</p>\n\n\n\n\n<p><strong>Combining Options</strong></p>\n\n\n\n\n<p>Using the options argument, it is possible to combine different types of listeners:</p>\n\n\n\n\n<p>A delayed, one-time listener.</p>\n\n\n\n\n<pre><code>myPanel.on('hide', this.handleClick, this, {\n    single: true,\n    delay: 100\n});\n</code></pre>\n\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><strong>Only when the <code>destroyable</code> option is specified. </strong></p>\n\n\n\n\n<p> A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call. For example:</p>\n\n\n\n\n<pre><code>this.btnListeners =  = myButton.on({\n    destroyable: true\n    mouseover:   function() { console.log('mouseover'); },\n    mouseout:    function() { console.log('mouseout'); },\n    click:       function() { console.log('click'); }\n});\n</code></pre>\n\n\n\n\n<p>And when those listeners need to be removed:</p>\n\n\n\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.btnListeners);\n</code></pre>\n\n\n\n\n<p>or</p>\n\n\n\n\n<pre><code>this.btnListeners.destroy();\n</code></pre>\n\n</div></li></ul></div></div></div><div id='method-addManagedListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-addManagedListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-addManagedListener' class='name expandable'>addManagedListener</a>( <span class='pre'>item, ename, [fn], [scope], [options]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Adds listeners to any Observable object (or Ext.Element) which are automatically removed when this Component is\ndestr...</div><div class='long'><p>Adds listeners to any Observable object (or <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>) which are automatically removed when this Component is\ndestroyed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The item to which to add a listener/listeners.</p>\n\n</div></li><li><span class='pre'>ename</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The event name, or an object containing event name properties.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the handler function.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the scope (<code>this</code> reference)\nin which the handler function is executed.</p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the\n<a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> options.</p>\n\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><strong>Only when the <code>destroyable</code> option is specified. </strong></p>\n\n\n\n\n<p> A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call. For example:</p>\n\n\n\n\n<pre><code>this.btnListeners =  = myButton.mon({\n    destroyable: true\n    mouseover:   function() { console.log('mouseover'); },\n    mouseout:    function() { console.log('mouseout'); },\n    click:       function() { console.log('click'); }\n});\n</code></pre>\n\n\n\n\n<p>And when those listeners need to be removed:</p>\n\n\n\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.btnListeners);\n</code></pre>\n\n\n\n\n<p>or</p>\n\n\n\n\n<pre><code>this.btnListeners.destroy();\n</code></pre>\n\n</div></li></ul></div></div></div><div id='method-addSorted' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-addSorted' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-addSorted' class='name expandable'>addSorted</a>( <span class='pre'>record</span> )</div><div class='description'><div class='short'>(Local sort only) Inserts the passed Record into the Store at the index where it\nshould go based on the current sort ...</div><div class='long'><p>(Local sort only) Inserts the passed Record into the Store at the index where it\nshould go based on the current sort information.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>record</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Record</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-afterCommit' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-afterCommit' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-afterCommit' class='name expandable'>afterCommit</a>( <span class='pre'>record</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>A model instance should call this method on the Store it has been joined to. ...</div><div class='long'><p>A model instance should call this method on the Store it has been <a href=\"#!/api/Ext.data.Model-method-join\" rel=\"Ext.data.Model-method-join\" class=\"docClass\">joined</a> to.</p>\n        <p>Available since: <b>3.4.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>record</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The model instance that was edited</p>\n</div></li></ul></div></div></div><div id='method-afterEdit' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-afterEdit' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-afterEdit' class='name expandable'>afterEdit</a>( <span class='pre'>record, modifiedFieldNames</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>A model instance should call this method on the Store it has been joined to. ...</div><div class='long'><p>A model instance should call this method on the Store it has been <a href=\"#!/api/Ext.data.Model-method-join\" rel=\"Ext.data.Model-method-join\" class=\"docClass\">joined</a> to.</p>\n        <p>Available since: <b>3.4.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>record</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The model instance that was edited</p>\n</div></li><li><span class='pre'>modifiedFieldNames</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>Array of field names changed during edit.</p>\n</div></li></ul></div></div></div><div id='method-afterReject' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-afterReject' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-afterReject' class='name expandable'>afterReject</a>( <span class='pre'>record</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>A model instance should call this method on the Store it has been joined to.. ...</div><div class='long'><p>A model instance should call this method on the Store it has been <a href=\"#!/api/Ext.data.Model-method-join\" rel=\"Ext.data.Model-method-join\" class=\"docClass\">joined</a> to..</p>\n        <p>Available since: <b>3.4.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>record</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The model instance that was edited</p>\n</div></li></ul></div></div></div><div id='method-aggregate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-aggregate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-aggregate' class='name expandable'>aggregate</a>( <span class='pre'>fn, [scope], [grouped], [args]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Runs the aggregate function for all the records in the store. ...</div><div class='long'><p>Runs the aggregate function for all the records in the store.</p>\n\n<p>When store is filtered, only items within the filter are aggregated.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The function to execute. The function is called with a single parameter,\nan array of records for that group.</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope to execute the function in. Defaults to the store.</p>\n</div></li><li><span class='pre'>grouped</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to perform the operation for each group\nin the store. The value returned will be an object literal with the key being the group\nname and the group average being the value. The grouped parameter is only honored if\nthe store has a groupField.</p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> (optional)<div class='sub-desc'><p>Any arguments to append to the function call</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>An object literal with the group names and their appropriate values.</p>\n</div></li></ul></div></div></div><div id='method-applyState' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-applyState' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-applyState' class='name expandable'>applyState</a>( <span class='pre'>state</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Restores state to the passed state ...</div><div class='long'><p>Restores state to the passed state</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>state</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-average' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-average' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-average' class='name expandable'>average</a>( <span class='pre'>field, [grouped]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Gets the average value in the store. ...</div><div class='long'><p>Gets the average value in the store.</p>\n\n<p>When store is filtered, only items within the filter are aggregated.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>field</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The field in each record</p>\n</div></li><li><span class='pre'>grouped</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to perform the operation for each group\nin the store. The value returned will be an object literal with the key being the group\nname and the group average being the value. The grouped parameter is only honored if\nthe store has a groupField.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The average value, if no items exist, 0.</p>\n</div></li></ul></div></div></div><div id='method-cachePage' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-cachePage' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-cachePage' class='name expandable'>cachePage</a>( <span class='pre'>records, page</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Caches the records in the prefetch and stripes them with their server-side\nindex. ...</div><div class='long'><p>Caches the records in the prefetch and stripes them with their server-side\nindex.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>records</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]<div class='sub-desc'><p>The records to cache</p>\n</div></li><li><span class='pre'>page</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The associated operation</p>\n</div></li></ul></div></div></div><div id='method-callOverridden' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-captureArgs' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-captureArgs' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-captureArgs' class='name expandable'>captureArgs</a>( <span class='pre'>o, fn, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>o</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-clearData' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-clearData' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-clearData' class='name expandable'>clearData</a>( <span class='pre'>isLoad</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>private ...</div><div class='long'><p>private</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>isLoad</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.AbstractStore-method-clearData' rel='Ext.data.AbstractStore-method-clearData' class='docClass'>Ext.data.AbstractStore.clearData</a></p></div></div></div><div id='method-clearFilter' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-clearFilter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-clearFilter' class='name expandable'>clearFilter</a>( <span class='pre'>[suppressEvent]</span> )</div><div class='description'><div class='short'>Reverts to a view of the Record cache with no filtering applied. ...</div><div class='long'><p>Reverts to a view of the Record cache with no filtering applied.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>suppressEvent</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>If <code>true</code> the filter is cleared silently.</p>\n\n<p>For a locally filtered Store, this means that the filter collection is cleared without firing the\n<a href=\"#!/api/Ext.data.Store-event-datachanged\" rel=\"Ext.data.Store-event-datachanged\" class=\"docClass\">datachanged</a> event.</p>\n\n<p>For a remotely filtered Store, this means that the filter collection is cleared, but the store\nis not reloaded from the server.</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.AbstractStore-method-clearFilter' rel='Ext.data.AbstractStore-method-clearFilter' class='docClass'>Ext.data.AbstractStore.clearFilter</a></p></div></div></div><div id='method-clearGrouping' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-clearGrouping' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-clearGrouping' class='name expandable'>clearGrouping</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Clear any groupers in the store ...</div><div class='long'><p>Clear any groupers in the store</p>\n</div></div></div><div id='method-clearListeners' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-clearListeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-clearListeners' class='name expandable'>clearListeners</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Removes all listeners for this object including the managed listeners ...</div><div class='long'><p>Removes all listeners for this object including the managed listeners</p>\n</div></div></div><div id='method-clearManagedListeners' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-clearManagedListeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-clearManagedListeners' class='name expandable'>clearManagedListeners</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Removes all managed listeners for this object. ...</div><div class='long'><p>Removes all managed listeners for this object.</p>\n</div></div></div><div id='method-collect' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-collect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-collect' class='name expandable'>collect</a>( <span class='pre'>dataIndex, [allowNull], [bypassFilter]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]</div><div class='description'><div class='short'>Collects unique values for a particular dataIndex from this store. ...</div><div class='long'><p>Collects unique values for a particular dataIndex from this store.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>dataIndex</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The property to collect</p>\n</div></li><li><span class='pre'>allowNull</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>Pass true to allow null, undefined or empty string values</p>\n</div></li><li><span class='pre'>bypassFilter</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>Pass true to collect from all records, even ones which are filtered.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]</span><div class='sub-desc'><p>An array of the unique values</p>\n</div></li></ul></div></div></div><div id='method-commitChanges' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-commitChanges' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-commitChanges' class='name expandable'>commitChanges</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Commits all Records with outstanding changes. ...</div><div class='long'><p>Commits all Records with <a href=\"#!/api/Ext.data.Store-method-getModifiedRecords\" rel=\"Ext.data.Store-method-getModifiedRecords\" class=\"docClass\">outstanding changes</a>. To handle updates for changes,\nsubscribe to the Store's <a href=\"#!/api/Ext.data.Store-event-update\" rel=\"Ext.data.Store-event-update\" class=\"docClass\">update event</a>, and perform updating when the third parameter is\n<a href=\"#!/api/Ext.data.Model-static-property-COMMIT\" rel=\"Ext.data.Model-static-property-COMMIT\" class=\"docClass\">Ext.data.Record.COMMIT</a>.</p>\n</div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-constructGroups' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-constructGroups' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-constructGroups' class='name expandable'>constructGroups</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-continueFireEvent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-continueFireEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-continueFireEvent' class='name expandable'>continueFireEvent</a>( <span class='pre'>eventName, args, bubbles</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Continue to fire event. ...</div><div class='long'><p>Continue to fire event.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li><li><span class='pre'>bubbles</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-count' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-count' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-count' class='name expandable'>count</a>( <span class='pre'>[grouped]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Gets the count of items in the store. ...</div><div class='long'><p>Gets the count of items in the store.</p>\n\n<p>When store is filtered, only items within the filter are counted.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>grouped</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to perform the operation for each group\nin the store. The value returned will be an object literal with the key being the group\nname and the count for each group being the value. The grouped parameter is only honored if\nthe store has a groupField.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>the count</p>\n</div></li></ul></div></div></div><div id='method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-create' class='name expandable'>create</a>( <span class='pre'>data, options</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>saves any phantom records ...</div><div class='long'><p>saves any phantom records</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>data</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createFilterFn' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-createFilterFn' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-createFilterFn' class='name expandable'>createFilterFn</a>( <span class='pre'>property, value, [anyMatch], [caseSensitive], [exactMatch]</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Returns a filter function used to test a the given property's value. ...</div><div class='long'><p>Returns a filter function used to test a the given property's value. Defers most of the work to\n<a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">Ext.util.MixedCollection</a>'s createValueMatcher function.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>property</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The property to create the filter function for</p>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a><div class='sub-desc'><p>The string/regex to compare the property value to</p>\n</div></li><li><span class='pre'>anyMatch</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True if we don't care if the filter value is not the full value.</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>caseSensitive</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to create a case-sensitive regex.</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>exactMatch</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to force exact match (^ and $ characters added to the regex).\nIgnored if anyMatch is true.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul></div></div></div><div id='method-createModel' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-createModel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-createModel' class='name expandable'>createModel</a>( <span class='pre'>record</span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Converts a literal to a model, if it's not a model already ...</div><div class='long'><p>Converts a literal to a model, if it's not a model already</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>record</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The record to create</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createRelayer' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-createRelayer' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-createRelayer' class='name expandable'>createRelayer</a>( <span class='pre'>newName, [beginEnd]</span> ) : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Creates an event handling function which refires the event from this object as the passed event name. ...</div><div class='long'><p>Creates an event handling function which refires the event from this object as the passed event name.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>newName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name under which to refire the passed parameters.</p>\n</div></li><li><span class='pre'>beginEnd</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> (optional)<div class='sub-desc'><p>The caller can specify on which indices to slice.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-decodeFilters' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-decodeFilters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-decodeFilters' class='name expandable'>decodeFilters</a>( <span class='pre'>filters</span> ) : <a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Ext.util.Filter</a>[]<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Normalizes an array of filter objects, ensuring that they are all Ext.util.Filter instances ...</div><div class='long'><p>Normalizes an array of filter objects, ensuring that they are all <a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Ext.util.Filter</a> instances</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>filters</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]<div class='sub-desc'><p>The filters array</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Ext.util.Filter</a>[]</span><div class='sub-desc'><p>Array of <a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Ext.util.Filter</a> objects</p>\n</div></li></ul></div></div></div><div id='method-decodeGroupers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-decodeGroupers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-decodeGroupers' class='name expandable'>decodeGroupers</a>( <span class='pre'>groupers</span> ) : <a href=\"#!/api/Ext.util.Grouper\" rel=\"Ext.util.Grouper\" class=\"docClass\">Ext.util.Grouper</a>[]<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Normalizes an array of grouper objects, ensuring that they are all Ext.util.Grouper instances ...</div><div class='long'><p>Normalizes an array of grouper objects, ensuring that they are all <a href=\"#!/api/Ext.util.Grouper\" rel=\"Ext.util.Grouper\" class=\"docClass\">Ext.util.Grouper</a> instances</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>groupers</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]<div class='sub-desc'><p>The groupers array</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Grouper\" rel=\"Ext.util.Grouper\" class=\"docClass\">Ext.util.Grouper</a>[]</span><div class='sub-desc'><p>Array of <a href=\"#!/api/Ext.util.Grouper\" rel=\"Ext.util.Grouper\" class=\"docClass\">Ext.util.Grouper</a> objects</p>\n</div></li></ul></div></div></div><div id='method-decodeSorters' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-method-decodeSorters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-method-decodeSorters' class='name expandable'>decodeSorters</a>( <span class='pre'>sorters</span> ) : <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a>[]<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Normalizes an array of sorter objects, ensuring that they are all Ext.util.Sorter instances ...</div><div class='long'><p>Normalizes an array of sorter objects, ensuring that they are all <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a> instances</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>sorters</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]<div class='sub-desc'><p>The sorters array</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a>[]</span><div class='sub-desc'><p>Array of <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a> objects</p>\n</div></li></ul></div></div></div><div id='method-destroy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-destroy' class='name expandable'>destroy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>tells the attached proxy to destroy the given records ...</div><div class='long'><p>tells the attached proxy to destroy the given records</p>\n        <p>Available since: <b>3.4.0</b></p>\n<p>Overrides: <a href='#!/api/Ext.Base-method-destroy' rel='Ext.Base-method-destroy' class='docClass'>Ext.Base.destroy</a></p></div></div></div><div id='method-destroyStore' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-destroyStore' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-destroyStore' class='name expandable'>destroyStore</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>private ...</div><div class='long'><p>private</p>\n</div></div></div><div id='method-doSort' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-doSort' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-doSort' class='name expandable'>doSort</a>( <span class='pre'>sorterFn</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>overriden to provide striping of the indexes as sorting occurs. ...</div><div class='long'><p>overriden to provide striping of the indexes as sorting occurs.\nthis cannot be done inside of sort because datachanged has already\nfired and will trigger a repaint of the bound view.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>sorterFn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.AbstractStore-method-doSort' rel='Ext.data.AbstractStore-method-doSort' class='docClass'>Ext.data.AbstractStore.doSort</a></p></div></div></div><div id='method-each' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-each' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-each' class='name expandable'>each</a>( <span class='pre'>fn, [scope]</span> )</div><div class='description'><div class='short'>Calls the specified function for each record in the store. ...</div><div class='long'><p>Calls the specified function for each <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">record</a> in the store.</p>\n\n<p>When store is filtered, only loops over the filtered records.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The function to call. The <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Record</a> is passed as the first parameter.\nReturning <code>false</code> aborts and exits the iteration.</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (this reference) in which the function is executed.\nDefaults to the current <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">record</a> in the iteration.</p>\n</div></li></ul></div></div></div><div id='method-emptyComparator' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-method-emptyComparator' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-method-emptyComparator' class='name expandable'>emptyComparator</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-enableBubble' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-enableBubble' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-enableBubble' class='name expandable'>enableBubble</a>( <span class='pre'>eventNames</span> )</div><div class='description'><div class='short'>Enables events fired by this Observable to bubble up an owner hierarchy by calling this.getBubbleTarget() if\npresent. ...</div><div class='long'><p>Enables events fired by this Observable to bubble up an owner hierarchy by calling <code>this.getBubbleTarget()</code> if\npresent. There is no implementation in the Observable base class.</p>\n\n<p>This is commonly used by Ext.Components to bubble events to owner Containers.\nSee <a href=\"#!/api/Ext.Component-method-getBubbleTarget\" rel=\"Ext.Component-method-getBubbleTarget\" class=\"docClass\">Ext.Component.getBubbleTarget</a>. The default implementation in <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a> returns the\nComponent's immediate owner. But if a known target is required, this can be overridden to access the\nrequired target more quickly.</p>\n\n<p>Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.overrides.form.field.Base', {\n    override: '<a href=\"#!/api/Ext.form.field.Base\" rel=\"Ext.form.field.Base\" class=\"docClass\">Ext.form.field.Base</a>',\n\n    //  Add functionality to Field's initComponent to enable the change event to bubble\n    initComponent: function () {\n        this.callParent();\n        this.enableBubble('change');\n    }\n});\n\nvar myForm = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.form.Panel\" rel=\"Ext.form.Panel\" class=\"docClass\">Ext.form.Panel</a>', {\n    title: 'User Details',\n    items: [{\n        ...\n    }],\n    listeners: {\n        change: function() {\n            // Title goes red if form has been modified.\n            myForm.header.setStyle('color', 'red');\n        }\n    }\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventNames</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>The event name to bubble, or an Array of event names.</p>\n</div></li></ul></div></div></div><div id='method-filter' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-filter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-filter' class='name expandable'>filter</a>( <span class='pre'>[filters], [value]</span> )</div><div class='description'><div class='short'>Filters the loaded set of records by a given set of filters. ...</div><div class='long'><p>Filters the loaded set of records by a given set of filters.</p>\n\n<p>By default, the passed filter(s) are <em>added</em> to the collection of filters being used to filter this Store.</p>\n\n<p>To remove existing filters before applying a new set of filters use</p>\n\n<pre><code>// Clear the filter collection without updating the UI\nstore.clearFilter(true);\n</code></pre>\n\n<p>see <a href=\"#!/api/Ext.data.Store-method-clearFilter\" rel=\"Ext.data.Store-method-clearFilter\" class=\"docClass\">clearFilter</a>.</p>\n\n<p>Alternatively, if filters are configured with an <code>id</code>, then existing filters store may be <em>replaced</em> by new\nfilters having the same <code>id</code>.</p>\n\n<p>Filtering by single field:</p>\n\n<pre><code>store.filter(\"email\", /\\.com$/);\n</code></pre>\n\n<p>Using multiple filters:</p>\n\n<pre><code>store.filter([\n    {property: \"email\", value: /\\.com$/},\n    {filterFn: function(item) { return item.get(\"age\") &gt; 10; }}\n]);\n</code></pre>\n\n<p>Using <a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Ext.util.Filter</a> instances instead of config objects\n(note that we need to specify the <a href=\"#!/api/Ext.util.Filter-cfg-root\" rel=\"Ext.util.Filter-cfg-root\" class=\"docClass\">root</a> config option in this case):</p>\n\n<pre><code>store.filter([\n    <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Ext.util.Filter</a>', {property: \"email\", value: /\\.com$/, root: 'data'}),\n    <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Ext.util.Filter</a>', {filterFn: function(item) { return item.get(\"age\") &gt; 10; }, root: 'data'})\n]);\n</code></pre>\n\n<p>When store is filtered, most of the methods for accessing store data will be working only\nwithin the set of filtered records. Two notable exceptions are <a href=\"#!/api/Ext.data.Store-method-queryBy\" rel=\"Ext.data.Store-method-queryBy\" class=\"docClass\">queryBy</a> and\n<a href=\"#!/api/Ext.data.Store-method-getById\" rel=\"Ext.data.Store-method-getById\" class=\"docClass\">getById</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>filters</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]/<a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Ext.util.Filter</a>[]/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The set of filters to apply to the data.\nThese are stored internally on the store, but the filtering itself is done on the Store's\n<a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">MixedCollection</a>. See MixedCollection's\n<a href=\"#!/api/Ext.util.MixedCollection-method-filter\" rel=\"Ext.util.MixedCollection-method-filter\" class=\"docClass\">filter</a> method for filter syntax.\nAlternatively, pass in a property string.</p>\n\n<p>If no parameters are passed, the Store's existing filter set is applied.</p>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>value to filter by (only if using a property string as the first argument)</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.AbstractStore-method-filter' rel='Ext.data.AbstractStore-method-filter' class='docClass'>Ext.data.AbstractStore.filter</a></p></div></div></div><div id='method-filterBy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-filterBy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-filterBy' class='name expandable'>filterBy</a>( <span class='pre'>fn, [scope]</span> )</div><div class='description'><div class='short'>Filters by a function. ...</div><div class='long'><p>Filters by a function. The specified function will be called for each\nRecord in this Store. If the function returns <code>true</code> the Record is included,\notherwise it is filtered out.</p>\n\n<p>When store is filtered, most of the methods for accessing store data will be working only\nwithin the set of filtered records. Two notable exceptions are <a href=\"#!/api/Ext.data.Store-method-queryBy\" rel=\"Ext.data.Store-method-queryBy\" class=\"docClass\">queryBy</a> and\n<a href=\"#!/api/Ext.data.Store-method-getById\" rel=\"Ext.data.Store-method-getById\" class=\"docClass\">getById</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The function to be called. It will be passed the following parameters:</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>record</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The record to test for filtering. Access field values\n using <a href=\"#!/api/Ext.data.Model-method-get\" rel=\"Ext.data.Model-method-get\" class=\"docClass\">Ext.data.Model.get</a>.</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The ID of the Record passed.</p>\n</div></li></ul></div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (this reference) in which the function is executed.\nDefaults to this Store.</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.AbstractStore-method-filterBy' rel='Ext.data.AbstractStore-method-filterBy' class='docClass'>Ext.data.AbstractStore.filterBy</a></p></div></div></div><div id='method-filterNew' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-filterNew' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-filterNew' class='name expandable'>filterNew</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Filter function for new records. ...</div><div class='long'><p>Filter function for new records.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-filterNewOnly' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-filterNewOnly' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-filterNewOnly' class='name expandable'>filterNewOnly</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-filterUpdated' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-filterUpdated' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-filterUpdated' class='name expandable'>filterUpdated</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Filter function for updated records. ...</div><div class='long'><p>Filter function for updated records.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-find' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-find' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-find' class='name expandable'>find</a>( <span class='pre'>fieldName, value, [startIndex], [anyMatch], [caseSensitive], [exactMatch]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Finds the index of the first matching Record in this store by a specific field value. ...</div><div class='long'><p>Finds the index of the first matching Record in this store by a specific field value.</p>\n\n<p>When store is filtered, finds records only within filter.</p>\n\n<p>**IMPORTANT</p>\n\n<p>If this store is <a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">buffered</a>, this can ONLY find records which happen to be cached in the page cache.\nThis will be parts of the dataset around the currently visible zone, or recently visited zones if the pages\nhave not yet been purged from the cache.**</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fieldName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the Record field to test.</p>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a><div class='sub-desc'><p>Either a string that the field value\nshould begin with, or a RegExp to test against the field.</p>\n</div></li><li><span class='pre'>startIndex</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>The index to start searching at</p>\n<p>Defaults to: <code>0</code></p></div></li><li><span class='pre'>anyMatch</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to match any part of the string, not just the beginning</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>caseSensitive</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True for case sensitive comparison</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>exactMatch</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to force exact match (^ and $ characters added to the regex).</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The matched index or -1</p>\n</div></li></ul></div></div></div><div id='method-findBy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-findBy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-findBy' class='name expandable'>findBy</a>( <span class='pre'>fn, [scope], [startIndex]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Find the index of the first matching Record in this Store by a function. ...</div><div class='long'><p>Find the index of the first matching Record in this Store by a function.\nIf the function returns <code>true</code> it is considered a match.</p>\n\n<p>When store is filtered, finds records only within filter.</p>\n\n<p>**IMPORTANT</p>\n\n<p>If this store is <a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">buffered</a>, this can ONLY find records which happen to be cached in the page cache.\nThis will be parts of the dataset around the currently visible zone, or recently visited zones if the pages\nhave not yet been purged from the cache.**</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The function to be called. It will be passed the following parameters:</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>record</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The record to test for filtering. Access field values\n using <a href=\"#!/api/Ext.data.Model-method-get\" rel=\"Ext.data.Model-method-get\" class=\"docClass\">Ext.data.Model.get</a>.</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The ID of the Record passed.</p>\n</div></li></ul></div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (this reference) in which the function is executed.\nDefaults to this Store.</p>\n</div></li><li><span class='pre'>startIndex</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>The index to start searching at</p>\n<p>Defaults to: <code>0</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The matched index or -1</p>\n</div></li></ul></div></div></div><div id='method-findExact' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-findExact' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-findExact' class='name expandable'>findExact</a>( <span class='pre'>fieldName, value, [startIndex]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Finds the index of the first matching Record in this store by a specific field value. ...</div><div class='long'><p>Finds the index of the first matching Record in this store by a specific field value.</p>\n\n<p>When store is filtered, finds records only within filter.</p>\n\n<p>**IMPORTANT</p>\n\n<p>If this store is <a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">buffered</a>, this can ONLY find records which happen to be cached in the page cache.\nThis will be parts of the dataset around the currently visible zone, or recently visited zones if the pages\nhave not yet been purged from the cache.**</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fieldName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the Record field to test.</p>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to match the field against.</p>\n</div></li><li><span class='pre'>startIndex</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>The index to start searching at</p>\n<p>Defaults to: <code>0</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The matched index or -1</p>\n</div></li></ul></div></div></div><div id='method-findRecord' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-findRecord' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-findRecord' class='name expandable'>findRecord</a>( <span class='pre'>fieldName, value, [startIndex], [anyMatch], [caseSensitive], [exactMatch]</span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></div><div class='description'><div class='short'>Finds the first matching Record in this store by a specific field value. ...</div><div class='long'><p>Finds the first matching Record in this store by a specific field value.</p>\n\n<p>When store is filtered, finds records only within filter.</p>\n\n<p>**IMPORTANT</p>\n\n<p>If this store is <a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">buffered</a>, this can ONLY find records which happen to be cached in the page cache.\nThis will be parts of the dataset around the currently visible zone, or recently visited zones if the pages\nhave not yet been purged from the cache.**</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fieldName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the Record field to test.</p>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a><div class='sub-desc'><p>Either a string that the field value\nshould begin with, or a RegExp to test against the field.</p>\n</div></li><li><span class='pre'>startIndex</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>The index to start searching at</p>\n<p>Defaults to: <code>0</code></p></div></li><li><span class='pre'>anyMatch</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to match any part of the string, not just the beginning</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>caseSensitive</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True for case sensitive comparison</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>exactMatch</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to force exact match (^ and $ characters added to the regex).</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></span><div class='sub-desc'><p>The matched record or null</p>\n</div></li></ul></div></div></div><div id='method-fireEvent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-fireEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-fireEvent' class='name expandable'>fireEvent</a>( <span class='pre'>eventName, args</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Fires the specified event with the passed parameters (minus the event name, plus the options object passed\nto addList...</div><div class='long'><p>Fires the specified event with the passed parameters (minus the event name, plus the <code>options</code> object passed\nto <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a>).</p>\n\n<p>An event may be set to bubble up an Observable parent hierarchy (See <a href=\"#!/api/Ext.Component-method-getBubbleTarget\" rel=\"Ext.Component-method-getBubbleTarget\" class=\"docClass\">Ext.Component.getBubbleTarget</a>) by\ncalling <a href=\"#!/api/Ext.util.Observable-method-enableBubble\" rel=\"Ext.util.Observable-method-enableBubble\" class=\"docClass\">enableBubble</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the event to fire.</p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>...<div class='sub-desc'><p>Variable number of parameters are passed to handlers.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>returns false if any of the handlers return false otherwise it returns true.</p>\n</div></li></ul></div></div></div><div id='method-fireEventArgs' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-fireEventArgs' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-fireEventArgs' class='name expandable'>fireEventArgs</a>( <span class='pre'>eventName, args</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Fires the specified event with the passed parameter list. ...</div><div class='long'><p>Fires the specified event with the passed parameter list.</p>\n\n<p>An event may be set to bubble up an Observable parent hierarchy (See <a href=\"#!/api/Ext.Component-method-getBubbleTarget\" rel=\"Ext.Component-method-getBubbleTarget\" class=\"docClass\">Ext.Component.getBubbleTarget</a>) by\ncalling <a href=\"#!/api/Ext.util.Observable-method-enableBubble\" rel=\"Ext.util.Observable-method-enableBubble\" class=\"docClass\">enableBubble</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the event to fire.</p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]<div class='sub-desc'><p>An array of parameters which are passed to handlers.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>returns false if any of the handlers return false otherwise it returns true.</p>\n</div></li></ul></div></div></div><div id='method-fireGroupChange' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-fireGroupChange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-fireGroupChange' class='name expandable'>fireGroupChange</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Fires the groupchange event. ...</div><div class='long'><p>Fires the groupchange event. Abstracted out so we can use it\nas a callback</p>\n</div></div></div><div id='method-first' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-first' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-first' class='name expandable'>first</a>( <span class='pre'>[grouped]</span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>/undefined</div><div class='description'><div class='short'>Convenience function for getting the first model instance in the store. ...</div><div class='long'><p>Convenience function for getting the first model instance in the store.</p>\n\n<p>When store is filtered, will return first item within the filter.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>grouped</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to perform the operation for each group\nin the store. The value returned will be an object literal with the key being the group\nname and the first record being the value. The grouped parameter is only honored if\nthe store has a groupField.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>/undefined</span><div class='sub-desc'><p>The first model instance in the store, or undefined</p>\n</div></li></ul></div></div></div><div id='method-generateComparator' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-method-generateComparator' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-method-generateComparator' class='name expandable'>generateComparator</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Returns a comparator function which compares two items and returns -1, 0, or 1 depending\non the currently defined set...</div><div class='long'><p>Returns a comparator function which compares two items and returns -1, 0, or 1 depending\non the currently defined set of <a href=\"#!/api/Ext.util.Sortable-cfg-sorters\" rel=\"Ext.util.Sortable-cfg-sorters\" class=\"docClass\">sorters</a>.</p>\n\n<p>If there are no <a href=\"#!/api/Ext.util.Sortable-cfg-sorters\" rel=\"Ext.util.Sortable-cfg-sorters\" class=\"docClass\">sorters</a> defined, it returns a function which returns <code>0</code> meaning\nthat no sorting will occur.</p>\n</div></div></div><div id='method-getAggregate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getAggregate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getAggregate' class='name expandable'>getAggregate</a>( <span class='pre'>fn, scope, records, args</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>records</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getAt' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getAt' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getAt' class='name expandable'>getAt</a>( <span class='pre'>index</span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></div><div class='description'><div class='short'>Get the Record at the specified index. ...</div><div class='long'><p>Get the Record at the specified index.</p>\n\n<p>The index is effected by filtering.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The index of the Record to find.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></span><div class='sub-desc'><p>The Record at the passed index. Returns undefined if not found.</p>\n</div></li></ul></div></div></div><div id='method-getAverage' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getAverage' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getAverage' class='name expandable'>getAverage</a>( <span class='pre'>records, field</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>, see average ...</div><div class='long'><p>, see average</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>records</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>field</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getBatchListeners' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-getBatchListeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-getBatchListeners' class='name expandable'>getBatchListeners</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Returns an object which is passed in as the listeners argument to proxy.batch inside this.sync. ...</div><div class='long'><p>Returns an object which is passed in as the listeners argument to proxy.batch inside this.sync.\nThis is broken out into a separate function to allow for customisation of the listeners</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The listeners object</p>\n</div></li></ul></div></div></div><div id='method-getBubbleParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-getBubbleParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-getBubbleParent' class='name expandable'>getBubbleParent</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Gets the bubbling parent for an Observable ...</div><div class='long'><p>Gets the bubbling parent for an Observable</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a></span><div class='sub-desc'><p>The bubble parent. null is returned if no bubble target exists</p>\n</div></li></ul></div></div></div><div id='method-getById' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getById' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getById' class='name expandable'>getById</a>( <span class='pre'>id</span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></div><div class='description'><div class='short'>Get the Record with the specified id. ...</div><div class='long'><p>Get the Record with the specified id.</p>\n\n<p>This method is not effected by filtering, lookup will be performed from all records\ninside the store, filtered or not.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : Mixed<div class='sub-desc'><p>The id of the Record to find.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></span><div class='sub-desc'><p>The Record with the passed id. Returns null if not found.</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.AbstractStore-method-getById' rel='Ext.data.AbstractStore-method-getById' class='docClass'>Ext.data.AbstractStore.getById</a></p></div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getCount' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getCount' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getCount' class='name expandable'>getCount</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Gets the number of records in store. ...</div><div class='long'><p>Gets the number of records in store.</p>\n\n<p>If using paging, this may not be the total size of the dataset. If the data object\nused by the Reader contains the dataset size, then the <a href=\"#!/api/Ext.data.Store-method-getTotalCount\" rel=\"Ext.data.Store-method-getTotalCount\" class=\"docClass\">getTotalCount</a> function returns\nthe dataset size.  <strong>Note</strong>: see the Important note in <a href=\"#!/api/Ext.data.Store-method-load\" rel=\"Ext.data.Store-method-load\" class=\"docClass\">load</a>.</p>\n\n<p>When store is filtered, it's the number of records matching the filter.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The number of Records in the Store.</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.AbstractStore-method-getCount' rel='Ext.data.AbstractStore-method-getCount' class='docClass'>Ext.data.AbstractStore.getCount</a></p></div></div></div><div id='method-getFirstSorter' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-method-getFirstSorter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-method-getFirstSorter' class='name expandable'>getFirstSorter</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Gets the first sorter from the sorters collection, excluding\nany groupers that may be in place ...</div><div class='long'><p>Gets the first sorter from the sorters collection, excluding\nany groupers that may be in place</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a></span><div class='sub-desc'><p>The sorter, null if none exist</p>\n</div></li></ul></div></div></div><div id='method-getGroupData' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getGroupData' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getGroupData' class='name expandable'>getGroupData</a>( <span class='pre'>[sort]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Returns records grouped by the configured grouper configuration. ...</div><div class='long'><p>Returns records grouped by the configured <a href=\"#!/api/Ext.data.Store-cfg-groupers\" rel=\"Ext.data.Store-cfg-groupers\" class=\"docClass\">grouper</a> configuration. Sample return value (in\nthis case grouping by genre and then author in a fictional books dataset):</p>\n\n<pre><code>[\n    {\n        name: 'Fantasy',\n        depth: 0,\n        records: [\n            //book1, book2, book3, book4\n        ],\n        children: [\n            {\n                name: 'Rowling',\n                depth: 1,\n                records: [\n                    //book1, book2\n                ]\n            },\n            {\n                name: 'Tolkein',\n                depth: 1,\n                records: [\n                    //book3, book4\n                ]\n            }\n        ]\n    }\n]\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>sort</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p><code>true</code> to call <a href=\"#!/api/Ext.data.Store-method-sort\" rel=\"Ext.data.Store-method-sort\" class=\"docClass\">sort</a> before finding groups. Sorting is required to make grouping\nfunction correctly so this should only be set to false if the Store is known to already be sorted correctly.</p>\n<p>Defaults to: <code>true</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]</span><div class='sub-desc'><p>The group data</p>\n</div></li></ul></div></div></div><div id='method-getGroupField' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getGroupField' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getGroupField' class='name expandable'>getGroupField</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getGroupString' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getGroupString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getGroupString' class='name expandable'>getGroupString</a>( <span class='pre'>instance</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns the string to group on for a given model instance. ...</div><div class='long'><p>Returns the string to group on for a given model instance. The default implementation of this method returns\nthe model's <a href=\"#!/api/Ext.data.Store-cfg-groupField\" rel=\"Ext.data.Store-cfg-groupField\" class=\"docClass\">groupField</a>, but this can be overridden to group by an arbitrary string. For example, to\ngroup by the first letter of a model's 'name' field, use the following code:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a>', {\n    groupDir: 'ASC',\n    getGroupString: function(instance) {\n        return instance.get('name')[0];\n    }\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>instance</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The model instance</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The string to compare when forming groups</p>\n</div></li></ul></div></div></div><div id='method-getGroups' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getGroups' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getGroups' class='name expandable'>getGroups</a>( <span class='pre'>[groupName]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]</div><div class='description'><div class='short'>Returns an array containing the result of applying grouping to the records in this store. ...</div><div class='long'><p>Returns an array containing the result of applying grouping to the records in this store.\nSee <a href=\"#!/api/Ext.data.Store-cfg-groupField\" rel=\"Ext.data.Store-cfg-groupField\" class=\"docClass\">groupField</a>, <a href=\"#!/api/Ext.data.Store-cfg-groupDir\" rel=\"Ext.data.Store-cfg-groupDir\" class=\"docClass\">groupDir</a> and <a href=\"#!/api/Ext.data.Store-method-getGroupString\" rel=\"Ext.data.Store-method-getGroupString\" class=\"docClass\">getGroupString</a>. Example for a store\ncontaining records with a color field:</p>\n\n<pre><code>var myStore = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a>', {\n    groupField: 'color',\n    groupDir  : 'DESC'\n});\n\nmyStore.getGroups(); // returns:\n[\n    {\n        name: 'yellow',\n        children: [\n            // all records where the color field is 'yellow'\n        ]\n    },\n    {\n        name: 'red',\n        children: [\n            // all records where the color field is 'red'\n        ]\n    }\n]\n</code></pre>\n\n<p>Group contents are effected by filtering.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>groupName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Pass in an optional groupName argument to access a specific\ngroup as defined by <a href=\"#!/api/Ext.data.Store-method-getGroupString\" rel=\"Ext.data.Store-method-getGroupString\" class=\"docClass\">getGroupString</a>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]</span><div class='sub-desc'><p>The grouped data</p>\n</div></li></ul></div></div></div><div id='method-getGroupsForGrouper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getGroupsForGrouper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getGroupsForGrouper' class='name expandable'>getGroupsForGrouper</a>( <span class='pre'>records, grouper</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>For a given set of records and a Grouper, returns an array of arrays - each of which is the set of records\nmatching a...</div><div class='long'><p>For a given set of records and a Grouper, returns an array of arrays - each of which is the set of records\nmatching a certain group.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>records</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>grouper</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getGroupsForGrouperIndex' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getGroupsForGrouperIndex' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getGroupsForGrouperIndex' class='name expandable'>getGroupsForGrouperIndex</a>( <span class='pre'>records, grouperIndex</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]<strong class='private signature' >private</strong></div><div class='description'><div class='short'>This is used recursively to gather the records into the configured Groupers. ...</div><div class='long'><p>This is used recursively to gather the records into the configured Groupers. The data MUST have been sorted for\nthis to work properly (see <a href=\"#!/api/Ext.data.Store-method-getGroupData\" rel=\"Ext.data.Store-method-getGroupData\" class=\"docClass\">getGroupData</a> and <a href=\"#!/api/Ext.data.Store-method-getGroupsForGrouper\" rel=\"Ext.data.Store-method-getGroupsForGrouper\" class=\"docClass\">getGroupsForGrouper</a>) Most of the work is done by\n<a href=\"#!/api/Ext.data.Store-method-getGroupsForGrouper\" rel=\"Ext.data.Store-method-getGroupsForGrouper\" class=\"docClass\">getGroupsForGrouper</a> - this function largely just handles the recursion.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>records</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]<div class='sub-desc'><p>The set or subset of records to group</p>\n</div></li><li><span class='pre'>grouperIndex</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The grouper index to retrieve</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]</span><div class='sub-desc'><p>The grouped records</p>\n</div></li></ul></div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-getMax' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getMax' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getMax' class='name expandable'>getMax</a>( <span class='pre'>records, field</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>, see max ...</div><div class='long'><p>, see max</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>records</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>field</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getMin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getMin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getMin' class='name expandable'>getMin</a>( <span class='pre'>records, field</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>, see min ...</div><div class='long'><p>, see min</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>records</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>field</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getModifiedRecords' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-getModifiedRecords' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-getModifiedRecords' class='name expandable'>getModifiedRecords</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</div><div class='description'><div class='short'>Gets all records added or updated since the last commit. ...</div><div class='long'><p>Gets all <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">records</a> added or updated since the last commit. Note that the order of records\nreturned is not deterministic and does not indicate the order in which records were modified. Note also that\nremoved records are not included (use <a href=\"#!/api/Ext.data.AbstractStore-method-getRemovedRecords\" rel=\"Ext.data.AbstractStore-method-getRemovedRecords\" class=\"docClass\">getRemovedRecords</a> for that).</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</span><div class='sub-desc'><p>The added and updated Model instances</p>\n</div></li></ul></div></div></div><div id='method-getNewRecords' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getNewRecords' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getNewRecords' class='name expandable'>getNewRecords</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</div><div class='description'><div class='short'>inherit docs\n\nReturns all Model instances that are either currently a phantom (e.g. ...</div><div class='long'><p>inherit docs</p>\n\n<p>Returns all Model instances that are either currently a phantom (e.g. have no id), or have an ID but have not\nyet been saved on this Store (this happens when adding a non-phantom record from another Store into this one)</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</span><div class='sub-desc'><p>The Model instances</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.AbstractStore-method-getNewRecords' rel='Ext.data.AbstractStore-method-getNewRecords' class='docClass'>Ext.data.AbstractStore.getNewRecords</a></p></div></div></div><div id='method-getPageFromRecordIndex' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getPageFromRecordIndex' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getPageFromRecordIndex' class='name expandable'>getPageFromRecordIndex</a>( <span class='pre'>index</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Determines the page from a record index ...</div><div class='long'><p>Determines the page from a record index</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The record index</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The page the record belongs to</p>\n</div></li></ul></div></div></div><div id='method-getProxy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-getProxy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-getProxy' class='name expandable'>getProxy</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a></div><div class='description'><div class='short'>Returns the proxy currently attached to this proxy instance ...</div><div class='long'><p>Returns the proxy currently attached to this proxy instance</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a></span><div class='sub-desc'><p>The Proxy instance</p>\n</div></li></ul></div></div></div><div id='method-getRange' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getRange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getRange' class='name expandable'>getRange</a>( <span class='pre'>start, end, [options]</span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</div><div class='description'><div class='short'>Gathers a range of Records between specified indices. ...</div><div class='long'><p>Gathers a range of Records between specified indices.</p>\n\n<p>If this store is <a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">buffered</a>, the indices are relative to the entire dataset, not the local record cache.</p>\n\n<p>If this store is <a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">buffered</a>, then the requested data range <em>may</em> not be immediately available, and will\nbe returned through a passed callback function.</p>\n\n<p>This method is affected by filtering.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>start</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The starting index. Defaults to zero for non <a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">buffered</a> Stores.</p>\n</div></li><li><span class='pre'>end</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The ending index. Defaults to the last Record for non <a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">buffered</a> Stores.</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Used when the Store is {@link <a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">buffered</a>] and the range may not be available synchronously.</p>\n<ul><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>A function to call when the range becomes available.</p>\n<ul><li><span class='pre'>range</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]<div class='sub-desc'><p>The requested range of records.</p>\n</div></li><li><span class='pre'>start</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The delivered start index.</p>\n</div></li><li><span class='pre'>end</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The delivered end index</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The passed options object.</p>\n</div></li></ul></div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</span><div class='sub-desc'><p>An array of records <strong>if the records are immediately available</strong>. For <a href=\"#!/api/Ext.data.Store-cfg-buffered\" rel=\"Ext.data.Store-cfg-buffered\" class=\"docClass\">buffered</a>\nstores, you should pass the callback option <strong>unless you know that the range will be present</strong> - see <a href=\"#!/api/Ext.data.Store-method-rangeCached\" rel=\"Ext.data.Store-method-rangeCached\" class=\"docClass\">rangeCached</a>.</p>\n</div></li></ul></div></div></div><div id='method-getRejectRecords' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getRejectRecords' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getRejectRecords' class='name expandable'>getRejectRecords</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Ideally in the future this will use getModifiedRecords, where there will be a param\nto getNewRecords &amp; getUpdated...</div><div class='long'><p>Ideally in the future this will use getModifiedRecords, where there will be a param\nto getNewRecords &amp; getUpdatedRecords to indicate whether to get only the valid\nrecords or grab all of them</p>\n</div></div></div><div id='method-getRemovedRecords' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-getRemovedRecords' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-getRemovedRecords' class='name expandable'>getRemovedRecords</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</div><div class='description'><div class='short'>Returns any records that have been removed from the store but not yet destroyed on the proxy. ...</div><div class='long'><p>Returns any records that have been removed from the store but not yet destroyed on the proxy.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</span><div class='sub-desc'><p>The removed Model instances</p>\n</div></li></ul></div></div></div><div id='method-getSorters' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-method-getSorters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-method-getSorters' class='name expandable'>getSorters</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getState' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-getState' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-getState' class='name expandable'>getState</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Returns the grouping, sorting and filtered state of this Store. ...</div><div class='long'><p>Returns the grouping, sorting and filtered state of this Store.</p>\n</div></div></div><div id='method-getSum' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getSum' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getSum' class='name expandable'>getSum</a>( <span class='pre'>records, field</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>, see sum ...</div><div class='long'><p>, see sum</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>records</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>field</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getTotalCount' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getTotalCount' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getTotalCount' class='name expandable'>getTotalCount</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the total number of Model instances that the Proxy\nindicates exist. ...</div><div class='long'><p>Returns the total number of <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Model</a> instances that the <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Proxy</a>\nindicates exist. This will usually differ from <a href=\"#!/api/Ext.data.Store-method-getCount\" rel=\"Ext.data.Store-method-getCount\" class=\"docClass\">getCount</a> when using paging - getCount returns the\nnumber of records loaded into the Store at the moment, getTotalCount returns the number of records that\ncould be loaded into the Store if the Store contained all data</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The total number of Model instances available via the Proxy. 0 returned if\nno value has been set via the reader.</p>\n</div></li></ul></div></div></div><div id='method-getUpdatedRecords' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-getUpdatedRecords' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-getUpdatedRecords' class='name expandable'>getUpdatedRecords</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</div><div class='description'><div class='short'>inherit docs\n\nReturns all Model instances that have been updated in the Store but not yet synchronized with the Proxy ...</div><div class='long'><p>inherit docs</p>\n\n<p>Returns all Model instances that have been updated in the Store but not yet synchronized with the Proxy</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</span><div class='sub-desc'><p>The updated Model instances</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.AbstractStore-method-getUpdatedRecords' rel='Ext.data.AbstractStore-method-getUpdatedRecords' class='docClass'>Ext.data.AbstractStore.getUpdatedRecords</a></p></div></div></div><div id='method-group' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-group' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-group' class='name expandable'>group</a>( <span class='pre'>groupers, [direction]</span> )</div><div class='description'><div class='short'>Groups data inside the store. ...</div><div class='long'><p>Groups data inside the store.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>groupers</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]<div class='sub-desc'><p>Either a string name of one of the fields in this Store's\nconfigured <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Model</a>, or an Array of grouper configurations.</p>\n</div></li><li><span class='pre'>direction</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The overall direction to group the data by.</p>\n<p>Defaults to: <code>&quot;ASC&quot;</code></p></div></li></ul></div></div></div><div id='method-guaranteeRange' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-guaranteeRange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-guaranteeRange' class='name expandable'>guaranteeRange</a>( <span class='pre'>start, end, callback, scope, options</span> )<strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Guarantee a specific range, this will load the store with a range (that\nmust be the pageSize or smaller) and take car...</div><div class='long'><p>Guarantee a specific range, this will load the store with a range (that\nmust be the <code>pageSize</code> or smaller) and take care of any loading that may\nbe necessary.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>Use <a href=\"#!/api/Ext.data.Store-method-getRange\" rel=\"Ext.data.Store-method-getRange\" class=\"docClass\">getRange</a></p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>start</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>end</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-hasListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-hasListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-hasListener' class='name expandable'>hasListener</a>( <span class='pre'>eventName</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks to see if this object has any listeners for a specified event, or whether the event bubbles. ...</div><div class='long'><p>Checks to see if this object has any listeners for a specified event, or whether the event bubbles. The answer\nindicates whether the event needs firing or not.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the event to check for</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p><code>true</code> if the event is being listened for or bubbles, else <code>false</code></p>\n</div></li></ul></div></div></div><div id='method-indexOf' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-indexOf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-indexOf' class='name expandable'>indexOf</a>( <span class='pre'>record</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Get the index of the record within the store. ...</div><div class='long'><p>Get the index of the record within the store.</p>\n\n<p>When store is filtered, records outside of filter will not be found.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>record</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a> object to find.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The index of the passed Record. Returns -1 if not found.</p>\n</div></li></ul></div></div></div><div id='method-indexOfId' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-indexOfId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-indexOfId' class='name expandable'>indexOfId</a>( <span class='pre'>id</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Get the index within the store of the Record with the passed id. ...</div><div class='long'><p>Get the index within the store of the Record with the passed id.</p>\n\n<p>Like <a href=\"#!/api/Ext.data.Store-method-indexOf\" rel=\"Ext.data.Store-method-indexOf\" class=\"docClass\">indexOf</a>, this method is effected by filtering.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The id of the Record to find.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The index of the Record. Returns -1 if not found.</p>\n</div></li></ul></div></div></div><div id='method-indexOfTotal' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-indexOfTotal' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-indexOfTotal' class='name expandable'>indexOfTotal</a>( <span class='pre'>record</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Get the index within the entire dataset. ...</div><div class='long'><p>Get the index within the entire dataset. From 0 to the totalCount.</p>\n\n<p>Like <a href=\"#!/api/Ext.data.Store-method-indexOf\" rel=\"Ext.data.Store-method-indexOf\" class=\"docClass\">indexOf</a>, this method is effected by filtering.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>record</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a> object to find.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The index of the passed Record. Returns -1 if not found.</p>\n</div></li></ul></div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-initSortable' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='defined-in docClass'>Ext.util.Sortable</a><br/><a href='source/Sortable.html#Ext-util-Sortable-method-initSortable' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Sortable-method-initSortable' class='name expandable'>initSortable</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Performs initialization of this mixin. ...</div><div class='long'><p>Performs initialization of this mixin. Component classes using this mixin should call this method during their\nown initialization.</p>\n</div></div></div><div id='method-insert' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-insert' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-insert' class='name expandable'>insert</a>( <span class='pre'>index, records</span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</div><div class='description'><div class='short'>Inserts Model instances into the Store at the given index and fires the add event. ...</div><div class='long'><p>Inserts Model instances into the Store at the given index and fires the <a href=\"#!/api/Ext.data.Store-event-add\" rel=\"Ext.data.Store-event-add\" class=\"docClass\">add</a> event.\nSee also <a href=\"#!/api/Ext.data.Store-method-add\" rel=\"Ext.data.Store-method-add\" class=\"docClass\">add</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The start index at which to insert the passed Records.</p>\n</div></li><li><span class='pre'>records</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]<div class='sub-desc'><p>An Array of <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a> objects to add to the store.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]</span><div class='sub-desc'><p>records The added records</p>\n</div></li></ul></div></div></div><div id='method-isFiltered' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-isFiltered' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-isFiltered' class='name expandable'>isFiltered</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if this store is currently filtered ...</div><div class='long'><p>Returns <code>true</code> if this store is currently filtered</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.AbstractStore-method-isFiltered' rel='Ext.data.AbstractStore-method-isFiltered' class='docClass'>Ext.data.AbstractStore.isFiltered</a></p></div></div></div><div id='method-isGrouped' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-isGrouped' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-isGrouped' class='name expandable'>isGrouped</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks if the store is currently grouped ...</div><div class='long'><p>Checks if the store is currently grouped</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p><code>true</code> if the store is grouped.</p>\n</div></li></ul></div></div></div><div id='method-isLoading' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-isLoading' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-isLoading' class='name expandable'>isLoading</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the Store is currently performing a load operation ...</div><div class='long'><p>Returns true if the Store is currently performing a load operation</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if the Store is currently loading</p>\n</div></li></ul></div></div></div><div id='method-last' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-last' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-last' class='name expandable'>last</a>( <span class='pre'>[grouped]</span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>/undefined</div><div class='description'><div class='short'>Convenience function for getting the last model instance in the store. ...</div><div class='long'><p>Convenience function for getting the last model instance in the store.</p>\n\n<p>When store is filtered, will return last item within the filter.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>grouped</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to perform the operation for each group\nin the store. The value returned will be an object literal with the key being the group\nname and the last record being the value. The grouped parameter is only honored if\nthe store has a groupField.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>/undefined</span><div class='sub-desc'><p>The last model instance in the store, or undefined</p>\n</div></li></ul></div></div></div><div id='method-load' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-load' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-load' class='name expandable'>load</a>( <span class='pre'>[options]</span> )</div><div class='description'><div class='short'>Loads data into the Store via the configured proxy. ...</div><div class='long'><p>Loads data into the Store via the configured <a href=\"#!/api/Ext.data.Store-cfg-proxy\" rel=\"Ext.data.Store-cfg-proxy\" class=\"docClass\">proxy</a>. This uses the Proxy to make an\nasynchronous call to whatever storage backend the Proxy uses, automatically adding the retrieved\ninstances into the Store and calling an optional callback if required. Example usage:</p>\n\n<pre><code>store.load({\n    scope: this,\n    callback: function(records, operation, success) {\n        // the <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">operation</a> object\n        // contains all of the details of the load operation\n        console.log(records);\n    }\n});\n</code></pre>\n\n<p>If the callback scope does not need to be set, a function can simply be passed:</p>\n\n<pre><code>store.load(function(records, operation, success) {\n    console.log('loaded records');\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>config object, passed into the <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a> object before loading.\nAdditionally <code>addRecords: true</code> can be specified to add these records to the existing records, default is\nto remove the Store's existing records first.</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.AbstractStore-method-load' rel='Ext.data.AbstractStore-method-load' class='docClass'>Ext.data.AbstractStore.load</a></p></div></div></div><div id='method-loadData' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-loadData' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-loadData' class='name expandable'>loadData</a>( <span class='pre'>data, [append]</span> )</div><div class='description'><div class='short'>Loads an array of data straight into the Store. ...</div><div class='long'><p>Loads an array of data straight into the Store.</p>\n\n<p>Using this method is great if the data is in the correct format already (e.g. it doesn't need to be\nprocessed by a reader). If your data requires processing to decode the data structure, use a\n<a href=\"#!/api/Ext.data.proxy.Memory\" rel=\"Ext.data.proxy.Memory\" class=\"docClass\">MemoryProxy</a> or <a href=\"#!/api/Ext.data.Store-method-loadRawData\" rel=\"Ext.data.Store-method-loadRawData\" class=\"docClass\">loadRawData</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>data</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]<div class='sub-desc'><p>Array of data to load. Any non-model instances will be cast\ninto model instances first.</p>\n</div></li><li><span class='pre'>append</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p><code>true</code> to add the records to the existing records in the store, <code>false</code>\nto remove the old ones first.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul></div></div></div><div id='method-loadPage' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-loadPage' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-loadPage' class='name expandable'>loadPage</a>( <span class='pre'>page, [options]</span> )</div><div class='description'><div class='short'>Loads a given 'page' of data by setting the start and limit values appropriately. ...</div><div class='long'><p>Loads a given 'page' of data by setting the start and limit values appropriately. Internally this just causes a normal\nload operation, passing in calculated 'start' and 'limit' params.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>page</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of the page to load.</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>See options for <a href=\"#!/api/Ext.data.Store-method-load\" rel=\"Ext.data.Store-method-load\" class=\"docClass\">load</a>.</p>\n</div></li></ul></div></div></div><div id='method-loadRawData' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-loadRawData' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-loadRawData' class='name expandable'>loadRawData</a>( <span class='pre'>data, [append]</span> )</div><div class='description'><div class='short'>Loads data via the bound Proxy's reader\n\nUse this method if you are attempting to load data and want to utilize the c...</div><div class='long'><p>Loads data via the bound Proxy's reader</p>\n\n<p>Use this method if you are attempting to load data and want to utilize the configured data reader.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>data</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]<div class='sub-desc'><p>The full JSON object you'd like to load into the Data store.</p>\n</div></li><li><span class='pre'>append</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p><code>true</code> to add the records to the existing records in the store, <code>false</code>\nto remove the old ones first.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul></div></div></div><div id='method-loadRecords' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-loadRecords' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-loadRecords' class='name expandable'>loadRecords</a>( <span class='pre'>records, options</span> )</div><div class='description'><div class='short'>Loads an array of model instances into the store, fires the datachanged event. ...</div><div class='long'><p>Loads an array of <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">model</a> instances into the store, fires the datachanged event. This should only usually\nbe called internally when loading from the <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Proxy</a>, when adding records manually use <a href=\"#!/api/Ext.data.Store-method-add\" rel=\"Ext.data.Store-method-add\" class=\"docClass\">add</a> instead</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>records</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]<div class='sub-desc'><p>The array of records to load</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n<ul><li><span class='pre'>addRecords</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>Pass <code>true</code> to add these records to the existing records, <code>false</code> to remove the Store's existing records first.</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>start</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>Only used by buffered Stores. The index <em>within the overall dataset</em> of the first record in the array.</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-loadToPrefetch' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-loadToPrefetch' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-loadToPrefetch' class='name expandable'>loadToPrefetch</a>( <span class='pre'>options</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-max' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-max' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-max' class='name expandable'>max</a>( <span class='pre'>field, [grouped]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Gets the maximum value in the store. ...</div><div class='long'><p>Gets the maximum value in the store.</p>\n\n<p>When store is filtered, only items within the filter are aggregated.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>field</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The field in each record</p>\n</div></li><li><span class='pre'>grouped</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to perform the operation for each group\nin the store. The value returned will be an object literal with the key being the group\nname and the maximum in the group being the value. The grouped parameter is only honored if\nthe store has a groupField.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The maximum value, if no items exist, undefined.</p>\n</div></li></ul></div></div></div><div id='method-min' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-min' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-min' class='name expandable'>min</a>( <span class='pre'>field, [grouped]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Gets the minimum value in the store. ...</div><div class='long'><p>Gets the minimum value in the store.</p>\n\n<p>When store is filtered, only items within the filter are aggregated.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>field</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The field in each record</p>\n</div></li><li><span class='pre'>grouped</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to perform the operation for each group\nin the store. The value returned will be an object literal with the key being the group\nname and the minimum in the group being the value. The grouped parameter is only honored if\nthe store has a groupField.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The minimum value, if no items exist, undefined.</p>\n</div></li></ul></div></div></div><div id='method-mon' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-mon' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-mon' class='name expandable'>mon</a>( <span class='pre'>item, ename, [fn], [scope], [options]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Shorthand for addManagedListener. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.util.Observable-method-addManagedListener\" rel=\"Ext.util.Observable-method-addManagedListener\" class=\"docClass\">addManagedListener</a>.</p>\n\n<p>Adds listeners to any Observable object (or <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>) which are automatically removed when this Component is\ndestroyed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The item to which to add a listener/listeners.</p>\n\n</div></li><li><span class='pre'>ename</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The event name, or an object containing event name properties.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the handler function.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the scope (<code>this</code> reference)\nin which the handler function is executed.</p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the\n<a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> options.</p>\n\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><strong>Only when the <code>destroyable</code> option is specified. </strong></p>\n\n\n\n\n<p> A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call. For example:</p>\n\n\n\n\n<pre><code>this.btnListeners =  = myButton.mon({\n    destroyable: true\n    mouseover:   function() { console.log('mouseover'); },\n    mouseout:    function() { console.log('mouseout'); },\n    click:       function() { console.log('click'); }\n});\n</code></pre>\n\n\n\n\n<p>And when those listeners need to be removed:</p>\n\n\n\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.btnListeners);\n</code></pre>\n\n\n\n\n<p>or</p>\n\n\n\n\n<pre><code>this.btnListeners.destroy();\n</code></pre>\n\n</div></li></ul></div></div></div><div id='method-mun' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-mun' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-mun' class='name expandable'>mun</a>( <span class='pre'>item, ename, [fn], [scope]</span> )</div><div class='description'><div class='short'>Shorthand for removeManagedListener. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.util.Observable-method-removeManagedListener\" rel=\"Ext.util.Observable-method-removeManagedListener\" class=\"docClass\">removeManagedListener</a>.</p>\n\n<p>Removes listeners that were added by the <a href=\"#!/api/Ext.util.Observable-method-mon\" rel=\"Ext.util.Observable-method-mon\" class=\"docClass\">mon</a> method.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The item from which to remove a listener/listeners.</p>\n\n</div></li><li><span class='pre'>ename</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The event name, or an object containing event name properties.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the handler function.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the scope (<code>this</code> reference)\nin which the handler function is executed.</p>\n\n</div></li></ul></div></div></div><div id='method-nextPage' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-nextPage' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-nextPage' class='name expandable'>nextPage</a>( <span class='pre'>options</span> )</div><div class='description'><div class='short'>Loads the next 'page' in the current data set ...</div><div class='long'><p>Loads the next 'page' in the current data set</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>See options for <a href=\"#!/api/Ext.data.Store-method-load\" rel=\"Ext.data.Store-method-load\" class=\"docClass\">load</a></p>\n</div></li></ul></div></div></div><div id='method-on' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-on' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-on' class='name expandable'>on</a>( <span class='pre'>eventName, [fn], [scope], [options]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Shorthand for addListener. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a>.</p>\n\n<p>Appends an event handler to this object.  For example:</p>\n\n<pre><code>myGridPanel.on(\"mouseover\", this.onMouseOver, this);\n</code></pre>\n\n<p>The method also allows for a single argument to be passed which is a config object\ncontaining properties which specify multiple events. For example:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: this.onCellClick,\n    mouseover: this.onMouseOver,\n    mouseout: this.onMouseOut,\n    scope: this // Important. Ensure \"this\" is correct during handler execution\n});\n</code></pre>\n\n<p>One can also specify options for each event handler separately:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: this.onCellClick, scope: this, single: true},\n    mouseover: {fn: panel.onMouseOver, scope: panel}\n});\n</code></pre>\n\n<p><em>Names</em> of methods in a specified scope may also be used. Note that\n<code>scope</code> MUST be specified to use this option:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: 'onCellClick', scope: this, single: true},\n    mouseover: {fn: 'onMouseOver', scope: panel}\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The name of the event to listen for.\nMay also be an object who's property names are event names.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The method the event invokes, or <em>if <code>scope</code> is specified, the </em>name* of the method within\nthe specified <code>scope</code>.  Will be called with arguments\ngiven to <a href=\"#!/api/Ext.util.Observable-method-fireEvent\" rel=\"Ext.util.Observable-method-fireEvent\" class=\"docClass\">fireEvent</a> plus the <code>options</code> parameter described below.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is\nexecuted. <strong>If omitted, defaults to the object which fired the event.</strong></p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>An object containing handler configuration.</p>\n\n\n\n\n<p><strong>Note:</strong> Unlike in ExtJS 3.x, the options object will also be passed as the last\nargument to every event handler.</p>\n\n\n\n\n<p>This object may contain any of the following properties:</p>\n\n<ul><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is executed. <strong>If omitted,\n  defaults to the object which fired the event.</strong></p>\n\n</div></li><li><span class='pre'>delay</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of milliseconds to delay the invocation of the handler after the event fires.</p>\n\n</div></li><li><span class='pre'>single</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to add a handler to handle just the next firing of the event, and then remove itself.</p>\n\n</div></li><li><span class='pre'>buffer</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Causes the handler to be scheduled to run in an <a href=\"#!/api/Ext.util.DelayedTask\" rel=\"Ext.util.DelayedTask\" class=\"docClass\">Ext.util.DelayedTask</a> delayed\n  by the specified number of milliseconds. If the event fires again within that time,\n  the original handler is <em>not</em> invoked, but the new handler is scheduled in its place.</p>\n\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a><div class='sub-desc'><p>Only call the handler if the event was fired on the target Observable, <em>not</em> if the event\n  was bubbled up from a child Observable.</p>\n\n</div></li><li><span class='pre'>element</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p><strong>This option is only valid for listeners bound to <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a>.</strong>\n  The name of a Component property which references an element to add a listener to.</p>\n\n\n\n\n<p>  This option is useful during Component construction to add DOM event listeners to elements of\n  <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a> which will exist only after the Component is rendered.\n  For example, to add a click listener to a Panel's body:</p>\n\n\n\n\n<pre><code>  new <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>({\n      title: 'The title',\n      listeners: {\n          click: this.handlePanelClick,\n          element: 'body'\n      }\n  });\n</code></pre>\n\n</div></li><li><span class='pre'>destroyable</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>When specified as <code>true</code>, the function returns A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call.</p>\n\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>priority</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>An optional numeric priority that determines the order in which event handlers\n  are run. Event handlers with no priority will be run as if they had a priority\n  of 0. Handlers with a higher priority will be prioritized to run sooner than\n  those with a lower priority.  Negative numbers can be used to set a priority\n  lower than the default. Internally, the framework uses a range of 1000 or\n  greater, and -1000 or lesser for handers that are intended to run before or\n  after all others, so it is recommended to stay within the range of -999 to 999\n  when setting the priority of event handlers in application-level code.</p>\n\n\n\n\n<p><strong>Combining Options</strong></p>\n\n\n\n\n<p>Using the options argument, it is possible to combine different types of listeners:</p>\n\n\n\n\n<p>A delayed, one-time listener.</p>\n\n\n\n\n<pre><code>myPanel.on('hide', this.handleClick, this, {\n    single: true,\n    delay: 100\n});\n</code></pre>\n\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><strong>Only when the <code>destroyable</code> option is specified. </strong></p>\n\n\n\n\n<p> A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call. For example:</p>\n\n\n\n\n<pre><code>this.btnListeners =  = myButton.on({\n    destroyable: true\n    mouseover:   function() { console.log('mouseover'); },\n    mouseout:    function() { console.log('mouseout'); },\n    click:       function() { console.log('click'); }\n});\n</code></pre>\n\n\n\n\n<p>And when those listeners need to be removed:</p>\n\n\n\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.btnListeners);\n</code></pre>\n\n\n\n\n<p>or</p>\n\n\n\n\n<pre><code>this.btnListeners.destroy();\n</code></pre>\n\n</div></li></ul></div></div></div><div id='method-onBatchComplete' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onBatchComplete' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onBatchComplete' class='name expandable'>onBatchComplete</a>( <span class='pre'>batch, operation</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Attached as the 'complete' event listener to a proxy's Batch object. ...</div><div class='long'><p>Attached as the 'complete' event listener to a proxy's Batch object. Iterates over the batch operations\nand updates the Store's internal data MixedCollection.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>batch</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onBatchException' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onBatchException' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onBatchException' class='name expandable'>onBatchException</a>( <span class='pre'>batch, operation</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>batch</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onBatchOperationComplete' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onBatchOperationComplete' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onBatchOperationComplete' class='name expandable'>onBatchOperationComplete</a>( <span class='pre'>batch, operation</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Attached as the 'operationcomplete' event listener to a proxy's Batch object. ...</div><div class='long'><p>Attached as the 'operationcomplete' event listener to a proxy's Batch object. By default just calls through\nto onProxyWrite.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>batch</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onBeforeSort' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-onBeforeSort' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-onBeforeSort' class='name expandable'>onBeforeSort</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.util.Sortable-method-onBeforeSort' rel='Ext.util.Sortable-method-onBeforeSort' class='docClass'>Ext.util.Sortable.onBeforeSort</a></p></div></div></div><div id='method-onClassExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onClassExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onClassExtended' class='name expandable'>onClassExtended</a>( <span class='pre'>cls, data, hooks</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>cls</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>data</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>hooks</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onCreateRecords' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onCreateRecords' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onCreateRecords' class='name expandable'>onCreateRecords</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>may be implemented by store subclasses ...</div><div class='long'><p>may be implemented by store subclasses</p>\n</div></div></div><div id='method-onDestroyRecords' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onDestroyRecords' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onDestroyRecords' class='name expandable'>onDestroyRecords</a>( <span class='pre'>records, operation, success</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Removes any records when a write is returned from the server. ...</div><div class='long'><p>Removes any records when a write is returned from the server.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>records</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]<div class='sub-desc'><p>The array of removed records</p>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The operation that just completed</p>\n</div></li><li><span class='pre'>success</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True if the operation was successful</p>\n</div></li></ul></div></div></div><div id='method-onGuaranteedRange' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-onGuaranteedRange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-onGuaranteedRange' class='name expandable'>onGuaranteedRange</a>( <span class='pre'>options</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Handles a guaranteed range being loaded ...</div><div class='long'><p>Handles a guaranteed range being loaded</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onIdChanged' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-onIdChanged' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-onIdChanged' class='name expandable'>onIdChanged</a>( <span class='pre'>rec, oldId, newId, oldInternalId</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>rec</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>oldId</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>newId</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>oldInternalId</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.AbstractStore-method-onIdChanged' rel='Ext.data.AbstractStore-method-onIdChanged' class='docClass'>Ext.data.AbstractStore.onIdChanged</a></p></div></div></div><div id='method-onMetaChange' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onMetaChange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onMetaChange' class='name expandable'>onMetaChange</a>( <span class='pre'>proxy, meta</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>private ...</div><div class='long'><p>private</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>proxy</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>meta</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onPageMapClear' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-onPageMapClear' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-onPageMapClear' class='name expandable'>onPageMapClear</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Cancels all pending prefetch requests. ...</div><div class='long'><p>Cancels all pending prefetch requests.</p>\n\n<p>This is called when the page map is cleared.</p>\n\n<p>Any requests which still make it through will be for the previous pageMapGeneration\n(pageMapGeneration is incremented upon clear), and so will be rejected upon arrival.</p>\n</div></div></div><div id='method-onProxyLoad' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-onProxyLoad' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-onProxyLoad' class='name expandable'>onProxyLoad</a>( <span class='pre'>operation</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Called internally when a Proxy has completed a load request ...</div><div class='long'><p>Called internally when a Proxy has completed a load request</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>operation</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onProxyPrefetch' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-onProxyPrefetch' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-onProxyPrefetch' class='name expandable'>onProxyPrefetch</a>( <span class='pre'>operation</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Called after the configured proxy completes a prefetch operation. ...</div><div class='long'><p>Called after the configured proxy completes a prefetch operation.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The operation that completed</p>\n</div></li></ul></div></div></div><div id='method-onProxyWrite' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onProxyWrite' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onProxyWrite' class='name expandable'>onProxyWrite</a>( <span class='pre'>operation</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Callback for any write Operation over the Proxy. ...</div><div class='long'><p>Callback for any write Operation over the Proxy. Updates the Store's MixedCollection to reflect\nthe updates provided by the Proxy</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>operation</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-onUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-onUpdate' class='name expandable'>onUpdate</a>( <span class='pre'>record, type, modifiedFieldNames</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>record</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>type</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>modifiedFieldNames</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.AbstractStore-method-onUpdate' rel='Ext.data.AbstractStore-method-onUpdate' class='docClass'>Ext.data.AbstractStore.onUpdate</a></p></div></div></div><div id='method-onUpdateRecords' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-onUpdateRecords' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-onUpdateRecords' class='name expandable'>onUpdateRecords</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>may be implemented by store subclasses ...</div><div class='long'><p>may be implemented by store subclasses</p>\n</div></div></div><div id='method-pageCached' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-pageCached' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-pageCached' class='name expandable'>pageCached</a>( <span class='pre'>page</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Determines if the passed page is available in the page cache. ...</div><div class='long'><p>Determines if the passed page is available in the page cache.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>page</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The page to find in the page cache.</p>\n</div></li></ul></div></div></div><div id='method-pagePending' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-pagePending' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-pagePending' class='name expandable'>pagePending</a>( <span class='pre'>page</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Determines if a request for a page is currently running ...</div><div class='long'><p>Determines if a request for a page is currently running</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>page</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The page to check for</p>\n</div></li></ul></div></div></div><div id='method-prefetch' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-prefetch' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-prefetch' class='name expandable'>prefetch</a>( <span class='pre'>[options]</span> )</div><div class='description'><div class='short'>Prefetches data into the store using its configured proxy. ...</div><div class='long'><p>Prefetches data into the store using its configured <a href=\"#!/api/Ext.data.Store-cfg-proxy\" rel=\"Ext.data.Store-cfg-proxy\" class=\"docClass\">proxy</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>config object, passed into the <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a> object before loading.\nSee <a href=\"#!/api/Ext.data.Store-method-load\" rel=\"Ext.data.Store-method-load\" class=\"docClass\">load</a></p>\n</div></li></ul></div></div></div><div id='method-prefetchPage' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-prefetchPage' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-prefetchPage' class='name expandable'>prefetchPage</a>( <span class='pre'>page, [options]</span> )</div><div class='description'><div class='short'>Prefetches a page of data. ...</div><div class='long'><p>Prefetches a page of data.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>page</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The page to prefetch</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>config object, passed into the <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a> object before loading.\nSee <a href=\"#!/api/Ext.data.Store-method-load\" rel=\"Ext.data.Store-method-load\" class=\"docClass\">load</a></p>\n</div></li></ul></div></div></div><div id='method-prefetchRange' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-prefetchRange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-prefetchRange' class='name expandable'>prefetchRange</a>( <span class='pre'>start, end</span> )</div><div class='description'><div class='short'>Ensures that the specified range of rows is present in the cache. ...</div><div class='long'><p>Ensures that the specified range of rows is present in the cache.</p>\n\n<p>Converts the row range to a page range and then only load pages which are not already\npresent in the page cache.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>start</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>end</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-prepareClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-prepareClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-prepareClass' class='name expandable'>prepareClass</a>( <span class='pre'>T</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Prepares a given class for observable instances. ...</div><div class='long'><p>Prepares a given class for observable instances. This method is called when a\nclass derives from this class or uses this class as a mixin.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>T</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The class constructor to prepare.</p>\n</div></li></ul></div></div></div><div id='method-previousPage' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-previousPage' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-previousPage' class='name expandable'>previousPage</a>( <span class='pre'>options</span> )</div><div class='description'><div class='short'>Loads the previous 'page' in the current data set ...</div><div class='long'><p>Loads the previous 'page' in the current data set</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>See options for <a href=\"#!/api/Ext.data.Store-method-load\" rel=\"Ext.data.Store-method-load\" class=\"docClass\">load</a></p>\n</div></li></ul></div></div></div><div id='method-primeCache' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-primeCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-primeCache' class='name expandable'>primeCache</a>( <span class='pre'>start, end, direction</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>start</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>end</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>direction</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-query' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-query' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-query' class='name expandable'>query</a>( <span class='pre'>property, value, [anyMatch], [caseSensitive], [exactMatch]</span> ) : <a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">Ext.util.MixedCollection</a></div><div class='description'><div class='short'>Query all the cached records in this Store by name/value pair. ...</div><div class='long'><p>Query all the cached records in this Store by name/value pair.\nThe parameters will be used to generated a filter function that is given\nto the queryBy method.</p>\n\n<p>This method compliments queryBy by generating the query function automatically.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>property</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The property to create the filter function for</p>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a><div class='sub-desc'><p>The string/regex to compare the property value to</p>\n</div></li><li><span class='pre'>anyMatch</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p><code>true</code> if we don't care if the filter value is not the full value.</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>caseSensitive</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p><code>true</code> to create a case-sensitive regex.</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>exactMatch</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p><code>true</code> to force exact match (^ and $ characters added to the regex).\nIgnored if <code>anyMatch</code> is <code>true</code>.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">Ext.util.MixedCollection</a></span><div class='sub-desc'><p>Returns an <a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">Ext.util.MixedCollection</a> of the matched records</p>\n</div></li></ul></div></div></div><div id='method-queryBy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-queryBy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-queryBy' class='name expandable'>queryBy</a>( <span class='pre'>fn, [scope]</span> ) : <a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">Ext.util.MixedCollection</a></div><div class='description'><div class='short'>Query all the cached records in this Store using a filtering function. ...</div><div class='long'><p>Query all the cached records in this Store using a filtering function. The specified function\nwill be called with each record in this Store. If the function returns <code>true</code> the record is\nincluded in the results.</p>\n\n<p>This method is not effected by filtering, it will always look from all records inside the store\nno matter if filter is applied or not.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The function to be called. It will be passed the following parameters:</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>record</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The record to test for filtering. Access field values\n using <a href=\"#!/api/Ext.data.Model-method-get\" rel=\"Ext.data.Model-method-get\" class=\"docClass\">Ext.data.Model.get</a>.</p>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The ID of the Record passed.</p>\n</div></li></ul></div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (this reference) in which the function is executed\nDefaults to this Store.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">Ext.util.MixedCollection</a></span><div class='sub-desc'><p>Returns an <a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">Ext.util.MixedCollection</a> of the matched records</p>\n</div></li></ul></div></div></div><div id='method-rangeCached' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-rangeCached' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-rangeCached' class='name expandable'>rangeCached</a>( <span class='pre'>start, end</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Determines if the passed range is available in the page cache. ...</div><div class='long'><p>Determines if the passed range is available in the page cache.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>start</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The start index</p>\n</div></li><li><span class='pre'>end</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The end index in the range</p>\n</div></li></ul></div></div></div><div id='method-rangeSatisfied' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-rangeSatisfied' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-rangeSatisfied' class='name expandable'>rangeSatisfied</a>( <span class='pre'>start, end</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><strong class='deprecated signature' >deprecated</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Determines if the passed range is available in the page cache. ...</div><div class='long'><p>Determines if the passed range is available in the page cache.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>use <a href=\"#!/api/Ext.data.Store-method-rangeCached\" rel=\"Ext.data.Store-method-rangeCached\" class=\"docClass\">rangeCached</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>start</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The start index</p>\n</div></li><li><span class='pre'>end</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The end index in the range</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-read' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-read' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-read' class='name expandable'>read</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-rejectChanges' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-rejectChanges' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-rejectChanges' class='name expandable'>rejectChanges</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Rejects outstanding changes on all modified records\nand re-insert any records that were removed locally. ...</div><div class='long'><p><a href=\"#!/api/Ext.data.Model-method-reject\" rel=\"Ext.data.Model-method-reject\" class=\"docClass\">Rejects</a> outstanding changes on all <a href=\"#!/api/Ext.data.Store-method-getModifiedRecords\" rel=\"Ext.data.Store-method-getModifiedRecords\" class=\"docClass\">modified records</a>\nand re-insert any records that were removed locally. Any phantom records will be removed.</p>\n</div></div></div><div id='method-relayEvents' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-relayEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-relayEvents' class='name expandable'>relayEvents</a>( <span class='pre'>origin, events, [prefix]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Relays selected events from the specified Observable as if the events were fired by this. ...</div><div class='long'><p>Relays selected events from the specified Observable as if the events were fired by <code>this</code>.</p>\n\n<p>For example if you are extending Grid, you might decide to forward some events from store.\nSo you can do this inside your initComponent:</p>\n\n<pre><code>this.relayEvents(this.getStore(), ['load']);\n</code></pre>\n\n<p>The grid instance will then have an observable 'load' event which will be passed the\nparameters of the store's load event and any function fired with the grid's load event\nwould have access to the grid using the <code>this</code> keyword.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>origin</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The Observable whose events this object is to relay.</p>\n</div></li><li><span class='pre'>events</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>Array of event names to relay.</p>\n</div></li><li><span class='pre'>prefix</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>A common prefix to prepend to the event names. For example:</p>\n\n<pre><code>this.relayEvents(this.getStore(), ['load', 'clear'], 'store');\n</code></pre>\n\n<p>Now the grid will forward 'load' and 'clear' events of store as 'storeload' and 'storeclear'.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which, when destroyed, removes all relayers. For example:</p>\n\n<pre><code>this.storeRelayers = this.relayEvents(this.getStore(), ['load', 'clear'], 'store');\n</code></pre>\n\n<p>Can be undone by calling</p>\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.storeRelayers);\n</code></pre>\n\n<p>or</p>\n\n<pre><code>this.store.relayers.destroy();\n</code></pre>\n</div></li></ul></div></div></div><div id='method-reload' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-reload' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-reload' class='name expandable'>reload</a>( <span class='pre'>options</span> )</div><div class='description'><div class='short'>Reloads the store using the last options passed to the load method. ...</div><div class='long'><p>Reloads the store using the last options passed to the <a href=\"#!/api/Ext.data.Store-method-load\" rel=\"Ext.data.Store-method-load\" class=\"docClass\">load</a> method.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>A config object which contains options which may override the options passed to the previous load call.</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.data.AbstractStore-method-reload' rel='Ext.data.AbstractStore-method-reload' class='docClass'>Ext.data.AbstractStore.reload</a></p></div></div></div><div id='method-remove' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-remove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-remove' class='name expandable'>remove</a>( <span class='pre'>records</span> )</div><div class='description'><div class='short'>Removes the specified record(s) from the Store, firing the remove event for each instance that is removed. ...</div><div class='long'><p>Removes the specified record(s) from the Store, firing the <a href=\"#!/api/Ext.data.Store-event-remove\" rel=\"Ext.data.Store-event-remove\" class=\"docClass\">remove</a> event for each instance that is removed.</p>\n\n<p>A <a href=\"#!/api/Ext.data.Store-event-bulkremove\" rel=\"Ext.data.Store-event-bulkremove\" class=\"docClass\">bulkremove</a> event is called at the end passing all removed records and their indices.\nplus a single 'datachanged' event after removal.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>records</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>/<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]/<a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]<div class='sub-desc'><p>Model instance or array of instances to remove or an array of indices from which to remove records.</p>\n</div></li></ul></div></div></div><div id='method-removeAll' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-removeAll' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-removeAll' class='name expandable'>removeAll</a>( <span class='pre'>[silent]</span> )</div><div class='description'><div class='short'>Removes all items from the store. ...</div><div class='long'><p>Removes all items from the store.</p>\n\n<p>Individual record <code><a href=\"#!/api/Ext.data.Store-event-remove\" rel=\"Ext.data.Store-event-remove\" class=\"docClass\">remove</a></code> events are not fired by this method.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>silent</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>Pass <code>true</code> to prevent the record <code><a href=\"#!/api/Ext.data.Store-event-bulkremove\" rel=\"Ext.data.Store-event-bulkremove\" class=\"docClass\">bulkremove</a></code>\nand <code><a href=\"#!/api/Ext.data.Store-event-clear\" rel=\"Ext.data.Store-event-clear\" class=\"docClass\">clear</a></code> events from being fired.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><p>Overrides: <a href='#!/api/Ext.data.AbstractStore-method-removeAll' rel='Ext.data.AbstractStore-method-removeAll' class='docClass'>Ext.data.AbstractStore.removeAll</a></p></div></div></div><div id='method-removeAt' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-removeAt' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-removeAt' class='name expandable'>removeAt</a>( <span class='pre'>index, [count]</span> )</div><div class='description'><div class='short'>Removes the model instance(s) at the given index ...</div><div class='long'><p>Removes the model instance(s) at the given index</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The record index</p>\n</div></li><li><span class='pre'>count</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>The number of records to delete</p>\n<p>Defaults to: <code>1</code></p></div></li></ul></div></div></div><div id='method-removeFilter' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-removeFilter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-removeFilter' class='name expandable'>removeFilter</a>( <span class='pre'>toRemove, [applyFilters]</span> )</div><div class='description'><div class='short'>Removes an individual Filter from the current filter set using the passed Filter/Filter id and\nby default, applys the...</div><div class='long'><p>Removes an individual Filter from the current <a href=\"#!/api/Ext.data.Store-property-filters\" rel=\"Ext.data.Store-property-filters\" class=\"docClass\">filter set</a> using the passed Filter/Filter id and\nby default, applys the updated filter set to the Store's unfiltered dataset.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>toRemove</span> : Mixed<div class='sub-desc'><p>The id of a Filter to remove from the filter set, or a Filter instance to remove.</p>\n</div></li><li><span class='pre'>applyFilters</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>Pass as <code>false</code> to remove the filter but not apply the updated filter set.</p>\n\n<p>If <code>null</code> is passed, all anonymous Filters (Filters with no <code>id</code> property) will be removed.</p>\n<p>Defaults to: <code>true</code></p></div></li></ul></div></div></div><div id='method-removeListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-removeListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-removeListener' class='name expandable'>removeListener</a>( <span class='pre'>eventName, fn, [scope]</span> )</div><div class='description'><div class='short'>Removes an event handler. ...</div><div class='long'><p>Removes an event handler.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The type of event the handler was associated with.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The handler to remove. <strong>This must be a reference to the function passed into the\n<a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> call.</strong></p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope originally specified for the handler. It must be the same as the\nscope argument specified in the original call to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> or the listener will not be removed.</p>\n\n</div></li></ul></div></div></div><div id='method-removeManagedListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-removeManagedListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-removeManagedListener' class='name expandable'>removeManagedListener</a>( <span class='pre'>item, ename, [fn], [scope]</span> )</div><div class='description'><div class='short'>Removes listeners that were added by the mon method. ...</div><div class='long'><p>Removes listeners that were added by the <a href=\"#!/api/Ext.util.Observable-method-mon\" rel=\"Ext.util.Observable-method-mon\" class=\"docClass\">mon</a> method.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The item from which to remove a listener/listeners.</p>\n\n</div></li><li><span class='pre'>ename</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The event name, or an object containing event name properties.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the handler function.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the scope (<code>this</code> reference)\nin which the handler function is executed.</p>\n\n</div></li></ul></div></div></div><div id='method-removeManagedListenerItem' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-removeManagedListenerItem' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-removeManagedListenerItem' class='name expandable'>removeManagedListenerItem</a>( <span class='pre'>isClear, managedListener</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Remove a single managed listener item ...</div><div class='long'><p>Remove a single managed listener item</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>isClear</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True if this is being called during a clear</p>\n</div></li><li><span class='pre'>managedListener</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The managed listener item\nSee removeManagedListener for other args</p>\n</div></li></ul></div></div></div><div id='method-resumeAutoSync' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-resumeAutoSync' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-resumeAutoSync' class='name expandable'>resumeAutoSync</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Resumes automatically syncing the Store with its Proxy. ...</div><div class='long'><p>Resumes automatically syncing the Store with its Proxy.  Only applicable if <a href=\"#!/api/Ext.data.AbstractStore-cfg-autoSync\" rel=\"Ext.data.AbstractStore-cfg-autoSync\" class=\"docClass\">autoSync</a> is <code>true</code></p>\n</div></div></div><div id='method-resumeEvent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-resumeEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-resumeEvent' class='name expandable'>resumeEvent</a>( <span class='pre'>eventName</span> )</div><div class='description'><div class='short'>Resumes firing of the named event(s). ...</div><div class='long'><p>Resumes firing of the named event(s).</p>\n\n<p>After calling this method to resume events, the events will fire when requested to fire.</p>\n\n<p><strong>Note that if the <a href=\"#!/api/Ext.util.Observable-method-suspendEvent\" rel=\"Ext.util.Observable-method-suspendEvent\" class=\"docClass\">suspendEvent</a> method is called multiple times for a certain event,\nthis converse method will have to be called the same number of times for it to resume firing.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>...<div class='sub-desc'><p>Multiple event names to resume.</p>\n</div></li></ul></div></div></div><div id='method-resumeEvents' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-resumeEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-resumeEvents' class='name expandable'>resumeEvents</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Resumes firing events (see suspendEvents). ...</div><div class='long'><p>Resumes firing events (see <a href=\"#!/api/Ext.util.Observable-method-suspendEvents\" rel=\"Ext.util.Observable-method-suspendEvents\" class=\"docClass\">suspendEvents</a>).</p>\n\n<p>If events were suspended using the <code>queueSuspended</code> parameter, then all events fired\nduring event suspension will be sent to any listeners now.</p>\n</div></div></div><div id='method-save' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-save' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-save' class='name expandable'>save</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Saves all pending changes via the configured proxy. ...</div><div class='long'><p>Saves all pending changes via the configured <a href=\"#!/api/Ext.data.AbstractStore-cfg-proxy\" rel=\"Ext.data.AbstractStore-cfg-proxy\" class=\"docClass\">proxy</a>. Use <a href=\"#!/api/Ext.data.AbstractStore-method-sync\" rel=\"Ext.data.AbstractStore-method-sync\" class=\"docClass\">sync</a> instead.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0.0</p>\n        <p>Will be removed in the next major version</p>\n\n        </div>\n</div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setProxy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-setProxy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-setProxy' class='name expandable'>setProxy</a>( <span class='pre'>proxy</span> ) : <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a></div><div class='description'><div class='short'>Sets the Store's Proxy by string, config object or Proxy instance ...</div><div class='long'><p>Sets the Store's Proxy by string, config object or Proxy instance</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>proxy</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a><div class='sub-desc'><p>The new Proxy, which can be either a type string, a configuration object\nor an <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a> instance</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a></span><div class='sub-desc'><p>The attached Proxy object</p>\n</div></li></ul></div></div></div><div id='method-sort' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-sort' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-sort' class='name expandable'>sort</a>( <span class='pre'>[sorters], [direction]</span> ) : <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a>[]</div><div class='description'><div class='short'>because prefetchData is stored by index\nthis invalidates all of the prefetchedData\n\nSorts the data in the Store by on...</div><div class='long'><p>because prefetchData is stored by index\nthis invalidates all of the prefetchedData</p>\n\n<p>Sorts the data in the Store by one or more of its properties. Example usage:</p>\n\n<pre><code>//sort by a single field\nmyStore.sort('myField', 'DESC');\n\n//sorting by multiple fields\nmyStore.sort([\n    {\n        property : 'age',\n        direction: 'ASC'\n    },\n    {\n        property : 'name',\n        direction: 'DESC'\n    }\n]);\n</code></pre>\n\n<p>Internally, Store converts the passed arguments into an array of <a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a> instances, and delegates\nthe actual sorting to its internal <a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">Ext.util.MixedCollection</a>.</p>\n\n<p>When passing a single string argument to sort, Store maintains a ASC/DESC toggler per field, so this code:</p>\n\n<pre><code>store.sort('myField');\nstore.sort('myField');\n</code></pre>\n\n<p>Is equivalent to this code, because Store handles the toggling automatically:</p>\n\n<pre><code>store.sort('myField', 'ASC');\nstore.sort('myField', 'DESC');\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>sorters</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a>[] (optional)<div class='sub-desc'><p>Either a string name of one of the fields in this Store's configured\n<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Model</a>, or an array of sorter configurations.</p>\n</div></li><li><span class='pre'>direction</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The overall direction to sort the data by.</p>\n<p>Defaults to: <code>&quot;ASC&quot;</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Sorter\" rel=\"Ext.util.Sorter\" class=\"docClass\">Ext.util.Sorter</a>[]</span><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.util.Sortable-method-sort' rel='Ext.util.Sortable-method-sort' class='docClass'>Ext.util.Sortable.sort</a></p></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-sum' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-sum' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-sum' class='name expandable'>sum</a>( <span class='pre'>field, [grouped]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sums the value of field for each record in store\nand returns the result. ...</div><div class='long'><p>Sums the value of <code>field</code> for each <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">record</a> in store\nand returns the result.</p>\n\n<p>When store is filtered, only sums items within the filter.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>field</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>A field in each record</p>\n</div></li><li><span class='pre'>grouped</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to perform the operation for each group\nin the store. The value returned will be an object literal with the key being the group\nname and the sum for that group being the value. The grouped parameter is only honored if\nthe store has a groupField.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The sum</p>\n</div></li></ul></div></div></div><div id='method-suspendAutoSync' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-suspendAutoSync' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-suspendAutoSync' class='name expandable'>suspendAutoSync</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Suspends automatically syncing the Store with its Proxy. ...</div><div class='long'><p>Suspends automatically syncing the Store with its Proxy.  Only applicable if <a href=\"#!/api/Ext.data.AbstractStore-cfg-autoSync\" rel=\"Ext.data.AbstractStore-cfg-autoSync\" class=\"docClass\">autoSync</a> is <code>true</code></p>\n</div></div></div><div id='method-suspendEvent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-suspendEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-suspendEvent' class='name expandable'>suspendEvent</a>( <span class='pre'>eventName</span> )</div><div class='description'><div class='short'>Suspends firing of the named event(s). ...</div><div class='long'><p>Suspends firing of the named event(s).</p>\n\n<p>After calling this method to suspend events, the events will no longer fire when requested to fire.</p>\n\n<p><strong>Note that if this is called multiple times for a certain event, the converse method\n<a href=\"#!/api/Ext.util.Observable-method-resumeEvent\" rel=\"Ext.util.Observable-method-resumeEvent\" class=\"docClass\">resumeEvent</a> will have to be called the same number of times for it to resume firing.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>...<div class='sub-desc'><p>Multiple event names to suspend.</p>\n</div></li></ul></div></div></div><div id='method-suspendEvents' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-suspendEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-suspendEvents' class='name expandable'>suspendEvents</a>( <span class='pre'>queueSuspended</span> )</div><div class='description'><div class='short'>Suspends the firing of all events. ...</div><div class='long'><p>Suspends the firing of all events. (see <a href=\"#!/api/Ext.util.Observable-method-resumeEvents\" rel=\"Ext.util.Observable-method-resumeEvents\" class=\"docClass\">resumeEvents</a>)</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>queueSuspended</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>Pass as true to queue up suspended events to be fired\nafter the <a href=\"#!/api/Ext.util.Observable-method-resumeEvents\" rel=\"Ext.util.Observable-method-resumeEvents\" class=\"docClass\">resumeEvents</a> call instead of discarding all suspended events.</p>\n</div></li></ul></div></div></div><div id='method-sync' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-sync' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-sync' class='name expandable'>sync</a>( <span class='pre'>[options]</span> ) : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a></div><div class='description'><div class='short'>Synchronizes the store with its proxy. ...</div><div class='long'><p>Synchronizes the store with its <a href=\"#!/api/Ext.data.AbstractStore-cfg-proxy\" rel=\"Ext.data.AbstractStore-cfg-proxy\" class=\"docClass\">proxy</a>. This asks the proxy to batch together any new, updated\nand deleted records in the store, updating the store's internal representation of the records\nas each operation completes.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Object containing one or more properties supported by the sync method (these get\npassed along to the underlying proxy's <a href=\"#!/api/Ext.data.proxy.Proxy-method-batch\" rel=\"Ext.data.proxy.Proxy-method-batch\" class=\"docClass\">batch</a> method):</p>\n<ul><li><span class='pre'>batch</span> : <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>A <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a> object (or batch config to apply\nto the created batch). If unspecified a default batch will be auto-created as needed.</p>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The function to be called upon completion of the sync.\nThe callback is called regardless of success or failure and is passed the following parameters:</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>batch</span> : <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">batch</a> that was processed,\ncontaining all operations in their current state after processing</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options argument that was originally passed into sync</p>\n</div></li></ul></div></li><li><span class='pre'>success</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The function to be called upon successful completion of the sync. The\nsuccess function is called only if no exceptions were reported in any operations. If one or more exceptions\noccurred then the failure function will be called instead. The success function is called\nwith the following parameters:</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>batch</span> : <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">batch</a> that was processed,\ncontaining all operations in their current state after processing</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options argument that was originally passed into sync</p>\n</div></li></ul></div></li><li><span class='pre'>failure</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The function to be called upon unsuccessful completion of the sync. The\nfailure function is called when one or more operations returns an exception during processing (even if some\noperations were also successful). In this case you can check the batch's <a href=\"#!/api/Ext.data.Batch-property-exceptions\" rel=\"Ext.data.Batch-property-exceptions\" class=\"docClass\">exceptions</a> array to see exactly which operations had exceptions. The failure function is called with the\nfollowing parameters:</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>batch</span> : <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.data.Batch\" rel=\"Ext.data.Batch\" class=\"docClass\">Ext.data.Batch</a> that was processed, containing all\noperations in their current state after processing</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options argument that was originally passed into sync</p>\n</div></li></ul></div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope in which to execute any callbacks (i.e. the <code>this</code> object inside\nthe callback, success and/or failure functions). Defaults to the store's proxy.</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-un' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-un' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-un' class='name expandable'>un</a>( <span class='pre'>eventName, fn, [scope]</span> )</div><div class='description'><div class='short'>Shorthand for removeListener. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.util.Observable-method-removeListener\" rel=\"Ext.util.Observable-method-removeListener\" class=\"docClass\">removeListener</a>.</p>\n\n<p>Removes an event handler.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The type of event the handler was associated with.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The handler to remove. <strong>This must be a reference to the function passed into the\n<a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> call.</strong></p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope originally specified for the handler. It must be the same as the\nscope argument specified in the original call to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> or the listener will not be removed.</p>\n\n</div></li></ul></div></div></div><div id='method-update' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-method-update' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-method-update' class='name expandable'>update</a>( <span class='pre'>options</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-updateGroupsOnAdd' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-updateGroupsOnAdd' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-updateGroupsOnAdd' class='name expandable'>updateGroupsOnAdd</a>( <span class='pre'>records</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>records</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-updateGroupsOnRemove' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-updateGroupsOnRemove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-updateGroupsOnRemove' class='name expandable'>updateGroupsOnRemove</a>( <span class='pre'>records</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>records</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-updateGroupsOnUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-method-updateGroupsOnUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-method-updateGroupsOnUpdate' class='name expandable'>updateGroupsOnUpdate</a>( <span class='pre'>record, modifiedFieldNames</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>record</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>modifiedFieldNames</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-event'>Events</h3><div class='subsection'><div id='event-add' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-add' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-add' class='name expandable'>add</a>( <span class='pre'>store, records, index, eOpts</span> )</div><div class='description'><div class='short'>Fired when a Model instance has been added to this Store. ...</div><div class='long'><p>Fired when a Model instance has been added to this Store.</p>\n        <p>Available since: <b>1.1.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>store</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>The store</p>\n</div></li><li><span class='pre'>records</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]<div class='sub-desc'><p>The Model instances that were added</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The index at which the instances were inserted</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-beforeload' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-beforeload' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-beforeload' class='name expandable'>beforeload</a>( <span class='pre'>store, operation, eOpts</span> )</div><div class='description'><div class='short'>Fires before a request is made for a new data object. ...</div><div class='long'><p>Fires before a request is made for a new data object. If the beforeload handler returns false the load\naction will be canceled.</p>\n        <p>Available since: <b>1.1.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>store</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>This Store</p>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a> object that will be passed to the Proxy to\nload the Store</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-beforeprefetch' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-event-beforeprefetch' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-event-beforeprefetch' class='name expandable'>beforeprefetch</a>( <span class='pre'>this, operation, eOpts</span> )</div><div class='description'><div class='short'>Fires before a prefetch occurs. ...</div><div class='long'><p>Fires before a prefetch occurs. Return <code>false</code> to cancel.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The associated operation.</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-beforesync' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-beforesync' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-beforesync' class='name expandable'>beforesync</a>( <span class='pre'>options, eOpts</span> )</div><div class='description'><div class='short'>Fired before a call to sync is executed. ...</div><div class='long'><p>Fired before a call to <a href=\"#!/api/Ext.data.AbstractStore-method-sync\" rel=\"Ext.data.AbstractStore-method-sync\" class=\"docClass\">sync</a> is executed. Return false from any listener to cancel the sync</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>Hash of all records to be synchronized, broken down into create, update and destroy</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-bulkremove' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-bulkremove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-bulkremove' class='name expandable'>bulkremove</a>( <span class='pre'>store, records, indexes, isMove, eOpts</span> )</div><div class='description'><div class='short'>Fired at the end of the remove method when all records in the passed array have been removed. ...</div><div class='long'><p>Fired at the <em>end</em> of the <a href=\"#!/api/Ext.data.Store-method-remove\" rel=\"Ext.data.Store-method-remove\" class=\"docClass\">remove</a> method when all records in the passed array have been removed.</p>\n\n<p>If many records may be removed in one go, then it is more efficient to listen for this event\nand perform any processing for a bulk remove than to listen for many <a href=\"#!/api/Ext.data.AbstractStore-event-remove\" rel=\"Ext.data.AbstractStore-event-remove\" class=\"docClass\">remove</a> events.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>store</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>The Store object</p>\n</div></li><li><span class='pre'>records</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]<div class='sub-desc'><p>The array of records that were removed (In the order they appear in the Store)</p>\n</div></li><li><span class='pre'>indexes</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]<div class='sub-desc'><p>The indexes of the records that were removed</p>\n</div></li><li><span class='pre'>isMove</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p><code>true</code> if the child nodes are being removed so they can be moved to another position in this Store.</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-clear' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-clear' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-clear' class='name expandable'>clear</a>( <span class='pre'>this, eOpts</span> )</div><div class='description'><div class='short'>Fired after the removeAll method is called. ...</div><div class='long'><p>Fired after the <a href=\"#!/api/Ext.data.AbstractStore-method-removeAll\" rel=\"Ext.data.AbstractStore-method-removeAll\" class=\"docClass\">removeAll</a> method is called.</p>\n        <p>Available since: <b>1.1.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-datachanged' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-datachanged' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-datachanged' class='name expandable'>datachanged</a>( <span class='pre'>this, eOpts</span> )</div><div class='description'><div class='short'>Fires whenever the records in the Store have changed in some way - this could include adding or removing\nrecords, or ...</div><div class='long'><p>Fires whenever the records in the Store have changed in some way - this could include adding or removing\nrecords, or updating the data in existing records</p>\n        <p>Available since: <b>1.1.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>The data store</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-filterchange' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-event-filterchange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-event-filterchange' class='name expandable'>filterchange</a>( <span class='pre'>store, filters, eOpts</span> )</div><div class='description'><div class='short'>Fired whenever the filter set changes. ...</div><div class='long'><p>Fired whenever the filter set changes.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>store</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>The store.</p>\n</div></li><li><span class='pre'>filters</span> : <a href=\"#!/api/Ext.util.Filter\" rel=\"Ext.util.Filter\" class=\"docClass\">Ext.util.Filter</a>[]<div class='sub-desc'><p>The array of Filter objects.</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-groupchange' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-event-groupchange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-event-groupchange' class='name expandable'>groupchange</a>( <span class='pre'>store, groupers, eOpts</span> )</div><div class='description'><div class='short'>Fired whenever the grouping in the grid changes. ...</div><div class='long'><p>Fired whenever the grouping in the grid changes.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>store</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>The store.</p>\n</div></li><li><span class='pre'>groupers</span> : <a href=\"#!/api/Ext.util.Grouper\" rel=\"Ext.util.Grouper\" class=\"docClass\">Ext.util.Grouper</a>[]<div class='sub-desc'><p>The array of Grouper objects.</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-load' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-load' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-load' class='name expandable'>load</a>( <span class='pre'>this, records, successful, eOpts</span> )</div><div class='description'><div class='short'>Fires whenever the store reads data from a remote data source. ...</div><div class='long'><p>Fires whenever the store reads data from a remote data source.</p>\n        <p>Available since: <b>1.1.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'>\n</div></li><li><span class='pre'>records</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]<div class='sub-desc'><p>An array of records</p>\n</div></li><li><span class='pre'>successful</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True if the operation was successful.</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-metachange' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-metachange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-metachange' class='name expandable'>metachange</a>( <span class='pre'>this, meta, eOpts</span> )</div><div class='description'><div class='short'>Fires when this store's underlying reader (available via the proxy) provides new metadata. ...</div><div class='long'><p>Fires when this store's underlying reader (available via the proxy) provides new metadata.\nMetadata usually consists of new field definitions, but can include any configuration data\nrequired by an application, and can be processed as needed in the event handler.\nThis event is currently only fired for JsonReaders.</p>\n        <p>Available since: <b>1.1.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'>\n</div></li><li><span class='pre'>meta</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The JSON metadata</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-prefetch' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='defined-in docClass'>Ext.data.Store</a><br/><a href='source/Store.html#Ext-data-Store-event-prefetch' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Store-event-prefetch' class='name expandable'>prefetch</a>( <span class='pre'>this, records, successful, operation, eOpts</span> )</div><div class='description'><div class='short'>Fires whenever records have been prefetched. ...</div><div class='long'><p>Fires whenever records have been prefetched.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'>\n</div></li><li><span class='pre'>records</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>[]<div class='sub-desc'><p>An array of records.</p>\n</div></li><li><span class='pre'>successful</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p><code>true</code> if the operation was successful.</p>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The associated operation.</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-refresh' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-refresh' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-refresh' class='name expandable'>refresh</a>( <span class='pre'>this, eOpts</span> )</div><div class='description'><div class='short'>Fires when the data cache has changed in a bulk manner (e.g., it has been sorted, filtered, etc.) and a\nwidget that i...</div><div class='long'><p>Fires when the data cache has changed in a bulk manner (e.g., it has been sorted, filtered, etc.) and a\nwidget that is using this Store as a Record cache should refresh its view.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>The data store</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-remove' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-remove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-remove' class='name expandable'>remove</a>( <span class='pre'>store, record, index, isMove, eOpts</span> )</div><div class='description'><div class='short'>Fired when a Model instance has been removed from this Store. ...</div><div class='long'><p>Fired when a Model instance has been removed from this Store.</p>\n\n<p><strong>If many records may be removed in one go, then it is more efficient to listen for the <a href=\"#!/api/Ext.data.AbstractStore-event-bulkremove\" rel=\"Ext.data.AbstractStore-event-bulkremove\" class=\"docClass\">bulkremove</a> event\nand perform any processing for a bulk remove than to listen for this <a href=\"#!/api/Ext.data.AbstractStore-event-remove\" rel=\"Ext.data.AbstractStore-event-remove\" class=\"docClass\">remove</a> event.</strong></p>\n        <p>Available since: <b>1.1.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>store</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>The Store object</p>\n</div></li><li><span class='pre'>record</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The record that was removed</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The index of the record that was removed</p>\n</div></li><li><span class='pre'>isMove</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p><code>true</code> if the child node is being removed so it can be moved to another position in this Store.</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-update' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-update' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-update' class='name expandable'>update</a>( <span class='pre'>this, record, operation, eOpts</span> )</div><div class='description'><div class='short'>Fires when a Model instance has been updated. ...</div><div class='long'><p>Fires when a Model instance has been updated.</p>\n        <p>Available since: <b>1.1.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'>\n</div></li><li><span class='pre'>record</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The Model instance that was updated</p>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The update operation being performed. Value may be one of:</p>\n\n<pre><code><a href=\"#!/api/Ext.data.Model-static-property-EDIT\" rel=\"Ext.data.Model-static-property-EDIT\" class=\"docClass\">Ext.data.Model.EDIT</a>\n<a href=\"#!/api/Ext.data.Model-static-property-REJECT\" rel=\"Ext.data.Model-static-property-REJECT\" class=\"docClass\">Ext.data.Model.REJECT</a>\n<a href=\"#!/api/Ext.data.Model-static-property-COMMIT\" rel=\"Ext.data.Model-static-property-COMMIT\" class=\"docClass\">Ext.data.Model.COMMIT</a>\n</code></pre>\n\n<p>@param {String[]} modifiedFieldNames Array of field names changed during edit.</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-write' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='defined-in docClass'>Ext.data.AbstractStore</a><br/><a href='source/AbstractStore.html#Ext-data-AbstractStore-event-write' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.AbstractStore-event-write' class='name expandable'>write</a>( <span class='pre'>store, operation, eOpts</span> )</div><div class='description'><div class='short'>Fires whenever a successful write has been made via the configured Proxy ...</div><div class='long'><p>Fires whenever a successful write has been made via the configured <a href=\"#!/api/Ext.data.AbstractStore-cfg-proxy\" rel=\"Ext.data.AbstractStore-cfg-proxy\" class=\"docClass\">Proxy</a></p>\n        <p>Available since: <b>3.4.0</b></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>store</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>This Store</p>\n</div></li><li><span class='pre'>operation</span> : <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Operation</a> object that was used in\nthe write</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div></div></div></div></div>","superclasses":["Ext.Base","Ext.data.AbstractStore","Ext.data.Store"],"meta":{},"code_type":"ext_define","requires":["Ext.data.proxy.Direct"],"html_meta":{},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"DirectStore.html#Ext-data-DirectStore","filename":"DirectStore.js"}],"linenr":1,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.data.Store","meta":{"private":true},"name":"addRecordsOptions","id":"property-addRecordsOptions"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.data.Store","meta":{},"name":"currentPage","id":"property-currentPage"},{"tagname":"property","owner":"Ext.data.Store","meta":{},"name":"data","id":"property-data"},{"tagname":"property","owner":"Ext.data.Store","meta":{"private":true},"name":"defaultPageSize","id":"property-defaultPageSize"},{"tagname":"property","owner":"Ext.data.AbstractStore","meta":{},"name":"defaultProxyType","id":"property-defaultProxyType"},{"tagname":"property","owner":"Ext.data.Store","meta":{"private":true},"name":"defaultViewSize","id":"property-defaultViewSize"},{"tagname":"property","owner":"Ext.util.Observable","meta":{"private":true},"name":"eventsSuspended","id":"property-eventsSuspended"},{"tagname":"property","owner":"Ext.data.AbstractStore","meta":{},"name":"filters","id":"property-filters"},{"tagname":"property","owner":"Ext.util.Observable","meta":{"readonly":true},"name":"hasListeners","id":"property-hasListeners"},{"tagname":"property","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"implicitModel","id":"property-implicitModel"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.data.AbstractStore","meta":{"since":"3.4.0"},"name":"isDestroyed","id":"property-isDestroyed"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.util.Observable","meta":{},"name":"isObservable","id":"property-isObservable"},{"tagname":"property","owner":"Ext.util.Sortable","meta":{},"name":"isSortable","id":"property-isSortable"},{"tagname":"property","owner":"Ext.data.AbstractStore","meta":{},"name":"isStore","id":"property-isStore"},{"tagname":"property","owner":"Ext.data.Store","meta":{"private":true},"name":"loading","id":"property-loading"},{"tagname":"property","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"modelDefaults","id":"property-modelDefaults"},{"tagname":"property","owner":"Ext.data.AbstractStore","meta":{"protected":true},"name":"removed","id":"property-removed"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"},{"tagname":"property","owner":"Ext.data.Store","meta":{},"name":"snapshot","id":"property-snapshot"},{"tagname":"property","owner":"Ext.util.Sortable","meta":{},"name":"sorters","id":"property-sorters"}],"cfg":[{"tagname":"cfg","owner":"Ext.data.Store","meta":{},"name":"autoDestroy","id":"cfg-autoDestroy"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{"since":"2.3.0"},"name":"autoLoad","id":"cfg-autoLoad"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"autoSync","id":"cfg-autoSync"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"batchUpdateMode","id":"cfg-batchUpdateMode"},{"tagname":"cfg","owner":"Ext.data.Store","meta":{},"name":"buffered","id":"cfg-buffered"},{"tagname":"cfg","owner":"Ext.data.Store","meta":{},"name":"clearOnPageLoad","id":"cfg-clearOnPageLoad"},{"tagname":"cfg","owner":"Ext.data.Store","meta":{},"name":"clearRemovedOnLoad","id":"cfg-clearRemovedOnLoad"},{"tagname":"cfg","owner":"Ext.data.Store","meta":{},"name":"data","id":"cfg-data"},{"tagname":"cfg","owner":"Ext.util.Sortable","meta":{},"name":"defaultSortDirection","id":"cfg-defaultSortDirection"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{"since":"2.3.0"},"name":"fields","id":"cfg-fields"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"filterOnLoad","id":"cfg-filterOnLoad"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"filters","id":"cfg-filters"},{"tagname":"cfg","owner":"Ext.data.Store","meta":{},"name":"groupDir","id":"cfg-groupDir"},{"tagname":"cfg","owner":"Ext.data.Store","meta":{},"name":"groupField","id":"cfg-groupField"},{"tagname":"cfg","owner":"Ext.data.Store","meta":{},"name":"groupers","id":"cfg-groupers"},{"tagname":"cfg","owner":"Ext.data.Store","meta":{},"name":"leadingBufferZone","id":"cfg-leadingBufferZone"},{"tagname":"cfg","owner":"Ext.util.Observable","meta":{},"name":"listeners","id":"cfg-listeners"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"model","id":"cfg-model"},{"tagname":"cfg","owner":"Ext.data.Store","meta":{},"name":"pageSize","id":"cfg-pageSize"},{"tagname":"cfg","owner":"Ext.data.Store","meta":{},"name":"proxy","id":"cfg-proxy"},{"tagname":"cfg","owner":"Ext.data.Store","meta":{},"name":"purgePageCount","id":"cfg-purgePageCount"},{"tagname":"cfg","owner":"Ext.data.Store","meta":{},"name":"remoteFilter","id":"cfg-remoteFilter"},{"tagname":"cfg","owner":"Ext.data.Store","meta":{},"name":"remoteGroup","id":"cfg-remoteGroup"},{"tagname":"cfg","owner":"Ext.data.Store","meta":{},"name":"remoteSort","id":"cfg-remoteSort"},{"tagname":"cfg","owner":"Ext.data.Store","meta":{},"name":"sortOnFilter","id":"cfg-sortOnFilter"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"sortOnLoad","id":"cfg-sortOnLoad"},{"tagname":"cfg","owner":"Ext.util.Sortable","meta":{},"name":"sortRoot","id":"cfg-sortRoot"},{"tagname":"cfg","owner":"Ext.util.Sortable","meta":{},"name":"sorters","id":"cfg-sorters"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"statefulFilters","id":"cfg-statefulFilters"},{"tagname":"cfg","owner":"Ext.data.AbstractStore","meta":{},"name":"storeId","id":"cfg-storeId"},{"tagname":"cfg","owner":"Ext.data.Store","meta":{},"name":"trailingBufferZone","id":"cfg-trailingBufferZone"}],"css_var":[],"method":[{"tagname":"method","owner":"Ext.data.DirectStore","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"add","id":"method-add"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"addEvents","id":"method-addEvents"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"addFilter","id":"method-addFilter"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"addListener","id":"method-addListener"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"addManagedListener","id":"method-addManagedListener"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"addSorted","id":"method-addSorted"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"since":"3.4.0","private":true},"name":"afterCommit","id":"method-afterCommit"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"since":"3.4.0","private":true},"name":"afterEdit","id":"method-afterEdit"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"since":"3.4.0","private":true},"name":"afterReject","id":"method-afterReject"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"aggregate","id":"method-aggregate"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"applyState","id":"method-applyState"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"average","id":"method-average"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"cachePage","id":"method-cachePage"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"captureArgs","id":"method-captureArgs"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"clearData","id":"method-clearData"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"clearFilter","id":"method-clearFilter"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"clearGrouping","id":"method-clearGrouping"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"clearListeners","id":"method-clearListeners"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"clearManagedListeners","id":"method-clearManagedListeners"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"collect","id":"method-collect"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"commitChanges","id":"method-commitChanges"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"constructGroups","id":"method-constructGroups"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"continueFireEvent","id":"method-continueFireEvent"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"count","id":"method-count"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"create","id":"method-create"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"createFilterFn","id":"method-createFilterFn"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"createModel","id":"method-createModel"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"createRelayer","id":"method-createRelayer"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"decodeFilters","id":"method-decodeFilters"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"decodeGroupers","id":"method-decodeGroupers"},{"tagname":"method","owner":"Ext.util.Sortable","meta":{"private":true},"name":"decodeSorters","id":"method-decodeSorters"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"since":"3.4.0","private":true},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"destroyStore","id":"method-destroyStore"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"doSort","id":"method-doSort"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"each","id":"method-each"},{"tagname":"method","owner":"Ext.util.Sortable","meta":{"private":true},"name":"emptyComparator","id":"method-emptyComparator"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"enableBubble","id":"method-enableBubble"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"filter","id":"method-filter"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"filterBy","id":"method-filterBy"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"filterNew","id":"method-filterNew"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"filterNewOnly","id":"method-filterNewOnly"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"filterUpdated","id":"method-filterUpdated"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"find","id":"method-find"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"findBy","id":"method-findBy"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"findExact","id":"method-findExact"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"findRecord","id":"method-findRecord"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"fireEvent","id":"method-fireEvent"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"fireEventArgs","id":"method-fireEventArgs"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"fireGroupChange","id":"method-fireGroupChange"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"first","id":"method-first"},{"tagname":"method","owner":"Ext.util.Sortable","meta":{},"name":"generateComparator","id":"method-generateComparator"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"getAggregate","id":"method-getAggregate"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"getAt","id":"method-getAt"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"getAverage","id":"method-getAverage"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"getBatchListeners","id":"method-getBatchListeners"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"getBubbleParent","id":"method-getBubbleParent"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"getById","id":"method-getById"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"getCount","id":"method-getCount"},{"tagname":"method","owner":"Ext.util.Sortable","meta":{"protected":true},"name":"getFirstSorter","id":"method-getFirstSorter"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"getGroupData","id":"method-getGroupData"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"getGroupField","id":"method-getGroupField"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"getGroupString","id":"method-getGroupString"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"getGroups","id":"method-getGroups"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"getGroupsForGrouper","id":"method-getGroupsForGrouper"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"getGroupsForGrouperIndex","id":"method-getGroupsForGrouperIndex"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"getMax","id":"method-getMax"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"getMin","id":"method-getMin"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"getModifiedRecords","id":"method-getModifiedRecords"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"getNewRecords","id":"method-getNewRecords"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"getPageFromRecordIndex","id":"method-getPageFromRecordIndex"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"getProxy","id":"method-getProxy"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"getRange","id":"method-getRange"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"getRejectRecords","id":"method-getRejectRecords"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"getRemovedRecords","id":"method-getRemovedRecords"},{"tagname":"method","owner":"Ext.util.Sortable","meta":{"private":true},"name":"getSorters","id":"method-getSorters"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"getState","id":"method-getState"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"getSum","id":"method-getSum"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"getTotalCount","id":"method-getTotalCount"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"getUpdatedRecords","id":"method-getUpdatedRecords"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"group","id":"method-group"},{"tagname":"method","owner":"Ext.data.Store","meta":{"deprecated":{"text":"Use {@link #getRange}"}},"name":"guaranteeRange","id":"method-guaranteeRange"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"hasListener","id":"method-hasListener"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"indexOf","id":"method-indexOf"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"indexOfId","id":"method-indexOfId"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"indexOfTotal","id":"method-indexOfTotal"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.util.Sortable","meta":{},"name":"initSortable","id":"method-initSortable"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"insert","id":"method-insert"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"isFiltered","id":"method-isFiltered"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"isGrouped","id":"method-isGrouped"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"isLoading","id":"method-isLoading"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"last","id":"method-last"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"load","id":"method-load"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"loadData","id":"method-loadData"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"loadPage","id":"method-loadPage"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"loadRawData","id":"method-loadRawData"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"loadRecords","id":"method-loadRecords"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"loadToPrefetch","id":"method-loadToPrefetch"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"max","id":"method-max"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"min","id":"method-min"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"mon","id":"method-mon"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"mun","id":"method-mun"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"nextPage","id":"method-nextPage"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"on","id":"method-on"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onBatchComplete","id":"method-onBatchComplete"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onBatchException","id":"method-onBatchException"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onBatchOperationComplete","id":"method-onBatchOperationComplete"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"onBeforeSort","id":"method-onBeforeSort"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onClassExtended","id":"method-onClassExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onCreateRecords","id":"method-onCreateRecords"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onDestroyRecords","id":"method-onDestroyRecords"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"onGuaranteedRange","id":"method-onGuaranteedRange"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"onIdChanged","id":"method-onIdChanged"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onMetaChange","id":"method-onMetaChange"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"onPageMapClear","id":"method-onPageMapClear"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"onProxyLoad","id":"method-onProxyLoad"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"onProxyPrefetch","id":"method-onProxyPrefetch"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onProxyWrite","id":"method-onProxyWrite"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"onUpdate","id":"method-onUpdate"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"onUpdateRecords","id":"method-onUpdateRecords"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"pageCached","id":"method-pageCached"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"pagePending","id":"method-pagePending"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"prefetch","id":"method-prefetch"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"prefetchPage","id":"method-prefetchPage"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"prefetchRange","id":"method-prefetchRange"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"prepareClass","id":"method-prepareClass"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"previousPage","id":"method-previousPage"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"primeCache","id":"method-primeCache"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"query","id":"method-query"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"queryBy","id":"method-queryBy"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"rangeCached","id":"method-rangeCached"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true,"deprecated":{"text":"use {@link #rangeCached} instead","version":"4.1.0"}},"name":"rangeSatisfied","id":"method-rangeSatisfied"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"read","id":"method-read"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"rejectChanges","id":"method-rejectChanges"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"relayEvents","id":"method-relayEvents"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"reload","id":"method-reload"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"remove","id":"method-remove"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"removeAll","id":"method-removeAll"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"removeAt","id":"method-removeAt"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"removeFilter","id":"method-removeFilter"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"removeListener","id":"method-removeListener"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"removeManagedListener","id":"method-removeManagedListener"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"removeManagedListenerItem","id":"method-removeManagedListenerItem"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"resumeAutoSync","id":"method-resumeAutoSync"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"resumeEvent","id":"method-resumeEvent"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"resumeEvents","id":"method-resumeEvents"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"deprecated":{"text":"Will be removed in the next major version","version":"4.0.0"}},"name":"save","id":"method-save"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"setProxy","id":"method-setProxy"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"sort","id":"method-sort"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"},{"tagname":"method","owner":"Ext.data.Store","meta":{},"name":"sum","id":"method-sum"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"suspendAutoSync","id":"method-suspendAutoSync"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"suspendEvent","id":"method-suspendEvent"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"suspendEvents","id":"method-suspendEvents"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{},"name":"sync","id":"method-sync"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"un","id":"method-un"},{"tagname":"method","owner":"Ext.data.AbstractStore","meta":{"private":true},"name":"update","id":"method-update"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"updateGroupsOnAdd","id":"method-updateGroupsOnAdd"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"updateGroupsOnRemove","id":"method-updateGroupsOnRemove"},{"tagname":"method","owner":"Ext.data.Store","meta":{"private":true},"name":"updateGroupsOnUpdate","id":"method-updateGroupsOnUpdate"}],"event":[{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"add","id":"event-add"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"beforeload","id":"event-beforeload"},{"tagname":"event","owner":"Ext.data.Store","meta":{},"name":"beforeprefetch","id":"event-beforeprefetch"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{},"name":"beforesync","id":"event-beforesync"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{},"name":"bulkremove","id":"event-bulkremove"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"clear","id":"event-clear"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"datachanged","id":"event-datachanged"},{"tagname":"event","owner":"Ext.data.Store","meta":{},"name":"filterchange","id":"event-filterchange"},{"tagname":"event","owner":"Ext.data.Store","meta":{},"name":"groupchange","id":"event-groupchange"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"load","id":"event-load"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"metachange","id":"event-metachange"},{"tagname":"event","owner":"Ext.data.Store","meta":{},"name":"prefetch","id":"event-prefetch"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{},"name":"refresh","id":"event-refresh"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"remove","id":"event-remove"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"1.1.0"},"name":"update","id":"event-update"},{"tagname":"event","owner":"Ext.data.AbstractStore","meta":{"since":"3.4.0"},"name":"write","id":"event-write"}],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.data.DirectStore","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.data.DirectStore","mixins":[],"mixedInto":[]});