Ext.data.JsonP.Ext_enums_Widget({"alternateClassNames":[],"aliases":{},"enum":{"type":"String","default":"widget.*","doc_only":true},"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/enums.html#Ext-enums-Widget' target='_blank'>enums.js</a></div></pre><div class='doc-contents'><p>Enumeration of all xtypes.</p>\n<p class='enum'><strong>ENUM:</strong> This enumeration defines a set of String values. It exists primarily for documentation purposes - in code use the actual string values like 'actioncolumn', don't reference them through this class like Ext.enums.Widget.actioncolumn.</p></div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-actioncolumn' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-actioncolumn' class='name expandable'>actioncolumn</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.column.Action. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.column.Action\" rel=\"Ext.grid.column.Action\" class=\"docClass\">Ext.grid.column.Action</a>.</p>\n<p>Defaults to: <code>'actioncolumn'</code></p></div></div></div><div id='property-booleancolumn' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-booleancolumn' class='name expandable'>booleancolumn</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.column.Boolean. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.column.Boolean\" rel=\"Ext.grid.column.Boolean\" class=\"docClass\">Ext.grid.column.Boolean</a>.</p>\n<p>Defaults to: <code>'booleancolumn'</code></p></div></div></div><div id='property-bordersplitter' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-bordersplitter' class='name expandable'>bordersplitter</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.resizer.BorderSplitter. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.resizer.BorderSplitter\" rel=\"Ext.resizer.BorderSplitter\" class=\"docClass\">Ext.resizer.BorderSplitter</a>.</p>\n<p>Defaults to: <code>'bordersplitter'</code></p></div></div></div><div id='property-boundlist' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-boundlist' class='name expandable'>boundlist</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.view.BoundList. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.view.BoundList\" rel=\"Ext.view.BoundList\" class=\"docClass\">Ext.view.BoundList</a>.</p>\n<p>Defaults to: <code>'boundlist'</code></p></div></div></div><div id='property-box' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-box' class='name expandable'>box</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.Component. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>.</p>\n<p>Defaults to: <code>'box'</code></p></div></div></div><div id='property-button' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-button' class='name expandable'>button</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.button.Button. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.button.Button\" rel=\"Ext.button.Button\" class=\"docClass\">Ext.button.Button</a>.</p>\n<p>Defaults to: <code>'button'</code></p></div></div></div><div id='property-buttongroup' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-buttongroup' class='name expandable'>buttongroup</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.container.ButtonGroup. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.container.ButtonGroup\" rel=\"Ext.container.ButtonGroup\" class=\"docClass\">Ext.container.ButtonGroup</a>.</p>\n<p>Defaults to: <code>'buttongroup'</code></p></div></div></div><div id='property-chart' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-chart' class='name expandable'>chart</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.chart.Chart. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.chart.Chart\" rel=\"Ext.chart.Chart\" class=\"docClass\">Ext.chart.Chart</a>.</p>\n<p>Defaults to: <code>'chart'</code></p></div></div></div><div id='property-checkbox' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-checkbox' class='name expandable'>checkbox</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.Checkbox. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.Checkbox\" rel=\"Ext.form.field.Checkbox\" class=\"docClass\">Ext.form.field.Checkbox</a>.</p>\n<p>Defaults to: <code>'checkbox'</code></p></div></div></div><div id='property-checkboxfield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-checkboxfield' class='name expandable'>checkboxfield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.Checkbox. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.Checkbox\" rel=\"Ext.form.field.Checkbox\" class=\"docClass\">Ext.form.field.Checkbox</a>.</p>\n<p>Defaults to: <code>'checkboxfield'</code></p></div></div></div><div id='property-checkboxgroup' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-checkboxgroup' class='name expandable'>checkboxgroup</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.CheckboxGroup. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.CheckboxGroup\" rel=\"Ext.form.CheckboxGroup\" class=\"docClass\">Ext.form.CheckboxGroup</a>.</p>\n<p>Defaults to: <code>'checkboxgroup'</code></p></div></div></div><div id='property-checkcolumn' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-checkcolumn' class='name expandable'>checkcolumn</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.column.CheckColumn. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.column.CheckColumn\" rel=\"Ext.grid.column.CheckColumn\" class=\"docClass\">Ext.grid.column.CheckColumn</a>.</p>\n<p>Defaults to: <code>'checkcolumn'</code></p></div></div></div><div id='property-colormenu' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-colormenu' class='name expandable'>colormenu</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.menu.ColorPicker. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.menu.ColorPicker\" rel=\"Ext.menu.ColorPicker\" class=\"docClass\">Ext.menu.ColorPicker</a>.</p>\n<p>Defaults to: <code>'colormenu'</code></p></div></div></div><div id='property-colorpicker' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-colorpicker' class='name expandable'>colorpicker</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.picker.Color. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.picker.Color\" rel=\"Ext.picker.Color\" class=\"docClass\">Ext.picker.Color</a>.</p>\n<p>Defaults to: <code>'colorpicker'</code></p></div></div></div><div id='property-combo' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-combo' class='name expandable'>combo</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.ComboBox. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.ComboBox\" rel=\"Ext.form.field.ComboBox\" class=\"docClass\">Ext.form.field.ComboBox</a>.</p>\n<p>Defaults to: <code>'combo'</code></p></div></div></div><div id='property-combobox' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-combobox' class='name expandable'>combobox</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.ComboBox. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.ComboBox\" rel=\"Ext.form.field.ComboBox\" class=\"docClass\">Ext.form.field.ComboBox</a>.</p>\n<p>Defaults to: <code>'combobox'</code></p></div></div></div><div id='property-component' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-component' class='name expandable'>component</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.Component. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>.</p>\n<p>Defaults to: <code>'component'</code></p></div></div></div><div id='property-container' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-container' class='name expandable'>container</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.container.Container. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.container.Container\" rel=\"Ext.container.Container\" class=\"docClass\">Ext.container.Container</a>.</p>\n<p>Defaults to: <code>'container'</code></p></div></div></div><div id='property-cycle' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-cycle' class='name expandable'>cycle</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.button.Cycle. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.button.Cycle\" rel=\"Ext.button.Cycle\" class=\"docClass\">Ext.button.Cycle</a>.</p>\n<p>Defaults to: <code>'cycle'</code></p></div></div></div><div id='property-dataview' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-dataview' class='name expandable'>dataview</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.view.View. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.view.View\" rel=\"Ext.view.View\" class=\"docClass\">Ext.view.View</a>.</p>\n<p>Defaults to: <code>'dataview'</code></p></div></div></div><div id='property-datecolumn' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-datecolumn' class='name expandable'>datecolumn</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.column.Date. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.column.Date\" rel=\"Ext.grid.column.Date\" class=\"docClass\">Ext.grid.column.Date</a>.</p>\n<p>Defaults to: <code>'datecolumn'</code></p></div></div></div><div id='property-datefield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-datefield' class='name expandable'>datefield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.Date. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.Date\" rel=\"Ext.form.field.Date\" class=\"docClass\">Ext.form.field.Date</a>.</p>\n<p>Defaults to: <code>'datefield'</code></p></div></div></div><div id='property-datemenu' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-datemenu' class='name expandable'>datemenu</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.menu.DatePicker. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.menu.DatePicker\" rel=\"Ext.menu.DatePicker\" class=\"docClass\">Ext.menu.DatePicker</a>.</p>\n<p>Defaults to: <code>'datemenu'</code></p></div></div></div><div id='property-datepicker' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-datepicker' class='name expandable'>datepicker</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.picker.Date. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.picker.Date\" rel=\"Ext.picker.Date\" class=\"docClass\">Ext.picker.Date</a>.</p>\n<p>Defaults to: <code>'datepicker'</code></p></div></div></div><div id='property-displayfield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-displayfield' class='name expandable'>displayfield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.Display. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.Display\" rel=\"Ext.form.field.Display\" class=\"docClass\">Ext.form.field.Display</a>.</p>\n<p>Defaults to: <code>'displayfield'</code></p></div></div></div><div id='property-draw' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-draw' class='name expandable'>draw</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.draw.Component. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.draw.Component\" rel=\"Ext.draw.Component\" class=\"docClass\">Ext.draw.Component</a>.</p>\n<p>Defaults to: <code>'draw'</code></p></div></div></div><div id='property-editor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-editor' class='name expandable'>editor</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.Editor. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.Editor\" rel=\"Ext.Editor\" class=\"docClass\">Ext.Editor</a>.</p>\n<p>Defaults to: <code>'editor'</code></p></div></div></div><div id='property-eventrecordermanager' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-eventrecordermanager' class='name expandable'>eventrecordermanager</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ux.event.RecorderManager. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ux.event.RecorderManager\" rel=\"Ext.ux.event.RecorderManager\" class=\"docClass\">Ext.ux.event.RecorderManager</a>.</p>\n<p>Defaults to: <code>'eventrecordermanager'</code></p></div></div></div><div id='property-field' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-field' class='name expandable'>field</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.Base. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.Base\" rel=\"Ext.form.field.Base\" class=\"docClass\">Ext.form.field.Base</a>.</p>\n<p>Defaults to: <code>'field'</code></p></div></div></div><div id='property-fieldcontainer' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-fieldcontainer' class='name expandable'>fieldcontainer</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.FieldContainer. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.FieldContainer\" rel=\"Ext.form.FieldContainer\" class=\"docClass\">Ext.form.FieldContainer</a>.</p>\n<p>Defaults to: <code>'fieldcontainer'</code></p></div></div></div><div id='property-fieldset' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-fieldset' class='name expandable'>fieldset</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.FieldSet. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.FieldSet\" rel=\"Ext.form.FieldSet\" class=\"docClass\">Ext.form.FieldSet</a>.</p>\n<p>Defaults to: <code>'fieldset'</code></p></div></div></div><div id='property-filebutton' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-filebutton' class='name expandable'>filebutton</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.FileButton. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.FileButton\" rel=\"Ext.form.field.FileButton\" class=\"docClass\">Ext.form.field.FileButton</a>.</p>\n<p>Defaults to: <code>'filebutton'</code></p></div></div></div><div id='property-filefield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-filefield' class='name expandable'>filefield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.File. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.File\" rel=\"Ext.form.field.File\" class=\"docClass\">Ext.form.field.File</a>.</p>\n<p>Defaults to: <code>'filefield'</code></p></div></div></div><div id='property-fileuploadfield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-fileuploadfield' class='name expandable'>fileuploadfield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.File. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.File\" rel=\"Ext.form.field.File\" class=\"docClass\">Ext.form.field.File</a>.</p>\n<p>Defaults to: <code>'fileuploadfield'</code></p></div></div></div><div id='property-flash' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-flash' class='name expandable'>flash</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.flash.Component. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.flash.Component\" rel=\"Ext.flash.Component\" class=\"docClass\">Ext.flash.Component</a>.</p>\n<p>Defaults to: <code>'flash'</code></p></div></div></div><div id='property-form' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-form' class='name expandable'>form</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.Panel. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.Panel\" rel=\"Ext.form.Panel\" class=\"docClass\">Ext.form.Panel</a>.</p>\n<p>Defaults to: <code>'form'</code></p></div></div></div><div id='property-gmappanel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-gmappanel' class='name expandable'>gmappanel</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ux.GMapPanel. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ux.GMapPanel\" rel=\"Ext.ux.GMapPanel\" class=\"docClass\">Ext.ux.GMapPanel</a>.</p>\n<p>Defaults to: <code>'gmappanel'</code></p></div></div></div><div id='property-grid' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-grid' class='name expandable'>grid</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.Panel. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.Panel\" rel=\"Ext.grid.Panel\" class=\"docClass\">Ext.grid.Panel</a>.</p>\n<p>Defaults to: <code>'grid'</code></p></div></div></div><div id='property-gridcolumn' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-gridcolumn' class='name expandable'>gridcolumn</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.column.Column. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.column.Column\" rel=\"Ext.grid.column.Column\" class=\"docClass\">Ext.grid.column.Column</a>.</p>\n<p>Defaults to: <code>'gridcolumn'</code></p></div></div></div><div id='property-gridpanel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-gridpanel' class='name expandable'>gridpanel</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.Panel. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.Panel\" rel=\"Ext.grid.Panel\" class=\"docClass\">Ext.grid.Panel</a>.</p>\n<p>Defaults to: <code>'gridpanel'</code></p></div></div></div><div id='property-gridview' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-gridview' class='name expandable'>gridview</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.View. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.View\" rel=\"Ext.grid.View\" class=\"docClass\">Ext.grid.View</a>.</p>\n<p>Defaults to: <code>'gridview'</code></p></div></div></div><div id='property-grouptabpanel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-grouptabpanel' class='name expandable'>grouptabpanel</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ux.GroupTabPanel. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ux.GroupTabPanel\" rel=\"Ext.ux.GroupTabPanel\" class=\"docClass\">Ext.ux.GroupTabPanel</a>.</p>\n<p>Defaults to: <code>'grouptabpanel'</code></p></div></div></div><div id='property-header' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-header' class='name expandable'>header</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.panel.Header. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.panel.Header\" rel=\"Ext.panel.Header\" class=\"docClass\">Ext.panel.Header</a>.</p>\n<p>Defaults to: <code>'header'</code></p></div></div></div><div id='property-headercontainer' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-headercontainer' class='name expandable'>headercontainer</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.header.Container. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.header.Container\" rel=\"Ext.grid.header.Container\" class=\"docClass\">Ext.grid.header.Container</a>.</p>\n<p>Defaults to: <code>'headercontainer'</code></p></div></div></div><div id='property-hidden' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-hidden' class='name expandable'>hidden</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.Hidden. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.Hidden\" rel=\"Ext.form.field.Hidden\" class=\"docClass\">Ext.form.field.Hidden</a>.</p>\n<p>Defaults to: <code>'hidden'</code></p></div></div></div><div id='property-hiddenfield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-hiddenfield' class='name expandable'>hiddenfield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.Hidden. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.Hidden\" rel=\"Ext.form.field.Hidden\" class=\"docClass\">Ext.form.field.Hidden</a>.</p>\n<p>Defaults to: <code>'hiddenfield'</code></p></div></div></div><div id='property-htmleditor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-htmleditor' class='name expandable'>htmleditor</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.HtmlEditor. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.HtmlEditor\" rel=\"Ext.form.field.HtmlEditor\" class=\"docClass\">Ext.form.field.HtmlEditor</a>.</p>\n<p>Defaults to: <code>'htmleditor'</code></p></div></div></div><div id='property-image' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-image' class='name expandable'>image</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.Img. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.Img\" rel=\"Ext.Img\" class=\"docClass\">Ext.Img</a>.</p>\n<p>Defaults to: <code>'image'</code></p></div></div></div><div id='property-imagecomponent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-imagecomponent' class='name expandable'>imagecomponent</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.Img. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.Img\" rel=\"Ext.Img\" class=\"docClass\">Ext.Img</a>.</p>\n<p>Defaults to: <code>'imagecomponent'</code></p></div></div></div><div id='property-itemselector' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-itemselector' class='name expandable'>itemselector</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ux.form.ItemSelector. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ux.form.ItemSelector\" rel=\"Ext.ux.form.ItemSelector\" class=\"docClass\">Ext.ux.form.ItemSelector</a>.</p>\n<p>Defaults to: <code>'itemselector'</code></p></div></div></div><div id='property-itemselectorfield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-itemselectorfield' class='name expandable'>itemselectorfield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ux.form.ItemSelector. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ux.form.ItemSelector\" rel=\"Ext.ux.form.ItemSelector\" class=\"docClass\">Ext.ux.form.ItemSelector</a>.</p>\n<p>Defaults to: <code>'itemselectorfield'</code></p></div></div></div><div id='property-jsonpstore' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-jsonpstore' class='name expandable'>jsonpstore</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.data.JsonPStore. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.data.JsonPStore\" rel=\"Ext.data.JsonPStore\" class=\"docClass\">Ext.data.JsonPStore</a>.</p>\n<p>Defaults to: <code>'jsonpstore'</code></p></div></div></div><div id='property-label' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-label' class='name expandable'>label</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.Label. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.Label\" rel=\"Ext.form.Label\" class=\"docClass\">Ext.form.Label</a>.</p>\n<p>Defaults to: <code>'label'</code></p></div></div></div><div id='property-loadmask' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-loadmask' class='name expandable'>loadmask</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.LoadMask. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.LoadMask\" rel=\"Ext.LoadMask\" class=\"docClass\">Ext.LoadMask</a>.</p>\n<p>Defaults to: <code>'loadmask'</code></p></div></div></div><div id='property-menu' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-menu' class='name expandable'>menu</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.menu.Menu. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.menu.Menu\" rel=\"Ext.menu.Menu\" class=\"docClass\">Ext.menu.Menu</a>.</p>\n<p>Defaults to: <code>'menu'</code></p></div></div></div><div id='property-menucheckitem' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-menucheckitem' class='name expandable'>menucheckitem</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.menu.CheckItem. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.menu.CheckItem\" rel=\"Ext.menu.CheckItem\" class=\"docClass\">Ext.menu.CheckItem</a>.</p>\n<p>Defaults to: <code>'menucheckitem'</code></p></div></div></div><div id='property-menuitem' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-menuitem' class='name expandable'>menuitem</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.menu.Item. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.menu.Item\" rel=\"Ext.menu.Item\" class=\"docClass\">Ext.menu.Item</a>.</p>\n<p>Defaults to: <code>'menuitem'</code></p></div></div></div><div id='property-menuseparator' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-menuseparator' class='name expandable'>menuseparator</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.menu.Separator. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.menu.Separator\" rel=\"Ext.menu.Separator\" class=\"docClass\">Ext.menu.Separator</a>.</p>\n<p>Defaults to: <code>'menuseparator'</code></p></div></div></div><div id='property-messagebox' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-messagebox' class='name expandable'>messagebox</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.window.MessageBox. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.window.MessageBox\" rel=\"Ext.window.MessageBox\" class=\"docClass\">Ext.window.MessageBox</a>.</p>\n<p>Defaults to: <code>'messagebox'</code></p></div></div></div><div id='property-monthpicker' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-monthpicker' class='name expandable'>monthpicker</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.picker.Month. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.picker.Month\" rel=\"Ext.picker.Month\" class=\"docClass\">Ext.picker.Month</a>.</p>\n<p>Defaults to: <code>'monthpicker'</code></p></div></div></div><div id='property-multiselect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-multiselect' class='name expandable'>multiselect</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ux.form.MultiSelect. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ux.form.MultiSelect\" rel=\"Ext.ux.form.MultiSelect\" class=\"docClass\">Ext.ux.form.MultiSelect</a>.</p>\n<p>Defaults to: <code>'multiselect'</code></p></div></div></div><div id='property-multiselectfield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-multiselectfield' class='name expandable'>multiselectfield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ux.form.MultiSelect. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ux.form.MultiSelect\" rel=\"Ext.ux.form.MultiSelect\" class=\"docClass\">Ext.ux.form.MultiSelect</a>.</p>\n<p>Defaults to: <code>'multiselectfield'</code></p></div></div></div><div id='property-multislider' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-multislider' class='name expandable'>multislider</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.slider.Multi. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.slider.Multi\" rel=\"Ext.slider.Multi\" class=\"docClass\">Ext.slider.Multi</a>.</p>\n<p>Defaults to: <code>'multislider'</code></p></div></div></div><div id='property-numbercolumn' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-numbercolumn' class='name expandable'>numbercolumn</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.column.Number. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.column.Number\" rel=\"Ext.grid.column.Number\" class=\"docClass\">Ext.grid.column.Number</a>.</p>\n<p>Defaults to: <code>'numbercolumn'</code></p></div></div></div><div id='property-numberfield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-numberfield' class='name expandable'>numberfield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.Number. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.Number\" rel=\"Ext.form.field.Number\" class=\"docClass\">Ext.form.field.Number</a>.</p>\n<p>Defaults to: <code>'numberfield'</code></p></div></div></div><div id='property-pagingtoolbar' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-pagingtoolbar' class='name expandable'>pagingtoolbar</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.toolbar.Paging. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.toolbar.Paging\" rel=\"Ext.toolbar.Paging\" class=\"docClass\">Ext.toolbar.Paging</a>.</p>\n<p>Defaults to: <code>'pagingtoolbar'</code></p></div></div></div><div id='property-panel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-panel' class='name expandable'>panel</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.panel.Panel. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>.</p>\n<p>Defaults to: <code>'panel'</code></p></div></div></div><div id='property-pickerfield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-pickerfield' class='name expandable'>pickerfield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.Picker. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.Picker\" rel=\"Ext.form.field.Picker\" class=\"docClass\">Ext.form.field.Picker</a>.</p>\n<p>Defaults to: <code>'pickerfield'</code></p></div></div></div><div id='property-progressbar' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-progressbar' class='name expandable'>progressbar</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ProgressBar. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ProgressBar\" rel=\"Ext.ProgressBar\" class=\"docClass\">Ext.ProgressBar</a>.</p>\n<p>Defaults to: <code>'progressbar'</code></p></div></div></div><div id='property-propertygrid' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-propertygrid' class='name expandable'>propertygrid</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.property.Grid. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.property.Grid\" rel=\"Ext.grid.property.Grid\" class=\"docClass\">Ext.grid.property.Grid</a>.</p>\n<p>Defaults to: <code>'propertygrid'</code></p></div></div></div><div id='property-quicktip' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-quicktip' class='name expandable'>quicktip</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.tip.QuickTip. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.tip.QuickTip\" rel=\"Ext.tip.QuickTip\" class=\"docClass\">Ext.tip.QuickTip</a>.</p>\n<p>Defaults to: <code>'quicktip'</code></p></div></div></div><div id='property-radio' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-radio' class='name expandable'>radio</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.Radio. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.Radio\" rel=\"Ext.form.field.Radio\" class=\"docClass\">Ext.form.field.Radio</a>.</p>\n<p>Defaults to: <code>'radio'</code></p></div></div></div><div id='property-radiofield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-radiofield' class='name expandable'>radiofield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.Radio. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.Radio\" rel=\"Ext.form.field.Radio\" class=\"docClass\">Ext.form.field.Radio</a>.</p>\n<p>Defaults to: <code>'radiofield'</code></p></div></div></div><div id='property-radiogroup' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-radiogroup' class='name expandable'>radiogroup</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.RadioGroup. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.RadioGroup\" rel=\"Ext.form.RadioGroup\" class=\"docClass\">Ext.form.RadioGroup</a>.</p>\n<p>Defaults to: <code>'radiogroup'</code></p></div></div></div><div id='property-roweditor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-roweditor' class='name expandable'>roweditor</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.grid.RowEditor. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.RowEditor\" rel=\"Ext.grid.RowEditor\" class=\"docClass\">Ext.grid.RowEditor</a>.</p>\n<p>Defaults to: <code>'roweditor'</code></p></div></div></div><div id='property-roweditorbuttons' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-roweditorbuttons' class='name expandable'>roweditorbuttons</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.grid.RowEditorButtons. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.RowEditorButtons\" rel=\"Ext.grid.RowEditorButtons\" class=\"docClass\">Ext.grid.RowEditorButtons</a>.</p>\n<p>Defaults to: <code>'roweditorbuttons'</code></p></div></div></div><div id='property-rownumberer' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-rownumberer' class='name expandable'>rownumberer</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.column.RowNumberer. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.column.RowNumberer\" rel=\"Ext.grid.column.RowNumberer\" class=\"docClass\">Ext.grid.column.RowNumberer</a>.</p>\n<p>Defaults to: <code>'rownumberer'</code></p></div></div></div><div id='property-slider' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-slider' class='name expandable'>slider</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.slider.Single. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.slider.Single\" rel=\"Ext.slider.Single\" class=\"docClass\">Ext.slider.Single</a>.</p>\n<p>Defaults to: <code>'slider'</code></p></div></div></div><div id='property-sliderfield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-sliderfield' class='name expandable'>sliderfield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.slider.Single. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.slider.Single\" rel=\"Ext.slider.Single\" class=\"docClass\">Ext.slider.Single</a>.</p>\n<p>Defaults to: <code>'sliderfield'</code></p></div></div></div><div id='property-slidertip' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-slidertip' class='name expandable'>slidertip</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.slider.Tip. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.slider.Tip\" rel=\"Ext.slider.Tip\" class=\"docClass\">Ext.slider.Tip</a>.</p>\n<p>Defaults to: <code>'slidertip'</code></p></div></div></div><div id='property-spinnerfield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-spinnerfield' class='name expandable'>spinnerfield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.Spinner. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.Spinner\" rel=\"Ext.form.field.Spinner\" class=\"docClass\">Ext.form.field.Spinner</a>.</p>\n<p>Defaults to: <code>'spinnerfield'</code></p></div></div></div><div id='property-splitbutton' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-splitbutton' class='name expandable'>splitbutton</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.button.Split. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.button.Split\" rel=\"Ext.button.Split\" class=\"docClass\">Ext.button.Split</a>.</p>\n<p>Defaults to: <code>'splitbutton'</code></p></div></div></div><div id='property-splitter' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-splitter' class='name expandable'>splitter</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.resizer.Splitter. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.resizer.Splitter\" rel=\"Ext.resizer.Splitter\" class=\"docClass\">Ext.resizer.Splitter</a>.</p>\n<p>Defaults to: <code>'splitter'</code></p></div></div></div><div id='property-statusbar' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-statusbar' class='name expandable'>statusbar</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ux.statusbar.StatusBar. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ux.statusbar.StatusBar\" rel=\"Ext.ux.statusbar.StatusBar\" class=\"docClass\">Ext.ux.statusbar.StatusBar</a>.</p>\n<p>Defaults to: <code>'statusbar'</code></p></div></div></div><div id='property-tab' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-tab' class='name expandable'>tab</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.tab.Tab. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.tab.Tab\" rel=\"Ext.tab.Tab\" class=\"docClass\">Ext.tab.Tab</a>.</p>\n<p>Defaults to: <code>'tab'</code></p></div></div></div><div id='property-tabbar' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-tabbar' class='name expandable'>tabbar</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.tab.Bar. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.tab.Bar\" rel=\"Ext.tab.Bar\" class=\"docClass\">Ext.tab.Bar</a>.</p>\n<p>Defaults to: <code>'tabbar'</code></p></div></div></div><div id='property-tablepanel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-tablepanel' class='name expandable'>tablepanel</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.panel.Table. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.panel.Table\" rel=\"Ext.panel.Table\" class=\"docClass\">Ext.panel.Table</a>.</p>\n<p>Defaults to: <code>'tablepanel'</code></p></div></div></div><div id='property-tableview' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-tableview' class='name expandable'>tableview</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.view.Table. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.view.Table\" rel=\"Ext.view.Table\" class=\"docClass\">Ext.view.Table</a>.</p>\n<p>Defaults to: <code>'tableview'</code></p></div></div></div><div id='property-tabpanel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-tabpanel' class='name expandable'>tabpanel</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.tab.Panel. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.tab.Panel\" rel=\"Ext.tab.Panel\" class=\"docClass\">Ext.tab.Panel</a>.</p>\n<p>Defaults to: <code>'tabpanel'</code></p></div></div></div><div id='property-tbfill' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-tbfill' class='name expandable'>tbfill</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.toolbar.Fill. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.toolbar.Fill\" rel=\"Ext.toolbar.Fill\" class=\"docClass\">Ext.toolbar.Fill</a>.</p>\n<p>Defaults to: <code>'tbfill'</code></p></div></div></div><div id='property-tbitem' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-tbitem' class='name expandable'>tbitem</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.toolbar.Item. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.toolbar.Item\" rel=\"Ext.toolbar.Item\" class=\"docClass\">Ext.toolbar.Item</a>.</p>\n<p>Defaults to: <code>'tbitem'</code></p></div></div></div><div id='property-tbseparator' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-tbseparator' class='name expandable'>tbseparator</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.toolbar.Separator. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.toolbar.Separator\" rel=\"Ext.toolbar.Separator\" class=\"docClass\">Ext.toolbar.Separator</a>.</p>\n<p>Defaults to: <code>'tbseparator'</code></p></div></div></div><div id='property-tbspacer' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-tbspacer' class='name expandable'>tbspacer</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.toolbar.Spacer. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.toolbar.Spacer\" rel=\"Ext.toolbar.Spacer\" class=\"docClass\">Ext.toolbar.Spacer</a>.</p>\n<p>Defaults to: <code>'tbspacer'</code></p></div></div></div><div id='property-tbtext' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-tbtext' class='name expandable'>tbtext</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.toolbar.TextItem. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.toolbar.TextItem\" rel=\"Ext.toolbar.TextItem\" class=\"docClass\">Ext.toolbar.TextItem</a>.</p>\n<p>Defaults to: <code>'tbtext'</code></p></div></div></div><div id='property-templatecolumn' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-templatecolumn' class='name expandable'>templatecolumn</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.column.Template. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.column.Template\" rel=\"Ext.grid.column.Template\" class=\"docClass\">Ext.grid.column.Template</a>.</p>\n<p>Defaults to: <code>'templatecolumn'</code></p></div></div></div><div id='property-text' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-text' class='name expandable'>text</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.draw.Text. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.draw.Text\" rel=\"Ext.draw.Text\" class=\"docClass\">Ext.draw.Text</a>.</p>\n<p>Defaults to: <code>'text'</code></p></div></div></div><div id='property-textarea' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-textarea' class='name expandable'>textarea</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.TextArea. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.TextArea\" rel=\"Ext.form.field.TextArea\" class=\"docClass\">Ext.form.field.TextArea</a>.</p>\n<p>Defaults to: <code>'textarea'</code></p></div></div></div><div id='property-textareafield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-textareafield' class='name expandable'>textareafield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.TextArea. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.TextArea\" rel=\"Ext.form.field.TextArea\" class=\"docClass\">Ext.form.field.TextArea</a>.</p>\n<p>Defaults to: <code>'textareafield'</code></p></div></div></div><div id='property-textfield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-textfield' class='name expandable'>textfield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.Text. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.Text\" rel=\"Ext.form.field.Text\" class=\"docClass\">Ext.form.field.Text</a>.</p>\n<p>Defaults to: <code>'textfield'</code></p></div></div></div><div id='property-timefield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-timefield' class='name expandable'>timefield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.Time. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.Time\" rel=\"Ext.form.field.Time\" class=\"docClass\">Ext.form.field.Time</a>.</p>\n<p>Defaults to: <code>'timefield'</code></p></div></div></div><div id='property-timepicker' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-timepicker' class='name expandable'>timepicker</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.picker.Time. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.picker.Time\" rel=\"Ext.picker.Time\" class=\"docClass\">Ext.picker.Time</a>.</p>\n<p>Defaults to: <code>'timepicker'</code></p></div></div></div><div id='property-tip' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-tip' class='name expandable'>tip</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.tip.Tip. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.tip.Tip\" rel=\"Ext.tip.Tip\" class=\"docClass\">Ext.tip.Tip</a>.</p>\n<p>Defaults to: <code>'tip'</code></p></div></div></div><div id='property-tool' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-tool' class='name expandable'>tool</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.panel.Tool. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.panel.Tool\" rel=\"Ext.panel.Tool\" class=\"docClass\">Ext.panel.Tool</a>.</p>\n<p>Defaults to: <code>'tool'</code></p></div></div></div><div id='property-toolbar' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-toolbar' class='name expandable'>toolbar</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.toolbar.Toolbar. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.toolbar.Toolbar\" rel=\"Ext.toolbar.Toolbar\" class=\"docClass\">Ext.toolbar.Toolbar</a>.</p>\n<p>Defaults to: <code>'toolbar'</code></p></div></div></div><div id='property-tooltip' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-tooltip' class='name expandable'>tooltip</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.tip.ToolTip. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.tip.ToolTip\" rel=\"Ext.tip.ToolTip\" class=\"docClass\">Ext.tip.ToolTip</a>.</p>\n<p>Defaults to: <code>'tooltip'</code></p></div></div></div><div id='property-treecolumn' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-treecolumn' class='name expandable'>treecolumn</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.tree.Column. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.tree.Column\" rel=\"Ext.tree.Column\" class=\"docClass\">Ext.tree.Column</a>.</p>\n<p>Defaults to: <code>'treecolumn'</code></p></div></div></div><div id='property-treepanel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-treepanel' class='name expandable'>treepanel</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.tree.Panel. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.tree.Panel\" rel=\"Ext.tree.Panel\" class=\"docClass\">Ext.tree.Panel</a>.</p>\n<p>Defaults to: <code>'treepanel'</code></p></div></div></div><div id='property-treepicker' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-treepicker' class='name expandable'>treepicker</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ux.TreePicker. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ux.TreePicker\" rel=\"Ext.ux.TreePicker\" class=\"docClass\">Ext.ux.TreePicker</a>.</p>\n<p>Defaults to: <code>'treepicker'</code></p></div></div></div><div id='property-treeview' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-treeview' class='name expandable'>treeview</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.tree.View. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.tree.View\" rel=\"Ext.tree.View\" class=\"docClass\">Ext.tree.View</a>.</p>\n<p>Defaults to: <code>'treeview'</code></p></div></div></div><div id='property-trigger' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-trigger' class='name expandable'>trigger</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.Trigger. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.Trigger\" rel=\"Ext.form.field.Trigger\" class=\"docClass\">Ext.form.field.Trigger</a>.</p>\n<p>Defaults to: <code>'trigger'</code></p></div></div></div><div id='property-triggerfield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-triggerfield' class='name expandable'>triggerfield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.form.field.Trigger. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.form.field.Trigger\" rel=\"Ext.form.field.Trigger\" class=\"docClass\">Ext.form.field.Trigger</a>.</p>\n<p>Defaults to: <code>'triggerfield'</code></p></div></div></div><div id='property-uxiframe' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-uxiframe' class='name expandable'>uxiframe</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ux.IFrame. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ux.IFrame\" rel=\"Ext.ux.IFrame\" class=\"docClass\">Ext.ux.IFrame</a>.</p>\n<p>Defaults to: <code>'uxiframe'</code></p></div></div></div><div id='property-viewport' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-viewport' class='name expandable'>viewport</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.container.Viewport. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.container.Viewport\" rel=\"Ext.container.Viewport\" class=\"docClass\">Ext.container.Viewport</a>.</p>\n<p>Defaults to: <code>'viewport'</code></p></div></div></div><div id='property-window' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Widget'>Ext.enums.Widget</span><br/><a href='source/enums.html#Ext-enums-Widget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Widget-property-window' class='name expandable'>window</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.window.Window. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.window.Window\" rel=\"Ext.window.Window\" class=\"docClass\">Ext.window.Window</a>.</p>\n<p>Defaults to: <code>'window'</code></p></div></div></div></div></div></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"enums.html#Ext-enums-Widget","filename":"enums.js"}],"linenr":10,"members":{"property":[{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"actioncolumn","id":"property-actioncolumn"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"booleancolumn","id":"property-booleancolumn"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":true},"name":"bordersplitter","id":"property-bordersplitter"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"boundlist","id":"property-boundlist"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"box","id":"property-box"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"button","id":"property-button"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"buttongroup","id":"property-buttongroup"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"chart","id":"property-chart"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"checkbox","id":"property-checkbox"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"checkboxfield","id":"property-checkboxfield"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"checkboxgroup","id":"property-checkboxgroup"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"checkcolumn","id":"property-checkcolumn"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"colormenu","id":"property-colormenu"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"colorpicker","id":"property-colorpicker"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"combo","id":"property-combo"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"combobox","id":"property-combobox"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"component","id":"property-component"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"container","id":"property-container"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"cycle","id":"property-cycle"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"dataview","id":"property-dataview"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"datecolumn","id":"property-datecolumn"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"datefield","id":"property-datefield"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"datemenu","id":"property-datemenu"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"datepicker","id":"property-datepicker"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"displayfield","id":"property-displayfield"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"draw","id":"property-draw"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"editor","id":"property-editor"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"eventrecordermanager","id":"property-eventrecordermanager"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"field","id":"property-field"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"fieldcontainer","id":"property-fieldcontainer"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"fieldset","id":"property-fieldset"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"filebutton","id":"property-filebutton"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"filefield","id":"property-filefield"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"fileuploadfield","id":"property-fileuploadfield"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"flash","id":"property-flash"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"form","id":"property-form"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"gmappanel","id":"property-gmappanel"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"grid","id":"property-grid"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"gridcolumn","id":"property-gridcolumn"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"gridpanel","id":"property-gridpanel"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"gridview","id":"property-gridview"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"grouptabpanel","id":"property-grouptabpanel"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"header","id":"property-header"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"headercontainer","id":"property-headercontainer"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"hidden","id":"property-hidden"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"hiddenfield","id":"property-hiddenfield"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"htmleditor","id":"property-htmleditor"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"image","id":"property-image"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"imagecomponent","id":"property-imagecomponent"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"itemselector","id":"property-itemselector"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"itemselectorfield","id":"property-itemselectorfield"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"jsonpstore","id":"property-jsonpstore"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"label","id":"property-label"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"loadmask","id":"property-loadmask"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"menu","id":"property-menu"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"menucheckitem","id":"property-menucheckitem"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"menuitem","id":"property-menuitem"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"menuseparator","id":"property-menuseparator"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"messagebox","id":"property-messagebox"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":true},"name":"monthpicker","id":"property-monthpicker"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"multiselect","id":"property-multiselect"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"multiselectfield","id":"property-multiselectfield"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"multislider","id":"property-multislider"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"numbercolumn","id":"property-numbercolumn"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"numberfield","id":"property-numberfield"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"pagingtoolbar","id":"property-pagingtoolbar"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"panel","id":"property-panel"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"pickerfield","id":"property-pickerfield"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"progressbar","id":"property-progressbar"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"propertygrid","id":"property-propertygrid"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"quicktip","id":"property-quicktip"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"radio","id":"property-radio"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"radiofield","id":"property-radiofield"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"radiogroup","id":"property-radiogroup"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":true},"name":"roweditor","id":"property-roweditor"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":true},"name":"roweditorbuttons","id":"property-roweditorbuttons"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"rownumberer","id":"property-rownumberer"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"slider","id":"property-slider"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"sliderfield","id":"property-sliderfield"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"slidertip","id":"property-slidertip"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"spinnerfield","id":"property-spinnerfield"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"splitbutton","id":"property-splitbutton"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"splitter","id":"property-splitter"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"statusbar","id":"property-statusbar"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"tab","id":"property-tab"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"tabbar","id":"property-tabbar"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"tablepanel","id":"property-tablepanel"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"tableview","id":"property-tableview"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"tabpanel","id":"property-tabpanel"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"tbfill","id":"property-tbfill"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"tbitem","id":"property-tbitem"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"tbseparator","id":"property-tbseparator"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"tbspacer","id":"property-tbspacer"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"tbtext","id":"property-tbtext"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"templatecolumn","id":"property-templatecolumn"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"text","id":"property-text"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"textarea","id":"property-textarea"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"textareafield","id":"property-textareafield"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"textfield","id":"property-textfield"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"timefield","id":"property-timefield"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"timepicker","id":"property-timepicker"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"tip","id":"property-tip"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"tool","id":"property-tool"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"toolbar","id":"property-toolbar"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"tooltip","id":"property-tooltip"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":true},"name":"treecolumn","id":"property-treecolumn"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"treepanel","id":"property-treepanel"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"treepicker","id":"property-treepicker"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"treeview","id":"property-treeview"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"trigger","id":"property-trigger"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"triggerfield","id":"property-triggerfield"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"uxiframe","id":"property-uxiframe"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"viewport","id":"property-viewport"},{"tagname":"property","owner":"Ext.enums.Widget","meta":{"private":null},"name":"window","id":"property-window"}],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.enums.Widget","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.enums.Widget","mixins":[],"mixedInto":[]});