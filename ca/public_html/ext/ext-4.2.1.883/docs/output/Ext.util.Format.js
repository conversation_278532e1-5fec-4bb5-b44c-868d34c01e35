Ext.data.JsonP.Ext_util_Format({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Format.html#Ext-util-Format' target='_blank'>Format.js</a></div></pre><div class='doc-contents'><p>This class is a centralized place for formatting functions. It includes\nfunctions to format various different types of data, such as text, dates and numeric values.</p>\n\n<h2>Localization</h2>\n\n<p>This class contains several options for localization. These can be set once the library has loaded,\nall calls to the functions from that point will use the locale settings that were specified.</p>\n\n<p>Options include:</p>\n\n<ul>\n<li>thousandSeparator</li>\n<li>decimalSeparator</li>\n<li>currenyPrecision</li>\n<li>currencySign</li>\n<li>currencyAtEnd</li>\n</ul>\n\n\n<p>This class also uses the default date format defined here: <a href=\"#!/api/Ext.Date-property-defaultFormat\" rel=\"Ext.Date-property-defaultFormat\" class=\"docClass\">Ext.Date.defaultFormat</a>.</p>\n\n<h2>Using with renderers</h2>\n\n<p>There are two helper functions that return a new function that can be used in conjunction with\ngrid renderers:</p>\n\n<pre><code>columns: [{\n    dataIndex: 'date',\n    renderer: <a href=\"#!/api/Ext.util.Format-method-dateRenderer\" rel=\"Ext.util.Format-method-dateRenderer\" class=\"docClass\">Ext.util.Format.dateRenderer</a>('Y-m-d')\n}, {\n    dataIndex: 'time',\n    renderer: <a href=\"#!/api/Ext.util.Format-method-numberRenderer\" rel=\"Ext.util.Format-method-numberRenderer\" class=\"docClass\">Ext.util.Format.numberRenderer</a>('0.000')\n}]\n</code></pre>\n\n<p>Functions that only take a single argument can also be passed directly:</p>\n\n<pre><code>columns: [{\n    dataIndex: 'cost',\n    renderer: <a href=\"#!/api/Ext.util.Format-method-usMoney\" rel=\"Ext.util.Format-method-usMoney\" class=\"docClass\">Ext.util.Format.usMoney</a>\n}, {\n    dataIndex: 'productCode',\n    renderer: <a href=\"#!/api/Ext.util.Format-method-uppercase\" rel=\"Ext.util.Format-method-uppercase\" class=\"docClass\">Ext.util.Format.uppercase</a>\n}]\n</code></pre>\n\n<h2>Using with XTemplates</h2>\n\n<p>XTemplates can also directly use <a href=\"#!/api/Ext.util.Format\" rel=\"Ext.util.Format\" class=\"docClass\">Ext.util.Format</a> functions:</p>\n\n<pre><code>new <a href=\"#!/api/Ext.XTemplate\" rel=\"Ext.XTemplate\" class=\"docClass\">Ext.XTemplate</a>([\n    'Date: {startDate:date(\"Y-m-d\")}',\n    'Cost: {cost:usMoney}'\n]);\n</code></pre>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-currencyAtEnd' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-property-currencyAtEnd' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-property-currencyAtEnd' class='name expandable'>currencyAtEnd</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>This may be set to true to make the currency function\nappend the currency sign to the formatted value. ...</div><div class='long'><p>This may be set to <code>true</code> to make the <a href=\"#!/api/Ext.util.Format-method-currency\" rel=\"Ext.util.Format-method-currency\" class=\"docClass\">currency</a> function\nappend the currency sign to the formatted value.</p>\n\n<p>This may be overridden in a locale file.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-currencyPrecision' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-property-currencyPrecision' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-property-currencyPrecision' class='name expandable'>currencyPrecision</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The number of decimal places that the currency function displays. ...</div><div class='long'><p>The number of decimal places that the <a href=\"#!/api/Ext.util.Format-method-currency\" rel=\"Ext.util.Format-method-currency\" class=\"docClass\">currency</a> function displays.</p>\n\n<p>This may be overridden in a locale file.</p>\n<p>Defaults to: <code>2</code></p></div></div></div><div id='property-currencySign' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-property-currencySign' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-property-currencySign' class='name expandable'>currencySign</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The currency sign that the currency function displays. ...</div><div class='long'><p>The currency sign that the <a href=\"#!/api/Ext.util.Format-method-currency\" rel=\"Ext.util.Format-method-currency\" class=\"docClass\">currency</a> function displays.</p>\n\n<p>This may be overridden in a locale file.</p>\n<p>Defaults to: <code>'$'</code></p></div></div></div><div id='property-decimalSeparator' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-property-decimalSeparator' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-property-decimalSeparator' class='name expandable'>decimalSeparator</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The character that the number function uses as a decimal point. ...</div><div class='long'><p>The character that the <a href=\"#!/api/Ext.util.Format-method-number\" rel=\"Ext.util.Format-method-number\" class=\"docClass\">number</a> function uses as a decimal point.</p>\n\n<p>This may be overridden in a locale file.</p>\n<p>Defaults to: <code>'.'</code></p></div></div></div><div id='property-thousandSeparator' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-property-thousandSeparator' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-property-thousandSeparator' class='name expandable'>thousandSeparator</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The character that the number function uses as a thousand separator. ...</div><div class='long'><p>The character that the <a href=\"#!/api/Ext.util.Format-method-number\" rel=\"Ext.util.Format-method-number\" class=\"docClass\">number</a> function uses as a thousand separator.</p>\n\n<p>This may be overridden in a locale file.</p>\n<p>Defaults to: <code>','</code></p></div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-attributes' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-attributes' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-attributes' class='name expandable'>attributes</a>( <span class='pre'>attributes</span> )</div><div class='description'><div class='short'>Formats an object of name value properties as HTML element attribute values suitable for using when creating textual ...</div><div class='long'><p>Formats an object of name value properties as HTML element attribute values suitable for using when creating textual markup.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>attributes</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>An object containing the HTML attributes as properties eg: <code>{height:40, vAlign:'top'}</code></p>\n</div></li></ul></div></div></div><div id='method-capitalize' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-capitalize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-capitalize' class='name expandable'>capitalize</a>( <span class='pre'>string</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Alias for Ext.String.capitalize. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.String-method-capitalize\" rel=\"Ext.String-method-capitalize\" class=\"docClass\">Ext.String.capitalize</a>.</p>\n\n<p>Capitalize the given string</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>string</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-currency' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-currency' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-currency' class='name expandable'>currency</a>( <span class='pre'>value, [sign], [decimals], [end]</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Format a number as a currency. ...</div><div class='long'><p>Format a number as a currency.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The numeric value to format</p>\n</div></li><li><span class='pre'>sign</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The currency sign to use (defaults to <a href=\"#!/api/Ext.util.Format-property-currencySign\" rel=\"Ext.util.Format-property-currencySign\" class=\"docClass\">currencySign</a>)</p>\n</div></li><li><span class='pre'>decimals</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>The number of decimals to use for the currency\n(defaults to <a href=\"#!/api/Ext.util.Format-property-currencyPrecision\" rel=\"Ext.util.Format-property-currencyPrecision\" class=\"docClass\">currencyPrecision</a>)</p>\n</div></li><li><span class='pre'>end</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True if the currency sign should be at the end of the string\n(defaults to <a href=\"#!/api/Ext.util.Format-property-currencyAtEnd\" rel=\"Ext.util.Format-property-currencyAtEnd\" class=\"docClass\">currencyAtEnd</a>)</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The formatted currency string</p>\n</div></li></ul></div></div></div><div id='method-date' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-date' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-date' class='name expandable'>date</a>( <span class='pre'>value, [format]</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Formats the passed date using the specified format pattern. ...</div><div class='long'><p>Formats the passed date using the specified format pattern.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The value to format. If a string is passed, it is converted to a Date\nby the Javascript's built-in <a href=\"#!/api/Date-static-method-parse\" rel=\"Date-static-method-parse\" class=\"docClass\">Date.parse</a> method.</p>\n</div></li><li><span class='pre'>format</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Any valid date format string. Defaults to <a href=\"#!/api/Ext.Date-property-defaultFormat\" rel=\"Ext.Date-property-defaultFormat\" class=\"docClass\">Ext.Date.defaultFormat</a>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The formatted date string.</p>\n</div></li></ul></div></div></div><div id='method-dateRenderer' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-dateRenderer' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-dateRenderer' class='name expandable'>dateRenderer</a>( <span class='pre'>format</span> ) : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></div><div class='description'><div class='short'>Returns a date rendering function that can be reused to apply a date format multiple times efficiently. ...</div><div class='long'><p>Returns a date rendering function that can be reused to apply a date format multiple times efficiently.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>format</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>Any valid date format string. Defaults to <a href=\"#!/api/Ext.Date-property-defaultFormat\" rel=\"Ext.Date-property-defaultFormat\" class=\"docClass\">Ext.Date.defaultFormat</a>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></span><div class='sub-desc'><p>The date formatting function</p>\n</div></li></ul></div></div></div><div id='method-defaultValue' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-defaultValue' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-defaultValue' class='name expandable'>defaultValue</a>( <span class='pre'>value, [defaultValue]</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Checks a reference and converts it to the default value if it's empty. ...</div><div class='long'><p>Checks a reference and converts it to the default value if it's empty.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>Reference to check</p>\n</div></li><li><span class='pre'>defaultValue</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The value to insert of it's undefined.</p>\n<p>Defaults to: <code>&quot;&quot;</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-ellipsis' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-ellipsis' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-ellipsis' class='name expandable'>ellipsis</a>( <span class='pre'>value, length, [word]</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Alias for Ext.String.ellipsis. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.String-method-ellipsis\" rel=\"Ext.String-method-ellipsis\" class=\"docClass\">Ext.String.ellipsis</a>.</p>\n\n<p>Truncate a string and add an ellipsis ('...') to the end if it exceeds the specified length.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The string to truncate.</p>\n</div></li><li><span class='pre'>length</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The maximum length to allow before truncating.</p>\n</div></li><li><span class='pre'>word</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p><code>true</code> to try to find a common word break.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The converted text.</p>\n</div></li></ul></div></div></div><div id='method-escapeRegex' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-escapeRegex' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-escapeRegex' class='name expandable'>escapeRegex</a>( <span class='pre'>str</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Escapes the passed string for use in a regular expression. ...</div><div class='long'><p>Escapes the passed string for use in a regular expression.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>str</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-fileSize' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-fileSize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-fileSize' class='name expandable'>fileSize</a>( <span class='pre'>size</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Simple format for a file size (xxx bytes, xxx KB, xxx MB). ...</div><div class='long'><p>Simple format for a file size (xxx bytes, xxx KB, xxx MB).</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>size</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The numeric value to format</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The formatted file size</p>\n</div></li></ul></div></div></div><div id='method-format' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-format' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-format' class='name expandable'>format</a>( <span class='pre'>string, values</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Alias for Ext.String.format. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.String-method-format\" rel=\"Ext.String-method-format\" class=\"docClass\">Ext.String.format</a>.</p>\n\n<p>Allows you to define a tokenized string and pass an arbitrary number of arguments to replace the tokens.  Each\ntoken must be unique, and must increment in the format {0}, {1}, etc.  Example usage:</p>\n\n<pre><code>var cls = 'my-class',\n    text = 'Some text';\nvar s = <a href=\"#!/api/Ext.String-method-format\" rel=\"Ext.String-method-format\" class=\"docClass\">Ext.String.format</a>('&lt;div class=\"{0}\"&gt;{1}&lt;/div&gt;', cls, text);\n// s now contains the string: '&lt;div class=\"my-class\"&gt;Some text&lt;/div&gt;'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>string</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The tokenized string to be formatted.</p>\n</div></li><li><span class='pre'>values</span> : Mixed...<div class='sub-desc'><p>The values to replace tokens <code>{0}</code>, <code>{1}</code>, etc in order.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The formatted string.</p>\n</div></li></ul></div></div></div><div id='method-htmlDecode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-htmlDecode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-htmlDecode' class='name expandable'>htmlDecode</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Alias for Ext.String.htmlDecode. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.String-method-htmlDecode\" rel=\"Ext.String-method-htmlDecode\" class=\"docClass\">Ext.String.htmlDecode</a>.</p>\n\n<p>Convert certain characters (&amp;, &lt;, >, ', and \") from their HTML character equivalents.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The string to decode.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The decoded text.</p>\n</div></li></ul></div></div></div><div id='method-htmlEncode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-htmlEncode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-htmlEncode' class='name expandable'>htmlEncode</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Alias for Ext.String.htmlEncode. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.String-method-htmlEncode\" rel=\"Ext.String-method-htmlEncode\" class=\"docClass\">Ext.String.htmlEncode</a>.</p>\n\n<p>Convert certain characters (&amp;, &lt;, >, ', and \") to their HTML character equivalents for literal display in web pages.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The string to encode.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The encoded text.</p>\n</div></li></ul></div></div></div><div id='method-leftPad' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-leftPad' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-leftPad' class='name expandable'>leftPad</a>( <span class='pre'>string, size, [character]</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Alias for Ext.String.leftPad. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.String-method-leftPad\" rel=\"Ext.String-method-leftPad\" class=\"docClass\">Ext.String.leftPad</a>.</p>\n\n<p>Pads the left side of a string with a specified character.  This is especially useful\nfor normalizing number and date strings.  Example usage:</p>\n\n<pre><code>var s = <a href=\"#!/api/Ext.String-method-leftPad\" rel=\"Ext.String-method-leftPad\" class=\"docClass\">Ext.String.leftPad</a>('123', 5, '0');\n// s now contains the string: '00123'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>string</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The original string.</p>\n</div></li><li><span class='pre'>size</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The total length of the output string.</p>\n</div></li><li><span class='pre'>character</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The character with which to pad the original string.</p>\n<p>Defaults to: <code>' '</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The padded string.</p>\n</div></li></ul></div></div></div><div id='method-lowercase' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-lowercase' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-lowercase' class='name expandable'>lowercase</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Converts a string to all lower case letters. ...</div><div class='long'><p>Converts a string to all lower case letters.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The text to convert</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The converted text</p>\n</div></li></ul></div></div></div><div id='method-math' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-math' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-math' class='name expandable'>math</a>( <span class='pre'></span> ) : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></div><div class='description'><div class='short'>It does simple math for use in a template, for example:\n\nvar tpl = new Ext.Template('{value} * 10 = {value:math(\"* 10...</div><div class='long'><p>It does simple math for use in a template, for example:</p>\n\n<pre><code>var tpl = new <a href=\"#!/api/Ext.Template\" rel=\"Ext.Template\" class=\"docClass\">Ext.Template</a>('{value} * 10 = {value:math(\"* 10\")}');\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></span><div class='sub-desc'><p>A function that operates on the passed value.</p>\n</div></li></ul></div></div></div><div id='method-nl2br' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-nl2br' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-nl2br' class='name expandable'>nl2br</a>( <span class='pre'>v</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Converts newline characters to the HTML tag &lt;br/&gt; ...</div><div class='long'><p>Converts newline characters to the HTML tag <code>&lt;br/&gt;</code></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>v</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The string value to format.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The string with embedded <code>&lt;br/&gt;</code> tags in place of newlines.</p>\n</div></li></ul></div></div></div><div id='method-number' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-number' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-number' class='name expandable'>number</a>( <span class='pre'>v, format</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Formats the passed number according to the passed format string. ...</div><div class='long'><p>Formats the passed number according to the passed format string.</p>\n\n<p>The number of digits after the decimal separator character specifies the number of\ndecimal places in the resulting string. The <em>local-specific</em> decimal character is\nused in the result.</p>\n\n<p>The <em>presence</em> of a thousand separator character in the format string specifies that\nthe <em>locale-specific</em> thousand separator (if any) is inserted separating thousand groups.</p>\n\n<p>By default, \",\" is expected as the thousand separator, and \".\" is expected as the decimal separator.</p>\n\n<h2>New to Ext JS 4</h2>\n\n<p>Locale-specific characters are always used in the formatted output when inserting\nthousand and decimal separators.</p>\n\n<p>The format string must specify separator characters according to US/UK conventions (\",\" as the\nthousand separator, and \".\" as the decimal separator)</p>\n\n<p>To allow specification of format strings according to local conventions for separator characters, add\nthe string <code>/i</code> to the end of the format string.</p>\n\n<p>examples (123456.789):</p>\n\n<ul>\n<li><code>0</code> - (123456) show only digits, no precision</li>\n<li><code>0.00</code> - (123456.78) show only digits, 2 precision</li>\n<li><code>0.0000</code> - (123456.7890) show only digits, 4 precision</li>\n<li><code>0,000</code> - (123,456) show comma and digits, no precision</li>\n<li><code>0,000.00</code> - (123,456.78) show comma and digits, 2 precision</li>\n<li><code>0,0.00</code> - (123,456.78) shortcut method, show comma and digits, 2 precision</li>\n<li><code>0.####</code> - (123,456,789) Allow maximum 4 decimal places, but do not right pad with zeroes</li>\n</ul>\n\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>v</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number to format.</p>\n</div></li><li><span class='pre'>format</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The way you would like to format this text.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The formatted number.</p>\n</div></li></ul></div></div></div><div id='method-numberRenderer' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-numberRenderer' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-numberRenderer' class='name expandable'>numberRenderer</a>( <span class='pre'>format</span> ) : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></div><div class='description'><div class='short'>Returns a number rendering function that can be reused to apply a number format multiple\ntimes efficiently. ...</div><div class='long'><p>Returns a number rendering function that can be reused to apply a number format multiple\ntimes efficiently.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>format</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>Any valid number format string for <a href=\"#!/api/Ext.util.Format-method-number\" rel=\"Ext.util.Format-method-number\" class=\"docClass\">number</a></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></span><div class='sub-desc'><p>The number formatting function</p>\n</div></li></ul></div></div></div><div id='method-parseBox' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-parseBox' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-parseBox' class='name expandable'>parseBox</a>( <span class='pre'>v</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Parses a number or string representing margin sizes into an object. ...</div><div class='long'><p>Parses a number or string representing margin sizes into an object.\nSupports CSS-style margin declarations (e.g. 10, \"10\", \"10 10\", \"10 10 10\" and\n\"10 10 10 10\" are all valid options and would return the same result).</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>v</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The encoded margins</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>An object with margin sizes for top, right, bottom and left</p>\n</div></li></ul></div></div></div><div id='method-plural' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-plural' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-plural' class='name expandable'>plural</a>( <span class='pre'>value, singular, [plural]</span> )</div><div class='description'><div class='short'>Selectively do a plural form of a word based on a numeric value. ...</div><div class='long'><p>Selectively do a plural form of a word based on a numeric value. For example, in a template,\n<code>{commentCount:plural(\"Comment\")}</code>  would result in <code>\"1 Comment\"</code> if commentCount was 1 or\nwould be <code>\"x Comments\"</code> if the value is 0 or greater than 1.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The value to compare against</p>\n</div></li><li><span class='pre'>singular</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The singular form of the word</p>\n</div></li><li><span class='pre'>plural</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The plural form of the word (defaults to the singular with an \"s\")</p>\n</div></li></ul></div></div></div><div id='method-round' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-round' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-round' class='name expandable'>round</a>( <span class='pre'>value, precision</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Rounds the passed number to the required decimal precision. ...</div><div class='long'><p>Rounds the passed number to the required decimal precision.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The numeric value to round.</p>\n</div></li><li><span class='pre'>precision</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of decimal places to which to round the first parameter's value.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The rounded value.</p>\n</div></li></ul></div></div></div><div id='method-stripScripts' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-stripScripts' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-stripScripts' class='name expandable'>stripScripts</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Strips all script tags. ...</div><div class='long'><p>Strips all script tags.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The text from which to strip script tags</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The stripped text</p>\n</div></li></ul></div></div></div><div id='method-stripTags' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-stripTags' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-stripTags' class='name expandable'>stripTags</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Strips all HTML tags. ...</div><div class='long'><p>Strips all HTML tags.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The text from which to strip tags</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The stripped text</p>\n</div></li></ul></div></div></div><div id='method-substr' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-substr' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-substr' class='name expandable'>substr</a>( <span class='pre'>value, start, length</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns a substring from within an original string. ...</div><div class='long'><p>Returns a substring from within an original string.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The original text</p>\n</div></li><li><span class='pre'>start</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The start index of the substring</p>\n</div></li><li><span class='pre'>length</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The length of the substring</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The substring</p>\n</div></li></ul></div></div></div><div id='method-trim' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-trim' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-trim' class='name expandable'>trim</a>( <span class='pre'>string</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Alias for Ext.String.trim. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.String-method-trim\" rel=\"Ext.String-method-trim\" class=\"docClass\">Ext.String.trim</a>.</p>\n\n<p>Trims whitespace from either end of a string, leaving spaces within the string intact.  Example:</p>\n\n<pre><code>var s = '  foo bar  ';\nalert('-' + s + '-');                   //alerts \"- foo bar -\"\nalert('-' + <a href=\"#!/api/Ext.String-method-trim\" rel=\"Ext.String-method-trim\" class=\"docClass\">Ext.String.trim</a>(s) + '-');  //alerts \"-foo bar-\"\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>string</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The string to trim.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The trimmed string.</p>\n</div></li></ul></div></div></div><div id='method-undef' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-undef' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-undef' class='name expandable'>undef</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Checks a reference and converts it to empty string if it is undefined. ...</div><div class='long'><p>Checks a reference and converts it to empty string if it is undefined.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>Reference to check</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Empty string if converted, otherwise the original value</p>\n</div></li></ul></div></div></div><div id='method-uppercase' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-uppercase' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-uppercase' class='name expandable'>uppercase</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Converts a string to all upper case letters. ...</div><div class='long'><p>Converts a string to all upper case letters.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The text to convert</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The converted text</p>\n</div></li></ul></div></div></div><div id='method-usMoney' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Format'>Ext.util.Format</span><br/><a href='source/Format.html#Ext-util-Format-method-usMoney' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Format-method-usMoney' class='name expandable'>usMoney</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Format a number as US currency. ...</div><div class='long'><p>Format a number as US currency.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The numeric value to format</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The formatted currency string</p>\n</div></li></ul></div></div></div></div></div></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"Format.html#Ext-util-Format","filename":"Format.js"}],"linenr":5,"members":{"property":[{"tagname":"property","owner":"Ext.util.Format","meta":{},"name":"currencyAtEnd","id":"property-currencyAtEnd"},{"tagname":"property","owner":"Ext.util.Format","meta":{},"name":"currencyPrecision","id":"property-currencyPrecision"},{"tagname":"property","owner":"Ext.util.Format","meta":{},"name":"currencySign","id":"property-currencySign"},{"tagname":"property","owner":"Ext.util.Format","meta":{},"name":"decimalSeparator","id":"property-decimalSeparator"},{"tagname":"property","owner":"Ext.util.Format","meta":{},"name":"thousandSeparator","id":"property-thousandSeparator"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"attributes","id":"method-attributes"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"capitalize","id":"method-capitalize"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"currency","id":"method-currency"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"date","id":"method-date"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"dateRenderer","id":"method-dateRenderer"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"defaultValue","id":"method-defaultValue"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"ellipsis","id":"method-ellipsis"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"escapeRegex","id":"method-escapeRegex"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"fileSize","id":"method-fileSize"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"format","id":"method-format"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"htmlDecode","id":"method-htmlDecode"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"htmlEncode","id":"method-htmlEncode"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"leftPad","id":"method-leftPad"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"lowercase","id":"method-lowercase"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"math","id":"method-math"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"nl2br","id":"method-nl2br"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"number","id":"method-number"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"numberRenderer","id":"method-numberRenderer"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"parseBox","id":"method-parseBox"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"plural","id":"method-plural"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"round","id":"method-round"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"stripScripts","id":"method-stripScripts"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"stripTags","id":"method-stripTags"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"substr","id":"method-substr"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"trim","id":"method-trim"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"undef","id":"method-undef"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"uppercase","id":"method-uppercase"},{"tagname":"method","owner":"Ext.util.Format","meta":{},"name":"usMoney","id":"method-usMoney"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.util.Format","singleton":true,"override":null,"inheritdoc":null,"id":"class-Ext.util.Format","mixins":[],"mixedInto":[]});