Ext.data.JsonP.Ext_layout_container_Absolute({"alternateClassNames":["Ext.layout.AbsoluteLayout"],"aliases":{"layout":["absolute"]},"enum":null,"parentMixins":["Ext.util.ElementContainer"],"tagname":"class","subclasses":[],"extends":"Ext.layout.container.Anchor","uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Alternate names</h4><div class='alternate-class-name'>Ext.layout.AbsoluteLayout</div><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='docClass'>Ext.layout.Layout</a><div class='subclass '><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='docClass'>Ext.layout.container.Container</a><div class='subclass '><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='docClass'>Ext.layout.container.Auto</a><div class='subclass '><a href='#!/api/Ext.layout.container.Anchor' rel='Ext.layout.container.Anchor' class='docClass'>Ext.layout.container.Anchor</a><div class='subclass '><strong>Ext.layout.container.Absolute</strong></div></div></div></div></div></div><h4>Inherited mixins</h4><div class='dependency'><a href='#!/api/Ext.util.ElementContainer' rel='Ext.util.ElementContainer' class='docClass'>Ext.util.ElementContainer</a></div><h4>Files</h4><div class='dependency'><a href='source/Absolute.html#Ext-layout-container-Absolute' target='_blank'>Absolute.js</a></div></pre><div class='doc-contents'><p>This is a layout that inherits the anchoring of <a href=\"#!/api/Ext.layout.container.Anchor\" rel=\"Ext.layout.container.Anchor\" class=\"docClass\">Ext.layout.container.Anchor</a> and adds the\nability for x/y positioning using the standard x and y component config options.</p>\n\n<p>This class is intended to be extended or created via the <a href=\"#!/api/Ext.container.Container-cfg-layout\" rel=\"Ext.container.Container-cfg-layout\" class=\"docClass\">layout</a>\nconfiguration property.  See <a href=\"#!/api/Ext.container.Container-cfg-layout\" rel=\"Ext.container.Container-cfg-layout\" class=\"docClass\">Ext.container.Container.layout</a> for additional details.</p>\n\n<pre class='inline-example '><code><a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.form.Panel\" rel=\"Ext.form.Panel\" class=\"docClass\">Ext.form.Panel</a>', {\n    title: 'Absolute Layout',\n    width: 300,\n    height: 275,\n    layout: {\n        type: 'absolute'\n        // layout-specific configs go here\n        //itemCls: 'x-abs-layout-item',\n    },\n    url:'save-form.php',\n    defaultType: 'textfield',\n    items: [{\n        x: 10,\n        y: 10,\n        xtype:'label',\n        text: 'Send To:'\n    },{\n        x: 80,\n        y: 10,\n        name: 'to',\n        anchor:'90%'  // anchor width by percentage\n    },{\n        x: 10,\n        y: 40,\n        xtype:'label',\n        text: 'Subject:'\n    },{\n        x: 80,\n        y: 40,\n        name: 'subject',\n        anchor: '90%'  // anchor width by percentage\n    },{\n        x:0,\n        y: 80,\n        xtype: 'textareafield',\n        name: 'msg',\n        anchor: '100% 100%'  // anchor width and height\n    }],\n    renderTo: <a href=\"#!/api/Ext-method-getBody\" rel=\"Ext-method-getBody\" class=\"docClass\">Ext.getBody</a>()\n});\n</code></pre>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-cfg'>Config options</h3><div class='subsection'><div id='cfg-anchor' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Anchor' rel='Ext.layout.container.Anchor' class='defined-in docClass'>Ext.layout.container.Anchor</a><br/><a href='source/Anchor.html#Ext-layout-container-Anchor-cfg-anchor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Anchor-cfg-anchor' class='name expandable'>anchor</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>This configuation option is to be applied to child items of a container managed by\nthis layout (ie. ...</div><div class='long'><p>This configuation option is to be applied to <strong>child <code>items</code></strong> of a container managed by\nthis layout (ie. configured with <code>layout:'anchor'</code>).</p>\n\n<p>This value is what tells the layout how an item should be anchored to the container. <code>items</code>\nadded to an AnchorLayout accept an anchoring-specific config property of <strong>anchor</strong> which is a string\ncontaining two values: the horizontal anchor value and the vertical anchor value (for example, '100% 50%').\nThe following types of anchor values are supported:</p>\n\n<ul>\n<li><p><strong>Percentage</strong> : Any value between 1 and 100, expressed as a percentage.</p>\n\n<p>The first anchor is the percentage width that the item should take up within the container, and the\nsecond is the percentage height.  For example:</p>\n\n<pre><code>// two values specified\nanchor: '100% 50%' // render item complete width of the container and\n                   // 1/2 height of the container\n// one value specified\nanchor: '100%'     // the width value; the height will default to auto\n</code></pre></li>\n<li><p><strong>Offsets</strong> : Any positive or negative integer value.</p>\n\n<p>This is a raw adjustment where the first anchor is the offset from the right edge of the container,\nand the second is the offset from the bottom edge. For example:</p>\n\n<pre><code>// two values specified\nanchor: '-50 -100' // render item the complete width of the container\n                   // minus 50 pixels and\n                   // the complete height minus 100 pixels.\n// one value specified\nanchor: '-50'      // anchor value is assumed to be the right offset value\n                   // bottom offset will default to 0\n</code></pre></li>\n<li><p><strong>Sides</strong> : Valid values are <code>right</code> (or <code>r</code>) and <code>bottom</code> (or <code>b</code>).</p>\n\n<p>Either the container must have a fixed size or an anchorSize config value defined at render time in\norder for these to have any effect.</p></li>\n<li><p><strong>Mixed</strong> :</p>\n\n<p>Anchor values can also be mixed as needed.  For example, to render the width offset from the container\nright edge by 50 pixels and 75% of the container's height use:</p>\n\n<pre><code>anchor:   '-50 75%'\n</code></pre></li>\n</ul>\n\n</div></div></div><div id='cfg-defaultAnchor' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Anchor' rel='Ext.layout.container.Anchor' class='defined-in docClass'>Ext.layout.container.Anchor</a><br/><a href='source/Anchor.html#Ext-layout-container-Anchor-cfg-defaultAnchor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Anchor-cfg-defaultAnchor' class='name expandable'>defaultAnchor</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Default anchor for all child container items applied if no anchor or specific width is set on the child item. ...</div><div class='long'><p>Default anchor for all child <strong>container</strong> items applied if no anchor or specific width is set on the child item.</p>\n<p>Defaults to: <code>'100%'</code></p></div></div></div><div id='cfg-ignoreOnContentChange' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.Absolute'>Ext.layout.container.Absolute</span><br/><a href='source/Absolute.html#Ext-layout-container-Absolute-cfg-ignoreOnContentChange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Absolute-cfg-ignoreOnContentChange' class='name expandable'>ignoreOnContentChange</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True indicates that changes to one item in this layout do not effect the layout in\ngeneral. ...</div><div class='long'><p>True indicates that changes to one item in this layout do not effect the layout in\ngeneral. This may need to be set to false if <a href=\"#!/api/Ext.Component-cfg-autoScroll\" rel=\"Ext.Component-cfg-autoScroll\" class=\"docClass\">Ext.Component.autoScroll</a>\nis enabled for the container.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-itemCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.Absolute'>Ext.layout.container.Absolute</span><br/><a href='source/Absolute.html#Ext-layout-container-Absolute-cfg-itemCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Absolute-cfg-itemCls' class='name expandable'>itemCls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>An optional extra CSS class that will be added to the container. ...</div><div class='long'><p>An optional extra CSS class that will be added to the container. This can be useful for\nadding customized styles to the container or any of its children using standard CSS\nrules. See <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>.<a href=\"#!/api/Ext.Component-cfg-componentCls\" rel=\"Ext.Component-cfg-componentCls\" class=\"docClass\">componentCls</a> also.</p>\n<p>Defaults to: <code>Ext.baseCSSPrefix + 'abs-layout-item'</code></p><p>Overrides: <a href='#!/api/Ext.layout.container.Container-cfg-itemCls' rel='Ext.layout.container.Container-cfg-itemCls' class='docClass'>Ext.layout.container.Container.itemCls</a></p></div></div></div><div id='cfg-reserveScrollbar' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-cfg-reserveScrollbar' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-cfg-reserveScrollbar' class='name expandable'>reserveScrollbar</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Set to true to leave space for a vertical scrollbar (if the OS shows space-consuming scrollbars) regardless\nof whethe...</div><div class='long'><p>Set to <code>true</code> to leave space for a vertical scrollbar (if the OS shows space-consuming scrollbars) regardless\nof whether a scrollbar is needed.</p>\n\n<p>This is useful if content height changes during application usage, but you do not want the calculated width\nof child items to change when a scrollbar appears or disappears. The scrollbar will appear in the reserved space,\nand the calculated width of child Components will not change.</p>\n\n<pre class='inline-example '><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Employee', {\n    extend: '<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>',\n    fields: [\n       {name: 'rating', type: 'int'},\n       {name: 'salary', type: 'float'},\n       {name: 'name'}\n    ]\n});\n\nfunction createFakeData(count) {\n    var firstNames   = ['Ed', 'Tommy', 'Aaron', 'Abe', 'Jamie', 'Adam', 'Dave', 'David', 'Jay', 'Nicolas', 'Nige'],\n        lastNames    = ['Spencer', 'Maintz', 'Conran', 'Elias', 'Avins', 'Mishcon', 'Kaneda', 'Davis', 'Robinson', 'Ferrero', 'White'],\n        ratings      = [1, 2, 3, 4, 5],\n        salaries     = [100, 400, 900, 1500, 1000000];\n\n    var data = [];\n    for (var i = 0; i &lt; (count || 25); i++) {\n        var ratingId    = Math.floor(Math.random() * ratings.length),\n            salaryId    = Math.floor(Math.random() * salaries.length),\n            firstNameId = Math.floor(Math.random() * firstNames.length),\n            lastNameId  = Math.floor(Math.random() * lastNames.length),\n\n            rating      = ratings[ratingId],\n            salary      = salaries[salaryId],\n            name        = <a href=\"#!/api/Ext.String-method-format\" rel=\"Ext.String-method-format\" class=\"docClass\">Ext.String.format</a>(\"{0} {1}\", firstNames[firstNameId], lastNames[lastNameId]);\n\n        data.push({\n            rating: rating,\n            salary: salary,\n            name: name\n        });\n    }\n    store.loadData(data);\n}\n\n// create the Data Store\nvar store = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a>', {\n    id: 'store',\n    model: 'Employee',\n    proxy: {\n        type: 'memory'\n    }\n});\ncreateFakeData(10);\n\nvar grid = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.grid.Panel\" rel=\"Ext.grid.Panel\" class=\"docClass\">Ext.grid.Panel</a>', {\n    title: 'Grid loaded with varying number of records',\n    anchor: '100%',\n    store: store,\n    columns: [{\n        xtype: 'rownumberer',\n        width: 40,\n        sortable: false\n    },{\n        text: 'Name',\n        flex: 1,\n        sortable: true,\n        dataIndex: 'name'\n    },{\n        text: 'Rating',\n        width: 125,\n        sortable: true,\n        dataIndex: 'rating'\n    },{\n        text: 'Salary',\n        width: 125,\n        sortable: true,\n        dataIndex: 'salary',\n        align: 'right',\n        renderer: <a href=\"#!/api/Ext.util.Format-method-usMoney\" rel=\"Ext.util.Format-method-usMoney\" class=\"docClass\">Ext.util.Format.usMoney</a>\n    }]\n});\n\n<a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>', {\n    renderTo: document.body,\n    width: 800,\n    height: 600,\n    layout: {\n        type: 'anchor',\n        reserveScrollbar: true // There will be a gap even when there's no scrollbar\n    },\n    autoScroll: true,\n    items: grid,\n    tbar: {\n        defaults: {\n            handler: function(b) {\n                createFakeData(b.count);\n            }\n        },\n        items: [{\n             text: '10 Items',\n             count: 10\n        },{\n             text: '100 Items',\n             count: 100\n        },{\n             text: '300 Items',\n             count: 300\n        },{\n             text: '1000 Items',\n             count: 1000\n        },{\n             text: '5000 Items',\n             count: 5000\n        }]\n    }\n});\n</code></pre>\n<p>Defaults to: <code>false</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-anchorFactory' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Anchor' rel='Ext.layout.container.Anchor' class='defined-in docClass'>Ext.layout.container.Anchor</a><br/><a href='source/Anchor.html#Ext-layout-container-Anchor-property-anchorFactory' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Anchor-property-anchorFactory' class='name not-expandable'>anchorFactory</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'><p>private</p>\n</div><div class='long'><p>private</p>\n</div></div></div><div id='property-animatePolicy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-property-animatePolicy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-property-animatePolicy' class='name expandable'>animatePolicy</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>An object which contains boolean properties specifying which properties are to be\nanimated upon flush of child Compon...</div><div class='long'><p>An object which contains boolean properties specifying which properties are to be\nanimated upon flush of child Component ContextItems. For example, Accordion would\nhave:</p>\n\n<pre><code> {\n     y: true,\n     height: true\n }\n</code></pre>\n</div></div></div><div id='property-autoSizePolicy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-property-autoSizePolicy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-property-autoSizePolicy' class='name expandable'>autoSizePolicy</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{readsWidth: 1, readsHeight: 1, setsWidth: 0, setsHeight: 0}</code></p></div></div></div><div id='property-childEls' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-property-childEls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-property-childEls' class='name expandable'>childEls</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>['outerCt', 'innerCt', 'clearEl']</code></p><p>Overrides: <a href='#!/api/Ext.layout.container.Container-property-childEls' rel='Ext.layout.container.Container-property-childEls' class='docClass'>Ext.layout.container.Container.childEls</a></p></div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-done' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-property-done' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-property-done' class='name expandable'>done</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Used only during a layout run, this value indicates that a\nlayout has finished its calculations. ...</div><div class='long'><p>Used only during a layout run, this value indicates that a\nlayout has finished its calculations. This flag is set to true prior to the call to\n<a href=\"#!/api/Ext.layout.Layout-method-calculate\" rel=\"Ext.layout.Layout-method-calculate\" class=\"docClass\">calculate</a> and should be set to false if this layout has more work to do.</p>\n</div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-initialized' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-property-initialized' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-property-initialized' class='name expandable'>initialized</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isLayout' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-property-isLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-property-isLayout' class='name expandable'>isLayout</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='readonly signature' >readonly</strong></div><div class='description'><div class='short'>true in this class to identify an object as an instantiated Layout, or subclass thereof. ...</div><div class='long'><p><code>true</code> in this class to identify an object as an instantiated Layout, or subclass thereof.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isShrinkWrapTpl' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-property-isShrinkWrapTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-property-isShrinkWrapTpl' class='name expandable'>isShrinkWrapTpl</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-lastOverflowAdjust' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-property-lastOverflowAdjust' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-property-lastOverflowAdjust' class='name expandable'>lastOverflowAdjust</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Begin with no previous adjustments ...</div><div class='long'><p>Begin with no previous adjustments</p>\n<p>Defaults to: <code>{width: 0, height: 0}</code></p></div></div></div><div id='property-manageOverflow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Anchor' rel='Ext.layout.container.Anchor' class='defined-in docClass'>Ext.layout.container.Anchor</a><br/><a href='source/Anchor.html#Ext-layout-container-Anchor-property-manageOverflow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Anchor-property-manageOverflow' class='name expandable'>manageOverflow</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true to rerun the layout if scrollbars are needed. ...</div><div class='long'><p>true to rerun the layout if scrollbars are needed.</p>\n<p>Defaults to: <code>true</code></p><p>Overrides: <a href='#!/api/Ext.layout.container.Auto-property-manageOverflow' rel='Ext.layout.container.Auto-property-manageOverflow' class='docClass'>Ext.layout.container.Auto.manageOverflow</a></p></div></div></div><div id='property-managePadding' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-property-managePadding' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-property-managePadding' class='name expandable'>managePadding</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>indicates that this layout will correct cross browser padding differences when the\ncontainer has overflow. ...</div><div class='long'><p>indicates that this layout will correct cross browser padding differences when the\ncontainer has overflow.</p>\n\n<p>In some browsers the right and/or bottom padding of a container is lost when\nthe container has overflow.  If managePadding is true the layout will apply the\npadding to an inner wrapping element instead of the container element that has the\noverflow so that paddding will be included in the scrollable area.\nNote: padding will not be managed if it is configured on the container using\na style config or css class.  In order to be managed, padding must be added to the\ncontainer using the appropriate <a href=\"#!/api/Ext.AbstractComponent-property-contentPaddingProperty\" rel=\"Ext.AbstractComponent-property-contentPaddingProperty\" class=\"docClass\">contentPaddingProperty</a>.  For <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Panels</a> use\n<a href=\"#!/api/Ext.panel.AbstractPanel-cfg-bodyPadding\" rel=\"Ext.panel.AbstractPanel-cfg-bodyPadding\" class=\"docClass\">Ext.panel.AbstractPanel.bodyPadding</a>, and for\n<a href=\"#!/api/Ext.container.Container\" rel=\"Ext.container.Container\" class=\"docClass\">Containers</a>, use\n<a href=\"#!/api/Ext.AbstractComponent-cfg-padding\" rel=\"Ext.AbstractComponent-cfg-padding\" class=\"docClass\">padding</a></p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-overflowPadderEl' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-property-overflowPadderEl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-property-overflowPadderEl' class='name not-expandable'>overflowPadderEl</a><span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span></div><div class='description'><div class='short'><p>The element used to correct body padding during overflow.</p>\n</div><div class='long'><p>The element used to correct body padding during overflow.</p>\n</div></div></div><div id='property-parseAnchorRE' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Anchor' rel='Ext.layout.container.Anchor' class='defined-in docClass'>Ext.layout.container.Anchor</a><br/><a href='source/Anchor.html#Ext-layout-container-Anchor-property-parseAnchorRE' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Anchor-property-parseAnchorRE' class='name expandable'>parseAnchorRE</a><span> : <a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>/^(r|right|b|bottom)$/i</code></p></div></div></div><div id='property-renderTpl' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-property-renderTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-property-renderTpl' class='name expandable'>renderTpl</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Auto layout's renderTpl wraps the content in an outerCt which is used to accomplish\nthe following 3 goals:\n\n\nWhen the...</div><div class='long'><p>Auto layout's renderTpl wraps the content in an outerCt which is used to accomplish\nthe following 3 goals:</p>\n\n<ol>\n<li>When the container has a shrink wrapped width and/or height, the outerCt is used\nto measure the size of the content.</li>\n<li>When the container has overflow some browsers lose the container's right and/or\nbottom padding.  To fix this, the padding is rendered to the outerCt instead of\nthe container target element.  This ensures that the padding is included in the\ncontainer's scrollWidth/scrollHeight. In Old IE when a table is used, the padding\nis rendered to the innerCt td element.</li>\n<li>The outerCt contains the margins of its children, that is to say, it prevents\nthem from collapsing.</li>\n</ol>\n\n<p>Overrides: <a href='#!/api/Ext.layout.container.Container-property-renderTpl' rel='Ext.layout.container.Container-property-renderTpl' class='docClass'>Ext.layout.container.Container.renderTpl</a></p></div></div></div><div id='property-running' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-property-running' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-property-running' class='name expandable'>running</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div><div id='property-sizePolicy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Anchor' rel='Ext.layout.container.Anchor' class='defined-in docClass'>Ext.layout.container.Anchor</a><br/><a href='source/Anchor.html#Ext-layout-container-Anchor-property-sizePolicy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Anchor-property-sizePolicy' class='name expandable'>sizePolicy</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{$: {readsWidth: 1, readsHeight: 1, setsWidth: 0, setsHeight: 0}, b: {readsWidth: 1, readsHeight: 0, setsWidth: 0, setsHeight: 1}, r: {$: {readsWidth: 0, readsHeight: 1, setsWidth: 1, setsHeight: 0}, b: {readsWidth: 0, readsHeight: 0, setsWidth: 1, setsHeight: 1}}}</code></p></div></div></div><div id='property-tableTpl' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-property-tableTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-property-tableTpl' class='name expandable'>tableTpl</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>This template is used for dynamically inserting a table outerCt/innerCt when needed. ...</div><div class='long'><p>This template is used for dynamically inserting a table outerCt/innerCt when needed.\nIt should be identical to the table template defined in renderTpl except that it\ndoes not have renderBody or clearEl.  It is an empty shell so that the contents\nof an already existing innerCt can be moved into it.</p>\n</div></div></div><div id='property-targetCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.Absolute'>Ext.layout.container.Absolute</span><br/><a href='source/Absolute.html#Ext-layout-container-Absolute-property-targetCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Absolute-property-targetCls' class='name expandable'>targetCls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>End Definitions ...</div><div class='long'><p>End Definitions</p>\n<p>Defaults to: <code>Ext.baseCSSPrefix + 'abs-layout-ct'</code></p></div></div></div><div id='property-type' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.Absolute'>Ext.layout.container.Absolute</span><br/><a href='source/Absolute.html#Ext-layout-container-Absolute-property-type' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Absolute-property-type' class='name expandable'>type</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'absolute'</code></p><p>Overrides: <a href='#!/api/Ext.layout.container.Anchor-property-type' rel='Ext.layout.container.Anchor-property-type' class='docClass'>Ext.layout.container.Anchor.type</a></p></div></div></div><div id='property-usesContainerHeight' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-property-usesContainerHeight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-property-usesContainerHeight' class='name expandable'>usesContainerHeight</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-usesContainerWidth' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-property-usesContainerWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-property-usesContainerWidth' class='name expandable'>usesContainerWidth</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-usesHeight' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-property-usesHeight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-property-usesHeight' class='name expandable'>usesHeight</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-usesWidth' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-property-usesWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-property-usesWidth' class='name expandable'>usesWidth</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-constructor' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/Ext.layout.container.Container-method-constructor' class='name expandable'>Ext.layout.container.Absolute</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.layout.container.Container\" rel=\"Ext.layout.container.Container\" class=\"docClass\">Ext.layout.container.Container</a></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.layout.container.Container\" rel=\"Ext.layout.container.Container\" class=\"docClass\">Ext.layout.container.Container</a></span><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.util.ElementContainer-method-constructor' rel='Ext.util.ElementContainer-method-constructor' class='docClass'>Ext.util.ElementContainer.constructor</a></p></div></div></div><div id='method-addChildEls' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.ElementContainer' rel='Ext.util.ElementContainer' class='defined-in docClass'>Ext.util.ElementContainer</a><br/><a href='source/ElementContainer.html#Ext-util-ElementContainer-method-addChildEls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.ElementContainer-method-addChildEls' class='name expandable'>addChildEls</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Adds each argument passed to this method to the childEls array. ...</div><div class='long'><p>Adds each argument passed to this method to the <a href=\"#!/api/Ext.AbstractComponent-cfg-childEls\" rel=\"Ext.AbstractComponent-cfg-childEls\" class=\"docClass\">childEls</a> array.</p>\n</div></div></div><div id='method-adjustHeightAnchor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.Absolute'>Ext.layout.container.Absolute</span><br/><a href='source/Absolute.html#Ext-layout-container-Absolute-method-adjustHeightAnchor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Absolute-method-adjustHeightAnchor' class='name expandable'>adjustHeightAnchor</a>( <span class='pre'>value, childContext</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>private ...</div><div class='long'><p>private</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>childContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.container.Anchor-method-adjustHeightAnchor' rel='Ext.layout.container.Anchor-method-adjustHeightAnchor' class='docClass'>Ext.layout.container.Anchor.adjustHeightAnchor</a></p></div></div></div><div id='method-adjustWidthAnchor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.Absolute'>Ext.layout.container.Absolute</span><br/><a href='source/Absolute.html#Ext-layout-container-Absolute-method-adjustWidthAnchor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Absolute-method-adjustWidthAnchor' class='name expandable'>adjustWidthAnchor</a>( <span class='pre'>value, childContext</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>private ...</div><div class='long'><p>private</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>childContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.container.Anchor-method-adjustWidthAnchor' rel='Ext.layout.container.Anchor-method-adjustWidthAnchor' class='docClass'>Ext.layout.container.Anchor.adjustWidthAnchor</a></p></div></div></div><div id='method-afterRemove' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-afterRemove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-afterRemove' class='name expandable'>afterRemove</a>( <span class='pre'>item</span> )<strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Removes layout's itemCls and owning Container's itemCls. ...</div><div class='long'><p>Removes layout's itemCls and owning Container's itemCls.\nClears the managed dimensions flags</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-afterRenderItem' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-afterRenderItem' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-afterRenderItem' class='name expandable'>afterRenderItem</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-applyChildEls' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.ElementContainer' rel='Ext.util.ElementContainer' class='defined-in docClass'>Ext.util.ElementContainer</a><br/><a href='source/ElementContainer.html#Ext-util-ElementContainer-method-applyChildEls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.ElementContainer-method-applyChildEls' class='name expandable'>applyChildEls</a>( <span class='pre'>el, id</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Sets references to elements inside the component. ...</div><div class='long'><p>Sets references to elements inside the component.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-beforeLayoutCycle' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-beforeLayoutCycle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-beforeLayoutCycle' class='name expandable'>beforeLayoutCycle</a>( <span class='pre'>ownerContext</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-beginCollapse' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-beginCollapse' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-beginCollapse' class='name expandable'>beginCollapse</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Called by an owning Panel before the Panel begins its collapse process. ...</div><div class='long'><p>Called by an owning Panel before the Panel begins its collapse process.\nMost layouts will not need to override the default <a href=\"#!/api/Ext-property-emptyFn\" rel=\"Ext-property-emptyFn\" class=\"docClass\">Ext.emptyFn</a> implementation.</p>\n</div></div></div><div id='method-beginExpand' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-beginExpand' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-beginExpand' class='name expandable'>beginExpand</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Called by an owning Panel before the Panel begins its expand process. ...</div><div class='long'><p>Called by an owning Panel before the Panel begins its expand process.\nMost layouts will not need to override the default <a href=\"#!/api/Ext-property-emptyFn\" rel=\"Ext-property-emptyFn\" class=\"docClass\">Ext.emptyFn</a> implementation.</p>\n</div></div></div><div id='method-beginLayout' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.Absolute'>Ext.layout.container.Absolute</span><br/><a href='source/Absolute.html#Ext-layout-container-Absolute-method-beginLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Absolute-method-beginLayout' class='name expandable'>beginLayout</a>( <span class='pre'>ownerContext</span> )</div><div class='description'><div class='short'>In addition to work done by our base classes, containers benefit from some extra\ncached data. ...</div><div class='long'><p>In addition to work done by our base classes, containers benefit from some extra\ncached data. The following properties are added to the ownerContext:</p>\n\n<ul>\n<li>visibleItems: the result of <a href=\"#!/api/Ext.layout.container.Absolute-method-getVisibleItems\" rel=\"Ext.layout.container.Absolute-method-getVisibleItems\" class=\"docClass\">getVisibleItems</a></li>\n<li>childItems: the ContextItem[] for each visible item</li>\n<li>targetContext: the ContextItem for the <a href=\"#!/api/Ext.layout.container.Absolute-method-getTarget\" rel=\"Ext.layout.container.Absolute-method-getTarget\" class=\"docClass\">getTarget</a> element</li>\n</ul>\n\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.container.Auto-method-beginLayout' rel='Ext.layout.container.Auto-method-beginLayout' class='docClass'>Ext.layout.container.Auto.beginLayout</a></p></div></div></div><div id='method-beginLayoutCycle' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Anchor' rel='Ext.layout.container.Anchor' class='defined-in docClass'>Ext.layout.container.Anchor</a><br/><a href='source/Anchor.html#Ext-layout-container-Anchor-method-beginLayoutCycle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Anchor-method-beginLayoutCycle' class='name expandable'>beginLayoutCycle</a>( <span class='pre'>ownerContext</span> )</div><div class='description'><div class='short'>Called before any calculation cycles to reset DOM values and prepare for calculation. ...</div><div class='long'><p>Called before any calculation cycles to reset DOM values and prepare for calculation.</p>\n\n<p>This is a write phase and DOM reads should be strictly avoided when overridding\nthis method.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'><p>The context item for the layout's owner\ncomponent.</p>\n\n\n\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.container.Auto-method-beginLayoutCycle' rel='Ext.layout.container.Auto-method-beginLayoutCycle' class='docClass'>Ext.layout.container.Auto.beginLayoutCycle</a></p></div></div></div><div id='method-cacheChildItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-cacheChildItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-cacheChildItems' class='name expandable'>cacheChildItems</a>( <span class='pre'>ownerContext</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-cacheElements' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-cacheElements' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-cacheElements' class='name expandable'>cacheElements</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-calculate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-calculate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-calculate' class='name expandable'>calculate</a>( <span class='pre'>ownerContext</span> )<strong class='abstract signature' >abstract</strong></div><div class='description'><div class='short'>Called to perform the calculations for this layout. ...</div><div class='long'><p>Called to perform the calculations for this layout. This method will be called at\nleast once and may be called repeatedly if the <a href=\"#!/api/Ext.layout.container.Auto-property-done\" rel=\"Ext.layout.container.Auto-property-done\" class=\"docClass\">done</a> property is cleared\nbefore return to indicate that this layout is not yet done. The <a href=\"#!/api/Ext.layout.container.Auto-property-done\" rel=\"Ext.layout.container.Auto-property-done\" class=\"docClass\">done</a> property\nis always set to <code>true</code> before entering this method.</p>\n\n<p>This is a read phase and DOM writes should be strictly avoided in derived classes.\nInstead, DOM writes need to be written to <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a> objects to\n be flushed at the next opportunity.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'><p>The context item for the layout's owner\ncomponent.</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-calculate' rel='Ext.layout.Layout-method-calculate' class='docClass'>Ext.layout.Layout.calculate</a></p></div></div></div><div id='method-calculateContentSize' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.Absolute'>Ext.layout.container.Absolute</span><br/><a href='source/Absolute.html#Ext-layout-container-Absolute-method-calculateContentSize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Absolute-method-calculateContentSize' class='name expandable'>calculateContentSize</a>( <span class='pre'>ownerContext, dimensions</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>dimensions</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.container.Auto-method-calculateContentSize' rel='Ext.layout.container.Auto-method-calculateContentSize' class='docClass'>Ext.layout.container.Auto.calculateContentSize</a></p></div></div></div><div id='method-calculateItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Anchor' rel='Ext.layout.container.Anchor' class='defined-in docClass'>Ext.layout.container.Anchor</a><br/><a href='source/Anchor.html#Ext-layout-container-Anchor-method-calculateItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Anchor-method-calculateItems' class='name expandable'>calculateItems</a>( <span class='pre'>ownerContext, containerSize</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>containerSize</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-calculateOverflow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-calculateOverflow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-calculateOverflow' class='name expandable'>calculateOverflow</a>( <span class='pre'>ownerContext</span> )<strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Handles overflow processing for a container. ...</div><div class='long'><p>Handles overflow processing for a container.  In addition to the ownerContext\npassed to the <a href=\"#!/api/Ext.layout.container.Auto-method-calculate\" rel=\"Ext.layout.container.Auto-method-calculate\" class=\"docClass\">calculate</a> method, this method also needs the containerSize\n(the object returned by <a href=\"#!/api/Ext.layout.container.Auto-method-getContainerSize\" rel=\"Ext.layout.container.Auto-method-getContainerSize\" class=\"docClass\">getContainerSize</a>).</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-callOverridden' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-completeLayout' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-completeLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-completeLayout' class='name expandable'>completeLayout</a>( <span class='pre'>ownerContext</span> )</div><div class='description'><div class='short'>This method (if implemented) is called at the end of the cycle in which this layout\ncompletes (by not setting done to...</div><div class='long'><p>This method (if implemented) is called at the end of the cycle in which this layout\ncompletes (by not setting <a href=\"#!/api/Ext.layout.container.Auto-property-done\" rel=\"Ext.layout.container.Auto-property-done\" class=\"docClass\">done</a> to <code>false</code> in <a href=\"#!/api/Ext.layout.container.Auto-method-calculate\" rel=\"Ext.layout.container.Auto-method-calculate\" class=\"docClass\">calculate</a>). It is\npossible for the layout to complete and yet become invalid before the end of the cycle,\nin which case, this method will not be called. It is also possible for this method to\nbe called and then later the layout becomes invalidated. This will result in\n<a href=\"#!/api/Ext.layout.container.Auto-method-calculate\" rel=\"Ext.layout.container.Auto-method-calculate\" class=\"docClass\">calculate</a> being called again, followed by another call to this method.</p>\n\n<p>This is a read phase and DOM writes should be strictly avoided in derived classes.\nInstead, DOM writes need to be written to <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a> objects to\nbe flushed at the next opportunity.</p>\n\n<p>This method need not be implemented by derived classes and, in fact, should only be\nimplemented when needed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'><p>The context item for the layout's owner\ncomponent.</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-completeLayout' rel='Ext.layout.Layout-method-completeLayout' class='docClass'>Ext.layout.Layout.completeLayout</a></p></div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-configureItem' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Anchor' rel='Ext.layout.container.Anchor' class='defined-in docClass'>Ext.layout.container.Anchor</a><br/><a href='source/Anchor.html#Ext-layout-container-Anchor-method-configureItem' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Anchor-method-configureItem' class='name expandable'>configureItem</a>( <span class='pre'>item</span> )<strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Adds layout's itemCls and owning Container's itemCls ...</div><div class='long'><p>Adds layout's itemCls and owning Container's itemCls</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.container.Container-method-configureItem' rel='Ext.layout.container.Container-method-configureItem' class='docClass'>Ext.layout.container.Container.configureItem</a></p></div></div></div><div id='method-destroy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-destroy' class='name expandable'>destroy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.util.ElementContainer-method-destroy' rel='Ext.util.ElementContainer-method-destroy' class='docClass'>Ext.util.ElementContainer.destroy</a></p></div></div></div><div id='method-doRenderBody' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-doRenderBody' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-doRenderBody' class='name expandable'>doRenderBody</a>( <span class='pre'>out, renderData</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>out</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>renderData</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-doRenderContainer' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-doRenderContainer' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-doRenderContainer' class='name expandable'>doRenderContainer</a>( <span class='pre'>out, renderData</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>out</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>renderData</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-doRenderItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-doRenderItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-doRenderItems' class='name expandable'>doRenderItems</a>( <span class='pre'>out, renderData</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>out</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>renderData</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-doRenderPadding' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-doRenderPadding' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-doRenderPadding' class='name expandable'>doRenderPadding</a>( <span class='pre'>out, renderData</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>out</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>renderData</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-finalizeLayout' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-finalizeLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-finalizeLayout' class='name expandable'>finalizeLayout</a>( <span class='pre'>ownerContext</span> )</div><div class='description'><div class='short'>This method (if implemented) is called after all layouts have completed. ...</div><div class='long'><p>This method (if implemented) is called after all layouts have completed. In most\nways this is similar to <a href=\"#!/api/Ext.layout.Layout-method-completeLayout\" rel=\"Ext.layout.Layout-method-completeLayout\" class=\"docClass\">completeLayout</a>. This call can cause this (or any\nlayout) to be become invalid (see <a href=\"#!/api/Ext.layout.Context-method-invalidate\" rel=\"Ext.layout.Context-method-invalidate\" class=\"docClass\">Ext.layout.Context.invalidate</a>), but this\nis best avoided. This method is intended to be where final reads are made and so it\nis best to avoid invalidating layouts at this point whenever possible. Even so, this\nmethod can be used to perform final checks that may require all other layouts to be\ncomplete and then invalidate some results.</p>\n\n<p>This is a read phase and DOM writes should be strictly avoided in derived classes.\nInstead, DOM writes need to be written to <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a> objects to\nbe flushed at the next opportunity.</p>\n\n<p>This method need not be implemented by derived classes and, in fact, should only be\nimplemented when needed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'><p>The context item for the layout's owner\ncomponent.</p>\n</div></li></ul></div></div></div><div id='method-finishRender' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-finishRender' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-finishRender' class='name expandable'>finishRender</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.layout.Layout-method-finishRender' rel='Ext.layout.Layout-method-finishRender' class='docClass'>Ext.layout.Layout.finishRender</a></p></div></div></div><div id='method-finishRenderItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-finishRenderItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-finishRenderItems' class='name expandable'>finishRenderItems</a>( <span class='pre'>target, items</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>items</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-finishedLayout' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-finishedLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-finishedLayout' class='name expandable'>finishedLayout</a>( <span class='pre'>ownerContext</span> )</div><div class='description'><div class='short'>This method is called after all layouts are complete and their calculations flushed\nto the DOM. ...</div><div class='long'><p>This method is called after all layouts are complete and their calculations flushed\nto the DOM. No further layouts will be run and this method is only called once per\nlayout run. The base component layout caches <code>lastComponentSize</code>.</p>\n\n<p>This is a write phase and DOM reads should be avoided if possible when overridding\nthis method.</p>\n\n<p>This method need not be implemented by derived classes and, in fact, should only be\nimplemented when needed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'><p>The context item for the layout's owner\ncomponent.</p>\n\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-finishedLayout' rel='Ext.layout.Layout-method-finishedLayout' class='docClass'>Ext.layout.Layout.finishedLayout</a></p></div></div></div><div id='method-getAnimatePolicy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-getAnimatePolicy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-getAnimatePolicy' class='name expandable'>getAnimatePolicy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getChildEls' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.ElementContainer' rel='Ext.util.ElementContainer' class='defined-in docClass'>Ext.util.ElementContainer</a><br/><a href='source/ElementContainer.html#Ext-util-ElementContainer-method-getChildEls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.ElementContainer-method-getChildEls' class='name expandable'>getChildEls</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getClassChildEls' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.ElementContainer' rel='Ext.util.ElementContainer' class='defined-in docClass'>Ext.util.ElementContainer</a><br/><a href='source/ElementContainer.html#Ext-util-ElementContainer-method-getClassChildEls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.ElementContainer-method-getClassChildEls' class='name expandable'>getClassChildEls</a>( <span class='pre'>cls</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>cls</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getContainerSize' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-getContainerSize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-getContainerSize' class='name expandable'>getContainerSize</a>( <span class='pre'>ownerContext, [inDom]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Returns the container size (that of the target). ...</div><div class='long'><p>Returns the container size (that of the target). Only the fixed-sized dimensions can\nbe returned because the shrinkWrap dimensions are based on the contentWidth/Height\nas determined by the container layout.</p>\n\n<p>If the <a href=\"#!/api/Ext.layout.container.Auto-method-calculateOverflow\" rel=\"Ext.layout.container.Auto-method-calculateOverflow\" class=\"docClass\">calculateOverflow</a> method is used and if <a href=\"#!/api/Ext.layout.container.Auto-property-manageOverflow\" rel=\"Ext.layout.container.Auto-property-manageOverflow\" class=\"docClass\">manageOverflow</a> is\ntrue, this will adjust the width/height by the size of scrollbars.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'><p>The owner's context item.</p>\n</div></li><li><span class='pre'>inDom</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True if the container size must be in the DOM.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The size</p>\n<ul><li><span class='pre'>width</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The width</p>\n</div></li><li><span class='pre'>height</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The height</p>\n</div></li></ul></div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.container.Container-method-getContainerSize' rel='Ext.layout.container.Container-method-getContainerSize' class='docClass'>Ext.layout.container.Container.getContainerSize</a></p></div></div></div><div id='method-getContentTarget' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-getContentTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-getContentTarget' class='name expandable'>getContentTarget</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.layout.container.Container-method-getContentTarget' rel='Ext.layout.container.Container-method-getContentTarget' class='docClass'>Ext.layout.container.Container.getContentTarget</a></p></div></div></div><div id='method-getElementTarget' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-getElementTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-getElementTarget' class='name expandable'>getElementTarget</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></div><div class='description'><div class='short'>Overridden method from Ext.layout.container.Container. ...</div><div class='long'><p>Overridden method from <a href=\"#!/api/Ext.layout.container.Container\" rel=\"Ext.layout.container.Container\" class=\"docClass\">Ext.layout.container.Container</a>.\nUsed by Container classes to insert special DOM elements which must exist in addition to the child components</p>\n\n<p>Returns the element into which extra functional DOM elements can be inserted. Defaults to the owner Component's encapsulating element.</p>\n\n<p>May be overridden in Component layout managers which implement a <a href=\"#!/api/Ext.layout.container.Auto-method-getRenderTarget\" rel=\"Ext.layout.container.Auto-method-getRenderTarget\" class=\"docClass\">component render target</a> which must only\ncontain child components.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.container.Container-method-getElementTarget' rel='Ext.layout.container.Container-method-getElementTarget' class='docClass'>Ext.layout.container.Container.getElementTarget</a></p></div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-getItemSizePolicy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Anchor' rel='Ext.layout.container.Anchor' class='defined-in docClass'>Ext.layout.container.Anchor</a><br/><a href='source/Anchor.html#Ext-layout-container-Anchor-method-getItemSizePolicy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Anchor-method-getItemSizePolicy' class='name expandable'>getItemSizePolicy</a>( <span class='pre'>item</span> ) : <a href=\"#!/api/Ext.layout.SizePolicy\" rel=\"Ext.layout.SizePolicy\" class=\"docClass\">Ext.layout.SizePolicy</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Returns an object describing how this layout manages the size of the given component. ...</div><div class='long'><p>Returns an object describing how this layout manages the size of the given component.\nThis method must be implemented by any layout that manages components.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.layout.SizePolicy\" rel=\"Ext.layout.SizePolicy\" class=\"docClass\">Ext.layout.SizePolicy</a></span><div class='sub-desc'><p>An object describing the sizing done by the layout\nfor this item.</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-getItemSizePolicy' rel='Ext.layout.Layout-method-getItemSizePolicy' class='docClass'>Ext.layout.Layout.getItemSizePolicy</a></p></div></div></div><div id='method-getItemsRenderTree' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-getItemsRenderTree' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-getItemsRenderTree' class='name expandable'>getItemsRenderTree</a>( <span class='pre'>items, renderCfgs</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'><hr />\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>items</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>renderCfgs</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getLayoutItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getLayoutItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getLayoutItems' class='name expandable'>getLayoutItems</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>[]</div><div class='description'><div class='short'>Returns an array of child components either for a render phase (Performed in the beforeLayout\nmethod of the layout's ...</div><div class='long'><p>Returns an array of child components either for a render phase (Performed in the beforeLayout\nmethod of the layout's base class), or the layout phase (onLayout).</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>[]</span><div class='sub-desc'><p>of child components</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-getLayoutItems' rel='Ext.layout.Layout-method-getLayoutItems' class='docClass'>Ext.layout.Layout.getLayoutItems</a></p></div></div></div><div id='method-getOverflowXStyle' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-getOverflowXStyle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-getOverflowXStyle' class='name expandable'>getOverflowXStyle</a>( <span class='pre'>ownerContext</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Returns the overflow-x style of the render target. ...</div><div class='long'><p>Returns the overflow-x style of the render target.\nNote: If overflow is configured on a container using style or css class this method\nwill read the dom the first time it is called. It is therefore preferable for\nperformance reasons to use the autoScroll or overflowX config when horizontal\noverflow is desired.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getOverflowYStyle' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-getOverflowYStyle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-getOverflowYStyle' class='name expandable'>getOverflowYStyle</a>( <span class='pre'>ownerContext</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Returns the overflow-y style of the render target. ...</div><div class='long'><p>Returns the overflow-y style of the render target.\nNote: If overflow is configured on a container using style or css class this method\nwill read the dom the first time it is called. It is therefore preferable for\nperformance reasons to use the autoScroll or overflowY config when vertical\noverflow is desired.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getPositionOffset' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getPositionOffset' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getPositionOffset' class='name expandable'>getPositionOffset</a>( <span class='pre'>position</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>This method is used to offset the DOM position when checking\nwhether the element is a certain child of the target. ...</div><div class='long'><p>This method is used to offset the DOM position when checking\nwhether the element is a certain child of the target. This is\nrequired in cases where the extra elements prepended to the target\nbefore any of the items. An example of this is when using labelAlign: 'top'\non a field. The label appears first in the DOM before any child items are\ncreated, so when we check the position we need to add an extra offset.\nContainers that create an innerCt are exempt because this new element\npreserves the order</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>position</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-getPositionOffset' rel='Ext.layout.Layout-method-getPositionOffset' class='docClass'>Ext.layout.Layout.getPositionOffset</a></p></div></div></div><div id='method-getRenderData' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-getRenderData' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-getRenderData' class='name expandable'>getRenderData</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.layout.container.Container-method-getRenderData' rel='Ext.layout.container.Container-method-getRenderData' class='docClass'>Ext.layout.container.Container.getRenderData</a></p></div></div></div><div id='method-getRenderTarget' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-getRenderTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-getRenderTarget' class='name expandable'>getRenderTarget</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></div><div class='description'><div class='short'>Overridden method from Ext.layout.container.Container. ...</div><div class='long'><p>Overridden method from <a href=\"#!/api/Ext.layout.container.Container\" rel=\"Ext.layout.container.Container\" class=\"docClass\">Ext.layout.container.Container</a>.\nUsed in the beforeLayout method to render all items into.</p>\n\n<p>Returns the element into which rendering must take place. Defaults to the owner Container's\ntarget element.</p>\n\n<p>May be overridden in layout managers which implement an inner element.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.container.Container-method-getRenderTarget' rel='Ext.layout.container.Container-method-getRenderTarget' class='docClass'>Ext.layout.container.Container.getRenderTarget</a></p></div></div></div><div id='method-getRenderTpl' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getRenderTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getRenderTpl' class='name expandable'>getRenderTpl</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getRenderTree' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getRenderTree' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getRenderTree' class='name expandable'>getRenderTree</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getRenderedItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getRenderedItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getRenderedItems' class='name expandable'>getRenderedItems</a>( <span class='pre'></span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Returns all items that are rendered ...</div><div class='long'><p>Returns all items that are rendered</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>All matching items</p>\n</div></li></ul></div></div></div><div id='method-getScrollbarsNeeded' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getScrollbarsNeeded' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getScrollbarsNeeded' class='name expandable'>getScrollbarsNeeded</a>( <span class='pre'>width, height, contentWidth, contentHeight</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>width</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>height</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>contentWidth</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>contentHeight</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getTarget' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getTarget' class='name expandable'>getTarget</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></div><div class='description'><div class='short'>Returns the owner component's resize element. ...</div><div class='long'><p>Returns the owner component's resize element.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getVisibleItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getVisibleItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getVisibleItems' class='name expandable'>getVisibleItems</a>( <span class='pre'></span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Returns all items that are both rendered and visible ...</div><div class='long'><p>Returns all items that are both rendered and visible</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>All matching items</p>\n</div></li></ul></div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-initContextItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-initContextItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-initContextItems' class='name expandable'>initContextItems</a>( <span class='pre'>ownerContext</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-initLayout' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-initLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-initLayout' class='name expandable'>initLayout</a>( <span class='pre'></span> )<strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>A one-time initialization method called just before rendering. ...</div><div class='long'><p>A one-time initialization method called just before rendering.</p>\n<p>Overrides: <a href='#!/api/Ext.layout.Layout-method-initLayout' rel='Ext.layout.Layout-method-initLayout' class='docClass'>Ext.layout.Layout.initLayout</a></p></div></div></div><div id='method-insertTableCt' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-insertTableCt' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-insertTableCt' class='name expandable'>insertTableCt</a>( <span class='pre'>ownerContext</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>In some cases a table-based outerCt/innerCt is required in old IE (see renderTpl). ...</div><div class='long'><p>In some cases a table-based outerCt/innerCt is required in old IE (see renderTpl).\nMost of the time this is determined at render time, however its possible that\nwe made the wrong determination at render time and now that the layout is in\nprogress we need a table.  If so, this method should be called to replace the\nexisting outerCt with a new table outerCt, and move the child elements to the new\ninnerCt.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isItemBoxParent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.Absolute'>Ext.layout.container.Absolute</span><br/><a href='source/Absolute.html#Ext-layout-container-Absolute-method-isItemBoxParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Absolute-method-isItemBoxParent' class='name expandable'>isItemBoxParent</a>( <span class='pre'>itemContext</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>itemContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-isItemBoxParent' rel='Ext.layout.Layout-method-isItemBoxParent' class='docClass'>Ext.layout.Layout.isItemBoxParent</a></p></div></div></div><div id='method-isItemLayoutRoot' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.Absolute'>Ext.layout.container.Absolute</span><br/><a href='source/Absolute.html#Ext-layout-container-Absolute-method-isItemLayoutRoot' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Absolute-method-isItemLayoutRoot' class='name expandable'>isItemLayoutRoot</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-isItemLayoutRoot' rel='Ext.layout.Layout-method-isItemLayoutRoot' class='docClass'>Ext.layout.Layout.isItemLayoutRoot</a></p></div></div></div><div id='method-isItemShrinkWrap' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.Absolute'>Ext.layout.container.Absolute</span><br/><a href='source/Absolute.html#Ext-layout-container-Absolute-method-isItemShrinkWrap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Absolute-method-isItemShrinkWrap' class='name expandable'>isItemShrinkWrap</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-isItemShrinkWrap' rel='Ext.layout.Layout-method-isItemShrinkWrap' class='docClass'>Ext.layout.Layout.isItemShrinkWrap</a></p></div></div></div><div id='method-isRunning' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-isRunning' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-isRunning' class='name expandable'>isRunning</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-isValidParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-isValidParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-isValidParent' class='name expandable'>isValidParent</a>( <span class='pre'>item, target, position</span> )<strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Validates item is in the proper place in the dom. ...</div><div class='long'><p>Validates item is in the proper place in the dom.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>position</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-measureContentHeight' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-measureContentHeight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-measureContentHeight' class='name expandable'>measureContentHeight</a>( <span class='pre'>ownerContext</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-measureContentWidth' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-measureContentWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-measureContentWidth' class='name expandable'>measureContentWidth</a>( <span class='pre'>ownerContext</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-moveItem' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-moveItem' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-moveItem' class='name expandable'>moveItem</a>( <span class='pre'>item, target, position</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Moves Component to the provided target instead. ...</div><div class='long'><p>Moves Component to the provided target instead.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>position</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-notifyOwner' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-notifyOwner' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-notifyOwner' class='name expandable'>notifyOwner</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Called for every layout in the layout context after all the layouts have been finally flushed ...</div><div class='long'><p>Called for every layout in the layout context after all the layouts have been finally flushed</p>\n<p>Overrides: <a href='#!/api/Ext.layout.Layout-method-notifyOwner' rel='Ext.layout.Layout-method-notifyOwner' class='docClass'>Ext.layout.Layout.notifyOwner</a></p></div></div></div><div id='method-onAdd' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-onAdd' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-onAdd' class='name expandable'>onAdd</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onContentChange' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.Absolute'>Ext.layout.container.Absolute</span><br/><a href='source/Absolute.html#Ext-layout-container-Absolute-method-onContentChange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Absolute-method-onContentChange' class='name expandable'>onContentChange</a>( <span class='pre'>child</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>This method is called when a child item changes in some way. ...</div><div class='long'><p>This method is called when a child item changes in some way. By default this calls\n<a href=\"#!/api/Ext.AbstractComponent-method-updateLayout\" rel=\"Ext.AbstractComponent-method-updateLayout\" class=\"docClass\">Ext.AbstractComponent.updateLayout</a> on this layout's owner.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>child</span> : <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a><div class='sub-desc'><p>The child item that has changed.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if this layout has handled the content change.</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-onContentChange' rel='Ext.layout.Layout-method-onContentChange' class='docClass'>Ext.layout.Layout.onContentChange</a></p></div></div></div><div id='method-onDestroy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-onDestroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-onDestroy' class='name expandable'>onDestroy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-onRemove' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-onRemove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-onRemove' class='name expandable'>onRemove</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-parseAnchor' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Anchor' rel='Ext.layout.container.Anchor' class='defined-in docClass'>Ext.layout.container.Anchor</a><br/><a href='source/Anchor.html#Ext-layout-container-Anchor-method-parseAnchor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Anchor-method-parseAnchor' class='name expandable'>parseAnchor</a>( <span class='pre'>a, start, cstart</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>a</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>start</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>cstart</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-prune' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.ElementContainer' rel='Ext.util.ElementContainer' class='defined-in docClass'>Ext.util.ElementContainer</a><br/><a href='source/ElementContainer.html#Ext-util-ElementContainer-method-prune' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.ElementContainer-method-prune' class='name expandable'>prune</a>( <span class='pre'>childEls, shared</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>childEls</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>shared</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-removeChildEls' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.ElementContainer' rel='Ext.util.ElementContainer' class='defined-in docClass'>Ext.util.ElementContainer</a><br/><a href='source/ElementContainer.html#Ext-util-ElementContainer-method-removeChildEls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.ElementContainer-method-removeChildEls' class='name expandable'>removeChildEls</a>( <span class='pre'>testFn</span> )</div><div class='description'><div class='short'>Removes items in the childEls array based on the return value of a supplied test\nfunction. ...</div><div class='long'><p>Removes items in the childEls array based on the return value of a supplied test\nfunction. The function is called with a entry in childEls and if the test function\nreturn true, that entry is removed. If false, that entry is kept.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>testFn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The test function.</p>\n</div></li></ul></div></div></div><div id='method-renderChildren' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-renderChildren' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-renderChildren' class='name expandable'>renderChildren</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.layout.Layout-method-renderChildren' rel='Ext.layout.Layout-method-renderChildren' class='docClass'>Ext.layout.Layout.renderChildren</a></p></div></div></div><div id='method-renderItem' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-renderItem' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-renderItem' class='name expandable'>renderItem</a>( <span class='pre'>item, target, position</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Renders the given Component into the target Element. ...</div><div class='long'><p>Renders the given Component into the target Element.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a><div class='sub-desc'><p>The Component to render</p>\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a><div class='sub-desc'><p>The target Element</p>\n</div></li><li><span class='pre'>position</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The position within the target to render the item to</p>\n</div></li></ul></div></div></div><div id='method-renderItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-renderItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-renderItems' class='name expandable'>renderItems</a>( <span class='pre'>items, target</span> )<strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Iterates over all passed items, ensuring they are rendered. ...</div><div class='long'><p>Iterates over all passed items, ensuring they are rendered.  If the items are already rendered,\nalso determines if the items are in the proper place in the dom.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>items</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-sanityCheck' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Anchor' rel='Ext.layout.container.Anchor' class='defined-in docClass'>Ext.layout.container.Anchor</a><br/><a href='source/Anchor.html#Ext-layout-container-Anchor-method-sanityCheck' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Anchor-method-sanityCheck' class='name expandable'>sanityCheck</a>( <span class='pre'>ownerContext</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'><p><debug></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setCtSizeIfNeeded' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-setCtSizeIfNeeded' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-setCtSizeIfNeeded' class='name expandable'>setCtSizeIfNeeded</a>( <span class='pre'>ownerContext, containerSize</span> )<strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method sets the height and/or width of the outerCt/innerCt to adjust for the\nfollowing browser-specific issues:\n...</div><div class='long'><p>This method sets the height and/or width of the outerCt/innerCt to adjust for the\nfollowing browser-specific issues:</p>\n\n<ol>\n<li><p>In IE6 and 7 strict if we are using the shrink wrap template, and the outerCt\nhas a 100% width (because the container is not shrink wrapping width currently),\nand the target element has a vertical scrollbar, the browser disregards the\nscrollbar when sizing the width of the outerCt.  This can result in the target\nelement gaining a horizontal scrollbar.  We fix this issue by setting a pixel\nwidth on the outerCt</p></li>\n<li><p>In IE quirks when using the \"non shrink wrap\" template, a long non-breaking word\ncan cause the outerCt's width to expand beyond the width of its container. This\nbehavior is desired if the container has the potential for horizontal overflow,\nbut can cause text to be hidden if the container's overflow is hidden. To prevent\nthis from happening we give the outerCt a fixed width in IE quirks when the\ncontainer does not have horizontal overflow.</p></li>\n<li><p>In some browsers a percentage-height element ignores the horizontal scrollbar\nof its parent (see <a href=\"#!/api/Ext.supports-property-PercentageHeightOverflowBug\" rel=\"Ext.supports-property-PercentageHeightOverflowBug\" class=\"docClass\">Ext.supports.PercentageHeightOverflowBug</a>).  If the browser is\naffected by this bug the outerCt needs a pixel height in order to support\npercentage-height children when not shrink-wrapping height. If the browser is not\naffected by this bug, a height of 100% is assigned to the outerCt (see\nbeginLayoutCycle).</p></li>\n<li><p>In IE6/7 strict when using the \"shrink wrap\" template, percentage heights on\nchildren do not work unless the innerCt td has a height set.  We can't use height\n100% on the innerCt because conent-box sizing will cause any top/bottom padding to\nbe added to the height.  The solution is to set a pixel height on the innerCt.</p></li>\n<li><p>IE8 strict mode has a bug with percentage height children.  if the innerCt has\na height of 100%, has padding, and has a child item with a percentage height, that\nchild item will be sized as a percentage of the parent's height plus padding height.\nIn other words, a child with height:50% would have its height caclulated thusly:\n(parentHeight + parentPaddingHeight) * 0.5\nTo fix this, we have to give the innerCt a pixel height.</p></li>\n<li><p>In IE7 strict if we're using the \"non shrink wrap\" template, and the target\nelement has overflow-y:auto, the outerCt reserves space for the target element's\nvertical scrollbar even when there is no vertical scrollbar.  This is fixed by\nsetting the targetEl's overflow property to \"hidden\" and then back to \"auto\".</p></li>\n</ol>\n\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'>\n</div></li><li><span class='pre'>containerSize</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setOwner' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-setOwner' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-setOwner' class='name expandable'>setOwner</a>( <span class='pre'>owner</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Sets the layout owner ...</div><div class='long'><p>Sets the layout owner</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>owner</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setupRenderTpl' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Auto' rel='Ext.layout.container.Auto' class='defined-in docClass'>Ext.layout.container.Auto</a><br/><a href='source/Auto2.html#Ext-layout-container-Auto-method-setupRenderTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Auto-method-setupRenderTpl' class='name expandable'>setupRenderTpl</a>( <span class='pre'>renderTpl</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>renderTpl</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.container.Container-method-setupRenderTpl' rel='Ext.layout.container.Container-method-setupRenderTpl' class='docClass'>Ext.layout.container.Container.setupRenderTpl</a></p></div></div></div><div id='method-sortWeightedItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-sortWeightedItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-sortWeightedItems' class='name expandable'>sortWeightedItems</a>( <span class='pre'>items, reverseProp</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>items</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>reverseProp</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-undoLayout' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-undoLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-undoLayout' class='name expandable'>undoLayout</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-create' class='name expandable'>create</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create a new instance of this Class. ...</div><div class='long'><p>Create a new instance of this Class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.create({\n    someConfig: true\n});\n</code></pre>\n\n<p>All parameters are passed to the constructor of the class.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the created instance.</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div></div></div>","superclasses":["Ext.Base","Ext.layout.Layout","Ext.layout.container.Container","Ext.layout.container.Auto","Ext.layout.container.Anchor"],"meta":{},"code_type":"ext_define","requires":[],"html_meta":{},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"Absolute.html#Ext-layout-container-Absolute","filename":"Absolute.js"}],"linenr":1,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.layout.container.Anchor","meta":{"private":true},"name":"anchorFactory","id":"property-anchorFactory"},{"tagname":"property","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"animatePolicy","id":"property-animatePolicy"},{"tagname":"property","owner":"Ext.layout.Layout","meta":{"private":true},"name":"autoSizePolicy","id":"property-autoSizePolicy"},{"tagname":"property","owner":"Ext.layout.container.Auto","meta":{"private":true},"name":"childEls","id":"property-childEls"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.layout.Layout","meta":{},"name":"done","id":"property-done"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.layout.Layout","meta":{"private":true},"name":"initialized","id":"property-initialized"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.layout.Layout","meta":{"readonly":true},"name":"isLayout","id":"property-isLayout"},{"tagname":"property","owner":"Ext.layout.container.Auto","meta":{"private":true},"name":"isShrinkWrapTpl","id":"property-isShrinkWrapTpl"},{"tagname":"property","owner":"Ext.layout.container.Auto","meta":{"private":true},"name":"lastOverflowAdjust","id":"property-lastOverflowAdjust"},{"tagname":"property","owner":"Ext.layout.container.Anchor","meta":{},"name":"manageOverflow","id":"property-manageOverflow"},{"tagname":"property","owner":"Ext.layout.container.Auto","meta":{},"name":"managePadding","id":"property-managePadding"},{"tagname":"property","owner":"Ext.layout.container.Container","meta":{},"name":"overflowPadderEl","id":"property-overflowPadderEl"},{"tagname":"property","owner":"Ext.layout.container.Anchor","meta":{"private":true},"name":"parseAnchorRE","id":"property-parseAnchorRE"},{"tagname":"property","owner":"Ext.layout.container.Auto","meta":{"private":true},"name":"renderTpl","id":"property-renderTpl"},{"tagname":"property","owner":"Ext.layout.Layout","meta":{"private":true},"name":"running","id":"property-running"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"},{"tagname":"property","owner":"Ext.layout.container.Anchor","meta":{"private":true},"name":"sizePolicy","id":"property-sizePolicy"},{"tagname":"property","owner":"Ext.layout.container.Auto","meta":{"private":true},"name":"tableTpl","id":"property-tableTpl"},{"tagname":"property","owner":"Ext.layout.container.Absolute","meta":{"private":true},"name":"targetCls","id":"property-targetCls"},{"tagname":"property","owner":"Ext.layout.container.Absolute","meta":{"private":true},"name":"type","id":"property-type"},{"tagname":"property","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"usesContainerHeight","id":"property-usesContainerHeight"},{"tagname":"property","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"usesContainerWidth","id":"property-usesContainerWidth"},{"tagname":"property","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"usesHeight","id":"property-usesHeight"},{"tagname":"property","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"usesWidth","id":"property-usesWidth"}],"cfg":[{"tagname":"cfg","owner":"Ext.layout.container.Anchor","meta":{},"name":"anchor","id":"cfg-anchor"},{"tagname":"cfg","owner":"Ext.layout.container.Anchor","meta":{},"name":"defaultAnchor","id":"cfg-defaultAnchor"},{"tagname":"cfg","owner":"Ext.layout.container.Absolute","meta":{},"name":"ignoreOnContentChange","id":"cfg-ignoreOnContentChange"},{"tagname":"cfg","owner":"Ext.layout.container.Absolute","meta":{},"name":"itemCls","id":"cfg-itemCls"},{"tagname":"cfg","owner":"Ext.layout.container.Auto","meta":{},"name":"reserveScrollbar","id":"cfg-reserveScrollbar"}],"css_var":[],"method":[{"tagname":"method","owner":"Ext.layout.container.Container","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"Ext.util.ElementContainer","meta":{},"name":"addChildEls","id":"method-addChildEls"},{"tagname":"method","owner":"Ext.layout.container.Absolute","meta":{"private":true},"name":"adjustHeightAnchor","id":"method-adjustHeightAnchor"},{"tagname":"method","owner":"Ext.layout.container.Absolute","meta":{"private":true},"name":"adjustWidthAnchor","id":"method-adjustWidthAnchor"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"protected":true},"name":"afterRemove","id":"method-afterRemove"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"afterRenderItem","id":"method-afterRenderItem"},{"tagname":"method","owner":"Ext.util.ElementContainer","meta":{"private":true},"name":"applyChildEls","id":"method-applyChildEls"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{"private":true},"name":"beforeLayoutCycle","id":"method-beforeLayoutCycle"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"beginCollapse","id":"method-beginCollapse"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"beginExpand","id":"method-beginExpand"},{"tagname":"method","owner":"Ext.layout.container.Absolute","meta":{},"name":"beginLayout","id":"method-beginLayout"},{"tagname":"method","owner":"Ext.layout.container.Anchor","meta":{},"name":"beginLayoutCycle","id":"method-beginLayoutCycle"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"cacheChildItems","id":"method-cacheChildItems"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"cacheElements","id":"method-cacheElements"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{"abstract":true},"name":"calculate","id":"method-calculate"},{"tagname":"method","owner":"Ext.layout.container.Absolute","meta":{"private":true},"name":"calculateContentSize","id":"method-calculateContentSize"},{"tagname":"method","owner":"Ext.layout.container.Anchor","meta":{"private":true},"name":"calculateItems","id":"method-calculateItems"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{"protected":true},"name":"calculateOverflow","id":"method-calculateOverflow"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{},"name":"completeLayout","id":"method-completeLayout"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.layout.container.Anchor","meta":{"protected":true},"name":"configureItem","id":"method-configureItem"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"doRenderBody","id":"method-doRenderBody"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"doRenderContainer","id":"method-doRenderContainer"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"doRenderItems","id":"method-doRenderItems"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{"private":true},"name":"doRenderPadding","id":"method-doRenderPadding"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{},"name":"finalizeLayout","id":"method-finalizeLayout"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"finishRender","id":"method-finishRender"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"finishRenderItems","id":"method-finishRenderItems"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{},"name":"finishedLayout","id":"method-finishedLayout"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"getAnimatePolicy","id":"method-getAnimatePolicy"},{"tagname":"method","owner":"Ext.util.ElementContainer","meta":{"private":true},"name":"getChildEls","id":"method-getChildEls"},{"tagname":"method","owner":"Ext.util.ElementContainer","meta":{"private":true},"name":"getClassChildEls","id":"method-getClassChildEls"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{"protected":true},"name":"getContainerSize","id":"method-getContainerSize"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{"private":true},"name":"getContentTarget","id":"method-getContentTarget"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{},"name":"getElementTarget","id":"method-getElementTarget"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.layout.container.Anchor","meta":{"protected":true},"name":"getItemSizePolicy","id":"method-getItemSizePolicy"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"getItemsRenderTree","id":"method-getItemsRenderTree"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{},"name":"getLayoutItems","id":"method-getLayoutItems"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{"protected":true},"name":"getOverflowXStyle","id":"method-getOverflowXStyle"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{"protected":true},"name":"getOverflowYStyle","id":"method-getOverflowYStyle"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"getPositionOffset","id":"method-getPositionOffset"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{"private":true},"name":"getRenderData","id":"method-getRenderData"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{},"name":"getRenderTarget","id":"method-getRenderTarget"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"getRenderTpl","id":"method-getRenderTpl"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"getRenderTree","id":"method-getRenderTree"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"protected":true},"name":"getRenderedItems","id":"method-getRenderedItems"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"getScrollbarsNeeded","id":"method-getScrollbarsNeeded"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{},"name":"getTarget","id":"method-getTarget"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"protected":true},"name":"getVisibleItems","id":"method-getVisibleItems"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{"private":true},"name":"initContextItems","id":"method-initContextItems"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{"protected":true},"name":"initLayout","id":"method-initLayout"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{"private":true},"name":"insertTableCt","id":"method-insertTableCt"},{"tagname":"method","owner":"Ext.layout.container.Absolute","meta":{"private":true},"name":"isItemBoxParent","id":"method-isItemBoxParent"},{"tagname":"method","owner":"Ext.layout.container.Absolute","meta":{"private":true},"name":"isItemLayoutRoot","id":"method-isItemLayoutRoot"},{"tagname":"method","owner":"Ext.layout.container.Absolute","meta":{"private":true},"name":"isItemShrinkWrap","id":"method-isItemShrinkWrap"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"isRunning","id":"method-isRunning"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"protected":true},"name":"isValidParent","id":"method-isValidParent"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{"private":true},"name":"measureContentHeight","id":"method-measureContentHeight"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{"private":true},"name":"measureContentWidth","id":"method-measureContentWidth"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"moveItem","id":"method-moveItem"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"notifyOwner","id":"method-notifyOwner"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"onAdd","id":"method-onAdd"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.layout.container.Absolute","meta":{},"name":"onContentChange","id":"method-onContentChange"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"onDestroy","id":"method-onDestroy"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"onRemove","id":"method-onRemove"},{"tagname":"method","owner":"Ext.layout.container.Anchor","meta":{"private":true},"name":"parseAnchor","id":"method-parseAnchor"},{"tagname":"method","owner":"Ext.util.ElementContainer","meta":{"private":true},"name":"prune","id":"method-prune"},{"tagname":"method","owner":"Ext.util.ElementContainer","meta":{},"name":"removeChildEls","id":"method-removeChildEls"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"renderChildren","id":"method-renderChildren"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"renderItem","id":"method-renderItem"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"protected":true},"name":"renderItems","id":"method-renderItems"},{"tagname":"method","owner":"Ext.layout.container.Anchor","meta":{"private":true},"name":"sanityCheck","id":"method-sanityCheck"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{"protected":true},"name":"setCtSizeIfNeeded","id":"method-setCtSizeIfNeeded"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"setOwner","id":"method-setOwner"},{"tagname":"method","owner":"Ext.layout.container.Auto","meta":{"private":true},"name":"setupRenderTpl","id":"method-setupRenderTpl"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"sortWeightedItems","id":"method-sortWeightedItems"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"undoLayout","id":"method-undoLayout"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.layout.container.Absolute","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.layout.container.Absolute","mixins":[],"mixedInto":[]});