Ext.data.JsonP.Ext_Base({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":["Ext.AbstractComponent","Ext.AbstractManager","Ext.AbstractPlugin","Ext.Action","Ext.ComponentQuery","Ext.ElementLoader","Ext.EventObject","Ext.FocusManager","Ext.Queryable","Ext.Shadow","Ext.ShadowPool","Ext.Template","Ext.XTemplateParser","Ext.ZIndexManager","Ext.app.Controller","Ext.app.EventBus","Ext.app.EventDomain","Ext.button.Manager","Ext.chart.Callout","Ext.chart.Highlight","Ext.chart.Label","Ext.chart.Legend","Ext.chart.Mask","Ext.chart.Navigation","Ext.chart.Shape","Ext.chart.Tip","Ext.chart.axis.Abstract","Ext.chart.series.Series","Ext.chart.theme.Base","Ext.container.DockingContainer","Ext.container.Monitor","Ext.data.AbstractStore","Ext.data.Batch","Ext.data.Connection","Ext.data.Field","Ext.data.IdGenerator","Ext.data.JsonP","Ext.data.Model","Ext.data.NodeInterface","Ext.data.Operation","Ext.data.Request","Ext.data.ResultSet","Ext.data.SortTypes","Ext.data.Tree","Ext.data.Types","Ext.data.amf.Encoder","Ext.data.amf.Packet","Ext.data.amf.RemotingMessage","Ext.data.amf.XmlDecoder","Ext.data.amf.XmlEncoder","Ext.data.association.Association","Ext.data.flash.BinaryXhr","Ext.data.proxy.Proxy","Ext.data.reader.Reader","Ext.data.validations","Ext.data.writer.Writer","Ext.dd.DragDrop","Ext.dd.DragDropManager","Ext.dd.DragTracker","Ext.dd.Registry","Ext.dd.ScrollManager","Ext.direct.Event","Ext.direct.Manager","Ext.direct.Provider","Ext.direct.RemotingMethod","Ext.direct.Transaction","Ext.dom.AbstractElement","Ext.dom.AbstractHelper","Ext.dom.AbstractQuery","Ext.dom.CompositeElementLite","Ext.draw.Color","Ext.draw.Draw","Ext.draw.Matrix","Ext.draw.Sprite","Ext.draw.Surface","Ext.draw.engine.ImageExporter","Ext.draw.engine.SvgExporter","Ext.form.FieldAncestor","Ext.form.Labelable","Ext.form.action.Action","Ext.form.field.Field","Ext.form.field.VTypes","Ext.fx.Anim","Ext.fx.Animator","Ext.fx.CubicBezier","Ext.fx.Manager","Ext.fx.PropertyHandler","Ext.fx.Queue","Ext.fx.target.Target","Ext.grid.CellContext","Ext.grid.ColumnManager","Ext.grid.locking.Lockable","Ext.grid.locking.View","Ext.layout.ClassList","Ext.layout.Context","Ext.layout.ContextItem","Ext.layout.Layout","Ext.layout.SizeModel","Ext.layout.container.boxOverflow.None","Ext.menu.Manager","Ext.panel.Proxy","Ext.perf.Accumulator","Ext.perf.Monitor","Ext.resizer.Resizer","Ext.slider.Thumb","Ext.state.Manager","Ext.state.Provider","Ext.state.Stateful","Ext.tip.QuickTipManager","Ext.util.AbstractMixedCollection","Ext.util.Animate","Ext.util.Bindable","Ext.util.CSS","Ext.util.Cookies","Ext.util.ElementContainer","Ext.util.Event","Ext.util.Filter","Ext.util.Floating","Ext.util.HashMap","Ext.util.History","Ext.util.Inflector","Ext.util.KeyMap","Ext.util.KeyNav","Ext.util.Memento","Ext.util.Observable","Ext.util.Offset","Ext.util.Positionable","Ext.util.ProtoElement","Ext.util.Queue","Ext.util.Region","Ext.util.Renderable","Ext.util.Sortable","Ext.util.Sorter","Ext.util.TaskRunner","Ext.util.TextMetrics","Ext.ux.BoxReorderer","Ext.ux.DataView.Animated","Ext.ux.DataView.DragSelector","Ext.ux.DataView.Draggable","Ext.ux.FieldReplicator","Ext.ux.ProgressBarPager","Ext.ux.SlidingPager","Ext.ux.Spotlight","Ext.ux.TabCloseMenu","Ext.ux.TabScrollerMenu","Ext.ux.ToolbarDroppable","Ext.ux.ajax.DataSimlet","Ext.ux.ajax.SimManager","Ext.ux.ajax.SimXhr","Ext.ux.ajax.Simlet","Ext.ux.event.Driver","Ext.ux.event.Maker","Ext.view.NodeCache"],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Subclasses</h4><div class='dependency'><a href='#!/api/Ext.AbstractComponent' rel='Ext.AbstractComponent' class='docClass'>Ext.AbstractComponent</a></div><div class='dependency'><a href='#!/api/Ext.AbstractManager' rel='Ext.AbstractManager' class='docClass'>Ext.AbstractManager</a></div><div class='dependency'><a href='#!/api/Ext.AbstractPlugin' rel='Ext.AbstractPlugin' class='docClass'>Ext.AbstractPlugin</a></div><div class='dependency'><a href='#!/api/Ext.Action' rel='Ext.Action' class='docClass'>Ext.Action</a></div><div class='dependency'><a href='#!/api/Ext.ComponentQuery' rel='Ext.ComponentQuery' class='docClass'>Ext.ComponentQuery</a></div><div class='dependency'><a href='#!/api/Ext.ElementLoader' rel='Ext.ElementLoader' class='docClass'>Ext.ElementLoader</a></div><div class='dependency'><a href='#!/api/Ext.EventObject' rel='Ext.EventObject' class='docClass'>Ext.EventObject</a></div><div class='dependency'><a href='#!/api/Ext.FocusManager' rel='Ext.FocusManager' class='docClass'>Ext.FocusManager</a></div><div class='dependency'><a href='#!/api/Ext.Queryable' rel='Ext.Queryable' class='docClass'>Ext.Queryable</a></div><div class='dependency'><a href='#!/api/Ext.Shadow' rel='Ext.Shadow' class='docClass'>Ext.Shadow</a></div><div class='dependency'><a href='#!/api/Ext.ShadowPool' rel='Ext.ShadowPool' class='docClass'>Ext.ShadowPool</a></div><div class='dependency'><a href='#!/api/Ext.Template' rel='Ext.Template' class='docClass'>Ext.Template</a></div><div class='dependency'><a href='#!/api/Ext.XTemplateParser' rel='Ext.XTemplateParser' class='docClass'>Ext.XTemplateParser</a></div><div class='dependency'><a href='#!/api/Ext.ZIndexManager' rel='Ext.ZIndexManager' class='docClass'>Ext.ZIndexManager</a></div><div class='dependency'><a href='#!/api/Ext.app.Controller' rel='Ext.app.Controller' class='docClass'>Ext.app.Controller</a></div><div class='dependency'><a href='#!/api/Ext.app.EventBus' rel='Ext.app.EventBus' class='docClass'>Ext.app.EventBus</a></div><div class='dependency'><a href='#!/api/Ext.app.EventDomain' rel='Ext.app.EventDomain' class='docClass'>Ext.app.EventDomain</a></div><div class='dependency'><a href='#!/api/Ext.button.Manager' rel='Ext.button.Manager' class='docClass'>Ext.button.Manager</a></div><div class='dependency'><a href='#!/api/Ext.chart.Callout' rel='Ext.chart.Callout' class='docClass'>Ext.chart.Callout</a></div><div class='dependency'><a href='#!/api/Ext.chart.Highlight' rel='Ext.chart.Highlight' class='docClass'>Ext.chart.Highlight</a></div><div class='dependency'><a href='#!/api/Ext.chart.Label' rel='Ext.chart.Label' class='docClass'>Ext.chart.Label</a></div><div class='dependency'><a href='#!/api/Ext.chart.Legend' rel='Ext.chart.Legend' class='docClass'>Ext.chart.Legend</a></div><div class='dependency'><a href='#!/api/Ext.chart.Mask' rel='Ext.chart.Mask' class='docClass'>Ext.chart.Mask</a></div><div class='dependency'><a href='#!/api/Ext.chart.Navigation' rel='Ext.chart.Navigation' class='docClass'>Ext.chart.Navigation</a></div><div class='dependency'><a href='#!/api/Ext.chart.Shape' rel='Ext.chart.Shape' class='docClass'>Ext.chart.Shape</a></div><div class='dependency'><a href='#!/api/Ext.chart.Tip' rel='Ext.chart.Tip' class='docClass'>Ext.chart.Tip</a></div><div class='dependency'><a href='#!/api/Ext.chart.axis.Abstract' rel='Ext.chart.axis.Abstract' class='docClass'>Ext.chart.axis.Abstract</a></div><div class='dependency'><a href='#!/api/Ext.chart.series.Series' rel='Ext.chart.series.Series' class='docClass'>Ext.chart.series.Series</a></div><div class='dependency'><a href='#!/api/Ext.chart.theme.Base' rel='Ext.chart.theme.Base' class='docClass'>Ext.chart.theme.Base</a></div><div class='dependency'><a href='#!/api/Ext.container.DockingContainer' rel='Ext.container.DockingContainer' class='docClass'>Ext.container.DockingContainer</a></div><div class='dependency'><a href='#!/api/Ext.container.Monitor' rel='Ext.container.Monitor' class='docClass'>Ext.container.Monitor</a></div><div class='dependency'><a href='#!/api/Ext.data.AbstractStore' rel='Ext.data.AbstractStore' class='docClass'>Ext.data.AbstractStore</a></div><div class='dependency'><a href='#!/api/Ext.data.Batch' rel='Ext.data.Batch' class='docClass'>Ext.data.Batch</a></div><div class='dependency'><a href='#!/api/Ext.data.Connection' rel='Ext.data.Connection' class='docClass'>Ext.data.Connection</a></div><div class='dependency'><a href='#!/api/Ext.data.Field' rel='Ext.data.Field' class='docClass'>Ext.data.Field</a></div><div class='dependency'><a href='#!/api/Ext.data.IdGenerator' rel='Ext.data.IdGenerator' class='docClass'>Ext.data.IdGenerator</a></div><div class='dependency'><a href='#!/api/Ext.data.JsonP' rel='Ext.data.JsonP' class='docClass'>Ext.data.JsonP</a></div><div class='dependency'><a href='#!/api/Ext.data.Model' rel='Ext.data.Model' class='docClass'>Ext.data.Model</a></div><div class='dependency'><a href='#!/api/Ext.data.NodeInterface' rel='Ext.data.NodeInterface' class='docClass'>Ext.data.NodeInterface</a></div><div class='dependency'><a href='#!/api/Ext.data.Operation' rel='Ext.data.Operation' class='docClass'>Ext.data.Operation</a></div><div class='dependency'><a href='#!/api/Ext.data.Request' rel='Ext.data.Request' class='docClass'>Ext.data.Request</a></div><div class='dependency'><a href='#!/api/Ext.data.ResultSet' rel='Ext.data.ResultSet' class='docClass'>Ext.data.ResultSet</a></div><div class='dependency'><a href='#!/api/Ext.data.SortTypes' rel='Ext.data.SortTypes' class='docClass'>Ext.data.SortTypes</a></div><div class='dependency'><a href='#!/api/Ext.data.Tree' rel='Ext.data.Tree' class='docClass'>Ext.data.Tree</a></div><div class='dependency'><a href='#!/api/Ext.data.Types' rel='Ext.data.Types' class='docClass'>Ext.data.Types</a></div><div class='dependency'><a href='#!/api/Ext.data.amf.Encoder' rel='Ext.data.amf.Encoder' class='docClass'>Ext.data.amf.Encoder</a></div><div class='dependency'><a href='#!/api/Ext.data.amf.Packet' rel='Ext.data.amf.Packet' class='docClass'>Ext.data.amf.Packet</a></div><div class='dependency'><a href='#!/api/Ext.data.amf.RemotingMessage' rel='Ext.data.amf.RemotingMessage' class='docClass'>Ext.data.amf.RemotingMessage</a></div><div class='dependency'><a href='#!/api/Ext.data.amf.XmlDecoder' rel='Ext.data.amf.XmlDecoder' class='docClass'>Ext.data.amf.XmlDecoder</a></div><div class='dependency'><a href='#!/api/Ext.data.amf.XmlEncoder' rel='Ext.data.amf.XmlEncoder' class='docClass'>Ext.data.amf.XmlEncoder</a></div><div class='dependency'><a href='#!/api/Ext.data.association.Association' rel='Ext.data.association.Association' class='docClass'>Ext.data.association.Association</a></div><div class='dependency'><a href='#!/api/Ext.data.flash.BinaryXhr' rel='Ext.data.flash.BinaryXhr' class='docClass'>Ext.data.flash.BinaryXhr</a></div><div class='dependency'><a href='#!/api/Ext.data.proxy.Proxy' rel='Ext.data.proxy.Proxy' class='docClass'>Ext.data.proxy.Proxy</a></div><div class='dependency'><a href='#!/api/Ext.data.reader.Reader' rel='Ext.data.reader.Reader' class='docClass'>Ext.data.reader.Reader</a></div><div class='dependency'><a href='#!/api/Ext.data.validations' rel='Ext.data.validations' class='docClass'>Ext.data.validations</a></div><div class='dependency'><a href='#!/api/Ext.data.writer.Writer' rel='Ext.data.writer.Writer' class='docClass'>Ext.data.writer.Writer</a></div><div class='dependency'><a href='#!/api/Ext.dd.DragDrop' rel='Ext.dd.DragDrop' class='docClass'>Ext.dd.DragDrop</a></div><div class='dependency'><a href='#!/api/Ext.dd.DragDropManager' rel='Ext.dd.DragDropManager' class='docClass'>Ext.dd.DragDropManager</a></div><div class='dependency'><a href='#!/api/Ext.dd.DragTracker' rel='Ext.dd.DragTracker' class='docClass'>Ext.dd.DragTracker</a></div><div class='dependency'><a href='#!/api/Ext.dd.Registry' rel='Ext.dd.Registry' class='docClass'>Ext.dd.Registry</a></div><div class='dependency'><a href='#!/api/Ext.dd.ScrollManager' rel='Ext.dd.ScrollManager' class='docClass'>Ext.dd.ScrollManager</a></div><div class='dependency'><a href='#!/api/Ext.direct.Event' rel='Ext.direct.Event' class='docClass'>Ext.direct.Event</a></div><div class='dependency'><a href='#!/api/Ext.direct.Manager' rel='Ext.direct.Manager' class='docClass'>Ext.direct.Manager</a></div><div class='dependency'><a href='#!/api/Ext.direct.Provider' rel='Ext.direct.Provider' class='docClass'>Ext.direct.Provider</a></div><div class='dependency'><a href='#!/api/Ext.direct.RemotingMethod' rel='Ext.direct.RemotingMethod' class='docClass'>Ext.direct.RemotingMethod</a></div><div class='dependency'><a href='#!/api/Ext.direct.Transaction' rel='Ext.direct.Transaction' class='docClass'>Ext.direct.Transaction</a></div><div class='dependency'><a href='#!/api/Ext.dom.AbstractElement' rel='Ext.dom.AbstractElement' class='docClass'>Ext.dom.AbstractElement</a></div><div class='dependency'><a href='#!/api/Ext.dom.AbstractHelper' rel='Ext.dom.AbstractHelper' class='docClass'>Ext.dom.AbstractHelper</a></div><div class='dependency'><a href='#!/api/Ext.dom.AbstractQuery' rel='Ext.dom.AbstractQuery' class='docClass'>Ext.dom.AbstractQuery</a></div><div class='dependency'><a href='#!/api/Ext.dom.CompositeElementLite' rel='Ext.dom.CompositeElementLite' class='docClass'>Ext.dom.CompositeElementLite</a></div><div class='dependency'><a href='#!/api/Ext.draw.Color' rel='Ext.draw.Color' class='docClass'>Ext.draw.Color</a></div><div class='dependency'><a href='#!/api/Ext.draw.Draw' rel='Ext.draw.Draw' class='docClass'>Ext.draw.Draw</a></div><div class='dependency'><a href='#!/api/Ext.draw.Matrix' rel='Ext.draw.Matrix' class='docClass'>Ext.draw.Matrix</a></div><div class='dependency'><a href='#!/api/Ext.draw.Sprite' rel='Ext.draw.Sprite' class='docClass'>Ext.draw.Sprite</a></div><div class='dependency'><a href='#!/api/Ext.draw.Surface' rel='Ext.draw.Surface' class='docClass'>Ext.draw.Surface</a></div><div class='dependency'><a href='#!/api/Ext.draw.engine.ImageExporter' rel='Ext.draw.engine.ImageExporter' class='docClass'>Ext.draw.engine.ImageExporter</a></div><div class='dependency'><a href='#!/api/Ext.draw.engine.SvgExporter' rel='Ext.draw.engine.SvgExporter' class='docClass'>Ext.draw.engine.SvgExporter</a></div><div class='dependency'><a href='#!/api/Ext.form.FieldAncestor' rel='Ext.form.FieldAncestor' class='docClass'>Ext.form.FieldAncestor</a></div><div class='dependency'><a href='#!/api/Ext.form.Labelable' rel='Ext.form.Labelable' class='docClass'>Ext.form.Labelable</a></div><div class='dependency'><a href='#!/api/Ext.form.action.Action' rel='Ext.form.action.Action' class='docClass'>Ext.form.action.Action</a></div><div class='dependency'><a href='#!/api/Ext.form.field.Field' rel='Ext.form.field.Field' class='docClass'>Ext.form.field.Field</a></div><div class='dependency'><a href='#!/api/Ext.form.field.VTypes' rel='Ext.form.field.VTypes' class='docClass'>Ext.form.field.VTypes</a></div><div class='dependency'><a href='#!/api/Ext.fx.Anim' rel='Ext.fx.Anim' class='docClass'>Ext.fx.Anim</a></div><div class='dependency'><a href='#!/api/Ext.fx.Animator' rel='Ext.fx.Animator' class='docClass'>Ext.fx.Animator</a></div><div class='dependency'><a href='#!/api/Ext.fx.CubicBezier' rel='Ext.fx.CubicBezier' class='docClass'>Ext.fx.CubicBezier</a></div><div class='dependency'><a href='#!/api/Ext.fx.Manager' rel='Ext.fx.Manager' class='docClass'>Ext.fx.Manager</a></div><div class='dependency'><a href='#!/api/Ext.fx.PropertyHandler' rel='Ext.fx.PropertyHandler' class='docClass'>Ext.fx.PropertyHandler</a></div><div class='dependency'><a href='#!/api/Ext.fx.Queue' rel='Ext.fx.Queue' class='docClass'>Ext.fx.Queue</a></div><div class='dependency'><a href='#!/api/Ext.fx.target.Target' rel='Ext.fx.target.Target' class='docClass'>Ext.fx.target.Target</a></div><div class='dependency'><a href='#!/api/Ext.grid.CellContext' rel='Ext.grid.CellContext' class='docClass'>Ext.grid.CellContext</a></div><div class='dependency'><a href='#!/api/Ext.grid.ColumnManager' rel='Ext.grid.ColumnManager' class='docClass'>Ext.grid.ColumnManager</a></div><div class='dependency'><a href='#!/api/Ext.grid.locking.Lockable' rel='Ext.grid.locking.Lockable' class='docClass'>Ext.grid.locking.Lockable</a></div><div class='dependency'><a href='#!/api/Ext.grid.locking.View' rel='Ext.grid.locking.View' class='docClass'>Ext.grid.locking.View</a></div><div class='dependency'><a href='#!/api/Ext.layout.ClassList' rel='Ext.layout.ClassList' class='docClass'>Ext.layout.ClassList</a></div><div class='dependency'><a href='#!/api/Ext.layout.Context' rel='Ext.layout.Context' class='docClass'>Ext.layout.Context</a></div><div class='dependency'><a href='#!/api/Ext.layout.ContextItem' rel='Ext.layout.ContextItem' class='docClass'>Ext.layout.ContextItem</a></div><div class='dependency'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='docClass'>Ext.layout.Layout</a></div><div class='dependency'><a href='#!/api/Ext.layout.SizeModel' rel='Ext.layout.SizeModel' class='docClass'>Ext.layout.SizeModel</a></div><div class='dependency'><a href='#!/api/Ext.layout.container.boxOverflow.None' rel='Ext.layout.container.boxOverflow.None' class='docClass'>Ext.layout.container.boxOverflow.None</a></div><div class='dependency'><a href='#!/api/Ext.menu.Manager' rel='Ext.menu.Manager' class='docClass'>Ext.menu.Manager</a></div><div class='dependency'><a href='#!/api/Ext.panel.Proxy' rel='Ext.panel.Proxy' class='docClass'>Ext.panel.Proxy</a></div><div class='dependency'><a href='#!/api/Ext.perf.Accumulator' rel='Ext.perf.Accumulator' class='docClass'>Ext.perf.Accumulator</a></div><div class='dependency'><a href='#!/api/Ext.perf.Monitor' rel='Ext.perf.Monitor' class='docClass'>Ext.perf.Monitor</a></div><div class='dependency'><a href='#!/api/Ext.resizer.Resizer' rel='Ext.resizer.Resizer' class='docClass'>Ext.resizer.Resizer</a></div><div class='dependency'><a href='#!/api/Ext.slider.Thumb' rel='Ext.slider.Thumb' class='docClass'>Ext.slider.Thumb</a></div><div class='dependency'><a href='#!/api/Ext.state.Manager' rel='Ext.state.Manager' class='docClass'>Ext.state.Manager</a></div><div class='dependency'><a href='#!/api/Ext.state.Provider' rel='Ext.state.Provider' class='docClass'>Ext.state.Provider</a></div><div class='dependency'><a href='#!/api/Ext.state.Stateful' rel='Ext.state.Stateful' class='docClass'>Ext.state.Stateful</a></div><div class='dependency'><a href='#!/api/Ext.tip.QuickTipManager' rel='Ext.tip.QuickTipManager' class='docClass'>Ext.tip.QuickTipManager</a></div><div class='dependency'><a href='#!/api/Ext.util.AbstractMixedCollection' rel='Ext.util.AbstractMixedCollection' class='docClass'>Ext.util.AbstractMixedCollection</a></div><div class='dependency'><a href='#!/api/Ext.util.Animate' rel='Ext.util.Animate' class='docClass'>Ext.util.Animate</a></div><div class='dependency'><a href='#!/api/Ext.util.Bindable' rel='Ext.util.Bindable' class='docClass'>Ext.util.Bindable</a></div><div class='dependency'><a href='#!/api/Ext.util.CSS' rel='Ext.util.CSS' class='docClass'>Ext.util.CSS</a></div><div class='dependency'><a href='#!/api/Ext.util.Cookies' rel='Ext.util.Cookies' class='docClass'>Ext.util.Cookies</a></div><div class='dependency'><a href='#!/api/Ext.util.ElementContainer' rel='Ext.util.ElementContainer' class='docClass'>Ext.util.ElementContainer</a></div><div class='dependency'><a href='#!/api/Ext.util.Event' rel='Ext.util.Event' class='docClass'>Ext.util.Event</a></div><div class='dependency'><a href='#!/api/Ext.util.Filter' rel='Ext.util.Filter' class='docClass'>Ext.util.Filter</a></div><div class='dependency'><a href='#!/api/Ext.util.Floating' rel='Ext.util.Floating' class='docClass'>Ext.util.Floating</a></div><div class='dependency'><a href='#!/api/Ext.util.HashMap' rel='Ext.util.HashMap' class='docClass'>Ext.util.HashMap</a></div><div class='dependency'><a href='#!/api/Ext.util.History' rel='Ext.util.History' class='docClass'>Ext.util.History</a></div><div class='dependency'><a href='#!/api/Ext.util.Inflector' rel='Ext.util.Inflector' class='docClass'>Ext.util.Inflector</a></div><div class='dependency'><a href='#!/api/Ext.util.KeyMap' rel='Ext.util.KeyMap' class='docClass'>Ext.util.KeyMap</a></div><div class='dependency'><a href='#!/api/Ext.util.KeyNav' rel='Ext.util.KeyNav' class='docClass'>Ext.util.KeyNav</a></div><div class='dependency'><a href='#!/api/Ext.util.Memento' rel='Ext.util.Memento' class='docClass'>Ext.util.Memento</a></div><div class='dependency'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='docClass'>Ext.util.Observable</a></div><div class='dependency'><a href='#!/api/Ext.util.Offset' rel='Ext.util.Offset' class='docClass'>Ext.util.Offset</a></div><div class='dependency'><a href='#!/api/Ext.util.Positionable' rel='Ext.util.Positionable' class='docClass'>Ext.util.Positionable</a></div><div class='dependency'><a href='#!/api/Ext.util.ProtoElement' rel='Ext.util.ProtoElement' class='docClass'>Ext.util.ProtoElement</a></div><div class='dependency'><a href='#!/api/Ext.util.Queue' rel='Ext.util.Queue' class='docClass'>Ext.util.Queue</a></div><div class='dependency'><a href='#!/api/Ext.util.Region' rel='Ext.util.Region' class='docClass'>Ext.util.Region</a></div><div class='dependency'><a href='#!/api/Ext.util.Renderable' rel='Ext.util.Renderable' class='docClass'>Ext.util.Renderable</a></div><div class='dependency'><a href='#!/api/Ext.util.Sortable' rel='Ext.util.Sortable' class='docClass'>Ext.util.Sortable</a></div><div class='dependency'><a href='#!/api/Ext.util.Sorter' rel='Ext.util.Sorter' class='docClass'>Ext.util.Sorter</a></div><div class='dependency'><a href='#!/api/Ext.util.TaskRunner' rel='Ext.util.TaskRunner' class='docClass'>Ext.util.TaskRunner</a></div><div class='dependency'><a href='#!/api/Ext.util.TextMetrics' rel='Ext.util.TextMetrics' class='docClass'>Ext.util.TextMetrics</a></div><div class='dependency'><a href='#!/api/Ext.ux.BoxReorderer' rel='Ext.ux.BoxReorderer' class='docClass'>Ext.ux.BoxReorderer</a></div><div class='dependency'><a href='#!/api/Ext.ux.DataView.Animated' rel='Ext.ux.DataView.Animated' class='docClass'>Ext.ux.DataView.Animated</a></div><div class='dependency'><a href='#!/api/Ext.ux.DataView.DragSelector' rel='Ext.ux.DataView.DragSelector' class='docClass'>Ext.ux.DataView.DragSelector</a></div><div class='dependency'><a href='#!/api/Ext.ux.DataView.Draggable' rel='Ext.ux.DataView.Draggable' class='docClass'>Ext.ux.DataView.Draggable</a></div><div class='dependency'><a href='#!/api/Ext.ux.FieldReplicator' rel='Ext.ux.FieldReplicator' class='docClass'>Ext.ux.FieldReplicator</a></div><div class='dependency'><a href='#!/api/Ext.ux.ProgressBarPager' rel='Ext.ux.ProgressBarPager' class='docClass'>Ext.ux.ProgressBarPager</a></div><div class='dependency'><a href='#!/api/Ext.ux.SlidingPager' rel='Ext.ux.SlidingPager' class='docClass'>Ext.ux.SlidingPager</a></div><div class='dependency'><a href='#!/api/Ext.ux.Spotlight' rel='Ext.ux.Spotlight' class='docClass'>Ext.ux.Spotlight</a></div><div class='dependency'><a href='#!/api/Ext.ux.TabCloseMenu' rel='Ext.ux.TabCloseMenu' class='docClass'>Ext.ux.TabCloseMenu</a></div><div class='dependency'><a href='#!/api/Ext.ux.TabScrollerMenu' rel='Ext.ux.TabScrollerMenu' class='docClass'>Ext.ux.TabScrollerMenu</a></div><div class='dependency'><a href='#!/api/Ext.ux.ToolbarDroppable' rel='Ext.ux.ToolbarDroppable' class='docClass'>Ext.ux.ToolbarDroppable</a></div><div class='dependency'><a href='#!/api/Ext.ux.ajax.DataSimlet' rel='Ext.ux.ajax.DataSimlet' class='docClass'>Ext.ux.ajax.DataSimlet</a></div><div class='dependency'><a href='#!/api/Ext.ux.ajax.SimManager' rel='Ext.ux.ajax.SimManager' class='docClass'>Ext.ux.ajax.SimManager</a></div><div class='dependency'><a href='#!/api/Ext.ux.ajax.SimXhr' rel='Ext.ux.ajax.SimXhr' class='docClass'>Ext.ux.ajax.SimXhr</a></div><div class='dependency'><a href='#!/api/Ext.ux.ajax.Simlet' rel='Ext.ux.ajax.Simlet' class='docClass'>Ext.ux.ajax.Simlet</a></div><div class='dependency'><a href='#!/api/Ext.ux.event.Driver' rel='Ext.ux.event.Driver' class='docClass'>Ext.ux.event.Driver</a></div><div class='dependency'><a href='#!/api/Ext.ux.event.Maker' rel='Ext.ux.event.Maker' class='docClass'>Ext.ux.event.Maker</a></div><div class='dependency'><a href='#!/api/Ext.view.NodeCache' rel='Ext.view.NodeCache' class='docClass'>Ext.view.NodeCache</a></div><h4>Files</h4><div class='dependency'><a href='source/Base.html#Ext-Base' target='_blank'>Base.js</a></div></pre><div class='doc-contents'><p>The root of all classes created with <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>.</p>\n\n<p><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a> is the building block of all Ext classes. All classes in Ext inherit from <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a>.\nAll prototype and static members of this class are inherited by all other classes.</p>\n</div><div class='members'><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-configMap' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-initConfigList' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-isInstance' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-self' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-callOverridden' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-configClass' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-destroy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-destroy' class='name expandable'>destroy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.util.ElementContainer-method-destroy' rel='Ext.util.ElementContainer-method-destroy' class='docClass'>Ext.util.ElementContainer.destroy</a>, <a href='#!/api/Ext.AbstractComponent-method-destroy' rel='Ext.AbstractComponent-method-destroy' class='docClass'>Ext.AbstractComponent.destroy</a>, <a href='#!/api/Ext.AbstractPlugin-method-destroy' rel='Ext.AbstractPlugin-method-destroy' class='docClass'>Ext.AbstractPlugin.destroy</a></p></div></div></div><div id='method-getConfig' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getInitialConfig' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-hasConfig' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-initConfig' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-onConfigUpdate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setConfig' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-statics' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-create' class='name expandable'>create</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create a new instance of this Class. ...</div><div class='long'><p>Create a new instance of this Class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.create({\n    someConfig: true\n});\n</code></pre>\n\n<p>All parameters are passed to the constructor of the class.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the created instance.</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-mixin' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Base'>Ext.Base</span><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div></div></div>","superclasses":[],"meta":{"author":["Jacky Nguyen <<EMAIL>>"],"docauthor":["Jacky Nguyen <<EMAIL>>"]},"requires":[],"html_meta":{"author":null,"docauthor":null},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"Base.html#Ext-Base","filename":"Base.js"}],"linenr":5,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.Base","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.Base","mixins":[],"mixedInto":[]});