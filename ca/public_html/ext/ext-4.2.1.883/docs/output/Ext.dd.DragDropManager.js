Ext.data.JsonP.Ext_dd_DragDropManager({"alternateClassNames":["Ext.dd.DDM","Ext.dd.DragDropMgr"],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":"Ext.Base","uses":["Ext.tip.QuickTipManager"],"html":"<div><pre class=\"hierarchy\"><h4>Alternate names</h4><div class='alternate-class-name'>Ext.dd.DDM</div><div class='alternate-class-name'>Ext.dd.DragDropMgr</div><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><strong>Ext.dd.DragDropManager</strong></div></div><h4>Requires</h4><div class='dependency'><a href='#!/api/Ext.util.Region' rel='Ext.util.Region' class='docClass'>Ext.util.Region</a></div><h4>Uses</h4><div class='dependency'><a href='#!/api/Ext.tip.QuickTipManager' rel='Ext.tip.QuickTipManager' class='docClass'>Ext.tip.QuickTipManager</a></div><h4>Files</h4><div class='dependency'><a href='source/DragDropManager.html#Ext-dd-DragDropManager' target='_blank'>DragDropManager.js</a></div></pre><div class='doc-contents'><p>DragDropManager is a singleton that tracks the element interaction for\nall DragDrop items in the window.  Generally, you will not call\nthis class directly, but it does have helper methods that could\nbe useful in your DragDrop implementations.</p>\n</div><div class='members'><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-INTERSECT' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-INTERSECT' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-INTERSECT' class='name expandable'>INTERSECT</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>In intersect mode, drag and drop interaction is defined by the\noverlap of two or more drag and drop objects. ...</div><div class='long'><p>In intersect mode, drag and drop interaction is defined by the\noverlap of two or more drag and drop objects.</p>\n<p>Defaults to: <code>1</code></p></div></div></div><div id='property-POINT' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-POINT' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-POINT' class='name expandable'>POINT</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>In point mode, drag and drop interaction is defined by the\nlocation of the cursor during the drag/drop ...</div><div class='long'><p>In point mode, drag and drop interaction is defined by the\nlocation of the cursor during the drag/drop</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-clickPixelThresh' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-clickPixelThresh' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-clickPixelThresh' class='name expandable'>clickPixelThresh</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The number of pixels that the mouse needs to move after the\nmousedown before the drag is initiated. ...</div><div class='long'><p>The number of pixels that the mouse needs to move after the\nmousedown before the drag is initiated.  Default=3;</p>\n<p>Defaults to: <code>3</code></p></div></div></div><div id='property-clickTimeThresh' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-clickTimeThresh' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-clickTimeThresh' class='name expandable'>clickTimeThresh</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The number of milliseconds after the mousedown event to initiate the\ndrag if we don't get a mouseup event. ...</div><div class='long'><p>The number of milliseconds after the mousedown event to initiate the\ndrag if we don't get a mouseup event. Default=350</p>\n<p>Defaults to: <code>350</code></p></div></div></div><div id='property-clickTimeout' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-clickTimeout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-clickTimeout' class='name not-expandable'>clickTimeout</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'><p>Timeout used for the click time threshold</p>\n</div><div class='long'><p>Timeout used for the click time threshold</p>\n</div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-deltaX' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-deltaX' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-deltaX' class='name expandable'>deltaX</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>the X distance between the cursor and the object being dragged ...</div><div class='long'><p>the X distance between the cursor and the object being dragged</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-deltaY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-deltaY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-deltaY' class='name expandable'>deltaY</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>the Y distance between the cursor and the object being dragged ...</div><div class='long'><p>the Y distance between the cursor and the object being dragged</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-dragCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-dragCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-dragCls' class='name expandable'>dragCls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='readonly signature' >readonly</strong></div><div class='description'><div class='short'>Class to add to the dragged element of a DragDrop instance. ...</div><div class='long'><p>Class to add to the <a href=\"#!/api/Ext.dd.DragDrop-method-getDragEl\" rel=\"Ext.dd.DragDrop-method-getDragEl\" class=\"docClass\">dragged element</a> of a DragDrop instance.</p>\n<p>Defaults to: <code>Ext.baseCSSPrefix + 'dd-drag-current'</code></p></div></div></div><div id='property-dragCurrent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-dragCurrent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-dragCurrent' class='name not-expandable'>dragCurrent</a><span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'><p>the DragDrop object that is currently being dragged</p>\n</div><div class='long'><p>the DragDrop object that is currently being dragged</p>\n</div></div></div><div id='property-dragOvers' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-dragOvers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-dragOvers' class='name expandable'>dragOvers</a><span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a>[]</span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>the DragDrop object(s) that are being hovered over ...</div><div class='long'><p>the DragDrop object(s) that are being hovered over</p>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-dragThreshMet' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-dragThreshMet' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-dragThreshMet' class='name expandable'>dragThreshMet</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Flag that indicates that either the drag pixel threshold or the\nmousdown time threshold has been met ...</div><div class='long'><p>Flag that indicates that either the drag pixel threshold or the\nmousdown time threshold has been met</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-elementCache' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-elementCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-elementCache' class='name expandable'>elementCache</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>A cache of DOM elements ...</div><div class='long'><p>A cache of DOM elements</p>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-handleIds' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-handleIds' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-handleIds' class='name expandable'>handleIds</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Array of element ids defined as drag handles. ...</div><div class='long'><p>Array of element ids defined as drag handles.  Used to determine\nif the element that generated the mousedown event is actually the\nhandle and not the html element itself.</p>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-ids' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-ids' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-ids' class='name expandable'>ids</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Two dimensional Array of registered DragDrop objects. ...</div><div class='long'><p>Two dimensional Array of registered DragDrop objects.  The first\ndimension is the DragDrop item group, the second the DragDrop\nobject.</p>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-initialized' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-initialized' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-initialized' class='name expandable'>initialized</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Internal flag that is set to true when drag and drop has been\nintialized ...</div><div class='long'><p>Internal flag that is set to true when drag and drop has been\nintialized</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-locationCache' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-locationCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-locationCache' class='name expandable'>locationCache</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Location cache that is set for all drag drop objects when a drag is\ninitiated, cleared when the drag is finished. ...</div><div class='long'><p>Location cache that is set for all drag drop objects when a drag is\ninitiated, cleared when the drag is finished.</p>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-locked' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-locked' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-locked' class='name expandable'>locked</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>All drag and drop can be disabled. ...</div><div class='long'><p>All drag and drop can be disabled.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-mode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-mode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-mode' class='name expandable'>mode</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The current drag and drop mode. ...</div><div class='long'><p>The current drag and drop mode.  Default: POINT</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-notifyOccluded' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-notifyOccluded' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-notifyOccluded' class='name expandable'>notifyOccluded</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>This config is only provided to provide old, usually unwanted drag/drop behaviour. ...</div><div class='long'><p>This config is only provided to provide old, usually unwanted drag/drop behaviour.</p>\n\n<p>From ExtJS 4.1.0 onwards, when drop targets are contained in floating, absolutely positioned elements\nsuch as in <a href=\"#!/api/Ext.window.Window\" rel=\"Ext.window.Window\" class=\"docClass\">Windows</a>, which may overlap each other, <code>over</code> and <code>drop</code> events\nare only delivered to the topmost drop target at the mouse position.</p>\n\n<p>If all targets below that in zIndex order should also receive notifications, set\n<code>notifyOccluded</code> to <code>true</code>.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-preventDefault' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-preventDefault' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-preventDefault' class='name expandable'>preventDefault</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Flag to determine if we should prevent the default behavior of the\nevents we define. ...</div><div class='long'><p>Flag to determine if we should prevent the default behavior of the\nevents we define. By default this is true, but this can be set to\nfalse if you need the default behavior (not recommended)</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div><div id='property-startX' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-startX' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-startX' class='name expandable'>startX</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>The X position of the mousedown event stored for later use when a\ndrag threshold is met. ...</div><div class='long'><p>The X position of the mousedown event stored for later use when a\ndrag threshold is met.</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-startY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-startY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-startY' class='name expandable'>startY</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>The Y position of the mousedown event stored for later use when a\ndrag threshold is met. ...</div><div class='long'><p>The Y position of the mousedown event stored for later use when a\ndrag threshold is met.</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-stopPropagation' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-stopPropagation' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-stopPropagation' class='name expandable'>stopPropagation</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Flag to determine if we should stop the propagation of the events\nwe generate. ...</div><div class='long'><p>Flag to determine if we should stop the propagation of the events\nwe generate. This is true by default but you may want to set it to\nfalse if the html element contains other features that require the\nmouse click.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-useCache' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-property-useCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-property-useCache' class='name expandable'>useCache</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Set useCache to false if you want to force object the lookup of each\ndrag and drop linked element constantly during a...</div><div class='long'><p>Set useCache to false if you want to force object the lookup of each\ndrag and drop linked element constantly during a drag.</p>\n<p>Defaults to: <code>true</code></p></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-_execOnAll' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-_execOnAll' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-_execOnAll' class='name expandable'>_execOnAll</a>( <span class='pre'>sMethod, args</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Runs method on all drag and drop objects ...</div><div class='long'><p>Runs method on all drag and drop objects</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>sMethod</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-_onLoad' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-_onLoad' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-_onLoad' class='name expandable'>_onLoad</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Drag and drop initialization. ...</div><div class='long'><p>Drag and drop initialization.  Sets up the global event handlers</p>\n</div></div></div><div id='method-_onResize' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-_onResize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-_onResize' class='name expandable'>_onResize</a>( <span class='pre'>e</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Reset constraints on all drag and drop objs ...</div><div class='long'><p>Reset constraints on all drag and drop objs</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-_onUnload' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-_onUnload' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-_onUnload' class='name expandable'>_onUnload</a>( <span class='pre'>e, me</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>unload event handler ...</div><div class='long'><p>unload event handler</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>me</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-_remove' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-_remove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-_remove' class='name expandable'>_remove</a>( <span class='pre'>oDD</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Unregisters a drag and drop item. ...</div><div class='long'><p>Unregisters a drag and drop item.  This is executed in\nDragDrop.unreg, use that method instead of calling this directly.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>oDD</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-byZIndex' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-byZIndex' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-byZIndex' class='name expandable'>byZIndex</a>( <span class='pre'>d1, d2</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Utility method to pass to Ext.Array.sort when sorting potential drop targets by z-index. ...</div><div class='long'><p>Utility method to pass to <a href=\"#!/api/Ext.Array-method-sort\" rel=\"Ext.Array-method-sort\" class=\"docClass\">Ext.Array.sort</a> when sorting potential drop targets by z-index.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>d1</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>d2</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-callOverridden' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-destroy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-destroy' class='name expandable'>destroy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.util.ElementContainer-method-destroy' rel='Ext.util.ElementContainer-method-destroy' class='docClass'>Ext.util.ElementContainer.destroy</a>, <a href='#!/api/Ext.AbstractComponent-method-destroy' rel='Ext.AbstractComponent-method-destroy' class='docClass'>Ext.AbstractComponent.destroy</a>, <a href='#!/api/Ext.AbstractPlugin-method-destroy' rel='Ext.AbstractPlugin-method-destroy' class='docClass'>Ext.AbstractPlugin.destroy</a></p></div></div></div><div id='method-fireEvents' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-fireEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-fireEvents' class='name expandable'>fireEvents</a>( <span class='pre'>e, isDrop</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Iterates over all of the DragDrop elements to find ones we are\nhovering over or dropping on ...</div><div class='long'><p>Iterates over all of the DragDrop elements to find ones we are\nhovering over or dropping on</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>the event</p>\n</div></li><li><span class='pre'>isDrop</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>is this a drop op or a mouseover op?</p>\n</div></li></ul></div></div></div><div id='method-getBestMatch' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-getBestMatch' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-getBestMatch' class='name expandable'>getBestMatch</a>( <span class='pre'>dds</span> ) : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a></div><div class='description'><div class='short'>Helper function for getting the best match from the list of drag\nand drop objects returned by the drag and drop event...</div><div class='long'><p>Helper function for getting the best match from the list of drag\nand drop objects returned by the drag and drop events when we are\nin INTERSECT mode.  It returns either the first object that the\ncursor is over, or the object that has the greatest overlap with\nthe dragged element.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>dds</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a>[]<div class='sub-desc'><p>The array of drag and drop objects\ntargeted</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a></span><div class='sub-desc'><p>The best single match</p>\n</div></li></ul></div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getCss' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-getCss' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-getCss' class='name expandable'>getCss</a>( <span class='pre'>id</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Returns the style property for the DOM element (i.e.,\ndocument.getElById(id).style) ...</div><div class='long'><p>Returns the style property for the DOM element (i.e.,\ndocument.getElById(id).style)</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the id of the elment to get</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The style property of the element</p>\n</div></li></ul></div></div></div><div id='method-getDDById' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-getDDById' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-getDDById' class='name expandable'>getDDById</a>( <span class='pre'>id</span> ) : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a></div><div class='description'><div class='short'>Returns the DragDrop instance for a given id ...</div><div class='long'><p>Returns the DragDrop instance for a given id</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the id of the DragDrop object</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a></span><div class='sub-desc'><p>the drag drop object, null if it is not found</p>\n</div></li></ul></div></div></div><div id='method-getElWrapper' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-getElWrapper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-getElWrapper' class='name expandable'>getElWrapper</a>( <span class='pre'>id</span> ) : <a href=\"#!/api/Ext.dd.DragDropManager.ElementWrapper\" rel=\"Ext.dd.DragDropManager.ElementWrapper\" class=\"docClass\">Ext.dd.DragDropManager.ElementWrapper</a><strong class='deprecated signature' >deprecated</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Get the wrapper for the DOM element specified ...</div><div class='long'><p>Get the wrapper for the DOM element specified</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>This wrapper isn't that useful</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the id of the element to get</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dd.DragDropManager.ElementWrapper\" rel=\"Ext.dd.DragDropManager.ElementWrapper\" class=\"docClass\">Ext.dd.DragDropManager.ElementWrapper</a></span><div class='sub-desc'><p>the wrapped element</p>\n</div></li></ul></div></div></div><div id='method-getElement' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-getElement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-getElement' class='name expandable'>getElement</a>( <span class='pre'>id</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Returns the actual DOM element ...</div><div class='long'><p>Returns the actual DOM element</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>use Ext.lib.Ext.getDom instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the id of the elment to get</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The element</p>\n</div></li></ul></div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-getLocation' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-getLocation' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-getLocation' class='name expandable'>getLocation</a>( <span class='pre'>oDD</span> ) : <a href=\"#!/api/Ext.util.Region\" rel=\"Ext.util.Region\" class=\"docClass\">Ext.util.Region</a></div><div class='description'><div class='short'>Returns a Region object containing the drag and drop element's position\nand size, including the padding configured fo...</div><div class='long'><p>Returns a Region object containing the drag and drop element's position\nand size, including the padding configured for it</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>oDD</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a><div class='sub-desc'><p>the drag and drop object to get the location for.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Region\" rel=\"Ext.util.Region\" class=\"docClass\">Ext.util.Region</a></span><div class='sub-desc'><p>a Region object representing the total area\nthe element occupies, including any padding\nthe instance is configured for.</p>\n</div></li></ul></div></div></div><div id='method-getRelated' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-getRelated' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-getRelated' class='name expandable'>getRelated</a>( <span class='pre'>p_oDD, bTargetsOnly</span> ) : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a>[]</div><div class='description'><div class='short'>Returns the drag and drop instances that are in all groups the\npassed in instance belongs to. ...</div><div class='long'><p>Returns the drag and drop instances that are in all groups the\npassed in instance belongs to.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>p_oDD</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a><div class='sub-desc'><p>the obj to get related data for</p>\n</div></li><li><span class='pre'>bTargetsOnly</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>if true, only return targetable objs</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a>[]</span><div class='sub-desc'><p>the related instances</p>\n</div></li></ul></div></div></div><div id='method-getZIndex' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-getZIndex' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-getZIndex' class='name expandable'>getZIndex</a>( <span class='pre'>element</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Collects the z-index of the passed element, looking up the parentNode axis to find an absolutely positioned ancestor\n...</div><div class='long'><p>Collects the z-index of the passed element, looking up the parentNode axis to find an absolutely positioned ancestor\nwhich is able to yield a z-index. If found to be not absolutely positionedm returns -1.</p>\n\n<p>This is used when sorting potential drop targets into z-index order so that only the topmost receives <code>over</code> and <code>drop</code> events.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>element</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The z-index of the element, or of its topmost absolutely positioned ancestor. Returns -1 if the element is not\nabsolutely positioned.</p>\n</div></li></ul></div></div></div><div id='method-handleMouseDown' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-handleMouseDown' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-handleMouseDown' class='name expandable'>handleMouseDown</a>( <span class='pre'>e, oDD</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Fired after a registered DragDrop object gets the mousedown event. ...</div><div class='long'><p>Fired after a registered DragDrop object gets the mousedown event.\nSets up the events required to track the object being dragged</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>the event</p>\n</div></li><li><span class='pre'>oDD</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a><div class='sub-desc'><p>the DragDrop object being dragged</p>\n</div></li></ul></div></div></div><div id='method-handleMouseMove' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-handleMouseMove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-handleMouseMove' class='name expandable'>handleMouseMove</a>( <span class='pre'>e</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Internal function to handle the mousemove event. ...</div><div class='long'><p>Internal function to handle the mousemove event.  Will be invoked\nfrom the context of the html element.</p>\n\n<p>TODO: figure out what we can do about mouse events lost when the\nuser drags objects beyond the window boundary.  Currently we can\ndetect this in internet explorer by verifying that the mouse is\ndown during the mousemove event.  Firefox doesn't give us the\nbutton state on the mousemove event.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>the event</p>\n</div></li></ul></div></div></div><div id='method-handleMouseUp' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-handleMouseUp' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-handleMouseUp' class='name expandable'>handleMouseUp</a>( <span class='pre'>e</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Internal function to handle the mouseup event. ...</div><div class='long'><p>Internal function to handle the mouseup event.  Will be invoked\nfrom the context of the document.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>the event</p>\n</div></li></ul></div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-init' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-init' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-init' class='name expandable'>init</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Called the first time an element is registered. ...</div><div class='long'><p>Called the first time an element is registered.</p>\n</div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-isDragDrop' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-isDragDrop' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-isDragDrop' class='name expandable'>isDragDrop</a>( <span class='pre'>id</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Utility function to determine if a given element has been\nregistered as a drag drop item. ...</div><div class='long'><p>Utility function to determine if a given element has been\nregistered as a drag drop item.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the element id to check</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>true if this element is a DragDrop item,\nfalse otherwise</p>\n</div></li></ul></div></div></div><div id='method-isHandle' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-isHandle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-isHandle' class='name expandable'>isHandle</a>( <span class='pre'>id</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Utility function to determine if a given element has been\nregistered as a drag drop handle for the given Drag Drop ob...</div><div class='long'><p>Utility function to determine if a given element has been\nregistered as a drag drop handle for the given Drag Drop object.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the element id to check</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>true if this element is a DragDrop handle, false\notherwise</p>\n</div></li></ul></div></div></div><div id='method-isLegalTarget' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-isLegalTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-isLegalTarget' class='name expandable'>isLegalTarget</a>( <span class='pre'>oDD, oTargetDD</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the specified dd target is a legal target for\nthe specifice drag obj ...</div><div class='long'><p>Returns true if the specified dd target is a legal target for\nthe specifice drag obj</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>oDD</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a><div class='sub-desc'><p>the drag obj</p>\n</div></li><li><span class='pre'>oTargetDD</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a><div class='sub-desc'><p>the target</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>true if the target is a legal target for the\ndd obj</p>\n</div></li></ul></div></div></div><div id='method-isLocked' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-isLocked' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-isLocked' class='name expandable'>isLocked</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Is drag and drop locked? ...</div><div class='long'><p>Is drag and drop locked?</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if drag and drop is locked, false otherwise.</p>\n</div></li></ul></div></div></div><div id='method-isOverTarget' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-isOverTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-isOverTarget' class='name expandable'>isOverTarget</a>( <span class='pre'>pt, oTarget</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Checks the cursor location to see if it over the target ...</div><div class='long'><p>Checks the cursor location to see if it over the target</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>pt</span> : <a href=\"#!/api/Ext.util.Point\" rel=\"Ext.util.Point\" class=\"docClass\">Ext.util.Point</a><div class='sub-desc'><p>The point to evaluate</p>\n</div></li><li><span class='pre'>oTarget</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a><div class='sub-desc'><p>the DragDrop object we are inspecting</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>true if the mouse is over the target</p>\n</div></li></ul></div></div></div><div id='method-isTypeOfDD' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-isTypeOfDD' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-isTypeOfDD' class='name expandable'>isTypeOfDD</a>( <span class='pre'>the</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>My goal is to be able to transparently determine if an object is\ntypeof DragDrop, and the exact subclass of DragDrop. ...</div><div class='long'><p>My goal is to be able to transparently determine if an object is\ntypeof DragDrop, and the exact subclass of DragDrop.  typeof\nreturns \"object\", oDD.constructor.toString() always returns\n\"DragDrop\" and not the name of the subclass.  So for now it just\nevaluates a well-known variable in DragDrop.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>the</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>object to evaluate</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>true if typeof oDD = DragDrop</p>\n</div></li></ul></div></div></div><div id='method-lock' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-lock' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-lock' class='name expandable'>lock</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Lock all drag and drop functionality ...</div><div class='long'><p>Lock all drag and drop functionality</p>\n</div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-refreshCache' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-refreshCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-refreshCache' class='name expandable'>refreshCache</a>( <span class='pre'>groups</span> )</div><div class='description'><div class='short'>Refreshes the cache of the top-left and bottom-right points of the\ndrag and drop objects in the specified group(s). ...</div><div class='long'><p>Refreshes the cache of the top-left and bottom-right points of the\ndrag and drop objects in the specified group(s).  This is in the\nformat that is stored in the drag and drop instance, so typical\nusage is:</p>\n\n<pre><code><a href=\"#!/api/Ext.dd.DragDropManager-method-refreshCache\" rel=\"Ext.dd.DragDropManager-method-refreshCache\" class=\"docClass\">Ext.dd.DragDropManager.refreshCache</a>(ddinstance.groups);\n</code></pre>\n\n<p>Alternatively:</p>\n\n<pre><code><a href=\"#!/api/Ext.dd.DragDropManager-method-refreshCache\" rel=\"Ext.dd.DragDropManager-method-refreshCache\" class=\"docClass\">Ext.dd.DragDropManager.refreshCache</a>({group1:true, group2:true});\n</code></pre>\n\n<p>TODO: this really should be an indexed array.  Alternatively this\nmethod could accept both.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>groups</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>an associative array of groups to refresh</p>\n</div></li></ul></div></div></div><div id='method-regDragDrop' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-regDragDrop' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-regDragDrop' class='name expandable'>regDragDrop</a>( <span class='pre'>oDD, sGroup</span> )</div><div class='description'><div class='short'>Each DragDrop instance must be registered with the DragDropManager. ...</div><div class='long'><p>Each DragDrop instance must be registered with the DragDropManager.\nThis is executed in DragDrop.init()</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>oDD</span> : <a href=\"#!/api/Ext.dd.DragDrop\" rel=\"Ext.dd.DragDrop\" class=\"docClass\">Ext.dd.DragDrop</a><div class='sub-desc'><p>the DragDrop object to register</p>\n</div></li><li><span class='pre'>sGroup</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the name of the group this element belongs to</p>\n</div></li></ul></div></div></div><div id='method-regHandle' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-regHandle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-regHandle' class='name expandable'>regHandle</a>( <span class='pre'>sDDId, sHandleId</span> )</div><div class='description'><div class='short'>Each DragDrop handle element must be registered. ...</div><div class='long'><p>Each DragDrop handle element must be registered.  This is done\nautomatically when executing DragDrop.setHandleElId()</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>sDDId</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the DragDrop id this element is a handle for</p>\n</div></li><li><span class='pre'>sHandleId</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the id of the element that is the drag\nhandle</p>\n</div></li></ul></div></div></div><div id='method-removeDDFromGroup' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-removeDDFromGroup' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-removeDDFromGroup' class='name expandable'>removeDDFromGroup</a>( <span class='pre'>oDD, sGroup</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Removes the supplied dd instance from the supplied group. ...</div><div class='long'><p>Removes the supplied dd instance from the supplied group. Executed\nby DragDrop.removeFromGroup, so don't call this function directly.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>oDD</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>sGroup</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-startDrag' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-startDrag' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-startDrag' class='name expandable'>startDrag</a>( <span class='pre'>x, y</span> )</div><div class='description'><div class='short'>Fired when either the drag pixel threshold or the mousedown hold\ntime threshold has been met. ...</div><div class='long'><p>Fired when either the drag pixel threshold or the mousedown hold\ntime threshold has been met.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>x</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the X position of the original mousedown</p>\n</div></li><li><span class='pre'>y</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the Y position of the original mousedown</p>\n</div></li></ul></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-stopDrag' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-stopDrag' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-stopDrag' class='name expandable'>stopDrag</a>( <span class='pre'>e</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Internal function to clean up event handlers after the drag\noperation is complete ...</div><div class='long'><p>Internal function to clean up event handlers after the drag\noperation is complete</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>the event</p>\n</div></li></ul></div></div></div><div id='method-stopEvent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-stopEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-stopEvent' class='name expandable'>stopEvent</a>( <span class='pre'>e</span> )</div><div class='description'><div class='short'>Utility to stop event propagation and event default, if these\nfeatures are turned on. ...</div><div class='long'><p>Utility to stop event propagation and event default, if these\nfeatures are turned on.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>e</span> : Event<div class='sub-desc'><p>the event as returned by this.getEvent()</p>\n</div></li></ul></div></div></div><div id='method-unlock' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-unlock' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-unlock' class='name expandable'>unlock</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Unlock all drag and drop functionality ...</div><div class='long'><p>Unlock all drag and drop functionality</p>\n</div></div></div><div id='method-unregAll' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-unregAll' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-unregAll' class='name expandable'>unregAll</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Cleans up the drag and drop events and objects. ...</div><div class='long'><p>Cleans up the drag and drop events and objects.</p>\n</div></div></div><div id='method-verifyEl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager'>Ext.dd.DragDropManager</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-method-verifyEl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager-method-verifyEl' class='name expandable'>verifyEl</a>( <span class='pre'>el</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>This checks to make sure an element exists and is in the DOM. ...</div><div class='long'><p>This checks to make sure an element exists and is in the DOM.  The\nmain purpose is to handle cases where innerHTML is used to remove\ndrag and drop objects from the DOM.  IE provides an 'unspecified\nerror' when trying to access the offsetParent of such an element</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : HTMLElement<div class='sub-desc'><p>the element to check</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>true if the element looks usable</p>\n</div></li></ul></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-create' class='name expandable'>create</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create a new instance of this Class. ...</div><div class='long'><p>Create a new instance of this Class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.create({\n    someConfig: true\n});\n</code></pre>\n\n<p>All parameters are passed to the constructor of the class.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the created instance.</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div></div></div>","superclasses":["Ext.Base"],"meta":{},"code_type":"ext_define","requires":["Ext.util.Region"],"html_meta":{},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"DragDropManager.html#Ext-dd-DragDropManager","filename":"DragDropManager.js"}],"linenr":10,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{},"name":"INTERSECT","id":"property-INTERSECT"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{},"name":"POINT","id":"property-POINT"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{},"name":"clickPixelThresh","id":"property-clickPixelThresh"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{},"name":"clickTimeThresh","id":"property-clickTimeThresh"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"clickTimeout","id":"property-clickTimeout"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"deltaX","id":"property-deltaX"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"deltaY","id":"property-deltaY"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{"readonly":true},"name":"dragCls","id":"property-dragCls"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"dragCurrent","id":"property-dragCurrent"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"dragOvers","id":"property-dragOvers"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"dragThreshMet","id":"property-dragThreshMet"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"elementCache","id":"property-elementCache"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"handleIds","id":"property-handleIds"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"ids","id":"property-ids"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"initialized","id":"property-initialized"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"locationCache","id":"property-locationCache"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"locked","id":"property-locked"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{},"name":"mode","id":"property-mode"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{},"name":"notifyOccluded","id":"property-notifyOccluded"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{},"name":"preventDefault","id":"property-preventDefault"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"startX","id":"property-startX"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"startY","id":"property-startY"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{},"name":"stopPropagation","id":"property-stopPropagation"},{"tagname":"property","owner":"Ext.dd.DragDropManager","meta":{},"name":"useCache","id":"property-useCache"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"_execOnAll","id":"method-_execOnAll"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"_onLoad","id":"method-_onLoad"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"_onResize","id":"method-_onResize"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"_onUnload","id":"method-_onUnload"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"_remove","id":"method-_remove"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"byZIndex","id":"method-byZIndex"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"fireEvents","id":"method-fireEvents"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"getBestMatch","id":"method-getBestMatch"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"getCss","id":"method-getCss"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"getDDById","id":"method-getDDById"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"private":true,"deprecated":{"text":"This wrapper isn't that useful"}},"name":"getElWrapper","id":"method-getElWrapper"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"deprecated":{"text":"use Ext.lib.Ext.getDom instead"}},"name":"getElement","id":"method-getElement"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"getLocation","id":"method-getLocation"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"getRelated","id":"method-getRelated"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"getZIndex","id":"method-getZIndex"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"handleMouseDown","id":"method-handleMouseDown"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"handleMouseMove","id":"method-handleMouseMove"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"handleMouseUp","id":"method-handleMouseUp"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"init","id":"method-init"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"isDragDrop","id":"method-isDragDrop"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"isHandle","id":"method-isHandle"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"isLegalTarget","id":"method-isLegalTarget"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"isLocked","id":"method-isLocked"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"isOverTarget","id":"method-isOverTarget"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"isTypeOfDD","id":"method-isTypeOfDD"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"lock","id":"method-lock"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"refreshCache","id":"method-refreshCache"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"regDragDrop","id":"method-regDragDrop"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"regHandle","id":"method-regHandle"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"removeDDFromGroup","id":"method-removeDDFromGroup"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"startDrag","id":"method-startDrag"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"stopDrag","id":"method-stopDrag"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"stopEvent","id":"method-stopEvent"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"unlock","id":"method-unlock"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{"private":true},"name":"unregAll","id":"method-unregAll"},{"tagname":"method","owner":"Ext.dd.DragDropManager","meta":{},"name":"verifyEl","id":"method-verifyEl"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.dd.DragDropManager","singleton":true,"override":null,"inheritdoc":null,"id":"class-Ext.dd.DragDropManager","mixins":[],"mixedInto":[]});