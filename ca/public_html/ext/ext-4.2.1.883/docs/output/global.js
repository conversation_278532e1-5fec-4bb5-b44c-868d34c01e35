Ext.data.JsonP.global({"alternateClassNames":[],"aliases":{},"parentMixins":[],"tagname":"class","uses":[],"subclasses":[],"html":"<div><div class='doc-contents'><p>Global variables and functions.</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-css_var'>CSS Variables</h3><div class='subsection'><div id='css_var-S-include-rtl' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='global'>global</span><br/><a href='source/Renderable.scss.html#global-css_var-S-include-rtl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/global-css_var-S-include-rtl' class='name expandable'>$include-rtl</a><span> : boolean</span></div><div class='description'><div class='short'>True to include right-to-left style rules. ...</div><div class='long'><p>True to include right-to-left style rules.  This variable gets set to true automatically\nfor rtl builds. You should not need to ever assign a value to this variable, however\nit can be used to suppress rtl-specific rules when they are not needed.  For example:</p>\n\n<pre><code>@if $include-rtl {\n    .x-rtl.foo {\n        margin-left: $margin-right;\n        margin-right: $margin-left;\n    }\n}\n</code></pre>\n\n<p>@member Global_CSS</p>\n<p>Defaults to: <code>true</code></p></div></div></div></div></div></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"","filename":""}],"members":{"property":[],"cfg":[],"css_var":[{"tagname":"css_var","owner":"global","meta":{},"name":"$include-rtl","id":"css_var-S-include-rtl"}],"method":[],"event":[],"css_mixin":[]},"component":false,"name":"global","mixins":[],"mixedInto":[]});