Ext.data.JsonP.Ext_enums_Plugin({"alternateClassNames":[],"aliases":{},"enum":{"type":"String","default":"plugin.*","doc_only":true},"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/enums.html#Ext-enums-Plugin' target='_blank'>enums.js</a></div></pre><div class='doc-contents'><p>Enumeration of all ptypes.</p>\n<p class='enum'><strong>ENUM:</strong> This enumeration defines a set of String values. It exists primarily for documentation purposes - in code use the actual string values like 'bufferedrenderer', don't reference them through this class like Ext.enums.Plugin.bufferedrenderer.</p></div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-bufferedrenderer' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Plugin'>Ext.enums.Plugin</span><br/><a href='source/enums.html#Ext-enums-Plugin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Plugin-property-bufferedrenderer' class='name expandable'>bufferedrenderer</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.plugin.BufferedRenderer. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.plugin.BufferedRenderer\" rel=\"Ext.grid.plugin.BufferedRenderer\" class=\"docClass\">Ext.grid.plugin.BufferedRenderer</a>.</p>\n<p>Defaults to: <code>'bufferedrenderer'</code></p></div></div></div><div id='property-celldragdrop' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Plugin'>Ext.enums.Plugin</span><br/><a href='source/enums.html#Ext-enums-Plugin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Plugin-property-celldragdrop' class='name expandable'>celldragdrop</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ux.CellDragDrop. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ux.CellDragDrop\" rel=\"Ext.ux.CellDragDrop\" class=\"docClass\">Ext.ux.CellDragDrop</a>.</p>\n<p>Defaults to: <code>'celldragdrop'</code></p></div></div></div><div id='property-cellediting' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Plugin'>Ext.enums.Plugin</span><br/><a href='source/enums.html#Ext-enums-Plugin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Plugin-property-cellediting' class='name expandable'>cellediting</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.plugin.CellEditing. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.plugin.CellEditing\" rel=\"Ext.grid.plugin.CellEditing\" class=\"docClass\">Ext.grid.plugin.CellEditing</a>.</p>\n<p>Defaults to: <code>'cellediting'</code></p></div></div></div><div id='property-gridheaderreorderer' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Plugin'>Ext.enums.Plugin</span><br/><a href='source/enums.html#Ext-enums-Plugin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Plugin-property-gridheaderreorderer' class='name expandable'>gridheaderreorderer</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.grid.plugin.HeaderReorderer. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.plugin.HeaderReorderer\" rel=\"Ext.grid.plugin.HeaderReorderer\" class=\"docClass\">Ext.grid.plugin.HeaderReorderer</a>.</p>\n<p>Defaults to: <code>'gridheaderreorderer'</code></p></div></div></div><div id='property-gridheaderresizer' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Plugin'>Ext.enums.Plugin</span><br/><a href='source/enums.html#Ext-enums-Plugin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Plugin-property-gridheaderresizer' class='name expandable'>gridheaderresizer</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.plugin.HeaderResizer. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.plugin.HeaderResizer\" rel=\"Ext.grid.plugin.HeaderResizer\" class=\"docClass\">Ext.grid.plugin.HeaderResizer</a>.</p>\n<p>Defaults to: <code>'gridheaderresizer'</code></p></div></div></div><div id='property-gridviewdragdrop' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Plugin'>Ext.enums.Plugin</span><br/><a href='source/enums.html#Ext-enums-Plugin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Plugin-property-gridviewdragdrop' class='name expandable'>gridviewdragdrop</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.plugin.DragDrop. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.plugin.DragDrop\" rel=\"Ext.grid.plugin.DragDrop\" class=\"docClass\">Ext.grid.plugin.DragDrop</a>.</p>\n<p>Defaults to: <code>'gridviewdragdrop'</code></p></div></div></div><div id='property-grouptabrenderer' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Plugin'>Ext.enums.Plugin</span><br/><a href='source/enums.html#Ext-enums-Plugin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Plugin-property-grouptabrenderer' class='name expandable'>grouptabrenderer</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ux.GroupTabRenderer. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ux.GroupTabRenderer\" rel=\"Ext.ux.GroupTabRenderer\" class=\"docClass\">Ext.ux.GroupTabRenderer</a>.</p>\n<p>Defaults to: <code>'grouptabrenderer'</code></p></div></div></div><div id='property-preview' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Plugin'>Ext.enums.Plugin</span><br/><a href='source/enums.html#Ext-enums-Plugin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Plugin-property-preview' class='name expandable'>preview</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ux.PreviewPlugin. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ux.PreviewPlugin\" rel=\"Ext.ux.PreviewPlugin\" class=\"docClass\">Ext.ux.PreviewPlugin</a>.</p>\n<p>Defaults to: <code>'preview'</code></p></div></div></div><div id='property-rowediting' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Plugin'>Ext.enums.Plugin</span><br/><a href='source/enums.html#Ext-enums-Plugin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Plugin-property-rowediting' class='name expandable'>rowediting</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.plugin.RowEditing. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.plugin.RowEditing\" rel=\"Ext.grid.plugin.RowEditing\" class=\"docClass\">Ext.grid.plugin.RowEditing</a>.</p>\n<p>Defaults to: <code>'rowediting'</code></p></div></div></div><div id='property-rowexpander' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Plugin'>Ext.enums.Plugin</span><br/><a href='source/enums.html#Ext-enums-Plugin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Plugin-property-rowexpander' class='name expandable'>rowexpander</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.plugin.RowExpander. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.plugin.RowExpander\" rel=\"Ext.grid.plugin.RowExpander\" class=\"docClass\">Ext.grid.plugin.RowExpander</a>.</p>\n<p>Defaults to: <code>'rowexpander'</code></p></div></div></div><div id='property-tabclosemenu' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Plugin'>Ext.enums.Plugin</span><br/><a href='source/enums.html#Ext-enums-Plugin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Plugin-property-tabclosemenu' class='name expandable'>tabclosemenu</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ux.TabCloseMenu. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ux.TabCloseMenu\" rel=\"Ext.ux.TabCloseMenu\" class=\"docClass\">Ext.ux.TabCloseMenu</a>.</p>\n<p>Defaults to: <code>'tabclosemenu'</code></p></div></div></div><div id='property-tabscrollermenu' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Plugin'>Ext.enums.Plugin</span><br/><a href='source/enums.html#Ext-enums-Plugin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Plugin-property-tabscrollermenu' class='name expandable'>tabscrollermenu</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ux.TabScrollerMenu. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ux.TabScrollerMenu\" rel=\"Ext.ux.TabScrollerMenu\" class=\"docClass\">Ext.ux.TabScrollerMenu</a>.</p>\n<p>Defaults to: <code>'tabscrollermenu'</code></p></div></div></div><div id='property-treeviewdragdrop' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Plugin'>Ext.enums.Plugin</span><br/><a href='source/enums.html#Ext-enums-Plugin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Plugin-property-treeviewdragdrop' class='name expandable'>treeviewdragdrop</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.tree.plugin.TreeViewDragDrop. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.tree.plugin.TreeViewDragDrop\" rel=\"Ext.tree.plugin.TreeViewDragDrop\" class=\"docClass\">Ext.tree.plugin.TreeViewDragDrop</a>.</p>\n<p>Defaults to: <code>'treeviewdragdrop'</code></p></div></div></div></div></div></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"enums.html#Ext-enums-Plugin","filename":"enums.js"}],"linenr":15,"members":{"property":[{"tagname":"property","owner":"Ext.enums.Plugin","meta":{"private":null},"name":"bufferedrenderer","id":"property-bufferedrenderer"},{"tagname":"property","owner":"Ext.enums.Plugin","meta":{"private":null},"name":"celldragdrop","id":"property-celldragdrop"},{"tagname":"property","owner":"Ext.enums.Plugin","meta":{"private":null},"name":"cellediting","id":"property-cellediting"},{"tagname":"property","owner":"Ext.enums.Plugin","meta":{"private":true},"name":"gridheaderreorderer","id":"property-gridheaderreorderer"},{"tagname":"property","owner":"Ext.enums.Plugin","meta":{"private":null},"name":"gridheaderresizer","id":"property-gridheaderresizer"},{"tagname":"property","owner":"Ext.enums.Plugin","meta":{"private":null},"name":"gridviewdragdrop","id":"property-gridviewdragdrop"},{"tagname":"property","owner":"Ext.enums.Plugin","meta":{"private":null},"name":"grouptabrenderer","id":"property-grouptabrenderer"},{"tagname":"property","owner":"Ext.enums.Plugin","meta":{"private":null},"name":"preview","id":"property-preview"},{"tagname":"property","owner":"Ext.enums.Plugin","meta":{"private":null},"name":"rowediting","id":"property-rowediting"},{"tagname":"property","owner":"Ext.enums.Plugin","meta":{"private":null},"name":"rowexpander","id":"property-rowexpander"},{"tagname":"property","owner":"Ext.enums.Plugin","meta":{"private":null},"name":"tabclosemenu","id":"property-tabclosemenu"},{"tagname":"property","owner":"Ext.enums.Plugin","meta":{"private":null},"name":"tabscrollermenu","id":"property-tabscrollermenu"},{"tagname":"property","owner":"Ext.enums.Plugin","meta":{"private":null},"name":"treeviewdragdrop","id":"property-treeviewdragdrop"}],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.enums.Plugin","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.enums.Plugin","mixins":[],"mixedInto":[]});