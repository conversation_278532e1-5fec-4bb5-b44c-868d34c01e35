Ext.data.JsonP.Ext_enums_Layout({"alternateClassNames":[],"aliases":{},"enum":{"type":"String","default":"layout.*","doc_only":true},"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/enums.html#Ext-enums-Layout' target='_blank'>enums.js</a></div></pre><div class='doc-contents'><p>Enumeration of all layout types.</p>\n<p class='enum'><strong>ENUM:</strong> This enumeration defines a set of String values. It exists primarily for documentation purposes - in code use the actual string values like 'absolute', don't reference them through this class like Ext.enums.Layout.absolute.</p></div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-absolute' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-absolute' class='name expandable'>absolute</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.layout.container.Absolute. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.container.Absolute\" rel=\"Ext.layout.container.Absolute\" class=\"docClass\">Ext.layout.container.Absolute</a>.</p>\n<p>Defaults to: <code>'absolute'</code></p></div></div></div><div id='property-accordion' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-accordion' class='name expandable'>accordion</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.layout.container.Accordion. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.container.Accordion\" rel=\"Ext.layout.container.Accordion\" class=\"docClass\">Ext.layout.container.Accordion</a>.</p>\n<p>Defaults to: <code>'accordion'</code></p></div></div></div><div id='property-anchor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-anchor' class='name expandable'>anchor</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.layout.container.Anchor. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.container.Anchor\" rel=\"Ext.layout.container.Anchor\" class=\"docClass\">Ext.layout.container.Anchor</a>.</p>\n<p>Defaults to: <code>'anchor'</code></p></div></div></div><div id='property-auto' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-auto' class='name expandable'>auto</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.layout.container.Auto. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.container.Auto\" rel=\"Ext.layout.container.Auto\" class=\"docClass\">Ext.layout.container.Auto</a>.</p>\n<p>Defaults to: <code>'auto'</code></p></div></div></div><div id='property-autocomponent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-autocomponent' class='name expandable'>autocomponent</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.layout.component.Auto. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.component.Auto\" rel=\"Ext.layout.component.Auto\" class=\"docClass\">Ext.layout.component.Auto</a>.</p>\n<p>Defaults to: <code>'autocomponent'</code></p></div></div></div><div id='property-autocontainer' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-autocontainer' class='name expandable'>autocontainer</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.layout.container.Auto. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.container.Auto\" rel=\"Ext.layout.container.Auto\" class=\"docClass\">Ext.layout.container.Auto</a>.</p>\n<p>Defaults to: <code>'autocontainer'</code></p></div></div></div><div id='property-body' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-body' class='name expandable'>body</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.layout.component.Body. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.component.Body\" rel=\"Ext.layout.component.Body\" class=\"docClass\">Ext.layout.component.Body</a>.</p>\n<p>Defaults to: <code>'body'</code></p></div></div></div><div id='property-border' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-border' class='name expandable'>border</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.layout.container.Border. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.container.Border\" rel=\"Ext.layout.container.Border\" class=\"docClass\">Ext.layout.container.Border</a>.</p>\n<p>Defaults to: <code>'border'</code></p></div></div></div><div id='property-boundlist' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-boundlist' class='name expandable'>boundlist</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.layout.component.BoundList. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.component.BoundList\" rel=\"Ext.layout.component.BoundList\" class=\"docClass\">Ext.layout.component.BoundList</a>.</p>\n<p>Defaults to: <code>'boundlist'</code></p></div></div></div><div id='property-box' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-box' class='name expandable'>box</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.layout.container.Box. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.container.Box\" rel=\"Ext.layout.container.Box\" class=\"docClass\">Ext.layout.container.Box</a>.</p>\n<p>Defaults to: <code>'box'</code></p></div></div></div><div id='property-button' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-button' class='name expandable'>button</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.layout.component.Button. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.component.Button\" rel=\"Ext.layout.component.Button\" class=\"docClass\">Ext.layout.component.Button</a>.</p>\n<p>Defaults to: <code>'button'</code></p></div></div></div><div id='property-card' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-card' class='name expandable'>card</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.layout.container.Card. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.container.Card\" rel=\"Ext.layout.container.Card\" class=\"docClass\">Ext.layout.container.Card</a>.</p>\n<p>Defaults to: <code>'card'</code></p></div></div></div><div id='property-checkboxgroup' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-checkboxgroup' class='name expandable'>checkboxgroup</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.layout.container.CheckboxGroup. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.container.CheckboxGroup\" rel=\"Ext.layout.container.CheckboxGroup\" class=\"docClass\">Ext.layout.container.CheckboxGroup</a>.</p>\n<p>Defaults to: <code>'checkboxgroup'</code></p></div></div></div><div id='property-column' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-column' class='name expandable'>column</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.layout.container.Column. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.container.Column\" rel=\"Ext.layout.container.Column\" class=\"docClass\">Ext.layout.container.Column</a>.</p>\n<p>Defaults to: <code>'column'</code></p></div></div></div><div id='property-columncomponent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-columncomponent' class='name expandable'>columncomponent</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.grid.ColumnComponentLayout. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.ColumnComponentLayout\" rel=\"Ext.grid.ColumnComponentLayout\" class=\"docClass\">Ext.grid.ColumnComponentLayout</a>.</p>\n<p>Defaults to: <code>'columncomponent'</code></p></div></div></div><div id='property-combobox' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-combobox' class='name expandable'>combobox</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.layout.component.field.ComboBox. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.component.field.ComboBox\" rel=\"Ext.layout.component.field.ComboBox\" class=\"docClass\">Ext.layout.component.field.ComboBox</a>.</p>\n<p>Defaults to: <code>'combobox'</code></p></div></div></div><div id='property-container' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-container' class='name expandable'>container</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.layout.container.Container. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.container.Container\" rel=\"Ext.layout.container.Container\" class=\"docClass\">Ext.layout.container.Container</a>.</p>\n<p>Defaults to: <code>'container'</code></p></div></div></div><div id='property-dock' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-dock' class='name expandable'>dock</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.layout.component.Dock. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.component.Dock\" rel=\"Ext.layout.component.Dock\" class=\"docClass\">Ext.layout.component.Dock</a>.</p>\n<p>Defaults to: <code>'dock'</code></p></div></div></div><div id='property-draw' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-draw' class='name expandable'>draw</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.layout.component.Draw. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.component.Draw\" rel=\"Ext.layout.component.Draw\" class=\"docClass\">Ext.layout.component.Draw</a>.</p>\n<p>Defaults to: <code>'draw'</code></p></div></div></div><div id='property-editor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-editor' class='name expandable'>editor</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.layout.container.Editor. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.container.Editor\" rel=\"Ext.layout.container.Editor\" class=\"docClass\">Ext.layout.container.Editor</a>.</p>\n<p>Defaults to: <code>'editor'</code></p></div></div></div><div id='property-field' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-field' class='name expandable'>field</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.layout.component.field.Field. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.component.field.Field\" rel=\"Ext.layout.component.field.Field\" class=\"docClass\">Ext.layout.component.field.Field</a>.</p>\n<p>Defaults to: <code>'field'</code></p></div></div></div><div id='property-fieldcontainer' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-fieldcontainer' class='name expandable'>fieldcontainer</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.layout.component.field.FieldContainer. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.component.field.FieldContainer\" rel=\"Ext.layout.component.field.FieldContainer\" class=\"docClass\">Ext.layout.component.field.FieldContainer</a>.</p>\n<p>Defaults to: <code>'fieldcontainer'</code></p></div></div></div><div id='property-fieldset' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-fieldset' class='name expandable'>fieldset</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.layout.component.FieldSet. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.component.FieldSet\" rel=\"Ext.layout.component.FieldSet\" class=\"docClass\">Ext.layout.component.FieldSet</a>.</p>\n<p>Defaults to: <code>'fieldset'</code></p></div></div></div><div id='property-fit' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-fit' class='name expandable'>fit</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.layout.container.Fit. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.container.Fit\" rel=\"Ext.layout.container.Fit\" class=\"docClass\">Ext.layout.container.Fit</a>.</p>\n<p>Defaults to: <code>'fit'</code></p></div></div></div><div id='property-form' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-form' class='name expandable'>form</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.layout.container.Form. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.container.Form\" rel=\"Ext.layout.container.Form\" class=\"docClass\">Ext.layout.container.Form</a>.</p>\n<p>Defaults to: <code>'form'</code></p></div></div></div><div id='property-gridcolumn' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-gridcolumn' class='name expandable'>gridcolumn</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.grid.ColumnLayout. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.ColumnLayout\" rel=\"Ext.grid.ColumnLayout\" class=\"docClass\">Ext.grid.ColumnLayout</a>.</p>\n<p>Defaults to: <code>'gridcolumn'</code></p></div></div></div><div id='property-hbox' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-hbox' class='name expandable'>hbox</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.layout.container.HBox. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.container.HBox\" rel=\"Ext.layout.container.HBox\" class=\"docClass\">Ext.layout.container.HBox</a>.</p>\n<p>Defaults to: <code>'hbox'</code></p></div></div></div><div id='property-htmleditor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-htmleditor' class='name expandable'>htmleditor</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.layout.component.field.HtmlEditor. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.component.field.HtmlEditor\" rel=\"Ext.layout.component.field.HtmlEditor\" class=\"docClass\">Ext.layout.component.field.HtmlEditor</a>.</p>\n<p>Defaults to: <code>'htmleditor'</code></p></div></div></div><div id='property-progressbar' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-progressbar' class='name expandable'>progressbar</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.layout.component.ProgressBar. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.component.ProgressBar\" rel=\"Ext.layout.component.ProgressBar\" class=\"docClass\">Ext.layout.component.ProgressBar</a>.</p>\n<p>Defaults to: <code>'progressbar'</code></p></div></div></div><div id='property-sliderfield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-sliderfield' class='name expandable'>sliderfield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.layout.component.field.Slider. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.component.field.Slider\" rel=\"Ext.layout.component.field.Slider\" class=\"docClass\">Ext.layout.component.field.Slider</a>.</p>\n<p>Defaults to: <code>'sliderfield'</code></p></div></div></div><div id='property-table' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-table' class='name expandable'>table</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.layout.container.Table. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.container.Table\" rel=\"Ext.layout.container.Table\" class=\"docClass\">Ext.layout.container.Table</a>.</p>\n<p>Defaults to: <code>'table'</code></p></div></div></div><div id='property-tableview' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-tableview' class='name expandable'>tableview</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.view.TableLayout. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.view.TableLayout\" rel=\"Ext.view.TableLayout\" class=\"docClass\">Ext.view.TableLayout</a>.</p>\n<p>Defaults to: <code>'tableview'</code></p></div></div></div><div id='property-textareafield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-textareafield' class='name expandable'>textareafield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.layout.component.field.TextArea. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.component.field.TextArea\" rel=\"Ext.layout.component.field.TextArea\" class=\"docClass\">Ext.layout.component.field.TextArea</a>.</p>\n<p>Defaults to: <code>'textareafield'</code></p></div></div></div><div id='property-textfield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-textfield' class='name expandable'>textfield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.layout.component.field.Text. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.component.field.Text\" rel=\"Ext.layout.component.field.Text\" class=\"docClass\">Ext.layout.component.field.Text</a>.</p>\n<p>Defaults to: <code>'textfield'</code></p></div></div></div><div id='property-triggerfield' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-triggerfield' class='name expandable'>triggerfield</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.layout.component.field.Trigger. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.component.field.Trigger\" rel=\"Ext.layout.component.field.Trigger\" class=\"docClass\">Ext.layout.component.field.Trigger</a>.</p>\n<p>Defaults to: <code>'triggerfield'</code></p></div></div></div><div id='property-ux.center' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-ux.center' class='name expandable'>ux.center</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ux.layout.Center. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ux.layout.Center\" rel=\"Ext.ux.layout.Center\" class=\"docClass\">Ext.ux.layout.Center</a>.</p>\n<p>Defaults to: <code>'ux.center'</code></p></div></div></div><div id='property-vbox' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Layout'>Ext.enums.Layout</span><br/><a href='source/enums.html#Ext-enums-Layout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Layout-property-vbox' class='name expandable'>vbox</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.layout.container.VBox. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.layout.container.VBox\" rel=\"Ext.layout.container.VBox\" class=\"docClass\">Ext.layout.container.VBox</a>.</p>\n<p>Defaults to: <code>'vbox'</code></p></div></div></div></div></div></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"enums.html#Ext-enums-Layout","filename":"enums.js"}],"linenr":5,"members":{"property":[{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"absolute","id":"property-absolute"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"accordion","id":"property-accordion"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"anchor","id":"property-anchor"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"auto","id":"property-auto"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"autocomponent","id":"property-autocomponent"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"autocontainer","id":"property-autocontainer"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"body","id":"property-body"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"border","id":"property-border"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"boundlist","id":"property-boundlist"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"box","id":"property-box"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"button","id":"property-button"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"card","id":"property-card"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"checkboxgroup","id":"property-checkboxgroup"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"column","id":"property-column"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"columncomponent","id":"property-columncomponent"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"combobox","id":"property-combobox"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"container","id":"property-container"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"dock","id":"property-dock"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"draw","id":"property-draw"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"editor","id":"property-editor"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"field","id":"property-field"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"fieldcontainer","id":"property-fieldcontainer"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"fieldset","id":"property-fieldset"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"fit","id":"property-fit"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"form","id":"property-form"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"gridcolumn","id":"property-gridcolumn"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"hbox","id":"property-hbox"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"htmleditor","id":"property-htmleditor"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"progressbar","id":"property-progressbar"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"sliderfield","id":"property-sliderfield"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"table","id":"property-table"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"tableview","id":"property-tableview"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"textareafield","id":"property-textareafield"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"textfield","id":"property-textfield"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":true},"name":"triggerfield","id":"property-triggerfield"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"ux.center","id":"property-ux.center"},{"tagname":"property","owner":"Ext.enums.Layout","meta":{"private":null},"name":"vbox","id":"property-vbox"}],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.enums.Layout","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.enums.Layout","mixins":[],"mixedInto":[]});