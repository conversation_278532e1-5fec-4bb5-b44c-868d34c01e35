Ext.data.JsonP.Ext_EventObject({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":"Ext.Base","uses":["Ext.util.Point"],"html":"<div><pre class=\"hierarchy\"><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><strong>Ext.EventObject</strong></div></div><h4>Uses</h4><div class='dependency'><a href='#!/api/Ext.util.Point' rel='Ext.util.Point' class='docClass'>Ext.util.Point</a></div><h4>Files</h4><div class='dependency'><a href='source/EventObject.html#Ext-EventObject' target='_blank'>EventObject.js</a></div></pre><div class='doc-contents'><p>Just as <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a> wraps around a native DOM node, <a href=\"#!/api/Ext.EventObject\" rel=\"Ext.EventObject\" class=\"docClass\">Ext.EventObject</a>\nwraps the browser's native event-object normalizing cross-browser differences,\nsuch as which mouse button is clicked, keys pressed, mechanisms to stop\nevent-propagation along with a method to prevent default actions from taking place.</p>\n\n<p>For example:</p>\n\n<pre><code>function handleClick(e, t){ // e is not a standard event object, it is a <a href=\"#!/api/Ext.EventObject\" rel=\"Ext.EventObject\" class=\"docClass\">Ext.EventObject</a>\n    e.preventDefault();\n    var target = e.getTarget(); // same as t (the target HTMLElement)\n    ...\n}\n\nvar myDiv = <a href=\"#!/api/Ext-method-get\" rel=\"Ext-method-get\" class=\"docClass\">Ext.get</a>(\"myDiv\");  // get reference to an <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>\nmyDiv.on(         // 'on' is shorthand for addListener\n    \"click\",      // perform an action on click of myDiv\n    handleClick   // reference to the action handler\n);\n\n// other methods to do the same:\n<a href=\"#!/api/Ext.EventManager-method-on\" rel=\"Ext.EventManager-method-on\" class=\"docClass\">Ext.EventManager.on</a>(\"myDiv\", 'click', handleClick);\n<a href=\"#!/api/Ext.EventManager-method-addListener\" rel=\"Ext.EventManager-method-addListener\" class=\"docClass\">Ext.EventManager.addListener</a>(\"myDiv\", 'click', handleClick);\n</code></pre>\n</div><div class='members'><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-A' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-A' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-A' class='name expandable'>A</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>65</code></p></div></div></div><div id='property-ALT' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-ALT' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-ALT' class='name expandable'>ALT</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>18</code></p></div></div></div><div id='property-B' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-B' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-B' class='name expandable'>B</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>66</code></p></div></div></div><div id='property-BACKSPACE' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-BACKSPACE' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-BACKSPACE' class='name expandable'>BACKSPACE</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>8</code></p></div></div></div><div id='property-C' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-C' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-C' class='name expandable'>C</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>67</code></p></div></div></div><div id='property-CAPS_LOCK' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-CAPS_LOCK' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-CAPS_LOCK' class='name expandable'>CAPS_LOCK</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>20</code></p></div></div></div><div id='property-CONTEXT_MENU' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-CONTEXT_MENU' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-CONTEXT_MENU' class='name expandable'>CONTEXT_MENU</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>93</code></p></div></div></div><div id='property-CTRL' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-CTRL' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-CTRL' class='name expandable'>CTRL</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>17</code></p></div></div></div><div id='property-D' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-D' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-D' class='name expandable'>D</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>68</code></p></div></div></div><div id='property-DELETE' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-DELETE' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-DELETE' class='name expandable'>DELETE</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>46</code></p></div></div></div><div id='property-DOWN' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-DOWN' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-DOWN' class='name expandable'>DOWN</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>40</code></p></div></div></div><div id='property-E' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-E' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-E' class='name expandable'>E</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>69</code></p></div></div></div><div id='property-EIGHT' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-EIGHT' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-EIGHT' class='name expandable'>EIGHT</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>56</code></p></div></div></div><div id='property-END' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-END' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-END' class='name expandable'>END</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>35</code></p></div></div></div><div id='property-ENTER' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-ENTER' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-ENTER' class='name expandable'>ENTER</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>13</code></p></div></div></div><div id='property-ESC' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-ESC' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-ESC' class='name expandable'>ESC</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>27</code></p></div></div></div><div id='property-F' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-F' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-F' class='name expandable'>F</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>70</code></p></div></div></div><div id='property-F1' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-F1' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-F1' class='name expandable'>F1</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>112</code></p></div></div></div><div id='property-F10' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-F10' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-F10' class='name expandable'>F10</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>121</code></p></div></div></div><div id='property-F11' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-F11' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-F11' class='name expandable'>F11</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>122</code></p></div></div></div><div id='property-F12' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-F12' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-F12' class='name expandable'>F12</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>123</code></p></div></div></div><div id='property-F2' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-F2' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-F2' class='name expandable'>F2</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>113</code></p></div></div></div><div id='property-F3' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-F3' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-F3' class='name expandable'>F3</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>114</code></p></div></div></div><div id='property-F4' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-F4' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-F4' class='name expandable'>F4</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>115</code></p></div></div></div><div id='property-F5' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-F5' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-F5' class='name expandable'>F5</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>116</code></p></div></div></div><div id='property-F6' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-F6' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-F6' class='name expandable'>F6</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>117</code></p></div></div></div><div id='property-F7' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-F7' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-F7' class='name expandable'>F7</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>118</code></p></div></div></div><div id='property-F8' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-F8' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-F8' class='name expandable'>F8</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>119</code></p></div></div></div><div id='property-F9' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-F9' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-F9' class='name expandable'>F9</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>120</code></p></div></div></div><div id='property-FIVE' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-FIVE' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-FIVE' class='name expandable'>FIVE</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>53</code></p></div></div></div><div id='property-FOUR' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-FOUR' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-FOUR' class='name expandable'>FOUR</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>52</code></p></div></div></div><div id='property-G' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-G' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-G' class='name expandable'>G</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>71</code></p></div></div></div><div id='property-H' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-H' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-H' class='name expandable'>H</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>72</code></p></div></div></div><div id='property-HOME' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-HOME' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-HOME' class='name expandable'>HOME</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>36</code></p></div></div></div><div id='property-I' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-I' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-I' class='name expandable'>I</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>73</code></p></div></div></div><div id='property-INSERT' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-INSERT' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-INSERT' class='name expandable'>INSERT</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>45</code></p></div></div></div><div id='property-J' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-J' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-J' class='name expandable'>J</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>74</code></p></div></div></div><div id='property-K' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-K' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-K' class='name expandable'>K</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>75</code></p></div></div></div><div id='property-L' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-L' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-L' class='name expandable'>L</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>76</code></p></div></div></div><div id='property-LEFT' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-LEFT' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-LEFT' class='name expandable'>LEFT</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>37</code></p></div></div></div><div id='property-M' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-M' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-M' class='name expandable'>M</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>77</code></p></div></div></div><div id='property-N' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-N' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-N' class='name expandable'>N</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>78</code></p></div></div></div><div id='property-NINE' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-NINE' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-NINE' class='name expandable'>NINE</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>57</code></p></div></div></div><div id='property-NUM_CENTER' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-NUM_CENTER' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-NUM_CENTER' class='name expandable'>NUM_CENTER</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>12</code></p></div></div></div><div id='property-NUM_DIVISION' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-NUM_DIVISION' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-NUM_DIVISION' class='name expandable'>NUM_DIVISION</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>111</code></p></div></div></div><div id='property-NUM_EIGHT' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-NUM_EIGHT' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-NUM_EIGHT' class='name expandable'>NUM_EIGHT</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>104</code></p></div></div></div><div id='property-NUM_FIVE' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-NUM_FIVE' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-NUM_FIVE' class='name expandable'>NUM_FIVE</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>101</code></p></div></div></div><div id='property-NUM_FOUR' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-NUM_FOUR' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-NUM_FOUR' class='name expandable'>NUM_FOUR</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>100</code></p></div></div></div><div id='property-NUM_MINUS' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-NUM_MINUS' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-NUM_MINUS' class='name expandable'>NUM_MINUS</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>109</code></p></div></div></div><div id='property-NUM_MULTIPLY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-NUM_MULTIPLY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-NUM_MULTIPLY' class='name expandable'>NUM_MULTIPLY</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>106</code></p></div></div></div><div id='property-NUM_NINE' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-NUM_NINE' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-NUM_NINE' class='name expandable'>NUM_NINE</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>105</code></p></div></div></div><div id='property-NUM_ONE' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-NUM_ONE' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-NUM_ONE' class='name expandable'>NUM_ONE</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>97</code></p></div></div></div><div id='property-NUM_PERIOD' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-NUM_PERIOD' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-NUM_PERIOD' class='name expandable'>NUM_PERIOD</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>110</code></p></div></div></div><div id='property-NUM_PLUS' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-NUM_PLUS' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-NUM_PLUS' class='name expandable'>NUM_PLUS</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>107</code></p></div></div></div><div id='property-NUM_SEVEN' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-NUM_SEVEN' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-NUM_SEVEN' class='name expandable'>NUM_SEVEN</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>103</code></p></div></div></div><div id='property-NUM_SIX' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-NUM_SIX' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-NUM_SIX' class='name expandable'>NUM_SIX</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>102</code></p></div></div></div><div id='property-NUM_THREE' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-NUM_THREE' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-NUM_THREE' class='name expandable'>NUM_THREE</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>99</code></p></div></div></div><div id='property-NUM_TWO' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-NUM_TWO' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-NUM_TWO' class='name expandable'>NUM_TWO</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>98</code></p></div></div></div><div id='property-NUM_ZERO' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-NUM_ZERO' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-NUM_ZERO' class='name expandable'>NUM_ZERO</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>96</code></p></div></div></div><div id='property-O' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-O' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-O' class='name expandable'>O</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>79</code></p></div></div></div><div id='property-ONE' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-ONE' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-ONE' class='name expandable'>ONE</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>49</code></p></div></div></div><div id='property-P' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-P' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-P' class='name expandable'>P</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>80</code></p></div></div></div><div id='property-PAGE_DOWN' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-PAGE_DOWN' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-PAGE_DOWN' class='name expandable'>PAGE_DOWN</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>34</code></p></div></div></div><div id='property-PAGE_UP' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-PAGE_UP' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-PAGE_UP' class='name expandable'>PAGE_UP</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>33</code></p></div></div></div><div id='property-PAUSE' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-PAUSE' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-PAUSE' class='name expandable'>PAUSE</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>19</code></p></div></div></div><div id='property-PRINT_SCREEN' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-PRINT_SCREEN' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-PRINT_SCREEN' class='name expandable'>PRINT_SCREEN</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>44</code></p></div></div></div><div id='property-Q' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-Q' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-Q' class='name expandable'>Q</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>81</code></p></div></div></div><div id='property-R' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-R' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-R' class='name expandable'>R</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>82</code></p></div></div></div><div id='property-RETURN' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-RETURN' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-RETURN' class='name expandable'>RETURN</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>13</code></p></div></div></div><div id='property-RIGHT' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-RIGHT' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-RIGHT' class='name expandable'>RIGHT</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>39</code></p></div></div></div><div id='property-S' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-S' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-S' class='name expandable'>S</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>83</code></p></div></div></div><div id='property-SEVEN' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-SEVEN' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-SEVEN' class='name expandable'>SEVEN</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>55</code></p></div></div></div><div id='property-SHIFT' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-SHIFT' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-SHIFT' class='name expandable'>SHIFT</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>16</code></p></div></div></div><div id='property-SIX' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-SIX' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-SIX' class='name expandable'>SIX</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>54</code></p></div></div></div><div id='property-SPACE' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-SPACE' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-SPACE' class='name expandable'>SPACE</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>32</code></p></div></div></div><div id='property-T' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-T' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-T' class='name expandable'>T</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>84</code></p></div></div></div><div id='property-TAB' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-TAB' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-TAB' class='name expandable'>TAB</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>9</code></p></div></div></div><div id='property-THREE' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-THREE' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-THREE' class='name expandable'>THREE</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>51</code></p></div></div></div><div id='property-TWO' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-TWO' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-TWO' class='name expandable'>TWO</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>50</code></p></div></div></div><div id='property-U' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-U' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-U' class='name expandable'>U</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>85</code></p></div></div></div><div id='property-UP' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-UP' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-UP' class='name expandable'>UP</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>38</code></p></div></div></div><div id='property-V' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-V' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-V' class='name expandable'>V</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>86</code></p></div></div></div><div id='property-W' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-W' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-W' class='name expandable'>W</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>87</code></p></div></div></div><div id='property-WHEEL_SCALE' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-WHEEL_SCALE' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-WHEEL_SCALE' class='name expandable'>WHEEL_SCALE</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The mouse wheel delta scaling factor. ...</div><div class='long'><p>The mouse wheel delta scaling factor. This value depends on browser version and OS and\nattempts to produce a similar scrolling experience across all platforms and browsers.</p>\n\n<p>To change this value:</p>\n\n<pre><code> Ext.EventObjectImpl.prototype.WHEEL_SCALE = 72;\n</code></pre>\n</div></div></div><div id='property-X' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-X' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-X' class='name expandable'>X</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>88</code></p></div></div></div><div id='property-Y' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-Y' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-Y' class='name expandable'>Y</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>89</code></p></div></div></div><div id='property-Z' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-Z' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-Z' class='name expandable'>Z</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>90</code></p></div></div></div><div id='property-ZERO' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-ZERO' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-ZERO' class='name expandable'>ZERO</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Key constant ...</div><div class='long'><p>Key constant</p>\n<p>Defaults to: <code>48</code></p></div></div></div><div id='property-altKey' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-altKey' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-altKey' class='name not-expandable'>altKey</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the alt key was down during the event.</p>\n</div><div class='long'><p>True if the alt key was down during the event.</p>\n</div></div></div><div id='property-btnMap' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-btnMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-btnMap' class='name not-expandable'>btnMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'><p>normalize button clicks, don't see any way to feature detect this.</p>\n</div><div class='long'><p>normalize button clicks, don't see any way to feature detect this.</p>\n</div></div></div><div id='property-clickRe' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-clickRe' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-clickRe' class='name expandable'>clickRe</a><span> : <a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Simple click regex ...</div><div class='long'><p>Simple click regex</p>\n<p>Defaults to: <code>/(dbl)?click/</code></p></div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-ctrlKey' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-ctrlKey' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-ctrlKey' class='name expandable'>ctrlKey</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True if the control key was down during the event. ...</div><div class='long'><p>True if the control key was down during the event.\nIn Mac this will also be true when meta key was down.</p>\n</div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-safariKeys' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-safariKeys' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-safariKeys' class='name expandable'>safariKeys</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>safari keypress events for special keys return bad keycodes ...</div><div class='long'><p>safari keypress events for special keys return bad keycodes</p>\n<p>Defaults to: <code>{3: 13, 63234: 37, 63235: 39, 63232: 38, 63233: 40, 63276: 33, 63277: 34, 63272: 46, 63273: 36, 63275: 35}</code></p></div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div><div id='property-shiftKey' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-property-shiftKey' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-property-shiftKey' class='name not-expandable'>shiftKey</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the shift key was down during the event.</p>\n</div><div class='long'><p>True if the shift key was down during the event.</p>\n</div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-callOverridden' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-correctWheelDelta' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-correctWheelDelta' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-correctWheelDelta' class='name expandable'>correctWheelDelta</a>( <span class='pre'>delta</span> )</div><div class='description'><div class='short'>Correctly scales a given wheel delta. ...</div><div class='long'><p>Correctly scales a given wheel delta.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>delta</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The delta value.</p>\n</div></li></ul></div></div></div><div id='method-destroy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-destroy' class='name expandable'>destroy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.util.ElementContainer-method-destroy' rel='Ext.util.ElementContainer-method-destroy' class='docClass'>Ext.util.ElementContainer.destroy</a>, <a href='#!/api/Ext.AbstractComponent-method-destroy' rel='Ext.AbstractComponent-method-destroy' class='docClass'>Ext.AbstractComponent.destroy</a>, <a href='#!/api/Ext.AbstractPlugin-method-destroy' rel='Ext.AbstractPlugin-method-destroy' class='docClass'>Ext.AbstractPlugin.destroy</a></p></div></div></div><div id='method-getCharCode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-getCharCode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-getCharCode' class='name expandable'>getCharCode</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Gets the character code for the event. ...</div><div class='long'><p>Gets the character code for the event.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-getKey' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-getKey' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-getKey' class='name expandable'>getKey</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns a normalized keyCode for the event. ...</div><div class='long'><p>Returns a normalized keyCode for the event.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The key code</p>\n</div></li></ul></div></div></div><div id='method-getPageX' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-getPageX' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-getPageX' class='name expandable'>getPageX</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Gets the x coordinate of the event. ...</div><div class='long'><p>Gets the x coordinate of the event.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0</p>\n        <p>Replaced by <a href=\"#!/api/Ext.EventObject-method-getX\" rel=\"Ext.EventObject-method-getX\" class=\"docClass\">getX</a></p>\n\n        </div>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getPageY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-getPageY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-getPageY' class='name expandable'>getPageY</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Gets the y coordinate of the event. ...</div><div class='long'><p>Gets the y coordinate of the event.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.0</p>\n        <p>Replaced by <a href=\"#!/api/Ext.EventObject-method-getY\" rel=\"Ext.EventObject-method-getY\" class=\"docClass\">getY</a></p>\n\n        </div>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getPoint' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-getPoint' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-getPoint' class='name expandable'>getPoint</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.util.Point\" rel=\"Ext.util.Point\" class=\"docClass\">Ext.util.Point</a></div><div class='description'><div class='short'>Returns a point object that consists of the object coordinates. ...</div><div class='long'><p>Returns a point object that consists of the object coordinates.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Point\" rel=\"Ext.util.Point\" class=\"docClass\">Ext.util.Point</a></span><div class='sub-desc'><p>point</p>\n</div></li></ul></div></div></div><div id='method-getRelatedTarget' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-getRelatedTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-getRelatedTarget' class='name expandable'>getRelatedTarget</a>( <span class='pre'>[selector], [maxDepth], [returnEl]</span> ) : HTMLElement</div><div class='description'><div class='short'>Gets the related target. ...</div><div class='long'><p>Gets the related target.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>selector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>A simple selector to filter the target or look for an ancestor of the target</p>\n</div></li><li><span class='pre'>maxDepth</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/HTMLElement (optional)<div class='sub-desc'><p>The max depth to search as a number or element (defaults to 10 || document.body)</p>\n</div></li><li><span class='pre'>returnEl</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return a <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a> object instead of DOM node</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>HTMLElement</span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getTarget' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-getTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-getTarget' class='name expandable'>getTarget</a>( <span class='pre'>[selector], [maxDepth], [returnEl]</span> ) : HTMLElement</div><div class='description'><div class='short'>Gets the target for the event. ...</div><div class='long'><p>Gets the target for the event.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>selector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>A simple selector to filter the target or look for an ancestor of the target</p>\n</div></li><li><span class='pre'>maxDepth</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/HTMLElement (optional)<div class='sub-desc'><p>The max depth to search as a number or element (defaults to 10 || document.body)</p>\n</div></li><li><span class='pre'>returnEl</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return a <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a> object instead of DOM node</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>HTMLElement</span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getWheelDelta' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-getWheelDelta' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-getWheelDelta' class='name expandable'>getWheelDelta</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Normalizes mouse wheel y-delta across browsers. ...</div><div class='long'><p>Normalizes mouse wheel y-delta across browsers. To get x-delta information, use\n<a href=\"#!/api/Ext.EventObject-method-getWheelDeltas\" rel=\"Ext.EventObject-method-getWheelDeltas\" class=\"docClass\">getWheelDeltas</a> instead.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The mouse wheel y-delta</p>\n</div></li></ul></div></div></div><div id='method-getWheelDeltas' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-getWheelDeltas' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-getWheelDeltas' class='name expandable'>getWheelDeltas</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Returns the mouse wheel deltas for this event. ...</div><div class='long'><p>Returns the mouse wheel deltas for this event.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>An object with \"x\" and \"y\" properties holding the mouse wheel deltas.</p>\n</div></li></ul></div></div></div><div id='method-getX' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-getX' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-getX' class='name expandable'>getX</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Gets the x coordinate of the event. ...</div><div class='long'><p>Gets the x coordinate of the event.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getXY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-getXY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-getXY' class='name expandable'>getXY</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</div><div class='description'><div class='short'>Gets the page coordinates of the event. ...</div><div class='long'><p>Gets the page coordinates of the event.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</span><div class='sub-desc'><p>The xy values like [x, y]</p>\n</div></li></ul></div></div></div><div id='method-getY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-getY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-getY' class='name expandable'>getY</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Gets the y coordinate of the event. ...</div><div class='long'><p>Gets the y coordinate of the event.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-hasModifier' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-hasModifier' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-hasModifier' class='name expandable'>hasModifier</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the control, meta, shift or alt key was pressed during this event. ...</div><div class='long'><p>Returns true if the control, meta, shift or alt key was pressed during this event.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-injectEvent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-injectEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-injectEvent' class='name expandable'>injectEvent</a>( <span class='pre'>[target]</span> )</div><div class='description'><div class='short'>Injects a DOM event using the data in this object and (optionally) a new target. ...</div><div class='long'><p>Injects a DOM event using the data in this object and (optionally) a new target.\nThis is a low-level technique and not likely to be used by application code. The\ncurrently supported event types are:</p>\n\n<p><b>HTMLEvents</b></p>\n\n\n<ul>\n<li>load</li>\n<li>unload</li>\n<li>select</li>\n<li>change</li>\n<li>submit</li>\n<li>reset</li>\n<li>resize</li>\n<li>scroll</li>\n</ul>\n\n\n<p><b>MouseEvents</b></p>\n\n\n<ul>\n<li>click</li>\n<li>dblclick</li>\n<li>mousedown</li>\n<li>mouseup</li>\n<li>mouseover</li>\n<li>mousemove</li>\n<li>mouseout</li>\n</ul>\n\n\n<p><b>UIEvents</b></p>\n\n\n<ul>\n<li>focusin</li>\n<li>focusout</li>\n<li>activate</li>\n<li>focus</li>\n<li>blur</li>\n</ul>\n\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>/HTMLElement (optional)<div class='sub-desc'><p>If specified, the target for the event. This\nis likely to be used when relaying a DOM event. If not specified, <a href=\"#!/api/Ext.EventObject-method-getTarget\" rel=\"Ext.EventObject-method-getTarget\" class=\"docClass\">getTarget</a>\nis used to determine the target.</p>\n</div></li></ul></div></div></div><div id='method-isNavKeyPress' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-isNavKeyPress' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-isNavKeyPress' class='name expandable'>isNavKeyPress</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks if the key pressed was a \"navigation\" key ...</div><div class='long'><p>Checks if the key pressed was a \"navigation\" key</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if the press is a navigation keypress</p>\n</div></li></ul></div></div></div><div id='method-isSpecialKey' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-isSpecialKey' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-isSpecialKey' class='name expandable'>isSpecialKey</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks if the key pressed was a \"special\" key ...</div><div class='long'><p>Checks if the key pressed was a \"special\" key</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if the press is a special keypress</p>\n</div></li></ul></div></div></div><div id='method-normalizeKey' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-normalizeKey' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-normalizeKey' class='name expandable'>normalizeKey</a>( <span class='pre'>key</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Normalize key codes across browsers ...</div><div class='long'><p>Normalize key codes across browsers</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>key</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The key code</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The normalized code</p>\n</div></li></ul></div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-preventDefault' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-preventDefault' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-preventDefault' class='name expandable'>preventDefault</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Prevents the browsers default handling of the event. ...</div><div class='long'><p>Prevents the browsers default handling of the event.</p>\n</div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setEvent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-setEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-setEvent' class='name expandable'>setEvent</a>( <span class='pre'>event, freezeEvent</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>event</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>freezeEvent</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-stopEvent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-stopEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-stopEvent' class='name expandable'>stopEvent</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Stop the event (preventDefault and stopPropagation) ...</div><div class='long'><p>Stop the event (preventDefault and stopPropagation)</p>\n</div></div></div><div id='method-stopPropagation' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-stopPropagation' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-stopPropagation' class='name expandable'>stopPropagation</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Cancels bubbling of the event. ...</div><div class='long'><p>Cancels bubbling of the event.</p>\n</div></div></div><div id='method-within' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventObject'>Ext.EventObject</span><br/><a href='source/EventObject.html#Ext-EventObject-method-within' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventObject-method-within' class='name expandable'>within</a>( <span class='pre'>el, [related], [allowEl]</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the target of this event is a child of el. ...</div><div class='long'><p>Returns true if the target of this event is a child of el.  Unless the allowEl parameter is set, it will return false if if the target is el.\nExample usage:</p>\n\n<pre><code>// Handle click on any child of an element\n<a href=\"#!/api/Ext-method-getBody\" rel=\"Ext-method-getBody\" class=\"docClass\">Ext.getBody</a>().on('click', function(e){\n    if(e.within('some-el')){\n        alert('Clicked on a child of some-el!');\n    }\n});\n\n// Handle click directly on an element, ignoring clicks on child nodes\n<a href=\"#!/api/Ext-method-getBody\" rel=\"Ext-method-getBody\" class=\"docClass\">Ext.getBody</a>().on('click', function(e,t){\n    if((t.id == 'some-el') && !e.within(t, true)){\n        alert('Clicked directly on some-el!');\n    }\n});\n</code></pre>\n\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The id, DOM element or <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a> to check</p>\n</div></li><li><span class='pre'>related</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p><code>true</code> to test if the related target is within el instead of the target</p>\n</div></li><li><span class='pre'>allowEl</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p><code>true</code> to also check if the passed element is the target or related target</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-create' class='name expandable'>create</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create a new instance of this Class. ...</div><div class='long'><p>Create a new instance of this Class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.create({\n    someConfig: true\n});\n</code></pre>\n\n<p>All parameters are passed to the constructor of the class.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the created instance.</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div></div></div>","superclasses":["Ext.Base"],"meta":{"markdown":true},"code_type":"ext_define","requires":[],"html_meta":{"markdown":null},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"EventObject.html#Ext-EventObject","filename":"EventObject.js"}],"linenr":5,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"A","id":"property-A"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"ALT","id":"property-ALT"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"B","id":"property-B"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"BACKSPACE","id":"property-BACKSPACE"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"C","id":"property-C"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"CAPS_LOCK","id":"property-CAPS_LOCK"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"CONTEXT_MENU","id":"property-CONTEXT_MENU"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"CTRL","id":"property-CTRL"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"D","id":"property-D"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"DELETE","id":"property-DELETE"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"DOWN","id":"property-DOWN"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"E","id":"property-E"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"EIGHT","id":"property-EIGHT"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"END","id":"property-END"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"ENTER","id":"property-ENTER"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"ESC","id":"property-ESC"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"F","id":"property-F"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"F1","id":"property-F1"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"F10","id":"property-F10"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"F11","id":"property-F11"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"F12","id":"property-F12"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"F2","id":"property-F2"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"F3","id":"property-F3"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"F4","id":"property-F4"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"F5","id":"property-F5"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"F6","id":"property-F6"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"F7","id":"property-F7"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"F8","id":"property-F8"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"F9","id":"property-F9"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"FIVE","id":"property-FIVE"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"FOUR","id":"property-FOUR"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"G","id":"property-G"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"H","id":"property-H"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"HOME","id":"property-HOME"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"I","id":"property-I"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"INSERT","id":"property-INSERT"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"J","id":"property-J"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"K","id":"property-K"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"L","id":"property-L"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"LEFT","id":"property-LEFT"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"M","id":"property-M"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"N","id":"property-N"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"NINE","id":"property-NINE"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"NUM_CENTER","id":"property-NUM_CENTER"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"NUM_DIVISION","id":"property-NUM_DIVISION"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"NUM_EIGHT","id":"property-NUM_EIGHT"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"NUM_FIVE","id":"property-NUM_FIVE"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"NUM_FOUR","id":"property-NUM_FOUR"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"NUM_MINUS","id":"property-NUM_MINUS"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"NUM_MULTIPLY","id":"property-NUM_MULTIPLY"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"NUM_NINE","id":"property-NUM_NINE"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"NUM_ONE","id":"property-NUM_ONE"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"NUM_PERIOD","id":"property-NUM_PERIOD"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"NUM_PLUS","id":"property-NUM_PLUS"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"NUM_SEVEN","id":"property-NUM_SEVEN"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"NUM_SIX","id":"property-NUM_SIX"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"NUM_THREE","id":"property-NUM_THREE"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"NUM_TWO","id":"property-NUM_TWO"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"NUM_ZERO","id":"property-NUM_ZERO"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"O","id":"property-O"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"ONE","id":"property-ONE"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"P","id":"property-P"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"PAGE_DOWN","id":"property-PAGE_DOWN"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"PAGE_UP","id":"property-PAGE_UP"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"PAUSE","id":"property-PAUSE"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"PRINT_SCREEN","id":"property-PRINT_SCREEN"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"Q","id":"property-Q"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"R","id":"property-R"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"RETURN","id":"property-RETURN"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"RIGHT","id":"property-RIGHT"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"S","id":"property-S"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"SEVEN","id":"property-SEVEN"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"SHIFT","id":"property-SHIFT"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"SIX","id":"property-SIX"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"SPACE","id":"property-SPACE"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"T","id":"property-T"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"TAB","id":"property-TAB"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"THREE","id":"property-THREE"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"TWO","id":"property-TWO"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"U","id":"property-U"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"UP","id":"property-UP"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"V","id":"property-V"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"W","id":"property-W"},{"tagname":"property","owner":"Ext.EventObject","meta":{"markdown":true},"name":"WHEEL_SCALE","id":"property-WHEEL_SCALE"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"X","id":"property-X"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"Y","id":"property-Y"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"Z","id":"property-Z"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"ZERO","id":"property-ZERO"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"altKey","id":"property-altKey"},{"tagname":"property","owner":"Ext.EventObject","meta":{"private":true},"name":"btnMap","id":"property-btnMap"},{"tagname":"property","owner":"Ext.EventObject","meta":{"private":true},"name":"clickRe","id":"property-clickRe"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"ctrlKey","id":"property-ctrlKey"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.EventObject","meta":{"private":true},"name":"safariKeys","id":"property-safariKeys"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"},{"tagname":"property","owner":"Ext.EventObject","meta":{},"name":"shiftKey","id":"property-shiftKey"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"correctWheelDelta","id":"method-correctWheelDelta"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"getCharCode","id":"method-getCharCode"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"getKey","id":"method-getKey"},{"tagname":"method","owner":"Ext.EventObject","meta":{"deprecated":{"text":"Replaced by {@link #getX}","version":"4.0"}},"name":"getPageX","id":"method-getPageX"},{"tagname":"method","owner":"Ext.EventObject","meta":{"deprecated":{"text":"Replaced by {@link #getY}","version":"4.0"}},"name":"getPageY","id":"method-getPageY"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"getPoint","id":"method-getPoint"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"getRelatedTarget","id":"method-getRelatedTarget"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"getTarget","id":"method-getTarget"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"getWheelDelta","id":"method-getWheelDelta"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"getWheelDeltas","id":"method-getWheelDeltas"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"getX","id":"method-getX"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"getXY","id":"method-getXY"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"getY","id":"method-getY"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"hasModifier","id":"method-hasModifier"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"injectEvent","id":"method-injectEvent"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"isNavKeyPress","id":"method-isNavKeyPress"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"isSpecialKey","id":"method-isSpecialKey"},{"tagname":"method","owner":"Ext.EventObject","meta":{"private":true},"name":"normalizeKey","id":"method-normalizeKey"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"preventDefault","id":"method-preventDefault"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.EventObject","meta":{"private":true},"name":"setEvent","id":"method-setEvent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"stopEvent","id":"method-stopEvent"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"stopPropagation","id":"method-stopPropagation"},{"tagname":"method","owner":"Ext.EventObject","meta":{},"name":"within","id":"method-within"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.EventObject","singleton":true,"override":null,"inheritdoc":null,"id":"class-Ext.EventObject","mixins":[],"mixedInto":[]});