Ext.data.JsonP.Ext_supports({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Support.html#Ext-supports' target='_blank'>Support.js</a></div></pre><div class='doc-contents'><p>Determines information about features are supported in the current environment</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-ArraySort' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-ArraySort' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-ArraySort' class='name not-expandable'>ArraySort</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the Array sort native method isn't bugged.</p>\n</div><div class='long'><p>True if the Array sort native method isn't bugged.</p>\n</div></div></div><div id='property-AudioTag' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-AudioTag' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-AudioTag' class='name not-expandable'>AudioTag</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the device supports the HTML5 audio tag</p>\n</div><div class='long'><p>True if the device supports the HTML5 audio tag</p>\n</div></div></div><div id='property-BoundingClientRect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-BoundingClientRect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-BoundingClientRect' class='name not-expandable'>BoundingClientRect</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the browser supports the getBoundingClientRect method on elements</p>\n</div><div class='long'><p>True if the browser supports the getBoundingClientRect method on elements</p>\n</div></div></div><div id='property-CSS3BorderRadius' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-CSS3BorderRadius' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-CSS3BorderRadius' class='name not-expandable'>CSS3BorderRadius</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the device supports CSS3 border radius</p>\n</div><div class='long'><p>True if the device supports CSS3 border radius</p>\n</div></div></div><div id='property-CSS3BoxShadow' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-CSS3BoxShadow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-CSS3BoxShadow' class='name not-expandable'>CSS3BoxShadow</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if document environment supports the CSS3 box-shadow style.</p>\n</div><div class='long'><p>True if document environment supports the CSS3 box-shadow style.</p>\n</div></div></div><div id='property-CSS3DTransform' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-CSS3DTransform' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-CSS3DTransform' class='name not-expandable'>CSS3DTransform</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the device supports CSS3DTransform</p>\n</div><div class='long'><p>True if the device supports CSS3DTransform</p>\n</div></div></div><div id='property-CSS3LinearGradient' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-CSS3LinearGradient' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-CSS3LinearGradient' class='name not-expandable'>CSS3LinearGradient</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the device supports CSS3 linear gradients</p>\n</div><div class='long'><p>True if the device supports CSS3 linear gradients</p>\n</div></div></div><div id='property-Canvas' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-Canvas' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-Canvas' class='name not-expandable'>Canvas</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the device supports Canvas</p>\n</div><div class='long'><p>True if the device supports Canvas</p>\n</div></div></div><div id='property-ClassList' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-ClassList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-ClassList' class='name not-expandable'>ClassList</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if document environment supports the HTML5 classList API.</p>\n</div><div class='long'><p>True if document environment supports the HTML5 classList API.</p>\n</div></div></div><div id='property-ComputedStyle' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-ComputedStyle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-ComputedStyle' class='name not-expandable'>ComputedStyle</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the browser supports document.defaultView.getComputedStyle()</p>\n</div><div class='long'><p>True if the browser supports document.defaultView.getComputedStyle()</p>\n</div></div></div><div id='property-CreateContextualFragment' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-CreateContextualFragment' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-CreateContextualFragment' class='name not-expandable'>CreateContextualFragment</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if browser support CreateContextualFragment range native methods.</p>\n</div><div class='long'><p>True if browser support CreateContextualFragment range native methods.</p>\n</div></div></div><div id='property-DeviceMotion' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-DeviceMotion' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-DeviceMotion' class='name not-expandable'>DeviceMotion</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the device supports device motion (acceleration and rotation rate)</p>\n</div><div class='long'><p>True if the device supports device motion (acceleration and rotation rate)</p>\n</div></div></div><div id='property-Direct2DBug' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-Direct2DBug' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-Direct2DBug' class='name expandable'>Direct2DBug</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True if when asking for an element's dimension via offsetWidth or offsetHeight,\ngetBoundingClientRect, etc. ...</div><div class='long'><p>True if when asking for an element's dimension via offsetWidth or offsetHeight,\ngetBoundingClientRect, etc. the browser returns the subpixel width rounded to the nearest pixel.</p>\n</div></div></div><div id='property-DisplayChangeInputSelectionBug' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-DisplayChangeInputSelectionBug' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-DisplayChangeInputSelectionBug' class='name expandable'>DisplayChangeInputSelectionBug</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>True if INPUT elements lose their\nselection when their display style is changed. ...</div><div class='long'><p>True if INPUT elements lose their\nselection when their display style is changed. Essentially, if a text input\nhas focus and its display style is changed, the I-beam disappears.</p>\n\n<p>This bug is encountered due to the work around in place for the <a href=\"#!/api/Ext.supports-property-RightMargin\" rel=\"Ext.supports-property-RightMargin\" class=\"docClass\">RightMargin</a>\nbug. This has been observed in Safari 4.0.4 and older, and appears to be fixed\nin Safari 5. It's not clear if Safari 4.1 has the bug, but it has the same WebKit\nversion number as Safari 5 (according to http://unixpapa.com/js/gecko.html).</p>\n</div></div></div><div id='property-DisplayChangeTextAreaSelectionBug' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-DisplayChangeTextAreaSelectionBug' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-DisplayChangeTextAreaSelectionBug' class='name expandable'>DisplayChangeTextAreaSelectionBug</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>True if TEXTAREA elements lose their\nselection when their display style is changed. ...</div><div class='long'><p>True if TEXTAREA elements lose their\nselection when their display style is changed. Essentially, if a text area has\nfocus and its display style is changed, the I-beam disappears.</p>\n\n<p>This bug is encountered due to the work around in place for the <a href=\"#!/api/Ext.supports-property-RightMargin\" rel=\"Ext.supports-property-RightMargin\" class=\"docClass\">RightMargin</a>\nbug. This has been observed in Chrome 10 and Safari 5 and older, and appears to\nbe fixed in Chrome 11.</p>\n</div></div></div><div id='property-Float' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-Float' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-Float' class='name not-expandable'>Float</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the device supports CSS float</p>\n</div><div class='long'><p>True if the device supports CSS float</p>\n</div></div></div><div id='property-GeoLocation' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-GeoLocation' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-GeoLocation' class='name not-expandable'>GeoLocation</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the device supports GeoLocation</p>\n</div><div class='long'><p>True if the device supports GeoLocation</p>\n</div></div></div><div id='property-GetPositionPercentage' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-GetPositionPercentage' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-GetPositionPercentage' class='name expandable'>GetPositionPercentage</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True if the browser will return the left/top/right/bottom\nposition as a percentage when explicitly set as a percentag...</div><div class='long'><p>True if the browser will return the left/top/right/bottom\nposition as a percentage when explicitly set as a percentage value.</p>\n</div></div></div><div id='property-History' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-History' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-History' class='name not-expandable'>History</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the device supports HTML5 history</p>\n</div><div class='long'><p>True if the device supports HTML5 history</p>\n</div></div></div><div id='property-LocalStorage' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-LocalStorage' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-LocalStorage' class='name not-expandable'>LocalStorage</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'><p>True if localStorage is supported</p>\n</div><div class='long'><p>True if localStorage is supported</p>\n</div></div></div><div id='property-MouseEnterLeave' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-MouseEnterLeave' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-MouseEnterLeave' class='name not-expandable'>MouseEnterLeave</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the browser supports mouseenter and mouseleave events</p>\n</div><div class='long'><p>True if the browser supports mouseenter and mouseleave events</p>\n</div></div></div><div id='property-MouseWheel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-MouseWheel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-MouseWheel' class='name not-expandable'>MouseWheel</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the browser supports the mousewheel event</p>\n</div><div class='long'><p>True if the browser supports the mousewheel event</p>\n</div></div></div><div id='property-Opacity' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-Opacity' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-Opacity' class='name not-expandable'>Opacity</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the browser supports normal css opacity</p>\n</div><div class='long'><p>True if the browser supports normal css opacity</p>\n</div></div></div><div id='property-OrientationChange' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-OrientationChange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-OrientationChange' class='name not-expandable'>OrientationChange</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the device supports orientation change</p>\n</div><div class='long'><p>True if the device supports orientation change</p>\n</div></div></div><div id='property-PercentageHeightOverflowBug' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-PercentageHeightOverflowBug' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-PercentageHeightOverflowBug' class='name expandable'>PercentageHeightOverflowBug</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>In some browsers (IE quirks, IE6, IE7, IE9, chrome, safari and opera at the time\nof this writing) a percentage-height...</div><div class='long'><p>In some browsers (IE quirks, IE6, IE7, IE9, chrome, safari and opera at the time\nof this writing) a percentage-height element ignores the horizontal scrollbar\nof its parent element.  This method returns true if the browser is affected\nby this bug.</p>\n</div></div></div><div id='property-Placeholder' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-Placeholder' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-Placeholder' class='name not-expandable'>Placeholder</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the browser supports the HTML5 placeholder attribute on inputs</p>\n</div><div class='long'><p>True if the browser supports the HTML5 placeholder attribute on inputs</p>\n</div></div></div><div id='property-PointerEvents' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-PointerEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-PointerEvents' class='name not-expandable'>PointerEvents</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if document environment supports the CSS3 pointer-events style.</p>\n</div><div class='long'><p>True if document environment supports the CSS3 pointer-events style.</p>\n</div></div></div><div id='property-Range' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-Range' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-Range' class='name not-expandable'>Range</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if browser support document.createRange native method.</p>\n</div><div class='long'><p>True if browser support document.createRange native method.</p>\n</div></div></div><div id='property-RightMargin' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-RightMargin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-RightMargin' class='name expandable'>RightMargin</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True if the device supports right margin. ...</div><div class='long'><p>True if the device supports right margin.\nSee https://bugs.webkit.org/show_bug.cgi?id=13343 for why this is needed.</p>\n</div></div></div><div id='property-RotatedBoundingClientRect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-RotatedBoundingClientRect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-RotatedBoundingClientRect' class='name not-expandable'>RotatedBoundingClientRect</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the BoundingClientRect is\nrotated when the element is rotated using a CSS transform.</p>\n</div><div class='long'><p>True if the BoundingClientRect is\nrotated when the element is rotated using a CSS transform.</p>\n</div></div></div><div id='property-ScrollWidthInlinePaddingBug' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-ScrollWidthInlinePaddingBug' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-ScrollWidthInlinePaddingBug' class='name expandable'>ScrollWidthInlinePaddingBug</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>In some browsers the right padding of an overflowing element is not accounted\nfor in its scrollWidth. ...</div><div class='long'><p>In some browsers the right padding of an overflowing element is not accounted\nfor in its scrollWidth.  The result can vary depending on whether or not\nThe element contains block-level children.  This method tests the effect\nof padding on scrollWidth when there are no block-level children inside the\noverflowing element.</p>\n\n<p>This method returns true if the browser is affected by this bug.</p>\n</div></div></div><div id='property-Svg' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-Svg' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-Svg' class='name not-expandable'>Svg</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the device supports SVG</p>\n</div><div class='long'><p>True if the device supports SVG</p>\n</div></div></div><div id='property-TextAreaMaxLength' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-TextAreaMaxLength' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-TextAreaMaxLength' class='name not-expandable'>TextAreaMaxLength</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the browser supports maxlength on textareas.</p>\n</div><div class='long'><p>True if the browser supports maxlength on textareas.</p>\n</div></div></div><div id='property-TimeoutActualLateness' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-TimeoutActualLateness' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-TimeoutActualLateness' class='name expandable'>TimeoutActualLateness</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True if the browser passes the \"actualLateness\" parameter to\nsetTimeout. ...</div><div class='long'><p>True if the browser passes the \"actualLateness\" parameter to\nsetTimeout. See: https://developer.mozilla.org/en/DOM/window.setTimeout</p>\n</div></div></div><div id='property-Touch' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-Touch' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-Touch' class='name not-expandable'>Touch</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the device supports touch</p>\n</div><div class='long'><p>True if the device supports touch</p>\n</div></div></div><div id='property-Transitions' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-Transitions' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-Transitions' class='name not-expandable'>Transitions</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the device supports CSS3 Transitions</p>\n</div><div class='long'><p>True if the device supports CSS3 Transitions</p>\n</div></div></div><div id='property-TransparentColor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-TransparentColor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-TransparentColor' class='name not-expandable'>TransparentColor</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the device supports transparent color</p>\n</div><div class='long'><p>True if the device supports transparent color</p>\n</div></div></div><div id='property-Vml' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-Vml' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-Vml' class='name not-expandable'>Vml</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the device supports VML</p>\n</div><div class='long'><p>True if the device supports VML</p>\n</div></div></div><div id='property-WindowOnError' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-WindowOnError' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-WindowOnError' class='name not-expandable'>WindowOnError</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if browser supports window.onerror.</p>\n</div><div class='long'><p>True if browser supports window.onerror.</p>\n</div></div></div><div id='property-xOriginBug' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-property-xOriginBug' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-property-xOriginBug' class='name expandable'>xOriginBug</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>In Chrome 24.0, an RTL element which has vertical overflow positions its right X origin incorrectly. ...</div><div class='long'><p>In Chrome 24.0, an RTL element which has vertical overflow positions its right X origin incorrectly.\nIt skips a non-existent scrollbar which has been moved to the left edge due to the RTL setting.</p>\n\n<p>http://code.google.com/p/chromium/issues/detail?id=174656</p>\n\n<p>This method returns true if the browser is affected by this bug.</p>\n</div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-generateVector' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-method-generateVector' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-method-generateVector' class='name expandable'>generateVector</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Generates a support vector for the current browser/mode. ...</div><div class='long'><p>Generates a support vector for the current browser/mode.  The result can be\nadded to supportsVectors to eliminate feature detection at startup time.</p>\n</div></div></div><div id='method-init' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.supports'>Ext.supports</span><br/><a href='source/Support.html#Ext-supports-method-init' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.supports-method-init' class='name expandable'>init</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Runs feature detection routines and sets the various flags. ...</div><div class='long'><p>Runs feature detection routines and sets the various flags. This is called when\nthe scripts loads (very early) and again at <a href=\"#!/api/Ext-method-onReady\" rel=\"Ext-method-onReady\" class=\"docClass\">Ext.onReady</a>. Some detections\nare flagged as <code>early</code> and run immediately. Others that require the document body\nwill not run until ready.</p>\n\n<p>Each test is run only once, so calling this method from an onReady function is safe\nand ensures that all flags have been set.</p>\n</div></div></div></div></div></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"Support.html#Ext-supports","filename":"Support.js"}],"linenr":135,"members":{"property":[{"tagname":"property","owner":"Ext.supports","meta":{},"name":"ArraySort","id":"property-ArraySort"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"AudioTag","id":"property-AudioTag"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"BoundingClientRect","id":"property-BoundingClientRect"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"CSS3BorderRadius","id":"property-CSS3BorderRadius"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"CSS3BoxShadow","id":"property-CSS3BoxShadow"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"CSS3DTransform","id":"property-CSS3DTransform"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"CSS3LinearGradient","id":"property-CSS3LinearGradient"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"Canvas","id":"property-Canvas"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"ClassList","id":"property-ClassList"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"ComputedStyle","id":"property-ComputedStyle"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"CreateContextualFragment","id":"property-CreateContextualFragment"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"DeviceMotion","id":"property-DeviceMotion"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"Direct2DBug","id":"property-Direct2DBug"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"DisplayChangeInputSelectionBug","id":"property-DisplayChangeInputSelectionBug"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"DisplayChangeTextAreaSelectionBug","id":"property-DisplayChangeTextAreaSelectionBug"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"Float","id":"property-Float"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"GeoLocation","id":"property-GeoLocation"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"GetPositionPercentage","id":"property-GetPositionPercentage"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"History","id":"property-History"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"LocalStorage","id":"property-LocalStorage"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"MouseEnterLeave","id":"property-MouseEnterLeave"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"MouseWheel","id":"property-MouseWheel"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"Opacity","id":"property-Opacity"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"OrientationChange","id":"property-OrientationChange"},{"tagname":"property","owner":"Ext.supports","meta":{"private":true},"name":"PercentageHeightOverflowBug","id":"property-PercentageHeightOverflowBug"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"Placeholder","id":"property-Placeholder"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"PointerEvents","id":"property-PointerEvents"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"Range","id":"property-Range"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"RightMargin","id":"property-RightMargin"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"RotatedBoundingClientRect","id":"property-RotatedBoundingClientRect"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"ScrollWidthInlinePaddingBug","id":"property-ScrollWidthInlinePaddingBug"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"Svg","id":"property-Svg"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"TextAreaMaxLength","id":"property-TextAreaMaxLength"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"TimeoutActualLateness","id":"property-TimeoutActualLateness"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"Touch","id":"property-Touch"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"Transitions","id":"property-Transitions"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"TransparentColor","id":"property-TransparentColor"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"Vml","id":"property-Vml"},{"tagname":"property","owner":"Ext.supports","meta":{},"name":"WindowOnError","id":"property-WindowOnError"},{"tagname":"property","owner":"Ext.supports","meta":{"private":true},"name":"xOriginBug","id":"property-xOriginBug"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.supports","meta":{"private":true},"name":"generateVector","id":"method-generateVector"},{"tagname":"method","owner":"Ext.supports","meta":{"markdown":true,"private":true},"name":"init","id":"method-init"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.supports","singleton":true,"override":null,"inheritdoc":null,"id":"class-Ext.supports","mixins":[],"mixedInto":[]});