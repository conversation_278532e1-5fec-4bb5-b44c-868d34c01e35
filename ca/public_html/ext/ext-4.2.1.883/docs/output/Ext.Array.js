Ext.data.JsonP.Ext_Array({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Array2.html#Ext-Array' target='_blank'>Array.js</a></div></pre><div class='doc-contents'><p>A set of useful static methods to deal with arrays; provide missing methods for older browsers.</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-clean' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-clean' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-clean' class='name expandable'>clean</a>( <span class='pre'>array</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Filter through an array and remove empty item as defined in Ext.isEmpty\n\nSee filter ...</div><div class='long'><p>Filter through an array and remove empty item as defined in <a href=\"#!/api/Ext-method-isEmpty\" rel=\"Ext-method-isEmpty\" class=\"docClass\">Ext.isEmpty</a></p>\n\n<p>See <a href=\"#!/api/Ext.Array-method-filter\" rel=\"Ext.Array-method-filter\" class=\"docClass\">filter</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>results</p>\n</div></li></ul></div></div></div><div id='method-clone' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-clone' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-clone' class='name expandable'>clone</a>( <span class='pre'>array</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Clone a flat array without referencing the previous one. ...</div><div class='long'><p>Clone a flat array without referencing the previous one. Note that this is different\nfrom <a href=\"#!/api/Ext-method-clone\" rel=\"Ext-method-clone\" class=\"docClass\">Ext.clone</a> since it doesn't handle recursive cloning. It's simply a convenient, easy-to-remember method\nfor Array.prototype.slice.call(array)</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>The clone array</p>\n</div></li></ul></div></div></div><div id='method-contains' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-contains' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-contains' class='name expandable'>contains</a>( <span class='pre'>array, item</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks whether or not the given array contains the specified item ...</div><div class='long'><p>Checks whether or not the given <code>array</code> contains the specified <code>item</code></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array to check</p>\n</div></li><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The item to look for</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if the array contains the item, false otherwise</p>\n</div></li></ul></div></div></div><div id='method-difference' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-difference' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-difference' class='name expandable'>difference</a>( <span class='pre'>arrayA, arrayB</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Perform a set difference A-B by subtracting all items in array B from array A. ...</div><div class='long'><p>Perform a set difference A-B by subtracting all items in array B from array A.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>arrayA</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li><li><span class='pre'>arrayB</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>difference</p>\n</div></li></ul></div></div></div><div id='method-each' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-each' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-each' class='name expandable'>each</a>( <span class='pre'>iterable, fn, [scope], [reverse]</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Iterates an array or an iterable value and invoke the given callback function for each item. ...</div><div class='long'><p>Iterates an array or an iterable value and invoke the given callback function for each item.</p>\n\n<pre><code>var countries = ['Vietnam', 'Singapore', 'United States', 'Russia'];\n\n<a href=\"#!/api/Ext.Array-method-each\" rel=\"Ext.Array-method-each\" class=\"docClass\">Ext.Array.each</a>(countries, function(name, index, countriesItSelf) {\n    console.log(name);\n});\n\nvar sum = function() {\n    var sum = 0;\n\n    <a href=\"#!/api/Ext.Array-method-each\" rel=\"Ext.Array-method-each\" class=\"docClass\">Ext.Array.each</a>(arguments, function(value) {\n        sum += value;\n    });\n\n    return sum;\n};\n\nsum(1, 2, 3); // returns 6\n</code></pre>\n\n<p>The iteration can be stopped by returning false in the function callback.</p>\n\n<pre><code><a href=\"#!/api/Ext.Array-method-each\" rel=\"Ext.Array-method-each\" class=\"docClass\">Ext.Array.each</a>(countries, function(name, index, countriesItSelf) {\n    if (name === 'Singapore') {\n        return false; // break here\n    }\n});\n</code></pre>\n\n<p><a href=\"#!/api/Ext-method-each\" rel=\"Ext-method-each\" class=\"docClass\">Ext.each</a> is alias for <a href=\"#!/api/Ext.Array-method-each\" rel=\"Ext.Array-method-each\" class=\"docClass\">Ext.Array.each</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>iterable</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/NodeList/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to be iterated. If this\nargument is not iterable, the callback function is called once.</p>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The callback function. If it returns false, the iteration stops and this method returns\nthe current <code>index</code>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The item at the current <code>index</code> in the passed <code>array</code></p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The current <code>index</code> within the <code>array</code></p>\n</div></li><li><span class='pre'>allItems</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The <code>array</code> itself which was passed as the first argument</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>Return false to stop iteration.</p>\n</div></li></ul></div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the specified function is executed.</p>\n</div></li><li><span class='pre'>reverse</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>Reverse the iteration order (loop from the end to the beginning)\nDefaults false</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>See description for the <code>fn</code> parameter.</p>\n</div></li></ul></div></div></div><div id='method-equals' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-equals' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-equals' class='name expandable'>equals</a>( <span class='pre'>array1, array2</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Shallow compares the contents of 2 arrays using strict equality. ...</div><div class='long'><p>Shallow compares the contents of 2 arrays using strict equality.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array1</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li><li><span class='pre'>array2</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p><code>true</code> if the arrays are equal.</p>\n</div></li></ul></div></div></div><div id='method-erase' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-erase' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-erase' class='name expandable'>erase</a>( <span class='pre'>array, index, removeCount</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Removes items from an array. ...</div><div class='long'><p>Removes items from an array. This is functionally equivalent to the splice method\nof Array, but works around bugs in IE8's splice method and does not copy the\nremoved elements in order to return them (because very often they are ignored).</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The Array on which to replace.</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The index in the array at which to operate.</p>\n</div></li><li><span class='pre'>removeCount</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of items to remove at index.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>The array passed.</p>\n</div></li></ul></div></div></div><div id='method-every' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-every' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-every' class='name expandable'>every</a>( <span class='pre'>array, fn, scope</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Executes the specified function for each array element until the function returns a falsy value. ...</div><div class='long'><p>Executes the specified function for each array element until the function returns a falsy value.\nIf such an item is found, the function will return false immediately.\nOtherwise, it will return true.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Callback function for each item</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : Mixed<div class='sub-desc'><p>Current item.</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Index of the item.</p>\n</div></li><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The whole array that's being iterated.</p>\n</div></li></ul></div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>Callback function scope</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if no false value is returned by the callback function.</p>\n</div></li></ul></div></div></div><div id='method-filter' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-filter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-filter' class='name expandable'>filter</a>( <span class='pre'>array, fn, scope</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Creates a new array with all of the elements of this array for which\nthe provided filtering function returns true. ...</div><div class='long'><p>Creates a new array with all of the elements of this array for which\nthe provided filtering function returns true.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Callback function for each item</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : Mixed<div class='sub-desc'><p>Current item.</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Index of the item.</p>\n</div></li><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The whole array that's being iterated.</p>\n</div></li></ul></div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>Callback function scope</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>results</p>\n</div></li></ul></div></div></div><div id='method-findBy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-findBy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-findBy' class='name expandable'>findBy</a>( <span class='pre'>array, fn, [scope]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Returns the first item in the array which elicits a true return value from the\npassed selection function. ...</div><div class='long'><p>Returns the first item in the array which elicits a true return value from the\npassed selection function.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array to search</p>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The selection function to execute for each item.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : Mixed<div class='sub-desc'><p>The array item.</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The index of the array item.</p>\n</div></li></ul></div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the\nfunction is executed. Defaults to the array</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The first item in the array which returned true from the selection\nfunction, or null if none was found.</p>\n</div></li></ul></div></div></div><div id='method-flatten' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-flatten' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-flatten' class='name expandable'>flatten</a>( <span class='pre'>array</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Recursively flattens into 1-d Array. ...</div><div class='long'><p>Recursively flattens into 1-d Array. Injects Arrays inline.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array to flatten</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>The 1-d array.</p>\n</div></li></ul></div></div></div><div id='method-forEach' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-forEach' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-forEach' class='name expandable'>forEach</a>( <span class='pre'>array, fn, [scope]</span> )</div><div class='description'><div class='short'>Iterates an array and invoke the given callback function for each item. ...</div><div class='long'><p>Iterates an array and invoke the given callback function for each item. Note that this will simply\ndelegate to the native Array.prototype.forEach method if supported. It doesn't support stopping the\niteration by returning false in the callback function like <a href=\"#!/api/Ext.Array-method-each\" rel=\"Ext.Array-method-each\" class=\"docClass\">each</a>. However, performance\ncould be much better in modern browsers comparing with <a href=\"#!/api/Ext.Array-method-each\" rel=\"Ext.Array-method-each\" class=\"docClass\">each</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array to iterate</p>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The callback function.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The item at the current <code>index</code> in the passed <code>array</code></p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The current <code>index</code> within the <code>array</code></p>\n</div></li><li><span class='pre'>allItems</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The <code>array</code> itself which was passed as the first argument</p>\n</div></li></ul></div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The execution scope (<code>this</code>) in which the specified function is executed.</p>\n</div></li></ul></div></div></div><div id='method-from' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-from' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-from' class='name expandable'>from</a>( <span class='pre'>value, [newReference]</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Converts a value to an array if it's not already an array; returns:\n\n\nAn empty array if given value is undefined or n...</div><div class='long'><p>Converts a value to an array if it's not already an array; returns:</p>\n\n<ul>\n<li>An empty array if given value is <code>undefined</code> or <code>null</code></li>\n<li>Itself if given value is already an array</li>\n<li>An array copy if given value is <a href=\"#!/api/Ext-method-isIterable\" rel=\"Ext-method-isIterable\" class=\"docClass\">iterable</a> (arguments, NodeList and alike)</li>\n<li>An array with one item which is the given value, otherwise</li>\n</ul>\n\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to convert to an array if it's not already is an array</p>\n</div></li><li><span class='pre'>newReference</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to clone the given array and return a new reference if necessary,\ndefaults to false</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>array</p>\n</div></li></ul></div></div></div><div id='method-include' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-include' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-include' class='name expandable'>include</a>( <span class='pre'>array, item</span> )</div><div class='description'><div class='short'>Push an item into the array only if the array doesn't contain it yet ...</div><div class='long'><p>Push an item into the array only if the array doesn't contain it yet</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array</p>\n</div></li><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The item to include</p>\n</div></li></ul></div></div></div><div id='method-indexOf' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-indexOf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-indexOf' class='name expandable'>indexOf</a>( <span class='pre'>array, item, [from]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Get the index of the provided item in the given array, a supplement for the\nmissing arrayPrototype.indexOf in Interne...</div><div class='long'><p>Get the index of the provided <code>item</code> in the given <code>array</code>, a supplement for the\nmissing arrayPrototype.indexOf in Internet Explorer.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array to check</p>\n</div></li><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The item to look for</p>\n</div></li><li><span class='pre'>from</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>The index at which to begin the search</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The index of item in the array (or -1 if it is not found)</p>\n</div></li></ul></div></div></div><div id='method-insert' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-insert' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-insert' class='name expandable'>insert</a>( <span class='pre'>array, index, items</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Inserts items in to an array. ...</div><div class='long'><p>Inserts items in to an array.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The Array in which to insert.</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The index in the array at which to operate.</p>\n</div></li><li><span class='pre'>items</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array of items to insert at index.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>The array passed.</p>\n</div></li></ul></div></div></div><div id='method-intersect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-intersect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-intersect' class='name expandable'>intersect</a>( <span class='pre'>array1, array2, etc</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Merge multiple arrays into one with unique items that exist in all of the arrays. ...</div><div class='long'><p>Merge multiple arrays into one with unique items that exist in all of the arrays.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array1</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li><li><span class='pre'>array2</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li><li><span class='pre'>etc</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>intersect</p>\n</div></li></ul></div></div></div><div id='method-map' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-map' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-map' class='name expandable'>map</a>( <span class='pre'>array, fn, [scope]</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Creates a new array with the results of calling a provided function on every element in this array. ...</div><div class='long'><p>Creates a new array with the results of calling a provided function on every element in this array.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Callback function for each item</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : Mixed<div class='sub-desc'><p>Current item.</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Index of the item.</p>\n</div></li><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The whole array that's being iterated.</p>\n</div></li></ul></div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Callback function scope</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>results</p>\n</div></li></ul></div></div></div><div id='method-max' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-max' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-max' class='name expandable'>max</a>( <span class='pre'>array, [comparisonFn]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Returns the maximum value in the Array. ...</div><div class='long'><p>Returns the maximum value in the Array.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/NodeList<div class='sub-desc'><p>The Array from which to select the maximum value.</p>\n</div></li><li><span class='pre'>comparisonFn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>a function to perform the comparision which determines maximization.\nIf omitted the \">\" operator will be used. Note: gt = 1; eq = 0; lt = -1</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>max</span> : Mixed<div class='sub-desc'><p>Current maximum value.</p>\n</div></li><li><span class='pre'>item</span> : Mixed<div class='sub-desc'><p>The value to compare with the current maximum.</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>maxValue The maximum value</p>\n</div></li></ul></div></div></div><div id='method-mean' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-mean' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-mean' class='name expandable'>mean</a>( <span class='pre'>array</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Calculates the mean of all items in the array. ...</div><div class='long'><p>Calculates the mean of all items in the array.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The Array to calculate the mean value of.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The mean.</p>\n</div></li></ul></div></div></div><div id='method-merge' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-merge' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-merge' class='name expandable'>merge</a>( <span class='pre'>array1, array2, etc</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Merge multiple arrays into one with unique items. ...</div><div class='long'><p>Merge multiple arrays into one with unique items.</p>\n\n<p><a href=\"#!/api/Ext.Array-method-union\" rel=\"Ext.Array-method-union\" class=\"docClass\">union</a> is alias for <a href=\"#!/api/Ext.Array-method-merge\" rel=\"Ext.Array-method-merge\" class=\"docClass\">merge</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array1</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li><li><span class='pre'>array2</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li><li><span class='pre'>etc</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>merged</p>\n\n</div></li></ul></div></div></div><div id='method-min' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-min' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-min' class='name expandable'>min</a>( <span class='pre'>array, [comparisonFn]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Returns the minimum value in the Array. ...</div><div class='long'><p>Returns the minimum value in the Array.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/NodeList<div class='sub-desc'><p>The Array from which to select the minimum value.</p>\n</div></li><li><span class='pre'>comparisonFn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>a function to perform the comparision which determines minimization.\nIf omitted the \"&lt;\" operator will be used. Note: gt = 1; eq = 0; lt = -1</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>min</span> : Mixed<div class='sub-desc'><p>Current minimum value.</p>\n</div></li><li><span class='pre'>item</span> : Mixed<div class='sub-desc'><p>The value to compare with the current minimum.</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>minValue The minimum value</p>\n</div></li></ul></div></div></div><div id='method-pluck' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-pluck' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-pluck' class='name expandable'>pluck</a>( <span class='pre'>array, propertyName</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Plucks the value of a property from each item in the Array. ...</div><div class='long'><p>Plucks the value of a property from each item in the Array. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext.Array-method-pluck\" rel=\"Ext.Array-method-pluck\" class=\"docClass\">Ext.Array.pluck</a>(<a href=\"#!/api/Ext-method-query\" rel=\"Ext-method-query\" class=\"docClass\">Ext.query</a>(\"p\"), \"className\"); // [el1.className, el2.className, ..., elN.className]\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/NodeList<div class='sub-desc'><p>The Array of items to pluck the value from.</p>\n</div></li><li><span class='pre'>propertyName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The property name to pluck from each element.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>The value from each item in the Array.</p>\n</div></li></ul></div></div></div><div id='method-push' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-push' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-push' class='name expandable'>push</a>( <span class='pre'>target, elements</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Pushes new items onto the end of an Array. ...</div><div class='long'><p>Pushes new items onto the end of an Array.</p>\n\n<p>Passed parameters may be single items, or arrays of items. If an Array is found in the argument list, all its\nelements are pushed into the end of the target Array.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The Array onto which to push new items</p>\n</div></li><li><span class='pre'>elements</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>...<div class='sub-desc'><p>The elements to add to the array. Each parameter may\nbe an Array, in which case all the elements of that Array will be pushed into the end of the\ndestination Array.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>An array containing all the new items push onto the end.</p>\n</div></li></ul></div></div></div><div id='method-remove' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-remove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-remove' class='name expandable'>remove</a>( <span class='pre'>array, item</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Removes the specified item from the array if it exists ...</div><div class='long'><p>Removes the specified item from the array if it exists</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array</p>\n</div></li><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The item to remove</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>The passed array itself</p>\n</div></li></ul></div></div></div><div id='method-replace' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-replace' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-replace' class='name expandable'>replace</a>( <span class='pre'>array, index, removeCount, [insert]</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Replaces items in an array. ...</div><div class='long'><p>Replaces items in an array. This is functionally equivalent to the splice method\nof Array, but works around bugs in IE8's splice method and is often more convenient\nto call because it accepts an array of items to insert rather than use a variadic\nargument list.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The Array on which to replace.</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The index in the array at which to operate.</p>\n</div></li><li><span class='pre'>removeCount</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of items to remove at index (can be 0).</p>\n</div></li><li><span class='pre'>insert</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> (optional)<div class='sub-desc'><p>An array of items to insert at index.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>The array passed.</p>\n</div></li></ul></div></div></div><div id='method-slice' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-slice' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-slice' class='name expandable'>slice</a>( <span class='pre'>array, begin, end</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Returns a shallow copy of a part of an array. ...</div><div class='long'><p>Returns a shallow copy of a part of an array. This is equivalent to the native\ncall \"Array.prototype.slice.call(array, begin, end)\". This is often used when \"array\"\nis \"arguments\" since the arguments object does not supply a slice method but can\nbe the context object to Array.prototype.slice.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array (or arguments object).</p>\n</div></li><li><span class='pre'>begin</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The index at which to begin. Negative values are offsets from\nthe end of the array.</p>\n</div></li><li><span class='pre'>end</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The index at which to end. The copied items do not include\nend. Negative values are offsets from the end of the array. If end is omitted,\nall items up to the end of the array are copied.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>The copied piece of the array.</p>\n</div></li></ul></div></div></div><div id='method-some' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-some' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-some' class='name expandable'>some</a>( <span class='pre'>array, fn, scope</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Executes the specified function for each array element until the function returns a truthy value. ...</div><div class='long'><p>Executes the specified function for each array element until the function returns a truthy value.\nIf such an item is found, the function will return true immediately. Otherwise, it will return false.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Callback function for each item</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : Mixed<div class='sub-desc'><p>Current item.</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Index of the item.</p>\n</div></li><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The whole array that's being iterated.</p>\n</div></li></ul></div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>Callback function scope</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if the callback function returns a truthy value.</p>\n</div></li></ul></div></div></div><div id='method-sort' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-sort' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-sort' class='name expandable'>sort</a>( <span class='pre'>array, [sortFn]</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Sorts the elements of an Array. ...</div><div class='long'><p>Sorts the elements of an Array.\nBy default, this method sorts the elements alphabetically and ascending.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array to sort.</p>\n</div></li><li><span class='pre'>sortFn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The comparison function.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>a</span> : Mixed<div class='sub-desc'><p>An item to compare.</p>\n</div></li><li><span class='pre'>b</span> : Mixed<div class='sub-desc'><p>Another item to compare.</p>\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>The sorted array.</p>\n</div></li></ul></div></div></div><div id='method-splice' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-splice' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-splice' class='name expandable'>splice</a>( <span class='pre'>array, index, removeCount, elements</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Replaces items in an array. ...</div><div class='long'><p>Replaces items in an array. This is equivalent to the splice method of Array, but\nworks around bugs in IE8's splice method. The signature is exactly the same as the\nsplice method except that the array is the first argument. All arguments following\nremoveCount are inserted in the array at index.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The Array on which to replace.</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The index in the array at which to operate.</p>\n</div></li><li><span class='pre'>removeCount</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of items to remove at index (can be 0).</p>\n</div></li><li><span class='pre'>elements</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>...<div class='sub-desc'><p>The elements to add to the array. If you don't specify\nany elements, splice simply removes elements from the array.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>An array containing the removed items.</p>\n</div></li></ul></div></div></div><div id='method-sum' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-sum' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-sum' class='name expandable'>sum</a>( <span class='pre'>array</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Calculates the sum of all items in the given array. ...</div><div class='long'><p>Calculates the sum of all items in the given array.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The Array to calculate the sum value of.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The sum.</p>\n</div></li></ul></div></div></div><div id='method-toArray' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-toArray' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-toArray' class='name expandable'>toArray</a>( <span class='pre'>iterable, [start], [end]</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Converts any iterable (numeric indices and a length property) into a true array. ...</div><div class='long'><p>Converts any iterable (numeric indices and a length property) into a true array.</p>\n\n<pre><code>function test() {\n    var args = <a href=\"#!/api/Ext.Array-method-toArray\" rel=\"Ext.Array-method-toArray\" class=\"docClass\">Ext.Array.toArray</a>(arguments),\n        fromSecondToLastArgs = <a href=\"#!/api/Ext.Array-method-toArray\" rel=\"Ext.Array-method-toArray\" class=\"docClass\">Ext.Array.toArray</a>(arguments, 1);\n\n    alert(args.join(' '));\n    alert(fromSecondToLastArgs.join(' '));\n}\n\ntest('just', 'testing', 'here'); // alerts 'just testing here';\n                                 // alerts 'testing here';\n\n<a href=\"#!/api/Ext.Array-method-toArray\" rel=\"Ext.Array-method-toArray\" class=\"docClass\">Ext.Array.toArray</a>(document.getElementsByTagName('div')); // will convert the NodeList into an array\n<a href=\"#!/api/Ext.Array-method-toArray\" rel=\"Ext.Array-method-toArray\" class=\"docClass\">Ext.Array.toArray</a>('splitted'); // returns ['s', 'p', 'l', 'i', 't', 't', 'e', 'd']\n<a href=\"#!/api/Ext.Array-method-toArray\" rel=\"Ext.Array-method-toArray\" class=\"docClass\">Ext.Array.toArray</a>('splitted', 0, 3); // returns ['s', 'p', 'l']\n</code></pre>\n\n<p><a href=\"#!/api/Ext-method-toArray\" rel=\"Ext-method-toArray\" class=\"docClass\">Ext.toArray</a> is alias for <a href=\"#!/api/Ext.Array-method-toArray\" rel=\"Ext.Array-method-toArray\" class=\"docClass\">Ext.Array.toArray</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>iterable</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>the iterable object to be turned into a true Array.</p>\n</div></li><li><span class='pre'>start</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>a zero-based index that specifies the start of extraction. Defaults to 0</p>\n</div></li><li><span class='pre'>end</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>a 1-based index that specifies the end of extraction. Defaults to the last\nindex of the iterable value</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>array</p>\n</div></li></ul></div></div></div><div id='method-toMap' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-toMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-toMap' class='name expandable'>toMap</a>( <span class='pre'>array, [getKey], [scope]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Creates a map (object) keyed by the elements of the given array. ...</div><div class='long'><p>Creates a map (object) keyed by the elements of the given array. The values in\nthe map are the index+1 of the array element. For example:</p>\n\n<pre><code> var map = <a href=\"#!/api/Ext.Array-method-toMap\" rel=\"Ext.Array-method-toMap\" class=\"docClass\">Ext.Array.toMap</a>(['a','b','c']);\n\n // map = { a: 1, b: 2, c: 3 };\n</code></pre>\n\n<p>Or a key property can be specified:</p>\n\n<pre><code> var map = <a href=\"#!/api/Ext.Array-method-toMap\" rel=\"Ext.Array-method-toMap\" class=\"docClass\">Ext.Array.toMap</a>([\n         { name: 'a' },\n         { name: 'b' },\n         { name: 'c' }\n     ], 'name');\n\n // map = { a: 1, b: 2, c: 3 };\n</code></pre>\n\n<p>Lastly, a key extractor can be provided:</p>\n\n<pre><code> var map = <a href=\"#!/api/Ext.Array-method-toMap\" rel=\"Ext.Array-method-toMap\" class=\"docClass\">Ext.Array.toMap</a>([\n         { name: 'a' },\n         { name: 'b' },\n         { name: 'c' }\n     ], function (obj) { return obj.name.toUpperCase(); });\n\n // map = { A: 1, B: 2, C: 3 };\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The Array to create the map from.</p>\n</div></li><li><span class='pre'>getKey</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>Name of the object property to use\nas a key or a function to extract the key.</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Value of this inside callback.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The resulting map.</p>\n</div></li></ul></div></div></div><div id='method-toValueMap' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-toValueMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-toValueMap' class='name expandable'>toValueMap</a>( <span class='pre'>array, [getKey], [scope]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Creates a map (object) keyed by a property of elements of the given array. ...</div><div class='long'><p>Creates a map (object) keyed by a property of elements of the given array. The values in\nthe map are the array element. For example:</p>\n\n<pre><code> var map = <a href=\"#!/api/Ext.Array-method-toMap\" rel=\"Ext.Array-method-toMap\" class=\"docClass\">Ext.Array.toMap</a>(['a','b','c']);\n\n // map = { a: 'a', b: 'b', c: 'c' };\n</code></pre>\n\n<p>Or a key property can be specified:</p>\n\n<pre><code> var map = <a href=\"#!/api/Ext.Array-method-toMap\" rel=\"Ext.Array-method-toMap\" class=\"docClass\">Ext.Array.toMap</a>([\n         { name: 'a' },\n         { name: 'b' },\n         { name: 'c' }\n     ], 'name');\n\n // map = { a: {name: 'a'}, b: {name: 'b'}, c: {name: 'c'} };\n</code></pre>\n\n<p>Lastly, a key extractor can be provided:</p>\n\n<pre><code> var map = <a href=\"#!/api/Ext.Array-method-toMap\" rel=\"Ext.Array-method-toMap\" class=\"docClass\">Ext.Array.toMap</a>([\n         { name: 'a' },\n         { name: 'b' },\n         { name: 'c' }\n     ], function (obj) { return obj.name.toUpperCase(); });\n\n // map = { A: {name: 'a'}, B: {name: 'b'}, C: {name: 'c'} };\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The Array to create the map from.</p>\n</div></li><li><span class='pre'>getKey</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>Name of the object property to use\nas a key or a function to extract the key.</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Value of this inside callback.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The resulting map.</p>\n</div></li></ul></div></div></div><div id='method-union' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-union' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-union' class='name expandable'>union</a>( <span class='pre'>array1, array2, etc</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Merge multiple arrays into one with unique items. ...</div><div class='long'><p>Merge multiple arrays into one with unique items.</p>\n\n<p><a href=\"#!/api/Ext.Array-method-union\" rel=\"Ext.Array-method-union\" class=\"docClass\">union</a> is alias for <a href=\"#!/api/Ext.Array-method-merge\" rel=\"Ext.Array-method-merge\" class=\"docClass\">merge</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array1</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li><li><span class='pre'>array2</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li><li><span class='pre'>etc</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>merged</p>\n\n</div></li></ul></div></div></div><div id='method-unique' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Array'>Ext.Array</span><br/><a href='source/Array2.html#Ext-Array-method-unique' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Array-method-unique' class='name expandable'>unique</a>( <span class='pre'>array</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Returns a new array with unique items ...</div><div class='long'><p>Returns a new array with unique items</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>results</p>\n</div></li></ul></div></div></div></div></div></div></div>","superclasses":[],"meta":{"author":["Jacky Nguyen <<EMAIL>>"],"docauthor":["Jacky Nguyen <<EMAIL>>"]},"requires":[],"html_meta":{"author":null,"docauthor":null},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"Array2.html#Ext-Array","filename":"Array.js"}],"linenr":5,"members":{"property":[],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Array","meta":{},"name":"clean","id":"method-clean"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"clone","id":"method-clone"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"contains","id":"method-contains"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"difference","id":"method-difference"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"each","id":"method-each"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"equals","id":"method-equals"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"erase","id":"method-erase"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"every","id":"method-every"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"filter","id":"method-filter"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"findBy","id":"method-findBy"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"flatten","id":"method-flatten"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"forEach","id":"method-forEach"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"from","id":"method-from"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"include","id":"method-include"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"indexOf","id":"method-indexOf"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"insert","id":"method-insert"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"intersect","id":"method-intersect"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"map","id":"method-map"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"max","id":"method-max"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"mean","id":"method-mean"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"merge","id":"method-merge"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"min","id":"method-min"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"pluck","id":"method-pluck"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"push","id":"method-push"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"remove","id":"method-remove"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"replace","id":"method-replace"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"slice","id":"method-slice"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"some","id":"method-some"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"sort","id":"method-sort"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"splice","id":"method-splice"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"sum","id":"method-sum"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"toArray","id":"method-toArray"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"toMap","id":"method-toMap"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"toValueMap","id":"method-toValueMap"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"union","id":"method-union"},{"tagname":"method","owner":"Ext.Array","meta":{},"name":"unique","id":"method-unique"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.Array","singleton":true,"override":null,"inheritdoc":null,"id":"class-Ext.Array","mixins":[],"mixedInto":[]});