Ext.data.JsonP.Date({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Date.html#Date' target='_blank'>Date.js</a></div></pre><div class='doc-contents'><p>Creates <code>Date</code> instances which let you work with dates and times.</p>\n\n<p>If you supply no arguments, the constructor creates a <code>Date</code> object for today's\ndate and time according to local time. If you supply some arguments but not\nothers, the missing arguments are set to 0. If you supply any arguments, you\nmust supply at least the year, month, and day. You can omit the hours, minutes,\nseconds, and milliseconds.</p>\n\n<p>The date is measured in milliseconds since midnight 01 January, 1970 UTC. A day\nholds 86,400,000 milliseconds. The <code>Date</code> object range is -100,000,000 days to\n100,000,000 days relative to 01 January, 1970 UTC.</p>\n\n<p>The <code>Date</code> object provides uniform behavior across platforms.</p>\n\n<p>The <code>Date</code> object supports a number of UTC (universal) methods, as well as\nlocal time methods. UTC, also known as Greenwich Mean Time (GMT), refers to the\ntime as set by the World Time Standard. The local time is the time known to the\ncomputer where JavaScript is executed.</p>\n\n<p>Invoking <code>Date</code> in a non-constructor context (i.e., without the <code>new</code> operator)\nwill return a string representing the current time.</p>\n\n<p>Note that <code>Date</code> objects can only be instantiated by calling <code>Date</code> or using it\nas a constructor; unlike other JavaScript object types, <code>Date</code> objects have no\nliteral syntax.</p>\n\n<h1>Several ways to assign dates</h1>\n\n<p>The following example shows several ways to assign dates:</p>\n\n<pre><code>today = new Date();\nbirthday = new Date(\"December 19, 1989 03:24:00\");\nbirthday = new Date(1989,11,19);\nbirthday = new Date(1989,11,17,3,24,0);\n</code></pre>\n\n<h1>Calculating elapsed time</h1>\n\n<p>The following examples show how to determine the elapsed time between two dates:</p>\n\n<pre><code>// using static methods\nvar start = <a href=\"#!/api/Date-static-method-now\" rel=\"Date-static-method-now\" class=\"docClass\">Date.now</a>();\n// the event you'd like to time goes here:\ndoSomethingForALongTime();\nvar end = <a href=\"#!/api/Date-static-method-now\" rel=\"Date-static-method-now\" class=\"docClass\">Date.now</a>();\nvar elapsed = end - start; // time in milliseconds\n\n// if you have Date objects\nvar start = new Date();\n// the event you'd like to time goes here:\ndoSomethingForALongTime();\nvar end = new Date();\nvar elapsed = end.getTime() - start.getTime(); // time in milliseconds\n\n// if you want to test a function and get back its return\nfunction printElapsedTime (fTest) {\n    var nStartTime = <a href=\"#!/api/Date-static-method-now\" rel=\"Date-static-method-now\" class=\"docClass\">Date.now</a>(), vReturn = fTest(), nEndTime = <a href=\"#!/api/Date-static-method-now\" rel=\"Date-static-method-now\" class=\"docClass\">Date.now</a>();\n    alert(\"Elapsed time: \" + String(nEndTime - nStartTime) + \"\n    milliseconds\");\n    return vReturn;\n}\n\nyourFunctionReturn = printElapsedTime(yourFunction);\n</code></pre>\n\n<h1>ISO 8601 formatted dates</h1>\n\n<p>The following example shows how to formate a date in an ISO 8601 format using\nUTC:</p>\n\n<pre><code>// use a function for the exact format desired...\nfunction ISODateString(d){\nfunction pad(n){return n&lt;10 ? '0'+n : n}\nreturn d.getUTCFullYear()+'-'\n    + pad(d.getUTCMonth()+1)+'-'\n    + pad(d.getUTCDate())+'T'\n    + pad(d.getUTCHours())+':'\n    + pad(d.getUTCMinutes())+':'\n    + pad(d.getUTCSeconds())+'Z'}\n\nvar d = new Date();\nprint(ISODateString(d)); // prints something like 2009-09-28T19:03:12Z\n</code></pre>\n\n<div class=\"notice\">\nDocumentation for this class comes from <a href=\"https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Date\">MDN</a>\nand is available under <a href=\"http://creativecommons.org/licenses/by-sa/2.0/\">Creative Commons: Attribution-Sharealike license</a>.\n</div>\n\n</div><div class='members'><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/Date-method-constructor' class='name expandable'>Date</a>( <span class='pre'>[year], [month], [day], [hour], [minute], [second], [millisecond]</span> ) : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a></div><div class='description'><div class='short'>Creates new Date object. ...</div><div class='long'><p>Creates new Date object.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>year</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Either UNIX timestamp, date string, or year (when month and day parameters also provided):</p>\n\n<ul>\n<li><p>Integer value representing the number of milliseconds since 1 January 1970\n00:00:00 UTC (Unix Epoch).</p></li>\n<li><p>String value representing a date. The string should be in a format recognized\nby the parse method (IETF-compliant RFC 1123 timestamps).</p></li>\n<li><p>Integer value representing the year. For compatibility (in order to avoid the\nY2K problem), you should always specify the year in full; use 1998, rather\nthan 98.</p></li>\n</ul>\n\n</div></li><li><span class='pre'>month</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>Integer value representing the month, beginning with 0 for January to 11\nfor December.</p>\n</div></li><li><span class='pre'>day</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>Integer value representing the day of the month (1-31).</p>\n</div></li><li><span class='pre'>hour</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>Integer value representing the hour of the day (0-23).</p>\n</div></li><li><span class='pre'>minute</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>Integer value representing the minute segment (0-59) of a time reading.</p>\n</div></li><li><span class='pre'>second</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>Integer value representing the second segment (0-59) of a time reading.</p>\n</div></li><li><span class='pre'>millisecond</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>Integer value representing the millisecond segment (0-999) of a time reading.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getDate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getDate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getDate' class='name expandable'>getDate</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The second statement below assigns the value 25 to the variable <code>day</code>, based on the value of the\n<code>Date</code> object <code>Xmas95</code>.</p>\n\n<pre><code>Xmas95 = new Date(\"December 25, 1995 23:15:00\")\nday = Xmas95.getDate()\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Value between 1 and 31.</p>\n</div></li></ul></div></div></div><div id='method-getDay' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getDay' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getDay' class='name expandable'>getDay</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The value returned by <code>getDay</code> is an integer corresponding to the day of the week: 0 for Sunday, 1\nfor Monday, 2 for Tuesday, and so on.</p>\n\n<p>The second statement below assigns the value 1 to <code>weekday</code>, based on the value of the <code>Date</code>\nobject <code>Xmas95</code>. December 25, 1995, is a Monday.</p>\n\n<pre><code>Xmas95 = new Date(\"December 25, 1995 23:15:00\");\nweekday = Xmas95.getDay();\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>A numeric representation of the day from Sunday (0) to\nSaturday (6).</p>\n</div></li></ul></div></div></div><div id='method-getFullYear' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getFullYear' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getFullYear' class='name expandable'>getFullYear</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The value returned by <code>getFullYear</code> is an absolute number. For dates between the years 1000 and\n9999, <code>getFullYear</code> returns a four-digit number, for example, 1995. Use this function to make sure\na year is compliant with years after 2000.</p>\n\n<p>Use this method instead of the <code>getYear</code> method.</p>\n\n<p>The following example assigns the four-digit value of the current year to the variable yr.</p>\n\n<pre><code>var today = new Date();\nvar yr = today.getFullYear();\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Four digit representation of the year.</p>\n</div></li></ul></div></div></div><div id='method-getHours' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getHours' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getHours' class='name expandable'>getHours</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The second statement below assigns the value 23 to the variable <code>hours</code>, based on the value of the\n<code>Date</code> object <code>Xmas95</code>.</p>\n\n<pre><code>Xmas95 = new Date(\"December 25, 1995 23:15:00\")\nhours = Xmas95.getHours()\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Value between 0 and 23, using 24-hour clock.</p>\n</div></li></ul></div></div></div><div id='method-getMilliseconds' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getMilliseconds' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getMilliseconds' class='name expandable'>getMilliseconds</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The following example assigns the milliseconds portion of the current time to the variable ms.</p>\n\n<pre><code>var ms;\nToday = new Date();\nms = Today.getMilliseconds();\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>A number between 0 and 999.</p>\n</div></li></ul></div></div></div><div id='method-getMinutes' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getMinutes' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getMinutes' class='name expandable'>getMinutes</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The second statement below assigns the value 15 to the variable <code>minutes</code>, based on the value of\nthe <code>Date</code> object <code>Xmas95</code>.</p>\n\n<pre><code>Xmas95 = new Date(\"December 25, 1995 23:15:00\")\nminutes = Xmas95.getMinutes()\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Value between 0 and 59.</p>\n</div></li></ul></div></div></div><div id='method-getMonth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getMonth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getMonth' class='name expandable'>getMonth</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The second statement below assigns the value 11 to the variable <code>month</code>, based on the value of the\n<code>Date</code> object <code>Xmas95</code>.</p>\n\n<pre><code>Xmas95 = new Date(\"December 25, 1995 23:15:00\")\nmonth = Xmas95.getMonth()\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>An integer between 0 and 11. 0 corresponds to January, 1 to February, and so on.</p>\n</div></li></ul></div></div></div><div id='method-getSeconds' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getSeconds' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getSeconds' class='name expandable'>getSeconds</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The second statement below assigns the value 30 to the variable <code>secs</code>, based on the value of the\n<code>Date</code> object <code>Xmas95</code>.</p>\n\n<pre><code>Xmas95 = new Date(\"December 25, 1995 23:15:30\")\nsecs = Xmas95.getSeconds()\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Value between 0 and 59.</p>\n</div></li></ul></div></div></div><div id='method-getTime' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getTime' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getTime' class='name expandable'>getTime</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The value returned by the <code>getTime</code> method is the number of milliseconds since 1 January 1970\n00:00:00 UTC. You can use this method to help assign a date and time to another <code>Date</code> object.</p>\n\n<p>This method is functionally equivalent to the <code>valueOf</code> method.</p>\n\n<p>Using getTime for copying dates</p>\n\n<p>Constructing a date object with the identical time value.</p>\n\n<pre><code>var birthday = new Date(1994, 12, 10);\nvar copy = new Date();\ncopy.setTime(birthday.getTime());\n</code></pre>\n\n<p>Measuring execution time</p>\n\n<p>Subtracting two subsequent getTime calls on newly generated Date objects, give the time span\nbetween these two calls. This can be used to calculate the executing time of some operations.</p>\n\n<pre><code>var end, start;\n\nstart = new Date();\nfor (var i = 0; i &lt; 1000; i++)\n    Math.sqrt(i);\nend = new Date();\n\nconsole.log(\"Operation took \" + (end.getTime() - start.getTime()) + \" msec\");\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Number of milliseconds since 1/1/1970 (GMT).</p>\n</div></li></ul></div></div></div><div id='method-getTimezoneOffset' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getTimezoneOffset' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getTimezoneOffset' class='name expandable'>getTimezoneOffset</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The time-zone offset is the difference, in minutes, between UTC and local time. Note that this\nmeans that the offset is positive if the local timezone is behind UTC and negative if it is ahead.\nFor example, if your time zone is UTC+10 (Australian Eastern Standard Time), -600 will be returned.\nDaylight savings time prevents this value from being a constant even for a given locale</p>\n\n<pre><code>x = new Date()\ncurrentTimeZoneOffsetInHours = x.getTimezoneOffset()/60\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Minutes between GMT and local time.</p>\n</div></li></ul></div></div></div><div id='method-getUTCDate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getUTCDate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getUTCDate' class='name expandable'>getUTCDate</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The following example assigns the day portion of the current date to the variable <code>d</code>.</p>\n\n<pre><code>var d;\nToday = new Date();\nd = Today.getUTCDate();\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Integer between 1 and 31 representing the day.</p>\n</div></li></ul></div></div></div><div id='method-getUTCDay' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getUTCDay' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getUTCDay' class='name expandable'>getUTCDay</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The following example assigns the weekday portion of the current date to the variable <code>weekday</code>.</p>\n\n<pre><code>var weekday;\nToday = new Date()\nweekday = Today.getUTCDay()\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>A numeric representation of the day from Sunday (0) to\nSaturday (6).</p>\n</div></li></ul></div></div></div><div id='method-getUTCFullYear' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getUTCFullYear' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getUTCFullYear' class='name expandable'>getUTCFullYear</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The following example assigns the four-digit value of the current year to the variable <code>yr</code>.</p>\n\n<pre><code>var yr;\nToday = new Date();\nyr = Today.getUTCFullYear();\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Four digit representation of the year.</p>\n</div></li></ul></div></div></div><div id='method-getUTCHours' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getUTCHours' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getUTCHours' class='name expandable'>getUTCHours</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The following example assigns the hours portion of the current time to the variable <code>hrs</code>.</p>\n\n<pre><code>var hrs;\nToday = new Date();\nhrs = Today.getUTCHours();\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Value between 0 and 23.</p>\n</div></li></ul></div></div></div><div id='method-getUTCMilliseconds' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getUTCMilliseconds' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getUTCMilliseconds' class='name expandable'>getUTCMilliseconds</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The following example assigns the milliseconds portion of the current time to the variable <code>ms</code>.</p>\n\n<pre><code>var ms;\nToday = new Date();\nms = Today.getUTCMilliseconds();\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Milliseconds portion of the Date.</p>\n</div></li></ul></div></div></div><div id='method-getUTCMinutes' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getUTCMinutes' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getUTCMinutes' class='name expandable'>getUTCMinutes</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The following example assigns the minutes portion of the current time to the variable <code>min</code>.</p>\n\n<pre><code>var min;\nToday = new Date();\nmin = Today.getUTCMinutes();\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Value between 0 and 59.</p>\n</div></li></ul></div></div></div><div id='method-getUTCMonth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getUTCMonth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getUTCMonth' class='name expandable'>getUTCMonth</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The following example assigns the month portion of the current date to the variable <code>mon</code>.</p>\n\n<pre><code>var mon;\nToday = new Date();\nmon = Today.getUTCMonth();\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Value between 0 (January) and 11 (December).</p>\n</div></li></ul></div></div></div><div id='method-getUTCSeconds' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-getUTCSeconds' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-getUTCSeconds' class='name expandable'>getUTCSeconds</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The following example assigns the seconds portion of the current time to the variable <code>sec</code>.</p>\n\n<pre><code>var sec;\nToday = new Date();\nsec = Today.getUTCSeconds();\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Value between 0 and 59.</p>\n</div></li></ul></div></div></div><div id='method-setDate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-setDate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-setDate' class='name expandable'>setDate</a>( <span class='pre'>dayValue</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets the day of the month (1-31) for a specified date according to local time. ...</div><div class='long'><p>Sets the day of the month (1-31) for a specified date according to local time.</p>\n\n<p>If the parameter you specify is outside of the expected range, <code>setDate</code> attempts to update the\ndate information in the <code>Date</code> object accordingly. For example, if you use 0 for <code>dayValue</code>, the\ndate will be set to the last day of the previous month.</p>\n\n<p>The second statement below changes the day for theBigDay to July 24 from its original value.</p>\n\n<pre><code>theBigDay = new Date(\"July 27, 1962 23:30:00\")\ntheBigDay.setDate(24)\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>dayValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer from 1 to 31, representing the day of the month.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>New date represented as milliseconds.</p>\n</div></li></ul></div></div></div><div id='method-setFullYear' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-setFullYear' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-setFullYear' class='name expandable'>setFullYear</a>( <span class='pre'>yearValue, monthValue, dayValue</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets the full year (4 digits for 4-digit years) for a specified date according to\nlocal time. ...</div><div class='long'><p>Sets the full year (4 digits for 4-digit years) for a specified date according to\nlocal time.</p>\n\n<p>If you do not specify the <code>monthValue</code> and <code>dayValue</code> parameters, the values returned from the\n<code>getMonth</code> and <code>getDate</code> methods are used.</p>\n\n<p>If a parameter you specify is outside of the expected range, <code>setFullYear</code> attempts to update the\nother parameters and the date information in the <code>Date</code> object accordingly. For example, if you\nspecify 15 for monthValue, the year is incremented by 1 (year + 1), and 3 is used for the month.</p>\n\n<p>theBigDay = new Date();\ntheBigDay.setFullYear(1997);</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>yearValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer specifying the numeric value of the year, for example, 1995.</p>\n</div></li><li><span class='pre'>monthValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 11 representing the months January through\nDecember.</p>\n</div></li><li><span class='pre'>dayValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 1 and 31 representing the day of the month. If you\nspecify the <code>dayValue</code> parameter, you must also specify the <code>monthValue</code>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>New date represented as milliseconds.</p>\n</div></li></ul></div></div></div><div id='method-setHours' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-setHours' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-setHours' class='name expandable'>setHours</a>( <span class='pre'>hoursValue, minutesValue, secondsValue, msValue</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets the hours (0-23) for a specified date according to local time. ...</div><div class='long'><p>Sets the hours (0-23) for a specified date according to local time.</p>\n\n<p>If you do not specify the <code>minutesValue</code>, <code>secondsValue</code>, and <code>msValue</code> parameters, the values\nreturned from the <code>getUTCMinutes</code>, <code>getUTCSeconds</code>, and <code>getMilliseconds</code> methods are used.</p>\n\n<p>If a parameter you specify is outside of the expected range, setHours attempts to update the date\ninformation in the <code>Date</code> object accordingly. For example, if you use 100 for <code>secondsValue</code>, the\nminutes will be incremented by 1 (min + 1), and 40 will be used for seconds.</p>\n\n<pre><code>theBigDay.setHours(7)\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>hoursValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 23, representing the hour.</p>\n</div></li><li><span class='pre'>minutesValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 59, representing the minutes.</p>\n</div></li><li><span class='pre'>secondsValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 59, representing the seconds. If you specify the\n<code>secondsValue</code> parameter, you must also specify the <code>minutesValue</code>.</p>\n</div></li><li><span class='pre'>msValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>A number between 0 and 999, representing the milliseconds. If you specify the\n<code>msValue</code> parameter, you must also specify the <code>minutesValue</code> and <code>secondsValue</code>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>New date represented as milliseconds.</p>\n</div></li></ul></div></div></div><div id='method-setMilliseconds' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-setMilliseconds' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-setMilliseconds' class='name expandable'>setMilliseconds</a>( <span class='pre'>millisecondsValue</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets the milliseconds (0-999) for a specified date according to local time. ...</div><div class='long'><p>Sets the milliseconds (0-999) for a specified date according to local time.</p>\n\n<p>If you specify a number outside the expected range, the date information in the <code>Date</code> object is\nupdated accordingly. For example, if you specify 1005, the number of seconds is incremented by 1,\nand 5 is used for the milliseconds.</p>\n\n<pre><code>theBigDay = new Date();\ntheBigDay.setMilliseconds(100);\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>millisecondsValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>A number between 0 and 999, representing the milliseconds.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>New date represented as milliseconds.</p>\n</div></li></ul></div></div></div><div id='method-setMinutes' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-setMinutes' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-setMinutes' class='name expandable'>setMinutes</a>( <span class='pre'>minutesValue, secondsValue, msValue</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets the minutes (0-59) for a specified date according to local time. ...</div><div class='long'><p>Sets the minutes (0-59) for a specified date according to local time.</p>\n\n<p>If you do not specify the <code>secondsValue</code> and <code>msValue</code> parameters, the values returned from\n<code>getSeconds</code> and <code>getMilliseconds</code> methods are used.</p>\n\n<p>If a parameter you specify is outside of the expected range, <code>setMinutes</code> attempts to update the\ndate information in the <code>Date</code> object accordingly. For example, if you use 100 for <code>secondsValue</code>,\nthe minutes (<code>minutesValue</code>) will be incremented by 1 (minutesValue + 1), and 40 will be used for\nseconds.</p>\n\n<pre><code>theBigDay.setMinutes(45)\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>minutesValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 59, representing the minutes.</p>\n</div></li><li><span class='pre'>secondsValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 59, representing the seconds. If you\nspecify the secondsValue parameter, you must also specify the <code>minutesValue</code>.</p>\n</div></li><li><span class='pre'>msValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>A number between 0 and 999, representing the milliseconds. If you specify\nthe <code>msValue</code> parameter, you must also specify the <code>minutesValue</code> and <code>secondsValue</code>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>New date represented as milliseconds.</p>\n</div></li></ul></div></div></div><div id='method-setMonth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-setMonth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-setMonth' class='name expandable'>setMonth</a>( <span class='pre'>monthValue, dayValue</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets the month (0-11) for a specified date according to local time. ...</div><div class='long'><p>Sets the month (0-11) for a specified date according to local time.</p>\n\n<p>If you do not specify the <code>dayValue</code> parameter, the value returned from the <code>getDate</code> method is\nused.</p>\n\n<p>If a parameter you specify is outside of the expected range, <code>setMonth</code> attempts to update the date\ninformation in the <code>Date</code> object accordingly. For example, if you use 15 for <code>monthValue</code>, the year\nwill be incremented by 1 (year + 1), and 3 will be used for month.</p>\n\n<pre><code>theBigDay.setMonth(6)\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>monthValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 11 (representing the months January through\nDecember).</p>\n</div></li><li><span class='pre'>dayValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer from 1 to 31, representing the day of the month.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>New date represented as milliseconds.</p>\n</div></li></ul></div></div></div><div id='method-setSeconds' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-setSeconds' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-setSeconds' class='name expandable'>setSeconds</a>( <span class='pre'>secondsValue, msValue</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets the seconds (0-59) for a specified date according to local time. ...</div><div class='long'><p>Sets the seconds (0-59) for a specified date according to local time.</p>\n\n<p>If you do not specify the <code>msValue</code> parameter, the value returned from the <code>getMilliseconds</code> method\nis used.</p>\n\n<p>If a parameter you specify is outside of the expected range, <code>setSeconds</code> attempts to update the\ndate information in the <code>Date</code> object accordingly. For example, if you use 100 for <code>secondsValue</code>,\nthe minutes stored in the <code>Date</code> object will be incremented by 1, and 40 will be used for seconds.</p>\n\n<pre><code>theBigDay.setSeconds(30)\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>secondsValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 59.</p>\n</div></li><li><span class='pre'>msValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>A number between 0 and 999, representing the milliseconds. If you specify\nthe<code>msValue</code> parameter, you must also specify the <code>minutesValue</code> and <code>secondsValue</code>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>New date represented as milliseconds.</p>\n</div></li></ul></div></div></div><div id='method-setTime' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-setTime' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-setTime' class='name expandable'>setTime</a>( <span class='pre'>timeValue</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets the Date object to the time represented by a number of milliseconds since\nJanuary 1, 1970, 00:00:00 UTC, allowin...</div><div class='long'><p>Sets the Date object to the time represented by a number of milliseconds since\nJanuary 1, 1970, 00:00:00 UTC, allowing for negative numbers for times prior.</p>\n\n<p>Use the <code>setTime</code> method to help assign a date and time to another <code>Date</code> object.</p>\n\n<pre><code>theBigDay = new Date(\"July 1, 1999\")\nsameAsBigDay = new Date()\nsameAsBigDay.setTime(theBigDay.getTime())\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>timeValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer representing the number of milliseconds since 1 January\n1970, 00:00:00 UTC.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>New date represented as milliseconds.</p>\n</div></li></ul></div></div></div><div id='method-setUTCDate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-setUTCDate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-setUTCDate' class='name expandable'>setUTCDate</a>( <span class='pre'>dayValue</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets the day of the month (1-31) for a specified date according to universal time. ...</div><div class='long'><p>Sets the day of the month (1-31) for a specified date according to universal time.</p>\n\n<p>If a parameter you specify is outside of the expected range, <code>setUTCDate</code> attempts to update the\ndate information in the <code>Date</code> object accordingly. For example, if you use 40 for <code>dayValue</code>, and\nthe month stored in the <code>Date</code> object is June, the day will be changed to 10 and the month will be\nincremented to July.</p>\n\n<pre><code>theBigDay = new Date();\ntheBigDay.setUTCDate(20);\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>dayValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer from 1 to 31, representing the day of the month.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>New date represented as milliseconds.</p>\n</div></li></ul></div></div></div><div id='method-setUTCFullYear' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-setUTCFullYear' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-setUTCFullYear' class='name expandable'>setUTCFullYear</a>( <span class='pre'>yearValue, monthValue, dayValue</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets the full year (4 digits for 4-digit years) for a specified date according\nto universal time. ...</div><div class='long'><p>Sets the full year (4 digits for 4-digit years) for a specified date according\nto universal time.</p>\n\n<p>If you do not specify the <code>monthValue</code> and <code>dayValue</code> parameters, the values returned from the\n<code>getMonth</code> and <code>getDate</code> methods are used.</p>\n\n<p>If a parameter you specify is outside of the expected range, <code>setUTCFullYear</code> attempts to update\nthe other parameters and the date information in the <code>Date</code> object accordingly. For example, if you\nspecify 15 for <code>monthValue</code>, the year is incremented by 1 (year + 1), and 3 is used for the month.</p>\n\n<pre><code>theBigDay = new Date();\ntheBigDay.setUTCFullYear(1997);\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>yearValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer specifying the numeric value of the year, for example, 1995.</p>\n</div></li><li><span class='pre'>monthValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 11 representing the months January through\nDecember.</p>\n</div></li><li><span class='pre'>dayValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 1 and 31 representing the day of the month. If you\nspecify the <code>dayValue</code> parameter, you must also specify the <code>monthValue</code>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>New date represented as milliseconds.</p>\n</div></li></ul></div></div></div><div id='method-setUTCHours' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-setUTCHours' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-setUTCHours' class='name expandable'>setUTCHours</a>( <span class='pre'>hoursValue, minutesValue, secondsValue, msValue</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets the hour (0-23) for a specified date according to universal time. ...</div><div class='long'><p>Sets the hour (0-23) for a specified date according to universal time.</p>\n\n<p>If you do not specify the <code>minutesValue</code>, <code>secondsValue</code>, and <code>msValue</code> parameters, the values\nreturned from the <code>getUTCMinutes</code>, <code>getUTCSeconds</code>, and <code>getUTCMilliseconds</code> methods are used.</p>\n\n<p>If a parameter you specify is outside of the expected range, <code>setUTCHours</code> attempts to update the\ndate information in the <code>Date</code> object accordingly. For example, if you use 100 for <code>secondsValue</code>,\nthe minutes will be incremented by 1 (min + 1), and 40 will be used for seconds.</p>\n\n<pre><code>theBigDay = new Date();\ntheBigDay.setUTCHours(8);\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>hoursValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 23, representing the hour.</p>\n</div></li><li><span class='pre'>minutesValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 59, representing the minutes.</p>\n</div></li><li><span class='pre'>secondsValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 59, representing the seconds. If you specify the\n<code>secondsValue</code> parameter, you must also specify the <code>minutesValue</code>.</p>\n</div></li><li><span class='pre'>msValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>A number between 0 and 999, representing the milliseconds. If you specify the\n<code>msValue</code> parameter, you must also specify the <code>minutesValue</code> and <code>secondsValue</code>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>New date represented as milliseconds.</p>\n</div></li></ul></div></div></div><div id='method-setUTCMilliseconds' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-setUTCMilliseconds' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-setUTCMilliseconds' class='name expandable'>setUTCMilliseconds</a>( <span class='pre'>millisecondsValue</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets the milliseconds (0-999) for a specified date according to universal time. ...</div><div class='long'><p>Sets the milliseconds (0-999) for a specified date according to universal time.</p>\n\n<p>If a parameter you specify is outside of the expected range, <code>setUTCMilliseconds</code> attempts to\nupdate the date information in the <code>Date</code> object accordingly. For example, if you use 1100 for\n<code>millisecondsValue</code>, the seconds stored in the Date object will be incremented by 1, and 100 will\nbe used for milliseconds.</p>\n\n<pre><code>theBigDay = new Date();\ntheBigDay.setUTCMilliseconds(500);\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>millisecondsValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>A number between 0 and 999, representing the milliseconds.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>New date represented as milliseconds.</p>\n</div></li></ul></div></div></div><div id='method-setUTCMinutes' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-setUTCMinutes' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-setUTCMinutes' class='name expandable'>setUTCMinutes</a>( <span class='pre'>minutesValue, secondsValue, msValue</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets the minutes (0-59) for a specified date according to universal time. ...</div><div class='long'><p>Sets the minutes (0-59) for a specified date according to universal time.</p>\n\n<p>If you do not specify the <code>secondsValue</code> and <code>msValue</code> parameters, the values returned from\n<code>getUTCSeconds</code> and <code>getUTCMilliseconds</code> methods are used.</p>\n\n<p>If a parameter you specify is outside of the expected range, <code>setUTCMinutes</code> attempts to update the\ndate information in the <code>Date</code> object accordingly. For example, if you use 100 for <code>secondsValue</code>,\nthe minutes (<code>minutesValue</code>) will be incremented by 1 (<code>minutesValue</code> + 1), and 40 will be used for\nseconds.</p>\n\n<pre><code>theBigDay = new Date();\ntheBigDay.setUTCMinutes(43);\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>minutesValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 59, representing the minutes.</p>\n</div></li><li><span class='pre'>secondsValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 59, representing the seconds. If you specify the <code>secondsValue</code> parameter, you must also specify the <code>minutesValue</code>.</p>\n</div></li><li><span class='pre'>msValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>A number between 0 and 999, representing the milliseconds. If you specify the <code>msValue</code> parameter, you must also specify the <code>minutesValue</code> and <code>secondsValue</code>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>New date represented as milliseconds.</p>\n</div></li></ul></div></div></div><div id='method-setUTCMonth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-setUTCMonth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-setUTCMonth' class='name expandable'>setUTCMonth</a>( <span class='pre'>monthValue, dayValue</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets the month (0-11) for a specified date according to universal time. ...</div><div class='long'><p>Sets the month (0-11) for a specified date according to universal time.</p>\n\n<p>If you do not specify the <code>dayValue</code> parameter, the value returned from the <code>getUTCDate</code> method is\nused.</p>\n\n<p>If a parameter you specify is outside of the expected range, <code>setUTCMonth</code> attempts to update the\ndate information in the <code>Date</code> object accordingly. For example, if you use 15 for <code>monthValue</code>, the\nyear will be incremented by 1 (year + 1), and 3 will be used for month.</p>\n\n<pre><code>theBigDay = new Date();\ntheBigDay.setUTCMonth(11);\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>monthValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 11, representing the months January through\nDecember.</p>\n</div></li><li><span class='pre'>dayValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer from 1 to 31, representing the day of the month.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>New date represented as milliseconds.</p>\n</div></li></ul></div></div></div><div id='method-setUTCSeconds' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-setUTCSeconds' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-setUTCSeconds' class='name expandable'>setUTCSeconds</a>( <span class='pre'>secondsValue, msValue</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets the seconds (0-59) for a specified date according to universal time. ...</div><div class='long'><p>Sets the seconds (0-59) for a specified date according to universal time.</p>\n\n<p>If you do not specify the <code>msValue</code> parameter, the value returned from the <code>getUTCMilliseconds</code>\nmethods is used.</p>\n\n<p>If a parameter you specify is outside of the expected range, <code>setUTCSeconds</code> attempts to update the\ndate information in the <code>Date</code> object accordingly. For example, if you use 100 for <code>secondsValue</code>,\nthe minutes stored in the <code>Date</code> object will be incremented by 1, and 40 will be used for seconds.</p>\n\n<pre><code>theBigDay = new Date();\ntheBigDay.setUTCSeconds(20);\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>secondsValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 59.</p>\n</div></li><li><span class='pre'>msValue</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>A number between 0 and 999, representing the milliseconds.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>New date represented as milliseconds.</p>\n</div></li></ul></div></div></div><div id='method-toDateString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-toDateString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-toDateString' class='name expandable'>toDateString</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns the \"date\" portion of the Date as a human-readable string in American English. ...</div><div class='long'><p>Returns the \"date\" portion of the Date as a human-readable string in American English.</p>\n\n<p><a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a> instances refer to a specific point in time. Calling <code>toString</code> will return the\ndate formatted in a human readable form in American English. In SpiderMonkey, this consists of the\ndate portion (day, month, and year) followed by the time portion (hours, minutes, seconds, and time\nzone). Sometimes it is desirable to obtain a string of the date portion; such a thing can be\naccomplished with the <code>toDateString</code> method.</p>\n\n<p>The <code>toDateString</code> method is especially useful because compliant engines implementing ECMA-262 may\ndiffer in the string obtained from <code>toString</code> for <code>Date</code> objects, as the format is implementation-\ndependent and simple string slicing approaches may not produce consistent results across multiple\nengines.</p>\n\n<pre><code>var d = new Date(1993, 6, 28, 14, 39, 7);\nprintln(d.toString()); // prints Wed Jul 28 1993 14:39:07 GMT-0600 (PDT)\nprintln(d.toDateString()); // prints Wed Jul 28 1993\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Human-readable string, in local time.</p>\n</div></li></ul></div></div></div><div id='method-toJSON' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-toJSON' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-toJSON' class='name expandable'>toJSON</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns a JSON representation of the Date object. ...</div><div class='long'><p>Returns a JSON representation of the Date object.</p>\n\n<p>Date instances refer to a specific point in time. Calling <code>toJSON()</code>\nreturns a JSON formatted string representing the Date object's\nvalue.  This method is generally intended to, by default, usefully\nserialize Date objects during JSON serialization.</p>\n\n<pre><code>var jsonDate = (new Date()).toJSON();\nvar backToDate = new Date(jsonDate);\n\nconsole.log(\"Serialized date object: \" + jsonDate);\n// Serialized date object: 2013-01-17T12:59:08.449Z\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Date value in <code>YYYY-MM-DDTHH-MM-SS.MMMZ</code> format.</p>\n</div></li></ul></div></div></div><div id='method-toLocaleDateString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-toLocaleDateString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-toLocaleDateString' class='name expandable'>toLocaleDateString</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns the \"date\" portion of the Date as a string, using the current locale's\nconventions. ...</div><div class='long'><p>Returns the \"date\" portion of the Date as a string, using the current locale's\nconventions.</p>\n\n<p>The <code>toLocaleDateString</code> method relies on the underlying operating system in formatting dates. It\nconverts the date to a string using the formatting convention of the operating system where the\nscript is running. For example, in the United States, the month appears before the date (04/15/98),\nwhereas in Germany the date appears before the month (15.04.98). If the operating system is not\nyear-2000 compliant and does not use the full year for years before 1900 or over 2000,\n<code>toLocaleDateString</code> returns a string that is not year-2000 compliant. <code>toLocaleDateString</code> behaves\nsimilarly to <code>toString</code> when converting a year that the operating system does not properly format.</p>\n\n<p>Methods such as <code>getDate</code>, <code>getMonth</code>, and <code>getFullYear</code> give more portable results than\n<code>toLocaleDateString</code>. Use <code>toLocaleDateString</code> when the intent is to display to the user a string\nformatted using the regional format chosen by the user. Be aware that this method, due to its\nnature, behaves differently depending on the operating system and on the user's settings.</p>\n\n<p>In the following example, <code>today</code> is a <code>Date</code> object:</p>\n\n<pre><code>today = new Date(95,11,18,17,28,35) //months are represented by 0 to 11\ntoday.toLocaleDateString()\n</code></pre>\n\n<p>In this example, <code>toLocaleDateString</code> returns a string value that is similar to the following form.\nThe exact format depends on the platform, locale and user's settings.</p>\n\n<pre><code>12/18/95\n</code></pre>\n\n<p>You shouldn't use this method in contexts where you rely on a particular format or locale.</p>\n\n<pre><code>\"Last visit: \" + someDate.toLocaleDateString(); // Good example\n\"Last visit was at \" + someDate.toLocaleDateString(); // Bad example\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Human-readable string that may be formatted differently depending\non the country.</p>\n</div></li></ul></div></div></div><div id='method-toLocaleString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-toLocaleString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-toLocaleString' class='name expandable'>toLocaleString</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Converts a date to a string, using the current locale's conventions. ...</div><div class='long'><p>Converts a date to a string, using the current locale's conventions. Overrides\nthe <code><a href=\"#!/api/Object-method-toLocaleString\" rel=\"Object-method-toLocaleString\" class=\"docClass\">Object.toLocaleString</a></code> method.</p>\n\n<p>The <code>toLocaleString</code> method relies on the underlying operating system in formatting dates. It\nconverts the date to a string using the formatting convention of the operating system where the\nscript is running. For example, in the United States, the month appears before the date (04/15/98),\nwhereas in Germany the date appears before the month (15.04.98). If the operating system is not\nyear-2000 compliant and does not use the full year for years before 1900 or over 2000,\n<code>toLocaleString</code> returns a string that is not year-2000 compliant. <code>toLocaleString</code> behaves\nsimilarly to <code>toString</code> when converting a year that the operating system does not properly format.</p>\n\n<p>Methods such as <code>getDate</code>, <code>getMonth</code>, <code>getFullYear</code>, <code>getHours</code>, <code>getMinutes</code>, and <code>getSeconds</code>\ngive more portable results than <code>toLocaleString</code>. Use <code>toLocaleString</code> when the intent is to\ndisplay to the user a string formatted using the regional format chosen by the user. Be aware that\nthis method, due to its nature, behaves differently depending on the operating system and on the\nuser's settings.</p>\n\n<p>In the following example, <code>today</code> is a <code>Date</code> object:</p>\n\n<pre><code>today = new Date(95,11,18,17,28,35); //months are represented by 0 to 11\ntoday.toLocaleString();\n</code></pre>\n\n<p>In this example, <code>toLocaleString</code> returns a string value that is similar to the following form. The\nexact format depends on the platform, locale and user's settings.</p>\n\n<pre><code>12/18/95 17:28:35\n</code></pre>\n\n<p>You shouldn't use this method in contexts where you rely on a particular format or locale.</p>\n\n<pre><code>\"Last visit: \" + someDate.toLocaleString(); // Good example\n\"Last visit was at \" + someDate.toLocaleString(); // Bad example\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Human-readable string that may be formatted differently depending\non the country.</p>\n</div></li></ul></div></div></div><div id='method-toLocaleTimeString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-toLocaleTimeString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-toLocaleTimeString' class='name expandable'>toLocaleTimeString</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns the \"time\" portion of the Date as a string, using the current locale's\nconventions. ...</div><div class='long'><p>Returns the \"time\" portion of the Date as a string, using the current locale's\nconventions.</p>\n\n<p>The <code>toLocaleTimeString</code> method relies on the underlying operating system in formatting dates. It\nconverts the date to a string using the formatting convention of the operating system where the\nscript is running. For example, in the United States, the month appears before the date (04/15/98),\nwhereas in Germany the date appears before the month (15.04.98).</p>\n\n<p>Methods such as <code>getHours</code>, <code>getMinutes</code>, and <code>getSeconds</code> give more consistent results than\n<code>toLocaleTimeString</code>. Use <code>toLocaleTimeString</code> when the intent is to display to the user a string\nformatted using the regional format chosen by the user. Be aware that this method, due to its\nnature, behaves differently depending on the operating system and on the user's settings.</p>\n\n<p>In the following example, <code>today</code> is a <code>Date</code> object:</p>\n\n<pre><code>today = new Date(95,11,18,17,28,35) //months are represented by 0 to 11\ntoday.toLocaleTimeString()\n</code></pre>\n\n<p>In this example, <code>toLocaleTimeString</code> returns a string value that is similar to the following form.\nThe exact format depends on the platform.</p>\n\n<pre><code>17:28:35\n</code></pre>\n\n<p>You shouldn't use this method in contexts where you rely on a particular format or locale.</p>\n\n<pre><code>\"Last visit: \" + someDate.toLocaleTimeString(); // Good example\n\"Last visit was at \" + someDate.toLocaleTimeString(); // Bad example\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Human-readable string that may be formatted differently depending\non the country.</p>\n</div></li></ul></div></div></div><div id='method-toString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-toString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-toString' class='name expandable'>toString</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns a string representing the specified Date object. ...</div><div class='long'><p>Returns a string representing the specified Date object. Overrides the\n<code>Object.prototype.toString</code> method.</p>\n\n<p>The <code>Date</code> object overrides the toString method of the Object object; it does not inherit\n<code><a href=\"#!/api/Object-method-toString\" rel=\"Object-method-toString\" class=\"docClass\">Object.toString</a></code>. For <code>Date</code> objects, the <code>toString</code> method returns a string representation of the\nobject.</p>\n\n<p><code>toString</code> always returns a string representation of the date in American English.</p>\n\n<p>JavaScript calls the <code>toString</code> method automatically when a date is to be represented as a text\nvalue or when a date is referred to in a string concatenation.</p>\n\n<p>The following assigns the <code>toString</code> value of a <code>Date</code> object to <code>myVar</code>:</p>\n\n<pre><code>x = new Date();\nmyVar=x.toString();   //assigns a value to myVar similar to:\n//Mon Sep 28 1998 14:36:22 GMT-0700 (Pacific Daylight Time)\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Human-readable string of the date in local time.</p>\n</div></li></ul></div></div></div><div id='method-toTimeString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-toTimeString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-toTimeString' class='name expandable'>toTimeString</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns the \"time\" portion of the Date as a human-readable string. ...</div><div class='long'><p>Returns the \"time\" portion of the Date as a human-readable string.</p>\n\n<p><a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a> instances refer to a specific point in time. Calling <code>toString</code> will return the\ndate formatted in a human readable form in American English. In SpiderMonkey, this consists of the\ndate portion (day, month, and year) followed by the time portion (hours, minutes, seconds, and\ntime zone). Sometimes it is desirable to obtain a string of the time portion; such a thing can be\naccomplished with the <code>toTimeString</code> method.</p>\n\n<p>The <code>toTimeString</code> method is especially useful because compliant engines implementing ECMA-262 may\ndiffer in the string obtained from <code>toString</code> for <code>Date</code> objects, as the format is implementation-\ndependent; simple string slicing approaches may not produce consistent results across multiple\nengines.</p>\n\n<pre><code>var d = new Date(1993, 6, 28, 14, 39, 7);\nprintln(d.toString()); // prints Wed Jul 28 1993 14:39:07 GMT-0600 (PDT)\nprintln(d.toTimeString()); // prints 14:39:07 GMT-0600 (PDT)\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Human-readable string of the date in local time.</p>\n</div></li></ul></div></div></div><div id='method-toUTCString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-toUTCString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-toUTCString' class='name expandable'>toUTCString</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Converts a date to a string, using the universal time convention. ...</div><div class='long'><p>Converts a date to a string, using the universal time convention.</p>\n\n<p>The value returned by <code>toUTCString</code> is a readable string in American English in the UTC time zone.\nThe format of the return value may vary according to the platform.</p>\n\n<pre><code>var today = new Date();\nvar UTCstring = today.toUTCString();\n// Mon, 03 Jul 2006 21:44:38 GMT\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>String of the date in UTC.</p>\n</div></li></ul></div></div></div><div id='method-valueOf' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-method-valueOf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-method-valueOf' class='name expandable'>valueOf</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the primitive value of a Date object. ...</div><div class='long'><p>Returns the primitive value of a Date object. Overrides the\nObject.prototype.valueOf method.</p>\n\n<p>The <code>valueOf</code> method returns the primitive value of a <code>Date</code> object as a number data type, the\nnumber of milliseconds since midnight 01 January, 1970 UTC.</p>\n\n<p>This method is functionally equivalent to the <code>getTime</code> method.</p>\n\n<p>This method is usually called internally by JavaScript and not explicitly in code.</p>\n\n<pre><code>x = new Date(56, 6, 17);\nmyVar = x.valueOf();      //assigns -424713600000 to myVar\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Date represented as milliseconds.</p>\n</div></li></ul></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-UTC' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-static-method-UTC' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-static-method-UTC' class='name expandable'>UTC</a>( <span class='pre'>year, month, date, hrs, min, sec, ms</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Accepts the same parameters as the longest form of the constructor, and returns\nthe number of milliseconds in a Date ...</div><div class='long'><p>Accepts the same parameters as the longest form of the constructor, and returns\nthe number of milliseconds in a <code>Date</code> object since January 1, 1970, 00:00:00,\nuniversal time.</p>\n\n<p><code>UTC</code> takes comma-delimited date parameters and returns the number of milliseconds between January\n1, 1970, 00:00:00, universal time and the time you specified.</p>\n\n<p>You should specify a full year for the year; for example, 1998. If a year between 0 and 99 is\nspecified, the method converts the year to a year in the 20th century (1900 + year); for example,\nif you specify 95, the year 1995 is used.</p>\n\n<p>The <code>UTC</code> method differs from the <code>Date</code> constructor in two ways.\n*   <code><a href=\"#!/api/Date-static-method-UTC\" rel=\"Date-static-method-UTC\" class=\"docClass\">Date.UTC</a></code> uses universal time instead of the local time.\n*   <code><a href=\"#!/api/Date-static-method-UTC\" rel=\"Date-static-method-UTC\" class=\"docClass\">Date.UTC</a></code> returns a time value as a number instead of creating a <code>Date</code> object.</p>\n\n<p>If a parameter you specify is outside of the expected range, the <code>UTC</code> method updates the other\nparameters to allow for your number. For example, if you use 15 for month, the year will be\nincremented by 1 (year + 1), and 3 will be used for the month.</p>\n\n<p>Because <code>UTC</code> is a static method of <code>Date</code>, you always use it as <code><a href=\"#!/api/Date-static-method-UTC\" rel=\"Date-static-method-UTC\" class=\"docClass\">Date.UTC</a>()</code>, rather than as a\nmethod of a <code>Date</code> object you created.</p>\n\n<p>The following statement creates a <code>Date</code> object using GMT instead of local time:</p>\n\n<pre><code>gmtDate = new Date(<a href=\"#!/api/Date-static-method-UTC\" rel=\"Date-static-method-UTC\" class=\"docClass\">Date.UTC</a>(96, 11, 1, 0, 0, 0));\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>year</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>A year after 1900.</p>\n</div></li><li><span class='pre'>month</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 11 representing the month.</p>\n</div></li><li><span class='pre'>date</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 1 and 31 representing the day of the month.</p>\n</div></li><li><span class='pre'>hrs</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 23 representing the hours.</p>\n</div></li><li><span class='pre'>min</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 59 representing the minutes.</p>\n</div></li><li><span class='pre'>sec</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 59 representing the seconds.</p>\n</div></li><li><span class='pre'>ms</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 999 representing the milliseconds.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Number of milliseconds since January 1, 1970, 00:00:00, universal time.</p>\n</div></li></ul></div></div></div><div id='static-method-now' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-static-method-now' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-static-method-now' class='name expandable'>now</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Returns the numeric value corresponding to the current time. ...</div><div class='long'><p>Returns the numeric value corresponding to the current time.</p>\n\n<p>The <code>now</code> method returns the milliseconds elapsed since 1 January 1970 00:00:00 UTC up until now as\na number.</p>\n\n<p>When using <code>now</code> to create timestamps or unique IDs, keep in mind that the resolution may be 15\nmilliseconds on Windows, so you could end up with several equal values if <code>now</code> is called multiple\ntimes within a short time span.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Returns the number of milliseconds elapsed since 1 January 1970 00:00:00 UTC.</p>\n</div></li></ul></div></div></div><div id='static-method-parse' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Date'>Date</span><br/><a href='source/Date.html#Date-static-method-parse' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Date-static-method-parse' class='name expandable'>parse</a>( <span class='pre'>dateString</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Parses a string representation of a date, and returns the number of milliseconds\nsince January 1, 1970, 00:00:00, loc...</div><div class='long'><p>Parses a string representation of a date, and returns the number of milliseconds\nsince January 1, 1970, 00:00:00, local time.</p>\n\n<p>The <code>parse</code> method takes a date string (such as <code>\"Dec 25, 1995\"</code>) and returns the number of\nmilliseconds since January 1, 1970, 00:00:00 UTC. The local time zone is used to interpret\narguments that do not contain time zone information. This function is useful for setting date\nvalues based on string values, for example in conjunction with the <code>setTime</code> method and the\n<a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a> object.</p>\n\n<p>Given a string representing a time, parse returns the time value. It accepts the IETF standard (RFC\n1123 Section 5.2.14 and elsewhere) date syntax: <code>\"Mon, 25 Dec 1995 13:30:00 GMT\"</code>. It understands\nthe continental US time-zone abbreviations, but for general use, use a time-zone offset, for\nexample, <code>\"Mon, 25 Dec 1995 13:30:00 GMT+0430\"</code> (4 hours, 30 minutes east of the Greenwich\nmeridian). If you do not specify a time zone, the local time zone is assumed. GMT and UTC are\nconsidered equivalent.</p>\n\n<h3>Using parse</h3>\n\n<p>If <code>IPOdate</code> is an existing <code>Date</code> object, then you can set it to August 9, 1995 (local time) as\nfollows:</p>\n\n<pre><code>IPOdate.setTime(<a href=\"#!/api/Date-static-method-parse\" rel=\"Date-static-method-parse\" class=\"docClass\">Date.parse</a>(\"Aug 9, 1995\"));\n</code></pre>\n\n<p>Some other examples:</p>\n\n<pre><code>// Returns 807937200000 in time zone GMT-0300, and other values in other\n// timezones, since the argument does not specify a time zone.\n<a href=\"#!/api/Date-static-method-parse\" rel=\"Date-static-method-parse\" class=\"docClass\">Date.parse</a>(\"Aug 9, 1995\");\n\n// Returns 807926400000 no matter the local time zone.\n<a href=\"#!/api/Date-static-method-parse\" rel=\"Date-static-method-parse\" class=\"docClass\">Date.parse</a>(\"Wed, 09 Aug 1995 00:00:00 GMT\");\n\n// Returns 807937200000 in timezone GMT-0300, and other values in other\n// timezones, since there is no time zone specifier in the argument.\n<a href=\"#!/api/Date-static-method-parse\" rel=\"Date-static-method-parse\" class=\"docClass\">Date.parse</a>(\"Wed, 09 Aug 1995 00:00:00\");\n\n// Returns 0 no matter the local time zone.\n<a href=\"#!/api/Date-static-method-parse\" rel=\"Date-static-method-parse\" class=\"docClass\">Date.parse</a>(\"Thu, 01 Jan 1970 00:00:00 GMT\");\n\n// Returns 14400000 in timezone GMT-0400, and other values in other\n// timezones, since there is no time zone specifier in the argument.\n<a href=\"#!/api/Date-static-method-parse\" rel=\"Date-static-method-parse\" class=\"docClass\">Date.parse</a>(\"Thu, 01 Jan 1970 00:00:00\");\n\n// Returns 14400000 no matter the local time zone.\n<a href=\"#!/api/Date-static-method-parse\" rel=\"Date-static-method-parse\" class=\"docClass\">Date.parse</a>(\"Thu, 01 Jan 1970 00:00:00 GMT-0400\");\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>dateString</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>A string representing a date.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Number of milliseconds since January 1, 1970, 00:00:00, local time.</p>\n</div></li></ul></div></div></div></div></div></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Date","meta":{"static":true},"name":"UTC","id":"static-method-UTC"},{"tagname":"method","owner":"Date","meta":{"static":true},"name":"now","id":"static-method-now"},{"tagname":"method","owner":"Date","meta":{"static":true},"name":"parse","id":"static-method-parse"}],"event":[],"css_mixin":[]},"files":[{"href":"Date.html#Date","filename":"Date.js"}],"linenr":1,"members":{"property":[],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Date","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"Date","meta":{},"name":"getDate","id":"method-getDate"},{"tagname":"method","owner":"Date","meta":{},"name":"getDay","id":"method-getDay"},{"tagname":"method","owner":"Date","meta":{},"name":"getFullYear","id":"method-getFullYear"},{"tagname":"method","owner":"Date","meta":{},"name":"getHours","id":"method-getHours"},{"tagname":"method","owner":"Date","meta":{},"name":"getMilliseconds","id":"method-getMilliseconds"},{"tagname":"method","owner":"Date","meta":{},"name":"getMinutes","id":"method-getMinutes"},{"tagname":"method","owner":"Date","meta":{},"name":"getMonth","id":"method-getMonth"},{"tagname":"method","owner":"Date","meta":{},"name":"getSeconds","id":"method-getSeconds"},{"tagname":"method","owner":"Date","meta":{},"name":"getTime","id":"method-getTime"},{"tagname":"method","owner":"Date","meta":{},"name":"getTimezoneOffset","id":"method-getTimezoneOffset"},{"tagname":"method","owner":"Date","meta":{},"name":"getUTCDate","id":"method-getUTCDate"},{"tagname":"method","owner":"Date","meta":{},"name":"getUTCDay","id":"method-getUTCDay"},{"tagname":"method","owner":"Date","meta":{},"name":"getUTCFullYear","id":"method-getUTCFullYear"},{"tagname":"method","owner":"Date","meta":{},"name":"getUTCHours","id":"method-getUTCHours"},{"tagname":"method","owner":"Date","meta":{},"name":"getUTCMilliseconds","id":"method-getUTCMilliseconds"},{"tagname":"method","owner":"Date","meta":{},"name":"getUTCMinutes","id":"method-getUTCMinutes"},{"tagname":"method","owner":"Date","meta":{},"name":"getUTCMonth","id":"method-getUTCMonth"},{"tagname":"method","owner":"Date","meta":{},"name":"getUTCSeconds","id":"method-getUTCSeconds"},{"tagname":"method","owner":"Date","meta":{},"name":"setDate","id":"method-setDate"},{"tagname":"method","owner":"Date","meta":{},"name":"setFullYear","id":"method-setFullYear"},{"tagname":"method","owner":"Date","meta":{},"name":"setHours","id":"method-setHours"},{"tagname":"method","owner":"Date","meta":{},"name":"setMilliseconds","id":"method-setMilliseconds"},{"tagname":"method","owner":"Date","meta":{},"name":"setMinutes","id":"method-setMinutes"},{"tagname":"method","owner":"Date","meta":{},"name":"setMonth","id":"method-setMonth"},{"tagname":"method","owner":"Date","meta":{},"name":"setSeconds","id":"method-setSeconds"},{"tagname":"method","owner":"Date","meta":{},"name":"setTime","id":"method-setTime"},{"tagname":"method","owner":"Date","meta":{},"name":"setUTCDate","id":"method-setUTCDate"},{"tagname":"method","owner":"Date","meta":{},"name":"setUTCFullYear","id":"method-setUTCFullYear"},{"tagname":"method","owner":"Date","meta":{},"name":"setUTCHours","id":"method-setUTCHours"},{"tagname":"method","owner":"Date","meta":{},"name":"setUTCMilliseconds","id":"method-setUTCMilliseconds"},{"tagname":"method","owner":"Date","meta":{},"name":"setUTCMinutes","id":"method-setUTCMinutes"},{"tagname":"method","owner":"Date","meta":{},"name":"setUTCMonth","id":"method-setUTCMonth"},{"tagname":"method","owner":"Date","meta":{},"name":"setUTCSeconds","id":"method-setUTCSeconds"},{"tagname":"method","owner":"Date","meta":{},"name":"toDateString","id":"method-toDateString"},{"tagname":"method","owner":"Date","meta":{},"name":"toJSON","id":"method-toJSON"},{"tagname":"method","owner":"Date","meta":{},"name":"toLocaleDateString","id":"method-toLocaleDateString"},{"tagname":"method","owner":"Date","meta":{},"name":"toLocaleString","id":"method-toLocaleString"},{"tagname":"method","owner":"Date","meta":{},"name":"toLocaleTimeString","id":"method-toLocaleTimeString"},{"tagname":"method","owner":"Date","meta":{},"name":"toString","id":"method-toString"},{"tagname":"method","owner":"Date","meta":{},"name":"toTimeString","id":"method-toTimeString"},{"tagname":"method","owner":"Date","meta":{},"name":"toUTCString","id":"method-toUTCString"},{"tagname":"method","owner":"Date","meta":{},"name":"valueOf","id":"method-valueOf"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Date","singleton":false,"override":null,"inheritdoc":null,"id":"class-Date","mixins":[],"mixedInto":[]});