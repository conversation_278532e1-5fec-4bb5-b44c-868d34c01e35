Ext.data.JsonP.Ext_data_amf_Encoder({"alternateClassNames":[],"aliases":{"data":["amf.Encoder"]},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":"Ext.Base","uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><strong>Ext.data.amf.Encoder</strong></div></div><h4>Files</h4><div class='dependency'><a href='source/Encoder.html#Ext-data-amf-Encoder' target='_blank'>Encoder.js</a></div></pre><div class='doc-contents'><p>This class serializes data in the Action Message Format (AMF) format.\nIt can write simple and complex objects, to be used in conjunction with an\nAMF-compliant server.\nTo encode a byte array, first construct an Encoder, optionally setting the format:</p>\n\n<pre><code>var encoder = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.data.amf.Encoder\" rel=\"Ext.data.amf.Encoder\" class=\"docClass\">Ext.data.amf.Encoder</a>', {\n  format: 3\n});\n</code></pre>\n\n<p>Then use the writer methods to out data to the :</p>\n\n<pre><code>encoder.writeObject(1);\n</code></pre>\n\n<p>And access the data through the <a href=\"#!/api/Ext.data.amf.Encoder-property-bytes\" rel=\"Ext.data.amf.Encoder-property-bytes\" class=\"docClass\">bytes</a> property:</p>\n\n<pre><code>encoder.bytes;\n</code></pre>\n\n<p>You can also reset the class to start a new byte array:</p>\n\n<pre><code>encoder.clear();\n</code></pre>\n\n<p>Current limitations:\nAMF3 format (format:3)\n- writeObject will write out XML object, not legacy XMLDocument objects. A\n  writeXmlDocument method is provided for explicitly writing XMLDocument\n  objects.\n- Each object is written out explicitly, not using the reference tables\n  supported by the AMF format. This means the function does NOT support\n  circular reference objects.\n- Array objects: only the numbered indices and data will be written out.\n  Associative values will be ignored.\n- Objects that aren't Arrays, Dates, Strings, Document (XML) or primitive\n  values will be written out as anonymous objects with dynamic data.\n- There's no JavaScript equivalent to the ByteArray type in ActionScript,\n  hence data will never be searialized as ByteArrays by the writeObject\n  function. A writeByteArray method is provided for writing out ByteArray objects.</p>\n\n<p>AMF0 format (format:0)\n- Each object is written out explicitly, not using the reference tables\n  supported by the AMF format. This means the function does NOT support\n  circular reference objects.\n- Array objects: the function always writes an associative array (following\n  the behavior of flex).\n- Objects that aren't Arrays, Dates, Strings, Document (XML) or primitive\n  values will be written out as anonymous objects.</p>\n\n<p>For more information on working with AMF data please refer to the\n<a href=\"#/guide/amf\">AMF Guide</a>.</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-cfg'>Config options</h3><div class='subsection'><div id='cfg-format' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-cfg-format' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-cfg-format' class='name expandable'>format</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>3</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-bytes' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-property-bytes' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-property-bytes' class='name expandable'>bytes</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='readonly signature' >readonly</strong></div><div class='description'><div class='short'>The constructed byte array. ...</div><div class='long'><p>The constructed byte array.</p>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/Ext.data.amf.Encoder-method-constructor' class='name expandable'>Ext.data.amf.Encoder</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.data.amf.Encoder\" rel=\"Ext.data.amf.Encoder\" class=\"docClass\">Ext.data.amf.Encoder</a></div><div class='description'><div class='short'>Creates new Encoder. ...</div><div class='long'><p>Creates new Encoder.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>Configuration options</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.amf.Encoder\" rel=\"Ext.data.amf.Encoder\" class=\"docClass\">Ext.data.amf.Encoder</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-applyFormat' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-applyFormat' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-applyFormat' class='name expandable'>applyFormat</a>( <span class='pre'>protocol_version</span> )</div><div class='description'><div class='short'>Sets the functions that will correctly serialize for the relevant\nprotocol version. ...</div><div class='long'><p>Sets the functions that will correctly serialize for the relevant\nprotocol version.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>protocol_version</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the protocol version to support</p>\n</div></li></ul></div></div></div><div id='method-callOverridden' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-clear' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-clear' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-clear' class='name expandable'>clear</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Reset all class states and starts a new empty array for encoding data. ...</div><div class='long'><p>Reset all class states and starts a new empty array for encoding data.\nThe method generates a new array for encoding, so it's safe to keep a\nreference to the old one.</p>\n</div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-convertXmlToString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-convertXmlToString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-convertXmlToString' class='name expandable'>convertXmlToString</a>( <span class='pre'>xml</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Converts an XML Document object to a string. ...</div><div class='long'><p>Converts an XML Document object to a string.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xml</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>XML document (type Document typically) to convert</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>A string representing the document</p>\n</div></li></ul></div></div></div><div id='method-destroy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-destroy' class='name expandable'>destroy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.util.ElementContainer-method-destroy' rel='Ext.util.ElementContainer-method-destroy' class='docClass'>Ext.util.ElementContainer.destroy</a>, <a href='#!/api/Ext.AbstractComponent-method-destroy' rel='Ext.AbstractComponent-method-destroy' class='docClass'>Ext.AbstractComponent.destroy</a>, <a href='#!/api/Ext.AbstractPlugin-method-destroy' rel='Ext.AbstractPlugin-method-destroy' class='docClass'>Ext.AbstractPlugin.destroy</a></p></div></div></div><div id='method-encode29Int' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-encode29Int' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-encode29Int' class='name expandable'>encode29Int</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Encodes a U29 int, returning a byte array with the encoded number. ...</div><div class='long'><p>Encodes a U29 int, returning a byte array with the encoded number.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><ul>\n<li>unsigned int value</li>\n</ul>\n\n</div></li></ul></div></div></div><div id='method-encode3Utf8StringLen' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-encode3Utf8StringLen' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-encode3Utf8StringLen' class='name expandable'>encode3Utf8StringLen</a>( <span class='pre'>utf8Data</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Encode the length of a UTF-8 string in AMF3 format. ...</div><div class='long'><p>Encode the length of a UTF-8 string in AMF3 format.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>utf8Data</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>byte array with the encoded data</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>byte array encoding of length</p>\n</div></li></ul></div></div></div><div id='method-encodeDouble' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-encodeDouble' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-encodeDouble' class='name expandable'>encodeDouble</a>( <span class='pre'>num</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Encodes an IEEE-754 double-precision number. ...</div><div class='long'><p>Encodes an IEEE-754 double-precision number.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>num</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the number to encode</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>byte array containing the encoded number</p>\n</div></li></ul></div></div></div><div id='method-encodeUtf8Char' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-encodeUtf8Char' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-encodeUtf8Char' class='name expandable'>encodeUtf8Char</a>( <span class='pre'>c</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Convert a UTF 16 char to a UTF 8 char ...</div><div class='long'><p>Convert a UTF 16 char to a UTF 8 char</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>c</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>char 16-bit code to convert</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>byte array with the UTF 8 values</p>\n</div></li></ul></div></div></div><div id='method-encodeUtf8String' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-encodeUtf8String' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-encodeUtf8String' class='name expandable'>encodeUtf8String</a>( <span class='pre'>str</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Accepts a string and returns a byte array encoded in UTF-8 ...</div><div class='long'><p>Accepts a string and returns a byte array encoded in UTF-8</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>str</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>String to encode</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>byte array with string encoded in UTF-8 format</p>\n</div></li></ul></div></div></div><div id='method-encodeXInt' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-encodeXInt' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-encodeXInt' class='name expandable'>encodeXInt</a>( <span class='pre'>value, byte_count</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Encode 16- or 32-bit integers into big-endian (network order) bytes ...</div><div class='long'><p>Encode 16- or 32-bit integers into big-endian (network order) bytes</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the number to encode.</p>\n</div></li><li><span class='pre'>byte_count</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>2 or 4 byte encoding</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>byte array with encoded number</p>\n</div></li></ul></div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getFormat' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-cfg-format' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-getFormat' class='name expandable'>getFormat</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the value of format. ...</div><div class='long'><p>Returns the value of <a href=\"#!/api/Ext.data.amf.Encoder-cfg-format\" rel=\"Ext.data.amf.Encoder-cfg-format\" class=\"docClass\">format</a>.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-isXmlDocument' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-isXmlDocument' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-isXmlDocument' class='name expandable'>isXmlDocument</a>( <span class='pre'>item</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Tries to determine if an object is an XML document ...</div><div class='long'><p>Tries to determine if an object is an XML document</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>to identify</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>true if it's an XML document, false otherwise</p>\n</div></li></ul></div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setFormat' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-cfg-format' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-setFormat' class='name expandable'>setFormat</a>( <span class='pre'>format</span> )</div><div class='description'><div class='short'>Sets the value of format. ...</div><div class='long'><p>Sets the value of <a href=\"#!/api/Ext.data.amf.Encoder-cfg-format\" rel=\"Ext.data.amf.Encoder-cfg-format\" class=\"docClass\">format</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>format</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-write0Array' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write0Array' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write0Array' class='name expandable'>write0Array</a>( <span class='pre'>arr</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes an associative array in AMF0 format. ...</div><div class='long'><p>Writes an associative array in AMF0 format.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>arr</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>the array to serialize.</p>\n</div></li></ul></div></div></div><div id='method-write0Boolean' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write0Boolean' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write0Boolean' class='name expandable'>write0Boolean</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes the appropriate AMF0 boolean value to the byte array. ...</div><div class='long'><p>Writes the appropriate AMF0 boolean value to the byte array.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">boolean</a><div class='sub-desc'><p>The value to write</p>\n</div></li></ul></div></div></div><div id='method-write0Date' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write0Date' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write0Date' class='name expandable'>write0Date</a>( <span class='pre'>date</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes a date in AMF0 format. ...</div><div class='long'><p>Writes a date in AMF0 format.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>the date object</p>\n</div></li></ul></div></div></div><div id='method-write0GenericObject' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write0GenericObject' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write0GenericObject' class='name expandable'>write0GenericObject</a>( <span class='pre'>obj</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Write an object to the byte array in AMF0 format. ...</div><div class='long'><p>Write an object to the byte array in AMF0 format.\nSince we don't have the class information form Flex, the object\nis written as an anonymous object.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>obj</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>the object to serialize.</p>\n</div></li></ul></div></div></div><div id='method-write0Null' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write0Null' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write0Null' class='name expandable'>write0Null</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes the AMF0 null value to the byte array. ...</div><div class='long'><p>Writes the AMF0 null value to the byte array.</p>\n</div></div></div><div id='method-write0Number' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write0Number' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write0Number' class='name expandable'>write0Number</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes a numberic value to the byte array in AMF0 format ...</div><div class='long'><p>Writes a numberic value to the byte array in AMF0 format</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>A native numeric value, Number instance or one of Infinity, -Infinity or NaN</p>\n</div></li></ul></div></div></div><div id='method-write0ObjectProperty' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write0ObjectProperty' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write0ObjectProperty' class='name expandable'>write0ObjectProperty</a>( <span class='pre'>key, value</span> )</div><div class='description'><div class='short'>Writes a key-value pair in AMF0 format. ...</div><div class='long'><p>Writes a key-value pair in AMF0 format.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>key</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the name of the property</p>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>to write in AMF0 format</p>\n</div></li></ul></div></div></div><div id='method-write0ShortUtf8String' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write0ShortUtf8String' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write0ShortUtf8String' class='name expandable'>write0ShortUtf8String</a>( <span class='pre'>str</span> )</div><div class='description'><div class='short'>Writes a short UTF8 string preceded with a 16-bit length. ...</div><div class='long'><p>Writes a short UTF8 string preceded with a 16-bit length.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>str</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the string to write</p>\n</div></li></ul></div></div></div><div id='method-write0StrictArray' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write0StrictArray' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write0StrictArray' class='name expandable'>write0StrictArray</a>( <span class='pre'>arr</span> )</div><div class='description'><div class='short'>Writes a strict-array in AMF0 format. ...</div><div class='long'><p>Writes a strict-array in AMF0 format. Unordered parts are ignored (e.g.\na[\"hello\"] will not be encoded). This function is included for\ncompleteness and will never be called by writeObject.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>arr</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>the array to serialize.</p>\n</div></li></ul></div></div></div><div id='method-write0String' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write0String' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write0String' class='name expandable'>write0String</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Write an AMF0 UTF-8 string to the byte array ...</div><div class='long'><p>Write an AMF0 UTF-8 string to the byte array</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The string to write</p>\n</div></li></ul></div></div></div><div id='method-write0Undefined' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write0Undefined' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write0Undefined' class='name expandable'>write0Undefined</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes the AMF0 undefined value to the byte array. ...</div><div class='long'><p>Writes the AMF0 undefined value to the byte array.</p>\n</div></div></div><div id='method-write0Xml' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write0Xml' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write0Xml' class='name expandable'>write0Xml</a>( <span class='pre'>xml</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes an XMLDocument in AMF0 format. ...</div><div class='long'><p>Writes an XMLDocument in AMF0 format.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xml</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>XML document (type Document typically) to write</p>\n</div></li></ul></div></div></div><div id='method-write3Array' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write3Array' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write3Array' class='name expandable'>write3Array</a>( <span class='pre'>arr</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes an array in AMF3 format. ...</div><div class='long'><p>Writes an array in AMF3 format. Only the ordered part of the array use handled.\nUnordered parts are ignored (e.g. a[\"hello\"] will not be encoded).</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>arr</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>the array to serialize.</p>\n</div></li></ul></div></div></div><div id='method-write3Boolean' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write3Boolean' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write3Boolean' class='name expandable'>write3Boolean</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes the appropriate AMF3 boolean value to the byte array. ...</div><div class='long'><p>Writes the appropriate AMF3 boolean value to the byte array.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">boolean</a><div class='sub-desc'><p>The value to write</p>\n</div></li></ul></div></div></div><div id='method-write3ByteArray' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write3ByteArray' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write3ByteArray' class='name expandable'>write3ByteArray</a>( <span class='pre'>arr</span> )</div><div class='description'><div class='short'>Write a byte array in AMF3 format. ...</div><div class='long'><p>Write a byte array in AMF3 format. This function is never called directly\nby writeObject since there's no way to distinguish a regular array from a\nbyte array.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>arr</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>the object to serialize.</p>\n</div></li></ul></div></div></div><div id='method-write3Date' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write3Date' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write3Date' class='name expandable'>write3Date</a>( <span class='pre'>date</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes a date in AMF3 format. ...</div><div class='long'><p>Writes a date in AMF3 format.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>the date object</p>\n</div></li></ul></div></div></div><div id='method-write3GenericObject' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write3GenericObject' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write3GenericObject' class='name expandable'>write3GenericObject</a>( <span class='pre'>obj</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Write an object to the byte array in AMF3 format. ...</div><div class='long'><p>Write an object to the byte array in AMF3 format.\nSince we don't have the class information form Flex, the object\nis written as an anonymous object.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>obj</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>the object to serialize.</p>\n</div></li></ul></div></div></div><div id='method-write3Null' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write3Null' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write3Null' class='name expandable'>write3Null</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes the AMF3 null value to the byte array. ...</div><div class='long'><p>Writes the AMF3 null value to the byte array.</p>\n</div></div></div><div id='method-write3Number' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write3Number' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write3Number' class='name expandable'>write3Number</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes a numberic value to the byte array in AMF3 format ...</div><div class='long'><p>Writes a numberic value to the byte array in AMF3 format</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>A native numeric value, Number instance or one of Infinity, -Infinity or NaN</p>\n</div></li></ul></div></div></div><div id='method-write3String' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write3String' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write3String' class='name expandable'>write3String</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Write an AMF3 UTF-8 string to the byte array ...</div><div class='long'><p>Write an AMF3 UTF-8 string to the byte array</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The string to write</p>\n</div></li></ul></div></div></div><div id='method-write3Undefined' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write3Undefined' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write3Undefined' class='name expandable'>write3Undefined</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes the AMF3 undefined value to the byte array. ...</div><div class='long'><p>Writes the AMF3 undefined value to the byte array.</p>\n</div></div></div><div id='method-write3Xml' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write3Xml' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write3Xml' class='name expandable'>write3Xml</a>( <span class='pre'>xml</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes an XML object (ActionScript 3 new XML object) in AMF3 format. ...</div><div class='long'><p>Writes an XML object (ActionScript 3 new XML object) in AMF3 format.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xml</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>XML document (type Document typically) to write</p>\n</div></li></ul></div></div></div><div id='method-write3XmlDocument' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write3XmlDocument' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write3XmlDocument' class='name expandable'>write3XmlDocument</a>( <span class='pre'>xml</span> )</div><div class='description'><div class='short'>Writes an Legacy XMLDocument (ActionScript Legacy XML object) in AMF3\nformat. ...</div><div class='long'><p>Writes an Legacy XMLDocument (ActionScript Legacy XML object) in AMF3\nformat. Must be called explicitly.\nThe writeObject method will call writeXml and not writeXmlDocument.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xml</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>XML document (type Document typically) to write</p>\n</div></li></ul></div></div></div><div id='method-write3XmlWithType' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-write3XmlWithType' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-write3XmlWithType' class='name expandable'>write3XmlWithType</a>( <span class='pre'>xml, amfType</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes an XML document in AMF3 format. ...</div><div class='long'><p>Writes an XML document in AMF3 format.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xml</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>XML document (type Document typically)</p>\n</div></li><li><span class='pre'>amfType</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">number</a><div class='sub-desc'><p>Either 0x07 or 0x0B - the AMF3 object type to use</p>\n</div></li></ul></div></div></div><div id='method-writeAmfHeader' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-writeAmfHeader' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-writeAmfHeader' class='name expandable'>writeAmfHeader</a>( <span class='pre'>headerName, mustUnderstand, value</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Write an AMF header to the byte array. ...</div><div class='long'><p>Write an AMF header to the byte array. AMF headers are always encoded in AMF0.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>headerName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the header name</p>\n</div></li><li><span class='pre'>mustUnderstand</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>true if the receiver must understand this header or else reject it, false otherwise</p>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>the value to serialize. Must be an object that can be serialized by AMF</p>\n</div></li></ul></div></div></div><div id='method-writeAmfMessage' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-writeAmfMessage' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-writeAmfMessage' class='name expandable'>writeAmfMessage</a>( <span class='pre'>targetUri, responseUri, body</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes an AMF message to the byte array. ...</div><div class='long'><p>Writes an AMF message to the byte array. AMF messages are always encoded in AMF0.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>targetUri</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the class / method to call</p>\n</div></li><li><span class='pre'>responseUri</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the response should call here</p>\n</div></li><li><span class='pre'>body</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>the parameters to pass to the called method, wrapped in an array</p>\n</div></li></ul></div></div></div><div id='method-writeAmfPacket' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-writeAmfPacket' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-writeAmfPacket' class='name expandable'>writeAmfPacket</a>( <span class='pre'>headers</span> )</div><div class='description'><div class='short'>Writes an AMF packet to the byte array ...</div><div class='long'><p>Writes an AMF packet to the byte array</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>headers</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>the headers to serialize. Each item in the array</p>\n\n<pre><code>           should be an object with three fields:\n           name, mustUnderstand, value\n</code></pre>\n\n<p>@param {Array} messages the messages to serialize. Each item in the array</p>\n\n<pre><code>           should be an object with three fields:\n           targetUri, responseUri, body\n</code></pre>\n</div></li></ul></div></div></div><div id='method-writeByte' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-writeByte' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-writeByte' class='name expandable'>writeByte</a>( <span class='pre'>b</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes a byte to the byte array ...</div><div class='long'><p>Writes a byte to the byte array</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>b</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">number</a><div class='sub-desc'><p>Byte to write to the array</p>\n</div></li></ul></div></div></div><div id='method-writeBytes' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-writeBytes' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-writeBytes' class='name expandable'>writeBytes</a>( <span class='pre'>b</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Writes a byte array to the byte array ...</div><div class='long'><p>Writes a byte array to the byte array</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>b</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">number</a><div class='sub-desc'><p>Byte array to append to the array</p>\n</div></li></ul></div></div></div><div id='method-writeObject' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.Encoder'>Ext.data.amf.Encoder</span><br/><a href='source/Encoder.html#Ext-data-amf-Encoder-method-writeObject' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.Encoder-method-writeObject' class='name expandable'>writeObject</a>( <span class='pre'>item</span> )</div><div class='description'><div class='short'>Write the appropriate data items to the byte array. ...</div><div class='long'><p>Write the appropriate data items to the byte array. Supported types:\n- undefined\n- null\n- boolean\n- integer (if AMF3 - limited by 29-bit int, otherwise passed as double)\n- double\n- UTF-8 string\n- XML Document (identified by being instaneof Document. Can be generated with: new DOMParser()).parseFromString(xml, \"text/xml\");</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>A primitive or object to write to the stream</p>\n</div></li></ul></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-create' class='name expandable'>create</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create a new instance of this Class. ...</div><div class='long'><p>Create a new instance of this Class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.create({\n    someConfig: true\n});\n</code></pre>\n\n<p>All parameters are passed to the constructor of the class.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the created instance.</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div></div></div>","superclasses":["Ext.Base"],"meta":{},"code_type":"ext_define","requires":[],"html_meta":{},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"Encoder.html#Ext-data-amf-Encoder","filename":"Encoder.js"}],"linenr":2,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.data.amf.Encoder","meta":{"readonly":true},"name":"bytes","id":"property-bytes"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"}],"cfg":[{"tagname":"cfg","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"format","id":"cfg-format"}],"css_var":[],"method":[{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{},"name":"applyFormat","id":"method-applyFormat"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{},"name":"clear","id":"method-clear"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"convertXmlToString","id":"method-convertXmlToString"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"encode29Int","id":"method-encode29Int"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"encode3Utf8StringLen","id":"method-encode3Utf8StringLen"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"encodeDouble","id":"method-encodeDouble"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{},"name":"encodeUtf8Char","id":"method-encodeUtf8Char"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"encodeUtf8String","id":"method-encodeUtf8String"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{},"name":"encodeXInt","id":"method-encodeXInt"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{},"name":"getFormat","id":"method-getFormat"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{},"name":"isXmlDocument","id":"method-isXmlDocument"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{},"name":"setFormat","id":"method-setFormat"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write0Array","id":"method-write0Array"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write0Boolean","id":"method-write0Boolean"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write0Date","id":"method-write0Date"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write0GenericObject","id":"method-write0GenericObject"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write0Null","id":"method-write0Null"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write0Number","id":"method-write0Number"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{},"name":"write0ObjectProperty","id":"method-write0ObjectProperty"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{},"name":"write0ShortUtf8String","id":"method-write0ShortUtf8String"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{},"name":"write0StrictArray","id":"method-write0StrictArray"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write0String","id":"method-write0String"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write0Undefined","id":"method-write0Undefined"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write0Xml","id":"method-write0Xml"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write3Array","id":"method-write3Array"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write3Boolean","id":"method-write3Boolean"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{},"name":"write3ByteArray","id":"method-write3ByteArray"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write3Date","id":"method-write3Date"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write3GenericObject","id":"method-write3GenericObject"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write3Null","id":"method-write3Null"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write3Number","id":"method-write3Number"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write3String","id":"method-write3String"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write3Undefined","id":"method-write3Undefined"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write3Xml","id":"method-write3Xml"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{},"name":"write3XmlDocument","id":"method-write3XmlDocument"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"write3XmlWithType","id":"method-write3XmlWithType"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"writeAmfHeader","id":"method-writeAmfHeader"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"writeAmfMessage","id":"method-writeAmfMessage"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{},"name":"writeAmfPacket","id":"method-writeAmfPacket"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"writeByte","id":"method-writeByte"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{"private":true},"name":"writeBytes","id":"method-writeBytes"},{"tagname":"method","owner":"Ext.data.amf.Encoder","meta":{},"name":"writeObject","id":"method-writeObject"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.data.amf.Encoder","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.data.amf.Encoder","mixins":[],"mixedInto":[]});