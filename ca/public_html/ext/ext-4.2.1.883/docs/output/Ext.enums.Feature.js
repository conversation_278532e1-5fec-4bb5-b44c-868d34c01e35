Ext.data.JsonP.Ext_enums_Feature({"alternateClassNames":[],"aliases":{},"enum":{"type":"String","default":"feature.*","doc_only":true},"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/enums.html#Ext-enums-Feature' target='_blank'>enums.js</a></div></pre><div class='doc-contents'><p>Enumeration of all ftypes.</p>\n<p class='enum'><strong>ENUM:</strong> This enumeration defines a set of String values. It exists primarily for documentation purposes - in code use the actual string values like 'abstractsummary', don't reference them through this class like Ext.enums.Feature.abstractsummary.</p></div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-abstractsummary' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Feature'>Ext.enums.Feature</span><br/><a href='source/enums.html#Ext-enums-Feature' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Feature-property-abstractsummary' class='name expandable'>abstractsummary</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.feature.AbstractSummary. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.feature.AbstractSummary\" rel=\"Ext.grid.feature.AbstractSummary\" class=\"docClass\">Ext.grid.feature.AbstractSummary</a>.</p>\n<p>Defaults to: <code>'abstractsummary'</code></p></div></div></div><div id='property-feature' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Feature'>Ext.enums.Feature</span><br/><a href='source/enums.html#Ext-enums-Feature' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Feature-property-feature' class='name expandable'>feature</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.feature.Feature. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.feature.Feature\" rel=\"Ext.grid.feature.Feature\" class=\"docClass\">Ext.grid.feature.Feature</a>.</p>\n<p>Defaults to: <code>'feature'</code></p></div></div></div><div id='property-filters' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Feature'>Ext.enums.Feature</span><br/><a href='source/enums.html#Ext-enums-Feature' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Feature-property-filters' class='name expandable'>filters</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.ux.grid.FiltersFeature. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.ux.grid.FiltersFeature\" rel=\"Ext.ux.grid.FiltersFeature\" class=\"docClass\">Ext.ux.grid.FiltersFeature</a>.</p>\n<p>Defaults to: <code>'filters'</code></p></div></div></div><div id='property-grouping' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Feature'>Ext.enums.Feature</span><br/><a href='source/enums.html#Ext-enums-Feature' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Feature-property-grouping' class='name expandable'>grouping</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.feature.Grouping. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.feature.Grouping\" rel=\"Ext.grid.feature.Grouping\" class=\"docClass\">Ext.grid.feature.Grouping</a>.</p>\n<p>Defaults to: <code>'grouping'</code></p></div></div></div><div id='property-groupingsummary' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Feature'>Ext.enums.Feature</span><br/><a href='source/enums.html#Ext-enums-Feature' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Feature-property-groupingsummary' class='name expandable'>groupingsummary</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.feature.GroupingSummary. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.feature.GroupingSummary\" rel=\"Ext.grid.feature.GroupingSummary\" class=\"docClass\">Ext.grid.feature.GroupingSummary</a>.</p>\n<p>Defaults to: <code>'groupingsummary'</code></p></div></div></div><div id='property-rowbody' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Feature'>Ext.enums.Feature</span><br/><a href='source/enums.html#Ext-enums-Feature' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Feature-property-rowbody' class='name expandable'>rowbody</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.feature.RowBody. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.feature.RowBody\" rel=\"Ext.grid.feature.RowBody\" class=\"docClass\">Ext.grid.feature.RowBody</a>.</p>\n<p>Defaults to: <code>'rowbody'</code></p></div></div></div><div id='property-rowwrap' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Feature'>Ext.enums.Feature</span><br/><a href='source/enums.html#Ext-enums-Feature' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Feature-property-rowwrap' class='name expandable'>rowwrap</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Alias for Ext.grid.feature.RowWrap. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.feature.RowWrap\" rel=\"Ext.grid.feature.RowWrap\" class=\"docClass\">Ext.grid.feature.RowWrap</a>.</p>\n<p>Defaults to: <code>'rowwrap'</code></p></div></div></div><div id='property-summary' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.enums.Feature'>Ext.enums.Feature</span><br/><a href='source/enums.html#Ext-enums-Feature' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.enums.Feature-property-summary' class='name expandable'>summary</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Alias for Ext.grid.feature.Summary. ...</div><div class='long'><p>Alias for <a href=\"#!/api/Ext.grid.feature.Summary\" rel=\"Ext.grid.feature.Summary\" class=\"docClass\">Ext.grid.feature.Summary</a>.</p>\n<p>Defaults to: <code>'summary'</code></p></div></div></div></div></div></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"enums.html#Ext-enums-Feature","filename":"enums.js"}],"linenr":20,"members":{"property":[{"tagname":"property","owner":"Ext.enums.Feature","meta":{"private":null},"name":"abstractsummary","id":"property-abstractsummary"},{"tagname":"property","owner":"Ext.enums.Feature","meta":{"private":null},"name":"feature","id":"property-feature"},{"tagname":"property","owner":"Ext.enums.Feature","meta":{"private":null},"name":"filters","id":"property-filters"},{"tagname":"property","owner":"Ext.enums.Feature","meta":{"private":null},"name":"grouping","id":"property-grouping"},{"tagname":"property","owner":"Ext.enums.Feature","meta":{"private":null},"name":"groupingsummary","id":"property-groupingsummary"},{"tagname":"property","owner":"Ext.enums.Feature","meta":{"private":null},"name":"rowbody","id":"property-rowbody"},{"tagname":"property","owner":"Ext.enums.Feature","meta":{"private":true},"name":"rowwrap","id":"property-rowwrap"},{"tagname":"property","owner":"Ext.enums.Feature","meta":{"private":null},"name":"summary","id":"property-summary"}],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.enums.Feature","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.enums.Feature","mixins":[],"mixedInto":[]});