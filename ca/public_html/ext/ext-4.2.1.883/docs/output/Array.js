Ext.data.JsonP.Array({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Array.html#Array' target='_blank'>Array.js</a></div></pre><div class='doc-contents'><p>In JavaScript, the <code>Array</code> property of the global object is a constructor for\narray instances.</p>\n\n<p>An array is a JavaScript object. Note that you shouldn't use it as an\nassociative array, use <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> instead.</p>\n\n<h1>Creating an Array</h1>\n\n<p>The following example creates an array, msgArray, with a length of 0, then assigns values to\nmsgArray[0] and msgArray[99], changing the length of the array to 100.</p>\n\n<pre><code>var msgArray = new Array();\nmsgArray[0] = \"Hello\";\nmsgArray[99] = \"world\";\n\nif (msgArray.length == 100)\nprint(\"The length is 100.\");\n</code></pre>\n\n<h1>Creating a Two-dimensional Array</h1>\n\n<p>The following creates chess board as a two dimensional array of strings. The first move is made by\ncopying the 'P' in 6,4 to 4,4. The position 4,4 is left blank.</p>\n\n<pre><code>var board =\n[ ['R','N','B','Q','K','B','N','R'],\n['P','P','P','P','P','P','P','P'],\n[' ',' ',' ',' ',' ',' ',' ',' '],\n[' ',' ',' ',' ',' ',' ',' ',' '],\n[' ',' ',' ',' ',' ',' ',' ',' '],\n[' ',' ',' ',' ',' ',' ',' ',' '],\n['p','p','p','p','p','p','p','p'],\n['r','n','b','q','k','b','n','r']];\nprint(board.join('\\n') + '\\n\\n');\n\n// Move King's Pawn forward 2\nboard[4][4] = board[6][4];\nboard[6][4] = ' ';\nprint(board.join('\\n'));\n</code></pre>\n\n<p>Here is the output:</p>\n\n<pre><code>R,N,B,Q,K,B,N,R\nP,P,P,P,P,P,P,P\n , , , , , , ,\n , , , , , , ,\n , , , , , , ,\n , , , , , , ,\np,p,p,p,p,p,p,p\nr,n,b,q,k,b,n,r\n\nR,N,B,Q,K,B,N,R\nP,P,P,P,P,P,P,P\n , , , , , , ,\n , , , , , , ,\n , , , ,p, , ,\n , , , , , , ,\np,p,p,p, ,p,p,p\nr,n,b,q,k,b,n,r\n</code></pre>\n\n<h1>Accessing array elements</h1>\n\n<p>Array elements are nothing less than object properties, so they are accessed as such.</p>\n\n<pre><code>var myArray = new Array(\"Wind\", \"Rain\", \"Fire\");\nmyArray[0]; // \"Wind\"\nmyArray[1]; // \"Rain\"\n// etc.\nmyArray.length; // 3\n\n// Even if indices are properties, the following notation throws a syntax error\nmyArray.2;\n\n// It should be noted that in JavaScript, object property names are strings. Consequently,\nmyArray[0] === myArray[\"0\"];\nmyArray[1] === myArray[\"1\"];\n// etc.\n\n// However, this should be considered carefully\nmyArray[02]; // \"Fire\". The number 02 is converted as the \"2\" string\nmyArray[\"02\"]; // undefined. There is no property named \"02\"\n</code></pre>\n\n<h1>Relationship between length and numerical properties</h1>\n\n<p>An array's length property and numerical properties are connected. Here is some\ncode explaining how this relationship works.</p>\n\n<pre><code>var a = [];\n\na[0] = 'a';\nconsole.log(a[0]); // 'a'\nconsole.log(a.length); // 1\n\na[1] = 32;\nconsole.log(a[1]); // 32\nconsole.log(a.length); // 2\n\na[13] = 12345;\nconsole.log(a[13]); // 12345\nconsole.log(a.length); // 14\n\na.length = 10;\nconsole.log(a[13]); // undefined, when reducing the length elements after length+1 are removed\nconsole.log(a.length); // 10\n</code></pre>\n\n<h1>Creating an array using the result of a match</h1>\n\n<p>The result of a match between a regular expression and a string can create an array.\nThis array has properties and elements that provide information about the match. An\narray is the return value of <a href=\"#!/api/RegExp-method-exec\" rel=\"RegExp-method-exec\" class=\"docClass\">RegExp.exec</a>, <a href=\"#!/api/String-method-match\" rel=\"String-method-match\" class=\"docClass\">String.match</a>, and <a href=\"#!/api/String-method-replace\" rel=\"String-method-replace\" class=\"docClass\">String.replace</a>. To help\nexplain these properties and elements, look at the following example and then refer\nto the table below:</p>\n\n<pre><code>// Match one d followed by one or more b's followed by one d\n// Remember matched b's and the following d\n// Ignore case\n\nvar myRe = /d(b+)(d)/i;\nvar myArray = myRe.exec(\"cdbBdbsbz\");\n</code></pre>\n\n<p>The properties and elements returned from this match are as follows:</p>\n\n<table>\n<thead>\n<tr>\n<th></th>\n<th align=\"left\"> Property/Element </th>\n<th align=\"left\"> Description                                                                           </th>\n<th align=\"left\"> Example</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td></td>\n<td align=\"left\"> <code>input</code>          </td>\n<td align=\"left\"> A read-only property that reflects the original string against which the              </td>\n<td align=\"left\"> cdbBdbsbz</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> regular expression was matched.                                                       </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>index</code>          </td>\n<td align=\"left\"> A read-only property that is the zero-based index of the match in the string.         </td>\n<td align=\"left\"> 1</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>[0]</code>            </td>\n<td align=\"left\"> A read-only element that specifies the last matched characters.                       </td>\n<td align=\"left\"> dbBd</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>[1], ...[n]</code>    </td>\n<td align=\"left\"> Read-only elements that specify the parenthesized substring matches, if included in   </td>\n<td align=\"left\"> [1]: bB [2]: d</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> the regular expression. The number of possible parenthesized substrings is unlimited. </td>\n<td></td>\n</tr>\n</tbody>\n</table>\n\n\n<div class=\"notice\">\nDocumentation for this class comes from <a href=\"https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array\">MDN</a>\nand is available under <a href=\"http://creativecommons.org/licenses/by-sa/2.0/\">Creative Commons: Attribution-Sharealike license</a>.\n</div>\n\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-length' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-property-length' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-property-length' class='name expandable'>length</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Reflects the number of elements in an array. ...</div><div class='long'><p>Reflects the number of elements in an array.</p>\n\n<p>The value of the <code>length</code> property is an integer with a positive sign and a value less than 2 to the 32\npower (232).</p>\n\n<p>You can set the <code>length</code> property to truncate an array at any time. When you extend an array by changing\nits <code>length</code> property, the number of actual elements does not increase; for example, if you set <code>length</code>\nto 3 when it is currently 2, the array still contains only 2 elements.</p>\n\n<p>In the following example the array numbers is iterated through by looking at the <code>length</code> property to see\nhow many elements it has. Each value is then doubled.</p>\n\n<pre><code>var numbers = [1,2,3,4,5];\nfor (var i = 0; i &lt; numbers.length; i++) {\n    numbers[i] *= 2;\n}\n// numbers is now [2,4,6,8,10];\n</code></pre>\n\n<p>The following example shortens the array <code>statesUS</code> to a length of 50 if the current <code>length</code> is greater\nthan 50.</p>\n\n<pre><code>if (statesUS.length &gt; 50) {\n    statesUS.length=50\n}\n</code></pre>\n</div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/Array-method-constructor' class='name expandable'>Array</a>( <span class='pre'>items</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Creates new Array object. ...</div><div class='long'><p>Creates new Array object.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>items</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>...<div class='sub-desc'><p>Either a number that specifies the length of array or any number of items\nfor the array.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-concat' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-concat' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-concat' class='name expandable'>concat</a>( <span class='pre'>values</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Returns a new array comprised of this array joined with other array(s) and/or value(s). ...</div><div class='long'><p>Returns a new array comprised of this array joined with other array(s) and/or value(s).</p>\n\n<p><code>concat</code> creates a new array consisting of the elements in the <code>this</code> object on which it is called,\nfollowed in order by, for each argument, the elements of that argument (if the argument is an\narray) or the argument itself (if the argument is not an array).</p>\n\n<p><code>concat</code> does not alter <code>this</code> or any of the arrays provided as arguments but instead returns a\n\"one level deep\" copy that contains copies of the same elements combined from the original arrays.\nElements of the original arrays are copied into the new array as follows:\nObject references (and not the actual object): <code>concat</code> copies object references into the new\narray. Both the original and new array refer to the same object. That is, if a referenced object is\nmodified, the changes are visible to both the new and original arrays.\nStrings and numbers (not <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> and <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> objects): <code>concat</code> copies the values of\nstrings and numbers into the new array.</p>\n\n<p>Any operation on the new array will have no effect on the original arrays, and vice versa.</p>\n\n<h3>Concatenating two arrays</h3>\n\n<p>The following code concatenates two arrays:</p>\n\n<pre><code>var alpha = [\"a\", \"b\", \"c\"];\nvar numeric = [1, 2, 3];\n\n// creates array [\"a\", \"b\", \"c\", 1, 2, 3]; alpha and numeric are unchanged\nvar alphaNumeric = alpha.concat(numeric);\n</code></pre>\n\n<h3>Concatenating three arrays</h3>\n\n<p>The following code concatenates three arrays:</p>\n\n<pre><code>var num1 = [1, 2, 3];\nvar num2 = [4, 5, 6];\nvar num3 = [7, 8, 9];\n\n// creates array [1, 2, 3, 4, 5, 6, 7, 8, 9]; num1, num2, num3 are unchanged\nvar nums = num1.concat(num2, num3);\n</code></pre>\n\n<h3>Concatenating values to an array</h3>\n\n<p>The following code concatenates three values to an array:</p>\n\n<pre><code>var alpha = ['a', 'b', 'c'];\n\n// creates array [\"a\", \"b\", \"c\", 1, 2, 3], leaving alpha unchanged\nvar alphaNumeric = alpha.concat(1, [2, 3]);\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>values</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>...<div class='sub-desc'><p>Arrays and/or values to concatenate to the resulting array.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>New array.</p>\n</div></li></ul></div></div></div><div id='method-every' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-every' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-every' class='name expandable'>every</a>( <span class='pre'>callback, [thisObject]</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Tests whether all elements in the array pass the test implemented\nby the provided function. ...</div><div class='long'><p>Tests whether all elements in the array pass the test implemented\nby the provided function.</p>\n\n<p><code>every</code> executes the provided <code>callback</code> function once for each element\npresent in the array until it finds one where <code>callback</code> returns a\nfalse value. If such an element is found, the <code>every</code> method\nimmediately returns false. Otherwise, if <code>callback</code> returned a true\nvalue for all elements, <code>every</code> will return true. <code>callback</code> is invoked\nonly for indexes of the array which have assigned values; it is not\ninvoked for indexes which have been deleted or which have never\nbeen assigned values.</p>\n\n<p>If a <code>thisObject</code> parameter is provided to <code>every</code>, it will be used as\nthe <code>this</code> for each invocation of the callback. If it is not\nprovided, or is <code>null</code>, the global object associated with callback is\nused instead.</p>\n\n<p><code>every</code> does not mutate the array on which it is called.</p>\n\n<p>The range of elements processed by <code>every</code> is set before the first\ninvocation of callback. Elements which are appended to the array\nafter the call to every begins will not be visited by <code>callback</code>. If\nexisting elements of the array are changed, their value as passed\nto <code>callback</code> will be the value at the time <code>every</code> visits them;\nelements that are deleted are not visited.</p>\n\n<p><code>every</code> acts like the \"for all\" quantifier in mathematics. In\nparticular, for an empty array, it returns true. (It is vacuously\ntrue that all elements of the empty set satisfy any given\ncondition.)</p>\n\n<p>The following example tests whether all elements in the array are\nbigger than 10.</p>\n\n<pre><code>function isBigEnough(element, index, array) {\n  return (element &gt;= 10);\n}\nvar passed = [12, 5, 8, 130, 44].every(isBigEnough);\n// passed is false\npassed = [12, 54, 18, 130, 44].every(isBigEnough);\n// passed is true\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Function to test for each element.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : Mixed<div class='sub-desc'><p>The element value.</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The element index.</p>\n</div></li><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array being traversed.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>Should return true when element passes the test.</p>\n</div></li></ul></div></li><li><span class='pre'>thisObject</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Object to use as <code>this</code> when executing <code>callback</code>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True when all elements pass the test.</p>\n</div></li></ul></div></div></div><div id='method-filter' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-filter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-filter' class='name expandable'>filter</a>( <span class='pre'>callback, [thisObject]</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Creates a new array with all elements that pass the test\nimplemented by the provided function. ...</div><div class='long'><p>Creates a new array with all elements that pass the test\nimplemented by the provided function.</p>\n\n<p><code>filter</code> calls a provided <code>callback</code> function once for each element in\nan array, and constructs a new array of all the values for which\n<code>callback</code> returns a true value. <code>callback</code> is invoked only for indexes\nof the array which have assigned values; it is not invoked for\nindexes which have been deleted or which have never been assigned\nvalues. Array elements which do not pass the <code>callback</code> test are\nsimply skipped, and are not included in the new array.</p>\n\n<p>If a <code>thisObject</code> parameter is provided to <code>filter</code>, it will be\nused as the <code>this</code> for each invocation of the <code>callback</code>. If it is not\nprovided, or is <code>null</code>, the global object associated with callback is\nused instead.</p>\n\n<p><code>filter</code> does not mutate the array on which it is called.</p>\n\n<p>The range of elements processed by <code>filter</code> is set before the first\ninvocation of <code>callback</code>. Elements which are appended to the array\nafter the call to <code>filter</code> begins will not be visited by <code>callback</code>. If\nexisting elements of the array are changed, or deleted, their value\nas passed to <code>callback</code> will be the value at the time <code>filter</code> visits\nthem; elements that are deleted are not visited.</p>\n\n<p>The following example uses filter to create a filtered array that\nhas all elements with values less than 10 removed.</p>\n\n<pre><code>function isBigEnough(element, index, array) {\n  return (element &gt;= 10);\n}\nvar filtered = [12, 5, 8, 130, 44].filter(isBigEnough);\n// filtered is [12, 130, 44]\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Function to test for each element.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : Mixed<div class='sub-desc'><p>The element value.</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The element index.</p>\n</div></li><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array being traversed.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>Should return true when element passes the test.</p>\n</div></li></ul></div></li><li><span class='pre'>thisObject</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Object to use as <code>this</code> when executing <code>callback</code>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>Array of elements that passed the test.</p>\n</div></li></ul></div></div></div><div id='method-forEach' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-forEach' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-forEach' class='name expandable'>forEach</a>( <span class='pre'>callback, [thisArg]</span> )</div><div class='description'><div class='short'>Executes a provided function once per array element. ...</div><div class='long'><p>Executes a provided function once per array element.</p>\n\n<p><code>forEach</code> executes the provided function (<code>callback</code>) once for each element present in the array. <code>callback</code>\nis invoked only for indexes of the array which have assigned values; it is not invoked for indexes which\nhave been deleted or which have never been assigned values.</p>\n\n<p>If a <code>thisArg</code> parameter is provided to <code>forEach</code>, it will be used as the <code>this</code> value for each <code>callback</code>\ninvocation as if <code>callback.call(thisArg, element, index, array)</code> was called. If <code>thisArg</code> is <code>undefined</code> or\n<code>null</code>, the <code>this</code> value within the function depends on whether the function is in strict mode or not\n(passed value if in strict mode, global object if in non-strict mode).</p>\n\n<p>The <code>range</code> of elements processed by <code>forEach</code> is set before the first invocation of <code>callback</code>. Elements\nwhich are appended to the array after the call to <code>forEach</code> begins will not be visited by <code>callback</code>. If\nexisting elements of the array are changed, or deleted, their value as passed to callback will be the\nvalue at the time <code>forEach</code> visits them; elements that are deleted are not visited.</p>\n\n<p>The following code logs a line for each element in an array:</p>\n\n<pre><code>function logArrayElements(element, index, array) {\n    console.log(\"a[\" + index + \"] = \" + element);\n}\n[2, 5, 9].forEach(logArrayElements);\n// logs:\n// a[0] = 2\n// a[1] = 5\n// a[2] = 9\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Function to execute for each element.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : Mixed<div class='sub-desc'><p>The element value.</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The element index.</p>\n</div></li><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array being traversed.</p>\n</div></li></ul></div></li><li><span class='pre'>thisArg</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Object to use as <code>this</code> when executing <code>callback</code>.</p>\n</div></li></ul></div></div></div><div id='method-indexOf' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-indexOf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-indexOf' class='name expandable'>indexOf</a>( <span class='pre'>searchElement, [fromIndex]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the first index at which a given element can be found in the array, or -1 if it is not present. ...</div><div class='long'><p>Returns the first index at which a given element can be found in the array, or -1 if it is not present.</p>\n\n<p><code>indexOf</code> compares <code>searchElement</code> to elements of the Array using strict equality (the same method used\nby the <code>===</code>, or triple-equals, operator).</p>\n\n<pre><code>var array = [2, 5, 9];\nvar index = array.indexOf(2);\n// index is 0\nindex = array.indexOf(7);\n// index is -1\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>searchElement</span> : Mixed<div class='sub-desc'><p>Element to locate in the array.</p>\n</div></li><li><span class='pre'>fromIndex</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>The index at which to begin the search. Defaults to 0, i.e. the whole array\nwill be searched. If the index is greater than or equal to the length of the array, -1 is returned, i.e.\nthe array will not be searched. If negative, it is taken as the offset from the end of the array. Note\nthat even when the index is negative, the array is still searched from front to back. If the calculated\nindex is less than 0, the whole array will be searched.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The index of element found or -1.</p>\n</div></li></ul></div></div></div><div id='method-join' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-join' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-join' class='name expandable'>join</a>( <span class='pre'>separator</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Joins all elements of an array into a string. ...</div><div class='long'><p>Joins all elements of an array into a string.</p>\n\n<p>The string conversions of all array elements are joined into one string.</p>\n\n<p>The following example creates an array, <code>a</code>, with three elements, then joins the array three times:\nusing the default separator, then a comma and a space, and then a plus.</p>\n\n<pre><code>var a = new Array(\"Wind\",\"Rain\",\"Fire\");\nvar myVar1 = a.join();      // assigns \"Wind,Rain,Fire\" to myVar1\nvar myVar2 = a.join(\", \");  // assigns \"Wind, Rain, Fire\" to myVar2\nvar myVar3 = a.join(\" + \"); // assigns \"Wind + Rain + Fire\" to myVar3\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>separator</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>Specifies a string to separate each element of the array. The separator\nis converted to a string if necessary. If omitted, the array elements are separated with a comma.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>A string of the array elements.</p>\n</div></li></ul></div></div></div><div id='method-lastIndexOf' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-lastIndexOf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-lastIndexOf' class='name expandable'>lastIndexOf</a>( <span class='pre'>searchElement, [fromIndex]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the last index at which a given element can be found in the array, or -1 if it is not present. ...</div><div class='long'><p>Returns the last index at which a given element can be found in the array, or -1 if it is not present.\nThe array is searched backwards, starting at <code>fromIndex</code>.</p>\n\n<p><code>lastIndexOf</code> compares <code>searchElement</code> to elements of the Array using strict equality (the same method\nused by the <code>===</code>, or triple-equals, operator).</p>\n\n<pre><code>var array = [2, 5, 9, 2];\nvar index = array.lastIndexOf(2);\n// index is 3\nindex = array.lastIndexOf(7);\n// index is -1\nindex = array.lastIndexOf(2, 3);\n// index is 3\nindex = array.lastIndexOf(2, 2);\n// index is 0\nindex = array.lastIndexOf(2, -2);\n// index is 0\nindex = array.lastIndexOf(2, -1);\n// index is 3\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>searchElement</span> : Mixed<div class='sub-desc'><p>Element to locate in the array.</p>\n</div></li><li><span class='pre'>fromIndex</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>The index at which to start searching backwards. Defaults to the array's\nlength, i.e. the whole array will be searched. If the index is greater than or equal to the length of\nthe array, the whole array will be searched. If negative, it is taken as the offset from the end of the\narray. Note that even when the index is negative, the array is still searched from back to front. If\nthe calculated index is less than 0, -1 is returned, i.e. the array will not be searched.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The index of element found or -1.</p>\n</div></li></ul></div></div></div><div id='method-map' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-map' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-map' class='name expandable'>map</a>( <span class='pre'>callback, [thisObject]</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Creates a new array with the results of calling a provided function\non every element in this array. ...</div><div class='long'><p>Creates a new array with the results of calling a provided function\non every element in this array.</p>\n\n<p><code>map</code> calls a provided <code>callback</code> function once for each element in\nan array, in order, and constructs a new array from the\nresults. <code>callback</code> is invoked only for indexes of the array which\nhave assigned values; it is not invoked for indexes which have been\ndeleted or which have never been assigned values.</p>\n\n<p>If a <code>thisArg</code> parameter is provided to map, it will be used as the\n<code>this</code> for each invocation of the <code>callback</code>. If it is not provided, or\nis <code>null</code>, the global object associated with callback is used\ninstead.</p>\n\n<p><code>map</code> does not mutate the array on which it is called.</p>\n\n<p>The range of elements processed by <code>map</code> is set before the first\ninvocation of <code>callback</code>. Elements which are appended to the array\nafter the call to <code>map</code> begins will not be visited by <code>callback</code>. If\nexisting elements of the array are changed, or deleted, their value\nas passed to <code>callback</code> will be the value at the time <code>map</code> visits\nthem; elements that are deleted are not visited.</p>\n\n<p>The following code creates an array of \"plural\" forms of nouns from\nan array of their singular forms.</p>\n\n<pre><code>function fuzzyPlural(single) {\n  var result = single.replace(/o/g, 'e');\n  if( single === 'kangaroo'){\n    result += 'se';\n  }\n  return result;\n}\n\nvar words = [\"foot\", \"goose\", \"moose\", \"kangaroo\"];\nconsole.log(words.map(fuzzyPlural));\n\n// [\"feet\", \"geese\", \"meese\", \"kangareese\"]\n</code></pre>\n\n<p>The following code takes an array of numbers and creates a new\narray containing the square roots of the numbers in the first\narray.</p>\n\n<pre><code>var numbers = [1, 4, 9];\nvar roots = numbers.map(Math.sqrt);\n// roots is now [1, 2, 3], numbers is still [1, 4, 9]\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Function that produces an element of the new Array\nfrom an element of the current one.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : Mixed<div class='sub-desc'><p>The element value.</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The element index.</p>\n</div></li><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array being traversed.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>Should return true when element passes the test.</p>\n</div></li></ul></div></li><li><span class='pre'>thisObject</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Object to use as <code>this</code> when executing <code>callback</code>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>Array of the return values of <code>callback</code> function.</p>\n</div></li></ul></div></div></div><div id='method-pop' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-pop' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-pop' class='name expandable'>pop</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>The pop method removes the last element from an array and returns that value to the caller. ...</div><div class='long'><p>The pop method removes the last element from an array and returns that value to the caller.</p>\n\n<p><code>pop</code> is intentionally generic; this method can be called or applied to objects resembling\narrays. Objects which do not contain a length property reflecting the last in a series of\nconsecutive, zero-based numerical properties may not behave in any meaningful manner.</p>\n\n<pre><code>var myFish = [\"angel\", \"clown\", \"mandarin\", \"surgeon\"];\nvar popped = myFish.pop();\nalert(popped); // Alerts 'surgeon'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The last element in the array</p>\n</div></li></ul></div></div></div><div id='method-push' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-push' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-push' class='name expandable'>push</a>( <span class='pre'>elements</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Adds one or more elements to the end of an array and returns the new length of the array. ...</div><div class='long'><p>Adds one or more elements to the end of an array and returns the new length of the array.</p>\n\n<p><code>push</code> is intentionally generic. This method can be called or applied to objects resembling\narrays. The push method relies on a length property to determine where to start inserting\nthe given values. If the length property cannot be converted into a number, the index used\nis 0. This includes the possibility of length being nonexistent, in which case length will\nalso be created.</p>\n\n<p>The only native, array-like objects are strings, although they are not suitable in\napplications of this method, as strings are immutable.</p>\n\n<h3>Adding elements to an array</h3>\n\n<p>The following code creates the sports array containing two elements, then appends two elements\nto it. After the code executes, sports contains 4 elements: \"soccer\", \"baseball\", \"football\"\nand \"swimming\".</p>\n\n<pre><code>var sports = [\"soccer\", \"baseball\"];\nsports.push(\"football\", \"swimming\");\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>elements</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>...<div class='sub-desc'><p>The elements to add to the end of the array.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The new length property of the object upon which the method was called.</p>\n</div></li></ul></div></div></div><div id='method-reduce' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-reduce' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-reduce' class='name expandable'>reduce</a>( <span class='pre'>callback, [initialValue]</span> ) : Mixed</div><div class='description'><div class='short'>Applies a function against an accumulator and each value of the\narray (from left-to-right) as to reduce it to a singl...</div><div class='long'><p>Applies a function against an accumulator and each value of the\narray (from left-to-right) as to reduce it to a single value.</p>\n\n<p><code>reduce</code> executes the <code>callback</code> function once for each element\npresent in the array, excluding holes in the array.</p>\n\n<p>The first time the <code>callback</code> is called, <code>previousValue</code> and\n<code>currentValue</code> can be one of two values. If <code>initialValue</code> is\nprovided in the call to <code>reduce</code>, then <code>previousValue</code> will be equal to\n<code>initialValue</code> and <code>currentValue</code> will be equal to the first value in\nthe array. If no <code>initialValue</code> was provided, then <code>previousValue</code> will\nbe equal to the first value in the array and <code>currentValue</code> will be\nequal to the second.</p>\n\n<p>Suppose the following use of reduce occurred:</p>\n\n<pre><code>[0,1,2,3,4].reduce(function(previousValue, currentValue, index, array){\n  return previousValue + currentValue;\n});\n</code></pre>\n\n<p>The callback would be invoked four times, with the arguments and\nreturn values in each call being as follows:</p>\n\n<table>\n<thead>\n<tr>\n<th></th>\n<th align=\"left\">             </th>\n<th align=\"left\"> previousValue </th>\n<th align=\"left\"> currentValue </th>\n<th align=\"left\"> index </th>\n<th align=\"left\"> array       </th>\n<th align=\"left\"> return value</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td></td>\n<td align=\"left\"> first call  </td>\n<td align=\"left\"> 0             </td>\n<td align=\"left\"> 1            </td>\n<td align=\"left\"> 1     </td>\n<td align=\"left\"> [0,1,2,3,4] </td>\n<td align=\"left\"> 1</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> second call </td>\n<td align=\"left\"> 1             </td>\n<td align=\"left\"> 2            </td>\n<td align=\"left\"> 2     </td>\n<td align=\"left\"> [0,1,2,3,4] </td>\n<td align=\"left\"> 3</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> third call  </td>\n<td align=\"left\"> 3             </td>\n<td align=\"left\"> 3            </td>\n<td align=\"left\"> 3     </td>\n<td align=\"left\"> [0,1,2,3,4] </td>\n<td align=\"left\"> 6</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> fourth call </td>\n<td align=\"left\"> 6             </td>\n<td align=\"left\"> 4            </td>\n<td align=\"left\"> 4     </td>\n<td align=\"left\"> [0,1,2,3,4] </td>\n<td align=\"left\"> 10</td>\n</tr>\n</tbody>\n</table>\n\n\n<p>The value returned by <code>reduce</code> would be that of the last callback\ninvocation (10).</p>\n\n<p>If you were to provide an initial value as the second argument to\nreduce, the result would look like this:</p>\n\n<pre><code>[0,1,2,3,4].reduce(function(previousValue, currentValue, index, array){\n  return previousValue + currentValue;\n}, 10);\n</code></pre>\n\n<table>\n<thead>\n<tr>\n<th></th>\n<th align=\"left\">             </th>\n<th align=\"left\"> previousValue </th>\n<th align=\"left\"> currentValue </th>\n<th align=\"left\"> index </th>\n<th align=\"left\"> array       </th>\n<th align=\"left\"> return value</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td></td>\n<td align=\"left\"> first call  </td>\n<td align=\"left\"> 10            </td>\n<td align=\"left\"> 0            </td>\n<td align=\"left\"> 0     </td>\n<td align=\"left\"> [0,1,2,3,4] </td>\n<td align=\"left\"> 10</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> second call </td>\n<td align=\"left\"> 10            </td>\n<td align=\"left\"> 1            </td>\n<td align=\"left\"> 1     </td>\n<td align=\"left\"> [0,1,2,3,4] </td>\n<td align=\"left\"> 11</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> third call  </td>\n<td align=\"left\"> 11            </td>\n<td align=\"left\"> 2            </td>\n<td align=\"left\"> 2     </td>\n<td align=\"left\"> [0,1,2,3,4] </td>\n<td align=\"left\"> 13</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> fourth call </td>\n<td align=\"left\"> 13            </td>\n<td align=\"left\"> 3            </td>\n<td align=\"left\"> 3     </td>\n<td align=\"left\"> [0,1,2,3,4] </td>\n<td align=\"left\"> 16</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> fifth call  </td>\n<td align=\"left\"> 16            </td>\n<td align=\"left\"> 4            </td>\n<td align=\"left\"> 4     </td>\n<td align=\"left\"> [0,1,2,3,4] </td>\n<td align=\"left\"> 20</td>\n</tr>\n</tbody>\n</table>\n\n\n<p>The value returned by <code>reduce</code> this time would be, of course, 20.</p>\n\n<p>Example: Sum up all values within an array:</p>\n\n<pre><code>var total = [0, 1, 2, 3].reduce(function(a, b) {\n    return a + b;\n});\n// total == 6\n</code></pre>\n\n<p>Example: Flatten an array of arrays:</p>\n\n<pre><code>var flattened = [[0, 1], [2, 3], [4, 5]].reduce(function(a, b) {\n    return a.concat(b);\n});\n// flattened is [0, 1, 2, 3, 4, 5]\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Function to execute on each value in the array.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>previousValue</span> : Mixed<div class='sub-desc'><p>The value previously returned in the last\ninvocation of the <code>callback</code>, or <code>initialValue</code>, if supplied.</p>\n</div></li><li><span class='pre'>currentValue</span> : Mixed<div class='sub-desc'><p>The current element being processed in the array.</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The index of the current element being processed in the array.</p>\n</div></li><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array <code>reduce</code> was called upon.</p>\n</div></li></ul></div></li><li><span class='pre'>initialValue</span> : Mixed (optional)<div class='sub-desc'><p>Object to use as the first argument to the first call\nof the <code>callback</code>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>Mixed</span><div class='sub-desc'><p>The value returned by final invocation of the <code>callback</code>.</p>\n</div></li></ul></div></div></div><div id='method-reduceRight' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-reduceRight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-reduceRight' class='name expandable'>reduceRight</a>( <span class='pre'>callback, [initialValue]</span> ) : Mixed</div><div class='description'><div class='short'>Applies a function simultaneously against two values of the array\n(from right-to-left) as to reduce it to a single va...</div><div class='long'><p>Applies a function simultaneously against two values of the array\n(from right-to-left) as to reduce it to a single value.</p>\n\n<p><code>reduceRight</code> executes the <code>callback</code> function once for each\nelement present in the array, excluding holes in the array.</p>\n\n<p>The first time the <code>callback</code> is called, <code>previousValue</code> and\n<code>currentValue</code> can be one of two values. If <code>initialValue</code> is\nprovided in the call to <code>reduceRight</code>, then <code>previousValue</code> will be equal to\n<code>initialValue</code> and <code>currentValue</code> will be equal to the last value in\nthe array. If no <code>initialValue</code> was provided, then <code>previousValue</code> will\nbe equal to the last value in the array and <code>currentValue</code> will be\nequal to the second-to-last value.</p>\n\n<p>Some example run-throughs of the function would look like this:</p>\n\n<pre><code>[0, 1, 2, 3, 4].reduceRight(function(previousValue, currentValue, index, array) {\n    return previousValue + currentValue;\n});\n\n// First call\npreviousValue = 4, currentValue = 3, index = 3\n\n// Second call\npreviousValue = 7, currentValue = 2, index = 2\n\n// Third call\npreviousValue = 9, currentValue = 1, index = 1\n\n// Fourth call\npreviousValue = 10, currentValue = 0, index = 0\n\n// array is always the object [0,1,2,3,4] upon which reduceRight was called\n\n// Return Value: 10\n</code></pre>\n\n<p>And if you were to provide an initialValue, the result would look like this:</p>\n\n<pre><code>[0, 1, 2, 3, 4].reduceRight(function(previousValue, currentValue, index, array) {\n    return previousValue + currentValue;\n}, 10);\n\n// First call\npreviousValue = 10, currentValue = 4, index = 4\n\n// Second call\npreviousValue = 14, currentValue = 3, index = 3\n\n// Third call\npreviousValue = 17, currentValue = 2, index = 2\n\n// Fourth call\npreviousValue = 19, currentValue = 1, index = 1\n\n// Fifth call\npreviousValue = 20, currentValue = 0, index = 0\n\n// array is always the object [0,1,2,3,4] upon which reduceRight was called\n\n// Return Value: 20\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Function to execute on each value in the array.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>previousValue</span> : Mixed<div class='sub-desc'><p>The value previously returned in the last\ninvocation of the <code>callback</code>, or <code>initialValue</code>, if supplied.</p>\n</div></li><li><span class='pre'>currentValue</span> : Mixed<div class='sub-desc'><p>The current element being processed in the array.</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The index of the current element being processed in the array.</p>\n</div></li><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array <code>reduceRight</code> was called upon.</p>\n</div></li></ul></div></li><li><span class='pre'>initialValue</span> : Mixed (optional)<div class='sub-desc'><p>Object to use as the first argument to the first call\nof the <code>callback</code>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>Mixed</span><div class='sub-desc'><p>The value returned by final invocation of the <code>callback</code>.</p>\n</div></li></ul></div></div></div><div id='method-reverse' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-reverse' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-reverse' class='name expandable'>reverse</a>( <span class='pre'></span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Reverses the order of the elements of an array -- the first becomes the last, and the\nlast becomes the first. ...</div><div class='long'><p>Reverses the order of the elements of an array -- the first becomes the last, and the\nlast becomes the first.</p>\n\n<p>The reverse method transposes the elements of the calling array object in place, mutating the\narray, and returning a reference to the array.</p>\n\n<p>The following example creates an array myArray, containing three elements, then reverses the array.</p>\n\n<pre><code>var myArray = [\"one\", \"two\", \"three\"];\nmyArray.reverse();\n</code></pre>\n\n<p>This code changes myArray so that:</p>\n\n<ul>\n<li>myArray[0] is \"three\"</li>\n<li>myArray[1] is \"two\"</li>\n<li>myArray[2] is \"one\"</li>\n</ul>\n\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>A reference to the array</p>\n</div></li></ul></div></div></div><div id='method-shift' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-shift' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-shift' class='name expandable'>shift</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Removes the first element from an array and returns that element. ...</div><div class='long'><p>Removes the first element from an array and returns that element.</p>\n\n<p>The <code>shift</code> method removes the element at the zeroeth index and shifts the values at consecutive\nindexes down, then returns the removed value.</p>\n\n<p><code>shift</code> is intentionally generic; this method can be called or applied to objects resembling\narrays. Objects which do not contain a <code>length</code> property reflecting the last in a series of\nconsecutive, zero-based numerical properties may not behave in any meaningful manner.</p>\n\n<p>The following code displays the <code>myFish</code> array before and after removing its first element. It also\ndisplays the removed element:</p>\n\n<pre><code>// assumes a println function is defined\nvar myFish = [\"angel\", \"clown\", \"mandarin\", \"surgeon\"];\nprintln(\"myFish before: \" + myFish);\nvar shifted = myFish.shift();\nprintln(\"myFish after: \" + myFish);\nprintln(\"Removed this element: \" + shifted);\n</code></pre>\n\n<p>This example displays the following:</p>\n\n<pre><code>myFish before: angel,clown,mandarin,surgeon\nmyFish after: clown,mandarin,surgeon\nRemoved this element: angel\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The first element of the array prior to shifting.</p>\n</div></li></ul></div></div></div><div id='method-slice' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-slice' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-slice' class='name expandable'>slice</a>( <span class='pre'>begin, end</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Extracts a section of an array and returns a new array. ...</div><div class='long'><p>Extracts a section of an array and returns a new array.</p>\n\n<p><code>slice</code> does not alter the original array, but returns a new \"one level deep\" copy that contains\ncopies of the elements sliced from the original array. Elements of the original array are copied\ninto the new array as follows:\n*   For object references (and not the actual object), <code>slice</code> copies object references into the\nnew array. Both the original and new array refer to the same object. If a referenced object\nchanges, the changes are visible to both the new and original arrays.\n*   For strings and numbers (not <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> and <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> objects), <code>slice</code> copies strings\nand numbers into the new array. Changes to the string or number in one array does not affect the\nother array.</p>\n\n<p>If a new element is added to either array, the other array is not affected.</p>\n\n<h3>Using slice</h3>\n\n<p>In the following example, <code>slice</code> creates a new array, <code>newCar</code>, from <code>myCar</code>. Both include a\nreference to the object <code>myHonda</code>. When the color of <code>myHonda</code> is changed to purple, both arrays\nreflect the change.</p>\n\n<pre><code>// Using slice, create newCar from myCar.\nvar myHonda = { color: \"red\", wheels: 4, engine: { cylinders: 4, size: 2.2 } };\nvar myCar = [myHonda, 2, \"cherry condition\", \"purchased 1997\"];\nvar newCar = myCar.slice(0, 2);\n\n// Print the values of myCar, newCar, and the color of myHonda\n//  referenced from both arrays.\nprint(\"myCar = \" + myCar.toSource());\nprint(\"newCar = \" + newCar.toSource());\nprint(\"myCar[0].color = \" + myCar[0].color);\nprint(\"newCar[0].color = \" + newCar[0].color);\n\n// Change the color of myHonda.\nmyHonda.color = \"purple\";\nprint(\"The new color of my Honda is \" + myHonda.color);\n\n// Print the color of myHonda referenced from both arrays.\nprint(\"myCar[0].color = \" + myCar[0].color);\nprint(\"newCar[0].color = \" + newCar[0].color);\n</code></pre>\n\n<p>This script writes:</p>\n\n<pre><code>myCar = [{color:\"red\", wheels:4, engine:{cylinders:4, size:2.2}}, 2, \"cherry condition\",\n\"purchased 1997\"]\nnewCar = [{color:\"red\", wheels:4, engine:{cylinders:4, size:2.2}}, 2]\nmyCar[0].color = red\nnewCar[0].color = red\nThe new color of my Honda is purple\nmyCar[0].color = purple\nnewCar[0].color = purple\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>begin</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Zero-based index at which to begin extraction.\nAs a negative index, <code>start</code> indicates an offset from the end of the sequence. <code>slice(-2)</code> extracts\nthe second-to-last element and the last element in the sequence</p>\n</div></li><li><span class='pre'>end</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Zero-based index at which to end extraction. <code>slice</code> extracts up to but not\nincluding <code>end</code>.\n<code>slice(1,4)</code> extracts the second element through the fourth element (elements indexed 1, 2, and 3).\nAs a negative index, end indicates an offset from the end of the sequence. <code>slice(2,-1)</code> extracts\nthe third element through the second-to-last element in the sequence.\nIf <code>end</code> is omitted, <code>slice</code> extracts to the end of the sequence.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>Array from the new start position up to (but not including) the specified end position.</p>\n</div></li></ul></div></div></div><div id='method-some' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-some' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-some' class='name expandable'>some</a>( <span class='pre'>callback, [thisObject]</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Tests whether some element in the array passes the test implemented\nby the provided function. ...</div><div class='long'><p>Tests whether some element in the array passes the test implemented\nby the provided function.</p>\n\n<p><code>some</code> executes the <code>callback</code> function once for each element\npresent in the array until it finds one where <code>callback</code> returns a\ntrue value. If such an element is found, some immediately returns\ntrue. Otherwise, some returns false. <code>callback</code> is invoked only for\nindexes of the array which have assigned values; it is not invoked\nfor indexes which have been deleted or which have never been\nassigned values.</p>\n\n<p>If a <code>thisObject</code> parameter is provided to some, it will be used as\nthe <code>this</code> for each invocation of the <code>callback</code>. If it is not\nprovided, or is <code>null</code>, the global object associated with callback is\nused instead.</p>\n\n<p><code>some</code> does not mutate the array on which it is called.</p>\n\n<p>The range of elements processed by <code>some</code> is set before the first\ninvocation of callback. Elements that are appended to the array\nafter the call to some begins will not be visited by <code>callback</code>. If\nan existing, unvisited element of the array is changed by <code>callback</code>,\nits value passed to the visiting callback will be the value at the\ntime that <code>some</code> visits that element's index; elements that are\ndeleted are not visited.</p>\n\n<p>The following example tests whether some element in the array is\nbigger than 10.</p>\n\n<pre><code>function isBigEnough(element, index, array) {\n  return (element &gt;= 10);\n}\nvar passed = [2, 5, 8, 1, 4].some(isBigEnough);\n// passed is false\npassed = [12, 5, 8, 1, 4].some(isBigEnough);\n// passed is true\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Function to test for each element.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : Mixed<div class='sub-desc'><p>The element value.</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The element index.</p>\n</div></li><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The array being traversed.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>Should return true when element passes the test.</p>\n</div></li></ul></div></li><li><span class='pre'>thisObject</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Object to use as <code>this</code> when executing <code>callback</code>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True when at least one element passes the test.</p>\n</div></li></ul></div></div></div><div id='method-sort' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-sort' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-sort' class='name expandable'>sort</a>( <span class='pre'>compareFunction</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Sorts the elements of an array. ...</div><div class='long'><p>Sorts the elements of an array.</p>\n\n<p>If <code>compareFunction</code> is not supplied, elements are sorted by converting them to strings and\ncomparing strings in lexicographic (\"dictionary\" or \"telephone book,\" not numerical) order. For\nexample, \"80\" comes before \"9\" in lexicographic order, but in a numeric sort 9 comes before 80.</p>\n\n<p>If <code>compareFunction</code> is supplied, the array elements are sorted according to the return value of\nthe compare function. If a and b are two elements being compared, then:\nIf <code>compareFunction(a, b)</code> is less than 0, sort <code>a</code> to a lower index than <code>b</code>.\nIf <code>compareFunction(a, b)</code> returns 0, leave <code>a</code> and <code>b</code> unchanged with respect to each other, but\nsorted with respect to all different elements. Note: the ECMAscript standard does not guarantee\nthis behaviour, and thus not all browsers respect this.\nIf <code>compareFunction(a, b)</code> is greater than 0, sort <code>b</code> to a lower index than <code>a</code>.\n<code>compareFunction(a, b)</code> must always returns the same value when given a specific pair of elements a\nand b as its two arguments. If inconsistent results are returned then the sort order is undefined</p>\n\n<p>So, the compare function has the following form:</p>\n\n<pre><code>function compare(a, b)\n{\n    if (a is less than b by some ordering criterion)\n        return -1;\n    if (a is greater than b by the ordering criterion)\n       return 1;\n    // a must be equal to b\n    return 0;\n}\n</code></pre>\n\n<p>To compare numbers instead of strings, the compare function can simply subtract <code>b</code> from <code>a</code>:</p>\n\n<pre><code>function compareNumbers(a, b)\n{\nreturn a - b;\n}\n</code></pre>\n\n<p>The sort() method can be conveniently used with closures:</p>\n\n<pre><code>var numbers = [4, 2, 5, 1, 3];\nnumbers.sort(function(a, b) {\n    return a - b;\n});\nprint(numbers);\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>compareFunction</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Specifies a function that defines the sort order. If omitted, the\narray is sorted lexicographically (in dictionary order) according to the string conversion of each\nelement.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>A reference to the array</p>\n</div></li></ul></div></div></div><div id='method-splice' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-splice' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-splice' class='name expandable'>splice</a>( <span class='pre'>index, howMany, elements</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Adds and/or removes elements from an array. ...</div><div class='long'><p>Adds and/or removes elements from an array.</p>\n\n<p>If you specify a different number of elements to insert than the number you're removing, the array\nwill have a different length at the end of the call.</p>\n\n<pre><code>// assumes a print function is defined\nvar myFish = [\"angel\", \"clown\", \"mandarin\", \"surgeon\"];\nprint(\"myFish: \" + myFish);\n\nvar removed = myFish.splice(2, 0, \"drum\");\nprint(\"After adding 1: \" + myFish);\nprint(\"removed is: \" + removed);\n\nremoved = myFish.splice(3, 1);\nprint(\"After removing 1: \" + myFish);\nprint(\"removed is: \" + removed);\n\nremoved = myFish.splice(2, 1, \"trumpet\");\nprint(\"After replacing 1: \" + myFish);\nprint(\"removed is: \" + removed);\n\nremoved = myFish.splice(0, 2, \"parrot\", \"anemone\", \"blue\");\nprint(\"After replacing 2: \" + myFish);\nprint(\"removed is: \" + removed);\n</code></pre>\n\n<p>This script displays:</p>\n\n<pre><code>myFish: angel,clown,mandarin,surgeon\nAfter adding 1: angel,clown,drum,mandarin,surgeon\nremoved is:\nAfter removing 1: angel,clown,drum,surgeon\nremoved is: mandarin\nAfter replacing 1: angel,clown,trumpet,surgeon\nremoved is: drum\nAfter replacing 2: parrot,anemone,blue,trumpet,surgeon\nremoved is: angel,clown\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Index at which to start changing the array. If negative, will begin that\nmany elements from the end.</p>\n</div></li><li><span class='pre'>howMany</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer indicating the number of old array elements to remove. If\n<code>howMany</code> is 0, no elements are removed. In this case, you should specify at least one new element.\nIf no <code>howMany</code> parameter is specified all elements after index are removed.</p>\n</div></li><li><span class='pre'>elements</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>...<div class='sub-desc'><p>The elements to add to the array. If you don't specify any\nelements, <code>splice</code> simply removes elements from the array.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>An array containing the removed elements. If only one element is removed, an array\nof one element is returned..</p>\n</div></li></ul></div></div></div><div id='method-toString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-toString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-toString' class='name expandable'>toString</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns a string representing the array and its elements. ...</div><div class='long'><p>Returns a string representing the array and its elements. Overrides the <code>Object.prototype.toString</code>\nmethod.</p>\n\n<p>The <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> object overrides the <code>toString</code> method of <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>. For Array objects, the\n<code>toString</code> method joins the array and returns one string containing each array element separated by\ncommas. For example, the following code creates an array and uses <code>toString</code> to convert the array\nto a string.</p>\n\n<pre><code>var monthNames = new Array(\"Jan\",\"Feb\",\"Mar\",\"Apr\");\nmyVar = monthNames.toString(); // assigns \"Jan,Feb,Mar,Apr\" to myVar\n</code></pre>\n\n<p>JavaScript calls the <code>toString</code> method automatically when an array is to be represented as a text\nvalue or when an array is referred to in a string concatenation.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The array as a string.</p>\n</div></li></ul></div></div></div><div id='method-unshift' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-method-unshift' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-method-unshift' class='name expandable'>unshift</a>( <span class='pre'>elements</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Adds one or more elements to the front of an array and returns the new length of the array. ...</div><div class='long'><p>Adds one or more elements to the front of an array and returns the new length of the array.</p>\n\n<p>The <code>unshift</code> method inserts the given values to the beginning of an array-like object.</p>\n\n<p><code>unshift</code> is intentionally generic; this method can be called or applied to objects resembling\narrays. Objects which do not contain a <code>length</code> property reflecting the last in a series of\nconsecutive, zero-based numerical properties may not behave in any meaningful manner.</p>\n\n<p>The following code displays the myFish array before and after adding elements to it.</p>\n\n<pre><code>// assumes a println function exists\nmyFish = [\"angel\", \"clown\"];\nprintln(\"myFish before: \" + myFish);\nunshifted = myFish.unshift(\"drum\", \"lion\");\nprintln(\"myFish after: \" + myFish);\nprintln(\"New length: \" + unshifted);\n</code></pre>\n\n<p>This example displays the following:</p>\n\n<pre><code>myFish before: [\"angel\", \"clown\"]\nmyFish after: [\"drum\", \"lion\", \"angel\", \"clown\"]\nNew length: 4\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>elements</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>...<div class='sub-desc'><p>The elements to add to the front of the array.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The array's new length.</p>\n</div></li></ul></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-isArray' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Array'>Array</span><br/><a href='source/Array.html#Array-static-method-isArray' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Array-static-method-isArray' class='name expandable'>isArray</a>( <span class='pre'>obj</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Returns true if an object is an array, false if it is not. ...</div><div class='long'><p>Returns true if an object is an array, false if it is not.</p>\n\n<pre><code>// all following calls return true\n<a href=\"#!/api/Array-static-method-isArray\" rel=\"Array-static-method-isArray\" class=\"docClass\">Array.isArray</a>([]);\n<a href=\"#!/api/Array-static-method-isArray\" rel=\"Array-static-method-isArray\" class=\"docClass\">Array.isArray</a>([1]);\n<a href=\"#!/api/Array-static-method-isArray\" rel=\"Array-static-method-isArray\" class=\"docClass\">Array.isArray</a>( new Array() );\n<a href=\"#!/api/Array-static-method-isArray\" rel=\"Array-static-method-isArray\" class=\"docClass\">Array.isArray</a>( Array.prototype ); // Little known fact: Array.prototype itself is an array.\n\n// all following calls return false\n<a href=\"#!/api/Array-static-method-isArray\" rel=\"Array-static-method-isArray\" class=\"docClass\">Array.isArray</a>();\n<a href=\"#!/api/Array-static-method-isArray\" rel=\"Array-static-method-isArray\" class=\"docClass\">Array.isArray</a>({});\n<a href=\"#!/api/Array-static-method-isArray\" rel=\"Array-static-method-isArray\" class=\"docClass\">Array.isArray</a>(null);\n<a href=\"#!/api/Array-static-method-isArray\" rel=\"Array-static-method-isArray\" class=\"docClass\">Array.isArray</a>(undefined);\n<a href=\"#!/api/Array-static-method-isArray\" rel=\"Array-static-method-isArray\" class=\"docClass\">Array.isArray</a>(17);\n<a href=\"#!/api/Array-static-method-isArray\" rel=\"Array-static-method-isArray\" class=\"docClass\">Array.isArray</a>(\"Array\");\n<a href=\"#!/api/Array-static-method-isArray\" rel=\"Array-static-method-isArray\" class=\"docClass\">Array.isArray</a>(true);\n<a href=\"#!/api/Array-static-method-isArray\" rel=\"Array-static-method-isArray\" class=\"docClass\">Array.isArray</a>(false);\n<a href=\"#!/api/Array-static-method-isArray\" rel=\"Array-static-method-isArray\" class=\"docClass\">Array.isArray</a>({ __proto__ : Array.prototype });\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>obj</span> : Mixed<div class='sub-desc'><p>The object to be checked.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True when Array.</p>\n</div></li></ul></div></div></div></div></div></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Array","meta":{"static":true},"name":"isArray","id":"static-method-isArray"}],"event":[],"css_mixin":[]},"files":[{"href":"Array.html#Array","filename":"Array.js"}],"linenr":1,"members":{"property":[{"tagname":"property","owner":"Array","meta":{},"name":"length","id":"property-length"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Array","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"Array","meta":{},"name":"concat","id":"method-concat"},{"tagname":"method","owner":"Array","meta":{},"name":"every","id":"method-every"},{"tagname":"method","owner":"Array","meta":{},"name":"filter","id":"method-filter"},{"tagname":"method","owner":"Array","meta":{},"name":"forEach","id":"method-forEach"},{"tagname":"method","owner":"Array","meta":{},"name":"indexOf","id":"method-indexOf"},{"tagname":"method","owner":"Array","meta":{},"name":"join","id":"method-join"},{"tagname":"method","owner":"Array","meta":{},"name":"lastIndexOf","id":"method-lastIndexOf"},{"tagname":"method","owner":"Array","meta":{},"name":"map","id":"method-map"},{"tagname":"method","owner":"Array","meta":{},"name":"pop","id":"method-pop"},{"tagname":"method","owner":"Array","meta":{},"name":"push","id":"method-push"},{"tagname":"method","owner":"Array","meta":{},"name":"reduce","id":"method-reduce"},{"tagname":"method","owner":"Array","meta":{},"name":"reduceRight","id":"method-reduceRight"},{"tagname":"method","owner":"Array","meta":{},"name":"reverse","id":"method-reverse"},{"tagname":"method","owner":"Array","meta":{},"name":"shift","id":"method-shift"},{"tagname":"method","owner":"Array","meta":{},"name":"slice","id":"method-slice"},{"tagname":"method","owner":"Array","meta":{},"name":"some","id":"method-some"},{"tagname":"method","owner":"Array","meta":{},"name":"sort","id":"method-sort"},{"tagname":"method","owner":"Array","meta":{},"name":"splice","id":"method-splice"},{"tagname":"method","owner":"Array","meta":{},"name":"toString","id":"method-toString"},{"tagname":"method","owner":"Array","meta":{},"name":"unshift","id":"method-unshift"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Array","singleton":false,"override":null,"inheritdoc":null,"id":"class-Array","mixins":[],"mixedInto":[]});