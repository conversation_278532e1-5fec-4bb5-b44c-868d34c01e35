Ext.data.JsonP.Ext_chart_theme_Theme({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Mixed into</h4><div class='dependency'><a href='#!/api/Ext.chart.Chart' rel='Ext.chart.Chart' class='docClass'>Ext.chart.Chart</a></div><h4>Files</h4><div class='dependency'><a href='source/Theme.html#Ext-chart-theme-Theme' target='_blank'>Theme.js</a></div></pre><div class='doc-contents'><p>Provides chart theming.</p>\n\n<p>Used as mixins by <a href=\"#!/api/Ext.chart.Chart\" rel=\"Ext.chart.Chart\" class=\"docClass\">Ext.chart.Chart</a>.</p>\n</div><div class='members'></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"Theme.html#Ext-chart-theme-Theme","filename":"Theme.js"}],"linenr":1,"members":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.chart.theme.Theme","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.chart.theme.Theme","mixins":[],"mixedInto":["Ext.chart.Chart"]});