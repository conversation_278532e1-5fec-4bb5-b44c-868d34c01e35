Ext.data.JsonP.Ext_layout_container_HBox({"alternateClassNames":["Ext.layout.HBoxLayout"],"aliases":{"layout":["hbox"]},"enum":null,"parentMixins":["Ext.util.ElementContainer"],"tagname":"class","subclasses":["Ext.grid.ColumnLayout"],"extends":"Ext.layout.container.Box","uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Alternate names</h4><div class='alternate-class-name'>Ext.layout.HBoxLayout</div><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='docClass'>Ext.layout.Layout</a><div class='subclass '><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='docClass'>Ext.layout.container.Container</a><div class='subclass '><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='docClass'>Ext.layout.container.Box</a><div class='subclass '><strong>Ext.layout.container.HBox</strong></div></div></div></div></div><h4>Inherited mixins</h4><div class='dependency'><a href='#!/api/Ext.util.ElementContainer' rel='Ext.util.ElementContainer' class='docClass'>Ext.util.ElementContainer</a></div><h4>Subclasses</h4><div class='dependency'><a href='#!/api/Ext.grid.ColumnLayout' rel='Ext.grid.ColumnLayout' class='docClass'>Ext.grid.ColumnLayout</a></div><h4>Files</h4><div class='dependency'><a href='source/HBox.html#Ext-layout-container-HBox' target='_blank'>HBox.js</a></div></pre><div class='doc-contents'><p>A layout that arranges items horizontally across a Container. This layout optionally divides available horizontal\nspace between child items containing a numeric <code>flex</code> configuration.</p>\n\n<p>This layout may also be used to set the heights of child items by configuring it with the <a href=\"#!/api/Ext.layout.container.HBox-cfg-align\" rel=\"Ext.layout.container.HBox-cfg-align\" class=\"docClass\">align</a> option.</p>\n\n<pre class='inline-example '><code><a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.Panel</a>', {\n    width: 500,\n    height: 300,\n    title: \"HBoxLayout Panel\",\n    layout: {\n        type: 'hbox',\n        align: 'stretch'\n    },\n    renderTo: document.body,\n    items: [{\n        xtype: 'panel',\n        title: 'Inner Panel One',\n        flex: 2\n    },{\n        xtype: 'panel',\n        title: 'Inner Panel Two',\n        flex: 1\n    },{\n        xtype: 'panel',\n        title: 'Inner Panel Three',\n        flex: 1\n    }]\n});\n</code></pre>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-cfg'>Config options</h3><div class='subsection'><div id='cfg-align' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.HBox'>Ext.layout.container.HBox</span><br/><a href='source/HBox.html#Ext-layout-container-HBox-cfg-align' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.HBox-cfg-align' class='name expandable'>align</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Controls how the child items of the container are aligned. ...</div><div class='long'><p>Controls how the child items of the container are aligned. Acceptable configuration values for this property are:</p>\n\n<ul>\n<li><strong>top</strong> : <strong>Default</strong> child items are aligned vertically at the <strong>top</strong> of the container.</li>\n<li><strong>middle</strong> : child items are aligned vertically in the <strong>middle</strong> of the container.</li>\n<li><strong>bottom</strong> : child items are aligned vertically at the <strong>bottom</strong> of the container.</li>\n<li><strong>stretch</strong> : child items are stretched vertically to fill the height of the container.</li>\n<li><strong>stretchmax</strong> : child items are stretched vertically to the height of the largest item.</li>\n</ul>\n\n<p>Defaults to: <code>'top'</code></p></div></div></div><div id='cfg-alignRoundingMethod' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.HBox'>Ext.layout.container.HBox</span><br/><a href='source/HBox.html#Ext-layout-container-HBox-cfg-alignRoundingMethod' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.HBox-cfg-alignRoundingMethod' class='name expandable'>alignRoundingMethod</a><span> : \"round\"/\"floor\"/\"ceil\"</span></div><div class='description'><div class='short'>The Math method to use\nfor rounding fractional pixels when align:middle is used. ...</div><div class='long'><p>The Math method to use\nfor rounding fractional pixels when <code><a href=\"#!/api/Ext.layout.container.HBox-cfg-align\" rel=\"Ext.layout.container.HBox-cfg-align\" class=\"docClass\">align</a>:middle</code> is used.</p>\n<p>Defaults to: <code>'round'</code></p></div></div></div><div id='cfg-constrainAlign' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.HBox'>Ext.layout.container.HBox</span><br/><a href='source/HBox.html#Ext-layout-container-HBox-cfg-constrainAlign' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.HBox-cfg-constrainAlign' class='name expandable'>constrainAlign</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Limits the size of aligned components to the size of the container under certain circumstances. ...</div><div class='long'><p>Limits the size of <a href=\"#!/api/Ext.layout.container.HBox-cfg-align\" rel=\"Ext.layout.container.HBox-cfg-align\" class=\"docClass\">aligned</a> components to the size of the container under certain circumstances.\nFirstly, the container height must not be determined by the height of the child components. Secondly, the child\ncomponents must have their height <a href=\"#!/api/Ext.AbstractComponent-cfg-shrinkWrap\" rel=\"Ext.AbstractComponent-cfg-shrinkWrap\" class=\"docClass\">shrinkwrapped</a>.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-defaultMargins' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-cfg-defaultMargins' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-cfg-defaultMargins' class='name expandable'>defaultMargins</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>If the individual contained items do not have a margins property specified or margin specified via CSS, the\ndefault m...</div><div class='long'><p>If the individual contained items do not have a margins property specified or margin specified via CSS, the\ndefault margins from this property will be applied to each item.</p>\n\n<p>This property may be specified as an object containing margins to apply in the format:</p>\n\n<pre><code>{\n    top: (top margin),\n    right: (right margin),\n    bottom: (bottom margin),\n    left: (left margin)\n}\n</code></pre>\n\n<p>This property may also be specified as a string containing space-separated, numeric margin values. The order of\nthe sides associated with each value matches the way CSS processes margin values:</p>\n\n<ul>\n<li>If there is only one value, it applies to all sides.</li>\n<li>If there are two values, the top and bottom borders are set to the first value and the right and left are\nset to the second.</li>\n<li>If there are three values, the top is set to the first value, the left and right are set to the second,\nand the bottom is set to the third.</li>\n<li>If there are four values, they apply to the top, right, bottom, and left, respectively.</li>\n</ul>\n\n<p>Defaults to: <code>{top: 0, right: 0, bottom: 0, left: 0}</code></p></div></div></div><div id='cfg-flex' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-cfg-flex' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-cfg-flex' class='name expandable'>flex</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>This configuration option is to be applied to child items of the container managed by this layout. ...</div><div class='long'><p>This configuration option is to be applied to <strong>child items</strong> of the container managed by this layout. Each child\nitem with a flex property will be flexed (horizontally in <code>hbox</code>, vertically in <code>vbox</code>) according to each item's\n<strong>relative</strong> flex value compared to the sum of all items with a flex value specified. Any child items that have\neither a <code>flex = 0</code> or <code>flex = undefined</code> will not be 'flexed' (the initial size will not be changed).</p>\n</div></div></div><div id='cfg-itemCls' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-cfg-itemCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-cfg-itemCls' class='name expandable'>itemCls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>An optional extra CSS class that will be added to the container. ...</div><div class='long'><p>An optional extra CSS class that will be added to the container. This can be useful for\nadding customized styles to the container or any of its children using standard CSS\nrules. See <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>.<a href=\"#!/api/Ext.Component-cfg-componentCls\" rel=\"Ext.Component-cfg-componentCls\" class=\"docClass\">componentCls</a> also.</p>\n</div></div></div><div id='cfg-itemCls' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-cfg-itemCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-cfg-itemCls' class='name expandable'>itemCls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>An optional extra CSS class that will be added to the container. ...</div><div class='long'><p>An optional extra CSS class that will be added to the container. This can be useful for\nadding customized styles to the container or any of its children using standard CSS\nrules. See <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>.<a href=\"#!/api/Ext.Component-cfg-componentCls\" rel=\"Ext.Component-cfg-componentCls\" class=\"docClass\">componentCls</a> also.</p>\n<p>Defaults to: <code>Ext.baseCSSPrefix + 'box-item'</code></p><p>Overrides: <a href='#!/api/Ext.layout.container.Container-cfg-itemCls' rel='Ext.layout.container.Container-cfg-itemCls' class='docClass'>Ext.layout.container.Container.itemCls</a></p></div></div></div><div id='cfg-pack' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-cfg-pack' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-cfg-pack' class='name expandable'>pack</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Controls how the child items of the container are packed together. ...</div><div class='long'><p>Controls how the child items of the container are packed together. Acceptable configuration values for this\nproperty are:</p>\n\n<ul>\n<li><strong>start</strong> - child items are packed together at <strong>left</strong> (HBox) or <strong>top</strong> (VBox) side of container (*default**)</li>\n<li><strong>center</strong> - child items are packed together at <strong>mid-width</strong> (HBox) or <strong>mid-height</strong> (VBox) of container</li>\n<li><strong>end</strong> - child items are packed together at <strong>right</strong> (HBox) or <strong>bottom</strong> (VBox) side of container</li>\n</ul>\n\n<p>Defaults to: <code>'start'</code></p></div></div></div><div id='cfg-padding' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-cfg-padding' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-cfg-padding' class='name expandable'>padding</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Sets the padding to be applied to all child items managed by this layout. ...</div><div class='long'><p>Sets the padding to be applied to all child items managed by this layout.</p>\n\n<p>This property must be specified as a string containing space-separated, numeric padding values. The order of the\nsides associated with each value matches the way CSS processes padding values:</p>\n\n<ul>\n<li>If there is only one value, it applies to all sides.</li>\n<li>If there are two values, the top and bottom borders are set to the first value and the right and left are\nset to the second.</li>\n<li>If there are three values, the top is set to the first value, the left and right are set to the second,\nand the bottom is set to the third.</li>\n<li>If there are four values, they apply to the top, right, bottom, and left, respectively.</li>\n</ul>\n\n<p>Defaults to: <code>0</code></p></div></div></div><div id='cfg-stretchMaxPartner' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-cfg-stretchMaxPartner' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-cfg-stretchMaxPartner' class='name expandable'>stretchMaxPartner</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a></span></div><div class='description'><div class='short'>Allows stretchMax calculation to take into account the max perpendicular size (height for HBox layout and width\nfor V...</div><div class='long'><p>Allows stretchMax calculation to take into account the max perpendicular size (height for HBox layout and width\nfor VBox layout) of another Box layout when calculating its maximum perpendicular child size.</p>\n\n<p>If specified as a string, this may be either a known Container ID, or a ComponentQuery selector which is rooted\nat this layout's Container (ie, to find a sibling, use <code>\"^&gt;#siblingItemId</code>).</p>\n</div></div></div></div></div><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-_percentageRe' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-property-_percentageRe' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-property-_percentageRe' class='name expandable'>_percentageRe</a><span> : <a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Matches: &lt;spaces&gt;digits[.digits]&lt;spaces&gt;%&lt;spaces&gt;\nCaptures: digits[.digits] ...</div><div class='long'><p>Matches: <code>&lt;spaces&gt;digits[.digits]&lt;spaces&gt;%&lt;spaces&gt;</code>\nCaptures: <code>digits[.digits]</code></p>\n<p>Defaults to: <code>/^\\s*(\\d+(?:\\.\\d*)?)\\s*[%]\\s*$/</code></p></div></div></div><div id='property-alignRoundingMethod' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-property-alignRoundingMethod' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-property-alignRoundingMethod' class='name expandable'>alignRoundingMethod</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'round'</code></p></div></div></div><div id='property-animatePolicy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-property-animatePolicy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-property-animatePolicy' class='name expandable'>animatePolicy</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>An object which contains boolean properties specifying which properties are to be\nanimated upon flush of child Compon...</div><div class='long'><p>An object which contains boolean properties specifying which properties are to be\nanimated upon flush of child Component ContextItems. For example, Accordion would\nhave:</p>\n\n<pre><code> {\n     y: true,\n     height: true\n }\n</code></pre>\n</div></div></div><div id='property-autoSizePolicy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-property-autoSizePolicy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-property-autoSizePolicy' class='name expandable'>autoSizePolicy</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{readsWidth: 1, readsHeight: 1, setsWidth: 0, setsHeight: 0}</code></p></div></div></div><div id='property-availableSpaceOffset' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-property-availableSpaceOffset' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-property-availableSpaceOffset' class='name expandable'>availableSpaceOffset</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>availableSpaceOffset is used to adjust the availableWidth, typically used\nto reserve space for a scrollbar ...</div><div class='long'><p>availableSpaceOffset is used to adjust the availableWidth, typically used\nto reserve space for a scrollbar</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-calculateChildBox' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-property-calculateChildBox' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-property-calculateChildBox' class='name not-expandable'>calculateChildBox</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'><p><debug></p>\n</div><div class='long'><p><debug></p>\n</div></div></div><div id='property-calculateChildBoxes' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-property-calculateChildBoxes' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-property-calculateChildBoxes' class='name not-expandable'>calculateChildBoxes</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-childEls' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-property-childEls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-property-childEls' class='name expandable'>childEls</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>['innerCt', 'targetEl']</code></p><p>Overrides: <a href='#!/api/Ext.layout.container.Container-property-childEls' rel='Ext.layout.container.Container-property-childEls' class='docClass'>Ext.layout.container.Container.childEls</a></p></div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-createsInnerCt' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-property-createsInnerCt' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-property-createsInnerCt' class='name expandable'>createsInnerCt</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-direction' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.HBox'>Ext.layout.container.HBox</span><br/><a href='source/HBox.html#Ext-layout-container-HBox-property-direction' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.HBox-property-direction' class='name expandable'>direction</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'horizontal'</code></p></div></div></div><div id='property-done' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-property-done' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-property-done' class='name expandable'>done</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Used only during a layout run, this value indicates that a\nlayout has finished its calculations. ...</div><div class='long'><p>Used only during a layout run, this value indicates that a\nlayout has finished its calculations. This flag is set to true prior to the call to\n<a href=\"#!/api/Ext.layout.Layout-method-calculate\" rel=\"Ext.layout.Layout-method-calculate\" class=\"docClass\">calculate</a> and should be set to false if this layout has more work to do.</p>\n</div></div></div><div id='property-horizontal' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.HBox'>Ext.layout.container.HBox</span><br/><a href='source/HBox.html#Ext-layout-container-HBox-property-horizontal' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.HBox-property-horizontal' class='name expandable'>horizontal</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-initialized' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-property-initialized' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-property-initialized' class='name expandable'>initialized</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-innerCls' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-property-innerCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-property-innerCls' class='name expandable'>innerCls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>Ext.baseCSSPrefix + 'box-inner'</code></p></div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isLayout' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-property-isLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-property-isLayout' class='name expandable'>isLayout</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='readonly signature' >readonly</strong></div><div class='description'><div class='short'>true in this class to identify an object as an instantiated Layout, or subclass thereof. ...</div><div class='long'><p><code>true</code> in this class to identify an object as an instantiated Layout, or subclass thereof.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-manageMargins' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-property-manageMargins' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-property-manageMargins' class='name expandable'>manageMargins</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-names' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.HBox'>Ext.layout.container.HBox</span><br/><a href='source/HBox.html#Ext-layout-container-HBox-property-names' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.HBox-property-names' class='name expandable'>names</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{beforeX: 'left', beforeScrollX: 'left', beforeScrollerSuffix: '-before-scroller', afterScrollerSuffix: '-after-scroller', leftCap: 'Left', afterX: 'right', width: 'width', contentWidth: 'contentWidth', minWidth: 'minWidth', maxWidth: 'maxWidth', widthCap: 'Width', widthModel: 'widthModel', widthIndex: 0, x: 'x', scrollLeft: 'scrollLeft', overflowX: 'overflowX', hasOverflowX: 'hasOverflowX', invalidateScrollX: 'invalidateScrollX', parallelMargins: 'lr', center: 'middle', beforeY: 'top', afterY: 'bottom', height: 'height', contentHeight: 'contentHeight', minHeight: 'minHeight', maxHeight: 'maxHeight', heightCap: 'Height', heightModel: 'heightModel', heightIndex: 1, y: 'y', overflowY: 'overflowY', hasOverflowY: 'hasOverflowY', invalidateScrollY: 'invalidateScrollY', perpendicularMargins: 'tb', getWidth: 'getWidth', getHeight: 'getHeight', setWidth: 'setWidth', setHeight: 'setHeight', gotWidth: 'gotWidth', gotHeight: 'gotHeight', setContentWidth: 'setContentWidth', setContentHeight: 'setContentHeight', setWidthInDom: 'setWidthInDom', setHeightInDom: 'setHeightInDom', getScrollLeft: 'getScrollLeft', setScrollLeft: 'setScrollLeft', scrollTo: 'scrollTo'}</code></p></div></div></div><div id='property-overflowPadderEl' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-property-overflowPadderEl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-property-overflowPadderEl' class='name not-expandable'>overflowPadderEl</a><span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span></div><div class='description'><div class='short'><p>The element used to correct body padding during overflow.</p>\n</div><div class='long'><p>The element used to correct body padding during overflow.</p>\n</div></div></div><div id='property-renderTpl' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-property-renderTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-property-renderTpl' class='name expandable'>renderTpl</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>['{%var oc,l=values.$comp.layout,oh=l.overflowHandler;', 'if (oh.getPrefixConfig!==Ext.emptyFn) {', 'if(oc=oh.getPrefixConfig())dh.generateMarkup(oc, out)', '}%}', '&lt;div id=&quot;{ownerId}-innerCt&quot; class=&quot;{[l.innerCls]} {[oh.getOverflowCls()]}&quot; role=&quot;presentation&quot;&gt;', '&lt;div id=&quot;{ownerId}-targetEl&quot; class=&quot;{targetElCls}&quot;&gt;', '{%this.renderBody(out, values)%}', '&lt;/div&gt;', '&lt;/div&gt;', '{%if (oh.getSuffixConfig!==Ext.emptyFn) {', 'if(oc=oh.getSuffixConfig())dh.generateMarkup(oc, out)', '}%}', {disableFormats: true, definitions: 'var dh=Ext.DomHelper;'}]</code></p><p>Overrides: <a href='#!/api/Ext.layout.container.Container-property-renderTpl' rel='Ext.layout.container.Container-property-renderTpl' class='docClass'>Ext.layout.container.Container.renderTpl</a></p></div></div></div><div id='property-reserveOffset' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-property-reserveOffset' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-property-reserveOffset' class='name expandable'>reserveOffset</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>whether or not to reserve the availableSpaceOffset in layout calculations ...</div><div class='long'><p>whether or not to reserve the availableSpaceOffset in layout calculations</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-running' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-property-running' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-property-running' class='name expandable'>running</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-scrollOffset' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-property-scrollOffset' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-property-scrollOffset' class='name expandable'>scrollOffset</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div><div id='property-sizePolicy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.HBox'>Ext.layout.container.HBox</span><br/><a href='source/HBox.html#Ext-layout-container-HBox-property-sizePolicy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.HBox-property-sizePolicy' class='name expandable'>sizePolicy</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{flex: {'': {readsWidth: 0, readsHeight: 1, setsWidth: 1, setsHeight: 0}, stretch: {readsWidth: 0, readsHeight: 0, setsWidth: 1, setsHeight: 1}, stretchmax: {readsWidth: 0, readsHeight: 1, setsWidth: 1, setsHeight: 1}}, '': {readsWidth: 1, readsHeight: 1, setsWidth: 0, setsHeight: 0}, stretch: {readsWidth: 1, readsHeight: 0, setsWidth: 0, setsHeight: 1}, stretchmax: {readsWidth: 1, readsHeight: 1, setsWidth: 0, setsHeight: 1}}</code></p></div></div></div><div id='property-targetCls' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-property-targetCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-property-targetCls' class='name expandable'>targetCls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>Ext.baseCSSPrefix + 'box-layout-ct'</code></p></div></div></div><div id='property-targetElCls' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-property-targetElCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-property-targetElCls' class='name expandable'>targetElCls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>Ext.baseCSSPrefix + 'box-target'</code></p></div></div></div><div id='property-type' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.container.HBox'>Ext.layout.container.HBox</span><br/><a href='source/HBox.html#Ext-layout-container-HBox-property-type' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.HBox-property-type' class='name expandable'>type</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'hbox'</code></p><p>Overrides: <a href='#!/api/Ext.layout.container.Box-property-type' rel='Ext.layout.container.Box-property-type' class='docClass'>Ext.layout.container.Box.type</a></p></div></div></div><div id='property-updateChildBoxes' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-property-updateChildBoxes' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-property-updateChildBoxes' class='name not-expandable'>updateChildBoxes</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-usesContainerHeight' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-property-usesContainerHeight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-property-usesContainerHeight' class='name expandable'>usesContainerHeight</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-usesContainerWidth' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-property-usesContainerWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-property-usesContainerWidth' class='name expandable'>usesContainerWidth</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-usesHeight' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-property-usesHeight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-property-usesHeight' class='name expandable'>usesHeight</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-usesWidth' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-property-usesWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-property-usesWidth' class='name expandable'>usesWidth</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-constructor' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/Ext.layout.container.Box-method-constructor' class='name expandable'>Ext.layout.container.HBox</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.layout.container.Box\" rel=\"Ext.layout.container.Box\" class=\"docClass\">Ext.layout.container.Box</a></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.layout.container.Box\" rel=\"Ext.layout.container.Box\" class=\"docClass\">Ext.layout.container.Box</a></span><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.container.Container-method-constructor' rel='Ext.layout.container.Container-method-constructor' class='docClass'>Ext.layout.container.Container.constructor</a></p></div></div></div><div id='method-addChildEls' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.ElementContainer' rel='Ext.util.ElementContainer' class='defined-in docClass'>Ext.util.ElementContainer</a><br/><a href='source/ElementContainer.html#Ext-util-ElementContainer-method-addChildEls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.ElementContainer-method-addChildEls' class='name expandable'>addChildEls</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Adds each argument passed to this method to the childEls array. ...</div><div class='long'><p>Adds each argument passed to this method to the <a href=\"#!/api/Ext.AbstractComponent-cfg-childEls\" rel=\"Ext.AbstractComponent-cfg-childEls\" class=\"docClass\">childEls</a> array.</p>\n</div></div></div><div id='method-afterRemove' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-afterRemove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-afterRemove' class='name expandable'>afterRemove</a>( <span class='pre'>item</span> )<strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Removes layout's itemCls and owning Container's itemCls. ...</div><div class='long'><p>Removes layout's itemCls and owning Container's itemCls.\nClears the managed dimensions flags</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-afterRenderItem' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-afterRenderItem' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-afterRenderItem' class='name expandable'>afterRenderItem</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-applyChildEls' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.ElementContainer' rel='Ext.util.ElementContainer' class='defined-in docClass'>Ext.util.ElementContainer</a><br/><a href='source/ElementContainer.html#Ext-util-ElementContainer-method-applyChildEls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.ElementContainer-method-applyChildEls' class='name expandable'>applyChildEls</a>( <span class='pre'>el, id</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Sets references to elements inside the component. ...</div><div class='long'><p>Sets references to elements inside the component.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-beginCollapse' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-beginCollapse' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-beginCollapse' class='name expandable'>beginCollapse</a>( <span class='pre'>child</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Called by an owning Panel before the Panel begins its collapse process. ...</div><div class='long'><p>Called by an owning Panel before the Panel begins its collapse process.\nMost layouts will not need to override the default <a href=\"#!/api/Ext-property-emptyFn\" rel=\"Ext-property-emptyFn\" class=\"docClass\">Ext.emptyFn</a> implementation.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>child</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.container.Container-method-beginCollapse' rel='Ext.layout.container.Container-method-beginCollapse' class='docClass'>Ext.layout.container.Container.beginCollapse</a></p></div></div></div><div id='method-beginExpand' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-beginExpand' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-beginExpand' class='name expandable'>beginExpand</a>( <span class='pre'>child</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Called by an owning Panel before the Panel begins its expand process. ...</div><div class='long'><p>Called by an owning Panel before the Panel begins its expand process.\nMost layouts will not need to override the default <a href=\"#!/api/Ext-property-emptyFn\" rel=\"Ext-property-emptyFn\" class=\"docClass\">Ext.emptyFn</a> implementation.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>child</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.container.Container-method-beginExpand' rel='Ext.layout.container.Container-method-beginExpand' class='docClass'>Ext.layout.container.Container.beginExpand</a></p></div></div></div><div id='method-beginLayout' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-beginLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-beginLayout' class='name expandable'>beginLayout</a>( <span class='pre'>ownerContext</span> )</div><div class='description'><div class='short'>In addition to work done by our base classes, containers benefit from some extra\ncached data. ...</div><div class='long'><p>In addition to work done by our base classes, containers benefit from some extra\ncached data. The following properties are added to the ownerContext:</p>\n\n<ul>\n<li>visibleItems: the result of <a href=\"#!/api/Ext.layout.container.Box-method-getVisibleItems\" rel=\"Ext.layout.container.Box-method-getVisibleItems\" class=\"docClass\">getVisibleItems</a></li>\n<li>childItems: the ContextItem[] for each visible item</li>\n<li>targetContext: the ContextItem for the <a href=\"#!/api/Ext.layout.container.Box-method-getTarget\" rel=\"Ext.layout.container.Box-method-getTarget\" class=\"docClass\">getTarget</a> element</li>\n</ul>\n\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.container.Container-method-beginLayout' rel='Ext.layout.container.Container-method-beginLayout' class='docClass'>Ext.layout.container.Container.beginLayout</a></p></div></div></div><div id='method-beginLayoutCycle' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-beginLayoutCycle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-beginLayoutCycle' class='name expandable'>beginLayoutCycle</a>( <span class='pre'>ownerContext</span> )</div><div class='description'><div class='short'>Called before any calculation cycles to reset DOM values and prepare for calculation. ...</div><div class='long'><p>Called before any calculation cycles to reset DOM values and prepare for calculation.</p>\n\n<p>This is a write phase and DOM reads should be strictly avoided when overridding\nthis method.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'><p>The context item for the layout's owner\ncomponent.</p>\n\n\n\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.container.Container-method-beginLayoutCycle' rel='Ext.layout.container.Container-method-beginLayoutCycle' class='docClass'>Ext.layout.container.Container.beginLayoutCycle</a></p></div></div></div><div id='method-cacheChildItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-cacheChildItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-cacheChildItems' class='name expandable'>cacheChildItems</a>( <span class='pre'>ownerContext</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-cacheElements' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-cacheElements' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-cacheElements' class='name expandable'>cacheElements</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-cacheFlexes' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-cacheFlexes' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-cacheFlexes' class='name expandable'>cacheFlexes</a>( <span class='pre'>ownerContext</span> )<strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is called to (re)cache our understanding of flexes. ...</div><div class='long'><p>This method is called to (re)cache our understanding of flexes. This happens during beginLayout and may need to\nbe called again if the flexes are changed during the layout (e.g., like ColumnLayout).</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-calculate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-calculate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-calculate' class='name expandable'>calculate</a>( <span class='pre'>ownerContext</span> )<strong class='abstract signature' >abstract</strong></div><div class='description'><div class='short'>Called to perform the calculations for this layout. ...</div><div class='long'><p>Called to perform the calculations for this layout. This method will be called at\nleast once and may be called repeatedly if the <a href=\"#!/api/Ext.layout.container.Box-property-done\" rel=\"Ext.layout.container.Box-property-done\" class=\"docClass\">done</a> property is cleared\nbefore return to indicate that this layout is not yet done. The <a href=\"#!/api/Ext.layout.container.Box-property-done\" rel=\"Ext.layout.container.Box-property-done\" class=\"docClass\">done</a> property\nis always set to <code>true</code> before entering this method.</p>\n\n<p>This is a read phase and DOM writes should be strictly avoided in derived classes.\nInstead, DOM writes need to be written to <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a> objects to\n be flushed at the next opportunity.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'><p>The context item for the layout's owner\ncomponent.</p>\n\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-calculate' rel='Ext.layout.Layout-method-calculate' class='docClass'>Ext.layout.Layout.calculate</a></p></div></div></div><div id='method-calculateParallel' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-calculateParallel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-calculateParallel' class='name expandable'>calculateParallel</a>( <span class='pre'>ownerContext, names, plan</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>plan</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-calculatePerpendicular' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-calculatePerpendicular' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-calculatePerpendicular' class='name expandable'>calculatePerpendicular</a>( <span class='pre'>ownerContext, names, plan</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>plan</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-calculateStretchMax' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-calculateStretchMax' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-calculateStretchMax' class='name expandable'>calculateStretchMax</a>( <span class='pre'>ownerContext, names, plan</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>plan</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-callOverridden' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-completeLayout' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-completeLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-completeLayout' class='name expandable'>completeLayout</a>( <span class='pre'>ownerContext</span> )</div><div class='description'><div class='short'>This method (if implemented) is called at the end of the cycle in which this layout\ncompletes (by not setting done to...</div><div class='long'><p>This method (if implemented) is called at the end of the cycle in which this layout\ncompletes (by not setting <a href=\"#!/api/Ext.layout.container.Box-property-done\" rel=\"Ext.layout.container.Box-property-done\" class=\"docClass\">done</a> to <code>false</code> in <a href=\"#!/api/Ext.layout.container.Box-method-calculate\" rel=\"Ext.layout.container.Box-method-calculate\" class=\"docClass\">calculate</a>). It is\npossible for the layout to complete and yet become invalid before the end of the cycle,\nin which case, this method will not be called. It is also possible for this method to\nbe called and then later the layout becomes invalidated. This will result in\n<a href=\"#!/api/Ext.layout.container.Box-method-calculate\" rel=\"Ext.layout.container.Box-method-calculate\" class=\"docClass\">calculate</a> being called again, followed by another call to this method.</p>\n\n<p>This is a read phase and DOM writes should be strictly avoided in derived classes.\nInstead, DOM writes need to be written to <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a> objects to\nbe flushed at the next opportunity.</p>\n\n<p>This method need not be implemented by derived classes and, in fact, should only be\nimplemented when needed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'><p>The context item for the layout's owner\ncomponent.</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-completeLayout' rel='Ext.layout.Layout-method-completeLayout' class='docClass'>Ext.layout.Layout.completeLayout</a></p></div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-configureItem' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-configureItem' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-configureItem' class='name expandable'>configureItem</a>( <span class='pre'>item</span> )<strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Adds layout's itemCls and owning Container's itemCls ...</div><div class='long'><p>Adds layout's itemCls and owning Container's itemCls</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-configureItem' rel='Ext.layout.Layout-method-configureItem' class='docClass'>Ext.layout.Layout.configureItem</a></p></div></div></div><div id='method-destroy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-destroy' class='name expandable'>destroy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.layout.container.Container-method-destroy' rel='Ext.layout.container.Container-method-destroy' class='docClass'>Ext.layout.container.Container.destroy</a></p></div></div></div><div id='method-doRenderBody' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-doRenderBody' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-doRenderBody' class='name expandable'>doRenderBody</a>( <span class='pre'>out, renderData</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>out</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>renderData</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-doRenderContainer' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-doRenderContainer' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-doRenderContainer' class='name expandable'>doRenderContainer</a>( <span class='pre'>out, renderData</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>out</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>renderData</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-doRenderItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-doRenderItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-doRenderItems' class='name expandable'>doRenderItems</a>( <span class='pre'>out, renderData</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>out</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>renderData</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-finalizeLayout' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-finalizeLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-finalizeLayout' class='name expandable'>finalizeLayout</a>( <span class='pre'>ownerContext</span> )</div><div class='description'><div class='short'>This method (if implemented) is called after all layouts have completed. ...</div><div class='long'><p>This method (if implemented) is called after all layouts have completed. In most\nways this is similar to <a href=\"#!/api/Ext.layout.Layout-method-completeLayout\" rel=\"Ext.layout.Layout-method-completeLayout\" class=\"docClass\">completeLayout</a>. This call can cause this (or any\nlayout) to be become invalid (see <a href=\"#!/api/Ext.layout.Context-method-invalidate\" rel=\"Ext.layout.Context-method-invalidate\" class=\"docClass\">Ext.layout.Context.invalidate</a>), but this\nis best avoided. This method is intended to be where final reads are made and so it\nis best to avoid invalidating layouts at this point whenever possible. Even so, this\nmethod can be used to perform final checks that may require all other layouts to be\ncomplete and then invalidate some results.</p>\n\n<p>This is a read phase and DOM writes should be strictly avoided in derived classes.\nInstead, DOM writes need to be written to <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a> objects to\nbe flushed at the next opportunity.</p>\n\n<p>This method need not be implemented by derived classes and, in fact, should only be\nimplemented when needed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'><p>The context item for the layout's owner\ncomponent.</p>\n</div></li></ul></div></div></div><div id='method-finishRender' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-finishRender' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-finishRender' class='name expandable'>finishRender</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.layout.Layout-method-finishRender' rel='Ext.layout.Layout-method-finishRender' class='docClass'>Ext.layout.Layout.finishRender</a></p></div></div></div><div id='method-finishRenderItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-finishRenderItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-finishRenderItems' class='name expandable'>finishRenderItems</a>( <span class='pre'>target, items</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>items</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-finishedLayout' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-finishedLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-finishedLayout' class='name expandable'>finishedLayout</a>( <span class='pre'>ownerContext</span> )</div><div class='description'><div class='short'>This method is called after all layouts are complete and their calculations flushed\nto the DOM. ...</div><div class='long'><p>This method is called after all layouts are complete and their calculations flushed\nto the DOM. No further layouts will be run and this method is only called once per\nlayout run. The base component layout caches <code>lastComponentSize</code>.</p>\n\n<p>This is a write phase and DOM reads should be avoided if possible when overridding\nthis method.</p>\n\n<p>This method need not be implemented by derived classes and, in fact, should only be\nimplemented when needed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'><p>The context item for the layout's owner\ncomponent.</p>\n\n\n\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-finishedLayout' rel='Ext.layout.Layout-method-finishedLayout' class='docClass'>Ext.layout.Layout.finishedLayout</a></p></div></div></div><div id='method-flexSort' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-flexSort' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-flexSort' class='name expandable'>flexSort</a>( <span class='pre'>a, b</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>a</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>b</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getAnimatePolicy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-getAnimatePolicy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-getAnimatePolicy' class='name expandable'>getAnimatePolicy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getChildEls' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.ElementContainer' rel='Ext.util.ElementContainer' class='defined-in docClass'>Ext.util.ElementContainer</a><br/><a href='source/ElementContainer.html#Ext-util-ElementContainer-method-getChildEls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.ElementContainer-method-getChildEls' class='name expandable'>getChildEls</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getClassChildEls' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.ElementContainer' rel='Ext.util.ElementContainer' class='defined-in docClass'>Ext.util.ElementContainer</a><br/><a href='source/ElementContainer.html#Ext-util-ElementContainer-method-getClassChildEls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.ElementContainer-method-getClassChildEls' class='name expandable'>getClassChildEls</a>( <span class='pre'>cls</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>cls</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getContainerSize' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getContainerSize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getContainerSize' class='name expandable'>getContainerSize</a>( <span class='pre'>ownerContext, [inDom]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Returns the container size (that of the target). ...</div><div class='long'><p>Returns the container size (that of the target). Only the fixed-sized dimensions can\nbe returned because the shrinkWrap dimensions are based on the contentWidth/Height\nas determined by the container layout.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'><p>The owner's context item.</p>\n</div></li><li><span class='pre'>inDom</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True if the container size must be in the DOM.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The size</p>\n<ul><li><span class='pre'>width</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The width</p>\n</div></li><li><span class='pre'>height</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The height</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-getContentTarget' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getContentTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getContentTarget' class='name expandable'>getContentTarget</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getElementTarget' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-getElementTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-getElementTarget' class='name expandable'>getElementTarget</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></div><div class='description'><div class='short'>Overridden method from Ext.layout.container.Container. ...</div><div class='long'><p>Overridden method from <a href=\"#!/api/Ext.layout.container.Container\" rel=\"Ext.layout.container.Container\" class=\"docClass\">Ext.layout.container.Container</a>.\nUsed by Container classes to insert special DOM elements which must exist in addition to the child components</p>\n\n<p>Returns the element into which extra functional DOM elements can be inserted. Defaults to the owner Component's encapsulating element.</p>\n\n<p>May be overridden in Component layout managers which implement a <a href=\"#!/api/Ext.layout.container.Box-method-getRenderTarget\" rel=\"Ext.layout.container.Box-method-getRenderTarget\" class=\"docClass\">component render target</a> which must only\ncontain child components.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.container.Container-method-getElementTarget' rel='Ext.layout.container.Container-method-getElementTarget' class='docClass'>Ext.layout.container.Container.getElementTarget</a></p></div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-getItemSizePolicy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-getItemSizePolicy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-getItemSizePolicy' class='name expandable'>getItemSizePolicy</a>( <span class='pre'>item</span> ) : <a href=\"#!/api/Ext.layout.SizePolicy\" rel=\"Ext.layout.SizePolicy\" class=\"docClass\">Ext.layout.SizePolicy</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Returns an object describing how this layout manages the size of the given component. ...</div><div class='long'><p>Returns an object describing how this layout manages the size of the given component.\nThis method must be implemented by any layout that manages components.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.layout.SizePolicy\" rel=\"Ext.layout.SizePolicy\" class=\"docClass\">Ext.layout.SizePolicy</a></span><div class='sub-desc'><p>An object describing the sizing done by the layout\nfor this item.</p>\n\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-getItemSizePolicy' rel='Ext.layout.Layout-method-getItemSizePolicy' class='docClass'>Ext.layout.Layout.getItemSizePolicy</a></p></div></div></div><div id='method-getItemsRenderTree' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-getItemsRenderTree' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-getItemsRenderTree' class='name expandable'>getItemsRenderTree</a>( <span class='pre'>items, renderCfgs</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'><hr />\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>items</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>renderCfgs</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getLayoutItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getLayoutItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getLayoutItems' class='name expandable'>getLayoutItems</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>[]</div><div class='description'><div class='short'>Returns an array of child components either for a render phase (Performed in the beforeLayout\nmethod of the layout's ...</div><div class='long'><p>Returns an array of child components either for a render phase (Performed in the beforeLayout\nmethod of the layout's base class), or the layout phase (onLayout).</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>[]</span><div class='sub-desc'><p>of child components</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-getLayoutItems' rel='Ext.layout.Layout-method-getLayoutItems' class='docClass'>Ext.layout.Layout.getLayoutItems</a></p></div></div></div><div id='method-getPositionOffset' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getPositionOffset' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getPositionOffset' class='name expandable'>getPositionOffset</a>( <span class='pre'>position</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>This method is used to offset the DOM position when checking\nwhether the element is a certain child of the target. ...</div><div class='long'><p>This method is used to offset the DOM position when checking\nwhether the element is a certain child of the target. This is\nrequired in cases where the extra elements prepended to the target\nbefore any of the items. An example of this is when using labelAlign: 'top'\non a field. The label appears first in the DOM before any child items are\ncreated, so when we check the position we need to add an extra offset.\nContainers that create an innerCt are exempt because this new element\npreserves the order</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>position</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-getPositionOffset' rel='Ext.layout.Layout-method-getPositionOffset' class='docClass'>Ext.layout.Layout.getPositionOffset</a></p></div></div></div><div id='method-getRenderData' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-getRenderData' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-getRenderData' class='name expandable'>getRenderData</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.layout.container.Container-method-getRenderData' rel='Ext.layout.container.Container-method-getRenderData' class='docClass'>Ext.layout.container.Container.getRenderData</a></p></div></div></div><div id='method-getRenderTarget' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-getRenderTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-getRenderTarget' class='name expandable'>getRenderTarget</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></div><div class='description'><div class='short'>Overridden method from Ext.layout.container.Container. ...</div><div class='long'><p>Overridden method from <a href=\"#!/api/Ext.layout.container.Container\" rel=\"Ext.layout.container.Container\" class=\"docClass\">Ext.layout.container.Container</a>.\nUsed in the beforeLayout method to render all items into.</p>\n\n<p>Returns the element into which rendering must take place. Defaults to the owner Container's\ntarget element.</p>\n\n<p>May be overridden in layout managers which implement an inner element.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.container.Container-method-getRenderTarget' rel='Ext.layout.container.Container-method-getRenderTarget' class='docClass'>Ext.layout.container.Container.getRenderTarget</a></p></div></div></div><div id='method-getRenderTpl' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getRenderTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getRenderTpl' class='name expandable'>getRenderTpl</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getRenderTree' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getRenderTree' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getRenderTree' class='name expandable'>getRenderTree</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getRenderedItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getRenderedItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getRenderedItems' class='name expandable'>getRenderedItems</a>( <span class='pre'></span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Returns all items that are rendered ...</div><div class='long'><p>Returns all items that are rendered</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>All matching items</p>\n</div></li></ul></div></div></div><div id='method-getScrollbarsNeeded' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getScrollbarsNeeded' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getScrollbarsNeeded' class='name expandable'>getScrollbarsNeeded</a>( <span class='pre'>width, height, contentWidth, contentHeight</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>width</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>height</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>contentWidth</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>contentHeight</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getTarget' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getTarget' class='name expandable'>getTarget</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></div><div class='description'><div class='short'>Returns the owner component's resize element. ...</div><div class='long'><p>Returns the owner component's resize element.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getVisibleItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-getVisibleItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-getVisibleItems' class='name expandable'>getVisibleItems</a>( <span class='pre'></span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Returns all items that are both rendered and visible ...</div><div class='long'><p>Returns all items that are both rendered and visible</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>All matching items</p>\n</div></li></ul></div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-initLayout' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-initLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-initLayout' class='name expandable'>initLayout</a>( <span class='pre'></span> )<strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>A one-time initialization method called just before rendering. ...</div><div class='long'><p>A one-time initialization method called just before rendering.</p>\n</div></div></div><div id='method-initOverflowHandler' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-initOverflowHandler' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-initOverflowHandler' class='name expandable'>initOverflowHandler</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-isItemBoxParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-isItemBoxParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-isItemBoxParent' class='name expandable'>isItemBoxParent</a>( <span class='pre'>itemContext</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>itemContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-isItemBoxParent' rel='Ext.layout.Layout-method-isItemBoxParent' class='docClass'>Ext.layout.Layout.isItemBoxParent</a></p></div></div></div><div id='method-isItemLayoutRoot' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-isItemLayoutRoot' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-isItemLayoutRoot' class='name expandable'>isItemLayoutRoot</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isItemShrinkWrap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-isItemShrinkWrap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-isItemShrinkWrap' class='name expandable'>isItemShrinkWrap</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-isItemShrinkWrap' rel='Ext.layout.Layout-method-isItemShrinkWrap' class='docClass'>Ext.layout.Layout.isItemShrinkWrap</a></p></div></div></div><div id='method-isRunning' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-isRunning' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-isRunning' class='name expandable'>isRunning</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-isValidParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-isValidParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-isValidParent' class='name expandable'>isValidParent</a>( <span class='pre'>item, target, position</span> )<strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Validates item is in the proper place in the dom. ...</div><div class='long'><p>Validates item is in the proper place in the dom.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>position</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-moveItem' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-moveItem' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-moveItem' class='name expandable'>moveItem</a>( <span class='pre'>item, target, position</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Moves Component to the provided target instead. ...</div><div class='long'><p>Moves Component to the provided target instead.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>position</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-notifyOwner' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-notifyOwner' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-notifyOwner' class='name expandable'>notifyOwner</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Called for every layout in the layout context after all the layouts have been finally flushed ...</div><div class='long'><p>Called for every layout in the layout context after all the layouts have been finally flushed</p>\n<p>Overrides: <a href='#!/api/Ext.layout.Layout-method-notifyOwner' rel='Ext.layout.Layout-method-notifyOwner' class='docClass'>Ext.layout.Layout.notifyOwner</a></p></div></div></div><div id='method-onAdd' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-onAdd' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-onAdd' class='name expandable'>onAdd</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onAfterConstrainInvalidateChild' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-onAfterConstrainInvalidateChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-onAfterConstrainInvalidateChild' class='name expandable'>onAfterConstrainInvalidateChild</a>( <span class='pre'>childContext, options</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>childContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onAfterStretchMaxInvalidateChild' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-onAfterStretchMaxInvalidateChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-onAfterStretchMaxInvalidateChild' class='name expandable'>onAfterStretchMaxInvalidateChild</a>( <span class='pre'>childContext, options</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>childContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onBeforeConstrainInvalidateChild' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-onBeforeConstrainInvalidateChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-onBeforeConstrainInvalidateChild' class='name expandable'>onBeforeConstrainInvalidateChild</a>( <span class='pre'>childContext, options</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>childContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onBeforeStretchMaxInvalidateChild' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-onBeforeStretchMaxInvalidateChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-onBeforeStretchMaxInvalidateChild' class='name expandable'>onBeforeStretchMaxInvalidateChild</a>( <span class='pre'>childContext, options</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>childContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onContentChange' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-onContentChange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-onContentChange' class='name expandable'>onContentChange</a>( <span class='pre'>child</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>This method is called when a child item changes in some way. ...</div><div class='long'><p>This method is called when a child item changes in some way. By default this calls\n<a href=\"#!/api/Ext.AbstractComponent-method-updateLayout\" rel=\"Ext.AbstractComponent-method-updateLayout\" class=\"docClass\">Ext.AbstractComponent.updateLayout</a> on this layout's owner.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>child</span> : <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a><div class='sub-desc'><p>The child item that has changed.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if this layout has handled the content change.</p>\n</div></li></ul></div></div></div><div id='method-onDestroy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-onDestroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-onDestroy' class='name expandable'>onDestroy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-onRemove' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-onRemove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-onRemove' class='name expandable'>onRemove</a>( <span class='pre'>comp</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>comp</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.layout.Layout-method-onRemove' rel='Ext.layout.Layout-method-onRemove' class='docClass'>Ext.layout.Layout.onRemove</a></p></div></div></div><div id='method-prune' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.ElementContainer' rel='Ext.util.ElementContainer' class='defined-in docClass'>Ext.util.ElementContainer</a><br/><a href='source/ElementContainer.html#Ext-util-ElementContainer-method-prune' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.ElementContainer-method-prune' class='name expandable'>prune</a>( <span class='pre'>childEls, shared</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>childEls</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>shared</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-publishInnerCtSize' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-publishInnerCtSize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-publishInnerCtSize' class='name expandable'>publishInnerCtSize</a>( <span class='pre'>ownerContext, reservedSpace</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>ownerContext</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>reservedSpace</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-removeChildEls' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.ElementContainer' rel='Ext.util.ElementContainer' class='defined-in docClass'>Ext.util.ElementContainer</a><br/><a href='source/ElementContainer.html#Ext-util-ElementContainer-method-removeChildEls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.ElementContainer-method-removeChildEls' class='name expandable'>removeChildEls</a>( <span class='pre'>testFn</span> )</div><div class='description'><div class='short'>Removes items in the childEls array based on the return value of a supplied test\nfunction. ...</div><div class='long'><p>Removes items in the childEls array based on the return value of a supplied test\nfunction. The function is called with a entry in childEls and if the test function\nreturn true, that entry is removed. If false, that entry is kept.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>testFn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The test function.</p>\n</div></li></ul></div></div></div><div id='method-renderChildren' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-renderChildren' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-renderChildren' class='name expandable'>renderChildren</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.layout.Layout-method-renderChildren' rel='Ext.layout.Layout-method-renderChildren' class='docClass'>Ext.layout.Layout.renderChildren</a></p></div></div></div><div id='method-renderItem' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-renderItem' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-renderItem' class='name expandable'>renderItem</a>( <span class='pre'>item, target, position</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Renders the given Component into the target Element. ...</div><div class='long'><p>Renders the given Component into the target Element.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a><div class='sub-desc'><p>The Component to render</p>\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a><div class='sub-desc'><p>The target Element</p>\n</div></li><li><span class='pre'>position</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The position within the target to render the item to</p>\n</div></li></ul></div></div></div><div id='method-renderItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-renderItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-renderItems' class='name expandable'>renderItems</a>( <span class='pre'>items, target</span> )<strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Iterates over all passed items, ensuring they are rendered. ...</div><div class='long'><p>Iterates over all passed items, ensuring they are rendered.  If the items are already rendered,\nalso determines if the items are in the proper place in the dom.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>items</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-roundFlex' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Box' rel='Ext.layout.container.Box' class='defined-in docClass'>Ext.layout.container.Box</a><br/><a href='source/Box.html#Ext-layout-container-Box-method-roundFlex' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Box-method-roundFlex' class='name expandable'>roundFlex</a>( <span class='pre'>width</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>width</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setOwner' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-setOwner' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-setOwner' class='name expandable'>setOwner</a>( <span class='pre'>owner</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Sets the layout owner ...</div><div class='long'><p>Sets the layout owner</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>owner</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setupRenderTpl' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.container.Container' rel='Ext.layout.container.Container' class='defined-in docClass'>Ext.layout.container.Container</a><br/><a href='source/Container3.html#Ext-layout-container-Container-method-setupRenderTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.container.Container-method-setupRenderTpl' class='name expandable'>setupRenderTpl</a>( <span class='pre'>renderTpl</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>renderTpl</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-sortWeightedItems' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-sortWeightedItems' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-sortWeightedItems' class='name expandable'>sortWeightedItems</a>( <span class='pre'>items, reverseProp</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>items</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>reverseProp</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-undoLayout' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.layout.Layout' rel='Ext.layout.Layout' class='defined-in docClass'>Ext.layout.Layout</a><br/><a href='source/Layout.html#Ext-layout-Layout-method-undoLayout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.Layout-method-undoLayout' class='name expandable'>undoLayout</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-create' class='name expandable'>create</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create a new instance of this Class. ...</div><div class='long'><p>Create a new instance of this Class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.create({\n    someConfig: true\n});\n</code></pre>\n\n<p>All parameters are passed to the constructor of the class.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the created instance.</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div></div></div>","superclasses":["Ext.Base","Ext.layout.Layout","Ext.layout.container.Container","Ext.layout.container.Box"],"meta":{},"code_type":"ext_define","requires":[],"html_meta":{},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"HBox.html#Ext-layout-container-HBox","filename":"HBox.js"}],"linenr":1,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"_percentageRe","id":"property-_percentageRe"},{"tagname":"property","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"alignRoundingMethod","id":"property-alignRoundingMethod"},{"tagname":"property","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"animatePolicy","id":"property-animatePolicy"},{"tagname":"property","owner":"Ext.layout.Layout","meta":{"private":true},"name":"autoSizePolicy","id":"property-autoSizePolicy"},{"tagname":"property","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"availableSpaceOffset","id":"property-availableSpaceOffset"},{"tagname":"property","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"calculateChildBox","id":"property-calculateChildBox"},{"tagname":"property","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"calculateChildBoxes","id":"property-calculateChildBoxes"},{"tagname":"property","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"childEls","id":"property-childEls"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"createsInnerCt","id":"property-createsInnerCt"},{"tagname":"property","owner":"Ext.layout.container.HBox","meta":{"private":true},"name":"direction","id":"property-direction"},{"tagname":"property","owner":"Ext.layout.Layout","meta":{},"name":"done","id":"property-done"},{"tagname":"property","owner":"Ext.layout.container.HBox","meta":{"private":true},"name":"horizontal","id":"property-horizontal"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.layout.Layout","meta":{"private":true},"name":"initialized","id":"property-initialized"},{"tagname":"property","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"innerCls","id":"property-innerCls"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.layout.Layout","meta":{"readonly":true},"name":"isLayout","id":"property-isLayout"},{"tagname":"property","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"manageMargins","id":"property-manageMargins"},{"tagname":"property","owner":"Ext.layout.container.HBox","meta":{"private":true},"name":"names","id":"property-names"},{"tagname":"property","owner":"Ext.layout.container.Container","meta":{},"name":"overflowPadderEl","id":"property-overflowPadderEl"},{"tagname":"property","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"renderTpl","id":"property-renderTpl"},{"tagname":"property","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"reserveOffset","id":"property-reserveOffset"},{"tagname":"property","owner":"Ext.layout.Layout","meta":{"private":true},"name":"running","id":"property-running"},{"tagname":"property","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"scrollOffset","id":"property-scrollOffset"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"},{"tagname":"property","owner":"Ext.layout.container.HBox","meta":{"private":true},"name":"sizePolicy","id":"property-sizePolicy"},{"tagname":"property","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"targetCls","id":"property-targetCls"},{"tagname":"property","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"targetElCls","id":"property-targetElCls"},{"tagname":"property","owner":"Ext.layout.container.HBox","meta":{"private":true},"name":"type","id":"property-type"},{"tagname":"property","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"updateChildBoxes","id":"property-updateChildBoxes"},{"tagname":"property","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"usesContainerHeight","id":"property-usesContainerHeight"},{"tagname":"property","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"usesContainerWidth","id":"property-usesContainerWidth"},{"tagname":"property","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"usesHeight","id":"property-usesHeight"},{"tagname":"property","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"usesWidth","id":"property-usesWidth"}],"cfg":[{"tagname":"cfg","owner":"Ext.layout.container.HBox","meta":{},"name":"align","id":"cfg-align"},{"tagname":"cfg","owner":"Ext.layout.container.HBox","meta":{},"name":"alignRoundingMethod","id":"cfg-alignRoundingMethod"},{"tagname":"cfg","owner":"Ext.layout.container.HBox","meta":{},"name":"constrainAlign","id":"cfg-constrainAlign"},{"tagname":"cfg","owner":"Ext.layout.container.Box","meta":{},"name":"defaultMargins","id":"cfg-defaultMargins"},{"tagname":"cfg","owner":"Ext.layout.container.Box","meta":{},"name":"flex","id":"cfg-flex"},{"tagname":"cfg","owner":"Ext.layout.container.Container","meta":{},"name":"itemCls","id":"cfg-itemCls"},{"tagname":"cfg","owner":"Ext.layout.container.Box","meta":{},"name":"itemCls","id":"cfg-itemCls"},{"tagname":"cfg","owner":"Ext.layout.container.Box","meta":{},"name":"pack","id":"cfg-pack"},{"tagname":"cfg","owner":"Ext.layout.container.Box","meta":{},"name":"padding","id":"cfg-padding"},{"tagname":"cfg","owner":"Ext.layout.container.Box","meta":{},"name":"stretchMaxPartner","id":"cfg-stretchMaxPartner"}],"css_var":[],"method":[{"tagname":"method","owner":"Ext.layout.container.Box","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"Ext.util.ElementContainer","meta":{},"name":"addChildEls","id":"method-addChildEls"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"protected":true},"name":"afterRemove","id":"method-afterRemove"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"afterRenderItem","id":"method-afterRenderItem"},{"tagname":"method","owner":"Ext.util.ElementContainer","meta":{"private":true},"name":"applyChildEls","id":"method-applyChildEls"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"beginCollapse","id":"method-beginCollapse"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"beginExpand","id":"method-beginExpand"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{},"name":"beginLayout","id":"method-beginLayout"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{},"name":"beginLayoutCycle","id":"method-beginLayoutCycle"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"cacheChildItems","id":"method-cacheChildItems"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"cacheElements","id":"method-cacheElements"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"protected":true},"name":"cacheFlexes","id":"method-cacheFlexes"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"abstract":true},"name":"calculate","id":"method-calculate"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"calculateParallel","id":"method-calculateParallel"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"calculatePerpendicular","id":"method-calculatePerpendicular"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"calculateStretchMax","id":"method-calculateStretchMax"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{},"name":"completeLayout","id":"method-completeLayout"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"protected":true},"name":"configureItem","id":"method-configureItem"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"doRenderBody","id":"method-doRenderBody"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"doRenderContainer","id":"method-doRenderContainer"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"doRenderItems","id":"method-doRenderItems"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{},"name":"finalizeLayout","id":"method-finalizeLayout"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"finishRender","id":"method-finishRender"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"finishRenderItems","id":"method-finishRenderItems"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{},"name":"finishedLayout","id":"method-finishedLayout"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"flexSort","id":"method-flexSort"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"getAnimatePolicy","id":"method-getAnimatePolicy"},{"tagname":"method","owner":"Ext.util.ElementContainer","meta":{"private":true},"name":"getChildEls","id":"method-getChildEls"},{"tagname":"method","owner":"Ext.util.ElementContainer","meta":{"private":true},"name":"getClassChildEls","id":"method-getClassChildEls"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"protected":true},"name":"getContainerSize","id":"method-getContainerSize"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"getContentTarget","id":"method-getContentTarget"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{},"name":"getElementTarget","id":"method-getElementTarget"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"protected":true},"name":"getItemSizePolicy","id":"method-getItemSizePolicy"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"getItemsRenderTree","id":"method-getItemsRenderTree"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{},"name":"getLayoutItems","id":"method-getLayoutItems"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"getPositionOffset","id":"method-getPositionOffset"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"getRenderData","id":"method-getRenderData"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{},"name":"getRenderTarget","id":"method-getRenderTarget"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"getRenderTpl","id":"method-getRenderTpl"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"getRenderTree","id":"method-getRenderTree"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"protected":true},"name":"getRenderedItems","id":"method-getRenderedItems"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"getScrollbarsNeeded","id":"method-getScrollbarsNeeded"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{},"name":"getTarget","id":"method-getTarget"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"protected":true},"name":"getVisibleItems","id":"method-getVisibleItems"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"protected":true},"name":"initLayout","id":"method-initLayout"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"initOverflowHandler","id":"method-initOverflowHandler"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"isItemBoxParent","id":"method-isItemBoxParent"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"isItemLayoutRoot","id":"method-isItemLayoutRoot"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"isItemShrinkWrap","id":"method-isItemShrinkWrap"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"isRunning","id":"method-isRunning"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"protected":true},"name":"isValidParent","id":"method-isValidParent"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"moveItem","id":"method-moveItem"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"notifyOwner","id":"method-notifyOwner"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"onAdd","id":"method-onAdd"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"onAfterConstrainInvalidateChild","id":"method-onAfterConstrainInvalidateChild"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"onAfterStretchMaxInvalidateChild","id":"method-onAfterStretchMaxInvalidateChild"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"onBeforeConstrainInvalidateChild","id":"method-onBeforeConstrainInvalidateChild"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"onBeforeStretchMaxInvalidateChild","id":"method-onBeforeStretchMaxInvalidateChild"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{},"name":"onContentChange","id":"method-onContentChange"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"onDestroy","id":"method-onDestroy"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"onRemove","id":"method-onRemove"},{"tagname":"method","owner":"Ext.util.ElementContainer","meta":{"private":true},"name":"prune","id":"method-prune"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"publishInnerCtSize","id":"method-publishInnerCtSize"},{"tagname":"method","owner":"Ext.util.ElementContainer","meta":{},"name":"removeChildEls","id":"method-removeChildEls"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"renderChildren","id":"method-renderChildren"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"renderItem","id":"method-renderItem"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"protected":true},"name":"renderItems","id":"method-renderItems"},{"tagname":"method","owner":"Ext.layout.container.Box","meta":{"private":true},"name":"roundFlex","id":"method-roundFlex"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"setOwner","id":"method-setOwner"},{"tagname":"method","owner":"Ext.layout.container.Container","meta":{"private":true},"name":"setupRenderTpl","id":"method-setupRenderTpl"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"sortWeightedItems","id":"method-sortWeightedItems"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"},{"tagname":"method","owner":"Ext.layout.Layout","meta":{"private":true},"name":"undoLayout","id":"method-undoLayout"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.layout.container.HBox","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.layout.container.HBox","mixins":[],"mixedInto":[]});