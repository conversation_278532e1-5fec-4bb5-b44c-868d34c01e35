Ext.data.JsonP.Ext_dd_DragDropManager_ElementWrapper({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/DragDropManager.html#Ext-dd-DragDropManager-ElementWrapper' target='_blank'>DragDropManager.js</a></div></pre><div class='doc-contents'><p class='private'><strong>NOTE</strong> This is a private utility class for internal use by the framework. Don't rely on its existence.</p><p>Deprecated inner class for cached elements.</p>\n        <div class='signature-box deprecated'>\n        <p>This class has been <strong>deprecated</strong> </p>\n        <p>This wrapper isn't that useful</p>\n\n        </div>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-css' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager.ElementWrapper'>Ext.dd.DragDropManager.ElementWrapper</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-ElementWrapper-property-css' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager.ElementWrapper-property-css' class='name not-expandable'>css</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'><p>A reference to the style property</p>\n</div><div class='long'><p>A reference to the style property</p>\n</div></div></div><div id='property-el' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager.ElementWrapper'>Ext.dd.DragDropManager.ElementWrapper</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-ElementWrapper-property-el' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager.ElementWrapper-property-el' class='name not-expandable'>el</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'><p>The element</p>\n</div><div class='long'><p>The element</p>\n</div></div></div><div id='property-id' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dd.DragDropManager.ElementWrapper'>Ext.dd.DragDropManager.ElementWrapper</span><br/><a href='source/DragDropManager.html#Ext-dd-DragDropManager-ElementWrapper-property-id' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dd.DragDropManager.ElementWrapper-property-id' class='name not-expandable'>id</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'><p>The element id</p>\n</div><div class='long'><p>The element id</p>\n</div></div></div></div></div></div></div>","superclasses":[],"meta":{"private":true,"deprecated":{"text":"This wrapper isn't that useful"}},"requires":[],"html_meta":{"private":null,"deprecated":"        <div class='signature-box deprecated'>\n        <p>This class has been <strong>deprecated</strong> </p>\n        <p>This wrapper isn't that useful</p>\n\n        </div>\n"},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"DragDropManager.html#Ext-dd-DragDropManager-ElementWrapper","filename":"DragDropManager.js"}],"linenr":1159,"members":{"property":[{"tagname":"property","owner":"Ext.dd.DragDropManager.ElementWrapper","meta":{},"name":"css","id":"property-css"},{"tagname":"property","owner":"Ext.dd.DragDropManager.ElementWrapper","meta":{},"name":"el","id":"property-el"},{"tagname":"property","owner":"Ext.dd.DragDropManager.ElementWrapper","meta":{},"name":"id","id":"property-id"}],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"inheritable":null,"private":true,"component":false,"name":"Ext.dd.DragDropManager.ElementWrapper","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.dd.DragDropManager.ElementWrapper","mixins":[],"mixedInto":[]});