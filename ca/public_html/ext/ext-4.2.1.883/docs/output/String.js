Ext.data.JsonP.String({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/String.html#String' target='_blank'>String.js</a></div></pre><div class='doc-contents'><p><code>String</code> is a global object that may be used to construct String instances.</p>\n\n<p>String objects may be created by calling the constructor <code>new String()</code>. The <code>String</code> object wraps\nJavaScript's string primitive data type with the methods described below. The global function\n<code>String()</code> can also be called without new in front to create a primitive string. String literals in\nJavaScript are primitive strings.</p>\n\n<p>Because JavaScript automatically converts between string primitives and String objects, you can call\nany of the methods of the <code>String</code> object on a string primitive. JavaScript automatically converts the\nstring primitive to a temporary <code>String</code> object, calls the method, then discards the temporary String\nobject. For example, you can use the <code><a href=\"#!/api/String-property-length\" rel=\"String-property-length\" class=\"docClass\">String.length</a></code> property on a string primitive created from a\nstring literal:</p>\n\n<pre><code>s_obj = new String(s_prim = s_also_prim = \"foo\");\n\ns_obj.length;       // 3\ns_prim.length;      // 3\ns_also_prim.length; // 3\n'foo'.length;       // 3\n\"foo\".length;       // 3\n</code></pre>\n\n<p>(A string literal is denoted with single or double quotation marks.)</p>\n\n<p>String objects can be converted to primitive strings with the <code>valueOf</code> method.</p>\n\n<p>String primitives and String objects give different results when evaluated as JavaScript. Primitives\nare treated as source code; String objects are treated as a character sequence object. For example:</p>\n\n<pre><code>s1 = \"2 + 2\";               // creates a string primitive\ns2 = new String(\"2 + 2\");   // creates a String object\neval(s1);                   // returns the number 4\neval(s2);                   // returns the string \"2 + 2\"\neval(s2.valueOf());         // returns the number 4\n</code></pre>\n\n<h1>Character access</h1>\n\n<p>There are two ways to access an individual character in a string. The first is the <code>charAt</code> method:</p>\n\n<pre><code>return 'cat'.charAt(1); // returns \"a\"\n</code></pre>\n\n<p>The other way is to treat the string as an array, where each index corresponds to an individual\ncharacter:</p>\n\n<pre><code>return 'cat'[1]; // returns \"a\"\n</code></pre>\n\n<p>The second way (treating the string as an array) is not part of ECMAScript 3. It is a JavaScript and\nECMAScript 5 feature.</p>\n\n<p>In both cases, attempting to set an individual character won't work. Trying to set a character\nthrough <code>charAt</code> results in an error, while trying to set a character via indexing does not throw an\nerror, but the string itself is unchanged.</p>\n\n<h1>Comparing strings</h1>\n\n<p>C developers have the <code>strcmp()</code> function for comparing strings. In JavaScript, you just use the less-\nthan and greater-than operators:</p>\n\n<pre><code>var a = \"a\";\nvar b = \"b\";\nif (a &lt; b) // true\n    print(a + \" is less than \" + b);\nelse if (a &gt; b)\n    print(a + \" is greater than \" + b);\nelse\n    print(a + \" and \" + b + \" are equal.\");\n</code></pre>\n\n<p>A similar result can be achieved using the <code>localeCompare</code> method inherited by <code>String</code> instances.</p>\n\n<div class=\"notice\">\nDocumentation for this class comes from <a href=\"https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/String\">MDN</a>\nand is available under <a href=\"http://creativecommons.org/licenses/by-sa/2.0/\">Creative Commons: Attribution-Sharealike license</a>.\n</div>\n\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-length' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-property-length' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-property-length' class='name expandable'>length</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Reflects the length of the string. ...</div><div class='long'><p>Reflects the length of the string.</p>\n\n<p>This property returns the number of code units in the string. UTF-16, the string format used by JavaScript, uses a single 16-bit\ncode unit to represent the most common characters, but needs to use two code units for less commonly-used characters, so it's\npossible for the value returned by <code>length</code> to not match the actual number of characters in the string.</p>\n\n<p>For an empty string, <code>length</code> is 0.</p>\n\n<pre><code>var x = \"Netscape\";\nvar empty = \"\";\n\nconsole.log(\"Netspace is \" + x.length + \" code units long\");\nconsole.log(\"The empty string is has a length of \" + empty.length); // should be 0\n</code></pre>\n</div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/String-method-constructor' class='name expandable'>String</a>( <span class='pre'>value</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Creates new String object. ...</div><div class='long'><p>Creates new String object.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to wrap into String object.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-charAt' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-charAt' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-charAt' class='name expandable'>charAt</a>( <span class='pre'>index</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns the character at the specified index. ...</div><div class='long'><p>Returns the character at the specified index.</p>\n\n<p>Characters in a string are indexed from left to right. The index of the first character is 0, and\nthe index of the last character in a string called <code>stringName</code> is <code>stringName.length - 1</code>. If the\nindex you supply is out of range, JavaScript returns an empty string.</p>\n\n<p>The following example displays characters at different locations in the string \"Brave new world\":</p>\n\n<pre><code>var anyString=\"Brave new world\";\n\ndocument.writeln(\"The character at index 0 is '\" + anyString.charAt(0) + \"'\");\ndocument.writeln(\"The character at index 1 is '\" + anyString.charAt(1) + \"'\");\ndocument.writeln(\"The character at index 2 is '\" + anyString.charAt(2) + \"'\");\ndocument.writeln(\"The character at index 3 is '\" + anyString.charAt(3) + \"'\");\ndocument.writeln(\"The character at index 4 is '\" + anyString.charAt(4) + \"'\");\ndocument.writeln(\"The character at index 999 is '\" + anyString.charAt(999) + \"'\");\n</code></pre>\n\n<p>These lines display the following:</p>\n\n<pre><code>The character at index 0 is 'B'\nThe character at index 1 is 'r'\nThe character at index 2 is 'a'\nThe character at index 3 is 'v'\nThe character at index 4 is 'e'\nThe character at index 999 is ''\n</code></pre>\n\n<p>The following provides a means of ensuring that going through a string loop always provides a whole\ncharacter, even if the string contains characters that are not in the Basic Multi-lingual Plane.</p>\n\n<pre><code>var str = 'A\\uD87E\\uDC04Z'; // We could also use a non-BMP character directly\nfor (var i=0, chr; i &lt; str.length; i++) {\n    if ((chr = getWholeChar(str, i)) === false) {continue;} // Adapt this line at the top of\n</code></pre>\n\n<p>each loop, passing in the whole string and the current iteration and returning a variable to\nrepresent the individual character</p>\n\n<pre><code>    alert(chr);\n}\n\nfunction getWholeChar (str, i) {\n    var code = str.charCodeAt(i);\n\n    if (isNaN(code)) {\n    return ''; // Position not found\n    }\n    if (code &lt; 0xD800 || code &gt; 0xDFFF) {\n        return str.charAt(i);\n    }\n    if (0xD800 &lt;= code &amp;&amp; code &lt;= 0xDBFF) { // High surrogate (could change last hex to 0xDB7F\n</code></pre>\n\n<p>to treat high private surrogates as single characters)</p>\n\n<pre><code>    if (str.length &lt;= (i+1))  {\n        throw 'High surrogate without following low surrogate';\n    }\n    var next = str.charCodeAt(i+1);\n    if (0xDC00 &gt; next || next &gt; 0xDFFF) {\n        throw 'High surrogate without following low surrogate';\n    }\n    return str.charAt(i)+str.charAt(i+1);\n}\n// Low surrogate (0xDC00 &lt;= code &amp;&amp; code &lt;= 0xDFFF)\nif (i === 0) {\n    throw 'Low surrogate without preceding high surrogate';\n}\nvar prev = str.charCodeAt(i-1);\nif (0xD800 &gt; prev || prev &gt; 0xDBFF) { // (could change last hex to 0xDB7F to treat high private\n</code></pre>\n\n<p>surrogates as single characters)</p>\n\n<pre><code>  throw 'Low surrogate without preceding high surrogate';\n}\nreturn false; // We can pass over low surrogates now as the second component in a pair which we\n</code></pre>\n\n<p>have already processed\n}</p>\n\n<p>While the second example may be more frequently useful for those wishing to support non-BMP\ncharacters (since the above does not require the caller to know where any non-BMP character might\nappear), in the event that one <em>does</em> wish, in choosing a character by index, to treat the surrogate\npairs within a string as the single characters they represent, one can use the following:</p>\n\n<pre><code>function fixedCharAt (str, idx) {\n    var ret = '';\n    str += '';\n    var end = str.length;\n\n    var surrogatePairs = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g;\n    while ((surrogatePairs.exec(str)) != null) {\n        var li = surrogatePairs.lastIndex;\n        if (li - 2 &lt; idx) {\n            idx++;\n        }\n        else {\n        break;\n    }\n}\n\nif (idx &gt;= end || idx &lt; 0) {\n    return '';\n}\n\nret += str.charAt(idx);\n\nif (/[\\uD800-\\uDBFF]/.test(ret) &amp;&amp; /[\\uDC00-\\uDFFF]/.test(str.charAt(idx+1))) {\n    ret += str.charAt(idx+1); // Go one further, since one of the \"characters\" is part of a\n</code></pre>\n\n<p>surrogate pair</p>\n\n<pre><code>}\nreturn ret;\n}\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and 1 less than the length of the string.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Individual character from string.</p>\n</div></li></ul></div></div></div><div id='method-charCodeAt' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-charCodeAt' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-charCodeAt' class='name expandable'>charCodeAt</a>( <span class='pre'>index</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns a number indicating the Unicode value of the character at the given index. ...</div><div class='long'><p>Returns a number indicating the Unicode value of the character at the given index.</p>\n\n<p>Unicode code points range from 0 to 1,114,111. The first 128 Unicode code points are a direct match\nof the ASCII character encoding.</p>\n\n<p>Note that <code>charCodeAt</code> will always return a value that is less than 65,536. This is because the\nhigher code points are represented by a pair of (lower valued) \"surrogate\" pseudo-characters which\nare used to comprise the real character. Because of this, in order to examine or reproduce the full\ncharacter for individual characters of value 65,536 and above, for such characters, it is necessary\nto retrieve not only <code>charCodeAt(i)</code>, but also <code>charCodeAt(i+1)</code> (as if examining/reproducing a\nstring with two letters). See example 2 and 3 below.</p>\n\n<p><code>charCodeAt</code> returns <code>NaN</code> if the given index is not greater than 0 or is greater than the length of\nthe string.</p>\n\n<p>Backward Compatibility with JavaScript 1.2</p>\n\n<p>The <code>charCodeAt</code> method returns a number indicating the ISO-Latin-1 codeset value of the character\nat the given index. The ISO-Latin-1 codeset ranges from 0 to 255. The first 0 to 127 are a direct\nmatch of the ASCII character set.</p>\n\n<p>Example 1: Using <code>charCodeAt</code></p>\n\n<p>The following example returns 65, the Unicode value for A.</p>\n\n<p>   \"ABC\".charCodeAt(0) // returns 65</p>\n\n<p>Example 2: Fixing <code>charCodeAt</code> to handle non-Basic-Multilingual-Plane characters if their presence\nearlier in the string is unknown</p>\n\n<p>This version might be used in for loops and the like when it is unknown whether non-BMP characters\nexist before the specified index position.</p>\n\n<pre><code>function fixedCharCodeAt (str, idx) {\n    // ex. fixedCharCodeAt ('\\uD800\\uDC00', 0); // 65536\n    // ex. fixedCharCodeAt ('\\uD800\\uDC00', 1); // 65536\n    idx = idx || 0;\n    var code = str.charCodeAt(idx);\n    var hi, low;\n    if (0xD800 &lt;= code &amp;&amp; code &lt;= 0xDBFF) { // High surrogate (could change last hex to 0xDB7F to treat high private surrogates as single characters)\n        hi = code;\n        low = str.charCodeAt(idx+1);\n        if (isNaN(low)) {\n            throw 'High surrogate not followed by low surrogate in fixedCharCodeAt()';\n        }\n        return ((hi - 0xD800) * 0x400) + (low - 0xDC00) + 0x10000;\n    }\n    if (0xDC00 &lt;= code &amp;&amp; code &lt;= 0xDFFF) { // Low surrogate\n    // We return false to allow loops to skip this iteration since should have already handled\n</code></pre>\n\n<p>high surrogate above in the previous iteration</p>\n\n<pre><code>        return false;\n    }\n    return code;\n}\n</code></pre>\n\n<p>Example 3: Fixing <code>charCodeAt</code> to handle non-Basic-Multilingual-Plane characters if their presence\nearlier in the string is known</p>\n\n<pre><code>function knownCharCodeAt (str, idx) {\n    str += '';\n    var code,\n    end = str.length;\n\n    var surrogatePairs = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g;\n    while ((surrogatePairs.exec(str)) != null) {\n        var li = surrogatePairs.lastIndex;\n        if (li - 2 &lt; idx) {\n            idx++;\n        }\n        else {\n            break;\n        }\n    }\n\n    if (idx &gt;= end || idx &lt; 0) {\n        return NaN;\n    }\n\n    code = str.charCodeAt(idx);\n\n    var hi, low;\n    if (0xD800 &lt;= code &amp;&amp; code &lt;= 0xDBFF) {\n        hi = code;\n        low = str.charCodeAt(idx+1); // Go one further, since one of the \"characters\" is part of\n</code></pre>\n\n<p>a surrogate pair</p>\n\n<pre><code>        return ((hi - 0xD800) * 0x400) + (low - 0xDC00) + 0x10000;\n    }\n    return code;\n}\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer greater than 0 and less than the length of the string; if it is\nnot a number, it defaults to 0.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Value between 0 and 65535.</p>\n</div></li></ul></div></div></div><div id='method-concat' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-concat' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-concat' class='name expandable'>concat</a>( <span class='pre'>string2</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Combines the text of two strings and returns a new string. ...</div><div class='long'><p>Combines the text of two strings and returns a new string.</p>\n\n<p><code>concat</code> combines the text from one or more strings and returns a new string. Changes to the text in\none string do not affect the other string.</p>\n\n<p>The following example combines strings into a new string.</p>\n\n<pre><code>var hello = \"Hello, \";\nconsole.log(hello.concat(\"Kevin\", \" have a nice day.\")); // Hello, Kevin have a nice day.\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>string2</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>...stringN</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Result of both strings.</p>\n</div></li></ul></div></div></div><div id='method-fromCharCode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-fromCharCode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-fromCharCode' class='name expandable'>fromCharCode</a>( <span class='pre'>num1</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns a string created by using the specified sequence of Unicode values. ...</div><div class='long'><p>Returns a string created by using the specified sequence of Unicode values.</p>\n\n<p>This method returns a string and not a <code>String</code> object.</p>\n\n<p>Because <code>fromCharCode</code> is a static method of <code>String</code>, you always use it as <code><a href=\"#!/api/String-method-fromCharCode\" rel=\"String-method-fromCharCode\" class=\"docClass\">String.fromCharCode</a>()</code>,\nrather than as a method of a <code>String</code> object you created.</p>\n\n<p>Although most common Unicode values can be represented in a fixed width system/with one number (as\nexpected early on during JavaScript standardization) and <code>fromCharCode()</code> can be used to return a\nsingle character for the most common values (i.e., UCS-2 values which are the subset of UTF-16 with\nthe most common characters), in order to deal with ALL legal Unicode values, <code>fromCharCode()</code> alone\nis inadequate. Since the higher code point characters use two (lower value) \"surrogate\" numbers to\nform a single character, <code>fromCharCode()</code> can be used to return such a pair and thus adequately\nrepresent these higher valued characters.</p>\n\n<p>Be aware, therefore, that the following utility function to grab the accurate character even for\nhigher value code points, may be returning a value which is rendered as a single character, but\nwhich has a string count of two (though usually the count will be one).</p>\n\n<pre><code>// <a href=\"#!/api/String-method-fromCharCode\" rel=\"String-method-fromCharCode\" class=\"docClass\">String.fromCharCode</a>() alone cannot get the character at such a high code point\n// The following, on the other hand, can return a 4-byte character as well as the\n//   usual 2-byte ones (i.e., it can return a single character which actually has\n//   a string length of 2 instead of 1!)\nalert(fixedFromCharCode(0x2F804)); // or 194564 in decimal\n\nfunction fixedFromCharCode (codePt) {\n    if (codePt &gt; 0xFFFF) {\n        codePt -= 0x10000;\n        return <a href=\"#!/api/String-method-fromCharCode\" rel=\"String-method-fromCharCode\" class=\"docClass\">String.fromCharCode</a>(0xD800 + (codePt &gt;&gt; 10), 0xDC00 +\n        (codePt &amp; 0x3FF));\n    }\n    else {\n        return <a href=\"#!/api/String-method-fromCharCode\" rel=\"String-method-fromCharCode\" class=\"docClass\">String.fromCharCode</a>(codePt);\n    }\n}\n</code></pre>\n\n<p>The following example returns the string \"ABC\".</p>\n\n<pre><code><a href=\"#!/api/String-method-fromCharCode\" rel=\"String-method-fromCharCode\" class=\"docClass\">String.fromCharCode</a>(65,66,67)\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>num1</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>, ..., numN A sequence of numbers that are Unicode values.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>String containing characters from encoding.</p>\n</div></li></ul></div></div></div><div id='method-indexOf' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-indexOf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-indexOf' class='name expandable'>indexOf</a>( <span class='pre'>searchValue, fromIndex</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the index within the calling String object of the first occurrence of the specified value,\nor -1 if not found. ...</div><div class='long'><p>Returns the index within the calling <code>String</code> object of the first occurrence of the specified value,\nor -1 if not found.</p>\n\n<p>Characters in a string are indexed from left to right. The index of the first character is 0, and the index of the last character\nof a string called <code>stringName</code> is <code>stringName.length - 1</code>.</p>\n\n<pre><code>\"Blue Whale\".indexOf(\"Blue\")    // returns 0\n\"Blue Whale\".indexOf(\"Blute\")   // returns -1\n\"Blue Whale\".indexOf(\"Whale\",0) // returns 5\n\"Blue Whale\".indexOf(\"Whale\",5) // returns 5\n\"Blue Whale\".indexOf(\"\",9)      // returns 9\n\"Blue Whale\".indexOf(\"\",10)     // returns 10\n\"Blue Whale\".indexOf(\"\",11)     // returns 10\n</code></pre>\n\n<p>The <code>indexOf</code> method is case sensitive. For example, the following expression returns -1:</p>\n\n<pre><code>\"Blue Whale\".indexOf(\"blue\")\n</code></pre>\n\n<p>Note that '0' doesn't evaluate to true and '-1' doesn't evaluate to false. Therefore, when checking if a specific string exists\nwithin another string the correct way to check would be:</p>\n\n<pre><code>\"Blue Whale\".indexOf(\"Blue\") != -1 // true\n\"Blue Whale\".indexOf(\"Bloe\") != -1 // false\n</code></pre>\n\n<p>The following example uses indexOf and lastIndexOf to locate values in the string \"Brave new world\".</p>\n\n<pre><code>var anyString=\"Brave new world\"\n\ndocument.write(\"&lt;P&gt;The index of the first w from the beginning is \" + anyString.indexOf(\"w\"))          // Displays 8\ndocument.write(\"&lt;P&gt;The index of the first w from the end is \" + anyString.lastIndexOf(\"w\"))      // Displays 10\ndocument.write(\"&lt;P&gt;The index of 'new' from the beginning is \" + anyString.indexOf(\"new\"))        // Displays 6\ndocument.write(\"&lt;P&gt;The index of 'new' from the end is \" + anyString.lastIndexOf(\"new\"))    // Displays 6\n</code></pre>\n\n<p>The following example defines two string variables. The variables contain the same string except that the second string contains\nuppercase letters. The first <code>writeln</code> method displays 19. But because the <code>indexOf</code> method is case sensitive, the string\n\"cheddar\" is not found in <code>myCapString</code>, so the second <code>writeln</code> method displays -1.</p>\n\n<pre><code>myString=\"brie, pepper jack, cheddar\"\nmyCapString=\"Brie, Pepper Jack, Cheddar\"\ndocument.writeln('myString.indexOf(\"cheddar\") is ' + myString.indexOf(\"cheddar\"))\ndocument.writeln('&lt;P&gt;myCapString.indexOf(\"cheddar\") is ' + myCapString.indexOf(\"cheddar\"))\n</code></pre>\n\n<p>The following example sets count to the number of occurrences of the letter x in the string str:</p>\n\n<pre><code>count = 0;\npos = str.indexOf(\"x\");\nwhile ( pos != -1 ) {\n    count++;\n    pos = str.indexOf(\"x\",pos+1);\n}\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>searchValue</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>A string representing the value to search for.</p>\n</div></li><li><span class='pre'>fromIndex</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The location within the calling string to start the search from. It can be any integer between 0 and\nthe length of the string. The default value is 0.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Position of specified value or -1 if not found.</p>\n</div></li></ul></div></div></div><div id='method-lastIndexOf' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-lastIndexOf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-lastIndexOf' class='name expandable'>lastIndexOf</a>( <span class='pre'>searchValue, fromIndex</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the index within the calling String object of the last occurrence of\nthe specified value, or -1 if not found. ...</div><div class='long'><p>Returns the index within the calling String object of the last occurrence of\nthe specified value, or -1 if not found. The calling string is searched\nbackward, starting at fromIndex.</p>\n\n<p>Characters in a string are indexed from left to right. The index of the first character is 0, and the index of the last character\nis <code>stringName.length - 1</code>.</p>\n\n<pre><code>\"canal\".lastIndexOf(\"a\")   // returns 3\n\"canal\".lastIndexOf(\"a\",2) // returns 1\n\"canal\".lastIndexOf(\"a\",0) // returns -1\n\"canal\".lastIndexOf(\"x\")   // returns -1\n</code></pre>\n\n<p>The <code>lastIndexOf</code> method is case sensitive. For example, the following expression returns -1:</p>\n\n<pre><code>\"Blue Whale, Killer Whale\".lastIndexOf(\"blue\")\n</code></pre>\n\n<p>The following example uses <code>indexOf</code> and <code>lastIndexOf</code> to locate values in the string \"<code>Brave new world</code>\".</p>\n\n<pre><code>var anyString=\"Brave new world\"\n\n// Displays 8\ndocument.write(\"&lt;P&gt;The index of the first w from the beginning is \" +\nanyString.indexOf(\"w\"))\n// Displays 10\ndocument.write(\"&lt;P&gt;The index of the first w from the end is \" +\nanyString.lastIndexOf(\"w\"))\n// Displays 6\ndocument.write(\"&lt;P&gt;The index of 'new' from the beginning is \" +\nanyString.indexOf(\"new\"))\n// Displays 6\ndocument.write(\"&lt;P&gt;The index of 'new' from the end is \" +\nanyString.lastIndexOf(\"new\"))\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>searchValue</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>A string representing the value to search for.</p>\n</div></li><li><span class='pre'>fromIndex</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The location within the calling string to start the search from, indexed from left to right. It can\nbe any integer between 0 and the length of the string. The default value is the length of the string.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-localeCompare' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-localeCompare' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-localeCompare' class='name expandable'>localeCompare</a>( <span class='pre'>compareString</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns a number indicating whether a reference string comes before or after or is the same as the\ngiven string in so...</div><div class='long'><p>Returns a number indicating whether a reference string comes before or after or is the same as the\ngiven string in sort order.</p>\n\n<p>Returns a number indicating whether a reference string comes before or after or is the same as the\ngiven string in sort order. Returns -1 if the string occurs earlier in a sort than <code>compareString</code>,\nreturns 1 if the string occurs afterwards in such a sort, and returns 0 if they occur at the same\nlevel.</p>\n\n<p>The following example demonstrates the different potential results for a string occurring before,\nafter, or at the same level as another:</p>\n\n<pre><code>alert('a'.localeCompare('b')); // -1\nalert('b'.localeCompare('a')); // 1\nalert('b'.localeCompare('b')); // 0\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>compareString</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The string against which the referring string is comparing.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Returns -1 if the string occurs earlier in a sort than\ncompareString, returns 1 if the string occurs afterwards in such a sort, and\nreturns 0 if they occur at the same level.</p>\n</div></li></ul></div></div></div><div id='method-match' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-match' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-match' class='name expandable'>match</a>( <span class='pre'>regexp</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Used to match a regular expression against a string. ...</div><div class='long'><p>Used to match a regular expression against a string.</p>\n\n<p>If the regular expression does not include the <code>g</code> flag, returns the same result as <code>regexp.exec(string)</code>.</p>\n\n<p>If the regular expression includes the <code>g</code> flag, the method returns an Array containing all matches. If there were no matches,\nthe method returns <code>null</code>.</p>\n\n<p>The returned <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> has an extra <code>input</code> property, which contains the regexp that generated it as a result. In addition,\nit has an <code>index</code> property, which represents the zero-based index of the match in the string.</p>\n\n<p>In the following example, <code>match</code> is used to find \"Chapter\" followed by 1 or more numeric characters followed by a decimal point\nand numeric character 0 or more times. The regular expression includes the <code>i</code> flag so that case will be ignored.</p>\n\n<pre><code>str = \"For more information, see Chapter 3.4.5.1\";\nre = /(chapter \\d+(\\.\\d)*)/i;\nfound = str.match(re);\ndocument.write(found);\n</code></pre>\n\n<p>This returns the array containing Chapter 3.4.5.1,Chapter 3.4.5.1,.1</p>\n\n<p>\"<code>Chapter 3.4.5.1</code>\" is the first match and the first value remembered from <code>(Chapter \\d+(\\.\\d)*)</code>.</p>\n\n<p>\"<code>.1</code>\" is the second value remembered from <code>(\\.\\d)</code>.</p>\n\n<p>The following example demonstrates the use of the global and ignore case flags with <code>match</code>. All letters A through E and a\nthrough e are returned, each its own element in the array</p>\n\n<pre><code>var str = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\";\nvar regexp = /[A-E]/gi;\nvar matches_array = str.match(regexp);\ndocument.write(matches_array);\n</code></pre>\n\n<p><code>matches_array</code> now equals <code>['A', 'B', 'C', 'D', 'E', 'a', 'b', 'c', 'd', 'e']</code>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>regexp</span> : <a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a><div class='sub-desc'><p>A <a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a> object. If a non-RegExp object <code>obj</code> is passed, it is\nimplicitly converted to a RegExp by using <code>new RegExp(obj)</code>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>Contains results of the match (if any).</p>\n</div></li></ul></div></div></div><div id='method-replace' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-replace' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-replace' class='name expandable'>replace</a>( <span class='pre'>pattern, replacement</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Used to find a match between a regular expression and a string, and to replace the matched substring\nwith a new subst...</div><div class='long'><p>Used to find a match between a regular expression and a string, and to replace the matched substring\nwith a new substring.</p>\n\n<p>This method does not change the <code>String</code> object it is called on. It simply returns a new string.</p>\n\n<p>To perform a global search and replace, either include the <code>g</code> flag in the regular expression or if\nthe first parameter is a string, include <code>g</code> in the flags parameter.</p>\n\n<p>The replacement string can include the following special replacement patterns:</p>\n\n<table>\n<thead>\n<tr>\n<th></th>\n<th align=\"left\"> Pattern       </th>\n<th align=\"left\"> Inserts</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td></td>\n<td align=\"left\"> <code>$$</code>          </td>\n<td align=\"left\"> Inserts a <code>$</code>.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>$&amp;</code>          </td>\n<td align=\"left\"> Inserts the matched substring.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> `$``          </td>\n<td align=\"left\"> Inserts the portion of the string that precedes the matched substring.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>$'</code>          </td>\n<td align=\"left\"> Inserts the portion of the string that follows the matched substring.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>$n</code> or <code>$nn</code> </td>\n<td align=\"left\"> Where <code>n</code> or <code>nn</code> are decimal digits, inserts the _n_th parenthesized submatch string, provided the first</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">               </td>\n<td align=\"left\"> argument was a <code>RegExp</code> object.</td>\n</tr>\n</tbody>\n</table>\n\n\n<p>You can specify a function as the second parameter. In this case, the function will be invoked after the match has been\nperformed. The function's result (return value) will be used as the replacement string. (Note: the above-mentioned special\nreplacement patterns do not apply in this case.) Note that the function will be invoked multiple times for each full match to be\nreplaced if the regular expression in the first parameter is global.</p>\n\n<p>The arguments to the function are as follows:</p>\n\n<table>\n<thead>\n<tr>\n<th></th>\n<th align=\"left\"> Possible Name </th>\n<th align=\"left\"> Supplied Value</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td></td>\n<td align=\"left\"> <code>str</code>         </td>\n<td align=\"left\"> The matched substring. (Corresponds to <code>$&amp;</code> above.)</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>p1, p2, ...</code> </td>\n<td align=\"left\"> The _n_th parenthesized submatch string, provided the first argument to replace was a <code>RegExp</code> object.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">               </td>\n<td align=\"left\"> (Correspond to $1, $2, etc. above.)</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>offset</code>      </td>\n<td align=\"left\"> The offset of the matched substring within the total string being examined. (For example, if the total string</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">               </td>\n<td align=\"left\"> was \"<code>abcd</code>\", and the matched substring was \"<code>bc</code>\", then this argument will be 1.)</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>s</code>           </td>\n<td align=\"left\"> The total string being examined.</td>\n</tr>\n</tbody>\n</table>\n\n\n<p>(The exact number of arguments will depend on whether the first argument was a <code>RegExp</code> object and, if so, how many parenthesized\nsubmatches it specifies.)</p>\n\n<p>The following example will set <code>newString</code> to \"<code>XXzzzz - XX , zzzz</code>\":</p>\n\n<pre><code>function replacer(str, p1, p2, offset, s)\n{\n    return str + \" - \" + p1 + \" , \" + p2;\n}\nvar newString = \"XXzzzz\".replace(/(X*)(z*)/, replacer);\n</code></pre>\n\n<p>In the following example, the regular expression includes the global and ignore case flags which permits replace to replace each\noccurrence of 'apples' in the string with 'oranges'.</p>\n\n<pre><code>var re = /apples/gi;\nvar str = \"Apples are round, and apples are juicy.\";\nvar newstr = str.replace(re, \"oranges\");\nprint(newstr);\n</code></pre>\n\n<p>In this version, a string is used as the first parameter and the global and ignore case flags are specified in the flags\nparameter.</p>\n\n<pre><code>var str = \"Apples are round, and apples are juicy.\";\nvar newstr = str.replace(\"apples\", \"oranges\", \"gi\");\nprint(newstr);\n</code></pre>\n\n<p>Both of these examples print \"oranges are round, and oranges are juicy.\"</p>\n\n<p>In the following example, the regular expression is defined in replace and includes the ignore case flag.</p>\n\n<pre><code>var str = \"Twas the night before Xmas...\";\nvar newstr = str.replace(/xmas/i, \"Christmas\");\nprint(newstr);\n</code></pre>\n\n<p>This prints \"Twas the night before Christmas...\"</p>\n\n<p>The following script switches the words in the string. For the replacement text, the script uses the $1 and $2 replacement\npatterns.</p>\n\n<pre><code>var re = /(\\w+)\\s(\\w+)/;\nvar str = \"John Smith\";\nvar newstr = str.replace(re, \"$2, $1\");\nprint(newstr);\n</code></pre>\n\n<p>This prints \"Smith, John\".</p>\n\n<p>In this example, all occurrences of capital letters in the string are converted to lower case, and a hyphen is inserted just\nbefore the match location. The important thing here is that additional operations are needed on the matched item before it is\ngiven back as a replacement.</p>\n\n<p>The replacement function accepts the matched snippet as its parameter, and uses it to transform the case and concatenate the\nhyphen before returning.</p>\n\n<pre><code>function styleHyphenFormat(propertyName)\n{\n    function upperToHyphenLower(match)\n    {\n        return '-' + match.toLowerCase();\n    }\n    return propertyName.replace(/[A-Z]/, upperToHyphenLower);\n}\n</code></pre>\n\n<p>Given <code>styleHyphenFormat('borderTop')</code>, this returns 'border-top'.</p>\n\n<p>Because we want to further transform the <em>result</em> of the match before the final substitution is made, we must use a function.\nThis forces the evaluation of the match prior to the <code>toLowerCase()</code> method. If we had tried to do this using the match without a\n function, the <code>toLowerCase()</code> would have no effect.</p>\n\n<pre><code>var newString = propertyName.replace(/[A-Z]/, '-' + '$&amp;'.toLowerCase());  // won't work\n</code></pre>\n\n<p>This is because <code>'$&amp;'.toLowerCase()</code> would be evaluated first as a string literal (resulting in the same <code>'$&amp;'</code>) before using the\ncharacters as a pattern.</p>\n\n<p>The following example replaces a Fahrenheit degree with its equivalent Celsius degree. The Fahrenheit degree should be a number\nending with F. The function returns the Celsius number ending with C. For example, if the input number is 212F, the function\n returns 100C. If the number is 0F, the function returns -17.77777777777778C.</p>\n\n<p>The regular expression <code>test</code> checks for any number that ends with F. The number of Fahrenheit degree is accessible to the\nfunction through its second parameter, <code>p1</code>. The function sets the Celsius number based on the Fahrenheit degree passed in a\nstring to the <code>f2c</code> function. <code>f2c</code> then returns the Celsius number. This function approximates Perl's <code>s///e</code> flag.</p>\n\n<pre><code>function f2c(x)\n{\n    function convert(str, p1, offset, s)\n    {\n        return ((p1-32) * 5/9) + \"C\";\n    }\n    var s = String(x);\n    var test = /(\\d+(?:\\.\\d*)?)F\\b/g;\n    return s.replace(test, convert);\n}\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>pattern</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a><div class='sub-desc'><p>Either a string or regular expression pattern to search for.</p>\n</div></li><li><span class='pre'>replacement</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>Either string or function:</p>\n\n<ul>\n<li>The String to replace the <code>pattern</code> with. Number of special replacement patterns are supported;\nsee the \"Specifying a string as a parameter\" section above.</li>\n<li>A function to be invoked to create the replacement.\nThe arguments supplied to this function are described in the \"Specifying a function as a parameter\"\nsection above.</li>\n</ul>\n\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>String of matched replaced items.</p>\n</div></li></ul></div></div></div><div id='method-search' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-search' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-search' class='name expandable'>search</a>( <span class='pre'>regexp</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Executes the search for a match between a regular expression and a specified string. ...</div><div class='long'><p>Executes the search for a match between a regular expression and a specified string.</p>\n\n<p>If successful, search returns the index of the regular expression inside the string. Otherwise, it\nreturns -1.</p>\n\n<p>When you want to know whether a pattern is found in a string use search (similar to the regular\nexpression <code>test</code> method); for more information (but slower execution) use <code>match</code> (similar to the\nregular expression <code>exec</code> method).</p>\n\n<p>The following example prints a message which depends on the success of the test.</p>\n\n<pre><code>function testinput(re, str){\n    if (str.search(re) != -1)\n        midstring = \" contains \";\n    else\n        midstring = \" does not contain \";\n    document.write (str + midstring + re);\n}\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>regexp</span> : <a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a><div class='sub-desc'><p>A regular expression object. If a non-RegExp object obj is passed, it is\nimplicitly converted to a RegExp by using <code>new RegExp(obj)</code>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>If successful, search returns the index of the regular\nexpression inside the string. Otherwise, it returns -1.</p>\n</div></li></ul></div></div></div><div id='method-slice' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-slice' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-slice' class='name expandable'>slice</a>( <span class='pre'>beginSlice, endSlice</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Extracts a section of a string and returns a new string. ...</div><div class='long'><p>Extracts a section of a string and returns a new string.</p>\n\n<p><code>slice</code> extracts the text from one string and returns a new string. Changes to the text in one\nstring do not affect the other string.</p>\n\n<p><code>slice</code> extracts up to but not including <code>endSlice</code>. <code>string.slice(1,4)</code> extracts the second\ncharacter through the fourth character (characters indexed 1, 2, and 3).</p>\n\n<p>As a negative index, <code>endSlice</code> indicates an offset from the end of the string. <code>string.slice(2,-1)</code>\nextracts the third character through the second to last character in the string.</p>\n\n<p>The following example uses slice to create a new string.</p>\n\n<pre><code>// assumes a print function is defined\nvar str1 = \"The morning is upon us.\";\nvar str2 = str1.slice(4, -2);\nprint(str2);\n</code></pre>\n\n<p>This writes:</p>\n\n<pre><code>morning is upon u\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>beginSlice</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The zero-based index at which to begin extraction.</p>\n</div></li><li><span class='pre'>endSlice</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The zero-based index at which to end extraction. If omitted, <code>slice</code>\nextracts to the end of the string.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>All characters from specified start up to (but excluding)\nend.</p>\n</div></li></ul></div></div></div><div id='method-split' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-split' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-split' class='name expandable'>split</a>( <span class='pre'>seperator, limit</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Splits a String object into an array of strings by separating the string into substrings. ...</div><div class='long'><p>Splits a <code>String</code> object into an array of strings by separating the string into substrings.</p>\n\n<p>The <code>split</code> method returns the new array.</p>\n\n<p>When found, <code>separator</code> is removed from the string and the substrings are returned in an array. If\n<code>separator</code> is omitted, the array contains one element consisting of the entire string.</p>\n\n<p>If <code>separator</code> is a regular expression that contains capturing parentheses, then each time separator\nis matched the results (including any undefined results) of the capturing parentheses are spliced\ninto the output array. However, not all browsers support this capability.</p>\n\n<p>Note: When the string is empty, <code>split</code> returns an array containing one empty string, rather than an\nempty array.</p>\n\n<p>The following example defines a function that splits a string into an array of strings using the\nspecified separator. After splitting the string, the function displays messages indicating the\noriginal string (before the split), the separator used, the number of elements in the array, and the\nindividual array elements.</p>\n\n<pre><code>function splitString(stringToSplit,separator)\n{\n    var arrayOfStrings = stringToSplit.split(separator);\n    print('The original string is: \"' + stringToSplit + '\"');\n    print('The separator is: \"' + separator + '\"');\n    print(\"The array has \" + arrayOfStrings.length + \" elements: \");\n\n    for (var i=0; i &lt; arrayOfStrings.length; i++)\n        print(arrayOfStrings[i] + \" / \");\n}\n\nvar tempestString = \"Oh brave new world that has such people in it.\";\nvar monthString = \"Jan,Feb,Mar,Apr,May,Jun,Jul,Aug,Sep,Oct,Nov,Dec\";\n\nvar space = \" \";\nvar comma = \",\";\n\nsplitString(tempestString, space);\nsplitString(tempestString);\nsplitString(monthString, comma);\n</code></pre>\n\n<p>This example produces the following output:</p>\n\n<pre><code>The original string is: \"Oh brave new world that has such people in it.\"\nThe separator is: \" \"\nThe array has 10 elements: Oh / brave / new / world / that / has / such / people / in / it. /\n\nThe original string is: \"Oh brave new world that has such people in it.\"\nThe separator is: \"undefined\"\nThe array has 1 elements: Oh brave new world that has such people in it. /\n</code></pre>\n\n<p>The original string is: \"Jan,Feb,Mar,Apr,May,Jun,Jul,Aug,Sep,Oct,Nov,Dec\"\nThe separator is: \",\"\nThe array has 12 elements: Jan / Feb / Mar / Apr / May / Jun / Jul / Aug / Sep / Oct / Nov / Dec /</p>\n\n<p>In the following example, <code>split</code> looks for 0 or more spaces followed by a semicolon followed by 0\nor more spaces and, when found, removes the spaces from the string. nameList is the array returned\nas a result of split.</p>\n\n<pre><code>var names = \"Harry Trump ;Fred Barney; Helen Rigby ; Bill Abel ;Chris Hand \";\nprint(names);\nvar re = /\\s*;\\s*\\/;\nvar nameList = names.split(re);\nprint(nameList);\n</code></pre>\n\n<p>This prints two lines; the first line prints the original string, and the second line prints the\nresulting array.</p>\n\n<pre><code>Harry Trump ;Fred Barney; Helen Rigby ; Bill Abel ;Chris Hand\nHarry Trump,Fred Barney,Helen Rigby,Bill Abel,Chris Hand\n</code></pre>\n\n<p>In the following example, split looks for 0 or more spaces in a string and returns the first 3\nsplits that it finds.</p>\n\n<pre><code>var myString = \"Hello World. How are you doing?\";\nvar splits = myString.split(\" \", 3);\nprint(splits);\n</code></pre>\n\n<p>This script displays the following:</p>\n\n<pre><code>Hello,World.,How\n</code></pre>\n\n<p>If <code>separator</code> contains capturing parentheses, matched results are returned in the array.</p>\n\n<pre><code>var myString = \"Hello 1 word. Sentence number 2.\";\nvar splits = myString.split(/(\\d)/);\nprint(splits);\n</code></pre>\n\n<p>This script displays the following:</p>\n\n<pre><code>Hello ,1, word. Sentence number ,2, .\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>seperator</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>Specifies the character to use for separating the string. The separator is treated as a string or a\nregular expression. If separator is omitted, the array returned contains one element consisting of the entire string.</p>\n</div></li><li><span class='pre'>limit</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Integer specifying a limit on the number of splits to be found.  The split method still splits on every\nmatch of separator, but it truncates the returned array to at most limit elements.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>Substrings are returned in an array.</p>\n</div></li></ul></div></div></div><div id='method-substr' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-substr' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-substr' class='name expandable'>substr</a>( <span class='pre'>start, length</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns the characters in a string beginning at the specified location through the specified number\nof characters. ...</div><div class='long'><p>Returns the characters in a string beginning at the specified location through the specified number\nof characters.</p>\n\n<p><code>start</code> is a character index. The index of the first character is 0, and the index of the last\ncharacter is 1 less than the length of the string. <code>substr</code> begins extracting characters at start\nand collects length characters (unless it reaches the end of the string first, in which case it will\nreturn fewer).</p>\n\n<p>If <code>start</code> is positive and is greater than or equal to the length of the string, <code>substr</code> returns an\nempty string.</p>\n\n<p>If <code>start</code> is negative, <code>substr</code> uses it as a character index from the end of the string. If start\nis negative and abs(start) is larger than the length of the string, <code>substr</code> uses 0 as the start\nindex. Note: the described handling of negative values of the start argument is not supported by\nMicrosoft JScript.</p>\n\n<p>If length is 0 or negative, <code>substr</code> returns an empty string. If length is omitted, <code>substr</code>\nextracts characters to the end of the string.</p>\n\n<p>Consider the following script:</p>\n\n<pre><code>// assumes a print function is defined\nvar str = \"abcdefghij\";\nprint(\"(1,2): \"    + str.substr(1,2));\nprint(\"(-3,2): \"   + str.substr(-3,2));\nprint(\"(-3): \"     + str.substr(-3));\nprint(\"(1): \"      + str.substr(1));\nprint(\"(-20, 2): \" + str.substr(-20,2));\nprint(\"(20, 2): \"  + str.substr(20,2));\n</code></pre>\n\n<p>This script displays:</p>\n\n<pre><code>(1,2): bc\n(-3,2): hi\n(-3): hij\n(1): bcdefghij\n(-20, 2): ab\n(20, 2):\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>start</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Location at which to begin extracting characters.</p>\n</div></li><li><span class='pre'>length</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of characters to extract.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Modified string.</p>\n</div></li></ul></div></div></div><div id='method-substring' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-substring' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-substring' class='name expandable'>substring</a>( <span class='pre'>indexA, [indexB]</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns the characters in a string between two indexes into the string. ...</div><div class='long'><p>Returns the characters in a string between two indexes into the string.</p>\n\n<p>substring extracts characters from indexA up to but not including indexB. In particular:\n*   If <code>indexA</code> equals <code>indexB</code>, <code>substring</code> returns an empty string.\n*   If <code>indexB</code> is omitted, substring extracts characters to the end of the string.\n*   If either argument is less than 0 or is <code>NaN</code>, it is treated as if it were 0.\n*   If either argument is greater than <code>stringName.length</code>, it is treated as if it were\n<code>stringName.length</code>.</p>\n\n<p>If <code>indexA</code> is larger than <code>indexB</code>, then the effect of substring is as if the two arguments were\nswapped; for example, <code>str.substring(1, 0) == str.substring(0, 1)</code>.</p>\n\n<p>The following example uses substring to display characters from the string \"Sencha\":</p>\n\n<pre><code>// assumes a print function is defined\nvar anyString = \"Sencha\";\n\n// Displays \"Sen\"\nprint(anyString.substring(0,3));\nprint(anyString.substring(3,0));\n\n// Displays \"cha\"\nprint(anyString.substring(3,6));\nprint(anyString.substring(6,3));\n\n// Displays \"Sencha\"\nprint(anyString.substring(0,6));\nprint(anyString.substring(0,10));\n</code></pre>\n\n<p>The following example replaces a substring within a string. It will replace both individual\ncharacters and <code>substrings</code>. The function call at the end of the example changes the string \"Brave\nNew World\" into \"Brave New Web\".</p>\n\n<pre><code>function replaceString(oldS, newS, fullS) {\n    // Replaces oldS with newS in the string fullS\n    for (var i = 0; i &lt; fullS.length; i++) {\n        if (fullS.substring(i, i + oldS.length) == oldS) {\n            fullS = fullS.substring(0, i) + newS + fullS.substring(i + oldS.length,\n</code></pre>\n\n<p>fullS.length);</p>\n\n<pre><code>        }\n    }\n    return fullS;\n}\n\nreplaceString(\"World\", \"Web\", \"Brave New World\");\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>indexA</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>An integer between 0 and one less than the length of the string.</p>\n</div></li><li><span class='pre'>indexB</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>An integer between 0 and the length of the string.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Returns the characters in a string between two indexes into the string.</p>\n</div></li></ul></div></div></div><div id='method-toLocaleLowerCase' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-toLocaleLowerCase' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-toLocaleLowerCase' class='name expandable'>toLocaleLowerCase</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>The characters within a string are converted to lower case while respecting the current locale. ...</div><div class='long'><p>The characters within a string are converted to lower case while respecting the current locale. For\nmost languages, this will return the same as <code>toLowerCase</code>.</p>\n\n<p>The <code>toLocaleLowerCase</code> method returns the value of the string converted to lower case according to\nany locale-specific case mappings. <code>toLocaleLowerCase</code> does not affect the value of the string\nitself. In most cases, this will produce the same result as <code>toLowerCase()</code>, but for some locales,\nsuch as Turkish, whose case mappings do not follow the default case mappings in Unicode, there may\nbe a different result.</p>\n\n<p>The following example displays the string \"sencha\":</p>\n\n<pre><code>var upperText=\"SENCHA\";\ndocument.write(upperText.toLocaleLowerCase());\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Returns value of the string in lowercase.</p>\n</div></li></ul></div></div></div><div id='method-toLocaleUpperCase' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-toLocaleUpperCase' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-toLocaleUpperCase' class='name expandable'>toLocaleUpperCase</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>The characters within a string are converted to upper case while respecting the current locale. ...</div><div class='long'><p>The characters within a string are converted to upper case while respecting the current locale. For\nmost languages, this will return the same as <code>toUpperCase</code>.</p>\n\n<p>The <code>toLocaleUpperCase</code> method returns the value of the string converted to upper case according to\nany locale-specific case mappings. <code>toLocaleUpperCase</code> does not affect the value of the string\nitself. In most cases, this will produce the same result as <code>toUpperCase()</code>, but for some locales,\nsuch as Turkish, whose case mappings do not follow the default case mappings in Unicode, there may\nbe a different result.</p>\n\n<p>The following example displays the string \"SENCHA\":</p>\n\n<pre><code>var lowerText=\"sencha\";\ndocument.write(lowerText.toLocaleUpperCase());\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Returns value of the string in uppercase.</p>\n</div></li></ul></div></div></div><div id='method-toLowerCase' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-toLowerCase' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-toLowerCase' class='name expandable'>toLowerCase</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns the calling string value converted to lower case. ...</div><div class='long'><p>Returns the calling string value converted to lower case.</p>\n\n<p>The <code>toLowerCase</code> method returns the value of the string converted to lowercase. <code>toLowerCase</code> does\nnot affect the value of the string itself.</p>\n\n<p>The following example displays the lowercase string \"sencha\":</p>\n\n<pre><code>var upperText=\"SENCHA\";\ndocument.write(upperText.toLowerCase());\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Returns value of the string in lowercase.</p>\n</div></li></ul></div></div></div><div id='method-toString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-toString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-toString' class='name expandable'>toString</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns a string representing the specified object. ...</div><div class='long'><p>Returns a string representing the specified object. Overrides the <code><a href=\"#!/api/Object-method-toString\" rel=\"Object-method-toString\" class=\"docClass\">Object.toString</a></code> method.</p>\n\n<p>The <code>String</code> object overrides the <code>toString</code> method of the <code>Object</code> object; it does not inherit\n<code><a href=\"#!/api/Object-method-toString\" rel=\"Object-method-toString\" class=\"docClass\">Object.toString</a></code>. For <code>String</code> objects, the <code>toString</code> method returns a string representation of\nthe object.</p>\n\n<p>The following example displays the string value of a String object:</p>\n\n<pre><code>x = new String(\"Hello world\");\nalert(x.toString())      // Displays \"Hello world\"\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>A string representation of the object.</p>\n</div></li></ul></div></div></div><div id='method-toUpperCase' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-toUpperCase' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-toUpperCase' class='name expandable'>toUpperCase</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns the calling string value converted to uppercase. ...</div><div class='long'><p>Returns the calling string value converted to uppercase.</p>\n\n<p>The <code>toUpperCase</code> method returns the value of the string converted to uppercase. <code>toUpperCase</code> does\nnot affect the value of the string itself.</p>\n\n<p>The following example displays the string \"SENCHA\":</p>\n\n<pre><code>var lowerText=\"sencha\";\ndocument.write(lowerText.toUpperCase());\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Returns value of the string in uppercase.</p>\n</div></li></ul></div></div></div><div id='method-trim' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-trim' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-trim' class='name expandable'>trim</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Removes whitespace from both ends of the string. ...</div><div class='long'><p>Removes whitespace from both ends of the string.</p>\n\n<p>Does not affect the value of the string itself.</p>\n\n<p>The following example displays the lowercase string <code>\"foo\"</code>:</p>\n\n<pre><code>var orig = \"   foo  \";\nalert(orig.trim());\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>A string stripped of whitespace on both ends.</p>\n</div></li></ul></div></div></div><div id='method-valueOf' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='String'>String</span><br/><a href='source/String.html#String-method-valueOf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/String-method-valueOf' class='name expandable'>valueOf</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns the primitive value of the specified object. ...</div><div class='long'><p>Returns the primitive value of the specified object. Overrides the <code><a href=\"#!/api/Object-method-valueOf\" rel=\"Object-method-valueOf\" class=\"docClass\">Object.valueOf</a></code> method.</p>\n\n<p>The <code>valueOf</code> method of String returns the primitive value of a <code>String</code> object as a string data\ntype. This value is equivalent to <code><a href=\"#!/api/String-method-toString\" rel=\"String-method-toString\" class=\"docClass\">String.toString</a></code>.</p>\n\n<p>This method is usually called internally by JavaScript and not explicitly in code.</p>\n\n<pre><code>x = new String(\"Hello world\");\nalert(x.valueOf())          // Displays \"Hello world\"\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Returns value of string.</p>\n</div></li></ul></div></div></div></div></div></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"String.html#String","filename":"String.js"}],"linenr":1,"members":{"property":[{"tagname":"property","owner":"String","meta":{},"name":"length","id":"property-length"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"String","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"String","meta":{},"name":"charAt","id":"method-charAt"},{"tagname":"method","owner":"String","meta":{},"name":"charCodeAt","id":"method-charCodeAt"},{"tagname":"method","owner":"String","meta":{},"name":"concat","id":"method-concat"},{"tagname":"method","owner":"String","meta":{},"name":"fromCharCode","id":"method-fromCharCode"},{"tagname":"method","owner":"String","meta":{},"name":"indexOf","id":"method-indexOf"},{"tagname":"method","owner":"String","meta":{},"name":"lastIndexOf","id":"method-lastIndexOf"},{"tagname":"method","owner":"String","meta":{},"name":"localeCompare","id":"method-localeCompare"},{"tagname":"method","owner":"String","meta":{},"name":"match","id":"method-match"},{"tagname":"method","owner":"String","meta":{},"name":"replace","id":"method-replace"},{"tagname":"method","owner":"String","meta":{},"name":"search","id":"method-search"},{"tagname":"method","owner":"String","meta":{},"name":"slice","id":"method-slice"},{"tagname":"method","owner":"String","meta":{},"name":"split","id":"method-split"},{"tagname":"method","owner":"String","meta":{},"name":"substr","id":"method-substr"},{"tagname":"method","owner":"String","meta":{},"name":"substring","id":"method-substring"},{"tagname":"method","owner":"String","meta":{},"name":"toLocaleLowerCase","id":"method-toLocaleLowerCase"},{"tagname":"method","owner":"String","meta":{},"name":"toLocaleUpperCase","id":"method-toLocaleUpperCase"},{"tagname":"method","owner":"String","meta":{},"name":"toLowerCase","id":"method-toLowerCase"},{"tagname":"method","owner":"String","meta":{},"name":"toString","id":"method-toString"},{"tagname":"method","owner":"String","meta":{},"name":"toUpperCase","id":"method-toUpperCase"},{"tagname":"method","owner":"String","meta":{},"name":"trim","id":"method-trim"},{"tagname":"method","owner":"String","meta":{},"name":"valueOf","id":"method-valueOf"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"String","singleton":false,"override":null,"inheritdoc":null,"id":"class-String","mixins":[],"mixedInto":[]});