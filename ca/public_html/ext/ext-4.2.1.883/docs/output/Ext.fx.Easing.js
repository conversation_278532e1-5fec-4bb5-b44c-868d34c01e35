Ext.data.JsonP.Ext_fx_Easing({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Easing.html#Ext-fx-Easing' target='_blank'>Easing.js</a></div></pre><div class='doc-contents'><p>This class contains a series of function definitions used to modify values during an animation.\nThey describe how the intermediate values used during a transition will be calculated. It allows for a transition to change\nspeed over its duration. The following options are available:</p>\n\n<ul>\n<li>linear The default easing type</li>\n<li>backIn</li>\n<li>backOut</li>\n<li>bounceIn</li>\n<li>bounceOut</li>\n<li>ease</li>\n<li>easeIn</li>\n<li>easeOut</li>\n<li>easeInOut</li>\n<li>elasticIn</li>\n<li>elasticOut</li>\n<li>cubic-bezier(x1, y1, x2, y2)</li>\n</ul>\n\n\n<p>Note that cubic-bezier will create a custom easing curve following the CSS3 <a href=\"http://www.w3.org/TR/css3-transitions/#transition-timing-function_tag\">transition-timing-function</a>\nspecification.  The four values specify points P1 and P2 of the curve as (x1, y1, x2, y2). All values must\nbe in the range [0, 1] or the definition is invalid.</p>\n</div><div class='members'></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"Easing.html#Ext-fx-Easing","filename":"Easing.js"}],"linenr":3,"members":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.fx.Easing","singleton":true,"override":null,"inheritdoc":null,"id":"class-Ext.fx.Easing","mixins":[],"mixedInto":[]});