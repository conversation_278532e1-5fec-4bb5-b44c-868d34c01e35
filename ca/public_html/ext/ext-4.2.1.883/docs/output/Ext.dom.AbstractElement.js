Ext.data.JsonP.Ext_dom_AbstractElement({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":["Ext.dom.Element"],"extends":"Ext.Base","uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><strong>Ext.dom.AbstractElement</strong></div></div><h4>Requires</h4><div class='dependency'><a href='#!/api/Ext.EventManager' rel='Ext.EventManager' class='docClass'>Ext.EventManager</a></div><div class='dependency'>Ext.dom.AbstractElement_insertion</div><div class='dependency'>Ext.dom.AbstractElement_static</div><div class='dependency'>Ext.dom.AbstractElement_style</div><div class='dependency'>Ext.dom.AbstractElement_traversal</div><h4>Subclasses</h4><div class='dependency'><a href='#!/api/Ext.dom.Element' rel='Ext.dom.Element' class='docClass'>Ext.dom.Element</a></div><h4>Files</h4><div class='dependency'><a href='source/AbstractElement.html#Ext-dom-AbstractElement' target='_blank'>AbstractElement.js</a></div><div class='dependency'><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement_static' target='_blank'>AbstractElement_static.js</a></div><div class='dependency'><a href='source/AbstractElement_insertion.html#Ext-dom-AbstractElement_insertion' target='_blank'>AbstractElement_insertion.js</a></div><div class='dependency'><a href='source/AbstractElement_traversal.html#Ext-dom-AbstractElement_traversal' target='_blank'>AbstractElement_traversal.js</a></div><div class='dependency'><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement_style' target='_blank'>AbstractElement_style.js</a></div></pre><div class='doc-contents'><p class='private'><strong>NOTE</strong> This is a private utility class for internal use by the framework. Don't rely on its existence.</p>\n</div><div class='members'><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-defaultUnit' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-property-defaultUnit' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-property-defaultUnit' class='name expandable'>defaultUnit</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The default unit to append to CSS values where a unit isn't provided. ...</div><div class='long'><p>The default unit to append to CSS values where a unit isn't provided.</p>\n\n<p><strong>Overridden in Ext.dom.AbstractElement_static.</strong></p>\n<p>Defaults to: <code>&quot;px&quot;</code></p></div></div></div><div id='property-dom' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-property-dom' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-property-dom' class='name not-expandable'>dom</a><span> : HTMLElement</span></div><div class='description'><div class='short'><p>The DOM element</p>\n</div><div class='long'><p>The DOM element</p>\n</div></div></div><div id='property-id' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-property-id' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-property-id' class='name not-expandable'>id</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'><p>The DOM element ID</p>\n</div><div class='long'><p>The DOM element ID</p>\n</div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div><div id='property-styleHooks' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-property-styleHooks' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-property-styleHooks' class='name expandable'>styleHooks</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>This shared object is keyed by style name (e.g., 'margin-left' or 'marginLeft'). ...</div><div class='long'><p>This shared object is keyed by style name (e.g., 'margin-left' or 'marginLeft'). The\nvalues are objects with the following properties:</p>\n\n<ul>\n<li><code>name</code> (String) : The actual name to be presented to the DOM. This is typically the value\n  returned by <a href=\"#!/api/Ext.dom.AbstractElement-static-method-normalize\" rel=\"Ext.dom.AbstractElement-static-method-normalize\" class=\"docClass\">normalize</a>.</li>\n<li><code>get</code> (Function) : A hook function that will perform the get on this style. These\n  functions receive \"(dom, el)\" arguments. The <code>dom</code> parameter is the DOM Element\n  from which to get ths tyle. The <code>el</code> argument (may be null) is the <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>.</li>\n<li><code>set</code> (Function) : A hook function that will perform the set on this style. These\n  functions receive \"(dom, value, el)\" arguments. The <code>dom</code> parameter is the DOM Element\n  from which to get ths tyle. The <code>value</code> parameter is the new value for the style. The\n  <code>el</code> argument (may be null) is the <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>.</li>\n</ul>\n\n\n<p>The <code>this</code> pointer is the object that contains <code>get</code> or <code>set</code>, which means that\n<code>this.name</code> can be accessed if needed. The hook functions are both optional.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<p>Defaults to: <code>{}</code></p></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='static-property-ASCLASS' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-static-property-ASCLASS' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-property-ASCLASS' class='name expandable'>ASCLASS</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Visibility mode constant for use with Ext.dom.Element.setVisibilityMode. ...</div><div class='long'><p>Visibility mode constant for use with <a href=\"#!/api/Ext.dom.Element-method-setVisibilityMode\" rel=\"Ext.dom.Element-method-setVisibilityMode\" class=\"docClass\">Ext.dom.Element.setVisibilityMode</a>.\nAdd or remove the <a href=\"#!/api/Ext.dom.Layer-cfg-visibilityCls\" rel=\"Ext.dom.Layer-cfg-visibilityCls\" class=\"docClass\">Ext.Layer.visibilityCls</a> class to hide the element.</p>\n<p>Defaults to: <code>4</code></p></div></div></div><div id='static-property-DISPLAY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-static-property-DISPLAY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-property-DISPLAY' class='name expandable'>DISPLAY</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Visibility mode constant for use with Ext.dom.Element.setVisibilityMode. ...</div><div class='long'><p>Visibility mode constant for use with <a href=\"#!/api/Ext.dom.Element-method-setVisibilityMode\" rel=\"Ext.dom.Element-method-setVisibilityMode\" class=\"docClass\">Ext.dom.Element.setVisibilityMode</a>.\nUse the CSS 'display' property to hide the element.</p>\n<p>Defaults to: <code>2</code></p></div></div></div><div id='static-property-OFFSETS' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-static-property-OFFSETS' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-property-OFFSETS' class='name expandable'>OFFSETS</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Visibility mode constant for use with Ext.dom.Element.setVisibilityMode. ...</div><div class='long'><p>Visibility mode constant for use with <a href=\"#!/api/Ext.dom.Element-method-setVisibilityMode\" rel=\"Ext.dom.Element-method-setVisibilityMode\" class=\"docClass\">Ext.dom.Element.setVisibilityMode</a>.\nUse CSS absolute positioning and top/left offsets to hide the element.</p>\n<p>Defaults to: <code>3</code></p></div></div></div><div id='static-property-VISIBILITY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-static-property-VISIBILITY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-property-VISIBILITY' class='name expandable'>VISIBILITY</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Visibility mode constant for use with Ext.dom.Element.setVisibilityMode. ...</div><div class='long'><p>Visibility mode constant for use with <a href=\"#!/api/Ext.dom.Element-method-setVisibilityMode\" rel=\"Ext.dom.Element-method-setVisibilityMode\" class=\"docClass\">Ext.dom.Element.setVisibilityMode</a>.\nUse the CSS 'visibility' property to hide the element.</p>\n\n<p>Note that in this mode, <a href=\"#!/api/Ext.dom.Element-method-isVisible\" rel=\"Ext.dom.Element-method-isVisible\" class=\"docClass\">isVisible</a> may return true\nfor an element even though it actually has a parent element that is hidden. For this\nreason, and in most cases, using the <a href=\"#!/api/Ext.dom.AbstractElement-static-property-OFFSETS\" rel=\"Ext.dom.AbstractElement-static-property-OFFSETS\" class=\"docClass\">OFFSETS</a> mode is a better choice.</p>\n<p>Defaults to: <code>1</code></p></div></div></div><div id='property-borders' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-property-borders' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-property-borders' class='name expandable'>borders</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Defined in override Ext.dom.AbstractElement_static. ...</div><div class='long'><p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<p>Defaults to: <code>{l: 'border-left-width', r: 'border-right-width', t: 'border-top-width', b: 'border-bottom-width'}</code></p></div></div></div><div id='property-camelRe' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-property-camelRe' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-property-camelRe' class='name expandable'>camelRe</a><span> : <a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Defined in override Ext.dom.AbstractElement_static. ...</div><div class='long'><p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<p>Defaults to: <code>/(-[a-z])/gi</code></p></div></div></div><div id='property-cssRe' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-property-cssRe' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-property-cssRe' class='name expandable'>cssRe</a><span> : <a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Defined in override Ext.dom.AbstractElement_static. ...</div><div class='long'><p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<p>Defaults to: <code>/([a-z0-9\\-]+)\\s*:\\s*([^;\\s]+(?:\\s*[^;\\s]+)*)?;?/gi</code></p></div></div></div><div id='property-margins' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-property-margins' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-property-margins' class='name expandable'>margins</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Defined in override Ext.dom.AbstractElement_static. ...</div><div class='long'><p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<p>Defaults to: <code>{l: 'margin-left', r: 'margin-right', t: 'margin-top', b: 'margin-bottom'}</code></p></div></div></div><div id='property-msRe' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-property-msRe' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-property-msRe' class='name expandable'>msRe</a><span> : <a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Defined in override Ext.dom.AbstractElement_static. ...</div><div class='long'><p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<p>Defaults to: <code>/^-ms-/</code></p></div></div></div><div id='property-opacityRe' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-property-opacityRe' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-property-opacityRe' class='name expandable'>opacityRe</a><span> : <a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Defined in override Ext.dom.AbstractElement_static. ...</div><div class='long'><p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<p>Defaults to: <code>/alpha\\(opacity=(.*)\\)/i</code></p></div></div></div><div id='property-paddings' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-property-paddings' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-property-paddings' class='name expandable'>paddings</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Defined in override Ext.dom.AbstractElement_static. ...</div><div class='long'><p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<p>Defaults to: <code>{l: 'padding-left', r: 'padding-right', t: 'padding-top', b: 'padding-bottom'}</code></p></div></div></div><div id='property-propertyCache' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-property-propertyCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-property-propertyCache' class='name expandable'>propertyCache</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Defined in override Ext.dom.AbstractElement_static. ...</div><div class='long'><p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-trimRe' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-property-trimRe' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-property-trimRe' class='name expandable'>trimRe</a><span> : <a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>/^\\s+|\\s+$/g</code></p></div></div></div><div id='property-unitRe' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-property-unitRe' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-property-unitRe' class='name expandable'>unitRe</a><span> : <a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Defined in override Ext.dom.AbstractElement_static. ...</div><div class='long'><p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<p>Defaults to: <code>/\\d+(px|em|%|en|ex|pt|in|cm|mm|pc)$/i</code></p></div></div></div><div id='property-whitespaceRe' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-property-whitespaceRe' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-property-whitespaceRe' class='name expandable'>whitespaceRe</a><span> : <a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>/\\s/</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/Ext.dom.AbstractElement-method-constructor' class='name expandable'>Ext.dom.AbstractElement</a>( <span class='pre'>element, forceNew</span> ) : <a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>element</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>forceNew</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-addCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-addCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-addCls' class='name expandable'>addCls</a>( <span class='pre'>className</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></div><div class='description'><div class='short'>Adds one or more CSS classes to the element. ...</div><div class='long'><p>Adds one or more CSS classes to the element. Duplicate classes are automatically filtered out.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>className</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>The CSS classes to add separated by space, or an array of classes</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-appendChild' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_insertion.html#Ext-dom-AbstractElement-method-appendChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-appendChild' class='name expandable'>appendChild</a>( <span class='pre'>el, [returnDom]</span> ) : <a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></div><div class='description'><div class='short'>Appends the passed element(s) to this element\n\nDefined in override Ext.dom.AbstractElement_insertion. ...</div><div class='long'><p>Appends the passed element(s) to this element</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_insertion.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The id or element to insert or a DomHelper config\nThe id of the node, a DOM Node or an existing Element.</p>\n</div></li><li><span class='pre'>returnDom</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return the raw DOM element instead of <a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></span><div class='sub-desc'><p>The inserted Element.</p>\n</div></li></ul></div></div></div><div id='method-appendTo' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_insertion.html#Ext-dom-AbstractElement-method-appendTo' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-appendTo' class='name expandable'>appendTo</a>( <span class='pre'>el</span> ) : <a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Appends this element to the passed element\n\nDefined in override Ext.dom.AbstractElement_insertion. ...</div><div class='long'><p>Appends this element to the passed element</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_insertion.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a><div class='sub-desc'><p>The new parent element.\nThe id of the node, a DOM Node or an existing Element.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></span><div class='sub-desc'><p>This element</p>\n</div></li></ul></div></div></div><div id='method-applyStyles' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-applyStyles' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-applyStyles' class='name expandable'>applyStyles</a>( <span class='pre'>styles</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></div><div class='description'><div class='short'>More flexible version of setStyle for setting style properties. ...</div><div class='long'><p>More flexible version of <a href=\"#!/api/Ext.dom.AbstractElement-method-setStyle\" rel=\"Ext.dom.AbstractElement-method-setStyle\" class=\"docClass\">setStyle</a> for setting style properties.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>styles</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>A style specification string, e.g. \"width:100px\", or object in the form {width:\"100px\"}, or\na function which returns such a specification.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-callOverridden' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-child' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_traversal.html#Ext-dom-AbstractElement-method-child' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-child' class='name expandable'>child</a>( <span class='pre'>selector, [returnDom]</span> ) : HTMLElement/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></div><div class='description'><div class='short'>Selects a single direct child based on the passed CSS selector (the selector should not contain an id). ...</div><div class='long'><p>Selects a single <em>direct</em> child based on the passed CSS selector (the selector should not contain an id).</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_traversal.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>selector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The CSS selector</p>\n</div></li><li><span class='pre'>returnDom</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return the DOM node instead of <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>HTMLElement/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></span><div class='sub-desc'><p>The child <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a> (or DOM node if returnDom = true)</p>\n</div></li></ul></div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-contains' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-contains' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-contains' class='name expandable'>contains</a>( <span class='pre'>el</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if this element is an ancestor of the passed element ...</div><div class='long'><p>Returns true if this element is an ancestor of the passed element</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : HTMLElement/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The element to check</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if this element is an ancestor of el, else false</p>\n</div></li></ul></div></div></div><div id='method-createChild' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_insertion.html#Ext-dom-AbstractElement-method-createChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-createChild' class='name expandable'>createChild</a>( <span class='pre'>config, [insertBefore], [returnDom]</span> ) : <a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></div><div class='description'><div class='short'>Creates the passed DomHelper config and appends it to this element or optionally inserts it before the passed child e...</div><div class='long'><p>Creates the passed DomHelper config and appends it to this element or optionally inserts it before the passed child element.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_insertion.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>DomHelper element config object.  If no tag is specified (e.g., {tag:'input'}) then a div will be\nautomatically generated with the specified attributes.</p>\n</div></li><li><span class='pre'>insertBefore</span> : HTMLElement (optional)<div class='sub-desc'><p>a child element of this element</p>\n</div></li><li><span class='pre'>returnDom</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>true to return the dom node instead of creating an Element</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></span><div class='sub-desc'><p>The new child element</p>\n</div></li></ul></div></div></div><div id='method-destroy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-destroy' class='name expandable'>destroy</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Alias to remove. ...</div><div class='long'><p>Alias to <a href=\"#!/api/Ext.dom.AbstractElement-method-remove\" rel=\"Ext.dom.AbstractElement-method-remove\" class=\"docClass\">remove</a>.</p>\n\n<p>Removes this element's dom reference. Note that event and cache removal is handled at <a href=\"#!/api/Ext-method-removeNode\" rel=\"Ext-method-removeNode\" class=\"docClass\">Ext.removeNode</a></p>\n<p>Overrides: <a href='#!/api/Ext.Base-method-destroy' rel='Ext.Base-method-destroy' class='docClass'>Ext.Base.destroy</a></p></div></div></div><div id='method-down' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_traversal.html#Ext-dom-AbstractElement-method-down' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-down' class='name expandable'>down</a>( <span class='pre'>selector, [returnDom]</span> ) : HTMLElement/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></div><div class='description'><div class='short'>Selects a single child at any depth below this element based on the passed CSS selector (the selector should not cont...</div><div class='long'><p>Selects a single child at any depth below this element based on the passed CSS selector (the selector should not contain an id).</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_traversal.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>selector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The CSS selector</p>\n</div></li><li><span class='pre'>returnDom</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return the DOM node instead of <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>HTMLElement/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></span><div class='sub-desc'><p>The child <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a> (or DOM node if returnDom = true)</p>\n</div></li></ul></div></div></div><div id='method-findParent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_traversal.html#Ext-dom-AbstractElement-method-findParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-findParent' class='name expandable'>findParent</a>( <span class='pre'>selector, [limit], [returnEl]</span> ) : HTMLElement</div><div class='description'><div class='short'>Looks at this node and then at parent nodes for a match of the passed simple selector (e.g. ...</div><div class='long'><p>Looks at this node and then at parent nodes for a match of the passed simple selector (e.g. div.some-class or span:first-child)</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_traversal.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>selector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The simple selector to test</p>\n</div></li><li><span class='pre'>limit</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a> (optional)<div class='sub-desc'><p>The max depth to search as a number or an element which causes the upward traversal to stop\nand is <b>not</b> considered for inclusion as the result. (defaults to 50 || document.documentElement)</p>\n</div></li><li><span class='pre'>returnEl</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return a <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a> object instead of DOM node</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>HTMLElement</span><div class='sub-desc'><p>The matching DOM node (or null if no match was found)</p>\n</div></li></ul></div></div></div><div id='method-findParentNode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_traversal.html#Ext-dom-AbstractElement-method-findParentNode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-findParentNode' class='name expandable'>findParentNode</a>( <span class='pre'>selector, [limit], [returnEl]</span> ) : HTMLElement</div><div class='description'><div class='short'>Looks at parent nodes for a match of the passed simple selector (e.g. ...</div><div class='long'><p>Looks at parent nodes for a match of the passed simple selector (e.g. div.some-class or span:first-child)</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_traversal.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>selector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The simple selector to test</p>\n</div></li><li><span class='pre'>limit</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a> (optional)<div class='sub-desc'><p>The max depth to search as a number or an element which causes the upward traversal to stop\nand is <b>not</b> considered for inclusion as the result. (defaults to 50 || document.documentElement)</p>\n</div></li><li><span class='pre'>returnEl</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return a <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a> object instead of DOM node</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>HTMLElement</span><div class='sub-desc'><p>The matching DOM node (or null if no match was found)</p>\n</div></li></ul></div></div></div><div id='method-first' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_traversal.html#Ext-dom-AbstractElement-method-first' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-first' class='name expandable'>first</a>( <span class='pre'>[selector], [returnDom]</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>/HTMLElement</div><div class='description'><div class='short'>Gets the first child, skipping text nodes\n\nDefined in override Ext.dom.AbstractElement_traversal. ...</div><div class='long'><p>Gets the first child, skipping text nodes</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_traversal.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>selector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Find the next sibling that matches the passed simple selector</p>\n</div></li><li><span class='pre'>returnDom</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return a raw dom node instead of an <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>/HTMLElement</span><div class='sub-desc'><p>The first child or null</p>\n</div></li></ul></div></div></div><div id='method-getActiveElement' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-method-getActiveElement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-getActiveElement' class='name expandable'>getActiveElement</a>( <span class='pre'></span> ) : HTMLElement</div><div class='description'><div class='short'>Returns the active element in the DOM. ...</div><div class='long'><p>Returns the active element in the DOM. If the browser supports activeElement\non the document, this is returned. If not, the focus is tracked and the active\nelement is maintained internally.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'>HTMLElement</span><div class='sub-desc'><p>The active (focused) element in the document.</p>\n</div></li></ul></div></div></div><div id='method-getAttribute' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-getAttribute' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-getAttribute' class='name expandable'>getAttribute</a>( <span class='pre'>name, [namespace]</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns the value of an attribute from the element's underlying DOM node. ...</div><div class='long'><p>Returns the value of an attribute from the element's underlying DOM node.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The attribute name</p>\n</div></li><li><span class='pre'>namespace</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The namespace in which to look for the attribute</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The attribute value</p>\n</div></li></ul></div></div></div><div id='method-getBorderWidth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-getBorderWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-getBorderWidth' class='name expandable'>getBorderWidth</a>( <span class='pre'>side</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Gets the width of the border(s) for the specified side(s)\n\nDefined in override Ext.dom.AbstractElement_style. ...</div><div class='long'><p>Gets the width of the border(s) for the specified side(s)</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>side</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>Can be t, l, r, b or any combination of those to add multiple values. For example,\npassing <code>'lr'</code> would get the border <strong>l</strong>eft width + the border <strong>r</strong>ight width.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The width of the sides passed added together</p>\n</div></li></ul></div></div></div><div id='method-getById' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-getById' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-getById' class='name expandable'>getById</a>( <span class='pre'>id, [asDom]</span> )</div><div class='description'><div class='short'>Returns a child element of this element given its id. ...</div><div class='long'><p>Returns a child element of this element given its <code>id</code>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The id of the desired child element.</p>\n</div></li><li><span class='pre'>asDom</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return the DOM element, false to return a\nwrapped Element object.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul></div></div></div><div id='method-getCache' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-getCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-getCache' class='name expandable'>getCache</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getHTML' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-getHTML' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-getHTML' class='name expandable'>getHTML</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Returns the innerHTML of an Element or an empty string if the element's\ndom no longer exists. ...</div><div class='long'><p>Returns the innerHTML of an Element or an empty string if the element's\ndom no longer exists.</p>\n</div></div></div><div id='method-getHeight' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-getHeight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-getHeight' class='name expandable'>getHeight</a>( <span class='pre'>[contentHeight]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the offset height of the element\n\nDefined in override Ext.dom.AbstractElement_style. ...</div><div class='long'><p>Returns the offset height of the element</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>contentHeight</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>true to get the height minus borders and padding</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The element's height</p>\n</div></li></ul></div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-getMargin' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-getMargin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-getMargin' class='name expandable'>getMargin</a>( <span class='pre'>[sides]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns an object with properties top, left, right and bottom representing the margins of this element unless sides i...</div><div class='long'><p>Returns an object with properties top, left, right and bottom representing the margins of this element unless sides is passed,\nthen it returns the calculated width of the sides (see getPadding)</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>sides</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Any combination of l, r, t, b to get the sum of those sides</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getPadding' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-getPadding' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-getPadding' class='name expandable'>getPadding</a>( <span class='pre'>side</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Gets the width of the padding(s) for the specified side(s)\n\nDefined in override Ext.dom.AbstractElement_style. ...</div><div class='long'><p>Gets the width of the padding(s) for the specified side(s)</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>side</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>Can be t, l, r, b or any combination of those to add multiple values. For example,\npassing <code>'lr'</code> would get the padding <strong>l</strong>eft + the padding <strong>r</strong>ight.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The padding of the sides passed added together</p>\n</div></li></ul></div></div></div><div id='method-getRightMarginFixCleaner' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-method-getRightMarginFixCleaner' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-getRightMarginFixCleaner' class='name expandable'>getRightMarginFixCleaner</a>( <span class='pre'>target</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Creates a function to call to clean up problems with the work-around for the\nWebKit RightMargin bug. ...</div><div class='long'><p>Creates a function to call to clean up problems with the work-around for the\nWebKit RightMargin bug. The work-around is to add \"display: 'inline-block'\" to\nthe element before calling getComputedStyle and then to restore its original\ndisplay value. The problem with this is that it corrupts the selection of an\nINPUT or TEXTAREA element (as in the \"I-beam\" goes away but ths focus remains).\nTo cleanup after this, we need to capture the selection of any such element and\nthen restore it after we have restored the display style.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a><div class='sub-desc'><p>The top-most element being adjusted.</p>\n</div></li></ul></div></div></div><div id='method-getSize' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-getSize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-getSize' class='name expandable'>getSize</a>( <span class='pre'>[contentSize]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Returns the size of the element. ...</div><div class='long'><p>Returns the size of the element.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>contentSize</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>true to get the width/size minus borders and padding</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>An object containing the element's size:</p>\n<ul><li><span class='pre'>width</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'></div></li><li><span class='pre'>height</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'></div></li></ul></div></li></ul></div></div></div><div id='method-getStyle' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-getStyle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-getStyle' class='name expandable'>getStyle</a>( <span class='pre'>property, [inline]</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Returns a named style property based on computed/currentStyle (primary) and\ninline-style if primary is not available. ...</div><div class='long'><p>Returns a named style property based on computed/currentStyle (primary) and\ninline-style if primary is not available.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>property</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>The style property (or multiple property names\nin an array) whose value is returned.</p>\n</div></li><li><span class='pre'>inline</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>if <code>true</code> only inline styles will be returned.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The current value of the style property for this element\n(or a hash of named style values if multiple property arguments are requested).</p>\n</div></li></ul></div></div></div><div id='method-getValue' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-getValue' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-getValue' class='name expandable'>getValue</a>( <span class='pre'>asNumber</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the value of the \"value\" attribute ...</div><div class='long'><p>Returns the value of the \"value\" attribute</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>asNumber</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>true to parse the value as a number</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getViewSize' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-getViewSize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-getViewSize' class='name expandable'>getViewSize</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Returns the dimensions of the element available to lay content out in. ...</div><div class='long'><p>Returns the dimensions of the element available to lay content out in.</p>\n\n<p>If the element (or any ancestor element) has CSS style <code>display: none</code>, the dimensions will be zero.</p>\n\n<p>Example:</p>\n\n<pre><code>var vpSize = <a href=\"#!/api/Ext-method-getBody\" rel=\"Ext-method-getBody\" class=\"docClass\">Ext.getBody</a>().getViewSize();\n\n// all Windows created afterwards will have a default value of 90% height and 95% width\n<a href=\"#!/api/Ext.window.Window-static-method-override\" rel=\"Ext.window.Window-static-method-override\" class=\"docClass\">Ext.Window.override</a>({\n    width: vpSize.width * 0.9,\n    height: vpSize.height * 0.95\n});\n// To handle window resizing you would have to hook onto onWindowResize.\n</code></pre>\n\n<p>getViewSize utilizes clientHeight/clientWidth which excludes sizing of scrollbars.\nTo obtain the size including scrollbars, use getStyleSize</p>\n\n<p>Sizing of the document body is handled at the adapter level which handles special cases for IE and strict modes, etc.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Object describing width and height.</p>\n<ul><li><span class='pre'>width</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'></div></li><li><span class='pre'>height</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'></div></li></ul></div></li></ul></div></div></div><div id='method-getVisibilityMode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-getVisibilityMode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-getVisibilityMode' class='name expandable'>getVisibilityMode</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getWidth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-getWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-getWidth' class='name expandable'>getWidth</a>( <span class='pre'>[contentWidth]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the offset width of the element\n\nDefined in override Ext.dom.AbstractElement_style. ...</div><div class='long'><p>Returns the offset width of the element</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>contentWidth</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>true to get the width minus borders and padding</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The element's width</p>\n</div></li></ul></div></div></div><div id='method-hasCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-hasCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-hasCls' class='name expandable'>hasCls</a>( <span class='pre'>className</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks if the specified CSS class exists on this element's DOM node. ...</div><div class='long'><p>Checks if the specified CSS class exists on this element's DOM node.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>className</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The CSS class to check for</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if the class exists, else false</p>\n</div></li></ul></div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-hide' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-hide' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-hide' class='name expandable'>hide</a>( <span class='pre'>[animate]</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Hide this element - Uses display mode to determine whether to use \"display\" or \"visibility\". ...</div><div class='long'><p>Hide this element - Uses display mode to determine whether to use \"display\" or \"visibility\". See <a href=\"#!/api/Ext.dom.AbstractElement-method-setVisible\" rel=\"Ext.dom.AbstractElement-method-setVisible\" class=\"docClass\">setVisible</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>animate</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>true for the default animation or a standard Element animation config object</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-insertAfter' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_insertion.html#Ext-dom-AbstractElement-method-insertAfter' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-insertAfter' class='name expandable'>insertAfter</a>( <span class='pre'>el</span> ) : <a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Inserts this element after the passed element in the DOM\n\nDefined in override Ext.dom.AbstractElement_insertion. ...</div><div class='long'><p>Inserts this element after the passed element in the DOM</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_insertion.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a><div class='sub-desc'><p>The element to insert after.\nThe id of the node, a DOM Node or an existing Element.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></span><div class='sub-desc'><p>This element</p>\n</div></li></ul></div></div></div><div id='method-insertBefore' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_insertion.html#Ext-dom-AbstractElement-method-insertBefore' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-insertBefore' class='name expandable'>insertBefore</a>( <span class='pre'>el</span> ) : <a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Inserts this element before the passed element in the DOM\n\nDefined in override Ext.dom.AbstractElement_insertion. ...</div><div class='long'><p>Inserts this element before the passed element in the DOM</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_insertion.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a><div class='sub-desc'><p>The element before which this element will be inserted.\nThe id of the node, a DOM Node or an existing Element.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></span><div class='sub-desc'><p>This element</p>\n</div></li></ul></div></div></div><div id='method-insertFirst' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_insertion.html#Ext-dom-AbstractElement-method-insertFirst' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-insertFirst' class='name expandable'>insertFirst</a>( <span class='pre'>el</span> ) : <a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></div><div class='description'><div class='short'>Inserts (or creates) an element (or DomHelper config) as the first child of this element\n\nDefined in override Ext.dom...</div><div class='long'><p>Inserts (or creates) an element (or DomHelper config) as the first child of this element</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_insertion.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The id or element to insert or a DomHelper config\nto create and insert</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></span><div class='sub-desc'><p>The new child</p>\n</div></li></ul></div></div></div><div id='method-insertHtml' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_insertion.html#Ext-dom-AbstractElement-method-insertHtml' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-insertHtml' class='name expandable'>insertHtml</a>( <span class='pre'>where, html, [returnEl]</span> ) : HTMLElement/<a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></div><div class='description'><div class='short'>Inserts an html fragment into this element\n\nDefined in override Ext.dom.AbstractElement_insertion. ...</div><div class='long'><p>Inserts an html fragment into this element</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_insertion.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>where</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>Where to insert the html in relation to this element - beforeBegin, afterBegin, beforeEnd, afterEnd.\nSee <a href=\"#!/api/Ext.dom.Helper-method-insertHtml\" rel=\"Ext.dom.Helper-method-insertHtml\" class=\"docClass\">Ext.dom.Helper.insertHtml</a> for details.</p>\n</div></li><li><span class='pre'>html</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The HTML fragment</p>\n</div></li><li><span class='pre'>returnEl</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return an <a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>HTMLElement/<a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></span><div class='sub-desc'><p>The inserted node (or nearest related if more than 1 inserted)</p>\n</div></li></ul></div></div></div><div id='method-insertSibling' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_insertion.html#Ext-dom-AbstractElement-method-insertSibling' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-insertSibling' class='name expandable'>insertSibling</a>( <span class='pre'>el, [where], [returnDom]</span> ) : <a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></div><div class='description'><div class='short'>Inserts (or creates) the passed element (or DomHelper config) as a sibling of this element\n\nDefined in override Ext.d...</div><div class='long'><p>Inserts (or creates) the passed element (or DomHelper config) as a sibling of this element</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_insertion.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The id, element to insert or a DomHelper config\nto create and insert <em>or</em> an array of any of those.</p>\n</div></li><li><span class='pre'>where</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>'before' or 'after'</p>\n<p>Defaults to: <code>'before'</code></p></div></li><li><span class='pre'>returnDom</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return the raw DOM element instead of <a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></span><div class='sub-desc'><p>The inserted Element. If an array is passed, the last inserted element is returned.</p>\n</div></li></ul></div></div></div><div id='method-is' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-is' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-is' class='name expandable'>is</a>( <span class='pre'>selector</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if this element matches the passed simple selector (e.g. ...</div><div class='long'><p>Returns true if this element matches the passed simple selector (e.g. div.some-class or span:first-child)</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>selector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The simple selector to test</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if this element matches the selector, else false</p>\n</div></li></ul></div></div></div><div id='method-isAncestor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_traversal.html#Ext-dom-AbstractElement-method-isAncestor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-isAncestor' class='name expandable'>isAncestor</a>( <span class='pre'>element</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Defined in override Ext.dom.AbstractElement_traversal. ...</div><div class='long'><p><strong>Defined in override Ext.dom.AbstractElement_traversal.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>element</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isStyle' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-isStyle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-isStyle' class='name expandable'>isStyle</a>( <span class='pre'>style, value</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks if the current value of a style is equal to a given value. ...</div><div class='long'><p>Checks if the current value of a style is equal to a given value.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>style</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>property whose value is returned.</p>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>to check against.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>true for when the current value equals the given value.</p>\n</div></li></ul></div></div></div><div id='method-isTransparent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-isTransparent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-isTransparent' class='name expandable'>isTransparent</a>( <span class='pre'>prop</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the value of the given property is visually transparent. ...</div><div class='long'><p>Returns true if the value of the given property is visually transparent. This\nmay be due to a 'transparent' style value or an rgba value with 0 in the alpha\ncomponent.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>prop</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The style property whose value is to be tested.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if the style property is visually transparent.</p>\n</div></li></ul></div></div></div><div id='method-last' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_traversal.html#Ext-dom-AbstractElement-method-last' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-last' class='name expandable'>last</a>( <span class='pre'>[selector], [returnDom]</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>/HTMLElement</div><div class='description'><div class='short'>Gets the last child, skipping text nodes\n\nDefined in override Ext.dom.AbstractElement_traversal. ...</div><div class='long'><p>Gets the last child, skipping text nodes</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_traversal.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>selector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Find the previous sibling that matches the passed simple selector</p>\n</div></li><li><span class='pre'>returnDom</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return a raw dom node instead of an <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>/HTMLElement</span><div class='sub-desc'><p>The last child or null</p>\n</div></li></ul></div></div></div><div id='method-mask' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-mask' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-mask' class='name expandable'>mask</a>( <span class='pre'>[msg], [msgCls]</span> )</div><div class='description'><div class='short'>Puts a mask over this element to disable user interaction. ...</div><div class='long'><p>Puts a mask over this element to disable user interaction. Requires core.css.\nThis method can only be applied to elements which accept child nodes.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>msg</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>A message to display in the mask</p>\n</div></li><li><span class='pre'>msgCls</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>A css class to apply to the msg element</p>\n</div></li></ul></div></div></div><div id='method-matchNode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_traversal.html#Ext-dom-AbstractElement-method-matchNode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-matchNode' class='name expandable'>matchNode</a>( <span class='pre'>dir, start, selector, returnDom</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Defined in override Ext.dom.AbstractElement_traversal. ...</div><div class='long'><p><strong>Defined in override Ext.dom.AbstractElement_traversal.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>dir</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>start</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>selector</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>returnDom</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-next' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_traversal.html#Ext-dom-AbstractElement-method-next' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-next' class='name expandable'>next</a>( <span class='pre'>[selector], [returnDom]</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>/HTMLElement</div><div class='description'><div class='short'>Gets the next sibling, skipping text nodes\n\nDefined in override Ext.dom.AbstractElement_traversal. ...</div><div class='long'><p>Gets the next sibling, skipping text nodes</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_traversal.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>selector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Find the next sibling that matches the passed simple selector</p>\n</div></li><li><span class='pre'>returnDom</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return a raw dom node instead of an <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>/HTMLElement</span><div class='sub-desc'><p>The next sibling or null</p>\n</div></li></ul></div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-parent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_traversal.html#Ext-dom-AbstractElement-method-parent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-parent' class='name expandable'>parent</a>( <span class='pre'>[selector], [returnDom]</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>/HTMLElement</div><div class='description'><div class='short'>Gets the parent node for this element, optionally chaining up trying to match a selector\n\nDefined in override Ext.dom...</div><div class='long'><p>Gets the parent node for this element, optionally chaining up trying to match a selector</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_traversal.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>selector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Find a parent node that matches the passed simple selector</p>\n</div></li><li><span class='pre'>returnDom</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return a raw dom node instead of an <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>/HTMLElement</span><div class='sub-desc'><p>The parent node or null</p>\n</div></li></ul></div></div></div><div id='method-prev' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_traversal.html#Ext-dom-AbstractElement-method-prev' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-prev' class='name expandable'>prev</a>( <span class='pre'>[selector], [returnDom]</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>/HTMLElement</div><div class='description'><div class='short'>Gets the previous sibling, skipping text nodes\n\nDefined in override Ext.dom.AbstractElement_traversal. ...</div><div class='long'><p>Gets the previous sibling, skipping text nodes</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_traversal.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>selector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Find the previous sibling that matches the passed simple selector</p>\n</div></li><li><span class='pre'>returnDom</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return a raw dom node instead of an <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>/HTMLElement</span><div class='sub-desc'><p>The previous sibling or null</p>\n</div></li></ul></div></div></div><div id='method-query' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_traversal.html#Ext-dom-AbstractElement-method-query' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-query' class='name expandable'>query</a>( <span class='pre'>selector</span> ) : HTMLElement[]</div><div class='description'><div class='short'>Selects child nodes based on the passed CSS selector (the selector should not contain an id). ...</div><div class='long'><p>Selects child nodes based on the passed CSS selector (the selector should not contain an id).</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_traversal.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>selector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The CSS selector</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>HTMLElement[]</span><div class='sub-desc'><p>An array of the matched nodes</p>\n</div></li></ul></div></div></div><div id='method-radioCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-radioCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-radioCls' class='name expandable'>radioCls</a>( <span class='pre'>className</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></div><div class='description'><div class='short'>Adds one or more CSS classes to this element and removes the same class(es) from all siblings. ...</div><div class='long'><p>Adds one or more CSS classes to this element and removes the same class(es) from all siblings.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>className</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>The CSS class to add, or an array of classes</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-remove' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-remove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-remove' class='name expandable'>remove</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Removes this element's dom reference. ...</div><div class='long'><p>Removes this element's dom reference. Note that event and cache removal is handled at <a href=\"#!/api/Ext-method-removeNode\" rel=\"Ext-method-removeNode\" class=\"docClass\">Ext.removeNode</a></p>\n</div></div></div><div id='method-removeCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-removeCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-removeCls' class='name expandable'>removeCls</a>( <span class='pre'>className</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></div><div class='description'><div class='short'>Removes one or more CSS classes from the element. ...</div><div class='long'><p>Removes one or more CSS classes from the element.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>className</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>The CSS classes to remove separated by space, or an array of classes</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-repaint' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-repaint' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-repaint' class='name expandable'>repaint</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Forces the browser to repaint this element\n\nDefined in override Ext.dom.AbstractElement_style. ...</div><div class='long'><p>Forces the browser to repaint this element</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-replace' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_insertion.html#Ext-dom-AbstractElement-method-replace' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-replace' class='name expandable'>replace</a>( <span class='pre'>el</span> ) : <a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Replaces the passed element with this element\n\nDefined in override Ext.dom.AbstractElement_insertion. ...</div><div class='long'><p>Replaces the passed element with this element</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_insertion.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a><div class='sub-desc'><p>The element to replace.\nThe id of the node, a DOM Node or an existing Element.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></span><div class='sub-desc'><p>This element</p>\n</div></li></ul></div></div></div><div id='method-replaceCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-replaceCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-replaceCls' class='name expandable'>replaceCls</a>( <span class='pre'>oldClassName, newClassName</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></div><div class='description'><div class='short'>Replaces a CSS class on the element with another. ...</div><div class='long'><p>Replaces a CSS class on the element with another.  If the old name does not exist, the new name will simply be added.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>oldClassName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The CSS class to replace</p>\n</div></li><li><span class='pre'>newClassName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The replacement CSS class</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-replaceWith' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_insertion.html#Ext-dom-AbstractElement-method-replaceWith' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-replaceWith' class='name expandable'>replaceWith</a>( <span class='pre'>el</span> ) : <a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></div><div class='description'><div class='short'>Replaces this element with the passed element\n\nDefined in override Ext.dom.AbstractElement_insertion. ...</div><div class='long'><p>Replaces this element with the passed element</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_insertion.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new element (id of the node, a DOM Node\nor an existing Element) or a DomHelper config of an element to create</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></span><div class='sub-desc'><p>This element</p>\n</div></li></ul></div></div></div><div id='method-select' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_traversal.html#Ext-dom-AbstractElement-method-select' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-select' class='name expandable'>select</a>( <span class='pre'>selector, [unique]</span> ) : <a href=\"#!/api/Ext.dom.CompositeElement\" rel=\"Ext.dom.CompositeElement\" class=\"docClass\">Ext.CompositeElement</a></div><div class='description'><div class='short'>Creates a Ext.CompositeElement for child nodes based on the passed CSS selector (the selector should not contain an id). ...</div><div class='long'><p>Creates a <a href=\"#!/api/Ext.dom.CompositeElement\" rel=\"Ext.dom.CompositeElement\" class=\"docClass\">Ext.CompositeElement</a> for child nodes based on the passed CSS selector (the selector should not contain an id).</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_traversal.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>selector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The CSS selector</p>\n</div></li><li><span class='pre'>unique</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to create a unique <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a> for each element. Defaults to a shared flyweight object.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.CompositeElement\" rel=\"Ext.dom.CompositeElement\" class=\"docClass\">Ext.CompositeElement</a></span><div class='sub-desc'><p>The composite element</p>\n</div></li></ul></div></div></div><div id='method-serializeForm' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-method-serializeForm' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-serializeForm' class='name expandable'>serializeForm</a>( <span class='pre'>form</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Serializes a DOM form into a url encoded string\n\nDefined in override Ext.dom.AbstractElement_static. ...</div><div class='long'><p>Serializes a DOM form into a url encoded string</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>form</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The form</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The url encoded form</p>\n</div></li></ul></div></div></div><div id='method-set' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-set' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-set' class='name expandable'>set</a>( <span class='pre'>o, [useSet]</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Sets the passed attributes as attributes of this element (a style attribute can be a string, object or function) ...</div><div class='long'><p>Sets the passed attributes as attributes of this element (a style attribute can be a string, object or function)</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>o</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object with the attributes</p>\n</div></li><li><span class='pre'>useSet</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>false to override the default setAttribute to use expandos.</p>\n<p>Defaults to: <code>true</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setHTML' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-setHTML' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-setHTML' class='name expandable'>setHTML</a>( <span class='pre'>html</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Set the innerHTML of this element ...</div><div class='long'><p>Set the innerHTML of this element</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>html</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The new HTML</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setHeight' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-setHeight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-setHeight' class='name expandable'>setHeight</a>( <span class='pre'>height</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></div><div class='description'><div class='short'>Set the height of this Element. ...</div><div class='long'><p>Set the height of this Element.</p>\n\n<pre><code>// change the height to 200px and animate with default configuration\n<a href=\"#!/api/Ext-method-fly\" rel=\"Ext-method-fly\" class=\"docClass\">Ext.fly</a>('elementId').setHeight(200, true);\n\n// change the height to 150px and animate with a custom configuration\n<a href=\"#!/api/Ext-method-fly\" rel=\"Ext-method-fly\" class=\"docClass\">Ext.fly</a>('elId').setHeight(150, {\n    duration : 500, // animation will have a duration of .5 seconds\n    // will change the content to \"finished\"\n    callback: function(){ this.<a href=\"#!/api/Ext.dom.AbstractElement-method-update\" rel=\"Ext.dom.AbstractElement-method-update\" class=\"docClass\">update</a>(\"finished\"); }\n});\n</code></pre>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>height</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The new height. This may be one of:</p>\n\n<ul>\n<li>A Number specifying the new height in this Element's <a href=\"#!/api/Ext.dom.AbstractElement-property-defaultUnit\" rel=\"Ext.dom.AbstractElement-property-defaultUnit\" class=\"docClass\">defaultUnit</a>s (by default, pixels.)</li>\n<li>A String used to set the CSS height style. Animation may <strong>not</strong> be used.</li>\n</ul>\n\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setSize' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-setSize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-setSize' class='name expandable'>setSize</a>( <span class='pre'>width, height</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></div><div class='description'><div class='short'>Set the size of this Element. ...</div><div class='long'><p>Set the size of this Element. If animation is true, both width and height will be animated concurrently.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>width</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The new width. This may be one of:</p>\n\n<ul>\n<li>A Number specifying the new width in this Element's <a href=\"#!/api/Ext.dom.AbstractElement-property-defaultUnit\" rel=\"Ext.dom.AbstractElement-property-defaultUnit\" class=\"docClass\">defaultUnit</a>s (by default, pixels).</li>\n<li>A String used to set the CSS width style. Animation may <strong>not</strong> be used.</li>\n<li>A size object in the format <code>{width: widthValue, height: heightValue}</code>.</li>\n</ul>\n\n</div></li><li><span class='pre'>height</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The new height. This may be one of:</p>\n\n<ul>\n<li>A Number specifying the new height in this Element's <a href=\"#!/api/Ext.dom.AbstractElement-property-defaultUnit\" rel=\"Ext.dom.AbstractElement-property-defaultUnit\" class=\"docClass\">defaultUnit</a>s (by default, pixels).</li>\n<li>A String used to set the CSS height style. Animation may <strong>not</strong> be used.</li>\n</ul>\n\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setStyle' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-setStyle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-setStyle' class='name expandable'>setStyle</a>( <span class='pre'>property, [value]</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></div><div class='description'><div class='short'>Wrapper for setting style properties, also takes single object parameter of multiple styles. ...</div><div class='long'><p>Wrapper for setting style properties, also takes single object parameter of multiple styles.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>property</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The style property to be set, or an object of multiple styles.</p>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The value to apply to the given property, or null if an object was passed.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setVisibilityMode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-setVisibilityMode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-setVisibilityMode' class='name expandable'>setVisibilityMode</a>( <span class='pre'>mode</span> ) : <a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Use this to change the visibility mode between VISIBILITY, DISPLAY, OFFSETS or ASCLASS. ...</div><div class='long'><p>Use this to change the visibility mode between <a href=\"#!/api/Ext.dom.AbstractElement-static-property-VISIBILITY\" rel=\"Ext.dom.AbstractElement-static-property-VISIBILITY\" class=\"docClass\">VISIBILITY</a>, <a href=\"#!/api/Ext.dom.AbstractElement-static-property-DISPLAY\" rel=\"Ext.dom.AbstractElement-static-property-DISPLAY\" class=\"docClass\">DISPLAY</a>, <a href=\"#!/api/Ext.dom.AbstractElement-static-property-OFFSETS\" rel=\"Ext.dom.AbstractElement-static-property-OFFSETS\" class=\"docClass\">OFFSETS</a> or <a href=\"#!/api/Ext.dom.AbstractElement-static-property-ASCLASS\" rel=\"Ext.dom.AbstractElement-static-property-ASCLASS\" class=\"docClass\">ASCLASS</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>mode</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setVisible' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-setVisible' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-setVisible' class='name expandable'>setVisible</a>( <span class='pre'>visible, [animate]</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></div><div class='description'><div class='short'>Sets the visibility of the element (see details). ...</div><div class='long'><p>Sets the visibility of the element (see details). If the visibilityMode is set to Element.DISPLAY, it will use\nthe display property to hide the element, otherwise it uses visibility. The default is to hide and show using the visibility property.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>visible</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>Whether the element is visible</p>\n</div></li><li><span class='pre'>animate</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>True for the default animation, or a standard Element animation config object</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setWidth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-setWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-setWidth' class='name expandable'>setWidth</a>( <span class='pre'>width</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></div><div class='description'><div class='short'>Set the width of this Element. ...</div><div class='long'><p>Set the width of this Element.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>width</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The new width. This may be one of:</p>\n\n<ul>\n<li>A Number specifying the new width in this Element's <a href=\"#!/api/Ext.dom.AbstractElement-property-defaultUnit\" rel=\"Ext.dom.AbstractElement-property-defaultUnit\" class=\"docClass\">defaultUnit</a>s (by default, pixels).</li>\n<li>A String used to set the CSS width style. Animation may <strong>not</strong> be used.</li>\n</ul>\n\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-show' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-show' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-show' class='name expandable'>show</a>( <span class='pre'>[animate]</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Show this element - Uses display mode to determine whether to use \"display\" or \"visibility\". ...</div><div class='long'><p>Show this element - Uses display mode to determine whether to use \"display\" or \"visibility\". See <a href=\"#!/api/Ext.dom.AbstractElement-method-setVisible\" rel=\"Ext.dom.AbstractElement-method-setVisible\" class=\"docClass\">setVisible</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>animate</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>true for the default animation or a standard Element animation config object</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-toggleCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-toggleCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-toggleCls' class='name expandable'>toggleCls</a>( <span class='pre'>className</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></div><div class='description'><div class='short'>Toggles the specified CSS class on this element (removes it if it already exists, otherwise adds it). ...</div><div class='long'><p>Toggles the specified CSS class on this element (removes it if it already exists, otherwise adds it).</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>className</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The CSS class to toggle</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-unmask' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_style.html#Ext-dom-AbstractElement-method-unmask' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-unmask' class='name expandable'>unmask</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Removes a previously applied mask. ...</div><div class='long'><p>Removes a previously applied mask.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_style.</strong></p>\n</div></div></div><div id='method-up' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_traversal.html#Ext-dom-AbstractElement-method-up' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-up' class='name expandable'>up</a>( <span class='pre'>selector, [limit], [returnDom]</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></div><div class='description'><div class='short'>Walks up the DOM looking for a parent node that matches the passed simple selector (e.g. ...</div><div class='long'><p>Walks up the DOM looking for a parent node that matches the passed simple selector (e.g. div.some-class or span:first-child).\nThis is a shortcut for findParentNode() that always returns an <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_traversal.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>selector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The simple selector to test</p>\n</div></li><li><span class='pre'>limit</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a> (optional)<div class='sub-desc'><p>The max depth to search as a number or an element which causes the upward traversal to stop\nand is <b>not</b> considered for inclusion as the result. (defaults to 50 || document.documentElement)</p>\n</div></li><li><span class='pre'>returnDom</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return the DOM node instead of <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span><div class='sub-desc'><p>The matching DOM node (or null if no match was found)</p>\n</div></li></ul></div></div></div><div id='method-update' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-update' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-update' class='name expandable'>update</a>( <span class='pre'>html</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Update the innerHTML of this element ...</div><div class='long'><p>Update the innerHTML of this element</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>html</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The new HTML</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-wrap' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_insertion.html#Ext-dom-AbstractElement-method-wrap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-wrap' class='name expandable'>wrap</a>( <span class='pre'>[config], [returnDom], [selector]</span> ) : HTMLElement/<a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></div><div class='description'><div class='short'>Creates and wraps this element with another element\n\nDefined in override Ext.dom.AbstractElement_insertion. ...</div><div class='long'><p>Creates and wraps this element with another element</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_insertion.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>DomHelper element config object for the wrapper element or null for an empty div</p>\n</div></li><li><span class='pre'>returnDom</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to return the raw DOM element instead of <a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>selector</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>A <a href=\"#!/api/Ext.dom.Query\" rel=\"Ext.dom.Query\" class=\"docClass\">DomQuery</a> selector to select a descendant node within the created element to use as the wrapping element.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>HTMLElement/<a href=\"#!/api/Ext.dom.AbstractElement\" rel=\"Ext.dom.AbstractElement\" class=\"docClass\">Ext.dom.AbstractElement</a></span><div class='sub-desc'><p>The newly created wrapper element</p>\n</div></li></ul></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-addMethods' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-addMethods' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-addMethods' class='name expandable'>addMethods</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-addToCache' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-method-addToCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-addToCache' class='name expandable'>addToCache</a>( <span class='pre'>el, id</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>id</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addUnits' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-static-method-addUnits' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-method-addUnits' class='name expandable'>addUnits</a>( <span class='pre'>size, units</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Test if size has a unit, otherwise appends the passed unit string, or the default for this Element. ...</div><div class='long'><p>Test if size has a unit, otherwise appends the passed unit string, or the default for this Element.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>size</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>{Object} The size to set</p>\n</div></li><li><span class='pre'>units</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>{String} The units to append to a numeric size value</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-camelReplaceFn' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-method-camelReplaceFn' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-method-camelReplaceFn' class='name expandable'>camelReplaceFn</a>( <span class='pre'>m, a</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>private\n\nDefined in override Ext.dom.AbstractElement_static. ...</div><div class='long'><p>private</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>m</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>a</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-create' class='name expandable'>create</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create a new instance of this Class. ...</div><div class='long'><p>Create a new instance of this Class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.create({\n    someConfig: true\n});\n</code></pre>\n\n<p>All parameters are passed to the constructor of the class.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the created instance.</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-fly' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-static-method-fly' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-method-fly' class='name expandable'>fly</a>( <span class='pre'>dom, [named]</span> ) : <a href=\"#!/api/Ext.dom.Element.Fly\" rel=\"Ext.dom.Element.Fly\" class=\"docClass\">Ext.dom.Element.Fly</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Gets the singleton flyweight element, with the passed node as the active element. ...</div><div class='long'><p>Gets the singleton <a href=\"#!/api/Ext.dom.Element.Fly\" rel=\"Ext.dom.Element.Fly\" class=\"docClass\">flyweight</a> element, with the passed node as the active element.</p>\n\n<p>Because it is a singleton, this Flyweight does not have an ID, and must be used and discarded in a single line.\nYou may not keep and use the reference to this singleton over multiple lines because methods that you call\nmay themselves make use of <a href=\"#!/api/Ext-method-fly\" rel=\"Ext-method-fly\" class=\"docClass\">Ext.fly</a> and may change the DOM element to which the instance refers.</p>\n\n<p><a href=\"#!/api/Ext-method-fly\" rel=\"Ext-method-fly\" class=\"docClass\">Ext.fly</a> is alias for <a href=\"#!/api/Ext.dom.AbstractElement-static-method-fly\" rel=\"Ext.dom.AbstractElement-static-method-fly\" class=\"docClass\">fly</a>.</p>\n\n<p>Use this to make one-time references to DOM elements which are not going to be accessed again either by\napplication code, or by Ext's classes. If accessing an element which will be processed regularly, then <a href=\"#!/api/Ext-method-get\" rel=\"Ext-method-get\" class=\"docClass\">Ext.get</a> will be more appropriate to take advantage of the caching provided by the <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>\nclass.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>dom</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement<div class='sub-desc'><p>The dom node or id</p>\n</div></li><li><span class='pre'>named</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Allows for creation of named reusable flyweights to prevent conflicts (e.g.\ninternally Ext uses \"_global\")</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element.Fly\" rel=\"Ext.dom.Element.Fly\" class=\"docClass\">Ext.dom.Element.Fly</a></span><div class='sub-desc'><p>The singleton flyweight object (or null if no matching element was found)</p>\n</div></li></ul></div></div></div><div id='static-method-fromPoint' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-static-method-fromPoint' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-method-fromPoint' class='name expandable'>fromPoint</a>( <span class='pre'>x, y</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Returns the top Element that is located at the passed coordinates\n\nDefined in override Ext.dom.AbstractElement_static. ...</div><div class='long'><p>Returns the top Element that is located at the passed coordinates</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>x</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The x coordinate</p>\n</div></li><li><span class='pre'>y</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The y coordinate</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The found Element</p>\n</div></li></ul></div></div></div><div id='static-method-get' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-static-method-get' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-method-get' class='name expandable'>get</a>( <span class='pre'>el</span> ) : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Retrieves Ext.dom.Element objects. ...</div><div class='long'><p>Retrieves <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a> objects. <a href=\"#!/api/Ext-method-get\" rel=\"Ext-method-get\" class=\"docClass\">Ext.get</a> is alias for <a href=\"#!/api/Ext.dom.Element-static-method-get\" rel=\"Ext.dom.Element-static-method-get\" class=\"docClass\">Ext.dom.Element.get</a>.</p>\n\n<p><strong>This method does not retrieve <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Component</a>s.</strong> This method retrieves <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a>\nobjects which encapsulate DOM elements. To retrieve a Component by its ID, use <a href=\"#!/api/Ext.ComponentManager-method-get\" rel=\"Ext.ComponentManager-method-get\" class=\"docClass\">Ext.ComponentManager.get</a>.</p>\n\n<p>When passing an id, it should not include the <code>#</code> character that is used for a css selector.</p>\n\n<pre><code>// For an element with id 'foo'\n<a href=\"#!/api/Ext-method-get\" rel=\"Ext-method-get\" class=\"docClass\">Ext.get</a>('foo'); // Correct\n<a href=\"#!/api/Ext-method-get\" rel=\"Ext-method-get\" class=\"docClass\">Ext.get</a>('#foo'); // Incorrect\n</code></pre>\n\n<p>Uses simple caching to consistently return the same object. Automatically fixes if an object was recreated with\nthe same id via AJAX or DOM.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The id of the node, a DOM Node or an existing Element.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a></span><div class='sub-desc'><p>The Element object (or null if no matching element was found)</p>\n</div></li></ul></div></div></div><div id='static-method-getDocumentHeight' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-static-method-getDocumentHeight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-method-getDocumentHeight' class='name expandable'>getDocumentHeight</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Retrieves the document height\n\nDefined in override Ext.dom.AbstractElement_static. ...</div><div class='long'><p>Retrieves the document height</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>documentHeight</p>\n</div></li></ul></div></div></div><div id='static-method-getDocumentWidth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-static-method-getDocumentWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-method-getDocumentWidth' class='name expandable'>getDocumentWidth</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Retrieves the document width\n\nDefined in override Ext.dom.AbstractElement_static. ...</div><div class='long'><p>Retrieves the document width</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>documentWidth</p>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-getOrientation' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-static-method-getOrientation' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-method-getOrientation' class='name expandable'>getOrientation</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Retrieves the current orientation of the window. ...</div><div class='long'><p>Retrieves the current orientation of the window. This is calculated by\ndeterming if the height is greater than the width.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Orientation of window: 'portrait' or 'landscape'</p>\n</div></li></ul></div></div></div><div id='static-method-getViewSize' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-static-method-getViewSize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-method-getViewSize' class='name expandable'>getViewSize</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Retrieves the viewport size of the window. ...</div><div class='long'><p>Retrieves the viewport size of the window.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>object containing width and height properties</p>\n</div></li></ul></div></div></div><div id='static-method-getViewportHeight' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-static-method-getViewportHeight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-method-getViewportHeight' class='name expandable'>getViewportHeight</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Retrieves the viewport height of the window. ...</div><div class='long'><p>Retrieves the viewport height of the window.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>viewportHeight</p>\n</div></li></ul></div></div></div><div id='static-method-getViewportWidth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-static-method-getViewportWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-method-getViewportWidth' class='name expandable'>getViewportWidth</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Retrieves the viewport width of the window. ...</div><div class='long'><p>Retrieves the viewport width of the window.</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>viewportWidth</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-isAncestor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-static-method-isAncestor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-method-isAncestor' class='name expandable'>isAncestor</a>( <span class='pre'>p, c</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Defined in override Ext.dom.AbstractElement_static. ...</div><div class='long'><p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>p</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>c</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-mergeClsList' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-static-method-mergeClsList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-method-mergeClsList' class='name expandable'>mergeClsList</a>( <span class='pre'>clsList1, clsList2</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Returns an array of unique class names based upon the input strings, or string arrays. ...</div><div class='long'><p>Returns an array of unique class names based upon the input strings, or string arrays.</p>\n\n\n<p>The number of parameters is unlimited.</p>\n\n\n<p>Example</p>\n\n\n<pre><code>// Add x-invalid and x-mandatory classes, do not duplicate\nmyElement.dom.className = <a href=\"#!/api/Ext.dom.Element-static-method-mergeClsList\" rel=\"Ext.dom.Element-static-method-mergeClsList\" class=\"docClass\">Ext.core.Element.mergeClsList</a>(this.initialClasses, 'x-invalid x-mandatory');\n</code></pre>\n\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>clsList1</span> : Mixed<div class='sub-desc'><p>A string of class names, or an array of class names.</p>\n</div></li><li><span class='pre'>clsList2</span> : Mixed<div class='sub-desc'><p>A string of class names, or an array of class names.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>An array of strings representing remaining unique, merged class names. If class names were added to the first list, the <code>changed</code> property will be <code>true</code>.</p>\n</div></li></ul></div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-normalize' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-static-method-normalize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-method-normalize' class='name expandable'>normalize</a>( <span class='pre'>prop</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Normalizes CSS property keys from dash delimited to camel case JavaScript Syntax. ...</div><div class='long'><p>Normalizes CSS property keys from dash delimited to camel case JavaScript Syntax.\nFor example:</p>\n\n<ul>\n<li>border-width -> borderWidth</li>\n<li>padding-top -> paddingTop</li>\n</ul>\n\n\n<p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>prop</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The property to normalize</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The normalized string</p>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-parseBox' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-static-method-parseBox' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-method-parseBox' class='name expandable'>parseBox</a>( <span class='pre'>box</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Parses a number or string representing margin sizes into an object. ...</div><div class='long'><p>Parses a number or string representing margin sizes into an object. Supports CSS-style margin declarations\n(e.g. 10, \"10\", \"10 10\", \"10 10 10\" and \"10 10 10 10\" are all valid options and would return the same result)</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>box</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The encoded margins</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>An object with margin sizes for top, right, bottom and left</p>\n</div></li></ul></div></div></div><div id='static-method-parseStyles' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-static-method-parseStyles' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-method-parseStyles' class='name expandable'>parseStyles</a>( <span class='pre'>styles</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Converts a CSS string into an object with a property for each style. ...</div><div class='long'><p>Converts a CSS string into an object with a property for each style.</p>\n\n<p>The sample code below would return an object with 2 properties, one\nfor background-color and one for color.</p>\n\n<pre><code>var css = 'background-color: red;color: blue; ';\nconsole.log(<a href=\"#!/api/Ext.dom.Element-static-method-parseStyles\" rel=\"Ext.dom.Element-static-method-parseStyles\" class=\"docClass\">Ext.dom.Element.parseStyles</a>(css));\n</code></pre>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>styles</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>A CSS string</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>styles</p>\n</div></li></ul></div></div></div><div id='static-method-removeCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement.html#Ext-dom-AbstractElement-static-method-removeCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-method-removeCls' class='name expandable'>removeCls</a>( <span class='pre'>existingClsList, removeClsList</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Returns an array of unique class names deom the first parameter with all class names\nfrom the second parameter removed. ...</div><div class='long'><p>Returns an array of unique class names deom the first parameter with all class names\nfrom the second parameter removed.</p>\n\n\n<p>Example</p>\n\n\n<pre><code>// Remove x-invalid and x-mandatory classes if present.\nmyElement.dom.className = <a href=\"#!/api/Ext.dom.Element-method-removeCls\" rel=\"Ext.dom.Element-method-removeCls\" class=\"docClass\">Ext.core.Element.removeCls</a>(this.initialClasses, 'x-invalid x-mandatory');\n</code></pre>\n\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>existingClsList</span> : Mixed<div class='sub-desc'><p>A string of class names, or an array of class names.</p>\n</div></li><li><span class='pre'>removeClsList</span> : Mixed<div class='sub-desc'><p>A string of class names, or an array of class names to remove from <code>existingClsList</code>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>An array of strings representing remaining class names. If class names were removed, the <code>changed</code> property will be <code>true</code>.</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='static-method-unitizeBox' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.dom.AbstractElement'>Ext.dom.AbstractElement</span><br/><a href='source/AbstractElement_static.html#Ext-dom-AbstractElement-static-method-unitizeBox' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.dom.AbstractElement-static-method-unitizeBox' class='name expandable'>unitizeBox</a>( <span class='pre'>box, units</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Parses a number or string representing margin sizes into an object. ...</div><div class='long'><p>Parses a number or string representing margin sizes into an object. Supports CSS-style margin declarations\n(e.g. 10, \"10\", \"10 10\", \"10 10 10\" and \"10 10 10 10\" are all valid options and would return the same result)</p>\n\n<p><strong>Defined in override Ext.dom.AbstractElement_static.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>box</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The encoded margins, or an object with top, right,\nbottom, and left properties</p>\n</div></li><li><span class='pre'>units</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The type of units to add</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>An string with unitized (px if units is not specified) metrics for top, right, bottom and left</p>\n</div></li></ul></div></div></div></div></div></div></div>","superclasses":["Ext.Base"],"meta":{"private":true},"code_type":"ext_define","requires":["Ext.EventManager","Ext.dom.AbstractElement_insertion","Ext.dom.AbstractElement_static","Ext.dom.AbstractElement_style","Ext.dom.AbstractElement_traversal"],"html_meta":{"private":null},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"ASCLASS","id":"static-property-ASCLASS"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"DISPLAY","id":"static-property-DISPLAY"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"OFFSETS","id":"static-property-OFFSETS"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"VISIBILITY","id":"static-property-VISIBILITY"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{"static":true,"private":true},"name":"borders","id":"property-borders"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{"static":true,"private":true},"name":"camelRe","id":"property-camelRe"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{"static":true,"private":true},"name":"cssRe","id":"property-cssRe"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{"static":true,"private":true},"name":"margins","id":"property-margins"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{"static":true,"private":true},"name":"msRe","id":"property-msRe"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{"static":true,"private":true},"name":"opacityRe","id":"property-opacityRe"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{"static":true,"private":true},"name":"paddings","id":"property-paddings"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{"static":true,"private":true},"name":"propertyCache","id":"property-propertyCache"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{"static":true,"private":true},"name":"trimRe","id":"property-trimRe"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{"static":true,"private":true},"name":"unitRe","id":"property-unitRe"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{"static":true,"private":true},"name":"whitespaceRe","id":"property-whitespaceRe"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true,"private":true},"name":"addMethods","id":"method-addMethods"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true,"private":true},"name":"addToCache","id":"method-addToCache"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true,"private":true},"name":"addUnits","id":"static-method-addUnits"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true,"private":true},"name":"camelReplaceFn","id":"method-camelReplaceFn"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"fly","id":"static-method-fly"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"fromPoint","id":"static-method-fromPoint"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"get","id":"static-method-get"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"getDocumentHeight","id":"static-method-getDocumentHeight"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"getDocumentWidth","id":"static-method-getDocumentWidth"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"getOrientation","id":"static-method-getOrientation"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"getViewSize","id":"static-method-getViewSize"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"getViewportHeight","id":"static-method-getViewportHeight"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"getViewportWidth","id":"static-method-getViewportWidth"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true,"private":true},"name":"isAncestor","id":"static-method-isAncestor"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"mergeClsList","id":"static-method-mergeClsList"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"normalize","id":"static-method-normalize"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"parseBox","id":"static-method-parseBox"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"parseStyles","id":"static-method-parseStyles"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"removeCls","id":"static-method-removeCls"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"static":true},"name":"unitizeBox","id":"static-method-unitizeBox"}],"event":[],"css_mixin":[]},"files":[{"href":"AbstractElement.html#Ext-dom-AbstractElement","filename":"AbstractElement.js"},{"href":"AbstractElement_static.html#Ext-dom-AbstractElement_static","filename":"AbstractElement_static.js"},{"href":"AbstractElement_insertion.html#Ext-dom-AbstractElement_insertion","filename":"AbstractElement_insertion.js"},{"href":"AbstractElement_traversal.html#Ext-dom-AbstractElement_traversal","filename":"AbstractElement_traversal.js"},{"href":"AbstractElement_style.html#Ext-dom-AbstractElement_style","filename":"AbstractElement_style.js"}],"linenr":4,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{},"name":"defaultUnit","id":"property-defaultUnit"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{},"name":"dom","id":"property-dom"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{},"name":"id","id":"property-id"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"},{"tagname":"property","owner":"Ext.dom.AbstractElement","meta":{"private":true},"name":"styleHooks","id":"property-styleHooks"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"addCls","id":"method-addCls"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"appendChild","id":"method-appendChild"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"chainable":true},"name":"appendTo","id":"method-appendTo"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"applyStyles","id":"method-applyStyles"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"child","id":"method-child"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"contains","id":"method-contains"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"createChild","id":"method-createChild"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"down","id":"method-down"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"findParent","id":"method-findParent"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"findParentNode","id":"method-findParentNode"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"first","id":"method-first"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"getActiveElement","id":"method-getActiveElement"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"getAttribute","id":"method-getAttribute"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"getBorderWidth","id":"method-getBorderWidth"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"getById","id":"method-getById"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"private":true},"name":"getCache","id":"method-getCache"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"getHTML","id":"method-getHTML"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"getHeight","id":"method-getHeight"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"getMargin","id":"method-getMargin"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"getPadding","id":"method-getPadding"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"private":true},"name":"getRightMarginFixCleaner","id":"method-getRightMarginFixCleaner"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"getSize","id":"method-getSize"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"getStyle","id":"method-getStyle"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"getValue","id":"method-getValue"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"getViewSize","id":"method-getViewSize"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"private":true},"name":"getVisibilityMode","id":"method-getVisibilityMode"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"getWidth","id":"method-getWidth"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"hasCls","id":"method-hasCls"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"chainable":true},"name":"hide","id":"method-hide"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"chainable":true},"name":"insertAfter","id":"method-insertAfter"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"chainable":true},"name":"insertBefore","id":"method-insertBefore"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"insertFirst","id":"method-insertFirst"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"insertHtml","id":"method-insertHtml"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"insertSibling","id":"method-insertSibling"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"is","id":"method-is"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"private":true},"name":"isAncestor","id":"method-isAncestor"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"isStyle","id":"method-isStyle"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"isTransparent","id":"method-isTransparent"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"last","id":"method-last"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"mask","id":"method-mask"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"private":true},"name":"matchNode","id":"method-matchNode"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"next","id":"method-next"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"parent","id":"method-parent"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"prev","id":"method-prev"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"query","id":"method-query"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"radioCls","id":"method-radioCls"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"remove","id":"method-remove"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"removeCls","id":"method-removeCls"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"chainable":true},"name":"repaint","id":"method-repaint"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"chainable":true},"name":"replace","id":"method-replace"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"replaceCls","id":"method-replaceCls"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"replaceWith","id":"method-replaceWith"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"select","id":"method-select"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"serializeForm","id":"method-serializeForm"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"chainable":true},"name":"set","id":"method-set"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"chainable":true},"name":"setHTML","id":"method-setHTML"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"setHeight","id":"method-setHeight"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"setSize","id":"method-setSize"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"setStyle","id":"method-setStyle"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"chainable":true},"name":"setVisibilityMode","id":"method-setVisibilityMode"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"setVisible","id":"method-setVisible"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"setWidth","id":"method-setWidth"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"chainable":true},"name":"show","id":"method-show"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"toggleCls","id":"method-toggleCls"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"unmask","id":"method-unmask"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"up","id":"method-up"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{"chainable":true},"name":"update","id":"method-update"},{"tagname":"method","owner":"Ext.dom.AbstractElement","meta":{},"name":"wrap","id":"method-wrap"}],"event":[],"css_mixin":[]},"inheritable":null,"private":true,"component":false,"name":"Ext.dom.AbstractElement","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.dom.AbstractElement","mixins":[],"mixedInto":[]});