Ext.data.JsonP.Ext_is({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Support.html#Ext-is' target='_blank'>Support.js</a></div></pre><div class='doc-contents'><p>Determines information about the current platform the application is running on.</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-Android' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.is'>Ext.is</span><br/><a href='source/Support.html#Ext-is-property-Android' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.is-property-Android' class='name not-expandable'>Android</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True when the browser is running on an Android device</p>\n</div><div class='long'><p>True when the browser is running on an Android device</p>\n</div></div></div><div id='property-Blackberry' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.is'>Ext.is</span><br/><a href='source/Support.html#Ext-is-property-Blackberry' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.is-property-Blackberry' class='name not-expandable'>Blackberry</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True when the browser is running on a Blackberry</p>\n</div><div class='long'><p>True when the browser is running on a Blackberry</p>\n</div></div></div><div id='property-Desktop' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.is'>Ext.is</span><br/><a href='source/Support.html#Ext-is-property-Desktop' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.is-property-Desktop' class='name not-expandable'>Desktop</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the browser is running on a desktop machine</p>\n</div><div class='long'><p>True if the browser is running on a desktop machine</p>\n</div></div></div><div id='property-Linux' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.is'>Ext.is</span><br/><a href='source/Support.html#Ext-is-property-Linux' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.is-property-Linux' class='name not-expandable'>Linux</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True when the browser is running on Linux</p>\n</div><div class='long'><p>True when the browser is running on Linux</p>\n</div></div></div><div id='property-Mac' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.is'>Ext.is</span><br/><a href='source/Support.html#Ext-is-property-Mac' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.is-property-Mac' class='name not-expandable'>Mac</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True when the browser is running on a Mac</p>\n</div><div class='long'><p>True when the browser is running on a Mac</p>\n</div></div></div><div id='property-Phone' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.is'>Ext.is</span><br/><a href='source/Support.html#Ext-is-property-Phone' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.is-property-Phone' class='name not-expandable'>Phone</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the browser is running on a phone.</p>\n</div><div class='long'><p>True if the browser is running on a phone.</p>\n</div></div></div><div id='property-Standalone' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.is'>Ext.is</span><br/><a href='source/Support.html#Ext-is-property-Standalone' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.is-property-Standalone' class='name not-expandable'>Standalone</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>Detects when application has been saved to homescreen.</p>\n</div><div class='long'><p>Detects when application has been saved to homescreen.</p>\n</div></div></div><div id='property-Tablet' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.is'>Ext.is</span><br/><a href='source/Support.html#Ext-is-property-Tablet' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.is-property-Tablet' class='name not-expandable'>Tablet</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'><p>True if the browser is running on a tablet (iPad)</p>\n</div><div class='long'><p>True if the browser is running on a tablet (iPad)</p>\n</div></div></div><div id='property-Windows' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.is'>Ext.is</span><br/><a href='source/Support.html#Ext-is-property-Windows' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.is-property-Windows' class='name not-expandable'>Windows</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True when the browser is running on Windows</p>\n</div><div class='long'><p>True when the browser is running on Windows</p>\n</div></div></div><div id='property-iOS' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.is'>Ext.is</span><br/><a href='source/Support.html#Ext-is-property-iOS' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.is-property-iOS' class='name not-expandable'>iOS</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if the browser is running on iOS</p>\n</div><div class='long'><p>True if the browser is running on iOS</p>\n</div></div></div><div id='property-iPad' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.is'>Ext.is</span><br/><a href='source/Support.html#Ext-is-property-iPad' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.is-property-iPad' class='name not-expandable'>iPad</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True when the browser is running on a iPad</p>\n</div><div class='long'><p>True when the browser is running on a iPad</p>\n</div></div></div><div id='property-iPhone' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.is'>Ext.is</span><br/><a href='source/Support.html#Ext-is-property-iPhone' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.is-property-iPhone' class='name not-expandable'>iPhone</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True when the browser is running on a iPhone</p>\n</div><div class='long'><p>True when the browser is running on a iPhone</p>\n</div></div></div><div id='property-iPod' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.is'>Ext.is</span><br/><a href='source/Support.html#Ext-is-property-iPod' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.is-property-iPod' class='name not-expandable'>iPod</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True when the browser is running on a iPod</p>\n</div><div class='long'><p>True when the browser is running on a iPod</p>\n</div></div></div></div></div></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"Support.html#Ext-is","filename":"Support.js"}],"linenr":5,"members":{"property":[{"tagname":"property","owner":"Ext.is","meta":{},"name":"Android","id":"property-Android"},{"tagname":"property","owner":"Ext.is","meta":{},"name":"Blackberry","id":"property-Blackberry"},{"tagname":"property","owner":"Ext.is","meta":{},"name":"Desktop","id":"property-Desktop"},{"tagname":"property","owner":"Ext.is","meta":{},"name":"Linux","id":"property-Linux"},{"tagname":"property","owner":"Ext.is","meta":{},"name":"Mac","id":"property-Mac"},{"tagname":"property","owner":"Ext.is","meta":{},"name":"Phone","id":"property-Phone"},{"tagname":"property","owner":"Ext.is","meta":{},"name":"Standalone","id":"property-Standalone"},{"tagname":"property","owner":"Ext.is","meta":{},"name":"Tablet","id":"property-Tablet"},{"tagname":"property","owner":"Ext.is","meta":{},"name":"Windows","id":"property-Windows"},{"tagname":"property","owner":"Ext.is","meta":{},"name":"iOS","id":"property-iOS"},{"tagname":"property","owner":"Ext.is","meta":{},"name":"iPad","id":"property-iPad"},{"tagname":"property","owner":"Ext.is","meta":{},"name":"iPhone","id":"property-iPhone"},{"tagname":"property","owner":"Ext.is","meta":{},"name":"iPod","id":"property-iPod"}],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.is","singleton":true,"override":null,"inheritdoc":null,"id":"class-Ext.is","mixins":[],"mixedInto":[]});