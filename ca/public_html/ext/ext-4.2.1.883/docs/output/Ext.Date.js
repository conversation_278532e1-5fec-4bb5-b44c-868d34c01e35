Ext.data.JsonP.Ext_Date({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Date2.html#Ext-Date' target='_blank'>Date.js</a></div></pre><div class='doc-contents'><p>A set of useful static methods to deal with date\nNote that if <a href=\"#!/api/Ext.Date\" rel=\"Ext.Date\" class=\"docClass\">Ext.Date</a> is required and loaded, it will copy all methods / properties to\nthis object for convenience</p>\n\n<p>The date parsing and formatting syntax contains a subset of\n<a href=\"http://www.php.net/date\">PHP's <code>date()</code> function</a>, and the formats that are\nsupported will provide results equivalent to their PHP versions.</p>\n\n<p>The following is a list of all currently supported formats:</p>\n\n<pre class=\"\">\nFormat      Description                                                               Example returned values\n------      -----------------------------------------------------------------------   -----------------------\n  d         Day of the month, 2 digits with leading zeros                             01 to 31\n  D         A short textual representation of the day of the week                     Mon to Sun\n  j         Day of the month without leading zeros                                    1 to 31\n  l         A full textual representation of the day of the week                      Sunday to Saturday\n  N         ISO-8601 numeric representation of the day of the week                    1 (for Monday) through 7 (for Sunday)\n  S         English ordinal suffix for the day of the month, 2 characters             st, nd, rd or th. Works well with j\n  w         Numeric representation of the day of the week                             0 (for Sunday) to 6 (for Saturday)\n  z         The day of the year (starting from 0)                                     0 to 364 (365 in leap years)\n  W         ISO-8601 week number of year, weeks starting on Monday                    01 to 53\n  F         A full textual representation of a month, such as January or March        January to December\n  m         Numeric representation of a month, with leading zeros                     01 to 12\n  M         A short textual representation of a month                                 Jan to Dec\n  n         Numeric representation of a month, without leading zeros                  1 to 12\n  t         Number of days in the given month                                         28 to 31\n  L         Whether it&#39;s a leap year                                                  1 if it is a leap year, 0 otherwise.\n  o         ISO-8601 year number (identical to (Y), but if the ISO week number (W)    Examples: 1998 or 2004\n            belongs to the previous or next year, that year is used instead)\n  Y         A full numeric representation of a year, 4 digits                         Examples: 1999 or 2003\n  y         A two digit representation of a year                                      Examples: 99 or 03\n  a         Lowercase Ante meridiem and Post meridiem                                 am or pm\n  A         Uppercase Ante meridiem and Post meridiem                                 AM or PM\n  g         12-hour format of an hour without leading zeros                           1 to 12\n  G         24-hour format of an hour without leading zeros                           0 to 23\n  h         12-hour format of an hour with leading zeros                              01 to 12\n  H         24-hour format of an hour with leading zeros                              00 to 23\n  i         Minutes, with leading zeros                                               00 to 59\n  s         Seconds, with leading zeros                                               00 to 59\n  u         Decimal fraction of a second                                              Examples:\n            (minimum 1 digit, arbitrary number of digits allowed)                     001 (i.e. 0.001s) or\n                                                                                      100 (i.e. 0.100s) or\n                                                                                      999 (i.e. 0.999s) or\n                                                                                      999876543210 (i.e. 0.999876543210s)\n  O         Difference to Greenwich time (GMT) in hours and minutes                   Example: +1030\n  P         Difference to Greenwich time (GMT) with colon between hours and minutes   Example: -08:00\n  T         Timezone abbreviation of the machine running the code                     Examples: EST, MDT, PDT ...\n  Z         Timezone offset in seconds (negative if west of UTC, positive if east)    -43200 to 50400\n  c         ISO 8601 date\n            Notes:                                                                    Examples:\n            1) If unspecified, the month / day defaults to the current month / day,   1991 or\n               the time defaults to midnight, while the timezone defaults to the      1992-10 or\n               browser's timezone. If a time is specified, it must include both hours 1993-09-20 or\n               and minutes. The \"T\" delimiter, seconds, milliseconds and timezone     1994-08-19T16:20+01:00 or\n               are optional.                                                          1995-07-18T17:21:28-02:00 or\n            2) The decimal fraction of a second, if specified, must contain at        1996-06-17T18:22:29.98765+03:00 or\n               least 1 digit (there is no limit to the maximum number                 1997-05-16T19:23:30,12345-0400 or\n               of digits allowed), and may be delimited by either a '.' or a ','      1998-04-15T20:24:31.2468Z or\n            Refer to the examples on the right for the various levels of              1999-03-14T20:24:32Z or\n            date-time granularity which are supported, or see                         2000-02-13T21:25:33\n            http://www.w3.org/TR/NOTE-datetime for more info.                         2001-01-12 22:26:34\n  U         Seconds since the Unix Epoch (January 1 1970 00:00:00 GMT)                1193432466 or -2138434463\n  MS        Microsoft AJAX serialized dates                                           \\/Date(1238606590509)\\/ (i.e. UTC milliseconds since epoch) or\n                                                                                      \\/Date(1238606590509+0800)\\/\n  time      A javascript millisecond timestamp                                        1350024476440\n  timestamp A UNIX timestamp (same as U)                                              1350024866            \n</pre>\n\n\n<p>Example usage (note that you must escape format specifiers with '\\' to render them as character literals):</p>\n\n<pre><code>// Sample date:\n// 'Wed Jan 10 2007 15:05:01 GMT-0600 (Central Standard Time)'\n\nvar dt = new Date('1/10/2007 03:05:01 PM GMT-0600');\nconsole.log(<a href=\"#!/api/Ext.Date-method-format\" rel=\"Ext.Date-method-format\" class=\"docClass\">Ext.Date.format</a>(dt, 'Y-m-d'));                          // 2007-01-10\nconsole.log(<a href=\"#!/api/Ext.Date-method-format\" rel=\"Ext.Date-method-format\" class=\"docClass\">Ext.Date.format</a>(dt, 'F j, Y, g:i a'));                  // January 10, 2007, 3:05 pm\nconsole.log(<a href=\"#!/api/Ext.Date-method-format\" rel=\"Ext.Date-method-format\" class=\"docClass\">Ext.Date.format</a>(dt, 'l, \\\\t\\\\he jS \\\\of F Y h:i:s A')); // Wednesday, the 10th of January 2007 03:05:01 PM\n</code></pre>\n\n<p>Here are some standard date/time patterns that you might find helpful.  They\nare not part of the source of <a href=\"#!/api/Ext.Date\" rel=\"Ext.Date\" class=\"docClass\">Ext.Date</a>, but to use them you can simply copy this\nblock of code into any script that is included after <a href=\"#!/api/Ext.Date\" rel=\"Ext.Date\" class=\"docClass\">Ext.Date</a> and they will also become\nglobally available on the Date object.  Feel free to add or remove patterns as needed in your code.</p>\n\n<pre><code>Ext.Date.patterns = {\n    ISO8601Long:\"Y-m-d H:i:s\",\n    ISO8601Short:\"Y-m-d\",\n    ShortDate: \"n/j/Y\",\n    LongDate: \"l, F d, Y\",\n    FullDateTime: \"l, F d, Y g:i:s A\",\n    MonthDay: \"F d\",\n    ShortTime: \"g:i A\",\n    LongTime: \"g:i:s A\",\n    SortableDateTime: \"Y-m-d\\\\TH:i:s\",\n    UniversalSortableDateTime: \"Y-m-d H:i:sO\",\n    YearMonth: \"F, Y\"\n};\n</code></pre>\n\n<p>Example usage:</p>\n\n<pre><code>var dt = new Date();\nconsole.log(<a href=\"#!/api/Ext.Date-method-format\" rel=\"Ext.Date-method-format\" class=\"docClass\">Ext.Date.format</a>(dt, Ext.Date.patterns.ShortDate));\n</code></pre>\n\n<p>Developer-written, custom formats may be used by supplying both a formatting and a parsing function\nwhich perform to specialized requirements. The functions are stored in <a href=\"#!/api/Ext.Date-property-parseFunctions\" rel=\"Ext.Date-property-parseFunctions\" class=\"docClass\">parseFunctions</a> and <a href=\"#!/api/Ext.Date-property-formatFunctions\" rel=\"Ext.Date-property-formatFunctions\" class=\"docClass\">formatFunctions</a>.</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-DAY' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-property-DAY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-property-DAY' class='name expandable'>DAY</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Date interval constant ...</div><div class='long'><p>Date interval constant</p>\n<p>Defaults to: <code>&quot;d&quot;</code></p></div></div></div><div id='property-HOUR' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-property-HOUR' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-property-HOUR' class='name expandable'>HOUR</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Date interval constant ...</div><div class='long'><p>Date interval constant</p>\n<p>Defaults to: <code>&quot;h&quot;</code></p></div></div></div><div id='property-MILLI' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-property-MILLI' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-property-MILLI' class='name expandable'>MILLI</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Date interval constant ...</div><div class='long'><p>Date interval constant</p>\n<p>Defaults to: <code>&quot;ms&quot;</code></p></div></div></div><div id='property-MINUTE' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-property-MINUTE' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-property-MINUTE' class='name expandable'>MINUTE</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Date interval constant ...</div><div class='long'><p>Date interval constant</p>\n<p>Defaults to: <code>&quot;mi&quot;</code></p></div></div></div><div id='property-MONTH' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-property-MONTH' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-property-MONTH' class='name expandable'>MONTH</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Date interval constant ...</div><div class='long'><p>Date interval constant</p>\n<p>Defaults to: <code>&quot;mo&quot;</code></p></div></div></div><div id='property-SECOND' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-property-SECOND' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-property-SECOND' class='name expandable'>SECOND</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Date interval constant ...</div><div class='long'><p>Date interval constant</p>\n<p>Defaults to: <code>&quot;s&quot;</code></p></div></div></div><div id='property-YEAR' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-property-YEAR' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-property-YEAR' class='name expandable'>YEAR</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Date interval constant ...</div><div class='long'><p>Date interval constant</p>\n<p>Defaults to: <code>&quot;y&quot;</code></p></div></div></div><div id='property-dayNames' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-property-dayNames' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-property-dayNames' class='name expandable'>dayNames</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</span></div><div class='description'><div class='short'>An array of textual day names. ...</div><div class='long'><p>An array of textual day names.\nOverride these values for international dates.</p>\n\n<p>Example:</p>\n\n<pre><code><a href=\"#!/api/Ext.Date-property-dayNames\" rel=\"Ext.Date-property-dayNames\" class=\"docClass\">Ext.Date.dayNames</a> = [\n    'SundayInYourLang',\n    'MondayInYourLang'\n    // ...\n];\n</code></pre>\n<p>Defaults to: <code>[&quot;Sunday&quot;, &quot;Monday&quot;, &quot;Tuesday&quot;, &quot;Wednesday&quot;, &quot;Thursday&quot;, &quot;Friday&quot;, &quot;Saturday&quot;]</code></p></div></div></div><div id='property-defaultFormat' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-property-defaultFormat' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-property-defaultFormat' class='name expandable'>defaultFormat</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The date format string that the Ext.util.Format.dateRenderer\nand Ext.util.Format.date functions use. ...</div><div class='long'><p>The date format string that the <a href=\"#!/api/Ext.util.Format-method-dateRenderer\" rel=\"Ext.util.Format-method-dateRenderer\" class=\"docClass\">Ext.util.Format.dateRenderer</a>\nand <a href=\"#!/api/Ext.util.Format-method-date\" rel=\"Ext.util.Format-method-date\" class=\"docClass\">Ext.util.Format.date</a> functions use.  See <a href=\"#!/api/Ext.Date\" rel=\"Ext.Date\" class=\"docClass\">Ext.Date</a> for details.</p>\n\n<p>This may be overridden in a locale file.</p>\n<p>Defaults to: <code>&quot;m/d/Y&quot;</code></p></div></div></div><div id='property-defaults' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-property-defaults' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-property-defaults' class='name expandable'>defaults</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>An object hash containing default date values used during date parsing. ...</div><div class='long'><p>An object hash containing default date values used during date parsing.</p>\n\n<p>The following properties are available:<div class=\"mdetail-params\"><ul>\n<li><code>y</code> : Number<div class=\"sub-desc\">The default year value. (defaults to undefined)</div></li>\n<li><code>m</code> : Number<div class=\"sub-desc\">The default 1-based month value. (defaults to undefined)</div></li>\n<li><code>d</code> : Number<div class=\"sub-desc\">The default day value. (defaults to undefined)</div></li>\n<li><code>h</code> : Number<div class=\"sub-desc\">The default hour value. (defaults to undefined)</div></li>\n<li><code>i</code> : Number<div class=\"sub-desc\">The default minute value. (defaults to undefined)</div></li>\n<li><code>s</code> : Number<div class=\"sub-desc\">The default second value. (defaults to undefined)</div></li>\n<li><code>ms</code> : Number<div class=\"sub-desc\">The default millisecond value. (defaults to undefined)</div></li>\n</ul></div></p>\n\n<p>Override these properties to customize the default date values used by the <a href=\"#!/api/Ext.Date-method-parse\" rel=\"Ext.Date-method-parse\" class=\"docClass\">parse</a> method.</p>\n\n<p><strong>Note:</strong> In countries which experience Daylight Saving Time (i.e. DST), the <code>h</code>, <code>i</code>, <code>s</code>\nand <code>ms</code> properties may coincide with the exact time in which DST takes effect.\nIt is the responsibility of the developer to account for this.</p>\n\n<p>Example Usage:</p>\n\n<pre><code>// set default day value to the first day of the month\nExt.Date.defaults.d = 1;\n\n// parse a February date string containing only year and month values.\n// setting the default day value to 1 prevents weird date rollover issues\n// when attempting to parse the following date string on, for example, March 31st 2009.\n<a href=\"#!/api/Ext.Date-method-parse\" rel=\"Ext.Date-method-parse\" class=\"docClass\">Ext.Date.parse</a>('2009-02', 'Y-m'); // returns a Date object representing February 1st 2009\n</code></pre>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-formatCodes' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-property-formatCodes' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-property-formatCodes' class='name expandable'>formatCodes</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>The base format-code to formatting-function hashmap used by the format method. ...</div><div class='long'><p>The base format-code to formatting-function hashmap used by the <a href=\"#!/api/Ext.Date-method-format\" rel=\"Ext.Date-method-format\" class=\"docClass\">format</a> method.\nFormatting functions are strings (or functions which return strings) which\nwill return the appropriate value when evaluated in the context of the Date object\nfrom which the <a href=\"#!/api/Ext.Date-method-format\" rel=\"Ext.Date-method-format\" class=\"docClass\">format</a> method is called.\nAdd to / override these mappings for custom date formatting.</p>\n\n<p><strong>Note:</strong> <a href=\"#!/api/Ext.Date-method-format\" rel=\"Ext.Date-method-format\" class=\"docClass\">Ext.Date.format</a>() treats characters as literals if an appropriate mapping cannot be found.</p>\n\n<p>Example:</p>\n\n<pre><code>Ext.Date.formatCodes.x = \"<a href=\"#!/api/Ext.util.Format-method-leftPad\" rel=\"Ext.util.Format-method-leftPad\" class=\"docClass\">Ext.util.Format.leftPad</a>(this.getDate(), 2, '0')\";\nconsole.log(<a href=\"#!/api/Ext.Date-method-format\" rel=\"Ext.Date-method-format\" class=\"docClass\">Ext.Date.format</a>(new Date(), 'X'); // returns the current day of the month\n</code></pre>\n\n<p>@type Object</p>\n</div></div></div><div id='property-formatFunctions' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-property-formatFunctions' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-property-formatFunctions' class='name expandable'>formatFunctions</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>An object hash in which each property is a date formatting function. ...</div><div class='long'><p>An object hash in which each property is a date formatting function. The property name is the\nformat string which corresponds to the produced formatted date string.</p>\n\n<p>This object is automatically populated with date formatting functions as\ndate formats are requested for Ext standard formatting strings.</p>\n\n<p>Custom formatting functions may be inserted into this object, keyed by a name which from then on\nmay be used as a format string to <a href=\"#!/api/Ext.Date-method-format\" rel=\"Ext.Date-method-format\" class=\"docClass\">format</a>.</p>\n\n<p>Example:</p>\n\n<pre><code><a href=\"#!/api/Ext.Date-property-formatFunctions\" rel=\"Ext.Date-property-formatFunctions\" class=\"docClass\">Ext.Date.formatFunctions</a>['x-date-format'] = myDateFormatter;\n</code></pre>\n\n<p>A formatting function should return a string representation of the passed Date object, and is passed the following parameters:<div class=\"mdetail-params\"><ul>\n<li><code>date</code> : Date<div class=\"sub-desc\">The Date to format.</div></li>\n</ul></div></p>\n\n<p>To enable date strings to also be <em>parsed</em> according to that format, a corresponding\nparsing function must be placed into the <a href=\"#!/api/Ext.Date-property-parseFunctions\" rel=\"Ext.Date-property-parseFunctions\" class=\"docClass\">parseFunctions</a> property.</p>\n</div></div></div><div id='property-monthNames' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-property-monthNames' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-property-monthNames' class='name expandable'>monthNames</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</span></div><div class='description'><div class='short'>An array of textual month names. ...</div><div class='long'><p>An array of textual month names.\nOverride these values for international dates.</p>\n\n<p>Example:</p>\n\n<pre><code><a href=\"#!/api/Ext.Date-property-monthNames\" rel=\"Ext.Date-property-monthNames\" class=\"docClass\">Ext.Date.monthNames</a> = [\n    'JanInYourLang',\n    'FebInYourLang'\n    // ...\n];\n</code></pre>\n<p>Defaults to: <code>[&quot;January&quot;, &quot;February&quot;, &quot;March&quot;, &quot;April&quot;, &quot;May&quot;, &quot;June&quot;, &quot;July&quot;, &quot;August&quot;, &quot;September&quot;, &quot;October&quot;, &quot;November&quot;, &quot;December&quot;]</code></p></div></div></div><div id='property-monthNumbers' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-property-monthNumbers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-property-monthNumbers' class='name expandable'>monthNumbers</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>An object hash of zero-based JavaScript month numbers (with short month names as keys. ...</div><div class='long'><p>An object hash of zero-based JavaScript month numbers (with short month names as keys. <strong>Note:</strong> keys are case-sensitive).\nOverride these values for international dates.</p>\n\n<p>Example:</p>\n\n<pre><code><a href=\"#!/api/Ext.Date-property-monthNumbers\" rel=\"Ext.Date-property-monthNumbers\" class=\"docClass\">Ext.Date.monthNumbers</a> = {\n    'LongJanNameInYourLang': 0,\n    'ShortJanNameInYourLang':0,\n    'LongFebNameInYourLang':1,\n    'ShortFebNameInYourLang':1\n    // ...\n};\n</code></pre>\n<p>Defaults to: <code>{January: 0, Jan: 0, February: 1, Feb: 1, March: 2, Mar: 2, April: 3, Apr: 3, May: 4, June: 5, Jun: 5, July: 6, Jul: 6, August: 7, Aug: 7, September: 8, Sep: 8, October: 9, Oct: 9, November: 10, Nov: 10, December: 11, Dec: 11}</code></p></div></div></div><div id='property-parseFunctions' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-property-parseFunctions' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-property-parseFunctions' class='name expandable'>parseFunctions</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>An object hash in which each property is a date parsing function. ...</div><div class='long'><p>An object hash in which each property is a date parsing function. The property name is the\nformat string which that function parses.</p>\n\n<p>This object is automatically populated with date parsing functions as\ndate formats are requested for Ext standard formatting strings.</p>\n\n<p>Custom parsing functions may be inserted into this object, keyed by a name which from then on\nmay be used as a format string to <a href=\"#!/api/Ext.Date-method-parse\" rel=\"Ext.Date-method-parse\" class=\"docClass\">parse</a>.</p>\n\n<p>Example:</p>\n\n<pre><code><a href=\"#!/api/Ext.Date-property-parseFunctions\" rel=\"Ext.Date-property-parseFunctions\" class=\"docClass\">Ext.Date.parseFunctions</a>['x-date-format'] = myDateParser;\n</code></pre>\n\n<p>A parsing function should return a Date object, and is passed the following parameters:<div class=\"mdetail-params\"><ul>\n<li><code>date</code> : String<div class=\"sub-desc\">The date string to parse.</div></li>\n<li><code>strict</code> : Boolean<div class=\"sub-desc\">True to validate date strings while parsing\n(i.e. prevent JavaScript Date \"rollover\") (The default must be <code>false</code>).\nInvalid date strings should return <code>null</code> when parsed.</div></li>\n</ul></div></p>\n\n<p>To enable Dates to also be <em>formatted</em> according to that format, a corresponding\nformatting function must be placed into the <a href=\"#!/api/Ext.Date-property-formatFunctions\" rel=\"Ext.Date-property-formatFunctions\" class=\"docClass\">formatFunctions</a> property.</p>\n</div></div></div><div id='property-useStrict' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-property-useStrict' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-property-useStrict' class='name expandable'>useStrict</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Global flag which determines if strict date parsing should be used. ...</div><div class='long'><p>Global flag which determines if strict date parsing should be used.\nStrict date parsing will not roll-over invalid dates, which is the\ndefault behavior of JavaScript Date objects.\n(see <a href=\"#!/api/Ext.Date-method-parse\" rel=\"Ext.Date-method-parse\" class=\"docClass\">parse</a> for more information)</p>\n<p>Defaults to: <code>false</code></p></div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-add' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-add' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-add' class='name expandable'>add</a>( <span class='pre'>date, interval, value</span> ) : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a></div><div class='description'><div class='short'>Provides a convenient method for performing basic date arithmetic. ...</div><div class='long'><p>Provides a convenient method for performing basic date arithmetic. This method\ndoes not modify the Date instance being called - it creates and returns\na new Date instance containing the resulting date value.</p>\n\n<p>Examples:</p>\n\n<pre><code>// Basic usage:\nvar dt = <a href=\"#!/api/Ext.Date-method-add\" rel=\"Ext.Date-method-add\" class=\"docClass\">Ext.Date.add</a>(new Date('10/29/2006'), <a href=\"#!/api/Ext.Date-property-DAY\" rel=\"Ext.Date-property-DAY\" class=\"docClass\">Ext.Date.DAY</a>, 5);\nconsole.log(dt); // returns 'Fri Nov 03 2006 00:00:00'\n\n// Negative values will be subtracted:\nvar dt2 = <a href=\"#!/api/Ext.Date-method-add\" rel=\"Ext.Date-method-add\" class=\"docClass\">Ext.Date.add</a>(new Date('10/1/2006'), <a href=\"#!/api/Ext.Date-property-DAY\" rel=\"Ext.Date-property-DAY\" class=\"docClass\">Ext.Date.DAY</a>, -5);\nconsole.log(dt2); // returns 'Tue Sep 26 2006 00:00:00'\n\n // Decimal values can be used:\nvar dt3 = <a href=\"#!/api/Ext.Date-method-add\" rel=\"Ext.Date-method-add\" class=\"docClass\">Ext.Date.add</a>(new Date('10/1/2006'), <a href=\"#!/api/Ext.Date-property-DAY\" rel=\"Ext.Date-property-DAY\" class=\"docClass\">Ext.Date.DAY</a>, 1.25);\nconsole.log(dt3); // returns 'Mon Oct 02 2006 06:00:00'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date to modify</p>\n</div></li><li><span class='pre'>interval</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>A valid date interval enum value.</p>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The amount to add to the current date.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a></span><div class='sub-desc'><p>The new Date instance.</p>\n</div></li></ul></div></div></div><div id='method-between' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-between' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-between' class='name expandable'>between</a>( <span class='pre'>date, start, end</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks if a date falls on or between the given start and end dates. ...</div><div class='long'><p>Checks if a date falls on or between the given start and end dates.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date to check</p>\n</div></li><li><span class='pre'>start</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>Start date</p>\n</div></li><li><span class='pre'>end</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>End date</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p><code>true</code> if this date falls on or between the given start and end dates.</p>\n</div></li></ul></div></div></div><div id='method-clearTime' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-clearTime' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-clearTime' class='name expandable'>clearTime</a>( <span class='pre'>date, [clone]</span> ) : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a></div><div class='description'><div class='short'>Attempts to clear all time information from this Date by setting the time to midnight of the same day,\nautomatically ...</div><div class='long'><p>Attempts to clear all time information from this Date by setting the time to midnight of the same day,\nautomatically adjusting for Daylight Saving Time (DST) where applicable.</p>\n\n<p><strong>Note:</strong> DST timezone information for the browser's host operating system is assumed to be up-to-date.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date</p>\n</div></li><li><span class='pre'>clone</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p><code>true</code> to create a clone of this date, clear the time and return it.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a></span><div class='sub-desc'><p>this or the clone.</p>\n</div></li></ul></div></div></div><div id='method-clone' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-clone' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-clone' class='name expandable'>clone</a>( <span class='pre'>date</span> ) : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a></div><div class='description'><div class='short'>Creates and returns a new Date instance with the exact same date value as the called instance. ...</div><div class='long'><p>Creates and returns a new Date instance with the exact same date value as the called instance.\nDates are copied and passed by reference, so if a copied date variable is modified later, the original\nvariable will also be changed.  When the intention is to create a new variable that will not\nmodify the original instance, you should create a clone.</p>\n\n<p>Example of correctly cloning a date:</p>\n\n<pre><code>//wrong way:\nvar orig = new Date('10/1/2006');\nvar copy = orig;\ncopy.setDate(5);\nconsole.log(orig);  // returns 'Thu Oct 05 2006'!\n\n//correct way:\nvar orig = new Date('10/1/2006'),\n    copy = <a href=\"#!/api/Ext.Date-method-clone\" rel=\"Ext.Date-method-clone\" class=\"docClass\">Ext.Date.clone</a>(orig);\ncopy.setDate(5);\nconsole.log(orig);  // returns 'Thu Oct 01 2006'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a></span><div class='sub-desc'><p>The new Date instance.</p>\n</div></li></ul></div></div></div><div id='method-format' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-format' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-format' class='name expandable'>format</a>( <span class='pre'>date, format</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Formats a date given the supplied format string. ...</div><div class='long'><p>Formats a date given the supplied format string.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date to format</p>\n</div></li><li><span class='pre'>format</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The format string</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The formatted date or an empty string if date parameter is not a JavaScript Date object</p>\n</div></li></ul></div></div></div><div id='method-formatContainsDateInfo' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-formatContainsDateInfo' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-formatContainsDateInfo' class='name expandable'>formatContainsDateInfo</a>( <span class='pre'>format</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks if the specified format contains information about\nanything other than the time. ...</div><div class='long'><p>Checks if the specified format contains information about\nanything other than the time.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>format</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The format to check</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if the format contains information about\ndate/day information.</p>\n</div></li></ul></div></div></div><div id='method-formatContainsHourInfo' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-formatContainsHourInfo' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-formatContainsHourInfo' class='name expandable'>formatContainsHourInfo</a>( <span class='pre'>format</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks if the specified format contains hour information ...</div><div class='long'><p>Checks if the specified format contains hour information</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>format</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The format to check</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if the format contains hour information</p>\n</div></li></ul></div></div></div><div id='method-getDayOfYear' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-getDayOfYear' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-getDayOfYear' class='name expandable'>getDayOfYear</a>( <span class='pre'>date</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Get the numeric day number of the year, adjusted for leap year. ...</div><div class='long'><p>Get the numeric day number of the year, adjusted for leap year.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>0 to 364 (365 in leap years).</p>\n</div></li></ul></div></div></div><div id='method-getDaysInMonth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-getDaysInMonth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-getDaysInMonth' class='name expandable'>getDaysInMonth</a>( <span class='pre'>date</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Get the number of days in the current month, adjusted for leap year. ...</div><div class='long'><p>Get the number of days in the current month, adjusted for leap year.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The number of days in the month.</p>\n</div></li></ul></div></div></div><div id='method-getElapsed' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-getElapsed' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-getElapsed' class='name expandable'>getElapsed</a>( <span class='pre'>dateA, [dateB]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the number of milliseconds between two dates. ...</div><div class='long'><p>Returns the number of milliseconds between two dates.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>dateA</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The first date.</p>\n</div></li><li><span class='pre'>dateB</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a> (optional)<div class='sub-desc'><p>The second date.</p>\n<p>Defaults to: <code>new Date()</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The difference in milliseconds</p>\n</div></li></ul></div></div></div><div id='method-getFirstDateOfMonth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-getFirstDateOfMonth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-getFirstDateOfMonth' class='name expandable'>getFirstDateOfMonth</a>( <span class='pre'>date</span> ) : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a></div><div class='description'><div class='short'>Get the date of the first day of the month in which this date resides. ...</div><div class='long'><p>Get the date of the first day of the month in which this date resides.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getFirstDayOfMonth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-getFirstDayOfMonth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-getFirstDayOfMonth' class='name expandable'>getFirstDayOfMonth</a>( <span class='pre'>date</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Get the first day of the current month, adjusted for leap year. ...</div><div class='long'><p>Get the first day of the current month, adjusted for leap year.  The returned value\nis the numeric day index within the week (0-6) which can be used in conjunction with\nthe <a href=\"#!/api/Ext.Date-property-monthNames\" rel=\"Ext.Date-property-monthNames\" class=\"docClass\">monthNames</a> array to retrieve the textual day name.</p>\n\n<p>Example:</p>\n\n<pre><code>var dt = new Date('1/10/2007'),\n    firstDay = <a href=\"#!/api/Ext.Date-method-getFirstDayOfMonth\" rel=\"Ext.Date-method-getFirstDayOfMonth\" class=\"docClass\">Ext.Date.getFirstDayOfMonth</a>(dt);\nconsole.log(<a href=\"#!/api/Ext.Date-property-dayNames\" rel=\"Ext.Date-property-dayNames\" class=\"docClass\">Ext.Date.dayNames</a>[firstDay]); // output: 'Monday'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The day number (0-6).</p>\n</div></li></ul></div></div></div><div id='method-getGMTOffset' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-getGMTOffset' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-getGMTOffset' class='name expandable'>getGMTOffset</a>( <span class='pre'>date, [colon]</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Get the offset from GMT of the current date (equivalent to the format specifier 'O'). ...</div><div class='long'><p>Get the offset from GMT of the current date (equivalent to the format specifier 'O').</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date</p>\n</div></li><li><span class='pre'>colon</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>true to separate the hours and minutes with a colon.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The 4-character offset string prefixed with + or - (e.g. '-0600').</p>\n</div></li></ul></div></div></div><div id='method-getLastDateOfMonth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-getLastDateOfMonth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-getLastDateOfMonth' class='name expandable'>getLastDateOfMonth</a>( <span class='pre'>date</span> ) : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a></div><div class='description'><div class='short'>Get the date of the last day of the month in which this date resides. ...</div><div class='long'><p>Get the date of the last day of the month in which this date resides.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getLastDayOfMonth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-getLastDayOfMonth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-getLastDayOfMonth' class='name expandable'>getLastDayOfMonth</a>( <span class='pre'>date</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Get the last day of the current month, adjusted for leap year. ...</div><div class='long'><p>Get the last day of the current month, adjusted for leap year.  The returned value\nis the numeric day index within the week (0-6) which can be used in conjunction with\nthe <a href=\"#!/api/Ext.Date-property-monthNames\" rel=\"Ext.Date-property-monthNames\" class=\"docClass\">monthNames</a> array to retrieve the textual day name.</p>\n\n<p>Example:</p>\n\n<pre><code>var dt = new Date('1/10/2007'),\n    lastDay = <a href=\"#!/api/Ext.Date-method-getLastDayOfMonth\" rel=\"Ext.Date-method-getLastDayOfMonth\" class=\"docClass\">Ext.Date.getLastDayOfMonth</a>(dt);\nconsole.log(<a href=\"#!/api/Ext.Date-property-dayNames\" rel=\"Ext.Date-property-dayNames\" class=\"docClass\">Ext.Date.dayNames</a>[lastDay]); // output: 'Wednesday'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The day number (0-6).</p>\n</div></li></ul></div></div></div><div id='method-getMonthNumber' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-getMonthNumber' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-getMonthNumber' class='name expandable'>getMonthNumber</a>( <span class='pre'>name</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Get the zero-based JavaScript month number for the given short/full month name. ...</div><div class='long'><p>Get the zero-based JavaScript month number for the given short/full month name.\nOverride this function for international dates.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The short/full month name.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The zero-based JavaScript month number.</p>\n</div></li></ul></div></div></div><div id='method-getShortDayName' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-getShortDayName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-getShortDayName' class='name expandable'>getShortDayName</a>( <span class='pre'>day</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Get the short day name for the given day number. ...</div><div class='long'><p>Get the short day name for the given day number.\nOverride this function for international dates.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>day</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>A zero-based JavaScript day number.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The short day name.</p>\n</div></li></ul></div></div></div><div id='method-getShortMonthName' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-getShortMonthName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-getShortMonthName' class='name expandable'>getShortMonthName</a>( <span class='pre'>month</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Get the short month name for the given month number. ...</div><div class='long'><p>Get the short month name for the given month number.\nOverride this function for international dates.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>month</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>A zero-based JavaScript month number.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The short month name.</p>\n</div></li></ul></div></div></div><div id='method-getSuffix' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-getSuffix' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-getSuffix' class='name expandable'>getSuffix</a>( <span class='pre'>date</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Get the English ordinal suffix of the current day (equivalent to the format specifier 'S'). ...</div><div class='long'><p>Get the English ordinal suffix of the current day (equivalent to the format specifier 'S').</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>'st, 'nd', 'rd' or 'th'.</p>\n</div></li></ul></div></div></div><div id='method-getTimezone' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-getTimezone' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-getTimezone' class='name expandable'>getTimezone</a>( <span class='pre'>date</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Get the timezone abbreviation of the current date (equivalent to the format specifier 'T'). ...</div><div class='long'><p>Get the timezone abbreviation of the current date (equivalent to the format specifier 'T').</p>\n\n<p><strong>Note:</strong> The date string returned by the JavaScript Date object's <code>toString()</code> method varies\nbetween browsers (e.g. FF vs IE) and system region settings (e.g. IE in Asia vs IE in America).\nFor a given date string e.g. \"Thu Oct 25 2007 22:55:35 GMT+0800 (Malay Peninsula Standard Time)\",\ngetTimezone() first tries to get the timezone abbreviation from between a pair of parentheses\n(which may or may not be present), failing which it proceeds to get the timezone abbreviation\nfrom the GMT offset portion of the date string.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The abbreviated timezone name (e.g. 'CST', 'PDT', 'EDT', 'MPST' ...).</p>\n</div></li></ul></div></div></div><div id='method-getWeekOfYear' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-getWeekOfYear' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-getWeekOfYear' class='name expandable'>getWeekOfYear</a>( <span class='pre'>date</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Get the numeric ISO-8601 week number of the year. ...</div><div class='long'><p>Get the numeric ISO-8601 week number of the year.\n(equivalent to the format specifier 'W', but without a leading zero).</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>1 to 53</p>\n</div></li></ul></div></div></div><div id='method-isDST' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-isDST' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-isDST' class='name expandable'>isDST</a>( <span class='pre'>date</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks if the current date is affected by Daylight Saving Time (DST). ...</div><div class='long'><p>Checks if the current date is affected by Daylight Saving Time (DST).</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p><code>true</code> if the current date is affected by DST.</p>\n</div></li></ul></div></div></div><div id='method-isEqual' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-isEqual' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-isEqual' class='name expandable'>isEqual</a>( <span class='pre'>date1, date2</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Compares if two dates are equal by comparing their values. ...</div><div class='long'><p>Compares if two dates are equal by comparing their values.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date1</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'>\n</div></li><li><span class='pre'>date2</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p><code>true</code> if the date values are equal</p>\n</div></li></ul></div></div></div><div id='method-isLeapYear' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-isLeapYear' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-isLeapYear' class='name expandable'>isLeapYear</a>( <span class='pre'>date</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks if the current date falls within a leap year. ...</div><div class='long'><p>Checks if the current date falls within a leap year.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if the current date falls within a leap year, false otherwise.</p>\n</div></li></ul></div></div></div><div id='method-isValid' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-isValid' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-isValid' class='name expandable'>isValid</a>( <span class='pre'>year, month, day, [hour], [minute], [second], [millisecond]</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks if the passed Date parameters will cause a JavaScript Date \"rollover\". ...</div><div class='long'><p>Checks if the passed Date parameters will cause a JavaScript Date \"rollover\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>year</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>4-digit year</p>\n</div></li><li><span class='pre'>month</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>1-based month-of-year</p>\n</div></li><li><span class='pre'>day</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Day of month</p>\n</div></li><li><span class='pre'>hour</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>Hour</p>\n</div></li><li><span class='pre'>minute</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>Minute</p>\n</div></li><li><span class='pre'>second</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>Second</p>\n</div></li><li><span class='pre'>millisecond</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>Millisecond</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p><code>true</code> if the passed parameters do not cause a Date \"rollover\", <code>false</code> otherwise.</p>\n</div></li></ul></div></div></div><div id='method-now' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-now' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-now' class='name expandable'>now</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the current timestamp. ...</div><div class='long'><p>Returns the current timestamp.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>Milliseconds since UNIX epoch.</p>\n</div></li></ul></div></div></div><div id='method-parse' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-parse' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-parse' class='name expandable'>parse</a>( <span class='pre'>input, format, [strict]</span> ) : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a></div><div class='description'><div class='short'>Parses the passed string using the specified date format. ...</div><div class='long'><p>Parses the passed string using the specified date format.\nNote that this function expects normal calendar dates, meaning that months are 1-based (i.e. 1 = January).\nThe <a href=\"#!/api/Ext.Date-property-defaults\" rel=\"Ext.Date-property-defaults\" class=\"docClass\">defaults</a> hash will be used for any date value (i.e. year, month, day, hour, minute, second or millisecond)\nwhich cannot be found in the passed string. If a corresponding default date value has not been specified in the <a href=\"#!/api/Ext.Date-property-defaults\" rel=\"Ext.Date-property-defaults\" class=\"docClass\">defaults</a> hash,\nthe current date's year, month, day or DST-adjusted zero-hour time value will be used instead.\nKeep in mind that the input date string must precisely match the specified format string\nin order for the parse operation to be successful (failed parse operations return a null value).</p>\n\n<p>Example:</p>\n\n<pre><code>//dt = Fri May 25 2007 (current date)\nvar dt = new Date();\n\n//dt = Thu May 25 2006 (today&amp;#39;s month/day in 2006)\ndt = <a href=\"#!/api/Ext.Date-method-parse\" rel=\"Ext.Date-method-parse\" class=\"docClass\">Ext.Date.parse</a>(\"2006\", \"Y\");\n\n//dt = Sun Jan 15 2006 (all date parts specified)\ndt = <a href=\"#!/api/Ext.Date-method-parse\" rel=\"Ext.Date-method-parse\" class=\"docClass\">Ext.Date.parse</a>(\"2006-01-15\", \"Y-m-d\");\n\n//dt = Sun Jan 15 2006 15:20:01\ndt = <a href=\"#!/api/Ext.Date-method-parse\" rel=\"Ext.Date-method-parse\" class=\"docClass\">Ext.Date.parse</a>(\"2006-01-15 3:20:01 PM\", \"Y-m-d g:i:s A\");\n\n// attempt to parse Sun Feb 29 2006 03:20:01 in strict mode\ndt = <a href=\"#!/api/Ext.Date-method-parse\" rel=\"Ext.Date-method-parse\" class=\"docClass\">Ext.Date.parse</a>(\"2006-02-29 03:20:01\", \"Y-m-d H:i:s\", true); // returns null\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>input</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The raw date string.</p>\n</div></li><li><span class='pre'>format</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The expected date string format.</p>\n</div></li><li><span class='pre'>strict</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p><code>true</code> to validate date strings while parsing (i.e. prevents JavaScript Date \"rollover\").\nInvalid date strings will return <code>null</code> when parsed.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a></span><div class='sub-desc'><p>The parsed Date.</p>\n</div></li></ul></div></div></div><div id='method-subtract' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-subtract' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-subtract' class='name expandable'>subtract</a>( <span class='pre'>date, interval, value</span> ) : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a></div><div class='description'><div class='short'>Provides a convenient method for performing basic date arithmetic. ...</div><div class='long'><p>Provides a convenient method for performing basic date arithmetic. This method\ndoes not modify the Date instance being called - it creates and returns\na new Date instance containing the resulting date value.</p>\n\n<p>Examples:</p>\n\n<pre><code>// Basic usage:\nvar dt = <a href=\"#!/api/Ext.Date-method-subtract\" rel=\"Ext.Date-method-subtract\" class=\"docClass\">Ext.Date.subtract</a>(new Date('10/29/2006'), <a href=\"#!/api/Ext.Date-property-DAY\" rel=\"Ext.Date-property-DAY\" class=\"docClass\">Ext.Date.DAY</a>, 5);\nconsole.log(dt); // returns 'Tue Oct 24 2006 00:00:00'\n\n// Negative values will be added:\nvar dt2 = <a href=\"#!/api/Ext.Date-method-subtract\" rel=\"Ext.Date-method-subtract\" class=\"docClass\">Ext.Date.subtract</a>(new Date('10/1/2006'), <a href=\"#!/api/Ext.Date-property-DAY\" rel=\"Ext.Date-property-DAY\" class=\"docClass\">Ext.Date.DAY</a>, -5);\nconsole.log(dt2); // returns 'Fri Oct 6 2006 00:00:00'\n\n // Decimal values can be used:\nvar dt3 = <a href=\"#!/api/Ext.Date-method-subtract\" rel=\"Ext.Date-method-subtract\" class=\"docClass\">Ext.Date.subtract</a>(new Date('10/1/2006'), <a href=\"#!/api/Ext.Date-property-DAY\" rel=\"Ext.Date-property-DAY\" class=\"docClass\">Ext.Date.DAY</a>, 1.25);\nconsole.log(dt3); // returns 'Fri Sep 29 2006 06:00:00'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>The date to modify</p>\n</div></li><li><span class='pre'>interval</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>A valid date interval enum value.</p>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The amount to subtract from the current date.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a></span><div class='sub-desc'><p>The new Date instance.</p>\n</div></li></ul></div></div></div><div id='method-toString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-toString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-toString' class='name expandable'>toString</a>( <span class='pre'>date</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Private for now ...</div><div class='long'><p>Private for now</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-unescapeFormat' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Date'>Ext.Date</span><br/><a href='source/Date2.html#Ext-Date-method-unescapeFormat' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Date-method-unescapeFormat' class='name expandable'>unescapeFormat</a>( <span class='pre'>format</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Removes all escaping for a date format string. ...</div><div class='long'><p>Removes all escaping for a date format string. In date formats,\nusing a '\\' can be used to escape special characters.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>format</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The format to unescape</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The unescaped format</p>\n</div></li></ul></div></div></div></div></div></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"Date2.html#Ext-Date","filename":"Date.js"}],"linenr":5,"members":{"property":[{"tagname":"property","owner":"Ext.Date","meta":{},"name":"DAY","id":"property-DAY"},{"tagname":"property","owner":"Ext.Date","meta":{},"name":"HOUR","id":"property-HOUR"},{"tagname":"property","owner":"Ext.Date","meta":{},"name":"MILLI","id":"property-MILLI"},{"tagname":"property","owner":"Ext.Date","meta":{},"name":"MINUTE","id":"property-MINUTE"},{"tagname":"property","owner":"Ext.Date","meta":{},"name":"MONTH","id":"property-MONTH"},{"tagname":"property","owner":"Ext.Date","meta":{},"name":"SECOND","id":"property-SECOND"},{"tagname":"property","owner":"Ext.Date","meta":{},"name":"YEAR","id":"property-YEAR"},{"tagname":"property","owner":"Ext.Date","meta":{},"name":"dayNames","id":"property-dayNames"},{"tagname":"property","owner":"Ext.Date","meta":{},"name":"defaultFormat","id":"property-defaultFormat"},{"tagname":"property","owner":"Ext.Date","meta":{},"name":"defaults","id":"property-defaults"},{"tagname":"property","owner":"Ext.Date","meta":{},"name":"formatCodes","id":"property-formatCodes"},{"tagname":"property","owner":"Ext.Date","meta":{},"name":"formatFunctions","id":"property-formatFunctions"},{"tagname":"property","owner":"Ext.Date","meta":{},"name":"monthNames","id":"property-monthNames"},{"tagname":"property","owner":"Ext.Date","meta":{},"name":"monthNumbers","id":"property-monthNumbers"},{"tagname":"property","owner":"Ext.Date","meta":{},"name":"parseFunctions","id":"property-parseFunctions"},{"tagname":"property","owner":"Ext.Date","meta":{},"name":"useStrict","id":"property-useStrict"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Date","meta":{},"name":"add","id":"method-add"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"between","id":"method-between"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"clearTime","id":"method-clearTime"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"clone","id":"method-clone"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"format","id":"method-format"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"formatContainsDateInfo","id":"method-formatContainsDateInfo"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"formatContainsHourInfo","id":"method-formatContainsHourInfo"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"getDayOfYear","id":"method-getDayOfYear"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"getDaysInMonth","id":"method-getDaysInMonth"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"getElapsed","id":"method-getElapsed"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"getFirstDateOfMonth","id":"method-getFirstDateOfMonth"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"getFirstDayOfMonth","id":"method-getFirstDayOfMonth"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"getGMTOffset","id":"method-getGMTOffset"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"getLastDateOfMonth","id":"method-getLastDateOfMonth"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"getLastDayOfMonth","id":"method-getLastDayOfMonth"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"getMonthNumber","id":"method-getMonthNumber"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"getShortDayName","id":"method-getShortDayName"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"getShortMonthName","id":"method-getShortMonthName"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"getSuffix","id":"method-getSuffix"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"getTimezone","id":"method-getTimezone"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"getWeekOfYear","id":"method-getWeekOfYear"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"isDST","id":"method-isDST"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"isEqual","id":"method-isEqual"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"isLeapYear","id":"method-isLeapYear"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"isValid","id":"method-isValid"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"now","id":"method-now"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"parse","id":"method-parse"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"subtract","id":"method-subtract"},{"tagname":"method","owner":"Ext.Date","meta":{"private":true},"name":"toString","id":"method-toString"},{"tagname":"method","owner":"Ext.Date","meta":{},"name":"unescapeFormat","id":"method-unescapeFormat"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.Date","singleton":true,"override":null,"inheritdoc":null,"id":"class-Ext.Date","mixins":[],"mixedInto":[]});