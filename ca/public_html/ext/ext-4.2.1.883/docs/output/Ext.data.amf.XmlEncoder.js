Ext.data.JsonP.Ext_data_amf_XmlEncoder({"alternateClassNames":[],"aliases":{"data":["amf.xmlencoder"]},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":"Ext.Base","uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><strong>Ext.data.amf.XmlEncoder</strong></div></div><h4>Files</h4><div class='dependency'><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder' target='_blank'>XmlEncoder.js</a></div></pre><div class='doc-contents'><p>This class serializes data in the Action Message Format XML (AMFX) format.\nIt can write simple and complex objects, to be used in conjunction with an\nAMFX-compliant server.\nTo create an encoded XMl, first construct an Encoder:</p>\n\n<pre><code>var encoder = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.data.amf.XmlEncoder\" rel=\"Ext.data.amf.XmlEncoder\" class=\"docClass\">Ext.data.amf.XmlEncoder</a>');\n</code></pre>\n\n<p>Then use the writer methods to out data to the :</p>\n\n<pre><code>encoder.writeObject(1);\nencoder.writeObject({a: \"b\"});\n</code></pre>\n\n<p>And access the data through the #bytes property:</p>\n\n<pre><code>encoder.body;\n</code></pre>\n\n<p>You can also reset the class to start a new body:</p>\n\n<pre><code>encoder.clear();\n</code></pre>\n\n<p>Current limitations:\nAMF3 format (format:3)\n- Each object is written out explicitly, not using the reference tables\n  supported by the AMFX format. This means the function does NOT support\n  circular reference objects.\n- Objects that aren't Arrays, Dates, Strings, Document (XML) or primitive\n  values will be written out as anonymous objects with dynamic data.\n- If the object has a $flexType field, that field will be used in signifying\n  the object-type as an attribute, instead of being passed as data.\n- There's no JavaScript equivalent to the ByteArray type in ActionScript,\n  hence data will never be searialized as ByteArrays by the writeObject\n  function. A writeByteArray method is provided for writing out ByteArray objects.</p>\n\n<p>For more information on working with AMF data please refer to the\n<a href=\"#/guide/amf\">AMF Guide</a>.</p>\n</div><div class='members'><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-body' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-property-body' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-property-body' class='name expandable'>body</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The output string ...</div><div class='long'><ul>\n<li>The output string</li>\n</ul>\n\n<p>Defaults to: <code>&quot;&quot;</code></p></div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/Ext.data.amf.XmlEncoder-method-constructor' class='name expandable'>Ext.data.amf.XmlEncoder</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.data.amf.XmlEncoder\" rel=\"Ext.data.amf.XmlEncoder\" class=\"docClass\">Ext.data.amf.XmlEncoder</a></div><div class='description'><div class='short'>Creates new encoder. ...</div><div class='long'><p>Creates new encoder.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>Configuration options</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.amf.XmlEncoder\" rel=\"Ext.data.amf.XmlEncoder\" class=\"docClass\">Ext.data.amf.XmlEncoder</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-callOverridden' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-clear' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-clear' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-clear' class='name expandable'>clear</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Clears the accumulated data, starting with an empty string ...</div><div class='long'><p>Clears the accumulated data, starting with an empty string</p>\n</div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-convertXmlToString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-convertXmlToString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-convertXmlToString' class='name expandable'>convertXmlToString</a>( <span class='pre'>xml</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Converts an XML Document object to a string. ...</div><div class='long'><p>Converts an XML Document object to a string.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xml</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>XML document to convert (typically Document object)</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>A string representing the document</p>\n</div></li></ul></div></div></div><div id='method-destroy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-destroy' class='name expandable'>destroy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.util.ElementContainer-method-destroy' rel='Ext.util.ElementContainer-method-destroy' class='docClass'>Ext.util.ElementContainer.destroy</a>, <a href='#!/api/Ext.AbstractComponent-method-destroy' rel='Ext.AbstractComponent-method-destroy' class='docClass'>Ext.AbstractComponent.destroy</a>, <a href='#!/api/Ext.AbstractPlugin-method-destroy' rel='Ext.AbstractPlugin-method-destroy' class='docClass'>Ext.AbstractPlugin.destroy</a></p></div></div></div><div id='method-encodeAmfxRemotingPacket' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-encodeAmfxRemotingPacket' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-encodeAmfxRemotingPacket' class='name expandable'>encodeAmfxRemotingPacket</a>( <span class='pre'>message</span> )</div><div class='description'><div class='short'>Encodes an AMFX remoting message with the AMFX envelope. ...</div><div class='long'><p>Encodes an AMFX remoting message with the AMFX envelope.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>message</span> : <a href=\"#!/api/Ext.data.amf.RemotingMessage\" rel=\"Ext.data.amf.RemotingMessage\" class=\"docClass\">Ext.data.amf.RemotingMessage</a><div class='sub-desc'><p>the message to pass on to serialize.</p>\n</div></li></ul></div></div></div><div id='method-encodeArray' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-encodeArray' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-encodeArray' class='name expandable'>encodeArray</a>( <span class='pre'>array</span> )</div><div class='description'><div class='short'>Encodes an array, marking it as an ECMA array if it has associative (non-ordinal) indices ...</div><div class='long'><p>Encodes an array, marking it as an ECMA array if it has associative (non-ordinal) indices</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>the array to encode</p>\n</div></li></ul></div></div></div><div id='method-encodeBoolean' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-encodeBoolean' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-encodeBoolean' class='name expandable'>encodeBoolean</a>( <span class='pre'>val</span> )</div><div class='description'><div class='short'>Returns an encoded boolean ...</div><div class='long'><p>Returns an encoded boolean</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>val</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>a boolean value</p>\n</div></li></ul></div></div></div><div id='method-encodeByteArray' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-encodeByteArray' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-encodeByteArray' class='name expandable'>encodeByteArray</a>( <span class='pre'>array</span> )</div><div class='description'><div class='short'>Encodes a byte arrat in AMFX format ...</div><div class='long'><p>Encodes a byte arrat in AMFX format</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>the byte array to encode</p>\n</div></li></ul></div></div></div><div id='method-encodeDate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-encodeDate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-encodeDate' class='name expandable'>encodeDate</a>( <span class='pre'>date</span> )</div><div class='description'><div class='short'>Encode a date ...</div><div class='long'><p>Encode a date</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>the date to encode</p>\n</div></li></ul></div></div></div><div id='method-encodeDouble' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-encodeDouble' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-encodeDouble' class='name expandable'>encodeDouble</a>( <span class='pre'>num</span> )</div><div class='description'><div class='short'>Returns an encoded double ...</div><div class='long'><p>Returns an encoded double</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>num</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the double to encode</p>\n</div></li></ul></div></div></div><div id='method-encodeEcmaElement' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-encodeEcmaElement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-encodeEcmaElement' class='name expandable'>encodeEcmaElement</a>( <span class='pre'>key, value</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Encodes one ECMA array element ...</div><div class='long'><p>Encodes one ECMA array element</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>key</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the name of the element</p>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>the value of the element</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>the encoded key-value pair</p>\n</div></li></ul></div></div></div><div id='method-encodeGenericObject' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-encodeGenericObject' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-encodeGenericObject' class='name expandable'>encodeGenericObject</a>( <span class='pre'>obj</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Encodes a generic object into AMFX format. ...</div><div class='long'><p>Encodes a generic object into AMFX format. If a <tt>$flexType</tt> member is defined, list that as the object type.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>obj</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>the object to encode</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>the encoded text</p>\n</div></li></ul></div></div></div><div id='method-encodeInt' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-encodeInt' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-encodeInt' class='name expandable'>encodeInt</a>( <span class='pre'>num</span> )</div><div class='description'><div class='short'>Returns an encoded int ...</div><div class='long'><p>Returns an encoded int</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>num</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the integer to encode</p>\n</div></li></ul></div></div></div><div id='method-encodeNull' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-encodeNull' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-encodeNull' class='name expandable'>encodeNull</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Returns the encoding for null ...</div><div class='long'><p>Returns the encoding for null</p>\n</div></div></div><div id='method-encodeNumber' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-encodeNumber' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-encodeNumber' class='name expandable'>encodeNumber</a>( <span class='pre'>num</span> )</div><div class='description'><div class='short'>Returns an encoded number. ...</div><div class='long'><p>Returns an encoded number. Decides wheter to use int or double encoding.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>num</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the number to encode</p>\n</div></li></ul></div></div></div><div id='method-encodeObject' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-encodeObject' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-encodeObject' class='name expandable'>encodeObject</a>( <span class='pre'>item</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>encode the appropriate data item. ...</div><div class='long'><p>encode the appropriate data item. Supported types:\n- undefined\n- null\n- boolean\n- integer\n- double\n- UTF-8 string\n- XML Document (identified by being instaneof Document. Can be generated with: new DOMParser()).parseFromString(xml, \"text/xml\");\n- Date\n- Array\n- Generic object</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>A primitive or object to write to the stream</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>the encoded object in AMFX format</p>\n</div></li></ul></div></div></div><div id='method-encodeString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-encodeString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-encodeString' class='name expandable'>encodeString</a>( <span class='pre'>str</span> )</div><div class='description'><div class='short'>Returns an encoded string ...</div><div class='long'><p>Returns an encoded string</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>str</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the string to encode</p>\n</div></li></ul></div></div></div><div id='method-encodeUndefined' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-encodeUndefined' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-encodeUndefined' class='name expandable'>encodeUndefined</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Returns the encoding for undefined (which is the same as the encoding for null) ...</div><div class='long'><p>Returns the encoding for undefined (which is the same as the encoding for null)</p>\n</div></div></div><div id='method-encodeXml' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-encodeXml' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-encodeXml' class='name expandable'>encodeXml</a>( <span class='pre'>xml</span> )</div><div class='description'><div class='short'>Encodes an xml document into a CDATA section ...</div><div class='long'><p>Encodes an xml document into a CDATA section</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xml</span> : XMLElement/HTMLElement<div class='sub-desc'><p>an XML document or element (Document type in some browsers)</p>\n</div></li></ul></div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-isXmlDocument' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-isXmlDocument' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-isXmlDocument' class='name expandable'>isXmlDocument</a>( <span class='pre'>item</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Tries to determine if an object is an XML document ...</div><div class='long'><p>Tries to determine if an object is an XML document</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>to identify</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>true if it's an XML document, false otherwise</p>\n</div></li></ul></div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-write' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-write' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-write' class='name expandable'>write</a>( <span class='pre'>str</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Appends a string to the body of the message ...</div><div class='long'><p>Appends a string to the body of the message</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>str</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the string to append</p>\n</div></li></ul></div></div></div><div id='method-writeAmfxRemotingPacket' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-writeAmfxRemotingPacket' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-writeAmfxRemotingPacket' class='name expandable'>writeAmfxRemotingPacket</a>( <span class='pre'>message</span> )</div><div class='description'><div class='short'>Writes an AMFX remoting message with the AMFX envelope to the string. ...</div><div class='long'><p>Writes an AMFX remoting message with the AMFX envelope to the string.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>message</span> : <a href=\"#!/api/Ext.data.amf.RemotingMessage\" rel=\"Ext.data.amf.RemotingMessage\" class=\"docClass\">Ext.data.amf.RemotingMessage</a><div class='sub-desc'><p>the message to pass on to serialize.</p>\n</div></li></ul></div></div></div><div id='method-writeArray' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-writeArray' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-writeArray' class='name expandable'>writeArray</a>( <span class='pre'>array</span> )</div><div class='description'><div class='short'>Writes an array to the string, marking it as an ECMA array if it has associative (non-ordinal) indices ...</div><div class='long'><p>Writes an array to the string, marking it as an ECMA array if it has associative (non-ordinal) indices</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>the array to encode</p>\n</div></li></ul></div></div></div><div id='method-writeBoolean' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-writeBoolean' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-writeBoolean' class='name expandable'>writeBoolean</a>( <span class='pre'>val</span> )</div><div class='description'><div class='short'>Writes a boolean value to the string ...</div><div class='long'><p>Writes a boolean value to the string</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>val</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>a boolean value</p>\n</div></li></ul></div></div></div><div id='method-writeByteArray' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-writeByteArray' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-writeByteArray' class='name expandable'>writeByteArray</a>( <span class='pre'>array</span> )</div><div class='description'><div class='short'>Writes an AMFX byte array to the string. ...</div><div class='long'><p>Writes an AMFX byte array to the string. This is for convenience only and is not called automatically by writeObject.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>array</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>the byte array to encode</p>\n</div></li></ul></div></div></div><div id='method-writeDate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-writeDate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-writeDate' class='name expandable'>writeDate</a>( <span class='pre'>date</span> )</div><div class='description'><div class='short'>Write a date to the string ...</div><div class='long'><p>Write a date to the string</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>date</span> : <a href=\"#!/api/Date\" rel=\"Date\" class=\"docClass\">Date</a><div class='sub-desc'><p>the date to encode</p>\n</div></li></ul></div></div></div><div id='method-writeDouble' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-writeDouble' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-writeDouble' class='name expandable'>writeDouble</a>( <span class='pre'>num</span> )</div><div class='description'><div class='short'>Writes a double tag with the content. ...</div><div class='long'><p>Writes a double tag with the content.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>num</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the double to encode</p>\n</div></li></ul></div></div></div><div id='method-writeGenericObject' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-writeGenericObject' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-writeGenericObject' class='name expandable'>writeGenericObject</a>( <span class='pre'>obj</span> )</div><div class='description'><div class='short'>Writes a generic object to the string. ...</div><div class='long'><p>Writes a generic object to the string. If a <tt>$flexType</tt> member is defined, list that as the object type.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>obj</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>the object to encode</p>\n</div></li></ul></div></div></div><div id='method-writeInt' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-writeInt' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-writeInt' class='name expandable'>writeInt</a>( <span class='pre'>num</span> )</div><div class='description'><div class='short'>Writes a int tag with the content. ...</div><div class='long'><p>Writes a int tag with the content.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>num</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the integer to encode</p>\n</div></li></ul></div></div></div><div id='method-writeNull' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-writeNull' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-writeNull' class='name expandable'>writeNull</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Writes the null value to the string ...</div><div class='long'><p>Writes the null value to the string</p>\n</div></div></div><div id='method-writeNumber' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-writeNumber' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-writeNumber' class='name expandable'>writeNumber</a>( <span class='pre'>num</span> )</div><div class='description'><div class='short'>Writes a number, deciding if to use int or double as the tag ...</div><div class='long'><p>Writes a number, deciding if to use int or double as the tag</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>num</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>the number to encode</p>\n</div></li></ul></div></div></div><div id='method-writeObject' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-writeObject' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-writeObject' class='name expandable'>writeObject</a>( <span class='pre'>item</span> )</div><div class='description'><div class='short'>Writes the appropriate data item to the string. ...</div><div class='long'><p>Writes the appropriate data item to the string. Supported types:\n- undefined\n- null\n- boolean\n- integer\n- double\n- UTF-8 string\n- XML Document (identified by being instaneof Document. Can be generated with: new DOMParser()).parseFromString(xml, \"text/xml\");\n- Date\n- Array\n- Generic object</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>A primitive or object to write to the stream</p>\n</div></li></ul></div></div></div><div id='method-writeString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-writeString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-writeString' class='name expandable'>writeString</a>( <span class='pre'>str</span> )</div><div class='description'><div class='short'>Writes a string tag with the string content. ...</div><div class='long'><p>Writes a string tag with the string content.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>str</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>the string to encode</p>\n</div></li></ul></div></div></div><div id='method-writeUndefined' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-writeUndefined' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-writeUndefined' class='name expandable'>writeUndefined</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Writes the undefined value to the string ...</div><div class='long'><p>Writes the undefined value to the string</p>\n</div></div></div><div id='method-writeXml' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-writeXml' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-writeXml' class='name expandable'>writeXml</a>( <span class='pre'>xml</span> )</div><div class='description'><div class='short'>Write an XML document to the string ...</div><div class='long'><p>Write an XML document to the string</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xml</span> : XMLElement/HTMLElement<div class='sub-desc'><p>an XML document or element (Document type in some browsers)</p>\n</div></li></ul></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-create' class='name expandable'>create</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create a new instance of this Class. ...</div><div class='long'><p>Create a new instance of this Class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.create({\n    someConfig: true\n});\n</code></pre>\n\n<p>All parameters are passed to the constructor of the class.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the created instance.</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-generateFlexUID' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.amf.XmlEncoder'>Ext.data.amf.XmlEncoder</span><br/><a href='source/XmlEncoder.html#Ext-data-amf-XmlEncoder-method-generateFlexUID' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.amf.XmlEncoder-method-generateFlexUID' class='name expandable'>generateFlexUID</a>( <span class='pre'>id</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Utility function to generate a flex-friendly UID ...</div><div class='long'><p>Utility function to generate a flex-friendly UID</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>used in the first 8 chars of the id. If not provided, a random number will be used.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>a string-encoded opaque UID</p>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div></div></div>","superclasses":["Ext.Base"],"meta":{},"code_type":"ext_define","requires":[],"html_meta":{},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{"static":true},"name":"generateFlexUID","id":"method-generateFlexUID"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"XmlEncoder.html#Ext-data-amf-XmlEncoder","filename":"XmlEncoder.js"}],"linenr":2,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"body","id":"property-body"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"clear","id":"method-clear"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{"private":true},"name":"convertXmlToString","id":"method-convertXmlToString"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"encodeAmfxRemotingPacket","id":"method-encodeAmfxRemotingPacket"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"encodeArray","id":"method-encodeArray"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"encodeBoolean","id":"method-encodeBoolean"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"encodeByteArray","id":"method-encodeByteArray"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"encodeDate","id":"method-encodeDate"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"encodeDouble","id":"method-encodeDouble"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{"private":true},"name":"encodeEcmaElement","id":"method-encodeEcmaElement"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"encodeGenericObject","id":"method-encodeGenericObject"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"encodeInt","id":"method-encodeInt"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"encodeNull","id":"method-encodeNull"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"encodeNumber","id":"method-encodeNumber"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"encodeObject","id":"method-encodeObject"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"encodeString","id":"method-encodeString"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"encodeUndefined","id":"method-encodeUndefined"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"encodeXml","id":"method-encodeXml"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"isXmlDocument","id":"method-isXmlDocument"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{"private":true},"name":"write","id":"method-write"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"writeAmfxRemotingPacket","id":"method-writeAmfxRemotingPacket"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"writeArray","id":"method-writeArray"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"writeBoolean","id":"method-writeBoolean"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"writeByteArray","id":"method-writeByteArray"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"writeDate","id":"method-writeDate"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"writeDouble","id":"method-writeDouble"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"writeGenericObject","id":"method-writeGenericObject"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"writeInt","id":"method-writeInt"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"writeNull","id":"method-writeNull"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"writeNumber","id":"method-writeNumber"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"writeObject","id":"method-writeObject"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"writeString","id":"method-writeString"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"writeUndefined","id":"method-writeUndefined"},{"tagname":"method","owner":"Ext.data.amf.XmlEncoder","meta":{},"name":"writeXml","id":"method-writeXml"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.data.amf.XmlEncoder","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.data.amf.XmlEncoder","mixins":[],"mixedInto":[]});