Ext.data.JsonP.RegExp({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/RegExp.html#RegExp' target='_blank'>RegExp.js</a></div></pre><div class='doc-contents'><p>Creates a regular expression object for matching text according to a pattern.</p>\n\n<p>When using the constructor function, the normal string escape rules (preceding\nspecial characters with \\ when included in a string) are necessary. For\nexample, the following are equivalent:</p>\n\n<pre><code>var re = new RegExp(\"\\\\w+\");\nvar re = /\\w+/;\n</code></pre>\n\n<p>Notice that the parameters to the literal format do not use quotation marks to\nindicate strings, while the parameters to the constructor function do use\nquotation marks. So the following expressions create the same regular\nexpression:</p>\n\n<pre><code>/ab+c/i;\nnew RegExp(\"ab+c\", \"i\");\n</code></pre>\n\n<h1>Special characters in regular expressions</h1>\n\n<table>\n<thead>\n<tr>\n<th></th>\n<th align=\"left\">     Character    </th>\n<th align=\"left\"> Meaning</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\</code>              </td>\n<td align=\"left\"> For characters that are usually treated literally, indicates that the next character</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> is special and not to be interpreted literally.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/b/</code> matches the character 'b'. By placing a backslash in front of b, that</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> is by using <code>/\\b/</code>, the character becomes special to mean match a word boundary.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> <em>or</em></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For characters that are usually treated specially, indicates that the next character is</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> not special and should be interpreted literally.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>*</code> is a special character that means 0 or more occurrences of the preceding</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> character should be matched; for example, <code>/a*\\/</code> means match 0 or more \"a\"s. To match *</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> literally, precede it with a backslash; for example, <code>/a\\*\\/</code> matches 'a*'.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>^</code>              </td>\n<td align=\"left\"> Matches beginning of input. If the multiline flag is set to true, also matches</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> immediately after a line break character.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/^A/</code> does not match the 'A' in \"an A\", but does match the first 'A' in</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> \"An A\".</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>$</code>              </td>\n<td align=\"left\"> Matches end of input. If the multiline flag is set to true, also matches immediately</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> before a line break character.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/t$/</code> does not match the 't' in \"eater\", but does match it in \"eat\".</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>*</code>              </td>\n<td align=\"left\"> Matches the preceding item 0 or more times.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/bo*\\/</code> matches 'boooo' in \"A ghost booooed\" and 'b' in \"A bird warbled\",</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> but nothing in \"A goat grunted\".</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>+</code>              </td>\n<td align=\"left\"> Matches the preceding item 1 or more times. Equivalent to <code>{1,}</code>.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/a+/</code> matches the 'a' in \"candy\" and all the a's in \"caaaaaaandy\".</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>?</code>              </td>\n<td align=\"left\"> Matches the preceding item 0 or 1 time.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/e?le?/</code> matches the 'el' in \"angel\" and the 'le' in \"angle.\"</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> If used immediately after any of the quantifiers <code>*</code>, <code>+</code>, <code>?</code>, or <code>{}</code>, makes the quantifier</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> non-greedy (matching the minimum number of times), as opposed to the default, which is</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> greedy (matching the maximum number of times).</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> Also used in lookahead assertions, described under <code>(?=)</code>, <code>(?!)</code>, and <code>(?:)</code> in this table.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>.</code>              </td>\n<td align=\"left\"> (The decimal point) matches any single character except the newline characters: \\n \\r</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> \\u2028 or \\u2029. (<code>[\\s\\S]</code> can be used to match any character including new lines.)</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/.n/</code> matches 'an' and 'on' in \"nay, an apple is on the tree\", but not 'nay'.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>(x)</code>            </td>\n<td align=\"left\"> Matches <code>x</code> and remembers the match. These are called capturing parentheses.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/(foo)/</code> matches and remembers 'foo' in \"foo bar.\" The matched substring can</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> be recalled from the resulting array's elements <code>[1], ..., [n]</code> or from the predefined RegExp</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> object's properties <code>$1, ..., $9</code>.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>(?:x)</code>          </td>\n<td align=\"left\"> Matches <code>x</code> but does not remember the match. These are called non-capturing parentheses.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> The matched substring can not be recalled from the resulting array's elements <code>[1], ..., [n]</code></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> or from the predefined RegExp object's properties <code>$1, ..., $9</code>.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>x(?=y)</code>         </td>\n<td align=\"left\"> Matches <code>x</code> only if <code>x</code> is followed by <code>y</code>. For example, <code>/Jack(?=Sprat)/</code> matches 'Jack' only if</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> it is followed by 'Sprat'. <code>/Jack(?=Sprat|Frost)/</code> matches 'Jack' only if it is followed by</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> 'Sprat' or 'Frost'. However, neither 'Sprat' nor 'Frost' is part of the match results.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>x(?!y)</code>         </td>\n<td align=\"left\"> Matches <code>x</code> only if <code>x</code> is not followed by <code>y</code>. For example, <code>/\\d+(?!\\.)/</code> matches a number only</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> if it is not followed by a decimal point.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> <code>/\\d+(?!\\.)/.exec(\"3.141\")</code> matches 141 but not 3.141.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"><code>x&#124;y</code></td>\n<td align=\"left\"> Matches either <code>x</code> or <code>y</code>.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/green|red/</code> matches 'green' in \"green apple\" and 'red' in \"red apple.\"</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>{n}</code>            </td>\n<td align=\"left\"> Where <code>n</code> is a positive integer. Matches exactly n occurrences of the preceding item.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/a{2}/</code> doesn't match the 'a' in \"candy,\" but it matches all of the a's</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> in \"caandy,\" and the first two a's in \"caaandy.\"</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>{n,}</code>           </td>\n<td align=\"left\"> Where <code>n</code> is a positive integer. Matches at least n occurrences of the preceding item.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/a{2,}/</code> doesn't match the 'a' in \"candy\", but matches all of the a's in</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> \"caandy\" and in \"caaaaaaandy.\"</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>{n,m}</code>          </td>\n<td align=\"left\"> Where <code>n</code> and <code>m</code> are positive integers. Matches at least <code>n</code> and at most <code>m</code> occurrences of the</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> preceding item.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/a{1,3}/</code> matches nothing in \"cndy\", the 'a' in \"candy,\" the first two a's</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> in \"caandy,\" and the first three a's in \"caaaaaaandy\". Notice that when matching</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> \"caaaaaaandy\", the match is \"aaa\", even though the original string had more a's in it.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>[xyz]</code>          </td>\n<td align=\"left\"> A character set. Matches any one of the enclosed characters. You can specify a range of</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> characters by using a hyphen.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>[abcd]</code> is the same as <code>[a-d]</code>. They match the 'b' in \"brisket\" and the 'c'</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> in \"chop\".</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>[^xyz]</code>         </td>\n<td align=\"left\"> A negated or complemented character set. That is, it matches anything that is not</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> enclosed in the brackets. You can specify a range of characters by using a hyphen.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>[^abc]</code> is the same as <code>[^a-c]</code>. They initially match 'r' in \"brisket\" and</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> 'h' in \"chop.\"</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>[\\b]</code>           </td>\n<td align=\"left\"> Matches a backspace. (Not to be confused with <code>\\b</code>.)</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\b</code>             </td>\n<td align=\"left\"> Matches a word boundary, such as a space. (Not to be confused with <code>[\\b]</code>.)</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/\\bn\\w/</code> matches the 'no' in \"noonday\"; <code>/\\wy\\b/</code> matches the 'ly' in</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> \"possibly yesterday.\"</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\B</code>             </td>\n<td align=\"left\"> Matches a non-word boundary.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/\\w\\Bn/</code> matches 'on' in \"noonday\", and <code>/y\\B\\w/</code> matches 'ye' in \"possibly</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> yesterday.\"</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\cX</code>            </td>\n<td align=\"left\"> Where X is a letter from A - Z. Matches a control character in a string.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/\\cM/</code> matches control-M in a string.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\d</code>             </td>\n<td align=\"left\"> Matches a digit character in the basic Latin alphabet. Equivalent to <code>[0-9]</code>.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/\\d/</code> or <code>/[0-9]/</code> matches '2' in \"B2 is the suite number.\"</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\D</code>             </td>\n<td align=\"left\"> Matches any non-digit character in the basic Latin alphabet. Equivalent to <code>[^0-9]</code>.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/\\D/</code> or <code>/[^0-9]/</code> matches 'B' in \"B2 is the suite number.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\f</code>             </td>\n<td align=\"left\"> Matches a form-feed.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\n</code>             </td>\n<td align=\"left\"> Matches a linefeed.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\r</code>             </td>\n<td align=\"left\"> Matches a carriage return.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\s</code>             </td>\n<td align=\"left\"> Matches a single white space character, including space, tab, form feed, line feed and</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> other unicode spaces. Equivalent to:</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> <code>[\\t\\n\\v\\f\\r \\u00a0\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u200b\\u2028\\u2029\\u3000]</code></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/\\s\\w*\\/</code> matches ' bar' in \"foo bar.\"</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\S</code>             </td>\n<td align=\"left\"> Matches a single character other than white space. Equivalent to:</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> <code>[^\\t\\n\\v\\f\\r \\u00a0\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u200b\\u2028\\u2029\\u3000]</code></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/\\S\\w*\\/</code> matches 'foo' in \"foo bar.\"</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\t</code>             </td>\n<td align=\"left\"> Matches a tab.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\v</code>             </td>\n<td align=\"left\"> Matches a vertical tab.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\w</code>             </td>\n<td align=\"left\"> Matches any alphanumeric character from the basic Latin alphabet, including the</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> underscore. Equivalent to <code>[A-Za-z0-9_]</code>.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/\\w/</code> matches 'a' in \"apple,\" '5' in \"$5.28,\" and '3' in \"3D.\"</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\W</code>             </td>\n<td align=\"left\"> Matches any character that is not a word character from the basic Latin alphabet. Equivalent</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> to <code>[^A-Za-z0-9_]</code>.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/\\W/</code> or <code>/[^A-Za-z0-9_]/</code> matches '%' in \"50%.\"</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\n</code>             </td>\n<td align=\"left\"> Where <code>n</code> is a positive integer. A back reference to the last substring matching the n</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> parenthetical in the regular expression (counting left parentheses).</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> For example, <code>/apple(,)\\sorange\\1/</code> matches 'apple, orange,' in \"apple, orange, cherry,</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> peach.\" A more complete example follows this table.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\0</code>             </td>\n<td align=\"left\"> Matches a NULL character. Do not follow this with another digit.</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\xhh</code>           </td>\n<td align=\"left\"> Matches the character with the code <code>hh</code> (two hexadecimal digits)</td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>\\uhhhh</code>         </td>\n<td align=\"left\"> Matches the character with the Unicode value <code>hhhh</code> (four hexadecimal digits)</td>\n</tr>\n</tbody>\n</table>\n\n\n<p>The literal notation provides compilation of the regular expression when the expression is evaluated. Use\nliteral notation when the regular expression will remain constant. For example, if you use literal notation\nto construct a regular expression used in a loop, the regular expression won't be recompiled on each iteration.</p>\n\n<p>The constructor of the regular expression object, for example, new RegExp(\"ab+c\"), provides runtime\ncompilation of the regular expression. Use the constructor function when you know the regular expression\npattern will be changing, or you don't know the pattern and are getting it from another source, such as user input.</p>\n\n<div class=\"notice\">\nDocumentation for this class comes from <a href=\"https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/RegExp\">MDN</a>\nand is available under <a href=\"http://creativecommons.org/licenses/by-sa/2.0/\">Creative Commons: Attribution-Sharealike license</a>.\n</div>\n\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-global' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='RegExp'>RegExp</span><br/><a href='source/RegExp.html#RegExp-property-global' target='_blank' class='view-source'>view source</a></div><a href='#!/api/RegExp-property-global' class='name expandable'>global</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Whether to test the regular expression against all possible matches in a\nstring, or only against the first. ...</div><div class='long'><p>Whether to test the regular expression against all possible matches in a\nstring, or only against the first.</p>\n\n<p><code>global</code> is a property of an individual regular expression object.</p>\n\n<p>The value of <code>global</code> is true if the \"<code>g</code>\" flag was used; otherwise, <code>false</code>. The \"<code>g</code>\" flag\nindicates that the regular expression should be tested against all possible matches in a string.</p>\n\n<p>You cannot change this property directly.</p>\n</div></div></div><div id='property-ignoreCase' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='RegExp'>RegExp</span><br/><a href='source/RegExp.html#RegExp-property-ignoreCase' target='_blank' class='view-source'>view source</a></div><a href='#!/api/RegExp-property-ignoreCase' class='name expandable'>ignoreCase</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Whether to ignore case while attempting a match in a string. ...</div><div class='long'><p>Whether to ignore case while attempting a match in a string.</p>\n\n<p><code>ignoreCase</code> is a property of an individual regular expression object.</p>\n\n<p>The value of <code>ignoreCase</code> is true if the \"<code>i</code>\" flag was used; otherwise, false. The \"<code>i</code>\" flag indicates\nthat case should be ignored while attempting a match in a string.</p>\n\n<p>You cannot change this property directly.</p>\n</div></div></div><div id='property-lastIndex' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='RegExp'>RegExp</span><br/><a href='source/RegExp.html#RegExp-property-lastIndex' target='_blank' class='view-source'>view source</a></div><a href='#!/api/RegExp-property-lastIndex' class='name expandable'>lastIndex</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The index at which to start the next match. ...</div><div class='long'><p>The index at which to start the next match. A read/write integer property that specifies the index\nat which to start the next match.</p>\n\n<p><code>lastIndex</code> is a property of an individual regular expression object.</p>\n\n<p>This property is set only if the regular expression used the \"<code>g</code>\" flag to indicate a global search.\nThe following rules apply:</p>\n\n<ul>\n<li>If <code>lastIndex</code> is greater than the length of the string, <code>regexp.test</code> and <code>regexp.exec</code> fail,\nand <code>lastIndex</code> is set to 0.</li>\n<li>If <code>lastIndex</code> is equal to the length of the string and if the regular expression matches the\nempty string, then the regular expression matches input starting at <code>lastIndex</code>.</li>\n<li>If <code>lastIndex</code> is equal to the length of the string and if the regular expression does not match\nthe empty string, then the regular expression mismatches input, and <code>lastIndex</code> is reset to 0.</li>\n<li>Otherwise, <code>lastIndex</code> is set to the next position following the most recent match.</li>\n</ul>\n\n\n<p>For example, consider the following sequence of statements:</p>\n\n<ul>\n<li><code>re = /(hi)?/g</code> Matches the empty string.</li>\n<li><code>re(\"hi\")</code> Returns <code>[\"hi\", \"hi\"]</code> with <code>lastIndex</code> equal to 2.</li>\n<li><code>re(\"hi\")</code> Returns <code>[\"\"]</code>, an empty array whose zeroth element is the match string. In this\ncase, the empty string because <code>lastIndex</code> was 2 (and still is 2) and \"<code>hi</code>\" has length 2.</li>\n</ul>\n\n</div></div></div><div id='property-multiline' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='RegExp'>RegExp</span><br/><a href='source/RegExp.html#RegExp-property-multiline' target='_blank' class='view-source'>view source</a></div><a href='#!/api/RegExp-property-multiline' class='name expandable'>multiline</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Whether or not to search in strings across multiple lines. ...</div><div class='long'><p>Whether or not to search in strings across multiple lines.</p>\n\n<p><code>multiline</code> is a property of an individual regular expression object..</p>\n\n<p>The value of <code>multiline</code> is true if the \"<code>m</code>\" flag was used; otherwise, <code>false</code>. The \"<code>m</code>\" flag\nindicates that a multiline input string should be treated as multiple lines. For example, if \"<code>m</code>\"\nis used, \"<code>^</code>\" and \"<code>$</code>\" change from matching at only the start or end of the entire string to the\nstart or end of any line within the string.</p>\n\n<p>You cannot change this property directly.</p>\n</div></div></div><div id='property-source' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='RegExp'>RegExp</span><br/><a href='source/RegExp.html#RegExp-property-source' target='_blank' class='view-source'>view source</a></div><a href='#!/api/RegExp-property-source' class='name expandable'>source</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The text of the pattern. ...</div><div class='long'><p>The text of the pattern.</p>\n\n<p>A read-only property that contains the text of the pattern, excluding the forward slashes.</p>\n\n<p><code>source</code> is a property of an individual regular expression object.</p>\n\n<p>You cannot change this property directly.</p>\n</div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='RegExp'>RegExp</span><br/><a href='source/RegExp.html#RegExp-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/RegExp-method-constructor' class='name expandable'>RegExp</a>( <span class='pre'>pattern, flags</span> ) : <a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a></div><div class='description'><div class='short'>Creates new regular expression object. ...</div><div class='long'><p>Creates new regular expression object.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>pattern</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The text of the regular expression.</p>\n</div></li><li><span class='pre'>flags</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>If specified, flags can have any combination of the following values:</p>\n\n<ul>\n<li>\"g\" - global match</li>\n<li>\"i\" - ignore case</li>\n<li>\"m\" - Treat beginning and end characters (^ and $) as working over multiple lines\n(i.e., match the beginning or end of <em>each</em> line (delimited by \\n or \\r), not\nonly the very beginning or end of the whole input string)</li>\n</ul>\n\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-exec' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='RegExp'>RegExp</span><br/><a href='source/RegExp.html#RegExp-method-exec' target='_blank' class='view-source'>view source</a></div><a href='#!/api/RegExp-method-exec' class='name expandable'>exec</a>( <span class='pre'>str</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Executes a search for a match in its string parameter. ...</div><div class='long'><p>Executes a search for a match in its string parameter.</p>\n\n<p>If the match succeeds, the <code>exec</code> method returns an array and updates properties of the regular\nexpression object. The returned array has the matched text as the first item, and then one item for\neach capturing parenthesis that matched containing the text that was captured.  If the match fails,\nthe <code>exec</code> method returns <code>null</code>.</p>\n\n<p>If you are executing a match simply to find true or false, use the <code>test</code> method or the <code>String\nsearch</code> method.</p>\n\n<p>Consider the following example:</p>\n\n<pre><code>// Match one d followed by one or more b's followed by one d\n// Remember matched b's and the following d\n// Ignore case\nvar re = /d(b+)(d)/ig;\nvar result = re.exec(\"cdbBdbsbz\");\n</code></pre>\n\n<p>The following table shows the results for this script:</p>\n\n<table>\n<thead>\n<tr>\n<th></th>\n<th align=\"left\"> Object           </th>\n<th align=\"left\"> Property/Index </th>\n<th align=\"left\"> Description                                                          </th>\n<th align=\"left\"> Example</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td></td>\n<td align=\"left\"> <code>result</code>         </td>\n<td align=\"left\">                </td>\n<td align=\"left\"> The content of myArray.                                              </td>\n<td align=\"left\"> <code>[\"dbBd\", \"bB\", \"d\"]</code></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> <code>index</code>        </td>\n<td align=\"left\"> The 0-based index of the match in the string                         </td>\n<td align=\"left\"> <code>1</code></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> <code>input</code>        </td>\n<td align=\"left\"> The original string.                                                 </td>\n<td align=\"left\"> <code>cdbDdbsbz</code></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> <code>[0]</code>          </td>\n<td align=\"left\"> The last matched characters.                                         </td>\n<td align=\"left\"> <code>dbBd</code></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> <code>[1], ...[n]</code>  </td>\n<td align=\"left\"> The parenthesized substring matches, if any. The number of possible  </td>\n<td align=\"left\"> <code>[1] = bB</code></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\">                </td>\n<td align=\"left\"> parenthesized substrings is unlimited.                               </td>\n<td align=\"left\"> <code>[2] = d</code></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\"> <code>re</code>             </td>\n<td align=\"left\"> <code>lastIndex</code>    </td>\n<td align=\"left\"> The index at which to start the next match.                          </td>\n<td align=\"left\"> <code>5</code></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> <code>ignoreCase</code>   </td>\n<td align=\"left\"> Indicates the \"<code>i</code>\" flag was used to ignore case.                    </td>\n<td align=\"left\"> <code>true</code></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> <code>global</code>       </td>\n<td align=\"left\"> Indicates the \"<code>g</code>\" flag was used for a global match.                </td>\n<td align=\"left\"> <code>true</code></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> <code>multiline</code>    </td>\n<td align=\"left\"> Indicates the \"<code>m</code>\" flag was used to search in strings across        </td>\n<td align=\"left\"> <code>false</code></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\">                </td>\n<td align=\"left\"> multiple lines.                                                      </td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td align=\"left\">                  </td>\n<td align=\"left\"> <code>source</code>       </td>\n<td align=\"left\"> The text of the pattern.                                             </td>\n<td align=\"left\"> d(b+)(d)</td>\n</tr>\n</tbody>\n</table>\n\n\n<p>If your regular expression uses the \"<code>g</code>\" flag, you can use the <code>exec</code> method multiple times to find\nsuccessive matches in the same string. When you do so, the search starts at the substring of <code>str</code>\nspecified by the regular expression's <code>lastIndex</code> property (<code>test</code> will also advance the <code>lastIndex</code>\nproperty). For example, assume you have this script:</p>\n\n<pre><code>var myRe = /ab*\\/g;\nvar str = \"abbcdefabh\";\nvar myArray;\nwhile ((myArray = myRe.exec(str)) != null)\n{\n    var msg = \"Found \" + myArray[0] + \".  \";\n    msg += \"Next match starts at \" + myRe.lastIndex;\nprint(msg);\n}\n</code></pre>\n\n<p>This script displays the following text:</p>\n\n<pre><code>Found abb. Next match starts at 3\nFound ab. Next match starts at 9\n</code></pre>\n\n<p>You can also use <code>exec()</code> without creating a RegExp object:</p>\n\n<pre><code>var matches = /(hello \\S+)/.exec('This is a hello world!');\nalert(matches[1]);\n</code></pre>\n\n<p>This will display an alert containing 'hello world!';</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>str</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The string against which to match the regular expression.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>Array of results or <code>NULL</code>.</p>\n</div></li></ul></div></div></div><div id='method-test' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='RegExp'>RegExp</span><br/><a href='source/RegExp.html#RegExp-method-test' target='_blank' class='view-source'>view source</a></div><a href='#!/api/RegExp-method-test' class='name expandable'>test</a>( <span class='pre'>str</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Tests for a match in its string parameter. ...</div><div class='long'><p>Tests for a match in its string parameter.</p>\n\n<p>When you want to know whether a pattern is found in a string use the test method (similar to the\n<code><a href=\"#!/api/String-method-search\" rel=\"String-method-search\" class=\"docClass\">String.search</a></code> method); for more information (but slower execution) use the exec method (similar to\nthe <code><a href=\"#!/api/String-method-match\" rel=\"String-method-match\" class=\"docClass\">String.match</a></code> method). As with exec (or in combination with it), test called multiple times on\nthe same global regular expression instance will advance past the previous match.</p>\n\n<p>The following example prints a message which depends on the success of the test:</p>\n\n<pre><code>function testinput(re, str){\n    if (re.test(str))\n        midstring = \" contains \";\n    else\n        midstring = \" does not contain \";\n    document.write (str + midstring + re.source);\n}\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>str</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The string against which to match the regular expression.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>true if string contains any matches, otherwise returns false.</p>\n</div></li></ul></div></div></div><div id='method-toString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='RegExp'>RegExp</span><br/><a href='source/RegExp.html#RegExp-method-toString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/RegExp-method-toString' class='name expandable'>toString</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns a string representing the specified object. ...</div><div class='long'><p>Returns a string representing the specified object. Overrides the <code>Object.prototype.toString</code>\nmethod.</p>\n\n<p>The RegExp object overrides the <code>toString</code> method of the <code>Object</code> object; it does not inherit\n<code><a href=\"#!/api/Object-method-toString\" rel=\"Object-method-toString\" class=\"docClass\">Object.toString</a></code>. For RegExp objects, the <code>toString</code> method returns a string representation of the\nregular expression.</p>\n\n<p>The following example displays the string value of a RegExp object:</p>\n\n<pre><code>myExp = new RegExp(\"a+b+c\");\nalert(myExp.toString());       // displays \"/a+b+c/\"\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Regular expression as a string.</p>\n</div></li></ul></div></div></div></div></div></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"RegExp.html#RegExp","filename":"RegExp.js"}],"linenr":1,"members":{"property":[{"tagname":"property","owner":"RegExp","meta":{},"name":"global","id":"property-global"},{"tagname":"property","owner":"RegExp","meta":{},"name":"ignoreCase","id":"property-ignoreCase"},{"tagname":"property","owner":"RegExp","meta":{},"name":"lastIndex","id":"property-lastIndex"},{"tagname":"property","owner":"RegExp","meta":{},"name":"multiline","id":"property-multiline"},{"tagname":"property","owner":"RegExp","meta":{},"name":"source","id":"property-source"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"RegExp","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"RegExp","meta":{},"name":"exec","id":"method-exec"},{"tagname":"method","owner":"RegExp","meta":{},"name":"test","id":"method-test"},{"tagname":"method","owner":"RegExp","meta":{},"name":"toString","id":"method-toString"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"RegExp","singleton":false,"override":null,"inheritdoc":null,"id":"class-RegExp","mixins":[],"mixedInto":[]});