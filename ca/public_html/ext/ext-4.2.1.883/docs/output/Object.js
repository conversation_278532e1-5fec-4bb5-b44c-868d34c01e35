Ext.data.JsonP.Object({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Object.html#Object' target='_blank'>Object.js</a></div></pre><div class='doc-contents'><p>Creates an object wrapper.</p>\n\n<p>The Object constructor creates an object wrapper for the given value. If the value is null or\nundefined, it will create and return an empty object, otherwise, it will return an object of a type\nthat corresponds to the given value.</p>\n\n<p>When called in a non-constructor context, Object behaves identically.</p>\n\n<h1>Using Object given undefined and null types</h1>\n\n<p>The following examples store an empty Object object in o:</p>\n\n<pre><code>var o = new Object();\n\nvar o = new Object(undefined);\n\nvar o = new Object(null);\n</code></pre>\n\n<h1>Using Object to create Boolean objects</h1>\n\n<p>The following examples store Boolean objects in o:</p>\n\n<pre><code>// equivalent to o = new Boolean(true);\nvar o = new Object(true);\n\n// equivalent to o = new Boolean(false);\nvar o = new Object(Boolean());\n</code></pre>\n\n<div class=\"notice\">\nDocumentation for this class comes from <a href=\"https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Object\">MDN</a>\nand is available under <a href=\"http://creativecommons.org/licenses/by-sa/2.0/\">Creative Commons: Attribution-Sharealike license</a>.\n</div>\n\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-property-constructor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-property-constructor' class='name expandable'>constructor</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>Specifies the function that creates an object's prototype. ...</div><div class='long'><p>Specifies the function that creates an object's prototype.</p>\n\n<p>Returns a reference to the Object function that created the instance's prototype. Note that the\nvalue of this property is a reference to the function itself, not a string containing the\nfunction's name, but it isn't read only (except for primitive Boolean, Number or String values: 1,\ntrue, \"read-only\").</p>\n\n<p>All objects inherit a <code>constructor</code> property from their <code>prototype</code>:</p>\n\n<pre><code>o = new Object // or o = {} in JavaScript 1.2\no.constructor == Object\na = new Array // or a = [] in JavaScript 1.2\na.constructor == Array\nn = new Number(3)\nn.constructor == Number\n</code></pre>\n\n<p>Even though you cannot construct most HTML objects, you can do comparisons. For example,</p>\n\n<pre><code>document.constructor == Document\ndocument.form3.constructor == Form\n</code></pre>\n\n<p>The following example creates a prototype, <code>Tree</code>, and an object of that type, theTree. The example then displays the <code>constructor</code> property for the object <code>theTree</code>.</p>\n\n<pre><code>function Tree(name) {\n    this.name = name;\n}\ntheTree = new Tree(\"Redwood\");\nconsole.log(\"theTree.constructor is \" + theTree.constructor);\n</code></pre>\n\n<p>This example displays the following output:</p>\n\n<pre><code>theTree.constructor is function Tree(name) {\n    this.name = name;\n}\n</code></pre>\n\n<p>The following example shows how to modify constructor value of generic objects. Only true, 1 and\n\"test\" variable constructors will not be changed. This example explains that is not always so safe\nto believe in constructor function.</p>\n\n<pre><code>function Type(){};\nvar types = [\n    new Array,  [],\nnew Boolean,    true,\nnew Date,\nnew Error,\nnew Function,   function(){},\nMath,\nnew Number, 1,\nnew Object, {},\nnew RegExp, /(?:)/,\nnew String, \"test\"\n];\nfor(var i = 0; i &lt; types.length; i++){\n    types[i].constructor = Type;\n    types[i] = [types[i].constructor, types[i] instanceof Type, types[i].toString()];\n};\nalert(types.join(\"\\n\"));\n</code></pre>\n</div></div></div><div id='property-prototype' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-property-prototype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-property-prototype' class='name not-expandable'>prototype</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'><p>Allows the addition of properties to all objects of type Object.</p>\n</div><div class='long'><p>Allows the addition of properties to all objects of type Object.</p>\n</div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/Object-method-constructor' class='name expandable'>Object</a>( <span class='pre'>[value]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Creates new Object. ...</div><div class='long'><p>Creates new Object.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The value to wrap.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-hasOwnProperty' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-method-hasOwnProperty' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-method-hasOwnProperty' class='name expandable'>hasOwnProperty</a>( <span class='pre'>prop</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns a boolean indicating whether an object contains the specified property as a direct property\nof that object an...</div><div class='long'><p>Returns a boolean indicating whether an object contains the specified property as a direct property\nof that object and not inherited through the prototype chain.</p>\n\n<p>Every object descended from <code>Object</code> inherits the <code>hasOwnProperty</code> method. This method can be used\nto determine whether an object has the specified property as a direct property of that object;\nunlike the <code>in</code> operator, this method does not check down the object's prototype chain.</p>\n\n<p>The following example determines whether the o object contains a property named prop:</p>\n\n<pre><code>o = new Object();\no.prop = 'exists';\n\nfunction changeO() {\n    o.newprop = o.prop;\n    delete o.prop;\n}\n\no.hasOwnProperty('prop');   //returns true\nchangeO();\no.hasOwnProperty('prop');   //returns false\n</code></pre>\n\n<p>The following example differentiates between direct properties and properties inherited through the\nprototype chain:</p>\n\n<pre><code>o = new Object();\no.prop = 'exists';\no.hasOwnProperty('prop');             // returns true\no.hasOwnProperty('toString');         // returns false\no.hasOwnProperty('hasOwnProperty');   // returns false\n</code></pre>\n\n<p>The following example shows how to iterate over the properties of an object without executing on\ninherit properties.</p>\n\n<pre><code>var buz = {\n    fog: 'stack'\n};\n\nfor (var name in buz) {\n    if (buz.hasOwnProperty(name)) {\n        alert(\"this is fog (\" + name + \") for sure. Value: \" + buz[name]);\n    }\n    else {\n        alert(name); // toString or something else\n    }\n}\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>prop</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the property to test.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>Returns true if object contains specified property; else\nreturns false.</p>\n</div></li></ul></div></div></div><div id='method-isPrototypeOf' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-method-isPrototypeOf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-method-isPrototypeOf' class='name expandable'>isPrototypeOf</a>( <span class='pre'>prototype, object</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns a boolean indication whether the specified object is in the prototype chain of the object\nthis method is call...</div><div class='long'><p>Returns a boolean indication whether the specified object is in the prototype chain of the object\nthis method is called upon.</p>\n\n<p><code>isPrototypeOf</code> allows you to check whether or not an object exists within another object's\nprototype chain.</p>\n\n<p>For example, consider the following prototype chain:</p>\n\n<pre><code>function Fee() {\n    // . . .\n}\n\nfunction Fi() {\n    // . . .\n}\nFi.prototype = new Fee();\n\nfunction Fo() {\n    // . . .\n}\nFo.prototype = new Fi();\n\nfunction Fum() {\n    // . . .\n}\nFum.prototype = new Fo();\n</code></pre>\n\n<p>Later on down the road, if you instantiate <code>Fum</code> and need to check if <code>Fi</code>'s prototype exists\nwithin the <code>Fum</code> prototype chain, you could do this:</p>\n\n<pre><code>var fum = new Fum();\n. . .\n\nif (Fi.prototype.isPrototypeOf(fum)) {\n// do something safe\n}\n</code></pre>\n\n<p>This, along with the <code>instanceof</code> operator particularly comes in handy if you have code that can\nonly function when dealing with objects descended from a specific prototype chain, e.g., to\nguarantee that certain methods or properties will be present on that object.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>prototype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>an object to be tested against each link in the prototype chain of the\n<em>object</em> argument</p>\n</div></li><li><span class='pre'>object</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>the object whose prototype chain will be searched</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>Returns true if object is a prototype and false if not.</p>\n</div></li></ul></div></div></div><div id='method-propertyIsEnumerable' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-method-propertyIsEnumerable' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-method-propertyIsEnumerable' class='name expandable'>propertyIsEnumerable</a>( <span class='pre'>prop</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns a boolean indicating if the internal ECMAScript DontEnum attribute is set. ...</div><div class='long'><p>Returns a boolean indicating if the internal ECMAScript DontEnum attribute is set.</p>\n\n<p>Every object has a <code>propertyIsEnumerable</code> method. This method can determine whether the specified\nproperty in an object can be enumerated by a <code>for...in</code> loop, with the exception of properties\ninherited through the prototype chain. If the object does not have the specified property, this\nmethod returns false.</p>\n\n<p>The following example shows the use of <code>propertyIsEnumerable</code> on objects and arrays:</p>\n\n<pre><code>var o = {};\nvar a = [];\no.prop = 'is enumerable';\na[0] = 'is enumerable';\n\no.propertyIsEnumerable('prop');   // returns true\na.propertyIsEnumerable(0);        // returns true\n</code></pre>\n\n<p>The following example demonstrates the enumerability of user-defined versus built-in properties:</p>\n\n<pre><code>var a = ['is enumerable'];\n\na.propertyIsEnumerable(0);          // returns true\na.propertyIsEnumerable('length');   // returns false\n\nMath.propertyIsEnumerable('random');   // returns false\nthis.propertyIsEnumerable('Math');     // returns false\n</code></pre>\n\n<p>Direct versus inherited properties</p>\n\n<pre><code>var a = [];\na.propertyIsEnumerable('constructor');         // returns false\n\nfunction firstConstructor()\n{\n    this.property = 'is not enumerable';\n}\nfirstConstructor.prototype.firstMethod = function () {};\n\nfunction secondConstructor()\n{\n    this.method = function method() { return 'is enumerable'; };\n}\n\nsecondConstructor.prototype = new firstConstructor;\nsecondConstructor.prototype.constructor = secondConstructor;\n\nvar o = new secondConstructor();\no.arbitraryProperty = 'is enumerable';\n\no.propertyIsEnumerable('arbitraryProperty');   // returns true\no.propertyIsEnumerable('method');              // returns true\no.propertyIsEnumerable('property');            // returns false\n\no.property = 'is enumerable';\n\no.propertyIsEnumerable('property');            // returns true\n\n// These return false as they are on the prototype which\n// propertyIsEnumerable does not consider (even though the last two\n// are iteratable with for-in)\no.propertyIsEnumerable('prototype'); // returns false (as of JS 1.8.1/FF3.6)\no.propertyIsEnumerable('constructor'); // returns false\no.propertyIsEnumerable('firstMethod'); // returns false\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>prop</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the property to test.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>If the object does not have the specified property, this\nmethod returns false.</p>\n</div></li></ul></div></div></div><div id='method-toLocaleString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-method-toLocaleString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-method-toLocaleString' class='name expandable'>toLocaleString</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns a string representing the object. ...</div><div class='long'><p>Returns a string representing the object. This method is meant to be overridden by derived objects\nfor locale-specific purposes.</p>\n\n<p><code>Object</code>'s <code>toLocaleString</code> returns the result of calling <code>toString</code>.</p>\n\n<p>This function is provided to give objects a generic <code>toLocaleString</code> method, even though not all\nmay use it. Currently, only <code>Array</code>, <code>Number</code>, and <code>Date</code> override <code>toLocaleString</code>.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Object represented as a string.</p>\n</div></li></ul></div></div></div><div id='method-toString' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-method-toString' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-method-toString' class='name expandable'>toString</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns a string representation of the object. ...</div><div class='long'><p>Returns a string representation of the object.</p>\n\n<p>Every object has a <code>toString()</code> method that is automatically called when the object is to be\nrepresented as a text value or when an object is referred to in a manner in which a string is\nexpected. By default, the <code>toString()</code> method is inherited by every object descended from <code>Object</code>.\nIf this method is not overridden in a custom object, <code>toString()</code> returns \"[object type]\", where\n<code>type</code> is the object type. The following code illustrates this:</p>\n\n<pre><code>var o = new Object();\no.toString();           // returns [object Object]\n</code></pre>\n\n<p>You can create a function to be called in place of the default <code>toString()</code> method. The\n<code>toString()</code> method takes no arguments and should return a string. The <code>toString()</code> method you\ncreate can be any value you want, but it will be most useful if it carries information about the\nobject.</p>\n\n<p>The following code defines the <code>Dog</code> object type and creates <code>theDog</code>, an object of type <code>Dog</code>:</p>\n\n<pre><code>function Dog(name,breed,color,sex) {\n    this.name=name;\n    this.breed=breed;\n    this.color=color;\n    this.sex=sex;\n}\n\ntheDog = new Dog(\"Gabby\",\"Lab\",\"chocolate\",\"female\");\n</code></pre>\n\n<p>If you call the <code>toString()</code> method on this custom object, it returns the default value inherited\nfrom <code>Object</code>:</p>\n\n<pre><code>theDog.toString(); //returns [object Object]\n</code></pre>\n\n<p>The following code creates and assigns <code>dogToString()</code> to override the default <code>toString()</code> method.\nThis function generates a string containing the name, breed, color, and sex of the object, in the\nform <code>\"property = value;\"</code>.</p>\n\n<pre><code>Dog.prototype.toString = function dogToString() {\n    var ret = \"Dog \" + this.name + \" is a \" + this.sex + \" \" + this.color + \" \" + this.breed;\n    return ret;\n}\n</code></pre>\n\n<p>With the preceding code in place, any time theDog is used in a string context, JavaScript\nautomatically calls the <code>dogToString()</code> function, which returns the following string:</p>\n\n<pre><code>Dog Gabby is a female chocolate Lab\n</code></pre>\n\n<p><code>toString()</code> can be used with every object and allows you to get its class. To use the\n<code>Object.prototype.toString()</code> with every object, you need to call <code>Function.prototype.call()</code> or\n<code>Function.prototype.apply()</code> on it, passing the object you want to inspect as the first parameter\ncalled <code>thisArg</code>.</p>\n\n<pre><code>var toString = Object.prototype.toString;\n\ntoString.call(new Date); // [object Date]\ntoString.call(new String); // [object String]\ntoString.call(Math); // [object Math]\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>Object represented as a string.</p>\n</div></li></ul></div></div></div><div id='method-valueOf' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-method-valueOf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-method-valueOf' class='name expandable'>valueOf</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Returns the primitive value of the specified object. ...</div><div class='long'><p>Returns the primitive value of the specified object.</p>\n\n<p>JavaScript calls the <code>valueOf</code> method to convert an object to a primitive value. You rarely need to\ninvoke the <code>valueOf</code> method yourself; JavaScript automatically invokes it when encountering an\nobject where a primitive value is expected.</p>\n\n<p>By default, the <code>valueOf</code> method is inherited by every object descended from <code>Object</code>. Every built-\nin core object overrides this method to return an appropriate value. If an object has no primitive\nvalue, <code>valueOf</code> returns the object itself, which is displayed as:</p>\n\n<pre><code>[object Object]\n</code></pre>\n\n<p>You can use <code>valueOf</code> within your own code to convert a built-in object into a primitive value.\nWhen you create a custom object, you can override <code><a href=\"#!/api/Object-method-valueOf\" rel=\"Object-method-valueOf\" class=\"docClass\">Object.valueOf</a></code> to call a custom method instead\nof the default <code>Object</code> method.</p>\n\n<p>You can create a function to be called in place of the default <code>valueOf</code> method. Your function must\ntake no arguments.</p>\n\n<p>Suppose you have an object type <code>myNumberType</code> and you want to create a <code>valueOf</code> method for it.\nThe following code assigns a user-defined function to the object's valueOf method:</p>\n\n<pre><code>myNumberType.prototype.valueOf = new Function(functionText)\n</code></pre>\n\n<p>With the preceding code in place, any time an object of type <code>myNumberType</code> is used in a context\nwhere it is to be represented as a primitive value, JavaScript automatically calls the function\ndefined in the preceding code.</p>\n\n<p>An object's <code>valueOf</code> method is usually invoked by JavaScript, but you can invoke it yourself as\nfollows:</p>\n\n<pre><code>myNumber.valueOf()\n</code></pre>\n\n<p>Note: Objects in string contexts convert via the <code>toString</code> method, which is different from\n<code>String</code> objects converting to string primitives using <code>valueOf</code>. All objects have a string\nconversion, if only <code>\"[object type]\"</code>. But many objects do not convert to number, boolean, or\nfunction.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns value of the object or the object itself.</p>\n</div></li></ul></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-create' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-static-method-create' class='name expandable'>create</a>( <span class='pre'>proto, [propertiesObject]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Creates a new object with the specified prototype object and properties. ...</div><div class='long'><p>Creates a new object with the specified prototype object and properties.</p>\n\n<h2>Classical inheritance with <a href=\"#!/api/Object-static-method-create\" rel=\"Object-static-method-create\" class=\"docClass\">Object.create</a></h2>\n\n<p>Below is an example of how to use <code><a href=\"#!/api/Object-static-method-create\" rel=\"Object-static-method-create\" class=\"docClass\">Object.create</a></code> to achieve\nclassical inheritance, this is for single inheritance, which is all\nthat Javascript supports.</p>\n\n<pre><code>//Shape - superclass\nfunction Shape() {\n  this.x = 0;\n  this.y = 0;\n}\n\nShape.prototype.move = function(x, y) {\n    this.x += x;\n    this.y += y;\n    console.info(\"Shape moved.\");\n};\n\n// Rectangle - subclass\nfunction Rectangle() {\n  Shape.call(this); //call super constructor.\n}\n\nRectangle.prototype = <a href=\"#!/api/Object-static-method-create\" rel=\"Object-static-method-create\" class=\"docClass\">Object.create</a>(Shape.prototype);\n\nvar rect = new Rectangle();\n\nrect instanceof Rectangle //true.\nrect instanceof Shape //true.\n\nrect.move(); //Outputs, \"Shape moved.\"\n</code></pre>\n\n<p>If you wish to inherit from multiple objects, then mixins are a possibility.</p>\n\n<pre><code>function MyClass() {\n     SuperClass.call(this);\n     OtherSuperClass.call(this);\n}\n\nMyClass.prototype = <a href=\"#!/api/Object-static-method-create\" rel=\"Object-static-method-create\" class=\"docClass\">Object.create</a>(SuperClass.prototype); //inherit\nmixin(MyClass.prototype, OtherSuperClass.prototype); //mixin\n\nMyClass.prototype.myMethod = function() {\n     // do a thing\n};\n</code></pre>\n\n<p>The mixin function would copy the functions from the superclass\nprototype to the subclass prototype, the mixin function needs to be\nsupplied by the user.</p>\n\n<h2>Using <code>propertiesObject</code> argument with <a href=\"#!/api/Object-static-method-create\" rel=\"Object-static-method-create\" class=\"docClass\">Object.create</a></h2>\n\n<pre><code>var o;\n\n// create an object with null as prototype\no = <a href=\"#!/api/Object-static-method-create\" rel=\"Object-static-method-create\" class=\"docClass\">Object.create</a>(null);\n\n\no = {};\n// is equivalent to:\no = <a href=\"#!/api/Object-static-method-create\" rel=\"Object-static-method-create\" class=\"docClass\">Object.create</a>(<a href=\"#!/api/Object-property-prototype\" rel=\"Object-property-prototype\" class=\"docClass\">Object.prototype</a>);\n\n\n// Example where we create an object with a couple of sample properties.\n// (Note that the second parameter maps keys to *property descriptors*.)\no = <a href=\"#!/api/Object-static-method-create\" rel=\"Object-static-method-create\" class=\"docClass\">Object.create</a>(<a href=\"#!/api/Object-property-prototype\" rel=\"Object-property-prototype\" class=\"docClass\">Object.prototype</a>, {\n  // foo is a regular \"value property\"\n  foo: { writable:true, configurable:true, value: \"hello\" },\n  // bar is a getter-and-setter (accessor) property\n  bar: {\n    configurable: false,\n    get: function() { return 10 },\n    set: function(value) { console.log(\"Setting `o.bar` to\", value) }\n}})\n\n\nfunction Constructor(){}\no = new Constructor();\n// is equivalent to:\no = <a href=\"#!/api/Object-static-method-create\" rel=\"Object-static-method-create\" class=\"docClass\">Object.create</a>(Constructor.prototype);\n// Of course, if there is actual initialization code in the Constructor function, the <a href=\"#!/api/Object-static-method-create\" rel=\"Object-static-method-create\" class=\"docClass\">Object.create</a> cannot reflect it\n\n\n// create a new object whose prototype is a new, empty object\n// and a adding single property 'p', with value 42\no = <a href=\"#!/api/Object-static-method-create\" rel=\"Object-static-method-create\" class=\"docClass\">Object.create</a>({}, { p: { value: 42 } })\n\n// by default properties ARE NOT writable, enumerable or configurable:\no.p = 24\no.p\n//42\n\no.q = 12\nfor (var prop in o) {\n   console.log(prop)\n}\n//\"q\"\n\ndelete o.p\n//false\n\n//to specify an ES3 property\no2 = <a href=\"#!/api/Object-static-method-create\" rel=\"Object-static-method-create\" class=\"docClass\">Object.create</a>({}, { p: { value: 42, writable: true, enumerable: true, configurable: true } });\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>proto</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object which should be the prototype of\nthe newly-created object.</p>\n\n<p>Throws a <code>TypeError</code> exception if the <code>proto</code> parameter isn't null or\nan object.</p>\n</div></li><li><span class='pre'>propertiesObject</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If specified and not undefined,\nan object whose enumerable own properties (that is, those\nproperties defined upon itself and not enumerable properties along\nits prototype chain) specify property descriptors to be added to\nthe newly-created object, with the corresponding property names.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the newly created object.</p>\n</div></li></ul></div></div></div><div id='static-method-defineProperties' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-static-method-defineProperties' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-static-method-defineProperties' class='name expandable'>defineProperties</a>( <span class='pre'>obj, props</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Defines new or modifies existing properties directly on an object,\nreturning the object. ...</div><div class='long'><p>Defines new or modifies existing properties directly on an object,\nreturning the object.</p>\n\n<p>In essence, it defines all properties corresponding to the\nenumerable own properties of props on the object.</p>\n\n<pre><code><a href=\"#!/api/Object-static-method-defineProperties\" rel=\"Object-static-method-defineProperties\" class=\"docClass\">Object.defineProperties</a>(obj, {\n  \"property1\": {\n    value: true,\n    writable: true\n  },\n  \"property2\": {\n    value: \"Hello\",\n    writable: false\n  }\n  // etc. etc.\n});\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>obj</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object on which to define or modify properties.</p>\n</div></li><li><span class='pre'>props</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>An object whose own enumerable properties\nconstitute descriptors for the properties to be defined or\nmodified.</p>\n</div></li></ul></div></div></div><div id='static-method-defineProperty' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-static-method-defineProperty' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-static-method-defineProperty' class='name expandable'>defineProperty</a>( <span class='pre'>obj, prop, descriptor</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Defines a new property directly on an object, or modifies an\nexisting property on an object, and returns the object. ...</div><div class='long'><p>Defines a new property directly on an object, or modifies an\nexisting property on an object, and returns the object.</p>\n\n<p>This method allows precise addition to or modification of a\nproperty on an object. Normal property addition through assignment\ncreates properties which show up during property enumeration\n(for...in loop or <a href=\"#!/api/Object-static-method-keys\" rel=\"Object-static-method-keys\" class=\"docClass\">keys</a> method), whose values may be\nchanged, and which may be deleted. This method allows these extra\ndetails to be changed from their defaults.</p>\n\n<p>Property descriptors present in objects come in two main flavors:\ndata descriptors and accessor descriptors. A data descriptor is a\nproperty that has a value, which may or may not be writable. An\naccessor descriptor is a property described by a getter-setter pair\nof functions. A descriptor must be one of these two flavors; it\ncannot be both.</p>\n\n<p>Both data and accessor descriptor is an object with the following\noptional keys:</p>\n\n<ul>\n<li><p><strong>configurable</strong> True if and only if the type of this property\ndescriptor may be changed and if the property may be deleted from\nthe corresponding object. Defaults to false.</p></li>\n<li><p><strong>enumerable</strong> True if and only if this property shows up during\nenumeration of the properties on the corresponding\nobject. Defaults to false.</p></li>\n</ul>\n\n\n<p>A data descriptor is an object with the following optional keys:</p>\n\n<ul>\n<li><p><strong>value</strong> The value associated with the property. Can be any\nvalid JavaScript value (number, object, function, etc) Defaults\nto undefined.</p></li>\n<li><p><strong>writable</strong> True if and only if the value associated with the\nproperty may be changed with an assignment operator. Defaults to\nfalse.</p></li>\n</ul>\n\n\n<p>An accessor descriptor is an object with the following optional\nkeys:</p>\n\n<ul>\n<li><p><strong>get</strong> A function which serves as a getter for the property, or\nundefined if there is no getter. The function return will be used\nas the value of property. Defaults to undefined.</p></li>\n<li><p><strong>set</strong> A function which serves as a setter for the property, or\nundefined if there is no setter. The function will receive as\nonly argument the new value being assigned to the\nproperty. Defaults to undefined.</p></li>\n</ul>\n\n\n<p>Bear in mind that these options are not necessarily own properties\nso, if inherited, will be considered too. In order to ensure these\ndefaults are preserved you might freeze the <a href=\"#!/api/Object-property-prototype\" rel=\"Object-property-prototype\" class=\"docClass\">Object.prototype</a>\nupfront, specify all options explicitly, or point to null as\n<strong>proto</strong> property.</p>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>obj</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object on which to define the property.</p>\n</div></li><li><span class='pre'>prop</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the property to be defined or modified.</p>\n</div></li><li><span class='pre'>descriptor</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The descriptor for the property being\ndefined or modified.</p>\n</div></li></ul></div></div></div><div id='static-method-freeze' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-static-method-freeze' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-static-method-freeze' class='name expandable'>freeze</a>( <span class='pre'>obj</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Freezes an object: that is, prevents new properties from being\nadded to it; prevents existing properties from being r...</div><div class='long'><p>Freezes an object: that is, prevents new properties from being\nadded to it; prevents existing properties from being removed; and\nprevents existing properties, or their enumerability,\nconfigurability, or writability, from being changed. In essence the\nobject is made effectively immutable. The method returns the object\nbeing frozen.</p>\n\n<p>Nothing can be added to or removed from the properties set of a\nfrozen object. Any attempt to do so will fail, either silently or\nby throwing a TypeError exception (most commonly, but not\nexclusively, when in strict mode).</p>\n\n<p>Values cannot be changed for data properties. Accessor properties\n(getters and setters) work the same (and still give the illusion\nthat you are changing the value). Note that values that are objects\ncan still be modified, unless they are also frozen.</p>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>obj</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object to freeze.</p>\n</div></li></ul></div></div></div><div id='static-method-getOwnPropertyDescriptor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-static-method-getOwnPropertyDescriptor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-static-method-getOwnPropertyDescriptor' class='name expandable'>getOwnPropertyDescriptor</a>( <span class='pre'>obj, prop</span> ) : Mixed<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Returns a property descriptor for an own property (that is, one\ndirectly present on an object, not present by dint of...</div><div class='long'><p>Returns a property descriptor for an own property (that is, one\ndirectly present on an object, not present by dint of being along\nan object's prototype chain) of a given object.</p>\n\n<p>This method permits examination of the precise description of a\nproperty. A property in JavaScript consists of a string-valued name\nand a property descriptor. Further information about property\ndescriptor types and their attributes can be found in\n<a href=\"#!/api/Object-static-method-defineProperty\" rel=\"Object-static-method-defineProperty\" class=\"docClass\">defineProperty</a>.</p>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>obj</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object in which to look for the property.</p>\n</div></li><li><span class='pre'>prop</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the property whose description is\nto be retrieved.</p>\n\n<p>A property descriptor is a record with some of the following\nattributes:</p>\n\n<ul>\n<li><p><strong>value</strong> The value associated with the property (data\ndescriptors only).</p></li>\n<li><p><strong>writable</strong> True if and only if the value associated with\nthe property may be changed (data descriptors only).</p></li>\n<li><p><strong>get</strong> A function which serves as a getter for the property,\n or undefined if there is no getter (accessor descriptors only).</p></li>\n<li><p><strong>set</strong> A function which serves as a setter for the property,\nor undefined if there is no setter (accessor descriptors only).</p></li>\n<li><p><strong>configurable</strong> true if and only if the type of this property\ndescriptor may be changed and if the property may be deleted\nfrom the corresponding object.</p></li>\n<li><p><strong>enumerable</strong> true if and only if this property shows up\nduring enumeration of the properties on the corresponding object.</p></li>\n</ul>\n\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>Mixed</span><div class='sub-desc'><p>Value of the property descriptor.</p>\n</div></li></ul></div></div></div><div id='static-method-getOwnPropertyNames' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-static-method-getOwnPropertyNames' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-static-method-getOwnPropertyNames' class='name expandable'>getOwnPropertyNames</a>( <span class='pre'>obj</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Returns an array of all properties (enumerable or not) found\ndirectly upon a given object. ...</div><div class='long'><p>Returns an array of all properties (enumerable or not) found\ndirectly upon a given object.</p>\n\n<p>Rreturns an array whose elements are strings corresponding to the\nenumerable and non-enumerable properties found directly upon\nobj. The ordering of the enumerable properties in the array is\nconsistent with the ordering exposed by a for...in loop (or by\n<a href=\"#!/api/Object-static-method-keys\" rel=\"Object-static-method-keys\" class=\"docClass\">keys</a>) over the properties of the object. The\nordering of the non-enumerable properties in the array, and among\nthe enumerable properties, is not defined.</p>\n\n<pre><code>var arr = [\"a\", \"b\", \"c\"];\nprint(<a href=\"#!/api/Object-static-method-getOwnPropertyNames\" rel=\"Object-static-method-getOwnPropertyNames\" class=\"docClass\">Object.getOwnPropertyNames</a>(arr).sort()); // prints \"0,1,2,length\"\n\n// Array-like object\nvar obj = { 0: \"a\", 1: \"b\", 2: \"c\"};\nprint(<a href=\"#!/api/Object-static-method-getOwnPropertyNames\" rel=\"Object-static-method-getOwnPropertyNames\" class=\"docClass\">Object.getOwnPropertyNames</a>(obj).sort()); // prints \"0,1,2\"\n\n// Printing property names and values using <a href=\"#!/api/Array-method-forEach\" rel=\"Array-method-forEach\" class=\"docClass\">Array.forEach</a>\n<a href=\"#!/api/Object-static-method-getOwnPropertyNames\" rel=\"Object-static-method-getOwnPropertyNames\" class=\"docClass\">Object.getOwnPropertyNames</a>(obj).forEach(function(val, idx, array) {\n  print(val + \" -&gt; \" + obj[val]);\n});\n// prints\n// 0 -&gt; a\n// 1 -&gt; b\n// 2 -&gt; c\n\n// non-enumerable property\nvar my_obj = <a href=\"#!/api/Object-static-method-create\" rel=\"Object-static-method-create\" class=\"docClass\">Object.create</a>({}, { getFoo: { value: function() { return this.foo; }, enumerable: false } });\nmy_obj.foo = 1;\n\nprint(<a href=\"#!/api/Object-static-method-getOwnPropertyNames\" rel=\"Object-static-method-getOwnPropertyNames\" class=\"docClass\">Object.getOwnPropertyNames</a>(my_obj).sort()); // prints \"foo, getFoo\"\n</code></pre>\n\n<p>If you want only the enumerable properties, see <a href=\"#!/api/Object-static-method-keys\" rel=\"Object-static-method-keys\" class=\"docClass\">keys</a>\nor use a for...in loop (although note that this will return\nenumerable properties not found directly upon that object but also\nalong the prototype chain for the object unless the latter is\nfiltered with <a href=\"#!/api/Object-method-hasOwnProperty\" rel=\"Object-method-hasOwnProperty\" class=\"docClass\">hasOwnProperty</a>).</p>\n\n<p>Items on the prototype chain are not listed:</p>\n\n<pre><code>function ParentClass () {\n}\nParentClass.prototype.inheritedMethod = function () {\n};\n\nfunction ChildClass () {\n  this.prop = 5;\n  this.method = function () {};\n}\nChildClass.prototype = new ParentClass;\nChildClass.prototype.prototypeMethod = function () {\n};\n\nalert(\n  <a href=\"#!/api/Object-static-method-getOwnPropertyNames\" rel=\"Object-static-method-getOwnPropertyNames\" class=\"docClass\">Object.getOwnPropertyNames</a>(\n    new ChildClass() // [\"prop\", \"method\"]\n  )\n)\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>obj</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object whose enumerable and non-enumerable\nown properties are to be returned.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</span><div class='sub-desc'><p>Array of property names.</p>\n</div></li></ul></div></div></div><div id='static-method-getPrototypeOf' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-static-method-getPrototypeOf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-static-method-getPrototypeOf' class='name expandable'>getPrototypeOf</a>( <span class='pre'>object</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Returns the prototype (i.e. ...</div><div class='long'><p>Returns the prototype (i.e. the internal <code>[[Prototype]]</code>) of the\nspecified object.</p>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>object</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object whose prototype is to be returned.\nThrows a TypeError exception if this parameter isn't an Object.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the prototype</p>\n</div></li></ul></div></div></div><div id='static-method-isExtensible' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-static-method-isExtensible' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-static-method-isExtensible' class='name expandable'>isExtensible</a>( <span class='pre'>obj</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Determines if an object is extensible (whether it can have new\nproperties added to it). ...</div><div class='long'><p>Determines if an object is extensible (whether it can have new\nproperties added to it).</p>\n\n<p>Objects are extensible by default: they can have new properties\nadded to them, and can be modified. An object can be marked as\nnon-extensible using <a href=\"#!/api/Object-static-method-preventExtensions\" rel=\"Object-static-method-preventExtensions\" class=\"docClass\">preventExtensions</a>,\n<a href=\"#!/api/Object-static-method-seal\" rel=\"Object-static-method-seal\" class=\"docClass\">seal</a>, or <a href=\"#!/api/Object-static-method-freeze\" rel=\"Object-static-method-freeze\" class=\"docClass\">freeze</a>.</p>\n\n<pre><code>// New objects are extensible.\nvar empty = {};\nassert(<a href=\"#!/api/Object-static-method-isExtensible\" rel=\"Object-static-method-isExtensible\" class=\"docClass\">Object.isExtensible</a>(empty) === true);\n\n// ...but that can be changed.\n<a href=\"#!/api/Object-static-method-preventExtensions\" rel=\"Object-static-method-preventExtensions\" class=\"docClass\">Object.preventExtensions</a>(empty);\nassert(<a href=\"#!/api/Object-static-method-isExtensible\" rel=\"Object-static-method-isExtensible\" class=\"docClass\">Object.isExtensible</a>(empty) === false);\n\n// Sealed objects are by definition non-extensible.\nvar sealed = <a href=\"#!/api/Object-static-method-seal\" rel=\"Object-static-method-seal\" class=\"docClass\">Object.seal</a>({});\nassert(<a href=\"#!/api/Object-static-method-isExtensible\" rel=\"Object-static-method-isExtensible\" class=\"docClass\">Object.isExtensible</a>(sealed) === false);\n\n// Frozen objects are also by definition non-extensible.\nvar frozen = <a href=\"#!/api/Object-static-method-freeze\" rel=\"Object-static-method-freeze\" class=\"docClass\">Object.freeze</a>({});\nassert(<a href=\"#!/api/Object-static-method-isExtensible\" rel=\"Object-static-method-isExtensible\" class=\"docClass\">Object.isExtensible</a>(frozen) === false);\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>obj</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object which should be checked.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True when object is extensible.</p>\n</div></li></ul></div></div></div><div id='static-method-isFrozen' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-static-method-isFrozen' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-static-method-isFrozen' class='name expandable'>isFrozen</a>( <span class='pre'>obj</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Determines if an object is frozen. ...</div><div class='long'><p>Determines if an object is frozen.</p>\n\n<p>An object is frozen if and only if it is not extensible, all its\nproperties are non-configurable, and all its data properties (that\nis, properties which are not accessor properties with getter or\nsetter components) are non-writable.</p>\n\n<pre><code>// A new object is extensible, so it is not frozen.\nassert(<a href=\"#!/api/Object-static-method-isFrozen\" rel=\"Object-static-method-isFrozen\" class=\"docClass\">Object.isFrozen</a>({}) === false);\n\n// An empty object which is not extensible is vacuously frozen.\nvar vacuouslyFrozen = <a href=\"#!/api/Object-static-method-preventExtensions\" rel=\"Object-static-method-preventExtensions\" class=\"docClass\">Object.preventExtensions</a>({});\nassert(<a href=\"#!/api/Object-static-method-isFrozen\" rel=\"Object-static-method-isFrozen\" class=\"docClass\">Object.isFrozen</a>(vacuouslyFrozen) === true);\n\n// A new object with one property is also extensible, ergo not frozen.\nvar oneProp = { p: 42 };\nassert(<a href=\"#!/api/Object-static-method-isFrozen\" rel=\"Object-static-method-isFrozen\" class=\"docClass\">Object.isFrozen</a>(oneProp) === false);\n\n// Preventing extensions to the object still doesn't make it frozen,\n// because the property is still configurable (and writable).\n<a href=\"#!/api/Object-static-method-preventExtensions\" rel=\"Object-static-method-preventExtensions\" class=\"docClass\">Object.preventExtensions</a>(oneProp);\nassert(<a href=\"#!/api/Object-static-method-isFrozen\" rel=\"Object-static-method-isFrozen\" class=\"docClass\">Object.isFrozen</a>(oneProp) === false);\n\n// ...but then deleting that property makes the object vacuously frozen.\ndelete oneProp.p;\nassert(<a href=\"#!/api/Object-static-method-isFrozen\" rel=\"Object-static-method-isFrozen\" class=\"docClass\">Object.isFrozen</a>(oneProp) === true);\n\n// A non-extensible object with a non-writable but still configurable property is not frozen.\nvar nonWritable = { e: \"plep\" };\n<a href=\"#!/api/Object-static-method-preventExtensions\" rel=\"Object-static-method-preventExtensions\" class=\"docClass\">Object.preventExtensions</a>(nonWritable);\n<a href=\"#!/api/Object-static-method-defineProperty\" rel=\"Object-static-method-defineProperty\" class=\"docClass\">Object.defineProperty</a>(nonWritable, \"e\", { writable: false }); // make non-writable\nassert(<a href=\"#!/api/Object-static-method-isFrozen\" rel=\"Object-static-method-isFrozen\" class=\"docClass\">Object.isFrozen</a>(nonWritable) === false);\n\n// Changing that property to non-configurable then makes the object frozen.\n<a href=\"#!/api/Object-static-method-defineProperty\" rel=\"Object-static-method-defineProperty\" class=\"docClass\">Object.defineProperty</a>(nonWritable, \"e\", { configurable: false }); // make non-configurable\nassert(<a href=\"#!/api/Object-static-method-isFrozen\" rel=\"Object-static-method-isFrozen\" class=\"docClass\">Object.isFrozen</a>(nonWritable) === true);\n\n// A non-extensible object with a non-configurable but still writable property also isn't frozen.\nvar nonConfigurable = { release: \"the kraken!\" };\n<a href=\"#!/api/Object-static-method-preventExtensions\" rel=\"Object-static-method-preventExtensions\" class=\"docClass\">Object.preventExtensions</a>(nonConfigurable);\n<a href=\"#!/api/Object-static-method-defineProperty\" rel=\"Object-static-method-defineProperty\" class=\"docClass\">Object.defineProperty</a>(nonConfigurable, \"release\", { configurable: false });\nassert(<a href=\"#!/api/Object-static-method-isFrozen\" rel=\"Object-static-method-isFrozen\" class=\"docClass\">Object.isFrozen</a>(nonConfigurable) === false);\n\n// Changing that property to non-writable then makes the object frozen.\n<a href=\"#!/api/Object-static-method-defineProperty\" rel=\"Object-static-method-defineProperty\" class=\"docClass\">Object.defineProperty</a>(nonConfigurable, \"release\", { writable: false });\nassert(<a href=\"#!/api/Object-static-method-isFrozen\" rel=\"Object-static-method-isFrozen\" class=\"docClass\">Object.isFrozen</a>(nonConfigurable) === true);\n\n// A non-extensible object with a configurable accessor property isn't frozen.\nvar accessor = { get food() { return \"yum\"; } };\n<a href=\"#!/api/Object-static-method-preventExtensions\" rel=\"Object-static-method-preventExtensions\" class=\"docClass\">Object.preventExtensions</a>(accessor);\nassert(<a href=\"#!/api/Object-static-method-isFrozen\" rel=\"Object-static-method-isFrozen\" class=\"docClass\">Object.isFrozen</a>(accessor) === false);\n\n// ...but make that property non-configurable and it becomes frozen.\n<a href=\"#!/api/Object-static-method-defineProperty\" rel=\"Object-static-method-defineProperty\" class=\"docClass\">Object.defineProperty</a>(accessor, \"food\", { configurable: false });\nassert(<a href=\"#!/api/Object-static-method-isFrozen\" rel=\"Object-static-method-isFrozen\" class=\"docClass\">Object.isFrozen</a>(accessor) === true);\n\n// But the easiest way for an object to be frozen is if <a href=\"#!/api/Object-static-method-freeze\" rel=\"Object-static-method-freeze\" class=\"docClass\">Object.freeze</a> has been called on it.\nvar frozen = { 1: 81 };\nassert(<a href=\"#!/api/Object-static-method-isFrozen\" rel=\"Object-static-method-isFrozen\" class=\"docClass\">Object.isFrozen</a>(frozen) === false);\n<a href=\"#!/api/Object-static-method-freeze\" rel=\"Object-static-method-freeze\" class=\"docClass\">Object.freeze</a>(frozen);\nassert(<a href=\"#!/api/Object-static-method-isFrozen\" rel=\"Object-static-method-isFrozen\" class=\"docClass\">Object.isFrozen</a>(frozen) === true);\n\n// By definition, a frozen object is non-extensible.\nassert(<a href=\"#!/api/Object-static-method-isExtensible\" rel=\"Object-static-method-isExtensible\" class=\"docClass\">Object.isExtensible</a>(frozen) === false);\n\n// Also by definition, a frozen object is sealed.\nassert(<a href=\"#!/api/Object-static-method-isSealed\" rel=\"Object-static-method-isSealed\" class=\"docClass\">Object.isSealed</a>(frozen) === true);\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>obj</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object which should be checked.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if the object is frozen, otherwise false.</p>\n</div></li></ul></div></div></div><div id='static-method-isSealed' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-static-method-isSealed' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-static-method-isSealed' class='name expandable'>isSealed</a>( <span class='pre'>obj</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Determines if an object is sealed. ...</div><div class='long'><p>Determines if an object is sealed.</p>\n\n<p>An object is sealed if it is non-extensible and if all its\nproperties are non-configurable and therefore not removable (but\nnot necessarily non-writable).</p>\n\n<pre><code>// Objects aren't sealed by default.\nvar empty = {};\nassert(<a href=\"#!/api/Object-static-method-isSealed\" rel=\"Object-static-method-isSealed\" class=\"docClass\">Object.isSealed</a>(empty) === false);\n\n// If you make an empty object non-extensible, it is vacuously sealed.\n<a href=\"#!/api/Object-static-method-preventExtensions\" rel=\"Object-static-method-preventExtensions\" class=\"docClass\">Object.preventExtensions</a>(empty);\nassert(<a href=\"#!/api/Object-static-method-isSealed\" rel=\"Object-static-method-isSealed\" class=\"docClass\">Object.isSealed</a>(empty) === true);\n\n// The same is not true of a non-empty object, unless its properties are all non-configurable.\nvar hasProp = { fee: \"fie foe fum\" };\n<a href=\"#!/api/Object-static-method-preventExtensions\" rel=\"Object-static-method-preventExtensions\" class=\"docClass\">Object.preventExtensions</a>(hasProp);\nassert(<a href=\"#!/api/Object-static-method-isSealed\" rel=\"Object-static-method-isSealed\" class=\"docClass\">Object.isSealed</a>(hasProp) === false);\n\n// But make them all non-configurable and the object becomes sealed.\n<a href=\"#!/api/Object-static-method-defineProperty\" rel=\"Object-static-method-defineProperty\" class=\"docClass\">Object.defineProperty</a>(hasProp, \"fee\", { configurable: false });\nassert(<a href=\"#!/api/Object-static-method-isSealed\" rel=\"Object-static-method-isSealed\" class=\"docClass\">Object.isSealed</a>(hasProp) === true);\n\n// The easiest way to seal an object, of course, is <a href=\"#!/api/Object-static-method-seal\" rel=\"Object-static-method-seal\" class=\"docClass\">Object.seal</a>.\nvar sealed = {};\n<a href=\"#!/api/Object-static-method-seal\" rel=\"Object-static-method-seal\" class=\"docClass\">Object.seal</a>(sealed);\nassert(<a href=\"#!/api/Object-static-method-isSealed\" rel=\"Object-static-method-isSealed\" class=\"docClass\">Object.isSealed</a>(sealed) === true);\n\n// A sealed object is, by definition, non-extensible.\nassert(<a href=\"#!/api/Object-static-method-isExtensible\" rel=\"Object-static-method-isExtensible\" class=\"docClass\">Object.isExtensible</a>(sealed) === false);\n\n// A sealed object might be frozen, but it doesn't have to be.\nassert(<a href=\"#!/api/Object-static-method-isFrozen\" rel=\"Object-static-method-isFrozen\" class=\"docClass\">Object.isFrozen</a>(sealed) === true); // all properties also non-writable\n\nvar s2 = <a href=\"#!/api/Object-static-method-seal\" rel=\"Object-static-method-seal\" class=\"docClass\">Object.seal</a>({ p: 3 });\nassert(<a href=\"#!/api/Object-static-method-isFrozen\" rel=\"Object-static-method-isFrozen\" class=\"docClass\">Object.isFrozen</a>(s2) === false); // \"p\" is still writable\n\nvar s3 = <a href=\"#!/api/Object-static-method-seal\" rel=\"Object-static-method-seal\" class=\"docClass\">Object.seal</a>({ get p() { return 0; } });\nassert(<a href=\"#!/api/Object-static-method-isFrozen\" rel=\"Object-static-method-isFrozen\" class=\"docClass\">Object.isFrozen</a>(s3) === true); // only configurability matters for accessor properties\n</code></pre>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>obj</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object which should be checked.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if the object is sealed, otherwise false.</p>\n</div></li></ul></div></div></div><div id='static-method-keys' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-static-method-keys' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-static-method-keys' class='name expandable'>keys</a>( <span class='pre'>obj</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Returns an array of a given object's own enumerable properties, in\nthe same order as that provided by a for-in loop (...</div><div class='long'><p>Returns an array of a given object's own enumerable properties, in\nthe same order as that provided by a for-in loop (the difference\nbeing that a for-in loop enumerates properties in the prototype\nchain as well).</p>\n\n<p>Returns an array whose elements are strings corresponding to the\nenumerable properties found directly upon object. The ordering of\nthe properties is the same as that given by looping over the\nproperties of the object manually.</p>\n\n<pre><code>var arr = [\"a\", \"b\", \"c\"];\nalert(<a href=\"#!/api/Object-static-method-keys\" rel=\"Object-static-method-keys\" class=\"docClass\">Object.keys</a>(arr)); // will alert \"0,1,2\"\n\n// array like object\nvar obj = { 0 : \"a\", 1 : \"b\", 2 : \"c\"};\nalert(<a href=\"#!/api/Object-static-method-keys\" rel=\"Object-static-method-keys\" class=\"docClass\">Object.keys</a>(obj)); // will alert \"0,1,2\"\n\n// getFoo is property which isn't enumerable\nvar my_obj = <a href=\"#!/api/Object-static-method-create\" rel=\"Object-static-method-create\" class=\"docClass\">Object.create</a>({}, { getFoo : { value : function () { return this.foo } } });\nmy_obj.foo = 1;\n\nalert(<a href=\"#!/api/Object-static-method-keys\" rel=\"Object-static-method-keys\" class=\"docClass\">Object.keys</a>(my_obj)); // will alert only foo\n</code></pre>\n\n<p>If you want all properties, even the not enumerable, see\n<a href=\"#!/api/Object-static-method-getOwnPropertyNames\" rel=\"Object-static-method-getOwnPropertyNames\" class=\"docClass\">getOwnPropertyNames</a>.</p>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>obj</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object whose enumerable own properties are\nto be returned.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</span><div class='sub-desc'><p>Array of property names.</p>\n</div></li></ul></div></div></div><div id='static-method-preventExtensions' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-static-method-preventExtensions' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-static-method-preventExtensions' class='name expandable'>preventExtensions</a>( <span class='pre'>obj</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Prevents new properties from ever being added to an object\n(i.e. ...</div><div class='long'><p>Prevents new properties from ever being added to an object\n(i.e. prevents future extensions to the object).</p>\n\n<p>An object is extensible if new properties can be added to it.\n<code>preventExtensions</code> marks an object as no longer extensible, so that\nit will never have properties beyond the ones it had at the time it\nwas marked as non-extensible.  Note that the properties of a\nnon-extensible object, in general, may still be deleted. Attempting\nto add new properties to a non-extensible object will fail, either\nsilently or by throwing a TypeError (most commonly, but not\nexclusively, when in strict mode).</p>\n\n<p>It only prevents addition of own properties. Properties can still\nbe added to the object prototype.</p>\n\n<p>If there is a way to turn an extensible object to a non-extensible\none, there is no way to do the opposite in ECMAScript 5</p>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>obj</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object which should be made non-extensible.</p>\n</div></li></ul></div></div></div><div id='static-method-seal' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Object'>Object</span><br/><a href='source/Object.html#Object-static-method-seal' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Object-static-method-seal' class='name expandable'>seal</a>( <span class='pre'>obj</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Seals an object, preventing new properties from being added to it\nand marking all existing properties as non-configur...</div><div class='long'><p>Seals an object, preventing new properties from being added to it\nand marking all existing properties as non-configurable. Values of\npresent properties can still be changed as long as they are\nwritable.</p>\n\n<p>By default, objects are extensible (new properties can be added to\nthem). Sealing an object prevents new properties from being added\nand marks all existing properties as non-configurable. This has the\neffect of making the set of properties on the object fixed and\nimmutable. Making all properties non-configurable also prevents\nthem from being converted from data properties to accessor\nproperties and vice versa, but it does not prevent the values of\ndata properties from being changed. Attempting to delete or add\nproperties to a sealed object, or to convert a data property to\naccessor or vice versa, will fail, either silently or by throwing a\nTypeError (most commonly, although not exclusively, when in strict\nmode code).</p>\n\n<p>The prototype chain remains untouched.</p>\n\n<p><strong>NOTE:</strong> This method is part of the ECMAScript 5 standard.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>obj</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The object which should be sealed.</p>\n</div></li></ul></div></div></div></div></div></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Object","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Object","meta":{"static":true},"name":"defineProperties","id":"static-method-defineProperties"},{"tagname":"method","owner":"Object","meta":{"static":true},"name":"defineProperty","id":"static-method-defineProperty"},{"tagname":"method","owner":"Object","meta":{"static":true},"name":"freeze","id":"static-method-freeze"},{"tagname":"method","owner":"Object","meta":{"static":true},"name":"getOwnPropertyDescriptor","id":"static-method-getOwnPropertyDescriptor"},{"tagname":"method","owner":"Object","meta":{"static":true},"name":"getOwnPropertyNames","id":"static-method-getOwnPropertyNames"},{"tagname":"method","owner":"Object","meta":{"static":true},"name":"getPrototypeOf","id":"static-method-getPrototypeOf"},{"tagname":"method","owner":"Object","meta":{"static":true},"name":"isExtensible","id":"static-method-isExtensible"},{"tagname":"method","owner":"Object","meta":{"static":true},"name":"isFrozen","id":"static-method-isFrozen"},{"tagname":"method","owner":"Object","meta":{"static":true},"name":"isSealed","id":"static-method-isSealed"},{"tagname":"method","owner":"Object","meta":{"static":true},"name":"keys","id":"static-method-keys"},{"tagname":"method","owner":"Object","meta":{"static":true},"name":"preventExtensions","id":"static-method-preventExtensions"},{"tagname":"method","owner":"Object","meta":{"static":true},"name":"seal","id":"static-method-seal"}],"event":[],"css_mixin":[]},"files":[{"href":"Object.html#Object","filename":"Object.js"}],"linenr":1,"members":{"property":[{"tagname":"property","owner":"Object","meta":{},"name":"constructor","id":"property-constructor"},{"tagname":"property","owner":"Object","meta":{},"name":"prototype","id":"property-prototype"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Object","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"Object","meta":{},"name":"hasOwnProperty","id":"method-hasOwnProperty"},{"tagname":"method","owner":"Object","meta":{},"name":"isPrototypeOf","id":"method-isPrototypeOf"},{"tagname":"method","owner":"Object","meta":{},"name":"propertyIsEnumerable","id":"method-propertyIsEnumerable"},{"tagname":"method","owner":"Object","meta":{},"name":"toLocaleString","id":"method-toLocaleString"},{"tagname":"method","owner":"Object","meta":{},"name":"toString","id":"method-toString"},{"tagname":"method","owner":"Object","meta":{},"name":"valueOf","id":"method-valueOf"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Object","singleton":false,"override":null,"inheritdoc":null,"id":"class-Object","mixins":[],"mixedInto":[]});