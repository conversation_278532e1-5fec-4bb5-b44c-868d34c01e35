Ext.data.JsonP.Ext_data_NodeInterface({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":"Ext.Base","uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><strong>Ext.data.NodeInterface</strong></div></div><h4>Requires</h4><div class='dependency'><a href='#!/api/Ext.data.Field' rel='Ext.data.Field' class='docClass'>Ext.data.Field</a></div><div class='dependency'><a href='#!/api/Ext.data.writer.Json' rel='Ext.data.writer.Json' class='docClass'>Ext.data.writer.Json</a></div><h4>Files</h4><div class='dependency'><a href='source/NodeInterface.html#Ext-data-NodeInterface' target='_blank'>NodeInterface.js</a></div></pre><div class='doc-contents'><p>This class is used as a set of methods that are applied to the prototype of a\nModel to decorate it with a Node API. This means that models used in conjunction with a tree\nwill have all of the tree related methods available on the model. In general this class will\nnot be used directly by the developer. This class also creates extra fields on the model if\nthey do not exist, to help maintain the tree state and UI. These fields are documented as\nconfig options.</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-cfg'>Config options</h3><div class='subsection'><div id='cfg-allowDrag' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-allowDrag' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-allowDrag' class='name expandable'>allowDrag</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Set to false to deny dragging of this node. ...</div><div class='long'><p>Set to false to deny dragging of this node.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-allowDrop' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-allowDrop' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-allowDrop' class='name expandable'>allowDrop</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Set to false to deny dropping on this node. ...</div><div class='long'><p>Set to false to deny dropping on this node.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-checked' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-checked' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-checked' class='name expandable'>checked</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Set to true or false to show a checkbox alongside this node. ...</div><div class='long'><p>Set to true or false to show a checkbox alongside this node.</p>\n<p>Defaults to: <code>null</code></p></div></div></div><div id='cfg-children' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-children' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-children' class='name not-expandable'>children</a><span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a>[]</span></div><div class='description'><div class='short'><p>Array of child nodes.</p>\n</div><div class='long'><p>Array of child nodes.</p>\n</div></div></div><div id='cfg-cls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-cls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-cls' class='name not-expandable'>cls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'><p>CSS class to apply for this node.</p>\n</div><div class='long'><p>CSS class to apply for this node.</p>\n</div></div></div><div id='cfg-depth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-depth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-depth' class='name expandable'>depth</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The number of parents this node has. ...</div><div class='long'><p>The number of parents this node has. A root node has depth 0, a child of it depth 1, and so on...</p>\n</div></div></div><div id='cfg-expandable' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-expandable' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-expandable' class='name expandable'>expandable</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Set to true to allow for expanding/collapsing of this node. ...</div><div class='long'><p>Set to true to allow for expanding/collapsing of this node.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-expanded' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-expanded' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-expanded' class='name expandable'>expanded</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True if the node is expanded. ...</div><div class='long'><p>True if the node is expanded.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-href' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-href' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-href' class='name not-expandable'>href</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'><p>An URL for a link that's created when this config is specified.</p>\n</div><div class='long'><p>An URL for a link that's created when this config is specified.</p>\n</div></div></div><div id='cfg-hrefTarget' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-hrefTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-hrefTarget' class='name expandable'>hrefTarget</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Target for link. ...</div><div class='long'><p>Target for link. Only applicable when <a href=\"#!/api/Ext.data.NodeInterface-cfg-href\" rel=\"Ext.data.NodeInterface-cfg-href\" class=\"docClass\">href</a> also specified.</p>\n</div></div></div><div id='cfg-icon' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-icon' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-icon' class='name not-expandable'>icon</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'><p>URL for this node's icon.</p>\n</div><div class='long'><p>URL for this node's icon.</p>\n</div></div></div><div id='cfg-iconCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-iconCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-iconCls' class='name not-expandable'>iconCls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'><p>CSS class to apply for this node's icon.</p>\n</div><div class='long'><p>CSS class to apply for this node's icon.</p>\n</div></div></div><div id='cfg-index' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-index' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-index' class='name expandable'>index</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The position of the node inside its parent. ...</div><div class='long'><p>The position of the node inside its parent. When parent has 4 children and the node is third amongst them,\nindex will be 2.</p>\n</div></div></div><div id='cfg-isFirst' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-isFirst' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-isFirst' class='name not-expandable'>isFirst</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if this is the first node.</p>\n</div><div class='long'><p>True if this is the first node.</p>\n</div></div></div><div id='cfg-isLast' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-isLast' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-isLast' class='name not-expandable'>isLast</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if this is the last node.</p>\n</div><div class='long'><p>True if this is the last node.</p>\n</div></div></div><div id='cfg-leaf' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-leaf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-leaf' class='name expandable'>leaf</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Set to true to indicate that this child can have no children. ...</div><div class='long'><p>Set to true to indicate that this child can have no children. The expand icon/arrow will then not be\nrendered for this node.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-loaded' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-loaded' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-loaded' class='name expandable'>loaded</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True if the node has finished loading. ...</div><div class='long'><p>True if the node has finished loading.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-loading' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-loading' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-loading' class='name expandable'>loading</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True if the node is currently loading. ...</div><div class='long'><p>True if the node is currently loading.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-parentId' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-parentId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-parentId' class='name not-expandable'>parentId</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'><p>ID of parent node.</p>\n</div><div class='long'><p>ID of parent node.</p>\n</div></div></div><div id='cfg-qshowDelay' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-qshowDelay' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-qshowDelay' class='name not-expandable'>qshowDelay</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'><p>Tooltip showDelay.</p>\n</div><div class='long'><p>Tooltip showDelay.</p>\n</div></div></div><div id='cfg-qtip' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-qtip' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-qtip' class='name not-expandable'>qtip</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'><p>Tooltip text to show on this node.</p>\n</div><div class='long'><p>Tooltip text to show on this node.</p>\n</div></div></div><div id='cfg-qtitle' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-qtitle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-qtitle' class='name not-expandable'>qtitle</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'><p>Tooltip title.</p>\n</div><div class='long'><p>Tooltip title.</p>\n</div></div></div><div id='cfg-root' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-root' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-root' class='name not-expandable'>root</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'><p>True if this is the root node.</p>\n</div><div class='long'><p>True if this is the root node.</p>\n</div></div></div><div id='cfg-text' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-cfg-text' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-cfg-text' class='name not-expandable'>text</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'><p>The text to show on node label.</p>\n</div><div class='long'><p>The text to show on node label.</p>\n</div></div></div></div></div><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-childNodes' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-property-childNodes' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-property-childNodes' class='name expandable'>childNodes</a><span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a>[]</span></div><div class='description'><div class='short'>An array of this nodes children. ...</div><div class='long'><p>An array of this nodes children.  Array will be empty if this node has no chidren.</p>\n</div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-firstChild' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-property-firstChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-property-firstChild' class='name expandable'>firstChild</a><span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></span></div><div class='description'><div class='short'>A reference to this node's first child node. ...</div><div class='long'><p>A reference to this node's first child node. <code>null</code> if this node has no children.</p>\n</div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isNode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-property-isNode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-property-isNode' class='name expandable'>isNode</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true in this class to identify an object as an instantiated Node, or subclass thereof. ...</div><div class='long'><p><code>true</code> in this class to identify an object as an instantiated Node, or subclass thereof.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-lastChild' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-property-lastChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-property-lastChild' class='name expandable'>lastChild</a><span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></span></div><div class='description'><div class='short'>A reference to this node's last child node. ...</div><div class='long'><p>A reference to this node's last child node. <code>null</code> if this node has no children.</p>\n</div></div></div><div id='property-nextSibling' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-property-nextSibling' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-property-nextSibling' class='name expandable'>nextSibling</a><span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></span></div><div class='description'><div class='short'>A reference to this node's next sibling node. ...</div><div class='long'><p>A reference to this node's next sibling node. <code>null</code> if this node does not have a next sibling.</p>\n</div></div></div><div id='property-parentNode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-property-parentNode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-property-parentNode' class='name expandable'>parentNode</a><span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></span></div><div class='description'><div class='short'>A reference to this node's parent node. ...</div><div class='long'><p>A reference to this node's parent node. <code>null</code> if this node is the root node.</p>\n</div></div></div><div id='property-previousSibling' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-property-previousSibling' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-property-previousSibling' class='name expandable'>previousSibling</a><span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></span></div><div class='description'><div class='short'>A reference to this node's previous sibling node. ...</div><div class='long'><p>A reference to this node's previous sibling node. <code>null</code> if this node does not have a previous sibling.</p>\n</div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-appendChild' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-appendChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-appendChild' class='name expandable'>appendChild</a>( <span class='pre'>node, [suppressEvents], [commit]</span> ) : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></div><div class='description'><div class='short'>Inserts node(s) as the last child node of this node. ...</div><div class='long'><p>Inserts node(s) as the last child node of this node.</p>\n\n<p>If the node was previously a child node of another parent node, it will be removed from that node first.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>node</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a>/<a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a>[]/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The node or Array of nodes to append</p>\n</div></li><li><span class='pre'>suppressEvents</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to suppress firering of events.</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>commit</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></span><div class='sub-desc'><p>The appended node if single append, or null if an array was passed</p>\n</div></li></ul></div></div></div><div id='method-bubble' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-bubble' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-bubble' class='name expandable'>bubble</a>( <span class='pre'>fn, [scope], [args]</span> )</div><div class='description'><div class='short'>Bubbles up the tree from this node, calling the specified function with each node. ...</div><div class='long'><p>Bubbles up the tree from this node, calling the specified function with each node. The arguments to the function\nwill be the args provided or the current node. If the function returns false at any point,\nthe bubble is stopped.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The function to call</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (this reference) in which the function is executed. Defaults to the current Node.</p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> (optional)<div class='sub-desc'><p>The args to call the function with. Defaults to passing the current Node.</p>\n</div></li></ul></div></div></div><div id='method-callOverridden' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-cascadeBy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-cascadeBy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-cascadeBy' class='name expandable'>cascadeBy</a>( <span class='pre'>fn, [scope], [args]</span> )</div><div class='description'><div class='short'>Cascades down the tree from this node, calling the specified function with each node. ...</div><div class='long'><p>Cascades down the tree from this node, calling the specified function with each node. The arguments to the function\nwill be the args provided or the current node. If the function returns false at any point,\nthe cascade is stopped on that branch.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The function to call</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (this reference) in which the function is executed. Defaults to the current Node.</p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> (optional)<div class='sub-desc'><p>The args to call the function with. Defaults to passing the current Node.</p>\n</div></li></ul></div></div></div><div id='method-clear' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-clear' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-clear' class='name expandable'>clear</a>( <span class='pre'>[destroy]</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Clears the node. ...</div><div class='long'><p>Clears the node.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>destroy</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to destroy the node.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul></div></div></div><div id='method-collapse' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-collapse' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-collapse' class='name expandable'>collapse</a>( <span class='pre'>[recursive], [callback], [scope]</span> )</div><div class='description'><div class='short'>Collapse this node. ...</div><div class='long'><p>Collapse this node.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>recursive</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to recursively collapse all the children</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The function to execute once the collapse completes</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope to run the callback in</p>\n</div></li></ul></div></div></div><div id='method-collapseChildren' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-collapseChildren' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-collapseChildren' class='name expandable'>collapseChildren</a>( <span class='pre'>[recursive], [callback], [scope]</span> )</div><div class='description'><div class='short'>Collapse all the children of this node. ...</div><div class='long'><p>Collapse all the children of this node.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>recursive</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>True to recursively collapse all the children</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The function to execute once all the children are collapsed</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope to run the callback in</p>\n</div></li></ul></div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-contains' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-contains' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-contains' class='name expandable'>contains</a>( <span class='pre'>node</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if this node is an ancestor (at any point) of the passed node. ...</div><div class='long'><p>Returns true if this node is an ancestor (at any point) of the passed node.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>node</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-copy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-copy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-copy' class='name expandable'>copy</a>( <span class='pre'>[id], [deep]</span> ) : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></div><div class='description'><div class='short'>Creates a copy (clone) of this Node. ...</div><div class='long'><p>Creates a copy (clone) of this Node.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>A new id, defaults to this Node's id.</p>\n</div></li><li><span class='pre'>deep</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to recursively copy all child Nodes into the new Node.\nFalse to copy without child Nodes.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></span><div class='sub-desc'><p>A copy of this Node.</p>\n</div></li></ul></div></div></div><div id='method-createNode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-createNode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-createNode' class='name expandable'>createNode</a>( <span class='pre'>node</span> ) : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></div><div class='description'><div class='short'>Ensures that the passed object is an instance of a Record with the NodeInterface applied ...</div><div class='long'><p>Ensures that the passed object is an instance of a Record with the NodeInterface applied</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>node</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-destroy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-destroy' class='name expandable'>destroy</a>( <span class='pre'>silent</span> )</div><div class='description'><div class='short'>Destroys the node. ...</div><div class='long'><p>Destroys the node.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>silent</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.Base-method-destroy' rel='Ext.Base-method-destroy' class='docClass'>Ext.Base.destroy</a></p></div></div></div><div id='method-eachChild' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-eachChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-eachChild' class='name expandable'>eachChild</a>( <span class='pre'>fn, [scope], [args]</span> )</div><div class='description'><div class='short'>Interates the child nodes of this node, calling the specified function with each node. ...</div><div class='long'><p>Interates the child nodes of this node, calling the specified function with each node. The arguments to the function\nwill be the args provided or the current node. If the function returns false at any point,\nthe iteration stops.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The function to call</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (this reference) in which the function is executed. Defaults to the current Node in iteration.</p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> (optional)<div class='sub-desc'><p>The args to call the function with. Defaults to passing the current Node.</p>\n</div></li></ul></div></div></div><div id='method-expand' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-expand' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-expand' class='name expandable'>expand</a>( <span class='pre'>[recursive], [callback], [scope]</span> )</div><div class='description'><div class='short'>Expand this node. ...</div><div class='long'><p>Expand this node.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>recursive</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to recursively expand all the children</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The function to execute once the expand completes</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope to run the callback in</p>\n</div></li></ul></div></div></div><div id='method-expandChildren' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-expandChildren' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-expandChildren' class='name expandable'>expandChildren</a>( <span class='pre'>[recursive], [callback], [scope]</span> )</div><div class='description'><div class='short'>Expand all the children of this node. ...</div><div class='long'><p>Expand all the children of this node.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>recursive</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to recursively expand all the children</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The function to execute once all the children are expanded</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope to run the callback in</p>\n</div></li></ul></div></div></div><div id='method-findChild' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-findChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-findChild' class='name expandable'>findChild</a>( <span class='pre'>attribute, value, [deep]</span> ) : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></div><div class='description'><div class='short'>Finds the first child that has the attribute with the specified value. ...</div><div class='long'><p>Finds the first child that has the attribute with the specified value.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>attribute</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The attribute name</p>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to search for</p>\n</div></li><li><span class='pre'>deep</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to search through nodes deeper than the immediate children</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></span><div class='sub-desc'><p>The found child or null if none was found</p>\n</div></li></ul></div></div></div><div id='method-findChildBy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-findChildBy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-findChildBy' class='name expandable'>findChildBy</a>( <span class='pre'>fn, [scope], [deep]</span> ) : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></div><div class='description'><div class='short'>Finds the first child by a custom function. ...</div><div class='long'><p>Finds the first child by a custom function. The child matches if the function passed returns true.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>A function which must return true if the passed Node is the required Node.</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (this reference) in which the function is executed. Defaults to the Node being tested.</p>\n</div></li><li><span class='pre'>deep</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to search through nodes deeper than the immediate children</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></span><div class='sub-desc'><p>The found child or null if none was found</p>\n</div></li></ul></div></div></div><div id='method-getChildAt' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-getChildAt' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-getChildAt' class='name expandable'>getChildAt</a>( <span class='pre'>index</span> ) : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></div><div class='description'><div class='short'>Returns the child node at the specified index. ...</div><div class='long'><p>Returns the child node at the specified index.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getDepth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-getDepth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-getDepth' class='name expandable'>getDepth</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns depth of this node (the root node has a depth of 0) ...</div><div class='long'><p>Returns depth of this node (the root node has a depth of 0)</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-getOwnerTree' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-getOwnerTree' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-getOwnerTree' class='name expandable'>getOwnerTree</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.tree.Panel\" rel=\"Ext.tree.Panel\" class=\"docClass\">Ext.tree.Panel</a></div><div class='description'><div class='short'>Returns the tree this node is in. ...</div><div class='long'><p>Returns the tree this node is in.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.tree.Panel\" rel=\"Ext.tree.Panel\" class=\"docClass\">Ext.tree.Panel</a></span><div class='sub-desc'><p>The tree panel which owns this node.</p>\n</div></li></ul></div></div></div><div id='method-getPath' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-getPath' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-getPath' class='name expandable'>getPath</a>( <span class='pre'>[field], [separator]</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Gets the hierarchical path from the root of the current node. ...</div><div class='long'><p>Gets the hierarchical path from the root of the current node.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>field</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The field to construct the path from. Defaults to the model idProperty.</p>\n</div></li><li><span class='pre'>separator</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>A separator to use.</p>\n<p>Defaults to: <code>&quot;/&quot;</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The node path</p>\n</div></li></ul></div></div></div><div id='method-hasChildNodes' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-hasChildNodes' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-hasChildNodes' class='name expandable'>hasChildNodes</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if this node has one or more child nodes, else false. ...</div><div class='long'><p>Returns true if this node has one or more child nodes, else false.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-indexOf' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-indexOf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-indexOf' class='name expandable'>indexOf</a>( <span class='pre'>node</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the index of a child node ...</div><div class='long'><p>Returns the index of a child node</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>node</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The index of the node or -1 if it was not found</p>\n</div></li></ul></div></div></div><div id='method-indexOfId' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-indexOfId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-indexOfId' class='name expandable'>indexOfId</a>( <span class='pre'>id</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the index of a child node that matches the id ...</div><div class='long'><p>Returns the index of a child node that matches the id</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The id of the node to find</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The index of the node or -1 if it was not found</p>\n</div></li></ul></div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-insertBefore' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-insertBefore' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-insertBefore' class='name expandable'>insertBefore</a>( <span class='pre'>node, refNode</span> ) : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></div><div class='description'><div class='short'>Inserts the first node before the second node in this nodes childNodes collection. ...</div><div class='long'><p>Inserts the first node before the second node in this nodes childNodes collection.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>node</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The node to insert</p>\n</div></li><li><span class='pre'>refNode</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The node to insert before (if null the node is appended)</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></span><div class='sub-desc'><p>The inserted node</p>\n</div></li></ul></div></div></div><div id='method-insertChild' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-insertChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-insertChild' class='name expandable'>insertChild</a>( <span class='pre'>index, node</span> ) : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></div><div class='description'><div class='short'>Inserts a node into this node. ...</div><div class='long'><p>Inserts a node into this node.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The zero-based index to insert the node at</p>\n</div></li><li><span class='pre'>node</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The node to insert</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></span><div class='sub-desc'><p>The node you just inserted</p>\n</div></li></ul></div></div></div><div id='method-isAncestor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-isAncestor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-isAncestor' class='name expandable'>isAncestor</a>( <span class='pre'>node</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the passed node is an ancestor (at any point) of this node. ...</div><div class='long'><p>Returns true if the passed node is an ancestor (at any point) of this node.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>node</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isExpandable' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-isExpandable' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-isExpandable' class='name expandable'>isExpandable</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if this node has one or more child nodes, or if the expandable\nnode attribute is explicitly specified as...</div><div class='long'><p>Returns true if this node has one or more child nodes, or if the <tt>expandable</tt>\nnode attribute is explicitly specified as true, otherwise returns false.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isExpanded' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-isExpanded' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-isExpanded' class='name expandable'>isExpanded</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if this node is expaned ...</div><div class='long'><p>Returns true if this node is expaned</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isFirst' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-isFirst' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-isFirst' class='name expandable'>isFirst</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if this node is the first child of its parent ...</div><div class='long'><p>Returns true if this node is the first child of its parent</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isLast' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-isLast' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-isLast' class='name expandable'>isLast</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if this node is the last child of its parent ...</div><div class='long'><p>Returns true if this node is the last child of its parent</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isLeaf' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-isLeaf' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-isLeaf' class='name expandable'>isLeaf</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if this node is a leaf ...</div><div class='long'><p>Returns true if this node is a leaf</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isLoaded' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-isLoaded' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-isLoaded' class='name expandable'>isLoaded</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if this node is loaded ...</div><div class='long'><p>Returns true if this node is loaded</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isLoading' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-isLoading' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-isLoading' class='name expandable'>isLoading</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if this node is loading ...</div><div class='long'><p>Returns true if this node is loading</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isRoot' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-isRoot' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-isRoot' class='name expandable'>isRoot</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if this node is the root node ...</div><div class='long'><p>Returns true if this node is the root node</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isVisible' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-isVisible' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-isVisible' class='name expandable'>isVisible</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if this node is visible. ...</div><div class='long'><p>Returns true if this node is visible. Note that visibility refers to\nthe structure of the tree, the <a href=\"#!/api/Ext.tree.Panel-cfg-rootVisible\" rel=\"Ext.tree.Panel-cfg-rootVisible\" class=\"docClass\">Ext.tree.Panel.rootVisible</a>\nconfiguration is not taken into account here. If this method is called\non the root node, it will always be visible.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onChildNodesAvailable' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-onChildNodesAvailable' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-onChildNodesAvailable' class='name expandable'>onChildNodesAvailable</a>( <span class='pre'>records, recursive, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Called as a callback from the beforeexpand listener fired by expand when the child nodes have been loaded and appended. ...</div><div class='long'><p>Called as a callback from the beforeexpand listener fired by <a href=\"#!/api/Ext.data.NodeInterface-method-expand\" rel=\"Ext.data.NodeInterface-method-expand\" class=\"docClass\">expand</a> when the child nodes have been loaded and appended.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>records</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>recursive</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-remove' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-remove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-remove' class='name expandable'>remove</a>( <span class='pre'>[destroy]</span> ) : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Removes this node from its parent ...</div><div class='long'><p>Removes this node from its parent</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>destroy</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to destroy the node upon removal.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-removeAll' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-removeAll' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-removeAll' class='name expandable'>removeAll</a>( <span class='pre'>[destroy]</span> ) : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Removes all child nodes from this node. ...</div><div class='long'><p>Removes all child nodes from this node.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>destroy</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to destroy the node upon removal.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-removeChild' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-removeChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-removeChild' class='name expandable'>removeChild</a>( <span class='pre'>node, [destroy]</span> ) : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></div><div class='description'><div class='short'>Removes a child node from this node. ...</div><div class='long'><p>Removes a child node from this node.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>node</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The node to remove</p>\n</div></li><li><span class='pre'>destroy</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to destroy the node upon removal.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></span><div class='sub-desc'><p>The removed node</p>\n</div></li></ul></div></div></div><div id='method-replaceChild' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-replaceChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-replaceChild' class='name expandable'>replaceChild</a>( <span class='pre'>newChild, oldChild</span> ) : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></div><div class='description'><div class='short'>Replaces one child node in this node with another. ...</div><div class='long'><p>Replaces one child node in this node with another.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>newChild</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The replacement node</p>\n</div></li><li><span class='pre'>oldChild</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The node to replace</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a></span><div class='sub-desc'><p>The replaced node</p>\n</div></li></ul></div></div></div><div id='method-serialize' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-serialize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-serialize' class='name expandable'>serialize</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Creates an object representation of this node including its children. ...</div><div class='long'><p>Creates an object representation of this node including its children.</p>\n</div></div></div><div id='method-setCollapsed' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-setCollapsed' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-setCollapsed' class='name expandable'>setCollapsed</a>( <span class='pre'>recursive</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Sets the node into the collapsed state without affecting the UI. ...</div><div class='long'><p>Sets the node into the collapsed state without affecting the UI.</p>\n\n<p>This is called when a node is collapsed with the recursive flag. All the descendant\nnodes will have been removed from the store, but descendant non-leaf nodes still\nneed to be set to the collapsed state without affecting the UI.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>recursive</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setFirstChild' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-setFirstChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-setFirstChild' class='name expandable'>setFirstChild</a>( <span class='pre'>node</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Sets the first child of this node ...</div><div class='long'><p>Sets the first child of this node</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>node</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setLastChild' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-setLastChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-setLastChild' class='name expandable'>setLastChild</a>( <span class='pre'>node</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Sets the last child of this node ...</div><div class='long'><p>Sets the last child of this node</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>node</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-sort' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-sort' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-sort' class='name expandable'>sort</a>( <span class='pre'>fn, [recursive], [suppressEvent]</span> )</div><div class='description'><div class='short'>Sorts this nodes children using the supplied sort function. ...</div><div class='long'><p>Sorts this nodes children using the supplied sort function.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>A function which, when passed two Nodes, returns -1, 0 or 1 depending upon required sort order.</p>\n</div></li><li><span class='pre'>recursive</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to apply this sort recursively</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>suppressEvent</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to not fire a sort event.</p>\n<p>Defaults to: <code>false</code></p></div></li></ul></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-updateInfo' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-updateInfo' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-updateInfo' class='name expandable'>updateInfo</a>( <span class='pre'>commit, info</span> )</div><div class='description'><div class='short'>Updates general data of this node like isFirst, isLast, depth. ...</div><div class='long'><p>Updates general data of this node like isFirst, isLast, depth. This\nmethod is internally called after a node is moved. This shouldn't\nhave to be called by the developer unless they are creating custom\nTree plugins.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>commit</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'>\n</div></li><li><span class='pre'>info</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The info to update. May contain any of the following</p>\n<ul><li><span class='pre'>isFirst</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'></div></li><li><span class='pre'>isLast</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'></div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'></div></li><li><span class='pre'>depth</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'></div></li><li><span class='pre'>parentId</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'></div></li></ul></div></li></ul></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-applyFields' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-applyFields' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-applyFields' class='name expandable'>applyFields</a>( <span class='pre'>modelClass, addFields</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>modelClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>addFields</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-create' class='name expandable'>create</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create a new instance of this Class. ...</div><div class='long'><p>Create a new instance of this Class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.create({\n    someConfig: true\n});\n</code></pre>\n\n<p>All parameters are passed to the constructor of the class.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the created instance.</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-decorate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-static-method-decorate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-static-method-decorate' class='name expandable'>decorate</a>( <span class='pre'>modelClass</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>This method allows you to decorate a Model's class to implement the NodeInterface. ...</div><div class='long'><p>This method allows you to decorate a Model's class to implement the NodeInterface.\nThis adds a set of methods, new events, new properties and new fields on every Record.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>modelClass</span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a>/<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The Model class or an instance of the Model class you want to\ndecorate the prototype of.</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='method-getPrototypeBody' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-method-getPrototypeBody' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-method-getPrototypeBody' class='name expandable'>getPrototypeBody</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-event'>Events</h3><div class='subsection'><div id='event-append' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-event-append' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-event-append' class='name expandable'>append</a>( <span class='pre'>this, node, index, eOpts</span> )</div><div class='description'><div class='short'>Fires when a new child node is appended ...</div><div class='long'><p>Fires when a new child node is appended</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>This node</p>\n</div></li><li><span class='pre'>node</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The newly appended node</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The index of the newly appended node</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-beforeappend' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-event-beforeappend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-event-beforeappend' class='name expandable'>beforeappend</a>( <span class='pre'>this, node, eOpts</span> )</div><div class='description'><div class='short'>Fires before a new child is appended, return false to cancel the append. ...</div><div class='long'><p>Fires before a new child is appended, return false to cancel the append.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>This node</p>\n</div></li><li><span class='pre'>node</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The child node to be appended</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-beforecollapse' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-event-beforecollapse' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-event-beforecollapse' class='name expandable'>beforecollapse</a>( <span class='pre'>this, eOpts</span> )</div><div class='description'><div class='short'>Fires before this node is collapsed. ...</div><div class='long'><p>Fires before this node is collapsed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The collapsing node</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-beforeexpand' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-event-beforeexpand' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-event-beforeexpand' class='name expandable'>beforeexpand</a>( <span class='pre'>this, eOpts</span> )</div><div class='description'><div class='short'>Fires before this node is expanded. ...</div><div class='long'><p>Fires before this node is expanded.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The expanding node</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-beforeinsert' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-event-beforeinsert' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-event-beforeinsert' class='name expandable'>beforeinsert</a>( <span class='pre'>this, node, refNode, eOpts</span> )</div><div class='description'><div class='short'>Fires before a new child is inserted, return false to cancel the insert. ...</div><div class='long'><p>Fires before a new child is inserted, return false to cancel the insert.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>This node</p>\n</div></li><li><span class='pre'>node</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The child node to be inserted</p>\n</div></li><li><span class='pre'>refNode</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The child node the node is being inserted before</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-beforemove' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-event-beforemove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-event-beforemove' class='name expandable'>beforemove</a>( <span class='pre'>this, oldParent, newParent, index, eOpts</span> )</div><div class='description'><div class='short'>Fires before this node is moved to a new location in the tree. ...</div><div class='long'><p>Fires before this node is moved to a new location in the tree. Return false to cancel the move.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>This node</p>\n</div></li><li><span class='pre'>oldParent</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The parent of this node</p>\n</div></li><li><span class='pre'>newParent</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The new parent this node is moving to</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The index it is being moved to</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-beforeremove' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-event-beforeremove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-event-beforeremove' class='name expandable'>beforeremove</a>( <span class='pre'>this, node, isMove, eOpts</span> )</div><div class='description'><div class='short'>Fires before a child is removed, return false to cancel the remove. ...</div><div class='long'><p>Fires before a child is removed, return false to cancel the remove.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>This node</p>\n</div></li><li><span class='pre'>node</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The child node to be removed</p>\n</div></li><li><span class='pre'>isMove</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p><code>true</code> if the child node is being removed so it can be moved to another position in the tree.\n(a side effect of calling <a href=\"#!/api/Ext.data.NodeInterface-method-appendChild\" rel=\"Ext.data.NodeInterface-method-appendChild\" class=\"docClass\">appendChild</a> or\n<a href=\"#!/api/Ext.data.NodeInterface-method-insertBefore\" rel=\"Ext.data.NodeInterface-method-insertBefore\" class=\"docClass\">insertBefore</a> with a node that already has a parentNode)</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-collapse' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-event-collapse' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-event-collapse' class='name expandable'>collapse</a>( <span class='pre'>this, eOpts</span> )</div><div class='description'><div class='short'>Fires when this node is collapsed. ...</div><div class='long'><p>Fires when this node is collapsed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The collapsing node</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-expand' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-event-expand' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-event-expand' class='name expandable'>expand</a>( <span class='pre'>this, eOpts</span> )</div><div class='description'><div class='short'>Fires when this node is expanded. ...</div><div class='long'><p>Fires when this node is expanded.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The expanding node</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-insert' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-event-insert' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-event-insert' class='name expandable'>insert</a>( <span class='pre'>this, node, refNode, eOpts</span> )</div><div class='description'><div class='short'>Fires when a new child node is inserted. ...</div><div class='long'><p>Fires when a new child node is inserted.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>This node</p>\n</div></li><li><span class='pre'>node</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The child node inserted</p>\n</div></li><li><span class='pre'>refNode</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The child node the node was inserted before</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-move' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-event-move' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-event-move' class='name expandable'>move</a>( <span class='pre'>this, oldParent, newParent, index, eOpts</span> )</div><div class='description'><div class='short'>Fires when this node is moved to a new location in the tree ...</div><div class='long'><p>Fires when this node is moved to a new location in the tree</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>This node</p>\n</div></li><li><span class='pre'>oldParent</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The old parent of this node</p>\n</div></li><li><span class='pre'>newParent</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The new parent of this node</p>\n</div></li><li><span class='pre'>index</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The index it was moved to</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-remove' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-event-remove' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-event-remove' class='name expandable'>remove</a>( <span class='pre'>this, node, isMove, eOpts</span> )</div><div class='description'><div class='short'>Fires when a child node is removed ...</div><div class='long'><p>Fires when a child node is removed</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>This node</p>\n</div></li><li><span class='pre'>node</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>The removed node</p>\n</div></li><li><span class='pre'>isMove</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p><code>true</code> if the child node is being removed so it can be moved to another position in the tree.\n(a side effect of calling <a href=\"#!/api/Ext.data.NodeInterface-method-appendChild\" rel=\"Ext.data.NodeInterface-method-appendChild\" class=\"docClass\">appendChild</a> or\n<a href=\"#!/api/Ext.data.NodeInterface-method-insertBefore\" rel=\"Ext.data.NodeInterface-method-insertBefore\" class=\"docClass\">insertBefore</a> with a node that already has a parentNode)</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div><div id='event-sort' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.NodeInterface'>Ext.data.NodeInterface</span><br/><a href='source/NodeInterface.html#Ext-data-NodeInterface-event-sort' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.NodeInterface-event-sort' class='name expandable'>sort</a>( <span class='pre'>this, childNodes, eOpts</span> )</div><div class='description'><div class='short'>Fires when this node's childNodes are sorted. ...</div><div class='long'><p>Fires when this node's childNodes are sorted.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a><div class='sub-desc'><p>This node.</p>\n</div></li><li><span class='pre'>childNodes</span> : <a href=\"#!/api/Ext.data.NodeInterface\" rel=\"Ext.data.NodeInterface\" class=\"docClass\">Ext.data.NodeInterface</a>[]<div class='sub-desc'><p>The childNodes of this node.</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div></div></div></div></div>","superclasses":["Ext.Base"],"meta":{},"code_type":"ext_define","requires":["Ext.data.Field","Ext.data.writer.Json"],"html_meta":{},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{"static":true,"private":true},"name":"applyFields","id":"method-applyFields"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{"static":true},"name":"decorate","id":"static-method-decorate"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{"static":true,"private":true},"name":"getPrototypeBody","id":"method-getPrototypeBody"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"NodeInterface.html#Ext-data-NodeInterface","filename":"NodeInterface.js"}],"linenr":1,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.data.NodeInterface","meta":{},"name":"childNodes","id":"property-childNodes"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.data.NodeInterface","meta":{},"name":"firstChild","id":"property-firstChild"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.data.NodeInterface","meta":{},"name":"isNode","id":"property-isNode"},{"tagname":"property","owner":"Ext.data.NodeInterface","meta":{},"name":"lastChild","id":"property-lastChild"},{"tagname":"property","owner":"Ext.data.NodeInterface","meta":{},"name":"nextSibling","id":"property-nextSibling"},{"tagname":"property","owner":"Ext.data.NodeInterface","meta":{},"name":"parentNode","id":"property-parentNode"},{"tagname":"property","owner":"Ext.data.NodeInterface","meta":{},"name":"previousSibling","id":"property-previousSibling"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"}],"cfg":[{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"allowDrag","id":"cfg-allowDrag"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"allowDrop","id":"cfg-allowDrop"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"checked","id":"cfg-checked"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"children","id":"cfg-children"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"cls","id":"cfg-cls"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"depth","id":"cfg-depth"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"expandable","id":"cfg-expandable"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"expanded","id":"cfg-expanded"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"href","id":"cfg-href"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"hrefTarget","id":"cfg-hrefTarget"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"icon","id":"cfg-icon"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"iconCls","id":"cfg-iconCls"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"index","id":"cfg-index"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"isFirst","id":"cfg-isFirst"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"isLast","id":"cfg-isLast"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"leaf","id":"cfg-leaf"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"loaded","id":"cfg-loaded"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"loading","id":"cfg-loading"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"parentId","id":"cfg-parentId"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"qshowDelay","id":"cfg-qshowDelay"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"qtip","id":"cfg-qtip"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"qtitle","id":"cfg-qtitle"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"root","id":"cfg-root"},{"tagname":"cfg","owner":"Ext.data.NodeInterface","meta":{},"name":"text","id":"cfg-text"}],"css_var":[],"method":[{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"appendChild","id":"method-appendChild"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"bubble","id":"method-bubble"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"cascadeBy","id":"method-cascadeBy"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{"private":true},"name":"clear","id":"method-clear"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"collapse","id":"method-collapse"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"collapseChildren","id":"method-collapseChildren"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"contains","id":"method-contains"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"copy","id":"method-copy"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"createNode","id":"method-createNode"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"eachChild","id":"method-eachChild"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"expand","id":"method-expand"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"expandChildren","id":"method-expandChildren"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"findChild","id":"method-findChild"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"findChildBy","id":"method-findChildBy"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"getChildAt","id":"method-getChildAt"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"getDepth","id":"method-getDepth"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"getOwnerTree","id":"method-getOwnerTree"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"getPath","id":"method-getPath"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"hasChildNodes","id":"method-hasChildNodes"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"indexOf","id":"method-indexOf"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"indexOfId","id":"method-indexOfId"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"insertBefore","id":"method-insertBefore"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"insertChild","id":"method-insertChild"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"isAncestor","id":"method-isAncestor"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"isExpandable","id":"method-isExpandable"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"isExpanded","id":"method-isExpanded"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"isFirst","id":"method-isFirst"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"isLast","id":"method-isLast"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"isLeaf","id":"method-isLeaf"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"isLoaded","id":"method-isLoaded"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"isLoading","id":"method-isLoading"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"isRoot","id":"method-isRoot"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"isVisible","id":"method-isVisible"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{"private":true},"name":"onChildNodesAvailable","id":"method-onChildNodesAvailable"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{"chainable":true},"name":"remove","id":"method-remove"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{"chainable":true},"name":"removeAll","id":"method-removeAll"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"removeChild","id":"method-removeChild"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"replaceChild","id":"method-replaceChild"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"serialize","id":"method-serialize"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{"private":true},"name":"setCollapsed","id":"method-setCollapsed"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{"private":true},"name":"setFirstChild","id":"method-setFirstChild"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{"private":true},"name":"setLastChild","id":"method-setLastChild"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"sort","id":"method-sort"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"},{"tagname":"method","owner":"Ext.data.NodeInterface","meta":{},"name":"updateInfo","id":"method-updateInfo"}],"event":[{"tagname":"event","owner":"Ext.data.NodeInterface","meta":{},"name":"append","id":"event-append"},{"tagname":"event","owner":"Ext.data.NodeInterface","meta":{},"name":"beforeappend","id":"event-beforeappend"},{"tagname":"event","owner":"Ext.data.NodeInterface","meta":{},"name":"beforecollapse","id":"event-beforecollapse"},{"tagname":"event","owner":"Ext.data.NodeInterface","meta":{},"name":"beforeexpand","id":"event-beforeexpand"},{"tagname":"event","owner":"Ext.data.NodeInterface","meta":{},"name":"beforeinsert","id":"event-beforeinsert"},{"tagname":"event","owner":"Ext.data.NodeInterface","meta":{},"name":"beforemove","id":"event-beforemove"},{"tagname":"event","owner":"Ext.data.NodeInterface","meta":{},"name":"beforeremove","id":"event-beforeremove"},{"tagname":"event","owner":"Ext.data.NodeInterface","meta":{},"name":"collapse","id":"event-collapse"},{"tagname":"event","owner":"Ext.data.NodeInterface","meta":{},"name":"expand","id":"event-expand"},{"tagname":"event","owner":"Ext.data.NodeInterface","meta":{},"name":"insert","id":"event-insert"},{"tagname":"event","owner":"Ext.data.NodeInterface","meta":{},"name":"move","id":"event-move"},{"tagname":"event","owner":"Ext.data.NodeInterface","meta":{},"name":"remove","id":"event-remove"},{"tagname":"event","owner":"Ext.data.NodeInterface","meta":{},"name":"sort","id":"event-sort"}],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.data.NodeInterface","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.data.NodeInterface","mixins":[],"mixedInto":[]});