Ext.data.JsonP.Ext_layout_ContextItem({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":"Ext.Base","uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><strong>Ext.layout.ContextItem</strong></div></div><h4>Requires</h4><div class='dependency'><a href='#!/api/Ext.layout.ClassList' rel='Ext.layout.ClassList' class='docClass'>Ext.layout.ClassList</a></div><h4>Files</h4><div class='dependency'><a href='source/ContextItem2.html#Ext-layout-ContextItem' target='_blank'>ContextItem.js</a></div><div class='dependency'><a href='source/ContextItem.html#Ext-diag-layout-ContextItem' target='_blank'>ContextItem.js</a></div></pre><div class='doc-contents'><p class='private'><strong>NOTE</strong> This is a private utility class for internal use by the framework. Don't rely on its existence.</p><p>This class manages state information for a component or element during a layout.</p>\n\n<h1>Blocks</h1>\n\n<p>A \"block\" is a required value that is preventing further calculation. When a layout has\nencountered a situation where it cannot possibly calculate results, it can associate\nitself with the context item and missing property so that it will not be rescheduled\nuntil that property is set.</p>\n\n<p>Blocks are a one-shot registration. Once the property changes, the block is removed.</p>\n\n<p>Be careful with blocks. If <em>any</em> further calculations can be made, a block is not the\nright choice.</p>\n\n<h1>Triggers</h1>\n\n<p>Whenever any call to <a href=\"#!/api/Ext.layout.ContextItem-method-getProp\" rel=\"Ext.layout.ContextItem-method-getProp\" class=\"docClass\">getProp</a>, <a href=\"#!/api/Ext.layout.ContextItem-method-getDomProp\" rel=\"Ext.layout.ContextItem-method-getDomProp\" class=\"docClass\">getDomProp</a>, <a href=\"#!/api/Ext.layout.ContextItem-method-hasProp\" rel=\"Ext.layout.ContextItem-method-hasProp\" class=\"docClass\">hasProp</a> or\n<a href=\"#!/api/Ext.layout.ContextItem-method-hasDomProp\" rel=\"Ext.layout.ContextItem-method-hasDomProp\" class=\"docClass\">hasDomProp</a> is made, the current layout is automatically registered as being\ndependent on that property in the appropriate state. Any changes to the property will\ntrigger the layout and it will be queued in the <a href=\"#!/api/Ext.layout.Context\" rel=\"Ext.layout.Context\" class=\"docClass\">Ext.layout.Context</a>.</p>\n\n<p>Triggers, once added, remain for the entire layout. Any changes to the property will\nreschedule all unfinished layouts in their trigger set.</p>\n</div><div class='members'><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-borderNames' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-borderNames' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-borderNames' class='name expandable'>borderNames</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>['border-top-width', 'border-right-width', 'border-bottom-width', 'border-left-width']</code></p></div></div></div><div id='property-boxChildren' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-boxChildren' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-boxChildren' class='name not-expandable'>boxChildren</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'><p>plaed here by AbstractComponent.getSizeModel</p>\n</div><div class='long'><p>plaed here by AbstractComponent.getSizeModel</p>\n</div></div></div><div id='property-boxParent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-boxParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-boxParent' class='name not-expandable'>boxParent</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-cacheMissHandlers' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-cacheMissHandlers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-cacheMissHandlers' class='name not-expandable'>cacheMissHandlers</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-children' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-children' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-children' class='name expandable'>children</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-consumersContainerHeight' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-consumersContainerHeight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-consumersContainerHeight' class='name expandable'>consumersContainerHeight</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-consumersContainerWidth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-consumersContainerWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-consumersContainerWidth' class='name expandable'>consumersContainerWidth</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-consumersContentHeight' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-consumersContentHeight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-consumersContentHeight' class='name expandable'>consumersContentHeight</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-consumersContentWidth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-consumersContentWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-consumersContentWidth' class='name expandable'>consumersContentWidth</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-consumersHeight' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-consumersHeight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-consumersHeight' class='name expandable'>consumersHeight</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-consumersWidth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-consumersWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-consumersWidth' class='name expandable'>consumersWidth</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-dirty' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-dirty' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-dirty' class='name not-expandable'>dirty</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-dirtyCount' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-dirtyCount' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-dirtyCount' class='name expandable'>dirtyCount</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>The number of dirty properties ...</div><div class='long'><p>The number of dirty properties</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-hasRawContent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-hasRawContent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-hasRawContent' class='name expandable'>hasRawContent</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-heightModel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-heightModel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-heightModel' class='name not-expandable'>heightModel</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-isBorderBoxValue' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-isBorderBoxValue' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-isBorderBoxValue' class='name not-expandable'>isBorderBoxValue</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-isContextItem' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-isContextItem' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-isContextItem' class='name expandable'>isContextItem</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isTopLevel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-isTopLevel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-isTopLevel' class='name expandable'>isTopLevel</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-marginNames' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-marginNames' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-marginNames' class='name expandable'>marginNames</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>['margin-top', 'margin-right', 'margin-bottom', 'margin-left']</code></p></div></div></div><div id='property-optOut' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-optOut' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-optOut' class='name expandable'>optOut</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong><strong class='readonly signature' >readonly</strong></div><div class='description'><div class='short'>There are several cases that allow us to skip (opt out) of laying out a component\nand its children as long as its las...</div><div class='long'><p>There are several cases that allow us to skip (opt out) of laying out a component\nand its children as long as its <code>lastBox</code> is not marked as <code>invalid</code>. If anything\nhappens to change things, the <code>lastBox</code> is marked as <code>invalid</code> by <code>updateLayout</code>\nas it ascends the component hierarchy.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-ownerCtContext' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-ownerCtContext' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-ownerCtContext' class='name not-expandable'>ownerCtContext</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-ownerSizePolicy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-ownerSizePolicy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-ownerSizePolicy' class='name not-expandable'>ownerSizePolicy</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-paddingNames' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-paddingNames' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-paddingNames' class='name expandable'>paddingNames</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>['padding-top', 'padding-right', 'padding-bottom', 'padding-left']</code></p></div></div></div><div id='property-props' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-props' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-props' class='name not-expandable'>props</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'><p>the current set of property values:</p>\n</div><div class='long'><p>the current set of property values:</p>\n</div></div></div><div id='property-remainingChildDimensions' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-remainingChildDimensions' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-remainingChildDimensions' class='name expandable'>remainingChildDimensions</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div><div id='property-sizeModel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-sizeModel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-sizeModel' class='name not-expandable'>sizeModel</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-state' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-state' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-state' class='name expandable'>state</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>State variables that are cleared when invalidated. ...</div><div class='long'><p>State variables that are cleared when invalidated. Only applies to component items.</p>\n</div></div></div><div id='property-styles' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-styles' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-styles' class='name expandable'>styles</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Adds x and y values from a props object to a styles object as \"left\" and \"top\" values. ...</div><div class='long'><p>Adds x and y values from a props object to a styles object as \"left\" and \"top\" values.\nOverridden to add the x property as \"right\" in rtl mode. A styles object for an Element A ContextItem props object</p>\n</div></div></div><div id='property-translateProps' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-translateProps' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-translateProps' class='name expandable'>translateProps</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{x: 'left', y: 'top'}</code></p></div></div></div><div id='property-trblNames' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-trblNames' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-trblNames' class='name expandable'>trblNames</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>['top', 'right', 'bottom', 'left']</code></p></div></div></div><div id='property-widthModel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-widthModel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-widthModel' class='name not-expandable'>widthModel</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-wrapsComponent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-property-wrapsComponent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-property-wrapsComponent' class='name expandable'>wrapsComponent</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='readonly signature' >readonly</strong></div><div class='description'><div class='short'>True if this item wraps a Component (rather than an Element). ...</div><div class='long'><p>True if this item wraps a Component (rather than an Element).</p>\n<p>Defaults to: <code>false</code></p></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/Ext.layout.ContextItem-method-constructor' class='name expandable'>Ext.layout.ContextItem</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-addBlock' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-addBlock' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-addBlock' class='name expandable'>addBlock</a>( <span class='pre'>name, layout, propName</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Adds a block. ...</div><div class='long'><p>Adds a block.</p>\n\n<p><strong>Overridden in Ext.diag.layout.ContextItem.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the block list ('blocks' or 'domBlocks').</p>\n</div></li><li><span class='pre'>layout</span> : <a href=\"#!/api/Ext.layout.Layout\" rel=\"Ext.layout.Layout\" class=\"docClass\">Ext.layout.Layout</a><div class='sub-desc'><p>The layout that is blocked.</p>\n</div></li><li><span class='pre'>propName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The property name that blocked the layout (e.g., 'width').</p>\n</div></li></ul></div></div></div><div id='method-addBoxChild' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-addBoxChild' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-addBoxChild' class='name expandable'>addBoxChild</a>( <span class='pre'>boxChildItem</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Overridden in Ext.diag.layout.ContextItem. ...</div><div class='long'><p><strong>Overridden in Ext.diag.layout.ContextItem.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>boxChildItem</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-addCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-addCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-addCls' class='name expandable'>addCls</a>( <span class='pre'>newCls</span> )</div><div class='description'><div class='short'>Queue the addition of a class name (or array of class names) to this ContextItem's target when next flushed. ...</div><div class='long'><p>Queue the addition of a class name (or array of class names) to this ContextItem's target when next flushed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>newCls</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-addTrigger' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-addTrigger' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-addTrigger' class='name expandable'>addTrigger</a>( <span class='pre'>propName, inDom</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Adds a trigger. ...</div><div class='long'><p>Adds a trigger.</p>\n\n<p><strong>Overridden in Ext.diag.layout.ContextItem.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>propName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The property name that triggers the layout (e.g., 'width').</p>\n</div></li><li><span class='pre'>inDom</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True if the trigger list is <code>domTriggers</code>, false if <code>triggers</code>.</p>\n</div></li></ul></div></div></div><div id='method-block' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-block' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-block' class='name expandable'>block</a>( <span class='pre'>layout, propName</span> )</div><div class='description'><div class='short'>Registers a layout in the block list for the given property. ...</div><div class='long'><p>Registers a layout in the block list for the given property. Once the property is\nset in the <a href=\"#!/api/Ext.layout.Context\" rel=\"Ext.layout.Context\" class=\"docClass\">Ext.layout.Context</a>, the layout is unblocked.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>layout</span> : <a href=\"#!/api/Ext.layout.Layout\" rel=\"Ext.layout.Layout\" class=\"docClass\">Ext.layout.Layout</a><div class='sub-desc'>\n</div></li><li><span class='pre'>propName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The property name that blocked the layout (e.g., 'width').</p>\n</div></li></ul></div></div></div><div id='method-boxChildMeasured' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-boxChildMeasured' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-boxChildMeasured' class='name expandable'>boxChildMeasured</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-callOverridden' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-checkAuthority' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem.html#Ext-layout-ContextItem-method-checkAuthority' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-checkAuthority' class='name expandable'>checkAuthority</a>( <span class='pre'>prop</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Defined in override Ext.diag.layout.ContextItem. ...</div><div class='long'><p><strong>Defined in override Ext.diag.layout.ContextItem.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>prop</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-checkCache' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-checkCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-checkCache' class='name expandable'>checkCache</a>( <span class='pre'>entry</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>entry</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-clearAllBlocks' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-clearAllBlocks' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-clearAllBlocks' class='name expandable'>clearAllBlocks</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-clearBlocks' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-clearBlocks' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-clearBlocks' class='name expandable'>clearBlocks</a>( <span class='pre'>name, propName</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Removes any blocks on a property in the specified set. ...</div><div class='long'><p>Removes any blocks on a property in the specified set. Any layouts that were blocked\nby this property and are not still blocked (by other properties) will be rescheduled.</p>\n\n<p><strong>Overridden in Ext.diag.layout.ContextItem.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the block list ('blocks' or 'domBlocks').</p>\n</div></li><li><span class='pre'>propName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The property name that blocked the layout (e.g., 'width').</p>\n</div></li></ul></div></div></div><div id='method-clearMarginCache' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-clearMarginCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-clearMarginCache' class='name expandable'>clearMarginCache</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>clears the margin cache so that marginInfo get re-read from the dom on the next call to getMarginInfo()\nThis is neede...</div><div class='long'><p>clears the margin cache so that marginInfo get re-read from the dom on the next call to getMarginInfo()\nThis is needed in some special cases where the margins have changed since the last layout, making the cached\nvalues invalid.  For example collapsed window headers have different margin than expanded ones.</p>\n</div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-destroy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-destroy' class='name expandable'>destroy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.util.ElementContainer-method-destroy' rel='Ext.util.ElementContainer-method-destroy' class='docClass'>Ext.util.ElementContainer.destroy</a>, <a href='#!/api/Ext.AbstractComponent-method-destroy' rel='Ext.AbstractComponent-method-destroy' class='docClass'>Ext.AbstractComponent.destroy</a>, <a href='#!/api/Ext.AbstractPlugin-method-destroy' rel='Ext.AbstractPlugin-method-destroy' class='docClass'>Ext.AbstractPlugin.destroy</a></p></div></div></div><div id='method-domBlock' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-domBlock' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-domBlock' class='name expandable'>domBlock</a>( <span class='pre'>layout, propName</span> )</div><div class='description'><div class='short'>Registers a layout in the DOM block list for the given property. ...</div><div class='long'><p>Registers a layout in the DOM block list for the given property. Once the property\nflushed to the DOM by the <a href=\"#!/api/Ext.layout.Context\" rel=\"Ext.layout.Context\" class=\"docClass\">Ext.layout.Context</a>, the layout is unblocked.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>layout</span> : <a href=\"#!/api/Ext.layout.Layout\" rel=\"Ext.layout.Layout\" class=\"docClass\">Ext.layout.Layout</a><div class='sub-desc'>\n</div></li><li><span class='pre'>propName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The property name that blocked the layout (e.g., 'width').</p>\n</div></li></ul></div></div></div><div id='method-fireTriggers' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-fireTriggers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-fireTriggers' class='name expandable'>fireTriggers</a>( <span class='pre'>name, propName</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Reschedules any layouts associated with a given trigger. ...</div><div class='long'><p>Reschedules any layouts associated with a given trigger.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the trigger list ('triggers' or 'domTriggers').</p>\n</div></li><li><span class='pre'>propName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The property name that triggers the layout (e.g., 'width').</p>\n</div></li></ul></div></div></div><div id='method-flush' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-flush' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-flush' class='name expandable'>flush</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Flushes any updates in the dirty collection to the DOM. ...</div><div class='long'><p>Flushes any updates in the dirty collection to the DOM. This is only called if there\nare dirty entries because this object is only added to the flushQueue of the\n<a href=\"#!/api/Ext.layout.Context\" rel=\"Ext.layout.Context\" class=\"docClass\">Ext.layout.Context</a> when entries become dirty.</p>\n</div></div></div><div id='method-flushAnimations' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-flushAnimations' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-flushAnimations' class='name expandable'>flushAnimations</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getBorderInfo' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-getBorderInfo' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-getBorderInfo' class='name expandable'>getBorderInfo</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Gets the border information for the element as an object with left, top, right and\nbottom properties holding border s...</div><div class='long'><p>Gets the border information for the element as an object with left, top, right and\nbottom properties holding border size in pixels. This object is only read from the\nDOM on first request and is cached.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getClassList' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-getClassList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-getClassList' class='name expandable'>getClassList</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Returns a ClassList-like object to buffer access to this item's element's classes. ...</div><div class='long'><p>Returns a ClassList-like object to buffer access to this item's element's classes.</p>\n</div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getDomProp' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-getDomProp' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-getDomProp' class='name expandable'>getDomProp</a>( <span class='pre'>propName</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Gets a property of this object if it is correct in the DOM. ...</div><div class='long'><p>Gets a property of this object if it is correct in the DOM. Also tracks the current\nlayout as dependent on this property so that DOM writes of it will trigger the\nlayout to be recalculated.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>propName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The property name (e.g., 'width').</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The property value or undefined if not yet set or is dirty.</p>\n</div></li></ul></div></div></div><div id='method-getEl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-getEl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-getEl' class='name expandable'>getEl</a>( <span class='pre'>nameOrEl, [owner]</span> ) : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a></div><div class='description'><div class='short'>Returns the context item for an owned element. ...</div><div class='long'><p>Returns the context item for an owned element. This should only be called on a\ncomponent's item. The list of child items is used to manage invalidating calculated\nresults.</p>\n\n<p><strong>Overridden in Ext.diag.layout.ContextItem.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>nameOrEl</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a><div class='sub-desc'><p>The element or the name of an owned element</p>\n</div></li><li><span class='pre'>owner</span> : <a href=\"#!/api/Ext.layout.container.Container\" rel=\"Ext.layout.container.Container\" class=\"docClass\">Ext.layout.container.Container</a>/<a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a> (optional)<div class='sub-desc'><p>The owner of the\nnamed element if the passed \"nameOrEl\" parameter is a String. Defaults to this\nContextItem's \"target\" property.  For more details on owned elements see\n<a href=\"#!/api/Ext.Component-cfg-childEls\" rel=\"Ext.Component-cfg-childEls\" class=\"docClass\">childEls</a> and\n<a href=\"#!/api/Ext.Component-cfg-renderSelectors\" rel=\"Ext.Component-cfg-renderSelectors\" class=\"docClass\">renderSelectors</a></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getFrameInfo' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-getFrameInfo' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-getFrameInfo' class='name expandable'>getFrameInfo</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Gets the \"frame\" information for the element as an object with left, top, right and\nbottom properties holding border+...</div><div class='long'><p>Gets the \"frame\" information for the element as an object with left, top, right and\nbottom properties holding border+framing size in pixels. This object is calculated\non first request and is cached.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-getMarginInfo' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-getMarginInfo' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-getMarginInfo' class='name expandable'>getMarginInfo</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Gets the margin information for the element as an object with left, top, right and\nbottom properties holding margin s...</div><div class='long'><p>Gets the margin information for the element as an object with left, top, right and\nbottom properties holding margin size in pixels. This object is only read from the\nDOM on first request and is cached.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getPaddingInfo' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-getPaddingInfo' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-getPaddingInfo' class='name expandable'>getPaddingInfo</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Gets the padding information for the element as an object with left, top, right and\nbottom properties holding padding...</div><div class='long'><p>Gets the padding information for the element as an object with left, top, right and\nbottom properties holding padding size in pixels. This object is only read from the\nDOM on first request and is cached.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getProp' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-getProp' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-getProp' class='name expandable'>getProp</a>( <span class='pre'>propName</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Gets a property of this object. ...</div><div class='long'><p>Gets a property of this object. Also tracks the current layout as dependent on this\nproperty so that changes to it will trigger the layout to be recalculated.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>propName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The property name that blocked the layout (e.g., 'width').</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The property value or undefined if not yet set.</p>\n</div></li></ul></div></div></div><div id='method-getStyle' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-getStyle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-getStyle' class='name expandable'>getStyle</a>( <span class='pre'>styleName</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Returns a style for this item. ...</div><div class='long'><p>Returns a style for this item. Each style is read from the DOM only once on first\nrequest and is then cached. If the value is an integer, it is parsed automatically\n(so '5px' is not returned, but rather 5).</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>styleName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The CSS style name.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The value of the DOM style (parsed as necessary).</p>\n</div></li></ul></div></div></div><div id='method-getStyles' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-getStyles' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-getStyles' class='name expandable'>getStyles</a>( <span class='pre'>styleNames, [altNames]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Returns styles for this item. ...</div><div class='long'><p>Returns styles for this item. Each style is read from the DOM only once on first\nrequest and is then cached. If the value is an integer, it is parsed automatically\n(so '5px' is not returned, but rather 5).</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>styleNames</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>The CSS style names.</p>\n</div></li><li><span class='pre'>altNames</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[] (optional)<div class='sub-desc'><p>The alternate names for the returned styles. If given,\nthese names must correspond one-for-one to the <code>styleNames</code>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The values of the DOM styles (parsed as necessary).</p>\n</div></li></ul></div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-hasDomProp' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-hasDomProp' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-hasDomProp' class='name expandable'>hasDomProp</a>( <span class='pre'>propName</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the given property is correct in the DOM. ...</div><div class='long'><p>Returns true if the given property is correct in the DOM. This is equivalent to\ncalling <a href=\"#!/api/Ext.layout.ContextItem-method-getDomProp\" rel=\"Ext.layout.ContextItem-method-getDomProp\" class=\"docClass\">getDomProp</a> and not getting an undefined result. In particular,\nthis call registers the current layout to be triggered by flushes of this property.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>propName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The property name (e.g., 'width').</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-hasProp' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-hasProp' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-hasProp' class='name expandable'>hasProp</a>( <span class='pre'>propName</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the given property has been set. ...</div><div class='long'><p>Returns true if the given property has been set. This is equivalent to calling\n<a href=\"#!/api/Ext.layout.ContextItem-method-getProp\" rel=\"Ext.layout.ContextItem-method-getProp\" class=\"docClass\">getProp</a> and not getting an undefined result. In particular, this call\nregisters the current layout to be triggered by changes to this property.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>propName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The property name (e.g., 'width').</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-init' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-init' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-init' class='name expandable'>init</a>( <span class='pre'>full</span> ) : Mixed<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Clears all properties on this object except (perhaps) those not calculated by this\ncomponent. ...</div><div class='long'><p>Clears all properties on this object except (perhaps) those not calculated by this\ncomponent. This is more complex than it would seem because a layout can decide to\ninvalidate its results and run the component's layouts again, but since some of the\nvalues may be calculated by the container, care must be taken to preserve those\nvalues.</p>\n\n<p><strong>Overridden in Ext.diag.layout.ContextItem.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>full</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True if all properties are to be invalidated, false to keep\nthose calculated by the ownerCt.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>Mixed</span><div class='sub-desc'><p>A value to pass as the first argument to <a href=\"#!/api/Ext.layout.ContextItem-method-initContinue\" rel=\"Ext.layout.ContextItem-method-initContinue\" class=\"docClass\">initContinue</a>.</p>\n</div></li></ul></div></div></div><div id='method-initAnimation' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-initAnimation' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-initAnimation' class='name expandable'>initAnimation</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-initContinue' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-initContinue' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-initContinue' class='name expandable'>initContinue</a>( <span class='pre'>full</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>full</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-initDone' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-initDone' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-initDone' class='name expandable'>initDone</a>( <span class='pre'>containerLayoutDone</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>containerLayoutDone</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-invalidate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-invalidate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-invalidate' class='name expandable'>invalidate</a>( <span class='pre'>options</span> )</div><div class='description'><div class='short'>Invalidates the component associated with this item. ...</div><div class='long'><p>Invalidates the component associated with this item. The layouts for this component\nand all of its contained items will be re-run after first clearing any computed\nvalues.</p>\n\n<p>If state needs to be carried forward beyond the invalidation, the <code>options</code> parameter\ncan be used.</p>\n\n<p><strong>Overridden in Ext.diag.layout.ContextItem.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>An object describing how to handle the invalidation.</p>\n<ul><li><span class='pre'>state</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>An object to <a href=\"#!/api/Ext-method-apply\" rel=\"Ext-method-apply\" class=\"docClass\">Ext.apply</a> to the <a href=\"#!/api/Ext.layout.ContextItem-property-state\" rel=\"Ext.layout.ContextItem-property-state\" class=\"docClass\">state</a>\n of this item after invalidation clears all other properties.</p>\n</div></li><li><span class='pre'>before</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>A function to call after the context data is cleared\nand before the <a href=\"#!/api/Ext.layout.Layout-method-beginLayoutCycle\" rel=\"Ext.layout.Layout-method-beginLayoutCycle\" class=\"docClass\">Ext.layout.Layout.beginLayoutCycle</a> methods are called.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'><p>This ContextItem.</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.layout.ContextItem-method-invalidate\" rel=\"Ext.layout.ContextItem-method-invalidate\" class=\"docClass\">invalidate</a>.</p>\n</div></li></ul></div></li><li><span class='pre'>after</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>A function to call after the context data is cleared\nand after the <a href=\"#!/api/Ext.layout.Layout-method-beginLayoutCycle\" rel=\"Ext.layout.Layout-method-beginLayoutCycle\" class=\"docClass\">Ext.layout.Layout.beginLayoutCycle</a> methods are called.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.layout.ContextItem\" rel=\"Ext.layout.ContextItem\" class=\"docClass\">Ext.layout.ContextItem</a><div class='sub-desc'><p>This ContextItem.</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.layout.ContextItem-method-invalidate\" rel=\"Ext.layout.ContextItem-method-invalidate\" class=\"docClass\">invalidate</a>.</p>\n</div></li></ul></div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope to use when calling the callback functions.</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-markDirty' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-markDirty' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-markDirty' class='name expandable'>markDirty</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-onBoxMeasured' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-onBoxMeasured' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-onBoxMeasured' class='name expandable'>onBoxMeasured</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-parseMargins' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-parseMargins' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-parseMargins' class='name expandable'>parseMargins</a>( <span class='pre'>comp, margins, defaultMargins</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>comp</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>margins</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>defaultMargins</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-peek' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-peek' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-peek' class='name expandable'>peek</a>( <span class='pre'>propName</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>propName</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-recoverProp' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-recoverProp' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-recoverProp' class='name expandable'>recoverProp</a>( <span class='pre'>propName, oldProps, oldDirty</span> )</div><div class='description'><div class='short'>Recovers a property value from the last computation and restores its value and\ndirty state. ...</div><div class='long'><p>Recovers a property value from the last computation and restores its value and\ndirty state.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>propName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the property to recover.</p>\n</div></li><li><span class='pre'>oldProps</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The old \"props\" object from which to recover values.</p>\n</div></li><li><span class='pre'>oldDirty</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The old \"dirty\" object from which to recover state.</p>\n</div></li></ul></div></div></div><div id='method-redo' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-redo' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-redo' class='name expandable'>redo</a>( <span class='pre'>deep</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>deep</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-removeCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-removeCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-removeCls' class='name expandable'>removeCls</a>( <span class='pre'>removeCls</span> )</div><div class='description'><div class='short'>Queue the removal of a class name (or array of class names) from this ContextItem's target when next flushed. ...</div><div class='long'><p>Queue the removal of a class name (or array of class names) from this ContextItem's target when next flushed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>removeCls</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-removeEl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-removeEl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-removeEl' class='name expandable'>removeEl</a>( <span class='pre'>nameOrEl, [owner]</span> )</div><div class='description'><div class='short'>Removes a cached ContextItem that was created using getEl. ...</div><div class='long'><p>Removes a cached ContextItem that was created using <a href=\"#!/api/Ext.layout.ContextItem-method-getEl\" rel=\"Ext.layout.ContextItem-method-getEl\" class=\"docClass\">getEl</a>.  It may be\nnecessary to call this method if the dom reference for owned element changes so\nthat <a href=\"#!/api/Ext.layout.ContextItem-method-getEl\" rel=\"Ext.layout.ContextItem-method-getEl\" class=\"docClass\">getEl</a> can be called again to reinitialize the ContextItem with the\nnew element.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>nameOrEl</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a><div class='sub-desc'><p>The element or the name of an owned element</p>\n</div></li><li><span class='pre'>owner</span> : <a href=\"#!/api/Ext.layout.container.Container\" rel=\"Ext.layout.container.Container\" class=\"docClass\">Ext.layout.container.Container</a>/<a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a> (optional)<div class='sub-desc'><p>The owner of the\nnamed element if the passed \"nameOrEl\" parameter is a String. Defaults to this\nContextItem's \"target\" property.</p>\n</div></li></ul></div></div></div><div id='method-revertProps' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-revertProps' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-revertProps' class='name expandable'>revertProps</a>( <span class='pre'>props</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>props</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setAttribute' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-setAttribute' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-setAttribute' class='name expandable'>setAttribute</a>( <span class='pre'>name, value</span> )</div><div class='description'><div class='short'>Queue the setting of a DOM attribute on this ContextItem's target when next flushed. ...</div><div class='long'><p>Queue the setting of a DOM attribute on this ContextItem's target when next flushed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setBox' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-setBox' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-setBox' class='name expandable'>setBox</a>( <span class='pre'>box</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>box</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setContentHeight' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-setContentHeight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-setContentHeight' class='name expandable'>setContentHeight</a>( <span class='pre'>height, measured</span> )</div><div class='description'><div class='short'>Sets the contentHeight property. ...</div><div class='long'><p>Sets the contentHeight property. If the component uses raw content, then only the\nmeasured height is acceptable.</p>\n\n<p>Calculated values can sometimes be NaN or undefined, which generally mean the\ncalculation is not done. To indicate that such as value was passed, 0 is returned.\nOtherwise, 1 is returned.</p>\n\n<p>If the caller is not measuring (i.e., they are calculating) and the component has raw\ncontent, 1 is returned indicating that the caller is done.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>height</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>measured</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setContentSize' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-setContentSize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-setContentSize' class='name expandable'>setContentSize</a>( <span class='pre'>width, height, measured</span> )</div><div class='description'><div class='short'>Sets the contentWidth and contentHeight properties. ...</div><div class='long'><p>Sets the contentWidth and contentHeight properties. If the component uses raw content,\nthen only the measured values are acceptable.</p>\n\n<p>Calculated values can sometimes be NaN or undefined, which generally means that the\ncalculation is not done. To indicate that either passed value was such a value, false\nreturned. Otherwise, true is returned.</p>\n\n<p>If the caller is not measuring (i.e., they are calculating) and the component has raw\ncontent, true is returned indicating that the caller is done.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>width</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>height</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>measured</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setContentWidth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-setContentWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-setContentWidth' class='name expandable'>setContentWidth</a>( <span class='pre'>width, measured</span> )</div><div class='description'><div class='short'>Sets the contentWidth property. ...</div><div class='long'><p>Sets the contentWidth property. If the component uses raw content, then only the\nmeasured width is acceptable.</p>\n\n<p>Calculated values can sometimes be NaN or undefined, which generally means that the\ncalculation is not done. To indicate that such as value was passed, 0 is returned.\nOtherwise, 1 is returned.</p>\n\n<p>If the caller is not measuring (i.e., they are calculating) and the component has raw\ncontent, 1 is returned indicating that the caller is done.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>width</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>measured</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setHeight' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-setHeight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-setHeight' class='name expandable'>setHeight</a>( <span class='pre'>height, [dirty]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets the height and constrains the height to min/maxHeight range. ...</div><div class='long'><p>Sets the height and constrains the height to min/maxHeight range.</p>\n\n<p><strong>Overridden in Ext.diag.layout.ContextItem.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>height</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The height.</p>\n</div></li><li><span class='pre'>dirty</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>Specifies if the value is currently in the DOM. A\nvalue of <code>false</code> indicates that the value is already in the DOM.</p>\n<p>Defaults to: <code>true</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The actual height after constraining.</p>\n</div></li></ul></div></div></div><div id='method-setProp' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-setProp' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-setProp' class='name expandable'>setProp</a>( <span class='pre'>propName, value, dirty</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets a property value. ...</div><div class='long'><p>Sets a property value. This will unblock and/or trigger dependent layouts if the\nproperty value is being changed. Values of NaN and undefined are not accepted by\nthis method.</p>\n\n<p><strong>Overridden in Ext.diag.layout.ContextItem.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>propName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The property name (e.g., 'width').</p>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new value of the property.</p>\n</div></li><li><span class='pre'>dirty</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>Optionally specifies if the value is currently in the DOM\n (default is <code>true</code> which indicates the value is not in the DOM and must be flushed\n at some point).</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>1 if this call specified the property value, 0 if not.</p>\n</div></li></ul></div></div></div><div id='method-setSize' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-setSize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-setSize' class='name expandable'>setSize</a>( <span class='pre'>width, height, dirty</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>width</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>height</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>dirty</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setWidth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-setWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-setWidth' class='name expandable'>setWidth</a>( <span class='pre'>width, [dirty]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Sets the height and constrains the width to min/maxWidth range. ...</div><div class='long'><p>Sets the height and constrains the width to min/maxWidth range.</p>\n\n<p><strong>Overridden in Ext.diag.layout.ContextItem.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>width</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The width.</p>\n</div></li><li><span class='pre'>dirty</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>Specifies if the value is currently in the DOM. A\nvalue of <code>false</code> indicates that the value is already in the DOM.</p>\n<p>Defaults to: <code>true</code></p></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The actual width after constraining.</p>\n</div></li></ul></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-undo' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-undo' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-undo' class='name expandable'>undo</a>( <span class='pre'>deep</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>deep</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-unsetProp' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-unsetProp' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-unsetProp' class='name expandable'>unsetProp</a>( <span class='pre'>propName</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>propName</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-writeProps' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.ContextItem'>Ext.layout.ContextItem</span><br/><a href='source/ContextItem2.html#Ext-layout-ContextItem-method-writeProps' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.ContextItem-method-writeProps' class='name expandable'>writeProps</a>( <span class='pre'>dirtyProps, flushing</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>dirtyProps</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>flushing</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-create' class='name expandable'>create</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create a new instance of this Class. ...</div><div class='long'><p>Create a new instance of this Class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.create({\n    someConfig: true\n});\n</code></pre>\n\n<p>All parameters are passed to the constructor of the class.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the created instance.</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div></div></div>","superclasses":["Ext.Base"],"meta":{"private":true},"code_type":"ext_define","requires":["Ext.layout.ClassList"],"html_meta":{"private":null},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"ContextItem2.html#Ext-layout-ContextItem","filename":"ContextItem.js"},{"href":"ContextItem.html#Ext-diag-layout-ContextItem","filename":"ContextItem.js"}],"linenr":1,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"borderNames","id":"property-borderNames"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"boxChildren","id":"property-boxChildren"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"boxParent","id":"property-boxParent"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"cacheMissHandlers","id":"property-cacheMissHandlers"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"children","id":"property-children"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"consumersContainerHeight","id":"property-consumersContainerHeight"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"consumersContainerWidth","id":"property-consumersContainerWidth"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"consumersContentHeight","id":"property-consumersContentHeight"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"consumersContentWidth","id":"property-consumersContentWidth"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"consumersHeight","id":"property-consumersHeight"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"consumersWidth","id":"property-consumersWidth"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"dirty","id":"property-dirty"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"dirtyCount","id":"property-dirtyCount"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"hasRawContent","id":"property-hasRawContent"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"heightModel","id":"property-heightModel"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"isBorderBoxValue","id":"property-isBorderBoxValue"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"isContextItem","id":"property-isContextItem"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"isTopLevel","id":"property-isTopLevel"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"marginNames","id":"property-marginNames"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"readonly":true,"private":true},"name":"optOut","id":"property-optOut"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"ownerCtContext","id":"property-ownerCtContext"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"ownerSizePolicy","id":"property-ownerSizePolicy"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"paddingNames","id":"property-paddingNames"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"props","id":"property-props"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"remainingChildDimensions","id":"property-remainingChildDimensions"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"sizeModel","id":"property-sizeModel"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{},"name":"state","id":"property-state"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"styles","id":"property-styles"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"translateProps","id":"property-translateProps"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"trblNames","id":"property-trblNames"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"widthModel","id":"property-widthModel"},{"tagname":"property","owner":"Ext.layout.ContextItem","meta":{"readonly":true},"name":"wrapsComponent","id":"property-wrapsComponent"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"addBlock","id":"method-addBlock"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"addBoxChild","id":"method-addBoxChild"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"addCls","id":"method-addCls"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"addTrigger","id":"method-addTrigger"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"block","id":"method-block"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"boxChildMeasured","id":"method-boxChildMeasured"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"checkAuthority","id":"method-checkAuthority"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"checkCache","id":"method-checkCache"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"clearAllBlocks","id":"method-clearAllBlocks"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"clearBlocks","id":"method-clearBlocks"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"clearMarginCache","id":"method-clearMarginCache"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"domBlock","id":"method-domBlock"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"fireTriggers","id":"method-fireTriggers"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"flush","id":"method-flush"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"flushAnimations","id":"method-flushAnimations"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"getBorderInfo","id":"method-getBorderInfo"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"getClassList","id":"method-getClassList"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"getDomProp","id":"method-getDomProp"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"getEl","id":"method-getEl"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"getFrameInfo","id":"method-getFrameInfo"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"getMarginInfo","id":"method-getMarginInfo"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"getPaddingInfo","id":"method-getPaddingInfo"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"getProp","id":"method-getProp"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"getStyle","id":"method-getStyle"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"getStyles","id":"method-getStyles"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"hasDomProp","id":"method-hasDomProp"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"hasProp","id":"method-hasProp"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"init","id":"method-init"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"initAnimation","id":"method-initAnimation"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"initContinue","id":"method-initContinue"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"initDone","id":"method-initDone"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"invalidate","id":"method-invalidate"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"markDirty","id":"method-markDirty"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"onBoxMeasured","id":"method-onBoxMeasured"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"parseMargins","id":"method-parseMargins"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"peek","id":"method-peek"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"recoverProp","id":"method-recoverProp"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"redo","id":"method-redo"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"removeCls","id":"method-removeCls"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"removeEl","id":"method-removeEl"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"revertProps","id":"method-revertProps"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"setAttribute","id":"method-setAttribute"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"setBox","id":"method-setBox"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"setContentHeight","id":"method-setContentHeight"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"setContentSize","id":"method-setContentSize"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"setContentWidth","id":"method-setContentWidth"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"setHeight","id":"method-setHeight"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"setProp","id":"method-setProp"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"setSize","id":"method-setSize"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{},"name":"setWidth","id":"method-setWidth"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"undo","id":"method-undo"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"unsetProp","id":"method-unsetProp"},{"tagname":"method","owner":"Ext.layout.ContextItem","meta":{"private":true},"name":"writeProps","id":"method-writeProps"}],"event":[],"css_mixin":[]},"inheritable":null,"private":true,"component":false,"name":"Ext.layout.ContextItem","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.layout.ContextItem","mixins":[],"mixedInto":[]});