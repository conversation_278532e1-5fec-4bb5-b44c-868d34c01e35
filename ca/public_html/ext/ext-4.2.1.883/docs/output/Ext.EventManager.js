Ext.data.JsonP.Ext_EventManager({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/EventManager.html#Ext-EventManager' target='_blank'>EventManager.js</a></div></pre><div class='doc-contents'><p>Registers event handlers that want to receive a normalized EventObject instead of the standard browser event and provides\nseveral useful events directly.</p>\n\n<p>See <a href=\"#!/api/Ext.EventObject\" rel=\"Ext.EventObject\" class=\"docClass\">Ext.EventObject</a> for more details on normalized event objects.</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-deferReadyEvent' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-property-deferReadyEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-property-deferReadyEvent' class='name expandable'>deferReadyEvent</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Additionally, allow the 'DOM' listener thread to complete (usually desirable with mobWebkit, Gecko)\nbefore firing the...</div><div class='long'><p>Additionally, allow the 'DOM' listener thread to complete (usually desirable with mobWebkit, Gecko)\nbefore firing the entire onReady chain (high stack load on Loader) by specifying a delay value.\nDefaults to 1ms.</p>\n<p>Defaults to: <code>1</code></p></div></div></div><div id='property-hasBoundOnReady' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-property-hasBoundOnReady' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-property-hasBoundOnReady' class='name expandable'>hasBoundOnReady</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Check if we have bound our global onReady listener ...</div><div class='long'><p>Check if we have bound our global onReady listener</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-hasFiredReady' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-property-hasFiredReady' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-property-hasFiredReady' class='name expandable'>hasFiredReady</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Check if fireDocReady has been called ...</div><div class='long'><p>Check if fireDocReady has been called</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-idleEvent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-property-idleEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-property-idleEvent' class='name expandable'>idleEvent</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>Fires when an event handler finishes its run, just before returning to browser control. ...</div><div class='long'><p>Fires when an event handler finishes its run, just before returning to browser control.</p>\n\n<p>This includes DOM event handlers, Ajax (including JSONP) event handlers, and <a href=\"#!/api/Ext.util.TaskRunner\" rel=\"Ext.util.TaskRunner\" class=\"docClass\">TaskRunners</a></p>\n\n<p>This can be useful for performing cleanup, or update tasks which need to happen only\nafter all code in an event handler has been run, but which should not be executed in a timer\ndue to the intervening browser reflow/repaint which would take place.</p>\n</div></div></div><div id='property-propRe' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-property-propRe' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-property-propRe' class='name expandable'>propRe</a><span> : <a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Options to parse for the 4th argument to addListener. ...</div><div class='long'><p>Options to parse for the 4th argument to addListener.</p>\n<p>Defaults to: <code>/^(?:scope|delay|buffer|single|stopEvent|preventDefault|stopPropagation|normalized|args|delegate|freezeEvent)$/</code></p></div></div></div><div id='property-readyEvent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-property-readyEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-property-readyEvent' class='name not-expandable'>readyEvent</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'><p>Holds references to any onReady functions</p>\n</div><div class='long'><p>Holds references to any onReady functions</p>\n</div></div></div><div id='property-scrollTimeout' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-property-scrollTimeout' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-property-scrollTimeout' class='name not-expandable'>scrollTimeout</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'><p>Timer for doScroll polling</p>\n</div><div class='long'><p>Timer for doScroll polling</p>\n</div></div></div><div id='property-stoppedMouseDownEvent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-property-stoppedMouseDownEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-property-stoppedMouseDownEvent' class='name not-expandable'>stoppedMouseDownEvent</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'><p>Contains a list of all document mouse downs, so we can ensure they fire even when stopEvent is called.</p>\n</div><div class='long'><p>Contains a list of all document mouse downs, so we can ensure they fire even when stopEvent is called.</p>\n</div></div></div><div id='property-useKeyDown' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-property-useKeyDown' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-property-useKeyDown' class='name expandable'>useKeyDown</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>note 1: IE fires ONLY the keydown event on specialkey autorepeat\nnote 2: Safari &lt; 3.1, Gecko (Mac/Linux) &amp; Ope...</div><div class='long'><p>note 1: IE fires ONLY the keydown event on specialkey autorepeat\nnote 2: Safari &lt; 3.1, Gecko (Mac/Linux) &amp; Opera fire only the keypress event on specialkey autorepeat\n(research done by Jan Wolter at http://unixpapa.com/js/key.html)</p>\n</div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-addListener' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-addListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-addListener' class='name expandable'>addListener</a>( <span class='pre'>el, eventName, handler, [scope], [options]</span> )</div><div class='description'><div class='short'>Appends an event handler to an element. ...</div><div class='long'><p>Appends an event handler to an element.  The shorthand version <a href=\"#!/api/Ext.EventManager-method-on\" rel=\"Ext.EventManager-method-on\" class=\"docClass\">on</a> is equivalent.\nTypically you will use <a href=\"#!/api/Ext.dom.Element-method-addListener\" rel=\"Ext.dom.Element-method-addListener\" class=\"docClass\">Ext.Element.addListener</a> directly on an Element in favor of\ncalling this version.</p>\n\n<p><a href=\"#!/api/Ext.EventManager-method-on\" rel=\"Ext.EventManager-method-on\" class=\"docClass\">on</a> is an alias for <a href=\"#!/api/Ext.EventManager-method-addListener\" rel=\"Ext.EventManager-method-addListener\" class=\"docClass\">addListener</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>/HTMLElement/Window<div class='sub-desc'><p>The html element or id to assign the event handler to.</p>\n\n</div></li><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the event to listen for.</p>\n\n</div></li><li><span class='pre'>handler</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The handler function the event invokes. A String parameter\nis assumed to be method name in <code>scope</code> object, or Element object if no scope is provided.</p>\n\n<ul><li><span class='pre'>event</span> : <a href=\"#!/api/Ext.EventObject\" rel=\"Ext.EventObject\" class=\"docClass\">Ext.EventObject</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.EventObject\" rel=\"Ext.EventObject\" class=\"docClass\">EventObject</a> describing the event.</p>\n\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a><div class='sub-desc'><p>The Element which was the target of the event.\nNote that this may be filtered by using the <code>delegate</code> option.</p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object from the addListener call.</p>\n\n</div></li></ul></div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is executed.\nDefaults to the Element.</p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>An object containing handler configuration properties.\nThis may contain any of the following properties (See <a href=\"#!/api/Ext.dom.Element-method-addListener\" rel=\"Ext.dom.Element-method-addListener\" class=\"docClass\">Ext.Element.addListener</a>\nfor examples of how to use these options.):</p>\n\n<ul><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is executed. Defaults to the Element.</p>\n\n</div></li><li><span class='pre'>delegate</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>A simple selector to filter the target or look for a descendant of the target</p>\n\n</div></li><li><span class='pre'>stopEvent</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to stop the event. That is stop propagation, and prevent the default action.</p>\n\n</div></li><li><span class='pre'>preventDefault</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to prevent the default action</p>\n\n</div></li><li><span class='pre'>stopPropagation</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to prevent event propagation</p>\n\n</div></li><li><span class='pre'>normalized</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>False to pass a browser event to the handler function instead of an <a href=\"#!/api/Ext.EventObject\" rel=\"Ext.EventObject\" class=\"docClass\">Ext.EventObject</a></p>\n\n</div></li><li><span class='pre'>delay</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of milliseconds to delay the invocation of the handler after te event fires.</p>\n\n</div></li><li><span class='pre'>single</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to add a handler to handle just the next firing of the event, and then remove itself.</p>\n\n</div></li><li><span class='pre'>buffer</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Causes the handler to be scheduled to run in an <a href=\"#!/api/Ext.util.DelayedTask\" rel=\"Ext.util.DelayedTask\" class=\"docClass\">Ext.util.DelayedTask</a> delayed\nby the specified number of milliseconds. If the event fires again within that time, the original\nhandler is <em>not</em> invoked, but the new handler is scheduled in its place.</p>\n\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a><div class='sub-desc'><p>Only call the handler if the event was fired on the target Element,\n<em>not</em> if the event was bubbled up from a child node.</p>\n\n</div></li></ul></div></li></ul></div></div></div><div id='method-bindReadyEvent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-bindReadyEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-bindReadyEvent' class='name expandable'>bindReadyEvent</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Binds the appropriate browser event for checking if the DOM has loaded. ...</div><div class='long'><p>Binds the appropriate browser event for checking if the DOM has loaded.</p>\n</div></div></div><div id='method-cloneEventListenerCache' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-cloneEventListenerCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-cloneEventListenerCache' class='name expandable'>cloneEventListenerCache</a>( <span class='pre'>element, eventName</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Clones the event cache for a particular element for a particular event ...</div><div class='long'><p>Clones the event cache for a particular element for a particular event</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>element</span> : HTMLElement<div class='sub-desc'><p>The element</p>\n</div></li><li><span class='pre'>eventName</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The event name</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>The cloned events for the element</p>\n</div></li></ul></div></div></div><div id='method-contains' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-contains' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-contains' class='name expandable'>contains</a>( <span class='pre'>event</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Checks whether the event's relatedTarget is contained inside (or is) the element. ...</div><div class='long'><p>Checks whether the event's relatedTarget is contained inside (or <b>is</b>) the element.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>event</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createListenerWrap' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-createListenerWrap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-createListenerWrap' class='name expandable'>createListenerWrap</a>( <span class='pre'>dom, ename, fn, scope, options</span> ) : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Create the wrapper function for the event ...</div><div class='long'><p>Create the wrapper function for the event</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>dom</span> : HTMLElement<div class='sub-desc'><p>The dom element</p>\n</div></li><li><span class='pre'>ename</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The event name</p>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The function to execute</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope to execute callback in</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></span><div class='sub-desc'><p>the wrapper function</p>\n</div></li></ul></div></div></div><div id='method-fireDocReady' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-fireDocReady' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-fireDocReady' class='name expandable'>fireDocReady</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>We know the document is loaded, so trigger any onReady events. ...</div><div class='long'><p>We know the document is loaded, so trigger any onReady events.</p>\n</div></div></div><div id='method-fireReadyEvent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-fireReadyEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-fireReadyEvent' class='name expandable'>fireReadyEvent</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Fires the ready event ...</div><div class='long'><p>Fires the ready event</p>\n</div></div></div><div id='method-fireResize' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-fireResize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-fireResize' class='name expandable'>fireResize</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Fire the resize event. ...</div><div class='long'><p>Fire the resize event.</p>\n</div></div></div><div id='method-fireUnload' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-fireUnload' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-fireUnload' class='name expandable'>fireUnload</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Fires the unload event for items bound with onWindowUnload ...</div><div class='long'><p>Fires the unload event for items bound with onWindowUnload</p>\n</div></div></div><div id='method-getEventCache' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-getEventCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-getEventCache' class='name expandable'>getEventCache</a>( <span class='pre'>element</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Gets the event cache object for a particular element ...</div><div class='long'><p>Gets the event cache object for a particular element</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>element</span> : HTMLElement<div class='sub-desc'><p>The element</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The event cache object</p>\n</div></li></ul></div></div></div><div id='method-getEventListenerCache' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-getEventListenerCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-getEventListenerCache' class='name expandable'>getEventListenerCache</a>( <span class='pre'>element, eventName</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Get the event cache for a particular element for a particular event ...</div><div class='long'><p>Get the event cache for a particular element for a particular event</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>element</span> : HTMLElement<div class='sub-desc'><p>The element</p>\n</div></li><li><span class='pre'>eventName</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The event name</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>The events for the element</p>\n</div></li></ul></div></div></div><div id='method-getId' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-getId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-getId' class='name expandable'>getId</a>( <span class='pre'>element</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Get the id of the element. ...</div><div class='long'><p>Get the id of the element. If one has not been assigned, automatically assign it.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>element</span> : HTMLElement/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The element to get the id for.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>id</p>\n</div></li></ul></div></div></div><div id='method-getKeyEvent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-getKeyEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-getKeyEvent' class='name expandable'>getKeyEvent</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Indicates which event to use for getting key presses. ...</div><div class='long'><p>Indicates which event to use for getting key presses.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The appropriate event name.</p>\n</div></li></ul></div></div></div><div id='method-getPageX' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-getPageX' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-getPageX' class='name expandable'>getPageX</a>( <span class='pre'>event</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Gets the x coordinate from the event ...</div><div class='long'><p>Gets the x coordinate from the event</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>event</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The event</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The x coordinate</p>\n</div></li></ul></div></div></div><div id='method-getPageXY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-getPageXY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-getPageXY' class='name expandable'>getPageXY</a>( <span class='pre'>event</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</div><div class='description'><div class='short'>Gets the x &amp; y coordinate from the event ...</div><div class='long'><p>Gets the x &amp; y coordinate from the event</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>event</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The event</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</span><div class='sub-desc'><p>The x/y coordinate</p>\n</div></li></ul></div></div></div><div id='method-getPageY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-getPageY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-getPageY' class='name expandable'>getPageY</a>( <span class='pre'>event</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Gets the y coordinate from the event ...</div><div class='long'><p>Gets the y coordinate from the event</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>event</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The event</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The y coordinate</p>\n</div></li></ul></div></div></div><div id='method-getRelatedTarget' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-getRelatedTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-getRelatedTarget' class='name expandable'>getRelatedTarget</a>( <span class='pre'>event</span> ) : HTMLElement</div><div class='description'><div class='short'>Gets the related target from the event. ...</div><div class='long'><p>Gets the related target from the event.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>event</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The event</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>HTMLElement</span><div class='sub-desc'><p>The related target.</p>\n</div></li></ul></div></div></div><div id='method-getTarget' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-getTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-getTarget' class='name expandable'>getTarget</a>( <span class='pre'>event</span> ) : HTMLElement</div><div class='description'><div class='short'>Gets the target of the event. ...</div><div class='long'><p>Gets the target of the event.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>event</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The event</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>HTMLElement</span><div class='sub-desc'><p>target</p>\n</div></li></ul></div></div></div><div id='method-isReadyPaused' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-isReadyPaused' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-isReadyPaused' class='name expandable'>isReadyPaused</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>detects whether the EventManager has been placed in a paused state for synchronization\nwith external debugging / perf...</div><div class='long'><p>detects whether the EventManager has been placed in a paused state for synchronization\nwith external debugging / perf tools (PageAnalyzer)</p>\n</div></div></div><div id='method-normalizeEvent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-normalizeEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-normalizeEvent' class='name expandable'>normalizeEvent</a>( <span class='pre'>eventName, fn</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Normalize cross browser event differences ...</div><div class='long'><p>Normalize cross browser event differences</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The event name</p>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The function to execute</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The new event name/function</p>\n</div></li></ul></div></div></div><div id='method-on' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-on' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-on' class='name expandable'>on</a>( <span class='pre'>el, eventName, handler, [scope], [options]</span> )</div><div class='description'><div class='short'>Appends an event handler to an element. ...</div><div class='long'><p>Appends an event handler to an element.  The shorthand version <a href=\"#!/api/Ext.EventManager-method-on\" rel=\"Ext.EventManager-method-on\" class=\"docClass\">on</a> is equivalent.\nTypically you will use <a href=\"#!/api/Ext.dom.Element-method-addListener\" rel=\"Ext.dom.Element-method-addListener\" class=\"docClass\">Ext.Element.addListener</a> directly on an Element in favor of\ncalling this version.</p>\n\n<p><a href=\"#!/api/Ext.EventManager-method-on\" rel=\"Ext.EventManager-method-on\" class=\"docClass\">on</a> is an alias for <a href=\"#!/api/Ext.EventManager-method-addListener\" rel=\"Ext.EventManager-method-addListener\" class=\"docClass\">addListener</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>/HTMLElement/Window<div class='sub-desc'><p>The html element or id to assign the event handler to.</p>\n\n</div></li><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the event to listen for.</p>\n\n</div></li><li><span class='pre'>handler</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The handler function the event invokes. A String parameter\nis assumed to be method name in <code>scope</code> object, or Element object if no scope is provided.</p>\n\n<ul><li><span class='pre'>event</span> : <a href=\"#!/api/Ext.EventObject\" rel=\"Ext.EventObject\" class=\"docClass\">Ext.EventObject</a><div class='sub-desc'><p>The <a href=\"#!/api/Ext.EventObject\" rel=\"Ext.EventObject\" class=\"docClass\">EventObject</a> describing the event.</p>\n\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a><div class='sub-desc'><p>The Element which was the target of the event.\nNote that this may be filtered by using the <code>delegate</code> option.</p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object from the addListener call.</p>\n\n</div></li></ul></div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is executed.\nDefaults to the Element.</p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>An object containing handler configuration properties.\nThis may contain any of the following properties (See <a href=\"#!/api/Ext.dom.Element-method-addListener\" rel=\"Ext.dom.Element-method-addListener\" class=\"docClass\">Ext.Element.addListener</a>\nfor examples of how to use these options.):</p>\n\n<ul><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is executed. Defaults to the Element.</p>\n\n</div></li><li><span class='pre'>delegate</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>A simple selector to filter the target or look for a descendant of the target</p>\n\n</div></li><li><span class='pre'>stopEvent</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to stop the event. That is stop propagation, and prevent the default action.</p>\n\n</div></li><li><span class='pre'>preventDefault</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to prevent the default action</p>\n\n</div></li><li><span class='pre'>stopPropagation</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to prevent event propagation</p>\n\n</div></li><li><span class='pre'>normalized</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>False to pass a browser event to the handler function instead of an <a href=\"#!/api/Ext.EventObject\" rel=\"Ext.EventObject\" class=\"docClass\">Ext.EventObject</a></p>\n\n</div></li><li><span class='pre'>delay</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of milliseconds to delay the invocation of the handler after te event fires.</p>\n\n</div></li><li><span class='pre'>single</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to add a handler to handle just the next firing of the event, and then remove itself.</p>\n\n</div></li><li><span class='pre'>buffer</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Causes the handler to be scheduled to run in an <a href=\"#!/api/Ext.util.DelayedTask\" rel=\"Ext.util.DelayedTask\" class=\"docClass\">Ext.util.DelayedTask</a> delayed\nby the specified number of milliseconds. If the event fires again within that time, the original\nhandler is <em>not</em> invoked, but the new handler is scheduled in its place.</p>\n\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a><div class='sub-desc'><p>Only call the handler if the event was fired on the target Element,\n<em>not</em> if the event was bubbled up from a child node.</p>\n\n</div></li></ul></div></li></ul></div></div></div><div id='method-onDocumentReady' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-onDocumentReady' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-onDocumentReady' class='name expandable'>onDocumentReady</a>( <span class='pre'>fn, [scope], [options]</span> )</div><div class='description'><div class='short'>Adds a listener to be notified when the document is ready (before onload and before images are loaded). ...</div><div class='long'><p>Adds a listener to be notified when the document is ready (before onload and before images are loaded).</p>\n\n<p><a href=\"#!/api/Ext-method-onDocumentReady\" rel=\"Ext-method-onDocumentReady\" class=\"docClass\">Ext.onDocumentReady</a> is an alias for <a href=\"#!/api/Ext.EventManager-method-onDocumentReady\" rel=\"Ext.EventManager-method-onDocumentReady\" class=\"docClass\">onDocumentReady</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The method the event invokes.</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function executes.\nDefaults to the browser window.</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Options object as passed to <a href=\"#!/api/Ext.dom.Element-method-addListener\" rel=\"Ext.dom.Element-method-addListener\" class=\"docClass\">Ext.Element.addListener</a>.</p>\n</div></li></ul></div></div></div><div id='method-onWindowResize' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-onWindowResize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-onWindowResize' class='name expandable'>onWindowResize</a>( <span class='pre'>fn, scope, [options]</span> )</div><div class='description'><div class='short'>Adds a listener to be notified when the browser window is resized and provides resize event buffering (100 millisecon...</div><div class='long'><p>Adds a listener to be notified when the browser window is resized and provides resize event buffering (100 milliseconds),\npasses new viewport width and height to handlers.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The handler function the window resize event invokes.</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function executes. Defaults to the browser window.</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>Options object as passed to <a href=\"#!/api/Ext.dom.Element-method-addListener\" rel=\"Ext.dom.Element-method-addListener\" class=\"docClass\">Ext.Element.addListener</a></p>\n</div></li></ul></div></div></div><div id='method-onWindowUnload' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-onWindowUnload' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-onWindowUnload' class='name expandable'>onWindowUnload</a>( <span class='pre'>fn, scope, options</span> )</div><div class='description'><div class='short'>Adds a listener to be notified when the browser window is unloaded. ...</div><div class='long'><p>Adds a listener to be notified when the browser window is unloaded.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The handler function the window unload event invokes.</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function executes. Defaults to the browser window.</p>\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>Options object as passed to <a href=\"#!/api/Ext.dom.Element-method-addListener\" rel=\"Ext.dom.Element-method-addListener\" class=\"docClass\">Ext.Element.addListener</a></p>\n</div></li></ul></div></div></div><div id='method-pollScroll' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-pollScroll' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-pollScroll' class='name expandable'>pollScroll</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>This strategy has minimal benefits for Sencha solutions that build themselves (ie. ...</div><div class='long'><p>This strategy has minimal benefits for Sencha solutions that build themselves (ie. minimal initial page markup).\nHowever, progressively-enhanced pages (with image content and/or embedded frames) will benefit the most from it.\nBrowser timer resolution is too poor to ensure a doScroll check more than once on a page loaded with minimal\nassets (the readystatechange event 'complete' usually beats the doScroll timer on a 'lightly-loaded' initial document).</p>\n</div></div></div><div id='method-prepareListenerConfig' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-prepareListenerConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-prepareListenerConfig' class='name expandable'>prepareListenerConfig</a>( <span class='pre'>element, event, isRemove</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Convert a \"config style\" listener into a set of flat arguments so they can be passed to addListener ...</div><div class='long'><p>Convert a \"config style\" listener into a set of flat arguments so they can be passed to addListener</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>element</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The element the event is for</p>\n</div></li><li><span class='pre'>event</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The event configuration</p>\n</div></li><li><span class='pre'>isRemove</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>True if a removal should be performed, otherwise an add will be done.</p>\n</div></li></ul></div></div></div><div id='method-preventDefault' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-preventDefault' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-preventDefault' class='name expandable'>preventDefault</a>( <span class='pre'>event</span> )</div><div class='description'><div class='short'>Prevents the browsers default handling of the event. ...</div><div class='long'><p>Prevents the browsers default handling of the event.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>event</span> : Event<div class='sub-desc'><p>The event to prevent the default</p>\n</div></li></ul></div></div></div><div id='method-purgeElement' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-purgeElement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-purgeElement' class='name expandable'>purgeElement</a>( <span class='pre'>el, [eventName]</span> )</div><div class='description'><div class='short'>Recursively removes all previous added listeners from an element and its children. ...</div><div class='long'><p>Recursively removes all previous added listeners from an element and its children. Typically you will use <a href=\"#!/api/Ext.dom.Element-method-purgeAllListeners\" rel=\"Ext.dom.Element-method-purgeAllListeners\" class=\"docClass\">Ext.Element.purgeAllListeners</a>\ndirectly on an Element in favor of calling this version.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>/HTMLElement/Window<div class='sub-desc'><p>The id or html element from which to remove all event handlers.</p>\n</div></li><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The name of the event.</p>\n</div></li></ul></div></div></div><div id='method-removeAll' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-removeAll' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-removeAll' class='name expandable'>removeAll</a>( <span class='pre'>el</span> )</div><div class='description'><div class='short'>Removes all event handers from an element. ...</div><div class='long'><p>Removes all event handers from an element.  Typically you will use <a href=\"#!/api/Ext.dom.Element-method-removeAllListeners\" rel=\"Ext.dom.Element-method-removeAllListeners\" class=\"docClass\">Ext.Element.removeAllListeners</a>\ndirectly on an Element in favor of calling this version.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>/HTMLElement/Window<div class='sub-desc'><p>The id or html element from which to remove all event handlers.</p>\n</div></li></ul></div></div></div><div id='method-removeListener' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-removeListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-removeListener' class='name expandable'>removeListener</a>( <span class='pre'>el, eventName, fn, scope</span> )</div><div class='description'><div class='short'>Removes an event handler from an element. ...</div><div class='long'><p>Removes an event handler from an element.  The shorthand version <a href=\"#!/api/Ext.EventManager-method-un\" rel=\"Ext.EventManager-method-un\" class=\"docClass\">un</a> is equivalent.  Typically\nyou will use <a href=\"#!/api/Ext.dom.Element-method-removeListener\" rel=\"Ext.dom.Element-method-removeListener\" class=\"docClass\">Ext.Element.removeListener</a> directly on an Element in favor of calling this version.</p>\n\n<p><a href=\"#!/api/Ext.EventManager-method-on\" rel=\"Ext.EventManager-method-on\" class=\"docClass\">on</a> is an alias for <a href=\"#!/api/Ext.EventManager-method-addListener\" rel=\"Ext.EventManager-method-addListener\" class=\"docClass\">addListener</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>/HTMLElement/Window<div class='sub-desc'><p>The id or html element from which to remove the listener.</p>\n\n</div></li><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the event.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The handler function to remove. <strong>This must be a reference to the function passed\ninto the <a href=\"#!/api/Ext.EventManager-method-addListener\" rel=\"Ext.EventManager-method-addListener\" class=\"docClass\">addListener</a> call.</strong></p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>If a scope (<code>this</code> reference) was specified when the listener was added,\nthen this must refer to the same object.</p>\n\n</div></li></ul></div></div></div><div id='method-removeResizeListener' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-removeResizeListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-removeResizeListener' class='name expandable'>removeResizeListener</a>( <span class='pre'>fn, scope</span> )</div><div class='description'><div class='short'>Removes the passed window resize listener. ...</div><div class='long'><p>Removes the passed window resize listener.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The method the event invokes</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope of handler</p>\n</div></li></ul></div></div></div><div id='method-removeUnloadListener' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-removeUnloadListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-removeUnloadListener' class='name expandable'>removeUnloadListener</a>( <span class='pre'>fn, scope</span> )</div><div class='description'><div class='short'>Removes the passed window unload listener. ...</div><div class='long'><p>Removes the passed window unload listener.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The method the event invokes</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope of handler</p>\n</div></li></ul></div></div></div><div id='method-resolveTextNode' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-resolveTextNode' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-resolveTextNode' class='name expandable'>resolveTextNode</a>( <span class='pre'>node</span> ) : HTMLElement<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Resolve any text nodes accounting for browser differences. ...</div><div class='long'><p>Resolve any text nodes accounting for browser differences.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>node</span> : HTMLElement<div class='sub-desc'><p>The node</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'>HTMLElement</span><div class='sub-desc'><p>The resolved node</p>\n</div></li></ul></div></div></div><div id='method-stopEvent' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-stopEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-stopEvent' class='name expandable'>stopEvent</a>( <span class='pre'>event</span> )</div><div class='description'><div class='short'>Stop the event (preventDefault and stopPropagation) ...</div><div class='long'><p>Stop the event (preventDefault and stopPropagation)</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>event</span> : Event<div class='sub-desc'><p>The event to stop</p>\n</div></li></ul></div></div></div><div id='method-stopPropagation' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-stopPropagation' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-stopPropagation' class='name expandable'>stopPropagation</a>( <span class='pre'>event</span> )</div><div class='description'><div class='short'>Cancels bubbling of the event. ...</div><div class='long'><p>Cancels bubbling of the event.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>event</span> : Event<div class='sub-desc'><p>The event to stop bubbling.</p>\n</div></li></ul></div></div></div><div id='method-un' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.EventManager'>Ext.EventManager</span><br/><a href='source/EventManager.html#Ext-EventManager-method-un' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.EventManager-method-un' class='name expandable'>un</a>( <span class='pre'>el, eventName, fn, scope</span> )</div><div class='description'><div class='short'>Removes an event handler from an element. ...</div><div class='long'><p>Removes an event handler from an element.  The shorthand version <a href=\"#!/api/Ext.EventManager-method-un\" rel=\"Ext.EventManager-method-un\" class=\"docClass\">un</a> is equivalent.  Typically\nyou will use <a href=\"#!/api/Ext.dom.Element-method-removeListener\" rel=\"Ext.dom.Element-method-removeListener\" class=\"docClass\">Ext.Element.removeListener</a> directly on an Element in favor of calling this version.</p>\n\n<p><a href=\"#!/api/Ext.EventManager-method-on\" rel=\"Ext.EventManager-method-on\" class=\"docClass\">on</a> is an alias for <a href=\"#!/api/Ext.EventManager-method-addListener\" rel=\"Ext.EventManager-method-addListener\" class=\"docClass\">addListener</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>/HTMLElement/Window<div class='sub-desc'><p>The id or html element from which to remove the listener.</p>\n\n</div></li><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the event.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The handler function to remove. <strong>This must be a reference to the function passed\ninto the <a href=\"#!/api/Ext.EventManager-method-addListener\" rel=\"Ext.EventManager-method-addListener\" class=\"docClass\">addListener</a> call.</strong></p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>If a scope (<code>this</code> reference) was specified when the listener was added,\nthen this must refer to the same object.</p>\n\n</div></li></ul></div></div></div></div></div></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"EventManager.html#Ext-EventManager","filename":"EventManager.js"}],"linenr":5,"members":{"property":[{"tagname":"property","owner":"Ext.EventManager","meta":{"private":true},"name":"deferReadyEvent","id":"property-deferReadyEvent"},{"tagname":"property","owner":"Ext.EventManager","meta":{"private":true},"name":"hasBoundOnReady","id":"property-hasBoundOnReady"},{"tagname":"property","owner":"Ext.EventManager","meta":{"private":true},"name":"hasFiredReady","id":"property-hasFiredReady"},{"tagname":"property","owner":"Ext.EventManager","meta":{},"name":"idleEvent","id":"property-idleEvent"},{"tagname":"property","owner":"Ext.EventManager","meta":{"private":true},"name":"propRe","id":"property-propRe"},{"tagname":"property","owner":"Ext.EventManager","meta":{"private":true},"name":"readyEvent","id":"property-readyEvent"},{"tagname":"property","owner":"Ext.EventManager","meta":{"private":true},"name":"scrollTimeout","id":"property-scrollTimeout"},{"tagname":"property","owner":"Ext.EventManager","meta":{"private":true},"name":"stoppedMouseDownEvent","id":"property-stoppedMouseDownEvent"},{"tagname":"property","owner":"Ext.EventManager","meta":{"private":true},"name":"useKeyDown","id":"property-useKeyDown"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"addListener","id":"method-addListener"},{"tagname":"method","owner":"Ext.EventManager","meta":{"private":true},"name":"bindReadyEvent","id":"method-bindReadyEvent"},{"tagname":"method","owner":"Ext.EventManager","meta":{"private":true},"name":"cloneEventListenerCache","id":"method-cloneEventListenerCache"},{"tagname":"method","owner":"Ext.EventManager","meta":{"private":true},"name":"contains","id":"method-contains"},{"tagname":"method","owner":"Ext.EventManager","meta":{"private":true},"name":"createListenerWrap","id":"method-createListenerWrap"},{"tagname":"method","owner":"Ext.EventManager","meta":{"private":true},"name":"fireDocReady","id":"method-fireDocReady"},{"tagname":"method","owner":"Ext.EventManager","meta":{"private":true},"name":"fireReadyEvent","id":"method-fireReadyEvent"},{"tagname":"method","owner":"Ext.EventManager","meta":{"private":true},"name":"fireResize","id":"method-fireResize"},{"tagname":"method","owner":"Ext.EventManager","meta":{"private":true},"name":"fireUnload","id":"method-fireUnload"},{"tagname":"method","owner":"Ext.EventManager","meta":{"private":true},"name":"getEventCache","id":"method-getEventCache"},{"tagname":"method","owner":"Ext.EventManager","meta":{"private":true},"name":"getEventListenerCache","id":"method-getEventListenerCache"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"getId","id":"method-getId"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"getKeyEvent","id":"method-getKeyEvent"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"getPageX","id":"method-getPageX"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"getPageXY","id":"method-getPageXY"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"getPageY","id":"method-getPageY"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"getRelatedTarget","id":"method-getRelatedTarget"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"getTarget","id":"method-getTarget"},{"tagname":"method","owner":"Ext.EventManager","meta":{"private":true},"name":"isReadyPaused","id":"method-isReadyPaused"},{"tagname":"method","owner":"Ext.EventManager","meta":{"private":true},"name":"normalizeEvent","id":"method-normalizeEvent"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"on","id":"method-on"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"onDocumentReady","id":"method-onDocumentReady"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"onWindowResize","id":"method-onWindowResize"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"onWindowUnload","id":"method-onWindowUnload"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"pollScroll","id":"method-pollScroll"},{"tagname":"method","owner":"Ext.EventManager","meta":{"private":true},"name":"prepareListenerConfig","id":"method-prepareListenerConfig"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"preventDefault","id":"method-preventDefault"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"purgeElement","id":"method-purgeElement"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"removeAll","id":"method-removeAll"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"removeListener","id":"method-removeListener"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"removeResizeListener","id":"method-removeResizeListener"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"removeUnloadListener","id":"method-removeUnloadListener"},{"tagname":"method","owner":"Ext.EventManager","meta":{"private":true},"name":"resolveTextNode","id":"method-resolveTextNode"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"stopEvent","id":"method-stopEvent"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"stopPropagation","id":"method-stopPropagation"},{"tagname":"method","owner":"Ext.EventManager","meta":{},"name":"un","id":"method-un"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.EventManager","singleton":true,"override":null,"inheritdoc":null,"id":"class-Ext.EventManager","mixins":[],"mixedInto":[]});