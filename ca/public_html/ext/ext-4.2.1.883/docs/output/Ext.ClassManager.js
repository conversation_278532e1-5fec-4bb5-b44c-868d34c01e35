Ext.data.JsonP.Ext_ClassManager({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/ClassManager.html#Ext-ClassManager' target='_blank'>ClassManager.js</a></div></pre><div class='doc-contents'><p><a href=\"#!/api/Ext.ClassManager\" rel=\"Ext.ClassManager\" class=\"docClass\">Ext.ClassManager</a> manages all classes and handles mapping from string class name to\nactual class objects throughout the whole framework. It is not generally accessed directly, rather through\nthese convenient shorthands:</p>\n\n<ul>\n<li><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a></li>\n<li><a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a></li>\n<li><a href=\"#!/api/Ext-method-widget\" rel=\"Ext-method-widget\" class=\"docClass\">Ext.widget</a></li>\n<li><a href=\"#!/api/Ext-method-getClass\" rel=\"Ext-method-getClass\" class=\"docClass\">Ext.getClass</a></li>\n<li><a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a></li>\n</ul>\n\n\n<h1>Basic syntax:</h1>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>(className, properties);\n</code></pre>\n\n<p>in which <code>properties</code> is an object represent a collection of properties that apply to the class. See\n<a href=\"#!/api/Ext.ClassManager-method-create\" rel=\"Ext.ClassManager-method-create\" class=\"docClass\">create</a> for more detailed instructions.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Person', {\n     name: 'Unknown',\n\n     constructor: function(name) {\n         if (name) {\n             this.name = name;\n         }\n     },\n\n     eat: function(foodType) {\n         alert(\"I'm eating: \" + foodType);\n\n         return this;\n     }\n});\n\nvar aaron = new Person(\"Aaron\");\naaron.eat(\"Sandwich\"); // alert(\"I'm eating: Sandwich\");\n</code></pre>\n\n<p><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a> has a powerful set of extensible <a href=\"#!/api/Ext.Class-static-method-registerPreprocessor\" rel=\"Ext.Class-static-method-registerPreprocessor\" class=\"docClass\">pre-processors</a> which takes care of\neverything related to class creation, including but not limited to inheritance, mixins, configuration, statics, etc.</p>\n\n<h1>Inheritance:</h1>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Developer', {\n     extend: 'Person',\n\n     constructor: function(name, isGeek) {\n         this.isGeek = isGeek;\n\n         // Apply a method from the parent class' prototype\n         this.callParent([name]);\n     },\n\n     code: function(language) {\n         alert(\"I'm coding in: \" + language);\n\n         this.eat(\"Bugs\");\n\n         return this;\n     }\n});\n\nvar jacky = new Developer(\"Jacky\", true);\njacky.code(\"JavaScript\"); // alert(\"I'm coding in: JavaScript\");\n                          // alert(\"I'm eating: Bugs\");\n</code></pre>\n\n<p>See <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">Ext.Base.callParent</a> for more details on calling superclass' methods</p>\n\n<h1>Mixins:</h1>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('CanPlayGuitar', {\n     playGuitar: function() {\n        alert(\"F#...G...D...A\");\n     }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('CanComposeSongs', {\n     composeSongs: function() { ... }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('CanSing', {\n     sing: function() {\n         alert(\"I'm on the highway to hell...\")\n     }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Musician', {\n     extend: 'Person',\n\n     mixins: {\n         canPlayGuitar: 'CanPlayGuitar',\n         canComposeSongs: 'CanComposeSongs',\n         canSing: 'CanSing'\n     }\n})\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('CoolPerson', {\n     extend: 'Person',\n\n     mixins: {\n         canPlayGuitar: 'CanPlayGuitar',\n         canSing: 'CanSing'\n     },\n\n     sing: function() {\n         alert(\"Ahem....\");\n\n         this.mixins.canSing.sing.call(this);\n\n         alert(\"[Playing guitar at the same time...]\");\n\n         this.playGuitar();\n     }\n});\n\nvar me = new CoolPerson(\"Jacky\");\n\nme.sing(); // alert(\"Ahem...\");\n           // alert(\"I'm on the highway to hell...\");\n           // alert(\"[Playing guitar at the same time...]\");\n           // alert(\"F#...G...D...A\");\n</code></pre>\n\n<h1>Config:</h1>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('SmartPhone', {\n     config: {\n         hasTouchScreen: false,\n         operatingSystem: 'Other',\n         price: 500\n     },\n\n     isExpensive: false,\n\n     constructor: function(config) {\n         this.initConfig(config);\n     },\n\n     applyPrice: function(price) {\n         this.isExpensive = (price &gt; 500);\n\n         return price;\n     },\n\n     applyOperatingSystem: function(operatingSystem) {\n         if (!(/^(iOS|Android|BlackBerry)$/i).test(operatingSystem)) {\n             return 'Other';\n         }\n\n         return operatingSystem;\n     }\n});\n\nvar iPhone = new SmartPhone({\n     hasTouchScreen: true,\n     operatingSystem: 'iOS'\n});\n\niPhone.getPrice(); // 500;\niPhone.getOperatingSystem(); // 'iOS'\niPhone.getHasTouchScreen(); // true;\niPhone.hasTouchScreen(); // true\n\niPhone.isExpensive; // false;\niPhone.setPrice(600);\niPhone.getPrice(); // 600\niPhone.isExpensive; // true;\n\niPhone.setOperatingSystem('AlienOS');\niPhone.getOperatingSystem(); // 'Other'\n</code></pre>\n\n<h1>Statics:</h1>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Computer', {\n     statics: {\n         factory: function(brand) {\n            // 'this' in static methods refer to the class itself\n             return new this(brand);\n         }\n     },\n\n     constructor: function() { ... }\n});\n\nvar dellComputer = Computer.factory('Dell');\n</code></pre>\n\n<p>Also see <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">Ext.Base.statics</a> and <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">Ext.Base.self</a> for more details on accessing\nstatic properties within class methods</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-classes' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-property-classes' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-property-classes' class='name expandable'>classes</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>All classes which were defined through the ClassManager. ...</div><div class='long'><p>All classes which were defined through the ClassManager. Keys are the\nname of the classes and the values are references to the classes.</p>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-createdListeners' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-property-createdListeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-property-createdListeners' class='name expandable'>createdListeners</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-defaultPostprocessors' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-property-defaultPostprocessors' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-property-defaultPostprocessors' class='name expandable'>defaultPostprocessors</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-enableNamespaceParseCache' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-property-enableNamespaceParseCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-property-enableNamespaceParseCache' class='name expandable'>enableNamespaceParseCache</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-existCache' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-property-existCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-property-existCache' class='name expandable'>existCache</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-instantiators' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-property-instantiators' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-property-instantiators' class='name expandable'>instantiators</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-maps' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-property-maps' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-property-maps' class='name expandable'>maps</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{alternateToName: {}, aliasToName: {}, nameToAliases: {}, nameToAlternates: {}}</code></p></div></div></div><div id='property-nameCreatedListeners' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-property-nameCreatedListeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-property-nameCreatedListeners' class='name expandable'>nameCreatedListeners</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-namespaceParseCache' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-property-namespaceParseCache' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-property-namespaceParseCache' class='name expandable'>namespaceParseCache</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-namespaceRewrites' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-property-namespaceRewrites' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-property-namespaceRewrites' class='name not-expandable'>namespaceRewrites</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-postprocessors' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-property-postprocessors' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-property-postprocessors' class='name expandable'>postprocessors</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-addNameAliasMappings' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-addNameAliasMappings' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-addNameAliasMappings' class='name expandable'>addNameAliasMappings</a>( <span class='pre'>aliases</span> ) : <a href=\"#!/api/Ext.ClassManager\" rel=\"Ext.ClassManager\" class=\"docClass\">Ext.ClassManager</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Adds a batch of class name to alias mappings ...</div><div class='long'><p>Adds a batch of class name to alias mappings</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>aliases</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The set of mappings of the form\nclassName : [values...]</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.ClassManager\" rel=\"Ext.ClassManager\" class=\"docClass\">Ext.ClassManager</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-addNameAlternateMappings' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-addNameAlternateMappings' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-addNameAlternateMappings' class='name expandable'>addNameAlternateMappings</a>( <span class='pre'>alternates</span> ) : <a href=\"#!/api/Ext.ClassManager\" rel=\"Ext.ClassManager\" class=\"docClass\">Ext.ClassManager</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alternates</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The set of mappings of the form\nclassName : [values...]</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.ClassManager\" rel=\"Ext.ClassManager\" class=\"docClass\">Ext.ClassManager</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-create' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-create' class='name expandable'>create</a>( <span class='pre'>className, data, createdFn</span> )<strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>Defines a class. ...</div><div class='long'><p>Defines a class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead, as that also supports creating overrides.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>className</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>data</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>createdFn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-createNamespaces' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-createNamespaces' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-createNamespaces' class='name expandable'>createNamespaces</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>The new Ext.ns, supports namespace rewriting ...</div><div class='long'><p>The new <a href=\"#!/api/Ext-method-ns\" rel=\"Ext-method-ns\" class=\"docClass\">Ext.ns</a>, supports namespace rewriting</p>\n</div></div></div><div id='method-dynInstantiate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-dynInstantiate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-dynInstantiate' class='name expandable'>dynInstantiate</a>( <span class='pre'>name, args</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-get' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-get' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-get' class='name expandable'>get</a>( <span class='pre'>name</span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></div><div class='description'><div class='short'>Retrieve a class by its name. ...</div><div class='long'><p>Retrieve a class by its name.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'><p>class</p>\n</div></li></ul></div></div></div><div id='method-getAliasesByName' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-getAliasesByName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-getAliasesByName' class='name expandable'>getAliasesByName</a>( <span class='pre'>name</span> ) : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></div><div class='description'><div class='short'>Get the aliases of a class by the class name ...</div><div class='long'><p>Get the aliases of a class by the class name</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><div class='sub-desc'><p>aliases</p>\n</div></li></ul></div></div></div><div id='method-getByAlias' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-getByAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-getByAlias' class='name expandable'>getByAlias</a>( <span class='pre'>alias</span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></div><div class='description'><div class='short'>Get a reference to the class by its alias. ...</div><div class='long'><p>Get a reference to the class by its alias.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'><p>class</p>\n</div></li></ul></div></div></div><div id='method-getClass' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-getClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-getClass' class='name expandable'>getClass</a>( <span class='pre'>object</span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></div><div class='description'><div class='short'>Get the class of the provided object; returns null if it's not an instance\nof any class created with Ext.define. ...</div><div class='long'><p>Get the class of the provided object; returns null if it's not an instance\nof any class created with <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>.</p>\n\n<p><a href=\"#!/api/Ext.ClassManager-method-getClass\" rel=\"Ext.ClassManager-method-getClass\" class=\"docClass\">getClass</a> is usually invoked by the shorthand <a href=\"#!/api/Ext-method-getClass\" rel=\"Ext-method-getClass\" class=\"docClass\">Ext.getClass</a>.</p>\n\n<pre><code>var component = new <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>();\n\n<a href=\"#!/api/Ext-method-getClass\" rel=\"Ext-method-getClass\" class=\"docClass\">Ext.getClass</a>(component); // returns <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a>\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>object</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'><p>class</p>\n</div></li></ul></div></div></div><div id='method-getDisplayName' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-getDisplayName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-getDisplayName' class='name expandable'>getDisplayName</a>( <span class='pre'>object</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns the displayName property or className or object. ...</div><div class='long'><p>Returns the displayName property or className or object. When all else fails, returns \"Anonymous\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>object</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getInstantiator' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-getInstantiator' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-getInstantiator' class='name expandable'>getInstantiator</a>( <span class='pre'>length</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>length</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getName' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-getName' class='name expandable'>getName</a>( <span class='pre'>object</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Get the name of the class by its reference or its instance;\n\ngetName is usually invoked by the shorthand Ext.getClass...</div><div class='long'><p>Get the name of the class by its reference or its instance;</p>\n\n<p><a href=\"#!/api/Ext.ClassManager-method-getName\" rel=\"Ext.ClassManager-method-getName\" class=\"docClass\">getName</a> is usually invoked by the shorthand <a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>.</p>\n\n<pre><code>Ext.getName(<a href=\"#!/api/Ext.Action\" rel=\"Ext.Action\" class=\"docClass\">Ext.Action</a>); // returns \"<a href=\"#!/api/Ext.Action\" rel=\"Ext.Action\" class=\"docClass\">Ext.Action</a>\"\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>object</span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='method-getNameByAlias' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-getNameByAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-getNameByAlias' class='name expandable'>getNameByAlias</a>( <span class='pre'>alias</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Get the name of a class by its alias. ...</div><div class='long'><p>Get the name of a class by its alias.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='method-getNameByAlternate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-getNameByAlternate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-getNameByAlternate' class='name expandable'>getNameByAlternate</a>( <span class='pre'>alternate</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Get the name of a class by its alternate name. ...</div><div class='long'><p>Get the name of a class by its alternate name.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alternate</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='method-getNamesByExpression' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-getNamesByExpression' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-getNamesByExpression' class='name expandable'>getNamesByExpression</a>( <span class='pre'>expression</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</div><div class='description'><div class='short'>Converts a string expression to an array of matching class names. ...</div><div class='long'><p>Converts a string expression to an array of matching class names. An expression can either refers to class aliases\nor class names. Expressions support wildcards:</p>\n\n<pre><code> // returns ['<a href=\"#!/api/Ext.window.Window\" rel=\"Ext.window.Window\" class=\"docClass\">Ext.window.Window</a>']\nvar window = <a href=\"#!/api/Ext.ClassManager-method-getNamesByExpression\" rel=\"Ext.ClassManager-method-getNamesByExpression\" class=\"docClass\">Ext.ClassManager.getNamesByExpression</a>('widget.window');\n\n// returns ['widget.panel', 'widget.window', ...]\nvar allWidgets = <a href=\"#!/api/Ext.ClassManager-method-getNamesByExpression\" rel=\"Ext.ClassManager-method-getNamesByExpression\" class=\"docClass\">Ext.ClassManager.getNamesByExpression</a>('widget.*');\n\n// returns ['<a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a>', 'Ext.data.ArrayProxy', ...]\nvar allData = <a href=\"#!/api/Ext.ClassManager-method-getNamesByExpression\" rel=\"Ext.ClassManager-method-getNamesByExpression\" class=\"docClass\">Ext.ClassManager.getNamesByExpression</a>('Ext.data.*');\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>expression</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</span><div class='sub-desc'><p>classNames</p>\n</div></li></ul></div></div></div><div id='method-instantiate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-instantiate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-instantiate' class='name expandable'>instantiate</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-instantiateByAlias' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-instantiateByAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-instantiateByAlias' class='name expandable'>instantiateByAlias</a>( <span class='pre'>alias, args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Instantiate a class by its alias. ...</div><div class='long'><p>Instantiate a class by its alias.</p>\n\n<p><a href=\"#!/api/Ext.ClassManager-method-instantiateByAlias\" rel=\"Ext.ClassManager-method-instantiateByAlias\" class=\"docClass\">instantiateByAlias</a> is usually invoked by the shorthand <a href=\"#!/api/Ext-method-createByAlias\" rel=\"Ext-method-createByAlias\" class=\"docClass\">Ext.createByAlias</a>.</p>\n\n<p>If <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a> is <a href=\"#!/api/Ext.Loader-method-setConfig\" rel=\"Ext.Loader-method-setConfig\" class=\"docClass\">enabled</a> and the class has not been defined yet, it will\nattempt to load the class via synchronous loading.</p>\n\n<pre><code>var window = <a href=\"#!/api/Ext-method-createByAlias\" rel=\"Ext-method-createByAlias\" class=\"docClass\">Ext.createByAlias</a>('widget.window', { width: 600, height: 800, ... });\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>...<div class='sub-desc'><p>Additional arguments after the alias will be passed to the\nclass constructor.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>instance</p>\n</div></li></ul></div></div></div><div id='method-isCreated' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-isCreated' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-isCreated' class='name expandable'>isCreated</a>( <span class='pre'>className</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks if a class has already been created. ...</div><div class='long'><p>Checks if a class has already been created.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>className</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>exist</p>\n</div></li></ul></div></div></div><div id='method-onCreated' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-onCreated' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-onCreated' class='name expandable'>onCreated</a>( <span class='pre'>fn, scope, className</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>className</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-parseNamespace' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-parseNamespace' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-parseNamespace' class='name expandable'>parseNamespace</a>( <span class='pre'>namespace</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Supports namespace rewriting ...</div><div class='long'><p>Supports namespace rewriting</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>namespace</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-registerPostprocessor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-registerPostprocessor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-registerPostprocessor' class='name expandable'>registerPostprocessor</a>( <span class='pre'>name, postprocessor</span> ) : <a href=\"#!/api/Ext.ClassManager\" rel=\"Ext.ClassManager\" class=\"docClass\">Ext.ClassManager</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Register a post-processor function. ...</div><div class='long'><p>Register a post-processor function.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li><li><span class='pre'>postprocessor</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.ClassManager\" rel=\"Ext.ClassManager\" class=\"docClass\">Ext.ClassManager</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-set' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-set' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-set' class='name expandable'>set</a>( <span class='pre'>name, value</span> ) : <a href=\"#!/api/Ext.ClassManager\" rel=\"Ext.ClassManager\" class=\"docClass\">Ext.ClassManager</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Sets a name reference to a class. ...</div><div class='long'><p>Sets a name reference to a class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.ClassManager\" rel=\"Ext.ClassManager\" class=\"docClass\">Ext.ClassManager</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setAlias' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-setAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-setAlias' class='name expandable'>setAlias</a>( <span class='pre'>cls, alias</span> ) : <a href=\"#!/api/Ext.ClassManager\" rel=\"Ext.ClassManager\" class=\"docClass\">Ext.ClassManager</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Register the alias for a class. ...</div><div class='long'><p>Register the alias for a class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>cls</span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>a reference to a class or a className</p>\n</div></li><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>Alias to use when referring to this class</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.ClassManager\" rel=\"Ext.ClassManager\" class=\"docClass\">Ext.ClassManager</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setDefaultPostprocessorPosition' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-setDefaultPostprocessorPosition' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-setDefaultPostprocessorPosition' class='name expandable'>setDefaultPostprocessorPosition</a>( <span class='pre'>name, offset, relativeName</span> ) : <a href=\"#!/api/Ext.ClassManager\" rel=\"Ext.ClassManager\" class=\"docClass\">Ext.ClassManager</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Insert this post-processor at a specific position in the stack, optionally relative to\nany existing post-processor ...</div><div class='long'><p>Insert this post-processor at a specific position in the stack, optionally relative to\nany existing post-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The post-processor name. Note that it needs to be registered with\n<a href=\"#!/api/Ext.ClassManager-method-registerPostprocessor\" rel=\"Ext.ClassManager-method-registerPostprocessor\" class=\"docClass\">registerPostprocessor</a> before this</p>\n</div></li><li><span class='pre'>offset</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The insertion position. Four possible values are:\n'first', 'last', or: 'before', 'after' (relative to the name provided in the third argument)</p>\n</div></li><li><span class='pre'>relativeName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.ClassManager\" rel=\"Ext.ClassManager\" class=\"docClass\">Ext.ClassManager</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setDefaultPostprocessors' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-setDefaultPostprocessors' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-setDefaultPostprocessors' class='name expandable'>setDefaultPostprocessors</a>( <span class='pre'>postprocessors</span> ) : <a href=\"#!/api/Ext.ClassManager\" rel=\"Ext.ClassManager\" class=\"docClass\">Ext.ClassManager</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Set the default post processors array stack which are applied to every class. ...</div><div class='long'><p>Set the default post processors array stack which are applied to every class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>postprocessors</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The name of a registered post processor or an array of registered names.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.ClassManager\" rel=\"Ext.ClassManager\" class=\"docClass\">Ext.ClassManager</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setNamespace' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-setNamespace' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-setNamespace' class='name expandable'>setNamespace</a>( <span class='pre'>name, value</span> )</div><div class='description'><div class='short'>Creates a namespace and assign the value to the created object\n\nExt.ClassManager.setNamespace('MyCompany.pkg.Example'...</div><div class='long'><p>Creates a namespace and assign the <code>value</code> to the created object</p>\n\n<pre><code><a href=\"#!/api/Ext.ClassManager-method-setNamespace\" rel=\"Ext.ClassManager-method-setNamespace\" class=\"docClass\">Ext.ClassManager.setNamespace</a>('MyCompany.pkg.Example', someObject);\n\nalert(MyCompany.pkg.Example === someObject); // alerts true\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li><li><span class='pre'>value</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-triggerCreated' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-triggerCreated' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-triggerCreated' class='name expandable'>triggerCreated</a>( <span class='pre'>className</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>className</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-undefine' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.ClassManager'>Ext.ClassManager</span><br/><a href='source/ClassManager.html#Ext-ClassManager-method-undefine' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.ClassManager-method-undefine' class='name expandable'>undefine</a>( <span class='pre'>className</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Undefines a class defined using the #define method. ...</div><div class='long'><p>Undefines a class defined using the #define method. Typically used\nfor unit testing where setting up and tearing down a class multiple\ntimes is required.  For example:</p>\n\n<pre><code>// define a class\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Foo', {\n   ...\n});\n\n// run test\n\n// undefine the class\nExt.undefine('Foo');\n</code></pre>\n\n<p>@param {String} className The class name to undefine in string dot-namespaced format.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>className</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div></div></div></div></div>","superclasses":[],"meta":{"author":["Jacky Nguyen <<EMAIL>>"],"docauthor":["Jacky Nguyen <<EMAIL>>"]},"requires":[],"html_meta":{"author":null,"docauthor":null},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"ClassManager.html#Ext-ClassManager","filename":"ClassManager.js"}],"linenr":5,"members":{"property":[{"tagname":"property","owner":"Ext.ClassManager","meta":{"private":true},"name":"classes","id":"property-classes"},{"tagname":"property","owner":"Ext.ClassManager","meta":{"private":true},"name":"createdListeners","id":"property-createdListeners"},{"tagname":"property","owner":"Ext.ClassManager","meta":{"private":true},"name":"defaultPostprocessors","id":"property-defaultPostprocessors"},{"tagname":"property","owner":"Ext.ClassManager","meta":{"private":true},"name":"enableNamespaceParseCache","id":"property-enableNamespaceParseCache"},{"tagname":"property","owner":"Ext.ClassManager","meta":{"private":true},"name":"existCache","id":"property-existCache"},{"tagname":"property","owner":"Ext.ClassManager","meta":{"private":true},"name":"instantiators","id":"property-instantiators"},{"tagname":"property","owner":"Ext.ClassManager","meta":{"private":true},"name":"maps","id":"property-maps"},{"tagname":"property","owner":"Ext.ClassManager","meta":{"private":true},"name":"nameCreatedListeners","id":"property-nameCreatedListeners"},{"tagname":"property","owner":"Ext.ClassManager","meta":{"private":true},"name":"namespaceParseCache","id":"property-namespaceParseCache"},{"tagname":"property","owner":"Ext.ClassManager","meta":{"private":true},"name":"namespaceRewrites","id":"property-namespaceRewrites"},{"tagname":"property","owner":"Ext.ClassManager","meta":{"private":true},"name":"postprocessors","id":"property-postprocessors"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.ClassManager","meta":{"chainable":true},"name":"addNameAliasMappings","id":"method-addNameAliasMappings"},{"tagname":"method","owner":"Ext.ClassManager","meta":{"chainable":true},"name":"addNameAlternateMappings","id":"method-addNameAlternateMappings"},{"tagname":"method","owner":"Ext.ClassManager","meta":{"deprecated":{"text":"Use {@link Ext#define} instead, as that also supports creating overrides.","version":"4.1.0"}},"name":"create","id":"method-create"},{"tagname":"method","owner":"Ext.ClassManager","meta":{"private":true},"name":"createNamespaces","id":"method-createNamespaces"},{"tagname":"method","owner":"Ext.ClassManager","meta":{"private":true},"name":"dynInstantiate","id":"method-dynInstantiate"},{"tagname":"method","owner":"Ext.ClassManager","meta":{},"name":"get","id":"method-get"},{"tagname":"method","owner":"Ext.ClassManager","meta":{},"name":"getAliasesByName","id":"method-getAliasesByName"},{"tagname":"method","owner":"Ext.ClassManager","meta":{},"name":"getByAlias","id":"method-getByAlias"},{"tagname":"method","owner":"Ext.ClassManager","meta":{},"name":"getClass","id":"method-getClass"},{"tagname":"method","owner":"Ext.ClassManager","meta":{},"name":"getDisplayName","id":"method-getDisplayName"},{"tagname":"method","owner":"Ext.ClassManager","meta":{"private":true},"name":"getInstantiator","id":"method-getInstantiator"},{"tagname":"method","owner":"Ext.ClassManager","meta":{},"name":"getName","id":"method-getName"},{"tagname":"method","owner":"Ext.ClassManager","meta":{},"name":"getNameByAlias","id":"method-getNameByAlias"},{"tagname":"method","owner":"Ext.ClassManager","meta":{},"name":"getNameByAlternate","id":"method-getNameByAlternate"},{"tagname":"method","owner":"Ext.ClassManager","meta":{},"name":"getNamesByExpression","id":"method-getNamesByExpression"},{"tagname":"method","owner":"Ext.ClassManager","meta":{"private":true},"name":"instantiate","id":"method-instantiate"},{"tagname":"method","owner":"Ext.ClassManager","meta":{},"name":"instantiateByAlias","id":"method-instantiateByAlias"},{"tagname":"method","owner":"Ext.ClassManager","meta":{},"name":"isCreated","id":"method-isCreated"},{"tagname":"method","owner":"Ext.ClassManager","meta":{"private":true},"name":"onCreated","id":"method-onCreated"},{"tagname":"method","owner":"Ext.ClassManager","meta":{"private":true},"name":"parseNamespace","id":"method-parseNamespace"},{"tagname":"method","owner":"Ext.ClassManager","meta":{"chainable":true,"private":true},"name":"registerPostprocessor","id":"method-registerPostprocessor"},{"tagname":"method","owner":"Ext.ClassManager","meta":{"chainable":true},"name":"set","id":"method-set"},{"tagname":"method","owner":"Ext.ClassManager","meta":{"chainable":true},"name":"setAlias","id":"method-setAlias"},{"tagname":"method","owner":"Ext.ClassManager","meta":{"chainable":true,"private":true},"name":"setDefaultPostprocessorPosition","id":"method-setDefaultPostprocessorPosition"},{"tagname":"method","owner":"Ext.ClassManager","meta":{"chainable":true,"private":true},"name":"setDefaultPostprocessors","id":"method-setDefaultPostprocessors"},{"tagname":"method","owner":"Ext.ClassManager","meta":{},"name":"setNamespace","id":"method-setNamespace"},{"tagname":"method","owner":"Ext.ClassManager","meta":{"private":true},"name":"triggerCreated","id":"method-triggerCreated"},{"tagname":"method","owner":"Ext.ClassManager","meta":{"private":true},"name":"undefine","id":"method-undefine"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.ClassManager","singleton":true,"override":null,"inheritdoc":null,"id":"class-Ext.ClassManager","mixins":[],"mixedInto":[]});