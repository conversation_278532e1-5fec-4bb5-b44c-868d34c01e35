Ext.data.JsonP.Ext_Loader({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Loader.html#Ext-Loader' target='_blank'>Loader.js</a></div></pre><div class='doc-contents'><p><a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a> is the heart of the new dynamic dependency loading capability in Ext JS 4+. It is most commonly used\nvia the <a href=\"#!/api/Ext-method-require\" rel=\"Ext-method-require\" class=\"docClass\">Ext.require</a> shorthand. <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a> supports both asynchronous and synchronous loading\napproaches, and leverage their advantages for the best development flow. We'll discuss about the pros and cons of each approach:</p>\n\n<h1>Asynchronous Loading</h1>\n\n<ul>\n<li><p>Advantages:</p>\n\n<ul>\n<li>Cross-domain</li>\n<li>No web server needed: you can run the application via the file system protocol (i.e: <code>file://path/to/your/index\n.html</code>)</li>\n<li>Best possible debugging experience: error messages come with the exact file name and line number</li>\n</ul>\n</li>\n<li><p>Disadvantages:</p>\n\n<ul>\n<li>Dependencies need to be specified before-hand</li>\n</ul>\n</li>\n</ul>\n\n\n<h3>Method 1: Explicitly include what you need:</h3>\n\n<pre><code>// Syntax\n<a href=\"#!/api/Ext-method-require\" rel=\"Ext-method-require\" class=\"docClass\">Ext.require</a>({String/Array} expressions);\n\n// Example: Single alias\n<a href=\"#!/api/Ext-method-require\" rel=\"Ext-method-require\" class=\"docClass\">Ext.require</a>('widget.window');\n\n// Example: Single class name\n<a href=\"#!/api/Ext-method-require\" rel=\"Ext-method-require\" class=\"docClass\">Ext.require</a>('<a href=\"#!/api/Ext.window.Window\" rel=\"Ext.window.Window\" class=\"docClass\">Ext.window.Window</a>');\n\n// Example: Multiple aliases / class names mix\n<a href=\"#!/api/Ext-method-require\" rel=\"Ext-method-require\" class=\"docClass\">Ext.require</a>(['widget.window', 'layout.border', '<a href=\"#!/api/Ext.data.Connection\" rel=\"Ext.data.Connection\" class=\"docClass\">Ext.data.Connection</a>']);\n\n// Wildcards\n<a href=\"#!/api/Ext-method-require\" rel=\"Ext-method-require\" class=\"docClass\">Ext.require</a>(['widget.*', 'layout.*', 'Ext.data.*']);\n</code></pre>\n\n<h3>Method 2: Explicitly exclude what you don't need:</h3>\n\n<pre><code>// Syntax: Note that it must be in this chaining format.\n<a href=\"#!/api/Ext-method-exclude\" rel=\"Ext-method-exclude\" class=\"docClass\">Ext.exclude</a>({String/Array} expressions)\n   .require({String/Array} expressions);\n\n// Include everything except Ext.data.*\n<a href=\"#!/api/Ext-method-exclude\" rel=\"Ext-method-exclude\" class=\"docClass\">Ext.exclude</a>('Ext.data.*').require('*');\n\n// Include all widgets except widget.checkbox*,\n// which will match widget.checkbox, widget.checkboxfield, widget.checkboxgroup, etc.\n<a href=\"#!/api/Ext-method-exclude\" rel=\"Ext-method-exclude\" class=\"docClass\">Ext.exclude</a>('widget.checkbox*').require('widget.*');\n</code></pre>\n\n<h1>Synchronous Loading on Demand</h1>\n\n<ul>\n<li><p>Advantages:</p>\n\n<ul>\n<li>There's no need to specify dependencies before-hand, which is always the convenience of including ext-all.js\nbefore</li>\n</ul>\n</li>\n<li><p>Disadvantages:</p>\n\n<ul>\n<li>Not as good debugging experience since file name won't be shown (except in Firebug at the moment)</li>\n<li>Must be from the same domain due to XHR restriction</li>\n<li>Need a web server, same reason as above</li>\n</ul>\n</li>\n</ul>\n\n\n<p>There's one simple rule to follow: Instantiate everything with <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a> instead of the <code>new</code> keyword</p>\n\n<pre><code><a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('widget.window', { ... }); // Instead of new <a href=\"#!/api/Ext.window.Window\" rel=\"Ext.window.Window\" class=\"docClass\">Ext.window.Window</a>({...});\n\n<a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.window.Window\" rel=\"Ext.window.Window\" class=\"docClass\">Ext.window.Window</a>', {}); // Same as above, using full class name instead of alias\n\n<a href=\"#!/api/Ext-method-widget\" rel=\"Ext-method-widget\" class=\"docClass\">Ext.widget</a>('window', {}); // Same as above, all you need is the traditional `xtype`\n</code></pre>\n\n<p>Behind the scene, <a href=\"#!/api/Ext.ClassManager\" rel=\"Ext.ClassManager\" class=\"docClass\">Ext.ClassManager</a> will automatically check whether the given class name / alias has already\n existed on the page. If it's not, <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a> will immediately switch itself to synchronous mode and automatic load the given\n class and all its dependencies.</p>\n\n<h1>Hybrid Loading - The Best of Both Worlds</h1>\n\n<p>It has all the advantages combined from asynchronous and synchronous loading. The development flow is simple:</p>\n\n<h3>Step 1: Start writing your application using synchronous approach.</h3>\n\n<p><a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a> will automatically fetch all dependencies on demand as they're needed during run-time. For example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-onReady\" rel=\"Ext-method-onReady\" class=\"docClass\">Ext.onReady</a>(function(){\n    var window = <a href=\"#!/api/Ext-method-widget\" rel=\"Ext-method-widget\" class=\"docClass\">Ext.widget</a>('window', {\n        width: 500,\n        height: 300,\n        layout: {\n            type: 'border',\n            padding: 5\n        },\n        title: 'Hello Dialog',\n        items: [{\n            title: 'Navigation',\n            collapsible: true,\n            region: 'west',\n            width: 200,\n            html: 'Hello',\n            split: true\n        }, {\n            title: 'TabPanel',\n            region: 'center'\n        }]\n    });\n\n    window.show();\n})\n</code></pre>\n\n<h3>Step 2: Along the way, when you need better debugging ability, watch the console for warnings like these:</h3>\n\n<pre><code>[<a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>] Synchronously loading '<a href=\"#!/api/Ext.window.Window\" rel=\"Ext.window.Window\" class=\"docClass\">Ext.window.Window</a>'; consider adding <a href=\"#!/api/Ext-method-require\" rel=\"Ext-method-require\" class=\"docClass\">Ext.require</a>('<a href=\"#!/api/Ext.window.Window\" rel=\"Ext.window.Window\" class=\"docClass\">Ext.window.Window</a>') before your application's code\nClassManager.js:432\n[<a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>] Synchronously loading '<a href=\"#!/api/Ext.layout.container.Border\" rel=\"Ext.layout.container.Border\" class=\"docClass\">Ext.layout.container.Border</a>'; consider adding <a href=\"#!/api/Ext-method-require\" rel=\"Ext-method-require\" class=\"docClass\">Ext.require</a>('<a href=\"#!/api/Ext.layout.container.Border\" rel=\"Ext.layout.container.Border\" class=\"docClass\">Ext.layout.container.Border</a>') before your application's code\n</code></pre>\n\n<p>Simply copy and paste the suggested code above <code><a href=\"#!/api/Ext-method-onReady\" rel=\"Ext-method-onReady\" class=\"docClass\">Ext.onReady</a></code>, i.e:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-require\" rel=\"Ext-method-require\" class=\"docClass\">Ext.require</a>('<a href=\"#!/api/Ext.window.Window\" rel=\"Ext.window.Window\" class=\"docClass\">Ext.window.Window</a>');\n<a href=\"#!/api/Ext-method-require\" rel=\"Ext-method-require\" class=\"docClass\">Ext.require</a>('<a href=\"#!/api/Ext.layout.container.Border\" rel=\"Ext.layout.container.Border\" class=\"docClass\">Ext.layout.container.Border</a>');\n\n<a href=\"#!/api/Ext-method-onReady\" rel=\"Ext-method-onReady\" class=\"docClass\">Ext.onReady</a>(...);\n</code></pre>\n\n<p>Everything should now load via asynchronous mode.</p>\n\n<h1>Deployment</h1>\n\n<p>It's important to note that dynamic loading should only be used during development on your local machines.\nDuring production, all dependencies should be combined into one single JavaScript file. <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a> makes\nthe whole process of transitioning from / to between development / maintenance and production as easy as\npossible. Internally <a href=\"#!/api/Ext.Loader-property-history\" rel=\"Ext.Loader-property-history\" class=\"docClass\">Ext.Loader.history</a> maintains the list of all dependencies your application\nneeds in the exact loading sequence. It's as simple as concatenating all files in this array into one,\nthen include it on top of your application.</p>\n\n<p>This process will be automated with Sencha Command, to be released and documented towards Ext JS 4 Final.</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-cfg'>Config options</h3><div class='subsection'><div id='cfg-disableCaching' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-cfg-disableCaching' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-cfg-disableCaching' class='name expandable'>disableCaching</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Appends current timestamp to script files to prevent caching. ...</div><div class='long'><p>Appends current timestamp to script files to prevent caching.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-disableCachingParam' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-cfg-disableCachingParam' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-cfg-disableCachingParam' class='name expandable'>disableCachingParam</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The get parameter name for the cache buster's timestamp. ...</div><div class='long'><p>The get parameter name for the cache buster's timestamp.</p>\n<p>Defaults to: <code>'_dc'</code></p></div></div></div><div id='cfg-enabled' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-cfg-enabled' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-cfg-enabled' class='name expandable'>enabled</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Whether or not to enable the dynamic dependency loading feature. ...</div><div class='long'><p>Whether or not to enable the dynamic dependency loading feature.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-garbageCollect' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-cfg-garbageCollect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-cfg-garbageCollect' class='name expandable'>garbageCollect</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True to prepare an asynchronous script tag for garbage collection (effective only\nif preserveScripts is false) ...</div><div class='long'><p>True to prepare an asynchronous script tag for garbage collection (effective only\nif <a href=\"#!/api/Ext.Loader-cfg-preserveScripts\" rel=\"Ext.Loader-cfg-preserveScripts\" class=\"docClass\">preserveScripts</a> is false)</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-paths' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-cfg-paths' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-cfg-paths' class='name expandable'>paths</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>The mapping from namespaces to file paths\n\n{\n    'Ext': '.', // This is set by default, Ext.layout.container.Containe...</div><div class='long'><p>The mapping from namespaces to file paths</p>\n\n<pre><code>{\n    'Ext': '.', // This is set by default, <a href=\"#!/api/Ext.layout.container.Container\" rel=\"Ext.layout.container.Container\" class=\"docClass\">Ext.layout.container.Container</a> will be\n                // loaded from ./layout/Container.js\n\n    'My': './src/my_own_folder' // My.layout.Container will be loaded from\n                                // ./src/my_own_folder/layout/Container.js\n}\n</code></pre>\n\n<p>Note that all relative paths are relative to the current HTML document.\nIf not being specified, for example, <code>Other.awesome.Class</code>\nwill simply be loaded from <code>./Other/awesome/Class.js</code></p>\n<p>Defaults to: <code>{'Ext': '.'}</code></p></div></div></div><div id='cfg-preserveScripts' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-cfg-preserveScripts' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-cfg-preserveScripts' class='name expandable'>preserveScripts</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>False to remove and optionally garbage-collect asynchronously loaded scripts,\nTrue to retain script element for brows...</div><div class='long'><p>False to remove and optionally <a href=\"#!/api/Ext.Loader-cfg-garbageCollect\" rel=\"Ext.Loader-cfg-garbageCollect\" class=\"docClass\">garbage-collect</a> asynchronously loaded scripts,\nTrue to retain script element for browser debugger compatibility and improved load performance.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-scriptChainDelay' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-cfg-scriptChainDelay' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-cfg-scriptChainDelay' class='name expandable'>scriptChainDelay</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>millisecond delay between asynchronous script injection (prevents stack overflow on some user agents)\n'false' disable...</div><div class='long'><p>millisecond delay between asynchronous script injection (prevents stack overflow on some user agents)\n'false' disables delay but potentially increases stack load.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-scriptCharset' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-cfg-scriptCharset' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-cfg-scriptCharset' class='name not-expandable'>scriptCharset</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'><p>Optional charset to specify encoding of dynamic script content.</p>\n</div><div class='long'><p>Optional charset to specify encoding of dynamic script content.</p>\n</div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-classNameToFilePathMap' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-property-classNameToFilePathMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-property-classNameToFilePathMap' class='name not-expandable'>classNameToFilePathMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-config' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-property-config' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-property-config' class='name not-expandable'>config</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'><p>Configuration</p>\n</div><div class='long'><p>Configuration</p>\n</div></div></div><div id='property-documentHead' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-property-documentHead' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-property-documentHead' class='name not-expandable'>documentHead</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-hasFileLoadError' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-property-hasFileLoadError' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-property-hasFileLoadError' class='name expandable'>hasFileLoadError</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-history' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-property-history' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-property-history' class='name expandable'>history</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span></div><div class='description'><div class='short'>An array of class names to keep track of the dependency loading order. ...</div><div class='long'><p>An array of class names to keep track of the dependency loading order.\nThis is not guaranteed to be the same everytime due to the asynchronous\nnature of the Loader.</p>\n</div></div></div><div id='property-isClassFileLoaded' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-property-isClassFileLoaded' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-property-isClassFileLoaded' class='name not-expandable'>isClassFileLoaded</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'><p>Maintain the list of files that have already been handled so that they never get double-loaded</p>\n</div><div class='long'><p>Maintain the list of files that have already been handled so that they never get double-loaded</p>\n</div></div></div><div id='property-isFileLoaded' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-property-isFileLoaded' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-property-isFileLoaded' class='name not-expandable'>isFileLoaded</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-isInHistory' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-property-isInHistory' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-property-isInHistory' class='name not-expandable'>isInHistory</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-isLoading' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-property-isLoading' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-property-isLoading' class='name expandable'>isLoading</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Flag indicating whether there are still files being loaded ...</div><div class='long'><p>Flag indicating whether there are still files being loaded</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-numLoadedFiles' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-property-numLoadedFiles' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-property-numLoadedFiles' class='name expandable'>numLoadedFiles</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-numPendingFiles' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-property-numPendingFiles' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-property-numPendingFiles' class='name expandable'>numPendingFiles</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-optionalRequires' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-property-optionalRequires' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-property-optionalRequires' class='name not-expandable'>optionalRequires</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'><p>Contains classes referenced in <code>uses</code> properties.</p>\n</div><div class='long'><p>Contains classes referenced in <code>uses</code> properties.</p>\n</div></div></div><div id='property-queue' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-property-queue' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-property-queue' class='name expandable'>queue</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Maintain the queue for all dependencies. ...</div><div class='long'><p>Maintain the queue for all dependencies. Each item in the array is an object of the format:</p>\n\n<pre><code>{\n     requires: [...], // The required classes for this queue item\n     callback: function() { ... } // The function to execute when all classes specified in requires exist\n}\n</code></pre>\n</div></div></div><div id='property-readyListeners' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-property-readyListeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-property-readyListeners' class='name not-expandable'>readyListeners</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'><p>Maintain the list of listeners to execute when all required scripts are fully loaded</p>\n</div><div class='long'><p>Maintain the list of listeners to execute when all required scripts are fully loaded</p>\n</div></div></div><div id='property-requiresMap' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-property-requiresMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-property-requiresMap' class='name not-expandable'>requiresMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'><p>Map of fully qualified class names to an array of dependent classes.</p>\n</div><div class='long'><p>Map of fully qualified class names to an array of dependent classes.</p>\n</div></div></div><div id='property-scriptsLoading' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-property-scriptsLoading' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-property-scriptsLoading' class='name expandable'>scriptsLoading</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>The number of scripts loading via loadScript. ...</div><div class='long'><p>The number of scripts loading via loadScript.</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-syncModeEnabled' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-property-syncModeEnabled' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-property-syncModeEnabled' class='name expandable'>syncModeEnabled</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>false</code></p></div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div id='method-addClassPathMappings' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-addClassPathMappings' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-addClassPathMappings' class='name expandable'>addClassPathMappings</a>( <span class='pre'>paths</span> ) : <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Sets a batch of path entries ...</div><div class='long'><p>Sets a batch of path entries</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>paths</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> <div class='sub-desc'><p>a set of className: path mappings</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-addUsedClasses' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-addUsedClasses' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-addUsedClasses' class='name expandable'>addUsedClasses</a>( <span class='pre'>classes</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Ensure that any classes referenced in the uses property are loaded. ...</div><div class='long'><p>Ensure that any classes referenced in the <code>uses</code> property are loaded.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>classes</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-cleanupScriptElement' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-cleanupScriptElement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-cleanupScriptElement' class='name expandable'>cleanupScriptElement</a>( <span class='pre'>script, remove, collect</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>script</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>remove</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>collect</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-disableCacheBuster' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-disableCacheBuster' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-disableCacheBuster' class='name expandable'>disableCacheBuster</a>( <span class='pre'>disable, [path]</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Turns on or off the \"cache buster\" applied to dynamically loaded scripts. ...</div><div class='long'><p>Turns on or off the \"cache buster\" applied to dynamically loaded scripts. Normally\ndynamically loaded scripts have an extra query parameter appended to avoid stale\ncached scripts. This method can be used to disable this mechanism, and is primarily\nuseful for testing. This is done using a cookie.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>disable</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to disable the cache buster.</p>\n</div></li><li><span class='pre'>path</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>An optional path to scope the cookie.</p>\n<p>Defaults to: <code>&quot;/&quot;</code></p></div></li></ul></div></div></div><div id='method-exclude' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-exclude' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-exclude' class='name expandable'>exclude</a>( <span class='pre'>excludes</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Explicitly exclude files from being loaded. ...</div><div class='long'><p>Explicitly exclude files from being loaded. Useful when used in conjunction with a broad include expression.\nCan be chained with more <code>require</code> and <code>exclude</code> methods, eg:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-exclude\" rel=\"Ext-method-exclude\" class=\"docClass\">Ext.exclude</a>('Ext.data.*').require('*');\n\n<a href=\"#!/api/Ext-method-exclude\" rel=\"Ext-method-exclude\" class=\"docClass\">Ext.exclude</a>('widget.button*').require('widget.*');\n</code></pre>\n\n<p><a href=\"#!/api/Ext-method-exclude\" rel=\"Ext-method-exclude\" class=\"docClass\">Ext.exclude</a> is alias for <a href=\"#!/api/Ext.Loader-method-exclude\" rel=\"Ext.Loader-method-exclude\" class=\"docClass\">exclude</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>excludes</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>object contains <code>require</code> method for chaining</p>\n</div></li></ul></div></div></div><div id='method-getConfig' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Get the config value corresponding to the specified name. ...</div><div class='long'><p>Get the config value corresponding to the specified name. If no name is given, will return the config object</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The config property name</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getPath' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-getPath' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-getPath' class='name expandable'>getPath</a>( <span class='pre'>className</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Translates a className to a file path by adding the\nthe proper prefix and converting the .'s to /'s. ...</div><div class='long'><p>Translates a className to a file path by adding the\nthe proper prefix and converting the .'s to /'s. For example:</p>\n\n<pre><code><a href=\"#!/api/Ext.Loader-method-setPath\" rel=\"Ext.Loader-method-setPath\" class=\"docClass\">Ext.Loader.setPath</a>('My', '/path/to/My');\n\nalert(<a href=\"#!/api/Ext.Loader-method-getPath\" rel=\"Ext.Loader-method-getPath\" class=\"docClass\">Ext.Loader.getPath</a>('My.awesome.Class')); // alerts '/path/to/My/awesome/Class.js'\n</code></pre>\n\n<p>Note that the deeper namespace levels, if explicitly set, are always resolved first. For example:</p>\n\n<pre><code><a href=\"#!/api/Ext.Loader-method-setPath\" rel=\"Ext.Loader-method-setPath\" class=\"docClass\">Ext.Loader.setPath</a>({\n    'My': '/path/to/lib',\n    'My.awesome': '/other/path/for/awesome/stuff',\n    'My.awesome.more': '/more/awesome/path'\n});\n\nalert(<a href=\"#!/api/Ext.Loader-method-getPath\" rel=\"Ext.Loader-method-getPath\" class=\"docClass\">Ext.Loader.getPath</a>('My.awesome.Class')); // alerts '/other/path/for/awesome/stuff/Class.js'\n\nalert(<a href=\"#!/api/Ext.Loader-method-getPath\" rel=\"Ext.Loader-method-getPath\" class=\"docClass\">Ext.Loader.getPath</a>('My.awesome.more.Class')); // alerts '/more/awesome/path/Class.js'\n\nalert(<a href=\"#!/api/Ext.Loader-method-getPath\" rel=\"Ext.Loader-method-getPath\" class=\"docClass\">Ext.Loader.getPath</a>('My.cool.Class')); // alerts '/path/to/lib/cool/Class.js'\n\nalert(<a href=\"#!/api/Ext.Loader-method-getPath\" rel=\"Ext.Loader-method-getPath\" class=\"docClass\">Ext.Loader.getPath</a>('Unknown.strange.Stuff')); // alerts 'Unknown/strange/Stuff.js'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>className</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>path</p>\n</div></li></ul></div></div></div><div id='method-getPrefix' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-getPrefix' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-getPrefix' class='name expandable'>getPrefix</a>( <span class='pre'>className</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>className</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-historyPush' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-historyPush' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-historyPush' class='name expandable'>historyPush</a>( <span class='pre'>className</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>className</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-injectScriptElement' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-injectScriptElement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-injectScriptElement' class='name expandable'>injectScriptElement</a>( <span class='pre'>url, onLoad, onError, scope, charset</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Inject a script element to document's head, call onLoad and onError accordingly ...</div><div class='long'><p>Inject a script element to document's head, call onLoad and onError accordingly</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>url</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>onLoad</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>onError</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>charset</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isAClassNameWithAKnownPrefix' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-isAClassNameWithAKnownPrefix' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-isAClassNameWithAKnownPrefix' class='name expandable'>isAClassNameWithAKnownPrefix</a>( <span class='pre'>className</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>className</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-loadScript' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-loadScript' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-loadScript' class='name expandable'>loadScript</a>( <span class='pre'>options</span> )</div><div class='description'><div class='short'>Loads the specified script URL and calls the supplied callbacks. ...</div><div class='long'><p>Loads the specified script URL and calls the supplied callbacks. If this method\nis called before <a href=\"#!/api/Ext-property-isReady\" rel=\"Ext-property-isReady\" class=\"docClass\">Ext.isReady</a>, the script's load will delay the transition\nto ready. This can be used to load arbitrary scripts that may contain further\n<a href=\"#!/api/Ext-method-require\" rel=\"Ext-method-require\" class=\"docClass\">Ext.require</a> calls.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The options object or simply the URL to load.</p>\n<ul><li><span class='pre'>url</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The URL from which to load the script.</p>\n</div></li><li><span class='pre'>onLoad</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The callback to call on successful load.</p>\n</div></li><li><span class='pre'>onError</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The callback to call on failure to load.</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code>) for the supplied callbacks.</p>\n</div></li></ul></div></li></ul></div></div></div><div id='method-loadScriptFile' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-loadScriptFile' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-loadScriptFile' class='name expandable'>loadScriptFile</a>( <span class='pre'>url, onLoad, onError, scope, synchronous</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Load a script file, supports both asynchronous and synchronous approaches ...</div><div class='long'><p>Load a script file, supports both asynchronous and synchronous approaches</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>url</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>onLoad</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>onError</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>synchronous</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onFileLoadError' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-onFileLoadError' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-onFileLoadError' class='name expandable'>onFileLoadError</a>( <span class='pre'>className, filePath, errorMessage, isSynchronous</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>className</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>filePath</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>errorMessage</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>isSynchronous</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onFileLoaded' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-onFileLoaded' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-onFileLoaded' class='name expandable'>onFileLoaded</a>( <span class='pre'>className, filePath</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>className</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li><li><span class='pre'>filePath</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onReady' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-onReady' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-onReady' class='name expandable'>onReady</a>( <span class='pre'>fn, scope, withDomReady</span> )</div><div class='description'><div class='short'>Add a new listener to be executed when all required scripts are fully loaded ...</div><div class='long'><p>Add a new listener to be executed when all required scripts are fully loaded</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The function callback to be executed</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The execution scope (<code>this</code>) of the callback function</p>\n</div></li><li><span class='pre'>withDomReady</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>Whether or not to wait for document dom ready as well</p>\n</div></li></ul></div></div></div><div id='method-refreshQueue' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-refreshQueue' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-refreshQueue' class='name expandable'>refreshQueue</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Refresh all items in the queue. ...</div><div class='long'><p>Refresh all items in the queue. If all dependencies for an item exist during looping,\nit will execute the callback and call refreshQueue again. Triggers onReady when the queue is\nempty</p>\n</div></div></div><div id='method-removeScriptElement' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-removeScriptElement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-removeScriptElement' class='name expandable'>removeScriptElement</a>( <span class='pre'>url</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>url</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-require' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-require' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-require' class='name expandable'>require</a>( <span class='pre'>expressions, [fn], [scope], [excludes]</span> )</div><div class='description'><div class='short'>Loads all classes by the given names and all their direct dependencies; optionally executes\nthe given callback functi...</div><div class='long'><p>Loads all classes by the given names and all their direct dependencies; optionally executes\nthe given callback function when finishes, within the optional scope.</p>\n\n<p><a href=\"#!/api/Ext-method-require\" rel=\"Ext-method-require\" class=\"docClass\">Ext.require</a> is alias for <a href=\"#!/api/Ext.Loader-method-require\" rel=\"Ext.Loader-method-require\" class=\"docClass\">require</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>expressions</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>Can either be a string or an array of string</p>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The callback function</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The execution scope (<code>this</code>) of the callback function</p>\n</div></li><li><span class='pre'>excludes</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> (optional)<div class='sub-desc'><p>Classes to be excluded, useful when being used with expressions</p>\n</div></li></ul></div></div></div><div id='method-setConfig' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Set the configuration for the loader. ...</div><div class='long'><p>Set the configuration for the loader. This should be called right after ext-(debug).js\nis included in the page, and before <a href=\"#!/api/Ext-method-onReady\" rel=\"Ext-method-onReady\" class=\"docClass\">Ext.onReady</a>. i.e:</p>\n\n<pre><code>&lt;script type=\"text/javascript\" src=\"ext-core-debug.js\"&gt;&lt;/script&gt;\n&lt;script type=\"text/javascript\"&gt;\n    <a href=\"#!/api/Ext.Loader-method-setConfig\" rel=\"Ext.Loader-method-setConfig\" class=\"docClass\">Ext.Loader.setConfig</a>({\n      enabled: true,\n      paths: {\n          'My': 'my_own_path'\n      }\n    });\n&lt;/script&gt;\n&lt;script type=\"text/javascript\"&gt;\n    <a href=\"#!/api/Ext-method-require\" rel=\"Ext-method-require\" class=\"docClass\">Ext.require</a>(...);\n\n    <a href=\"#!/api/Ext-method-onReady\" rel=\"Ext-method-onReady\" class=\"docClass\">Ext.onReady</a>(function() {\n      // application code here\n    });\n&lt;/script&gt;\n</code></pre>\n\n<p>Refer to config options of <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a> for the list of possible properties</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The config object to override the default values</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setPath' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-setPath' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-setPath' class='name expandable'>setPath</a>( <span class='pre'>name, [path]</span> ) : <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Sets the path of a namespace. ...</div><div class='long'><p>Sets the path of a namespace.\nFor Example:</p>\n\n<pre><code><a href=\"#!/api/Ext.Loader-method-setPath\" rel=\"Ext.Loader-method-setPath\" class=\"docClass\">Ext.Loader.setPath</a>('Ext', '.');\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>See <a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>path</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>See <a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-syncRequire' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-syncRequire' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-syncRequire' class='name expandable'>syncRequire</a>( <span class='pre'>expressions, [fn], [scope], [excludes]</span> )</div><div class='description'><div class='short'>Synchronously loads all classes by the given names and all their direct dependencies; optionally\nexecutes the given c...</div><div class='long'><p>Synchronously loads all classes by the given names and all their direct dependencies; optionally\nexecutes the given callback function when finishes, within the optional scope.</p>\n\n<p><a href=\"#!/api/Ext-method-syncRequire\" rel=\"Ext-method-syncRequire\" class=\"docClass\">Ext.syncRequire</a> is alias for <a href=\"#!/api/Ext.Loader-method-syncRequire\" rel=\"Ext.Loader-method-syncRequire\" class=\"docClass\">syncRequire</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>expressions</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>Can either be a string or an array of string</p>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The callback function</p>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The execution scope (<code>this</code>) of the callback function</p>\n</div></li><li><span class='pre'>excludes</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> (optional)<div class='sub-desc'><p>Classes to be excluded, useful when being used with expressions</p>\n</div></li></ul></div></div></div><div id='method-triggerReady' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.Loader'>Ext.Loader</span><br/><a href='source/Loader.html#Ext-Loader-method-triggerReady' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Loader-method-triggerReady' class='name expandable'>triggerReady</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div></div></div>","superclasses":[],"meta":{"author":["Jacky Nguyen <<EMAIL>>"],"docauthor":["Jacky Nguyen <<EMAIL>>"]},"requires":[],"html_meta":{"author":null,"docauthor":null},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"Loader.html#Ext-Loader","filename":"Loader.js"}],"linenr":5,"members":{"property":[{"tagname":"property","owner":"Ext.Loader","meta":{"private":true},"name":"classNameToFilePathMap","id":"property-classNameToFilePathMap"},{"tagname":"property","owner":"Ext.Loader","meta":{"private":true},"name":"config","id":"property-config"},{"tagname":"property","owner":"Ext.Loader","meta":{"private":true},"name":"documentHead","id":"property-documentHead"},{"tagname":"property","owner":"Ext.Loader","meta":{"private":true},"name":"hasFileLoadError","id":"property-hasFileLoadError"},{"tagname":"property","owner":"Ext.Loader","meta":{},"name":"history","id":"property-history"},{"tagname":"property","owner":"Ext.Loader","meta":{"private":true},"name":"isClassFileLoaded","id":"property-isClassFileLoaded"},{"tagname":"property","owner":"Ext.Loader","meta":{"private":true},"name":"isFileLoaded","id":"property-isFileLoaded"},{"tagname":"property","owner":"Ext.Loader","meta":{"private":true},"name":"isInHistory","id":"property-isInHistory"},{"tagname":"property","owner":"Ext.Loader","meta":{"private":true},"name":"isLoading","id":"property-isLoading"},{"tagname":"property","owner":"Ext.Loader","meta":{"private":true},"name":"numLoadedFiles","id":"property-numLoadedFiles"},{"tagname":"property","owner":"Ext.Loader","meta":{"private":true},"name":"numPendingFiles","id":"property-numPendingFiles"},{"tagname":"property","owner":"Ext.Loader","meta":{"private":true},"name":"optionalRequires","id":"property-optionalRequires"},{"tagname":"property","owner":"Ext.Loader","meta":{"private":true},"name":"queue","id":"property-queue"},{"tagname":"property","owner":"Ext.Loader","meta":{"private":true},"name":"readyListeners","id":"property-readyListeners"},{"tagname":"property","owner":"Ext.Loader","meta":{"private":true},"name":"requiresMap","id":"property-requiresMap"},{"tagname":"property","owner":"Ext.Loader","meta":{"private":true},"name":"scriptsLoading","id":"property-scriptsLoading"},{"tagname":"property","owner":"Ext.Loader","meta":{"private":true},"name":"syncModeEnabled","id":"property-syncModeEnabled"}],"cfg":[{"tagname":"cfg","owner":"Ext.Loader","meta":{},"name":"disableCaching","id":"cfg-disableCaching"},{"tagname":"cfg","owner":"Ext.Loader","meta":{},"name":"disableCachingParam","id":"cfg-disableCachingParam"},{"tagname":"cfg","owner":"Ext.Loader","meta":{},"name":"enabled","id":"cfg-enabled"},{"tagname":"cfg","owner":"Ext.Loader","meta":{},"name":"garbageCollect","id":"cfg-garbageCollect"},{"tagname":"cfg","owner":"Ext.Loader","meta":{},"name":"paths","id":"cfg-paths"},{"tagname":"cfg","owner":"Ext.Loader","meta":{},"name":"preserveScripts","id":"cfg-preserveScripts"},{"tagname":"cfg","owner":"Ext.Loader","meta":{},"name":"scriptChainDelay","id":"cfg-scriptChainDelay"},{"tagname":"cfg","owner":"Ext.Loader","meta":{},"name":"scriptCharset","id":"cfg-scriptCharset"}],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Loader","meta":{"chainable":true},"name":"addClassPathMappings","id":"method-addClassPathMappings"},{"tagname":"method","owner":"Ext.Loader","meta":{"private":true},"name":"addUsedClasses","id":"method-addUsedClasses"},{"tagname":"method","owner":"Ext.Loader","meta":{"private":true},"name":"cleanupScriptElement","id":"method-cleanupScriptElement"},{"tagname":"method","owner":"Ext.Loader","meta":{"private":true},"name":"disableCacheBuster","id":"method-disableCacheBuster"},{"tagname":"method","owner":"Ext.Loader","meta":{},"name":"exclude","id":"method-exclude"},{"tagname":"method","owner":"Ext.Loader","meta":{},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.Loader","meta":{},"name":"getPath","id":"method-getPath"},{"tagname":"method","owner":"Ext.Loader","meta":{"private":true},"name":"getPrefix","id":"method-getPrefix"},{"tagname":"method","owner":"Ext.Loader","meta":{"private":true},"name":"historyPush","id":"method-historyPush"},{"tagname":"method","owner":"Ext.Loader","meta":{"private":true},"name":"injectScriptElement","id":"method-injectScriptElement"},{"tagname":"method","owner":"Ext.Loader","meta":{"private":true},"name":"isAClassNameWithAKnownPrefix","id":"method-isAClassNameWithAKnownPrefix"},{"tagname":"method","owner":"Ext.Loader","meta":{},"name":"loadScript","id":"method-loadScript"},{"tagname":"method","owner":"Ext.Loader","meta":{"private":true},"name":"loadScriptFile","id":"method-loadScriptFile"},{"tagname":"method","owner":"Ext.Loader","meta":{"private":true},"name":"onFileLoadError","id":"method-onFileLoadError"},{"tagname":"method","owner":"Ext.Loader","meta":{"private":true},"name":"onFileLoaded","id":"method-onFileLoaded"},{"tagname":"method","owner":"Ext.Loader","meta":{},"name":"onReady","id":"method-onReady"},{"tagname":"method","owner":"Ext.Loader","meta":{"private":true},"name":"refreshQueue","id":"method-refreshQueue"},{"tagname":"method","owner":"Ext.Loader","meta":{"private":true},"name":"removeScriptElement","id":"method-removeScriptElement"},{"tagname":"method","owner":"Ext.Loader","meta":{},"name":"require","id":"method-require"},{"tagname":"method","owner":"Ext.Loader","meta":{"chainable":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.Loader","meta":{"chainable":true},"name":"setPath","id":"method-setPath"},{"tagname":"method","owner":"Ext.Loader","meta":{},"name":"syncRequire","id":"method-syncRequire"},{"tagname":"method","owner":"Ext.Loader","meta":{"private":true},"name":"triggerReady","id":"method-triggerReady"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.Loader","singleton":true,"override":null,"inheritdoc":null,"id":"class-Ext.Loader","mixins":[],"mixedInto":[]});