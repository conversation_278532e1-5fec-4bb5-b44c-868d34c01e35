Ext.data.JsonP.Ext_chart_axis_Numeric({"alternateClassNames":["Ext.chart.NumericAxis"],"aliases":{"axis":["numeric"]},"enum":null,"parentMixins":[],"tagname":"class","subclasses":["Ext.chart.axis.Radial","Ext.chart.axis.Time"],"extends":"Ext.chart.axis.Axis","uses":["Ext.data.Store"],"html":"<div><pre class=\"hierarchy\"><h4>Alternate names</h4><div class='alternate-class-name'>Ext.chart.NumericAxis</div><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><a href='#!/api/Ext.chart.axis.Abstract' rel='Ext.chart.axis.Abstract' class='docClass'>Ext.chart.axis.Abstract</a><div class='subclass '><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='docClass'>Ext.chart.axis.Axis</a><div class='subclass '><strong>Ext.chart.axis.Numeric</strong></div></div></div></div><h4>Subclasses</h4><div class='dependency'><a href='#!/api/Ext.chart.axis.Radial' rel='Ext.chart.axis.Radial' class='docClass'>Ext.chart.axis.Radial</a></div><div class='dependency'><a href='#!/api/Ext.chart.axis.Time' rel='Ext.chart.axis.Time' class='docClass'>Ext.chart.axis.Time</a></div><h4>Uses</h4><div class='dependency'><a href='#!/api/Ext.data.Store' rel='Ext.data.Store' class='docClass'>Ext.data.Store</a></div><h4>Files</h4><div class='dependency'><a href='source/Numeric.html#Ext-chart-axis-Numeric' target='_blank'>Numeric.js</a></div></pre><div class='doc-contents'><p>An axis to handle numeric values. This axis is used for quantitative data as\nopposed to the category axis. You can set mininum and maximum values to the\naxis so that the values are bound to that. If no values are set, then the\nscale will auto-adjust to the values.</p>\n\n<pre class='inline-example '><code>var store = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.data.JsonStore\" rel=\"Ext.data.JsonStore\" class=\"docClass\">Ext.data.JsonStore</a>', {\n     fields: ['name', 'data1', 'data2', 'data3', 'data4', 'data5'],\n     data: [\n         {'name':'metric one', 'data1':10, 'data2':12, 'data3':14, 'data4':8, 'data5':13},\n         {'name':'metric two', 'data1':7, 'data2':8, 'data3':16, 'data4':10, 'data5':3},\n         {'name':'metric three', 'data1':5, 'data2':2, 'data3':14, 'data4':12, 'data5':7},\n         {'name':'metric four', 'data1':2, 'data2':14, 'data3':6, 'data4':1, 'data5':23},\n         {'name':'metric five', 'data1':27, 'data2':38, 'data3':36, 'data4':13, 'data5':33}\n     ]\n});\n\n<a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.chart.Chart\" rel=\"Ext.chart.Chart\" class=\"docClass\">Ext.chart.Chart</a>', {\n    renderTo: <a href=\"#!/api/Ext-method-getBody\" rel=\"Ext-method-getBody\" class=\"docClass\">Ext.getBody</a>(),\n    width: 500,\n    height: 300,\n    store: store,\n    axes: [{\n        type: 'Numeric',\n        position: 'left',\n        fields: ['data1', 'data2', 'data3', 'data4', 'data5'],\n        title: 'Sample Values',\n        grid: {\n            odd: {\n                opacity: 1,\n                fill: '#ddd',\n                stroke: '#bbb',\n                'stroke-width': 1\n            }\n        },\n        minimum: 0,\n        adjustMinimumByMajorUnit: 0\n    }, {\n        type: 'Category',\n        position: 'bottom',\n        fields: ['name'],\n        title: 'Sample Metrics',\n        grid: true,\n        label: {\n            rotate: {\n                degrees: 315\n            }\n        }\n    }],\n    series: [{\n        type: 'area',\n        highlight: false,\n        axis: 'left',\n        xField: 'name',\n        yField: ['data1', 'data2', 'data3', 'data4', 'data5'],\n        style: {\n            opacity: 0.93\n        }\n    }]\n});\n</code></pre>\n\n<p>In this example we create an axis of Numeric type. We set a minimum value so that\neven if all series have values greater than zero, the grid starts at zero. We bind\nthe axis onto the left part of the surface by setting <code>position</code> to <code>left</code>.\nWe bind three different store fields to this axis by setting <code>fields</code> to an array.\nWe set the title of the axis to <em>Number of Hits</em> by using the <code>title</code> property.\nWe use a <code>grid</code> configuration to set odd background rows to a certain style and even rows\nto be transparent/ignored.</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-cfg'>Config options</h3><div class='subsection'><div id='cfg-adjustEnd' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-cfg-adjustEnd' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-cfg-adjustEnd' class='name expandable'>adjustEnd</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Whether to adjust the label at the end of the axis. ...</div><div class='long'><p>Whether to adjust the label at the end of the axis.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-adjustMaximumByMajorUnit' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.chart.axis.Numeric'>Ext.chart.axis.Numeric</span><br/><a href='source/Numeric.html#Ext-chart-axis-Numeric-cfg-adjustMaximumByMajorUnit' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Numeric-cfg-adjustMaximumByMajorUnit' class='name expandable'>adjustMaximumByMajorUnit</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Indicates whether to extend maximum beyond data's maximum to the nearest\nmajorUnit. ...</div><div class='long'><p>Indicates whether to extend maximum beyond data's maximum to the nearest\nmajorUnit.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-adjustMinimumByMajorUnit' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.chart.axis.Numeric'>Ext.chart.axis.Numeric</span><br/><a href='source/Numeric.html#Ext-chart-axis-Numeric-cfg-adjustMinimumByMajorUnit' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Numeric-cfg-adjustMinimumByMajorUnit' class='name expandable'>adjustMinimumByMajorUnit</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Indicates whether to extend the minimum beyond data's minimum to the\nnearest majorUnit. ...</div><div class='long'><p>Indicates whether to extend the minimum beyond data's minimum to the\nnearest majorUnit.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-constrain' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.chart.axis.Numeric'>Ext.chart.axis.Numeric</span><br/><a href='source/Numeric.html#Ext-chart-axis-Numeric-cfg-constrain' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Numeric-cfg-constrain' class='name expandable'>constrain</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>If true, the values of the chart will be rendered only if they belong between minimum and maximum. ...</div><div class='long'><p>If true, the values of the chart will be rendered only if they belong between minimum and maximum.\nIf false, all values of the chart will be rendered, regardless of whether they belong between minimum and maximum or not.\nDefault's true if maximum and minimum is specified. It is ignored for stacked charts.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-dashSize' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-cfg-dashSize' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-cfg-dashSize' class='name expandable'>dashSize</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The size of the dash marker. ...</div><div class='long'><p>The size of the dash marker. Default's 3.</p>\n<p>Defaults to: <code>3</code></p></div></div></div><div id='cfg-decimals' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.chart.axis.Numeric'>Ext.chart.axis.Numeric</span><br/><a href='source/Numeric.html#Ext-chart-axis-Numeric-cfg-decimals' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Numeric-cfg-decimals' class='name expandable'>decimals</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The number of decimals to round the value to. ...</div><div class='long'><p>The number of decimals to round the value to.</p>\n<p>Defaults to: <code>2</code></p></div></div></div><div id='cfg-fields' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Abstract' rel='Ext.chart.axis.Abstract' class='defined-in docClass'>Ext.chart.axis.Abstract</a><br/><a href='source/Abstract.html#Ext-chart-axis-Abstract-cfg-fields' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Abstract-cfg-fields' class='name expandable'>fields</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</span></div><div class='description'><div class='short'>The fields of model to bind to this axis. ...</div><div class='long'><p>The fields of model to bind to this axis.</p>\n\n<p>For example if you have a data set of lap times per car, each having the fields:\n<code>'carName'</code>, <code>'avgSpeed'</code>, <code>'maxSpeed'</code>. Then you might want to show the data on chart\nwith <code>['carName']</code> on Name axis and <code>['avgSpeed', 'maxSpeed']</code> on Speed axis.</p>\n</div></div></div><div id='cfg-grid' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-cfg-grid' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-cfg-grid' class='name expandable'>grid</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>The grid configuration enables you to set a background grid for an axis. ...</div><div class='long'><p>The grid configuration enables you to set a background grid for an axis.\nIf set to <em>true</em> on a vertical axis, vertical lines will be drawn.\nIf set to <em>true</em> on a horizontal axis, horizontal lines will be drawn.\nIf both are set, a proper grid with horizontal and vertical lines will be drawn.</p>\n\n<p>You can set specific options for the grid configuration for odd and/or even lines/rows.\nSince the rows being drawn are rectangle sprites, you can set to an odd or even property\nall styles that apply to <a href=\"#!/api/Ext.draw.Sprite\" rel=\"Ext.draw.Sprite\" class=\"docClass\">Ext.draw.Sprite</a>. For more information on all the style\nproperties you can set please take a look at <a href=\"#!/api/Ext.draw.Sprite\" rel=\"Ext.draw.Sprite\" class=\"docClass\">Ext.draw.Sprite</a>. Some useful style\nproperties are <code>opacity</code>, <code>fill</code>, <code>stroke</code>, <code>stroke-width</code>, etc.</p>\n\n<p>The possible values for a grid option are then <em>true</em>, <em>false</em>, or an object with <code>{ odd, even }</code> properties\nwhere each property contains a sprite style descriptor object that is defined in <a href=\"#!/api/Ext.draw.Sprite\" rel=\"Ext.draw.Sprite\" class=\"docClass\">Ext.draw.Sprite</a>.</p>\n\n<p>For example:</p>\n\n<pre><code>axes: [{\n    type: 'Numeric',\n    position: 'left',\n    fields: ['data1', 'data2', 'data3'],\n    title: 'Number of Hits',\n    grid: {\n        odd: {\n            opacity: 1,\n            fill: '#ddd',\n            stroke: '#bbb',\n            'stroke-width': 1\n        }\n    }\n}, {\n    type: 'Category',\n    position: 'bottom',\n    fields: ['name'],\n    title: 'Month of the Year',\n    grid: true\n}]\n</code></pre>\n</div></div></div><div id='cfg-hidden' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-cfg-hidden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-cfg-hidden' class='name expandable'>hidden</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true to hide the axis. ...</div><div class='long'><p><code>true</code> to hide the axis.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-label' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Abstract' rel='Ext.chart.axis.Abstract' class='defined-in docClass'>Ext.chart.axis.Abstract</a><br/><a href='source/Abstract.html#Ext-chart-axis-Abstract-cfg-label' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Abstract-cfg-label' class='name not-expandable'>label</a><span> : <a href=\"#!/api/Ext.chart.Label\" rel=\"Ext.chart.Label\" class=\"docClass\">Ext.chart.Label</a></span></div><div class='description'><div class='short'><p>The config for chart label.</p>\n</div><div class='long'><p>The config for chart label.</p>\n</div></div></div><div id='cfg-length' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-cfg-length' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-cfg-length' class='name expandable'>length</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Offset axis position. ...</div><div class='long'><p>Offset axis position. Default's 0.</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='cfg-majorTickSteps' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-cfg-majorTickSteps' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-cfg-majorTickSteps' class='name expandable'>majorTickSteps</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>If minimum and maximum are specified it forces the number of major ticks to the specified value. ...</div><div class='long'><p>If <code>minimum</code> and <code>maximum</code> are specified it forces the number of major ticks to the specified value.\nIf a number of major ticks is forced, it wont search for pretty numbers at the ticks.</p>\n</div></div></div><div id='cfg-maximum' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.chart.axis.Numeric'>Ext.chart.axis.Numeric</span><br/><a href='source/Numeric.html#Ext-chart-axis-Numeric-cfg-maximum' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Numeric-cfg-maximum' class='name expandable'>maximum</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The maximum value drawn by the axis. ...</div><div class='long'><p>The maximum value drawn by the axis. If not set explicitly, the axis\nmaximum will be calculated automatically. It is ignored for stacked charts.</p>\n</div></div></div><div id='cfg-minimum' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.chart.axis.Numeric'>Ext.chart.axis.Numeric</span><br/><a href='source/Numeric.html#Ext-chart-axis-Numeric-cfg-minimum' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Numeric-cfg-minimum' class='name expandable'>minimum</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The minimum value drawn by the axis. ...</div><div class='long'><p>The minimum value drawn by the axis. If not set explicitly, the axis\nminimum will be calculated automatically. It is ignored for stacked charts.</p>\n</div></div></div><div id='cfg-minorTickSteps' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-cfg-minorTickSteps' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-cfg-minorTickSteps' class='name expandable'>minorTickSteps</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The number of small ticks between two major ticks. ...</div><div class='long'><p>The number of small ticks between two major ticks. Default is zero.</p>\n</div></div></div><div id='cfg-position' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.chart.axis.Numeric'>Ext.chart.axis.Numeric</span><br/><a href='source/Numeric.html#Ext-chart-axis-Numeric-cfg-position' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Numeric-cfg-position' class='name expandable'>position</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Indicates the position of the axis relative to the chart ...</div><div class='long'><p>Indicates the position of the axis relative to the chart</p>\n<p>Defaults to: <code>'left'</code></p><p>Overrides: <a href='#!/api/Ext.chart.axis.Axis-cfg-position' rel='Ext.chart.axis.Axis-cfg-position' class='docClass'>Ext.chart.axis.Axis.position</a></p></div></div></div><div id='cfg-scale' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.chart.axis.Numeric'>Ext.chart.axis.Numeric</span><br/><a href='source/Numeric.html#Ext-chart-axis-Numeric-cfg-scale' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Numeric-cfg-scale' class='name expandable'>scale</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>The scaling algorithm to use on this axis. ...</div><div class='long'><p>The scaling algorithm to use on this axis. May be \"linear\" or\n\"logarithmic\".  Currently only linear scale is implemented.</p>\n<p>Defaults to: <code>&quot;linear&quot;</code></p></div></div></div><div id='cfg-title' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-cfg-title' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-cfg-title' class='name not-expandable'>title</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'><p>The title for the Axis</p>\n</div><div class='long'><p>The title for the Axis</p>\n</div></div></div><div id='cfg-width' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-cfg-width' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-cfg-width' class='name expandable'>width</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>Offset axis width. ...</div><div class='long'><p>Offset axis width. Default's 0.</p>\n<p>Defaults to: <code>0</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-alignment' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Abstract' rel='Ext.chart.axis.Abstract' class='defined-in docClass'>Ext.chart.axis.Abstract</a><br/><a href='source/Abstract.html#Ext-chart-axis-Abstract-property-alignment' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Abstract-property-alignment' class='name not-expandable'>alignment</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-forceMinMax' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-property-forceMinMax' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-property-forceMinMax' class='name expandable'>forceMinMax</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>force min/max values from store ...</div><div class='long'><p>force min/max values from store</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-grid' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Abstract' rel='Ext.chart.axis.Abstract' class='defined-in docClass'>Ext.chart.axis.Abstract</a><br/><a href='source/Abstract.html#Ext-chart-axis-Abstract-property-grid' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Abstract-property-grid' class='name expandable'>grid</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isNumericAxis' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.chart.axis.Numeric'>Ext.chart.axis.Numeric</span><br/><a href='source/Numeric.html#Ext-chart-axis-Numeric-property-isNumericAxis' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Numeric-property-isNumericAxis' class='name expandable'>isNumericAxis</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-majorTickSteps' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-property-majorTickSteps' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-property-majorTickSteps' class='name expandable'>majorTickSteps</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-maxValue' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Abstract' rel='Ext.chart.axis.Abstract' class='defined-in docClass'>Ext.chart.axis.Abstract</a><br/><a href='source/Abstract.html#Ext-chart-axis-Abstract-property-maxValue' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Abstract-property-maxValue' class='name expandable'>maxValue</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-minValue' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Abstract' rel='Ext.chart.axis.Abstract' class='defined-in docClass'>Ext.chart.axis.Abstract</a><br/><a href='source/Abstract.html#Ext-chart-axis-Abstract-property-minValue' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Abstract-property-minValue' class='name expandable'>minValue</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-nullGutters' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-property-nullGutters' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-property-nullGutters' class='name not-expandable'>nullGutters</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div><div id='property-skipFirst' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-property-skipFirst' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-property-skipFirst' class='name expandable'>skipFirst</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-steps' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Abstract' rel='Ext.chart.axis.Abstract' class='defined-in docClass'>Ext.chart.axis.Abstract</a><br/><a href='source/Abstract.html#Ext-chart-axis-Abstract-property-steps' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Abstract-property-steps' class='name expandable'>steps</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>10</code></p></div></div></div><div id='property-type' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.chart.axis.Numeric'>Ext.chart.axis.Numeric</span><br/><a href='source/Numeric.html#Ext-chart-axis-Numeric-property-type' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Numeric-property-type' class='name expandable'>type</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>End Definitions ...</div><div class='long'><p>End Definitions</p>\n<p>Defaults to: <code>'Numeric'</code></p></div></div></div><div id='property-x' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Abstract' rel='Ext.chart.axis.Abstract' class='defined-in docClass'>Ext.chart.axis.Abstract</a><br/><a href='source/Abstract.html#Ext-chart-axis-Abstract-property-x' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Abstract-property-x' class='name expandable'>x</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-y' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Abstract' rel='Ext.chart.axis.Abstract' class='defined-in docClass'>Ext.chart.axis.Abstract</a><br/><a href='source/Abstract.html#Ext-chart-axis-Abstract-property-y' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Abstract-property-y' class='name expandable'>y</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>0</code></p></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.chart.axis.Numeric'>Ext.chart.axis.Numeric</span><br/><a href='source/Numeric.html#Ext-chart-axis-Numeric-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/Ext.chart.axis.Numeric-method-constructor' class='name expandable'>Ext.chart.axis.Numeric</a>( <span class='pre'>[config]</span> ) : <a href=\"#!/api/Ext.chart.axis.Numeric\" rel=\"Ext.chart.axis.Numeric\" class=\"docClass\">Ext.chart.axis.Numeric</a></div><div class='description'><div class='short'>Creates new Axis. ...</div><div class='long'><p>Creates new Axis.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Config options.</p>\n\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.chart.axis.Numeric\" rel=\"Ext.chart.axis.Numeric\" class=\"docClass\">Ext.chart.axis.Numeric</a></span><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.chart.axis.Abstract-method-constructor' rel='Ext.chart.axis.Abstract-method-constructor' class='docClass'>Ext.chart.axis.Abstract.constructor</a></p></div></div></div><div id='method-addDisplayAndLabels' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Abstract' rel='Ext.chart.axis.Abstract' class='defined-in docClass'>Ext.chart.axis.Abstract</a><br/><a href='source/Abstract.html#Ext-chart-axis-Abstract-method-addDisplayAndLabels' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Abstract-method-addDisplayAndLabels' class='name expandable'>addDisplayAndLabels</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-applyData' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.chart.axis.Numeric'>Ext.chart.axis.Numeric</span><br/><a href='source/Numeric.html#Ext-chart-axis-Numeric-method-applyData' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Numeric-method-applyData' class='name expandable'>applyData</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>apply data. ...</div><div class='long'><p>apply data.</p>\n<p>Overrides: <a href='#!/api/Ext.chart.axis.Axis-method-applyData' rel='Ext.chart.axis.Axis-method-applyData' class='docClass'>Ext.chart.axis.Axis.applyData</a></p></div></div></div><div id='method-calcEnds' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-method-calcEnds' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-method-calcEnds' class='name expandable'>calcEnds</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>creates a structure with start, end and step points. ...</div><div class='long'><p>creates a structure with start, end and step points.</p>\n</div></div></div><div id='method-callOverridden' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-destroy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-destroy' class='name expandable'>destroy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.util.ElementContainer-method-destroy' rel='Ext.util.ElementContainer-method-destroy' class='docClass'>Ext.util.ElementContainer.destroy</a>, <a href='#!/api/Ext.AbstractComponent-method-destroy' rel='Ext.AbstractComponent-method-destroy' class='docClass'>Ext.AbstractComponent.destroy</a>, <a href='#!/api/Ext.AbstractPlugin-method-destroy' rel='Ext.AbstractPlugin-method-destroy' class='docClass'>Ext.AbstractPlugin.destroy</a></p></div></div></div><div id='method-doConstrain' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.chart.axis.Numeric'>Ext.chart.axis.Numeric</span><br/><a href='source/Numeric.html#Ext-chart-axis-Numeric-method-doConstrain' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Numeric-method-doConstrain' class='name expandable'>doConstrain</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>constrains to datapoints between minimum and maximum only ...</div><div class='long'><p>constrains to datapoints between minimum and maximum only</p>\n</div></div></div><div id='method-drawAxis' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-method-drawAxis' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-method-drawAxis' class='name expandable'>drawAxis</a>( <span class='pre'>init</span> )</div><div class='description'><div class='short'>Renders the axis into the screen and updates its position. ...</div><div class='long'><p>Renders the axis into the screen and updates its position.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>init</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.chart.axis.Abstract-method-drawAxis' rel='Ext.chart.axis.Abstract-method-drawAxis' class='docClass'>Ext.chart.axis.Abstract.drawAxis</a></p></div></div></div><div id='method-drawGrid' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-method-drawGrid' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-method-drawGrid' class='name expandable'>drawGrid</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Renders an horizontal and/or vertical grid into the Surface. ...</div><div class='long'><p>Renders an horizontal and/or vertical grid into the Surface.</p>\n</div></div></div><div id='method-drawHorizontalLabels' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-method-drawHorizontalLabels' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-method-drawHorizontalLabels' class='name expandable'>drawHorizontalLabels</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-drawLabel' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-method-drawLabel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-method-drawLabel' class='name expandable'>drawLabel</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Renders the labels in the axes. ...</div><div class='long'><p>Renders the labels in the axes.</p>\n</div></div></div><div id='method-drawTitle' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-method-drawTitle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-method-drawTitle' class='name expandable'>drawTitle</a>( <span class='pre'>maxWidth, maxHeight</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>draws the title for the axis. ...</div><div class='long'><p>draws the title for the axis.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>maxWidth</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>maxHeight</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-drawVerticalLabels' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-method-drawVerticalLabels' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-method-drawVerticalLabels' class='name expandable'>drawVerticalLabels</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getId' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Abstract' rel='Ext.chart.axis.Abstract' class='defined-in docClass'>Ext.chart.axis.Abstract</a><br/><a href='source/Abstract.html#Ext-chart-axis-Abstract-method-getId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Abstract-method-getId' class='name expandable'>getId</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-getOrCreateLabel' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-method-getOrCreateLabel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-method-getOrCreateLabel' class='name expandable'>getOrCreateLabel</a>( <span class='pre'>i, text</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>i</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>text</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getRange' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-method-getRange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-method-getRange' class='name expandable'>getRange</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-intersect' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-method-intersect' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-method-intersect' class='name expandable'>intersect</a>( <span class='pre'>l1, l2</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>l1</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>l2</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-processView' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.chart.axis.Numeric'>Ext.chart.axis.Numeric</span><br/><a href='source/Numeric.html#Ext-chart-axis-Numeric-method-processView' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Numeric-method-processView' class='name expandable'>processView</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>applying constraint ...</div><div class='long'><p>applying constraint</p>\n<p>Overrides: <a href='#!/api/Ext.chart.axis.Abstract-method-processView' rel='Ext.chart.axis.Abstract-method-processView' class='docClass'>Ext.chart.axis.Abstract.processView</a></p></div></div></div><div id='method-rect2pointArray' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-method-rect2pointArray' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-method-rect2pointArray' class='name expandable'>rect2pointArray</a>( <span class='pre'>sprite</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>sprite</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-roundToDecimal' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.chart.axis.Numeric'>Ext.chart.axis.Numeric</span><br/><a href='source/Numeric.html#Ext-chart-axis-Numeric-method-roundToDecimal' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Numeric-method-roundToDecimal' class='name expandable'>roundToDecimal</a>( <span class='pre'>v, dec</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>v</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>dec</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setTitle' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.chart.axis.Axis' rel='Ext.chart.axis.Axis' class='defined-in docClass'>Ext.chart.axis.Axis</a><br/><a href='source/Axis.html#Ext-chart-axis-Axis-method-setTitle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.chart.axis.Axis-method-setTitle' class='name expandable'>setTitle</a>( <span class='pre'>title</span> )</div><div class='description'><div class='short'>Updates the title of this axis. ...</div><div class='long'><p>Updates the <a href=\"#!/api/Ext.chart.axis.Axis-cfg-title\" rel=\"Ext.chart.axis.Axis-cfg-title\" class=\"docClass\">title</a> of this axis.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>title</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-create' class='name expandable'>create</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create a new instance of this Class. ...</div><div class='long'><p>Create a new instance of this Class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.create({\n    someConfig: true\n});\n</code></pre>\n\n<p>All parameters are passed to the constructor of the class.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the created instance.</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div></div></div>","superclasses":["Ext.Base","Ext.chart.axis.Abstract","Ext.chart.axis.Axis"],"meta":{},"code_type":"ext_define","requires":[],"html_meta":{},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"Numeric.html#Ext-chart-axis-Numeric","filename":"Numeric.js"}],"linenr":1,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.chart.axis.Abstract","meta":{"private":true},"name":"alignment","id":"property-alignment"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.chart.axis.Axis","meta":{"private":true},"name":"forceMinMax","id":"property-forceMinMax"},{"tagname":"property","owner":"Ext.chart.axis.Abstract","meta":{"private":true},"name":"grid","id":"property-grid"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.chart.axis.Numeric","meta":{"private":true},"name":"isNumericAxis","id":"property-isNumericAxis"},{"tagname":"property","owner":"Ext.chart.axis.Axis","meta":{"private":true},"name":"majorTickSteps","id":"property-majorTickSteps"},{"tagname":"property","owner":"Ext.chart.axis.Abstract","meta":{"private":true},"name":"maxValue","id":"property-maxValue"},{"tagname":"property","owner":"Ext.chart.axis.Abstract","meta":{"private":true},"name":"minValue","id":"property-minValue"},{"tagname":"property","owner":"Ext.chart.axis.Axis","meta":{"private":true},"name":"nullGutters","id":"property-nullGutters"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"},{"tagname":"property","owner":"Ext.chart.axis.Axis","meta":{"private":true},"name":"skipFirst","id":"property-skipFirst"},{"tagname":"property","owner":"Ext.chart.axis.Abstract","meta":{"private":true},"name":"steps","id":"property-steps"},{"tagname":"property","owner":"Ext.chart.axis.Numeric","meta":{"private":true},"name":"type","id":"property-type"},{"tagname":"property","owner":"Ext.chart.axis.Abstract","meta":{"private":true},"name":"x","id":"property-x"},{"tagname":"property","owner":"Ext.chart.axis.Abstract","meta":{"private":true},"name":"y","id":"property-y"}],"cfg":[{"tagname":"cfg","owner":"Ext.chart.axis.Axis","meta":{},"name":"adjustEnd","id":"cfg-adjustEnd"},{"tagname":"cfg","owner":"Ext.chart.axis.Numeric","meta":{},"name":"adjustMaximumByMajorUnit","id":"cfg-adjustMaximumByMajorUnit"},{"tagname":"cfg","owner":"Ext.chart.axis.Numeric","meta":{},"name":"adjustMinimumByMajorUnit","id":"cfg-adjustMinimumByMajorUnit"},{"tagname":"cfg","owner":"Ext.chart.axis.Numeric","meta":{},"name":"constrain","id":"cfg-constrain"},{"tagname":"cfg","owner":"Ext.chart.axis.Axis","meta":{},"name":"dashSize","id":"cfg-dashSize"},{"tagname":"cfg","owner":"Ext.chart.axis.Numeric","meta":{},"name":"decimals","id":"cfg-decimals"},{"tagname":"cfg","owner":"Ext.chart.axis.Abstract","meta":{},"name":"fields","id":"cfg-fields"},{"tagname":"cfg","owner":"Ext.chart.axis.Axis","meta":{},"name":"grid","id":"cfg-grid"},{"tagname":"cfg","owner":"Ext.chart.axis.Axis","meta":{},"name":"hidden","id":"cfg-hidden"},{"tagname":"cfg","owner":"Ext.chart.axis.Abstract","meta":{},"name":"label","id":"cfg-label"},{"tagname":"cfg","owner":"Ext.chart.axis.Axis","meta":{},"name":"length","id":"cfg-length"},{"tagname":"cfg","owner":"Ext.chart.axis.Axis","meta":{},"name":"majorTickSteps","id":"cfg-majorTickSteps"},{"tagname":"cfg","owner":"Ext.chart.axis.Numeric","meta":{},"name":"maximum","id":"cfg-maximum"},{"tagname":"cfg","owner":"Ext.chart.axis.Numeric","meta":{},"name":"minimum","id":"cfg-minimum"},{"tagname":"cfg","owner":"Ext.chart.axis.Axis","meta":{},"name":"minorTickSteps","id":"cfg-minorTickSteps"},{"tagname":"cfg","owner":"Ext.chart.axis.Numeric","meta":{},"name":"position","id":"cfg-position"},{"tagname":"cfg","owner":"Ext.chart.axis.Numeric","meta":{"private":true},"name":"scale","id":"cfg-scale"},{"tagname":"cfg","owner":"Ext.chart.axis.Axis","meta":{},"name":"title","id":"cfg-title"},{"tagname":"cfg","owner":"Ext.chart.axis.Axis","meta":{},"name":"width","id":"cfg-width"}],"css_var":[],"method":[{"tagname":"method","owner":"Ext.chart.axis.Numeric","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"Ext.chart.axis.Abstract","meta":{"private":true},"name":"addDisplayAndLabels","id":"method-addDisplayAndLabels"},{"tagname":"method","owner":"Ext.chart.axis.Numeric","meta":{"private":true},"name":"applyData","id":"method-applyData"},{"tagname":"method","owner":"Ext.chart.axis.Axis","meta":{"private":true},"name":"calcEnds","id":"method-calcEnds"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.chart.axis.Numeric","meta":{"private":true},"name":"doConstrain","id":"method-doConstrain"},{"tagname":"method","owner":"Ext.chart.axis.Axis","meta":{},"name":"drawAxis","id":"method-drawAxis"},{"tagname":"method","owner":"Ext.chart.axis.Axis","meta":{},"name":"drawGrid","id":"method-drawGrid"},{"tagname":"method","owner":"Ext.chart.axis.Axis","meta":{"private":true},"name":"drawHorizontalLabels","id":"method-drawHorizontalLabels"},{"tagname":"method","owner":"Ext.chart.axis.Axis","meta":{},"name":"drawLabel","id":"method-drawLabel"},{"tagname":"method","owner":"Ext.chart.axis.Axis","meta":{"private":true},"name":"drawTitle","id":"method-drawTitle"},{"tagname":"method","owner":"Ext.chart.axis.Axis","meta":{"private":true},"name":"drawVerticalLabels","id":"method-drawVerticalLabels"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.chart.axis.Abstract","meta":{"private":true},"name":"getId","id":"method-getId"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.chart.axis.Axis","meta":{"private":true},"name":"getOrCreateLabel","id":"method-getOrCreateLabel"},{"tagname":"method","owner":"Ext.chart.axis.Axis","meta":{"private":true},"name":"getRange","id":"method-getRange"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.chart.axis.Axis","meta":{"private":true},"name":"intersect","id":"method-intersect"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.chart.axis.Numeric","meta":{"private":true},"name":"processView","id":"method-processView"},{"tagname":"method","owner":"Ext.chart.axis.Axis","meta":{"private":true},"name":"rect2pointArray","id":"method-rect2pointArray"},{"tagname":"method","owner":"Ext.chart.axis.Numeric","meta":{"private":true},"name":"roundToDecimal","id":"method-roundToDecimal"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.chart.axis.Axis","meta":{},"name":"setTitle","id":"method-setTitle"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.chart.axis.Numeric","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.chart.axis.Numeric","mixins":[],"mixedInto":[]});