Ext.data.JsonP.Ext_form_Labelable({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":"Ext.Base","uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><strong>Ext.form.Labelable</strong></div></div><h4>Requires</h4><div class='dependency'><a href='#!/api/Ext.XTemplate' rel='Ext.XTemplate' class='docClass'>Ext.XTemplate</a></div><h4>Mixed into</h4><div class='dependency'><a href='#!/api/Ext.form.FieldContainer' rel='Ext.form.FieldContainer' class='docClass'>Ext.form.FieldContainer</a></div><div class='dependency'><a href='#!/api/Ext.form.field.Base' rel='Ext.form.field.Base' class='docClass'>Ext.form.field.Base</a></div><h4>Files</h4><div class='dependency'><a href='source/Labelable.html#Ext-form-Labelable' target='_blank'>Labelable.js</a></div><div class='dependency'><a href='source/Labelable.scss3.html#Ext-form-Labelable' target='_blank'>Labelable.scss</a></div></pre><div class='doc-contents'><p>A mixin which allows a component to be configured and decorated with a label and/or error message as is\ncommon for form fields. This is used by e.g. <a href=\"#!/api/Ext.form.field.Base\" rel=\"Ext.form.field.Base\" class=\"docClass\">Ext.form.field.Base</a> and <a href=\"#!/api/Ext.form.FieldContainer\" rel=\"Ext.form.FieldContainer\" class=\"docClass\">Ext.form.FieldContainer</a>\nto let them be managed by the Field layout.</p>\n\n<p>NOTE: This mixin is mainly for internal library use and most users should not need to use it directly. It\nis more likely you will want to use one of the component classes that import this mixin, such as\n<a href=\"#!/api/Ext.form.field.Base\" rel=\"Ext.form.field.Base\" class=\"docClass\">Ext.form.field.Base</a> or <a href=\"#!/api/Ext.form.FieldContainer\" rel=\"Ext.form.FieldContainer\" class=\"docClass\">Ext.form.FieldContainer</a>.</p>\n\n<p>Use of this mixin does not make a component a field in the logical sense, meaning it does not provide any\nlogic or state related to values or validation; that is handled by the related <a href=\"#!/api/Ext.form.field.Field\" rel=\"Ext.form.field.Field\" class=\"docClass\">Ext.form.field.Field</a>\nmixin. These two mixins may be used separately (for example <a href=\"#!/api/Ext.form.FieldContainer\" rel=\"Ext.form.FieldContainer\" class=\"docClass\">Ext.form.FieldContainer</a> is Labelable but not a\nField), or in combination (for example <a href=\"#!/api/Ext.form.field.Base\" rel=\"Ext.form.field.Base\" class=\"docClass\">Ext.form.field.Base</a> implements both and has logic for connecting the\ntwo.)</p>\n\n<p>Component classes which use this mixin should use the Field layout\nor a derivation thereof to properly size and position the label and message according to the component config.\nThey must also call the <a href=\"#!/api/Ext.form.Labelable-method-initLabelable\" rel=\"Ext.form.Labelable-method-initLabelable\" class=\"docClass\">initLabelable</a> method during component initialization to ensure the mixin gets\nset up correctly.</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-cfg'>Config options</h3><div class='subsection'><div id='cfg-activeError' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-activeError' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-activeError' class='name expandable'>activeError</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>If specified, then the component will be displayed with this value as its active error when first rendered. ...</div><div class='long'><p>If specified, then the component will be displayed with this value as its active error when first rendered. Use\n<a href=\"#!/api/Ext.form.Labelable-method-setActiveError\" rel=\"Ext.form.Labelable-method-setActiveError\" class=\"docClass\">setActiveError</a> or <a href=\"#!/api/Ext.form.Labelable-method-unsetActiveError\" rel=\"Ext.form.Labelable-method-unsetActiveError\" class=\"docClass\">unsetActiveError</a> to change it after component creation.</p>\n</div></div></div><div id='cfg-activeErrorsTpl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-activeErrorsTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-activeErrorsTpl' class='name expandable'>activeErrorsTpl</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]/<a href=\"#!/api/Ext.XTemplate\" rel=\"Ext.XTemplate\" class=\"docClass\">Ext.XTemplate</a></span></div><div class='description'><div class='short'>The template used to format the Array of error messages passed to setActiveErrors into a single HTML\nstring. ...</div><div class='long'><p>The template used to format the Array of error messages passed to <a href=\"#!/api/Ext.form.Labelable-method-setActiveErrors\" rel=\"Ext.form.Labelable-method-setActiveErrors\" class=\"docClass\">setActiveErrors</a> into a single HTML\nstring. if the <a href=\"#!/api/Ext.form.Labelable-cfg-msgTarget\" rel=\"Ext.form.Labelable-cfg-msgTarget\" class=\"docClass\">msgTarget</a> is title, it defaults to a list separated by new lines. Otherwise, it\nrenders each message as an item in an unordered list.</p>\n</div></div></div><div id='cfg-afterBodyEl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-afterBodyEl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-afterBodyEl' class='name expandable'>afterBodyEl</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/Ext.XTemplate\" rel=\"Ext.XTemplate\" class=\"docClass\">Ext.XTemplate</a></span></div><div class='description'><div class='short'>An optional string or XTemplate configuration to insert in the field markup\nat the end of the input containing element. ...</div><div class='long'><p>An optional string or <code>XTemplate</code> configuration to insert in the field markup\nat the end of the input containing element. If an <code>XTemplate</code> is used, the component's <a href=\"#!/api/Ext.AbstractComponent-cfg-renderData\" rel=\"Ext.AbstractComponent-cfg-renderData\" class=\"docClass\">render data</a>\nserves as the context.</p>\n</div></div></div><div id='cfg-afterLabelTextTpl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-afterLabelTextTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-afterLabelTextTpl' class='name expandable'>afterLabelTextTpl</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/Ext.XTemplate\" rel=\"Ext.XTemplate\" class=\"docClass\">Ext.XTemplate</a></span></div><div class='description'><div class='short'>An optional string or XTemplate configuration to insert in the field markup\nafter the label text. ...</div><div class='long'><p>An optional string or <code>XTemplate</code> configuration to insert in the field markup\nafter the label text. If an <code>XTemplate</code> is used, the component's <a href=\"#!/api/Ext.AbstractComponent-cfg-renderData\" rel=\"Ext.AbstractComponent-cfg-renderData\" class=\"docClass\">render data</a>\nserves as the context.</p>\n</div></div></div><div id='cfg-afterLabelTpl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-afterLabelTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-afterLabelTpl' class='name expandable'>afterLabelTpl</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/Ext.XTemplate\" rel=\"Ext.XTemplate\" class=\"docClass\">Ext.XTemplate</a></span></div><div class='description'><div class='short'>An optional string or XTemplate configuration to insert in the field markup\nafter the label element. ...</div><div class='long'><p>An optional string or <code>XTemplate</code> configuration to insert in the field markup\nafter the label element. If an <code>XTemplate</code> is used, the component's <a href=\"#!/api/Ext.AbstractComponent-cfg-renderData\" rel=\"Ext.AbstractComponent-cfg-renderData\" class=\"docClass\">render data</a>\nserves as the context.</p>\n</div></div></div><div id='cfg-afterSubTpl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-afterSubTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-afterSubTpl' class='name expandable'>afterSubTpl</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/Ext.XTemplate\" rel=\"Ext.XTemplate\" class=\"docClass\">Ext.XTemplate</a></span></div><div class='description'><div class='short'>An optional string or XTemplate configuration to insert in the field markup\nafter the subTpl markup. ...</div><div class='long'><p>An optional string or <code>XTemplate</code> configuration to insert in the field markup\nafter the <a href=\"#!/api/Ext.form.Labelable-method-getSubTplMarkup\" rel=\"Ext.form.Labelable-method-getSubTplMarkup\" class=\"docClass\">subTpl markup</a>. If an <code>XTemplate</code> is used, the\ncomponent's <a href=\"#!/api/Ext.AbstractComponent-cfg-renderData\" rel=\"Ext.AbstractComponent-cfg-renderData\" class=\"docClass\">render data</a> serves as the context.</p>\n</div></div></div><div id='cfg-autoFitErrors' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-autoFitErrors' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-autoFitErrors' class='name expandable'>autoFitErrors</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Whether to adjust the component's body area to make room for 'side' or 'under' error messages. ...</div><div class='long'><p>Whether to adjust the component's body area to make room for 'side' or 'under' <a href=\"#!/api/Ext.form.Labelable-cfg-msgTarget\" rel=\"Ext.form.Labelable-cfg-msgTarget\" class=\"docClass\">error messages</a>.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-baseBodyCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-baseBodyCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-baseBodyCls' class='name expandable'>baseBodyCls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The CSS class to be applied to the body content element. ...</div><div class='long'><p>The CSS class to be applied to the body content element.</p>\n<p>Defaults to: <code>Ext.baseCSSPrefix + 'form-item-body'</code></p></div></div></div><div id='cfg-beforeBodyEl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-beforeBodyEl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-beforeBodyEl' class='name expandable'>beforeBodyEl</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/Ext.XTemplate\" rel=\"Ext.XTemplate\" class=\"docClass\">Ext.XTemplate</a></span></div><div class='description'><div class='short'>An optional string or XTemplate configuration to insert in the field markup\nat the beginning of the input containing ...</div><div class='long'><p>An optional string or <code>XTemplate</code> configuration to insert in the field markup\nat the beginning of the input containing element. If an <code>XTemplate</code> is used, the component's <a href=\"#!/api/Ext.AbstractComponent-cfg-renderData\" rel=\"Ext.AbstractComponent-cfg-renderData\" class=\"docClass\">render data</a>\nserves as the context.</p>\n</div></div></div><div id='cfg-beforeLabelTextTpl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-beforeLabelTextTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-beforeLabelTextTpl' class='name expandable'>beforeLabelTextTpl</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/Ext.XTemplate\" rel=\"Ext.XTemplate\" class=\"docClass\">Ext.XTemplate</a></span></div><div class='description'><div class='short'>An optional string or XTemplate configuration to insert in the field markup\nbefore the label text. ...</div><div class='long'><p>An optional string or <code>XTemplate</code> configuration to insert in the field markup\nbefore the label text. If an <code>XTemplate</code> is used, the component's <a href=\"#!/api/Ext.AbstractComponent-cfg-renderData\" rel=\"Ext.AbstractComponent-cfg-renderData\" class=\"docClass\">render data</a>\nserves as the context.</p>\n</div></div></div><div id='cfg-beforeLabelTpl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-beforeLabelTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-beforeLabelTpl' class='name expandable'>beforeLabelTpl</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/Ext.XTemplate\" rel=\"Ext.XTemplate\" class=\"docClass\">Ext.XTemplate</a></span></div><div class='description'><div class='short'>An optional string or XTemplate configuration to insert in the field markup\nbefore the label element. ...</div><div class='long'><p>An optional string or <code>XTemplate</code> configuration to insert in the field markup\nbefore the label element. If an <code>XTemplate</code> is used, the component's <a href=\"#!/api/Ext.AbstractComponent-cfg-renderData\" rel=\"Ext.AbstractComponent-cfg-renderData\" class=\"docClass\">render data</a>\nserves as the context.</p>\n</div></div></div><div id='cfg-beforeSubTpl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-beforeSubTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-beforeSubTpl' class='name expandable'>beforeSubTpl</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/Ext.XTemplate\" rel=\"Ext.XTemplate\" class=\"docClass\">Ext.XTemplate</a></span></div><div class='description'><div class='short'>An optional string or XTemplate configuration to insert in the field markup\nbefore the subTpl markup. ...</div><div class='long'><p>An optional string or <code>XTemplate</code> configuration to insert in the field markup\nbefore the <a href=\"#!/api/Ext.form.Labelable-method-getSubTplMarkup\" rel=\"Ext.form.Labelable-method-getSubTplMarkup\" class=\"docClass\">subTpl markup</a>. If an <code>XTemplate</code> is used, the\ncomponent's <a href=\"#!/api/Ext.AbstractComponent-cfg-renderData\" rel=\"Ext.AbstractComponent-cfg-renderData\" class=\"docClass\">render data</a> serves as the context.</p>\n</div></div></div><div id='cfg-clearCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-clearCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-clearCls' class='name expandable'>clearCls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The CSS class to be applied to the special clearing div rendered directly after the field contents wrapper to\nprovide...</div><div class='long'><p>The CSS class to be applied to the special clearing div rendered directly after the field contents wrapper to\nprovide field clearing.</p>\n<p>Defaults to: <code>Ext.baseCSSPrefix + 'clear'</code></p></div></div></div><div id='cfg-errorMsgCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-errorMsgCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-errorMsgCls' class='name expandable'>errorMsgCls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The CSS class to be applied to the error message element. ...</div><div class='long'><p>The CSS class to be applied to the error message element.</p>\n<p>Defaults to: <code>Ext.baseCSSPrefix + 'form-error-msg'</code></p></div></div></div><div id='cfg-fieldBodyCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-fieldBodyCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-fieldBodyCls' class='name expandable'>fieldBodyCls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>An extra CSS class to be applied to the body content element in addition to baseBodyCls. ...</div><div class='long'><p>An extra CSS class to be applied to the body content element in addition to <a href=\"#!/api/Ext.form.Labelable-cfg-baseBodyCls\" rel=\"Ext.form.Labelable-cfg-baseBodyCls\" class=\"docClass\">baseBodyCls</a>.</p>\n<p>Defaults to: <code>''</code></p></div></div></div><div id='cfg-fieldLabel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-fieldLabel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-fieldLabel' class='name expandable'>fieldLabel</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The label for the field. ...</div><div class='long'><p>The label for the field. It gets appended with the <a href=\"#!/api/Ext.form.Labelable-cfg-labelSeparator\" rel=\"Ext.form.Labelable-cfg-labelSeparator\" class=\"docClass\">labelSeparator</a>, and its position and sizing is\ndetermined by the <a href=\"#!/api/Ext.form.Labelable-cfg-labelAlign\" rel=\"Ext.form.Labelable-cfg-labelAlign\" class=\"docClass\">labelAlign</a>, <a href=\"#!/api/Ext.form.Labelable-cfg-labelWidth\" rel=\"Ext.form.Labelable-cfg-labelWidth\" class=\"docClass\">labelWidth</a>, and <a href=\"#!/api/Ext.form.Labelable-cfg-labelPad\" rel=\"Ext.form.Labelable-cfg-labelPad\" class=\"docClass\">labelPad</a> configs.</p>\n</div></div></div><div id='cfg-formItemCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-formItemCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-formItemCls' class='name expandable'>formItemCls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>A CSS class to be applied to the outermost element to denote that it is participating in the form field layout. ...</div><div class='long'><p>A CSS class to be applied to the outermost element to denote that it is participating in the form field layout.</p>\n<p>Defaults to: <code>Ext.baseCSSPrefix + 'form-item'</code></p></div></div></div><div id='cfg-hideEmptyLabel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-hideEmptyLabel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-hideEmptyLabel' class='name expandable'>hideEmptyLabel</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>When set to true, the label element (fieldLabel and labelSeparator) will be automatically\nhidden if the fieldLabel is...</div><div class='long'><p>When set to true, the label element (<a href=\"#!/api/Ext.form.Labelable-cfg-fieldLabel\" rel=\"Ext.form.Labelable-cfg-fieldLabel\" class=\"docClass\">fieldLabel</a> and <a href=\"#!/api/Ext.form.Labelable-cfg-labelSeparator\" rel=\"Ext.form.Labelable-cfg-labelSeparator\" class=\"docClass\">labelSeparator</a>) will be automatically\nhidden if the <a href=\"#!/api/Ext.form.Labelable-cfg-fieldLabel\" rel=\"Ext.form.Labelable-cfg-fieldLabel\" class=\"docClass\">fieldLabel</a> is empty. Setting this to false will cause the empty label element to be\nrendered and space to be reserved for it; this is useful if you want a field without a label to line up with\nother labeled fields in the same form.</p>\n\n<p>If you wish to unconditionall hide the label even if a non-empty fieldLabel is configured, then set the\n<a href=\"#!/api/Ext.form.Labelable-cfg-hideLabel\" rel=\"Ext.form.Labelable-cfg-hideLabel\" class=\"docClass\">hideLabel</a> config to true.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='cfg-hideLabel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-hideLabel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-hideLabel' class='name expandable'>hideLabel</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Set to true to completely hide the label element (fieldLabel and labelSeparator). ...</div><div class='long'><p>Set to true to completely hide the label element (<a href=\"#!/api/Ext.form.Labelable-cfg-fieldLabel\" rel=\"Ext.form.Labelable-cfg-fieldLabel\" class=\"docClass\">fieldLabel</a> and <a href=\"#!/api/Ext.form.Labelable-cfg-labelSeparator\" rel=\"Ext.form.Labelable-cfg-labelSeparator\" class=\"docClass\">labelSeparator</a>). Also see\n<a href=\"#!/api/Ext.form.Labelable-cfg-hideEmptyLabel\" rel=\"Ext.form.Labelable-cfg-hideEmptyLabel\" class=\"docClass\">hideEmptyLabel</a>, which controls whether space will be reserved for an empty fieldLabel.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='cfg-invalidCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-invalidCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-invalidCls' class='name expandable'>invalidCls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The CSS class to use when marking the component invalid. ...</div><div class='long'><p>The CSS class to use when marking the component invalid.</p>\n<p>Defaults to: <code>Ext.baseCSSPrefix + 'form-invalid'</code></p></div></div></div><div id='cfg-labelAlign' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-labelAlign' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-labelAlign' class='name expandable'>labelAlign</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Controls the position and alignment of the fieldLabel. ...</div><div class='long'><p>Controls the position and alignment of the <a href=\"#!/api/Ext.form.Labelable-cfg-fieldLabel\" rel=\"Ext.form.Labelable-cfg-fieldLabel\" class=\"docClass\">fieldLabel</a>. Valid values are:</p>\n\n<ul>\n<li>\"left\" (the default) - The label is positioned to the left of the field, with its text aligned to the left.\nIts width is determined by the <a href=\"#!/api/Ext.form.Labelable-cfg-labelWidth\" rel=\"Ext.form.Labelable-cfg-labelWidth\" class=\"docClass\">labelWidth</a> config.</li>\n<li>\"top\" - The label is positioned above the field.</li>\n<li>\"right\" - The label is positioned to the left of the field, with its text aligned to the right.\nIts width is determined by the <a href=\"#!/api/Ext.form.Labelable-cfg-labelWidth\" rel=\"Ext.form.Labelable-cfg-labelWidth\" class=\"docClass\">labelWidth</a> config.</li>\n</ul>\n\n<p>Defaults to: <code>'left'</code></p></div></div></div><div id='cfg-labelAttrTpl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-labelAttrTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-labelAttrTpl' class='name expandable'>labelAttrTpl</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/Ext.XTemplate\" rel=\"Ext.XTemplate\" class=\"docClass\">Ext.XTemplate</a></span></div><div class='description'><div class='short'>An optional string or XTemplate configuration to insert in the field markup\ninside the label element (as attributes). ...</div><div class='long'><p>An optional string or <code>XTemplate</code> configuration to insert in the field markup\ninside the label element (as attributes). If an <code>XTemplate</code> is used, the component's\n<a href=\"#!/api/Ext.AbstractComponent-cfg-renderData\" rel=\"Ext.AbstractComponent-cfg-renderData\" class=\"docClass\">render data</a> serves as the context.</p>\n</div></div></div><div id='cfg-labelCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-labelCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-labelCls' class='name expandable'>labelCls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The CSS class to be applied to the label element. ...</div><div class='long'><p>The CSS class to be applied to the label element. This (single) CSS class is used to formulate the renderSelector\nand drives the field layout where it is concatenated with a hyphen ('-') and <a href=\"#!/api/Ext.form.Labelable-cfg-labelAlign\" rel=\"Ext.form.Labelable-cfg-labelAlign\" class=\"docClass\">labelAlign</a>. To add\nadditional classes, use <a href=\"#!/api/Ext.form.Labelable-cfg-labelClsExtra\" rel=\"Ext.form.Labelable-cfg-labelClsExtra\" class=\"docClass\">labelClsExtra</a>.</p>\n<p>Defaults to: <code>Ext.baseCSSPrefix + 'form-item-label'</code></p></div></div></div><div id='cfg-labelClsExtra' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-labelClsExtra' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-labelClsExtra' class='name expandable'>labelClsExtra</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>An optional string of one or more additional CSS classes to add to the label element. ...</div><div class='long'><p>An optional string of one or more additional CSS classes to add to the label element. Defaults to empty.</p>\n</div></div></div><div id='cfg-labelPad' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-labelPad' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-labelPad' class='name expandable'>labelPad</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The amount of space in pixels between the fieldLabel and the input field. ...</div><div class='long'><p>The amount of space in pixels between the <a href=\"#!/api/Ext.form.Labelable-cfg-fieldLabel\" rel=\"Ext.form.Labelable-cfg-fieldLabel\" class=\"docClass\">fieldLabel</a> and the input field.</p>\n<p>Defaults to: <code>5</code></p></div></div></div><div id='cfg-labelSeparator' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-labelSeparator' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-labelSeparator' class='name expandable'>labelSeparator</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>Character(s) to be inserted at the end of the label text. ...</div><div class='long'><p>Character(s) to be inserted at the end of the <a href=\"#!/api/Ext.form.Labelable-cfg-fieldLabel\" rel=\"Ext.form.Labelable-cfg-fieldLabel\" class=\"docClass\">label text</a>.</p>\n\n<p>Set to empty string to hide the separator completely.</p>\n<p>Defaults to: <code>':'</code></p></div></div></div><div id='cfg-labelStyle' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-labelStyle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-labelStyle' class='name not-expandable'>labelStyle</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'><p>A CSS style specification string to apply directly to this field's label.</p>\n</div><div class='long'><p>A CSS style specification string to apply directly to this field's label.</p>\n</div></div></div><div id='cfg-labelWidth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-labelWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-labelWidth' class='name expandable'>labelWidth</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span></div><div class='description'><div class='short'>The width of the fieldLabel in pixels. ...</div><div class='long'><p>The width of the <a href=\"#!/api/Ext.form.Labelable-cfg-fieldLabel\" rel=\"Ext.form.Labelable-cfg-fieldLabel\" class=\"docClass\">fieldLabel</a> in pixels. Only applicable if the <a href=\"#!/api/Ext.form.Labelable-cfg-labelAlign\" rel=\"Ext.form.Labelable-cfg-labelAlign\" class=\"docClass\">labelAlign</a> is set to \"left\" or\n\"right\".</p>\n<p>Defaults to: <code>100</code></p></div></div></div><div id='cfg-labelableRenderTpl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-labelableRenderTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-labelableRenderTpl' class='name expandable'>labelableRenderTpl</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]/<a href=\"#!/api/Ext.XTemplate\" rel=\"Ext.XTemplate\" class=\"docClass\">Ext.XTemplate</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>The rendering template for the field decorations. ...</div><div class='long'><p>The rendering template for the field decorations. Component classes using this mixin\nshould include logic to use this as their <a href=\"#!/api/Ext.AbstractComponent-cfg-renderTpl\" rel=\"Ext.AbstractComponent-cfg-renderTpl\" class=\"docClass\">renderTpl</a>,\nand implement the <a href=\"#!/api/Ext.form.Labelable-method-getSubTplMarkup\" rel=\"Ext.form.Labelable-method-getSubTplMarkup\" class=\"docClass\">getSubTplMarkup</a> method to generate the field body content.</p>\n\n<p>The structure of a field is a table as follows:</p>\n\n<p>If <code>labelAlign: 'left',</code>msgTarget: 'side'`</p>\n\n<pre><code> +----------------------+----------------------+-------------+\n | Label:               | InputField           | sideErrorEl |\n +----------------------+----------------------+-------------+\n</code></pre>\n\n<p>If <code>labelAlign: 'left',</code>msgTarget: 'under'`</p>\n\n<pre><code> +----------------------+------------------------------------+\n | Label:               | InputField      (colspan=2)        |\n |                      | underErrorEl                       |\n +----------------------+------------------------------------+\n</code></pre>\n\n<p>If <code>labelAlign: 'top',</code>msgTarget: 'side'`</p>\n\n<pre><code> +---------------------------------------------+-------------+\n | label                                       |             |\n | InputField                                  | sideErrorEl |\n +---------------------------------------------+-------------+\n</code></pre>\n\n<p>If <code>labelAlign: 'top',</code>msgTarget: 'under'`</p>\n\n<pre><code> +-----------------------------------------------------------+\n | label                                                     |\n | InputField                      (colspan=2)               |\n | underErrorEl                                              |\n +-----------------------------------------------------------+\n</code></pre>\n\n<p>The total columns always the same for fields with each setting of <a href=\"#!/api/Ext.form.Labelable-cfg-labelAlign\" rel=\"Ext.form.Labelable-cfg-labelAlign\" class=\"docClass\">labelAlign</a> because when\nrendered into a <a href=\"#!/api/Ext.layout.container.Form\" rel=\"Ext.layout.container.Form\" class=\"docClass\">Ext.layout.container.Form</a> layout, just the <code>TR</code> of the table\nwill be placed into the form's main <code>TABLE</code>, and the columns of all the siblings\nmust match so that they all line up. In a <a href=\"#!/api/Ext.layout.container.Form\" rel=\"Ext.layout.container.Form\" class=\"docClass\">Ext.layout.container.Form</a> layout, different\nsettings of <a href=\"#!/api/Ext.form.Labelable-cfg-labelAlign\" rel=\"Ext.form.Labelable-cfg-labelAlign\" class=\"docClass\">labelAlign</a> are not supported because of the incompatible column structure.</p>\n\n<p>When the triggerCell or side error cell are hidden or shown, the input cell's colspan\nis recalculated to maintain the correct 3 visible column count.</p>\n<p>Defaults to: <code>['&lt;tr role=&quot;presentation&quot; id=&quot;{id}-inputRow&quot; &lt;tpl if=&quot;inFormLayout&quot;&gt;id=&quot;{id}&quot;&lt;/tpl&gt; class=&quot;{inputRowCls}&quot;&gt;', '&lt;tpl if=&quot;labelOnLeft&quot;&gt;', '&lt;td role=&quot;presentation&quot; id=&quot;{id}-labelCell&quot; style=&quot;{labelCellStyle}&quot; {labelCellAttrs}&gt;', '{beforeLabelTpl}', '&lt;label id=&quot;{id}-labelEl&quot; {labelAttrTpl}&lt;tpl if=&quot;inputId&quot;&gt; for=&quot;{inputId}&quot;&lt;/tpl&gt; class=&quot;{labelCls}&quot;', '&lt;tpl if=&quot;labelStyle&quot;&gt; style=&quot;{labelStyle}&quot;&lt;/tpl&gt;', ' unselectable=&quot;on&quot;', '&gt;', '{beforeLabelTextTpl}', '&lt;tpl if=&quot;fieldLabel&quot;&gt;{fieldLabel}{labelSeparator}&lt;/tpl&gt;', '{afterLabelTextTpl}', '&lt;/label&gt;', '{afterLabelTpl}', '&lt;/td&gt;', '&lt;/tpl&gt;', '&lt;td role=&quot;presentation&quot; class=&quot;{baseBodyCls} {fieldBodyCls} {extraFieldBodyCls}&quot; id=&quot;{id}-bodyEl&quot; colspan=&quot;{bodyColspan}&quot; role=&quot;presentation&quot;&gt;', '{beforeBodyEl}', '&lt;tpl if=&quot;labelAlign==\\'top\\'&quot;&gt;', '{beforeLabelTpl}', '&lt;div role=&quot;presentation&quot; id=&quot;{id}-labelCell&quot; style=&quot;{labelCellStyle}&quot;&gt;', '&lt;label id=&quot;{id}-labelEl&quot; {labelAttrTpl}&lt;tpl if=&quot;inputId&quot;&gt; for=&quot;{inputId}&quot;&lt;/tpl&gt; class=&quot;{labelCls}&quot;', '&lt;tpl if=&quot;labelStyle&quot;&gt; style=&quot;{labelStyle}&quot;&lt;/tpl&gt;', ' unselectable=&quot;on&quot;', '&gt;', '{beforeLabelTextTpl}', '&lt;tpl if=&quot;fieldLabel&quot;&gt;{fieldLabel}{labelSeparator}&lt;/tpl&gt;', '{afterLabelTextTpl}', '&lt;/label&gt;', '&lt;/div&gt;', '{afterLabelTpl}', '&lt;/tpl&gt;', '{beforeSubTpl}', '{[values.$comp.getSubTplMarkup(values)]}', '{afterSubTpl}', '&lt;tpl if=&quot;msgTarget===\\'side\\'&quot;&gt;', '{afterBodyEl}', '&lt;/td&gt;', '&lt;td role=&quot;presentation&quot; id=&quot;{id}-sideErrorCell&quot; vAlign=&quot;{[values.labelAlign===\\'top\\' &amp;&amp; !values.hideLabel ? \\'bottom\\' : \\'middle\\']}&quot; style=&quot;{[values.autoFitErrors ? \\'display:none\\' : \\'\\']}&quot; width=&quot;{errorIconWidth}&quot;&gt;', '&lt;div role=&quot;presentation&quot; id=&quot;{id}-errorEl&quot; class=&quot;{errorMsgCls}&quot; style=&quot;display:none&quot;&gt;&lt;/div&gt;', '&lt;/td&gt;', '&lt;tpl elseif=&quot;msgTarget==\\'under\\'&quot;&gt;', '&lt;div role=&quot;presentation&quot; id=&quot;{id}-errorEl&quot; class=&quot;{errorMsgClass}&quot; colspan=&quot;2&quot; style=&quot;display:none&quot;&gt;&lt;/div&gt;', '{afterBodyEl}', '&lt;/td&gt;', '&lt;/tpl&gt;', '&lt;/tr&gt;', {disableFormats: true}]</code></p></div></div></div><div id='cfg-msgTarget' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-msgTarget' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-msgTarget' class='name expandable'>msgTarget</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The location where the error message text should display. ...</div><div class='long'><p>The location where the error message text should display. Must be one of the following values:</p>\n\n<ul>\n<li><p><code>qtip</code> Display a quick tip containing the message when the user hovers over the field.\nThis is the default.</p>\n\n<p><strong><a href=\"#!/api/Ext.tip.QuickTipManager-method-init\" rel=\"Ext.tip.QuickTipManager-method-init\" class=\"docClass\">Ext.tip.QuickTipManager.init</a> must have been called for this setting to work.</strong></p></li>\n<li><p><code>title</code> Display the message in a default browser title attribute popup.</p></li>\n<li><code>under</code> Add a block div beneath the field containing the error message.</li>\n<li><code>side</code> Add an error icon to the right of the field, displaying the message in a popup on hover.</li>\n<li><code>none</code> Don't display any error message. This might be useful if you are implementing custom error display.</li>\n<li><code>[element id]</code> Add the error message directly to the innerHTML of the specified element.</li>\n</ul>\n\n<p>Defaults to: <code>'qtip'</code></p></div></div></div><div id='cfg-preventMark' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-cfg-preventMark' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-cfg-preventMark' class='name expandable'>preventMark</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true to disable displaying any error message set on this object. ...</div><div class='long'><p>true to disable displaying any <a href=\"#!/api/Ext.form.Labelable-method-setActiveError\" rel=\"Ext.form.Labelable-method-setActiveError\" class=\"docClass\">error message</a> set on this object.</p>\n<p>Defaults to: <code>false</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-autoEl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-property-autoEl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-property-autoEl' class='name expandable'>autoEl</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{tag: 'table', cellpadding: 0}</code></p></div></div></div><div id='property-bodyEl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-property-bodyEl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-property-bodyEl' class='name expandable'>bodyEl</a><span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span></div><div class='description'><div class='short'>The div Element wrapping the component's contents. ...</div><div class='long'><p>The div Element wrapping the component's contents. Only available after the component has been rendered.</p>\n</div></div></div><div id='property-childEls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-property-childEls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-property-childEls' class='name expandable'>childEls</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>['labelCell', 'labelEl', 'bodyEl', 'sideErrorCell', 'errorEl', 'inputRow']</code></p><p>Overrides: <a href='#!/api/Ext.util.ElementContainer-property-childEls' rel='Ext.util.ElementContainer-property-childEls' class='docClass'>Ext.util.ElementContainer.childEls</a></p></div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-errorEl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-property-errorEl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-property-errorEl' class='name expandable'>errorEl</a><span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span></div><div class='description'><div class='short'>The div Element that will contain the component's error message(s). ...</div><div class='long'><p>The div Element that will contain the component's error message(s). Note that depending on the configured\n<a href=\"#!/api/Ext.form.Labelable-cfg-msgTarget\" rel=\"Ext.form.Labelable-cfg-msgTarget\" class=\"docClass\">msgTarget</a>, this element may be hidden in favor of some other form of presentation, but will always\nbe present in the DOM for use by assistive technologies.</p>\n</div></div></div><div id='property-htmlActiveErrorsTpl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-property-htmlActiveErrorsTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-property-htmlActiveErrorsTpl' class='name expandable'>htmlActiveErrorsTpl</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>['&lt;tpl if=&quot;errors &amp;&amp; errors.length&quot;&gt;', '&lt;ul class=&quot;{listCls}&quot;&gt;&lt;tpl for=&quot;errors&quot;&gt;&lt;li role=&quot;alert&quot;&gt;{.}&lt;/li&gt;&lt;/tpl&gt;&lt;/ul&gt;', '&lt;/tpl&gt;']</code></p></div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-inputRowCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-property-inputRowCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-property-inputRowCls' class='name expandable'>inputRowCls</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>private ...</div><div class='long'><p>private</p>\n<p>Defaults to: <code>Ext.baseCSSPrefix + 'form-item-input-row'</code></p></div></div></div><div id='property-isFieldLabelable' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-property-isFieldLabelable' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-property-isFieldLabelable' class='name expandable'>isFieldLabelable</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>Flag denoting that this object is labelable as a field. ...</div><div class='long'><p>Flag denoting that this object is labelable as a field. Always true.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-labelCell' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-property-labelCell' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-property-labelCell' class='name expandable'>labelCell</a><span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span></div><div class='description'><div class='short'>The &lt;TD&gt; Element which contains the label Element for this component. ...</div><div class='long'><p>The <code>&lt;TD&gt;</code> Element which contains the label Element for this component. Only available after the component has been rendered.</p>\n</div></div></div><div id='property-labelEl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-property-labelEl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-property-labelEl' class='name expandable'>labelEl</a><span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a></span></div><div class='description'><div class='short'>The label Element for this component. ...</div><div class='long'><p>The label Element for this component. Only available after the component has been rendered.</p>\n</div></div></div><div id='property-labelableInsertions' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-property-labelableInsertions' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-property-labelableInsertions' class='name expandable'>labelableInsertions</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>['beforeBodyEl', 'afterBodyEl', 'beforeLabelTpl', 'afterLabelTpl', 'beforeSubTpl', 'afterSubTpl', 'beforeLabelTextTpl', 'afterLabelTextTpl', 'labelAttrTpl']</code></p></div></div></div><div id='property-labelableRenderProps' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-property-labelableRenderProps' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-property-labelableRenderProps' class='name expandable'>labelableRenderProps</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>This is an array to avoid a split on every call to Ext.copyTo ...</div><div class='long'><p>This is an array to avoid a split on every call to <a href=\"#!/api/Ext-method-copyTo\" rel=\"Ext-method-copyTo\" class=\"docClass\">Ext.copyTo</a></p>\n<p>Defaults to: <code>['allowBlank', 'id', 'labelAlign', 'fieldBodyCls', 'extraFieldBodyCls', 'baseBodyCls', 'clearCls', 'labelSeparator', 'msgTarget', 'inputRowCls']</code></p></div></div></div><div id='property-noWrap' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-property-noWrap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-property-noWrap' class='name expandable'>noWrap</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Tells the layout system that the height can be measured immediately because the width does not need setting. ...</div><div class='long'><p>Tells the layout system that the height can be measured immediately because the width does not need setting.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-plaintextActiveErrorsTpl' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-property-plaintextActiveErrorsTpl' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-property-plaintextActiveErrorsTpl' class='name expandable'>plaintextActiveErrorsTpl</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>['&lt;tpl if=&quot;errors &amp;&amp; errors.length&quot;&gt;', '&lt;tpl for=&quot;errors&quot;&gt;&lt;tpl if=&quot;xindex &amp;gt; 1&quot;&gt;\\n&lt;/tpl&gt;{.}&lt;/tpl&gt;', '&lt;/tpl&gt;']</code></p></div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div><div id='property-xhooks' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-property-xhooks' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-property-xhooks' class='name not-expandable'>xhooks</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>\n</div><div class='long'>\n</div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-callOverridden' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-destroy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-destroy' class='name expandable'>destroy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.util.ElementContainer-method-destroy' rel='Ext.util.ElementContainer-method-destroy' class='docClass'>Ext.util.ElementContainer.destroy</a>, <a href='#!/api/Ext.AbstractComponent-method-destroy' rel='Ext.AbstractComponent-method-destroy' class='docClass'>Ext.AbstractComponent.destroy</a>, <a href='#!/api/Ext.AbstractPlugin-method-destroy' rel='Ext.AbstractPlugin-method-destroy' class='docClass'>Ext.AbstractPlugin.destroy</a></p></div></div></div><div id='method-getActiveError' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-getActiveError' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-getActiveError' class='name expandable'>getActiveError</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Gets the active error message for this component, if any. ...</div><div class='long'><p>Gets the active error message for this component, if any. This does not trigger validation on its own, it merely\nreturns any message that the component may already hold.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The active error message on the component; if there is no error, an empty string is returned.</p>\n</div></li></ul></div></div></div><div id='method-getActiveErrors' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-getActiveErrors' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-getActiveErrors' class='name expandable'>getActiveErrors</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</div><div class='description'><div class='short'>Gets an Array of any active error messages currently applied to the field. ...</div><div class='long'><p>Gets an Array of any active error messages currently applied to the field. This does not trigger validation on\nits own, it merely returns any messages that the component may already hold.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</span><div class='sub-desc'><p>The active error messages on the component; if there are no errors, an empty Array is\nreturned.</p>\n</div></li></ul></div></div></div><div id='method-getBodyColspan' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-getBodyColspan' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-getBodyColspan' class='name expandable'>getBodyColspan</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Calculates the colspan value for the body cell - the cell which contains the input field. ...</div><div class='long'><p>Calculates the colspan value for the body cell - the cell which contains the input field.</p>\n\n<p>The field table structure contains 4 columns:</p>\n</div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getErrorMsgCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-getErrorMsgCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-getErrorMsgCls' class='name expandable'>getErrorMsgCls</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getFieldLabel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-getFieldLabel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-getFieldLabel' class='name expandable'>getFieldLabel</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='template signature' >template</strong></div><div class='description'><div class='short'>Returns the label for the field. ...</div><div class='long'><p>Returns the label for the field. Defaults to simply returning the <a href=\"#!/api/Ext.form.Labelable-cfg-fieldLabel\" rel=\"Ext.form.Labelable-cfg-fieldLabel\" class=\"docClass\">fieldLabel</a> config. Can be overridden\nto provide a custom generated label.</p>\n      <div class='signature-box template'>\n      <p>This is a <a href=\"#!/guide/components\">template method</a>.\n         a hook into the functionality of this class.\n         Feel free to override it in child classes.</p>\n      </div>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The configured field label, or empty string if not defined</p>\n</div></li></ul></div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-getInputId' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-getInputId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-getInputId' class='name expandable'>getInputId</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Get the input id, if any, for this component. ...</div><div class='long'><p>Get the input id, if any, for this component. This is used as the \"for\" attribute on the label element.\nImplementing subclasses may also use this as e.g. the id for their own input element.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The input id</p>\n</div></li></ul></div></div></div><div id='method-getInsertionRenderData' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-getInsertionRenderData' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-getInsertionRenderData' class='name expandable'>getInsertionRenderData</a>( <span class='pre'>data, names</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>data</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getLabelCellAttrs' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-getLabelCellAttrs' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-getLabelCellAttrs' class='name expandable'>getLabelCellAttrs</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getLabelCellStyle' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-getLabelCellStyle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-getLabelCellStyle' class='name expandable'>getLabelCellStyle</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getLabelCls' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-getLabelCls' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-getLabelCls' class='name expandable'>getLabelCls</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getLabelStyle' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-getLabelStyle' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-getLabelStyle' class='name expandable'>getLabelStyle</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Gets any label styling for the labelEl ...</div><div class='long'><p>Gets any label styling for the labelEl</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The label styling</p>\n</div></li></ul></div></div></div><div id='method-getLabelWidth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-getLabelWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-getLabelWidth' class='name expandable'>getLabelWidth</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Gets the width of the label (if visible) ...</div><div class='long'><p>Gets the width of the label (if visible)</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The label width</p>\n</div></li></ul></div></div></div><div id='method-getLabelableRenderData' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-getLabelableRenderData' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-getLabelableRenderData' class='name expandable'>getLabelableRenderData</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Generates the arguments for the field decorations rendering template. ...</div><div class='long'><p>Generates the arguments for the field decorations <a href=\"#!/api/Ext.form.Labelable-cfg-labelableRenderTpl\" rel=\"Ext.form.Labelable-cfg-labelableRenderTpl\" class=\"docClass\">rendering template</a>.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The template arguments</p>\n\n</div></li></ul></div></div></div><div id='method-getSubTplMarkup' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-getSubTplMarkup' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-getSubTplMarkup' class='name expandable'>getSubTplMarkup</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Gets the markup to be inserted into the outer template's bodyEl. ...</div><div class='long'><p>Gets the markup to be inserted into the outer template's bodyEl. Defaults to empty string, should be implemented\nby classes including this mixin as needed.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The markup to be inserted</p>\n\n</div></li></ul></div></div></div><div id='method-hasActiveError' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-hasActiveError' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-hasActiveError' class='name expandable'>hasActiveError</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Tells whether the field currently has an active error message. ...</div><div class='long'><p>Tells whether the field currently has an active error message. This does not trigger validation on its own, it\nmerely looks for any message that the component may already hold.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-hasVisibleLabel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-hasVisibleLabel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-hasVisibleLabel' class='name expandable'>hasVisibleLabel</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks if the field has a visible label ...</div><div class='long'><p>Checks if the field has a visible label</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if the field has a visible label</p>\n</div></li></ul></div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-initLabelable' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-initLabelable' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-initLabelable' class='name expandable'>initLabelable</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Performs initialization of this mixin. ...</div><div class='long'><p>Performs initialization of this mixin. Component classes using this mixin should call this method during their\nown initialization.</p>\n</div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-renderActiveError' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-renderActiveError' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-renderActiveError' class='name expandable'>renderActiveError</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Updates the rendered DOM to match the current activeError. ...</div><div class='long'><p>Updates the rendered DOM to match the current activeError. This only updates the content and\nattributes, you'll have to call doComponentLayout to actually update the display.</p>\n</div></div></div><div id='method-setActiveError' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-setActiveError' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-setActiveError' class='name expandable'>setActiveError</a>( <span class='pre'>msg</span> )</div><div class='description'><div class='short'>Sets the active error message to the given string. ...</div><div class='long'><p>Sets the active error message to the given string. This replaces the entire error message contents with the given\nstring. Also see <a href=\"#!/api/Ext.form.Labelable-method-setActiveErrors\" rel=\"Ext.form.Labelable-method-setActiveErrors\" class=\"docClass\">setActiveErrors</a> which accepts an Array of messages and formats them according to the\n<a href=\"#!/api/Ext.form.Labelable-cfg-activeErrorsTpl\" rel=\"Ext.form.Labelable-cfg-activeErrorsTpl\" class=\"docClass\">activeErrorsTpl</a>. Note that this only updates the error message element's text and attributes, you'll\nhave to call doComponentLayout to actually update the field's layout to match. If the field extends <a href=\"#!/api/Ext.form.field.Base\" rel=\"Ext.form.field.Base\" class=\"docClass\">Ext.form.field.Base</a> you should call <a href=\"#!/api/Ext.form.field.Base-method-markInvalid\" rel=\"Ext.form.field.Base-method-markInvalid\" class=\"docClass\">markInvalid</a> instead.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>msg</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The error message</p>\n</div></li></ul></div></div></div><div id='method-setActiveErrors' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-setActiveErrors' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-setActiveErrors' class='name expandable'>setActiveErrors</a>( <span class='pre'>errors</span> )</div><div class='description'><div class='short'>Set the active error message to an Array of error messages. ...</div><div class='long'><p>Set the active error message to an Array of error messages. The messages are formatted into a single message\nstring using the <a href=\"#!/api/Ext.form.Labelable-cfg-activeErrorsTpl\" rel=\"Ext.form.Labelable-cfg-activeErrorsTpl\" class=\"docClass\">activeErrorsTpl</a>. Also see <a href=\"#!/api/Ext.form.Labelable-method-setActiveError\" rel=\"Ext.form.Labelable-method-setActiveError\" class=\"docClass\">setActiveError</a> which allows setting the entire error\ncontents with a single string. Note that this only updates the error message element's text and attributes,\nyou'll have to call doComponentLayout to actually update the field's layout to match. If the field extends\n<a href=\"#!/api/Ext.form.field.Base\" rel=\"Ext.form.field.Base\" class=\"docClass\">Ext.form.field.Base</a> you should call <a href=\"#!/api/Ext.form.field.Base-method-markInvalid\" rel=\"Ext.form.field.Base-method-markInvalid\" class=\"docClass\">markInvalid</a> instead.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>errors</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>The error messages</p>\n</div></li></ul></div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setFieldDefaults' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-setFieldDefaults' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-setFieldDefaults' class='name expandable'>setFieldDefaults</a>( <span class='pre'>defaults</span> )</div><div class='description'><div class='short'>Applies a set of default configuration values to this Labelable instance. ...</div><div class='long'><p>Applies a set of default configuration values to this Labelable instance. For each of the properties in the given\nobject, check if this component hasOwnProperty that config; if not then it's inheriting a default value from its\nprototype and we should apply the default value.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>defaults</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The defaults to apply to the object.</p>\n</div></li></ul></div></div></div><div id='method-setFieldLabel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-setFieldLabel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-setFieldLabel' class='name expandable'>setFieldLabel</a>( <span class='pre'>label</span> )</div><div class='description'><div class='short'>Set the label of this field. ...</div><div class='long'><p>Set the label of this field.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>label</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The new label. The <a href=\"#!/api/Ext.form.Labelable-cfg-labelSeparator\" rel=\"Ext.form.Labelable-cfg-labelSeparator\" class=\"docClass\">labelSeparator</a> will be automatically appended to the label\nstring.</p>\n</div></li></ul></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-trimLabelSeparator' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-trimLabelSeparator' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-trimLabelSeparator' class='name expandable'>trimLabelSeparator</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns the trimmed label by slicing off the label separator character. ...</div><div class='long'><p>Returns the trimmed label by slicing off the label separator character. Can be overridden.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The trimmed field label, or empty string if not defined</p>\n</div></li></ul></div></div></div><div id='method-unsetActiveError' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-method-unsetActiveError' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-method-unsetActiveError' class='name expandable'>unsetActiveError</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Clears the active error message(s). ...</div><div class='long'><p>Clears the active error message(s). Note that this only clears the error message element's text and attributes,\nyou'll have to call doComponentLayout to actually update the field's layout to match. If the field extends <a href=\"#!/api/Ext.form.field.Base\" rel=\"Ext.form.field.Base\" class=\"docClass\">Ext.form.field.Base</a> you should call <a href=\"#!/api/Ext.form.field.Base-method-clearInvalid\" rel=\"Ext.form.field.Base-method-clearInvalid\" class=\"docClass\">clearInvalid</a> instead.</p>\n</div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-create' class='name expandable'>create</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create a new instance of this Class. ...</div><div class='long'><p>Create a new instance of this Class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.create({\n    someConfig: true\n});\n</code></pre>\n\n<p>All parameters are passed to the constructor of the class.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the created instance.</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-event'>Events</h3><div class='subsection'><div id='event-errorchange' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.html#Ext-form-Labelable-event-errorchange' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-event-errorchange' class='name expandable'>errorchange</a>( <span class='pre'>this, error, eOpts</span> )</div><div class='description'><div class='short'>Fires when the active error message is changed via setActiveError. ...</div><div class='long'><p>Fires when the active error message is changed via <a href=\"#!/api/Ext.form.Labelable-method-setActiveError\" rel=\"Ext.form.Labelable-method-setActiveError\" class=\"docClass\">setActiveError</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.form.Labelable\" rel=\"Ext.form.Labelable\" class=\"docClass\">Ext.form.Labelable</a><div class='sub-desc'>\n</div></li><li><span class='pre'>error</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The active error message</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-css_var'>CSS Variables</h3><div class='subsection'><div id='css_var-S-form-error-icon-height' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-error-icon-height' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-error-icon-height' class='name expandable'>$form-error-icon-height</a><span> : number</span></div><div class='description'><div class='short'>Height for form error icons. ...</div><div class='long'><p>Height for form error icons.</p>\n<p>Defaults to: <code>16px</code></p></div></div></div><div id='css_var-S-form-error-icon-side-margin' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-error-icon-side-margin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-error-icon-side-margin' class='name expandable'>$form-error-icon-side-margin</a><span> : number/list</span></div><div class='description'><div class='short'>Margin for error icons that are aligned to the side of the field ...</div><div class='long'><p>Margin for error icons that are aligned to the side of the field</p>\n<p>Defaults to: <code>0 1px</code></p></div></div></div><div id='css_var-S-form-error-icon-width' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-error-icon-width' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-error-icon-width' class='name expandable'>$form-error-icon-width</a><span> : number</span></div><div class='description'><div class='short'>Width for form error icons. ...</div><div class='long'><p>Width for form error icons.</p>\n<p>Defaults to: <code>16px</code></p></div></div></div><div id='css_var-S-form-error-msg-color' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-error-msg-color' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-error-msg-color' class='name expandable'>$form-error-msg-color</a><span> : color</span></div><div class='description'><div class='short'>The text color of form error messages ...</div><div class='long'><p>The text color of form error messages</p>\n<p>Defaults to: <code>$form-field-invalid-border-color</code></p></div></div></div><div id='css_var-S-form-error-msg-font-family' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-error-msg-font-family' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-error-msg-font-family' class='name expandable'>$form-error-msg-font-family</a><span> : string</span></div><div class='description'><div class='short'>The font-family of form error messages ...</div><div class='long'><p>The font-family of form error messages</p>\n<p>Defaults to: <code>$font-family</code></p></div></div></div><div id='css_var-S-form-error-msg-font-size' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-error-msg-font-size' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-error-msg-font-size' class='name expandable'>$form-error-msg-font-size</a><span> : number</span></div><div class='description'><div class='short'>The font-size of form error messages ...</div><div class='long'><p>The font-size of form error messages</p>\n<p>Defaults to: <code>$font-size</code></p></div></div></div><div id='css_var-S-form-error-msg-font-weight' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-error-msg-font-weight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-error-msg-font-weight' class='name expandable'>$form-error-msg-font-weight</a><span> : string</span></div><div class='description'><div class='short'>The font-weight of form error messages ...</div><div class='long'><p>The font-weight of form error messages</p>\n<p>Defaults to: <code>normal</code></p></div></div></div><div id='css_var-S-form-error-msg-line-height' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-error-msg-line-height' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-error-msg-line-height' class='name expandable'>$form-error-msg-line-height</a><span> : number</span></div><div class='description'><div class='short'>The line-height of form error messages ...</div><div class='long'><p>The line-height of form error messages</p>\n<p>Defaults to: <code>$form-error-icon-height</code></p></div></div></div><div id='css_var-S-form-error-under-icon-spacing' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-error-under-icon-spacing' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-error-under-icon-spacing' class='name expandable'>$form-error-under-icon-spacing</a><span> : number</span></div><div class='description'><div class='short'>The space between the icon and the message for errors that display under the field ...</div><div class='long'><p>The space between the icon and the message for errors that display under the field</p>\n<p>Defaults to: <code>4px</code></p></div></div></div><div id='css_var-S-form-error-under-padding' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-error-under-padding' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-error-under-padding' class='name expandable'>$form-error-under-padding</a><span> : number/list</span></div><div class='description'><div class='short'>The padding on errors that display under the form field ...</div><div class='long'><p>The padding on errors that display under the form field</p>\n<p>Defaults to: <code>2px 2px 2px 0</code></p></div></div></div><div id='css_var-S-form-item-margin-bottom' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-item-margin-bottom' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-item-margin-bottom' class='name expandable'>$form-item-margin-bottom</a><span> : measurement</span></div><div class='description'><div class='short'>The bottom margin to apply to form items when in auto, anchor, vbox, or table layout ...</div><div class='long'><p>The bottom margin to apply to form items when in auto, anchor, vbox, or table layout</p>\n<p>Defaults to: <code>5px</code></p></div></div></div><div id='css_var-S-form-label-font-color' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-label-font-color' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-label-font-color' class='name expandable'>$form-label-font-color</a><span> : color</span></div><div class='description'><div class='short'>The text color of form field labels ...</div><div class='long'><p>The text color of form field labels</p>\n<p>Defaults to: <code>$color</code></p></div></div></div><div id='css_var-S-form-label-font-family' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-label-font-family' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-label-font-family' class='name expandable'>$form-label-font-family</a><span> : string</span></div><div class='description'><div class='short'>The font-family of form field labels ...</div><div class='long'><p>The font-family of form field labels</p>\n<p>Defaults to: <code>$font-family</code></p></div></div></div><div id='css_var-S-form-label-font-size' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-label-font-size' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-label-font-size' class='name expandable'>$form-label-font-size</a><span> : number</span></div><div class='description'><div class='short'>The font-size of form field labels ...</div><div class='long'><p>The font-size of form field labels</p>\n<p>Defaults to: <code>$font-size</code></p></div></div></div><div id='css_var-S-form-label-font-weight' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-label-font-weight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-label-font-weight' class='name expandable'>$form-label-font-weight</a><span> : string</span></div><div class='description'><div class='short'>The font-weight of form field labels ...</div><div class='long'><p>The font-weight of form field labels</p>\n<p>Defaults to: <code>normal</code></p></div></div></div><div id='css_var-S-form-label-line-height' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-label-line-height' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-label-line-height' class='name expandable'>$form-label-line-height</a><span> : number</span></div><div class='description'><div class='short'>The line-height of form field labels ...</div><div class='long'><p>The line-height of form field labels</p>\n<p>Defaults to: <code>round ( $form-label-font-size * 1.15 )</code></p></div></div></div><div id='css_var-S-form-toolbar-label-color' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-toolbar-label-color' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-toolbar-label-color' class='name expandable'>$form-toolbar-label-color</a><span> : color</span></div><div class='description'><div class='short'>The text color of toolbar field labels ...</div><div class='long'><p>The text color of toolbar field labels</p>\n<p>Defaults to: <code>$color</code></p></div></div></div><div id='css_var-S-form-toolbar-label-font-family' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-toolbar-label-font-family' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-toolbar-label-font-family' class='name expandable'>$form-toolbar-label-font-family</a><span> : string</span></div><div class='description'><div class='short'>The font-family of toolbar field labels ...</div><div class='long'><p>The font-family of toolbar field labels</p>\n<p>Defaults to: <code>$font-family</code></p></div></div></div><div id='css_var-S-form-toolbar-label-font-size' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-toolbar-label-font-size' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-toolbar-label-font-size' class='name expandable'>$form-toolbar-label-font-size</a><span> : number</span></div><div class='description'><div class='short'>The font-size of toolbar field labels ...</div><div class='long'><p>The font-size of toolbar field labels</p>\n<p>Defaults to: <code>$font-size</code></p></div></div></div><div id='css_var-S-form-toolbar-label-font-weight' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-toolbar-label-font-weight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-toolbar-label-font-weight' class='name expandable'>$form-toolbar-label-font-weight</a><span> : string</span></div><div class='description'><div class='short'>The font-weight of toolbar field labels ...</div><div class='long'><p>The font-weight of toolbar field labels</p>\n<p>Defaults to: <code>normal</code></p></div></div></div><div id='css_var-S-form-toolbar-label-line-height' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.form.Labelable'>Ext.form.Labelable</span><br/><a href='source/Labelable.scss3.html#Ext-form-Labelable-css_var-S-form-toolbar-label-line-height' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.form.Labelable-css_var-S-form-toolbar-label-line-height' class='name expandable'>$form-toolbar-label-line-height</a><span> : number</span></div><div class='description'><div class='short'>The line-height of toolbar field labels ...</div><div class='long'><p>The line-height of toolbar field labels</p>\n<p>Defaults to: <code>round ( $form-toolbar-label-font-size * 1.15 )</code></p></div></div></div></div></div></div></div>","superclasses":["Ext.Base"],"meta":{"docauthor":["Jason Johnston <<EMAIL>>"]},"code_type":"ext_define","requires":["Ext.XTemplate"],"html_meta":{"docauthor":null},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"Labelable.html#Ext-form-Labelable","filename":"Labelable.js"},{"href":"Labelable.scss3.html#Ext-form-Labelable","filename":"Labelable.scss"}],"linenr":1,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.form.Labelable","meta":{"private":true},"name":"autoEl","id":"property-autoEl"},{"tagname":"property","owner":"Ext.form.Labelable","meta":{},"name":"bodyEl","id":"property-bodyEl"},{"tagname":"property","owner":"Ext.form.Labelable","meta":{"private":true},"name":"childEls","id":"property-childEls"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.form.Labelable","meta":{},"name":"errorEl","id":"property-errorEl"},{"tagname":"property","owner":"Ext.form.Labelable","meta":{"private":true},"name":"htmlActiveErrorsTpl","id":"property-htmlActiveErrorsTpl"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.form.Labelable","meta":{"private":true},"name":"inputRowCls","id":"property-inputRowCls"},{"tagname":"property","owner":"Ext.form.Labelable","meta":{},"name":"isFieldLabelable","id":"property-isFieldLabelable"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.form.Labelable","meta":{},"name":"labelCell","id":"property-labelCell"},{"tagname":"property","owner":"Ext.form.Labelable","meta":{},"name":"labelEl","id":"property-labelEl"},{"tagname":"property","owner":"Ext.form.Labelable","meta":{"private":true},"name":"labelableInsertions","id":"property-labelableInsertions"},{"tagname":"property","owner":"Ext.form.Labelable","meta":{"private":true},"name":"labelableRenderProps","id":"property-labelableRenderProps"},{"tagname":"property","owner":"Ext.form.Labelable","meta":{"private":true},"name":"noWrap","id":"property-noWrap"},{"tagname":"property","owner":"Ext.form.Labelable","meta":{"private":true},"name":"plaintextActiveErrorsTpl","id":"property-plaintextActiveErrorsTpl"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"},{"tagname":"property","owner":"Ext.form.Labelable","meta":{"private":true},"name":"xhooks","id":"property-xhooks"}],"cfg":[{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"activeError","id":"cfg-activeError"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"activeErrorsTpl","id":"cfg-activeErrorsTpl"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"afterBodyEl","id":"cfg-afterBodyEl"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"afterLabelTextTpl","id":"cfg-afterLabelTextTpl"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"afterLabelTpl","id":"cfg-afterLabelTpl"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"afterSubTpl","id":"cfg-afterSubTpl"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"autoFitErrors","id":"cfg-autoFitErrors"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"baseBodyCls","id":"cfg-baseBodyCls"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"beforeBodyEl","id":"cfg-beforeBodyEl"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"beforeLabelTextTpl","id":"cfg-beforeLabelTextTpl"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"beforeLabelTpl","id":"cfg-beforeLabelTpl"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"beforeSubTpl","id":"cfg-beforeSubTpl"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"clearCls","id":"cfg-clearCls"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"errorMsgCls","id":"cfg-errorMsgCls"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"fieldBodyCls","id":"cfg-fieldBodyCls"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"fieldLabel","id":"cfg-fieldLabel"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"formItemCls","id":"cfg-formItemCls"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"hideEmptyLabel","id":"cfg-hideEmptyLabel"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"hideLabel","id":"cfg-hideLabel"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"invalidCls","id":"cfg-invalidCls"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"labelAlign","id":"cfg-labelAlign"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"labelAttrTpl","id":"cfg-labelAttrTpl"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"labelCls","id":"cfg-labelCls"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"labelClsExtra","id":"cfg-labelClsExtra"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"labelPad","id":"cfg-labelPad"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"labelSeparator","id":"cfg-labelSeparator"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"labelStyle","id":"cfg-labelStyle"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"labelWidth","id":"cfg-labelWidth"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{"private":true},"name":"labelableRenderTpl","id":"cfg-labelableRenderTpl"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"msgTarget","id":"cfg-msgTarget"},{"tagname":"cfg","owner":"Ext.form.Labelable","meta":{},"name":"preventMark","id":"cfg-preventMark"}],"css_var":[{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-error-icon-height","id":"css_var-S-form-error-icon-height"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-error-icon-side-margin","id":"css_var-S-form-error-icon-side-margin"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-error-icon-width","id":"css_var-S-form-error-icon-width"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-error-msg-color","id":"css_var-S-form-error-msg-color"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-error-msg-font-family","id":"css_var-S-form-error-msg-font-family"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-error-msg-font-size","id":"css_var-S-form-error-msg-font-size"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-error-msg-font-weight","id":"css_var-S-form-error-msg-font-weight"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-error-msg-line-height","id":"css_var-S-form-error-msg-line-height"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-error-under-icon-spacing","id":"css_var-S-form-error-under-icon-spacing"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-error-under-padding","id":"css_var-S-form-error-under-padding"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-item-margin-bottom","id":"css_var-S-form-item-margin-bottom"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-label-font-color","id":"css_var-S-form-label-font-color"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-label-font-family","id":"css_var-S-form-label-font-family"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-label-font-size","id":"css_var-S-form-label-font-size"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-label-font-weight","id":"css_var-S-form-label-font-weight"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-label-line-height","id":"css_var-S-form-label-line-height"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-toolbar-label-color","id":"css_var-S-form-toolbar-label-color"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-toolbar-label-font-family","id":"css_var-S-form-toolbar-label-font-family"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-toolbar-label-font-size","id":"css_var-S-form-toolbar-label-font-size"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-toolbar-label-font-weight","id":"css_var-S-form-toolbar-label-font-weight"},{"tagname":"css_var","owner":"Ext.form.Labelable","meta":{},"name":"$form-toolbar-label-line-height","id":"css_var-S-form-toolbar-label-line-height"}],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{},"name":"getActiveError","id":"method-getActiveError"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{},"name":"getActiveErrors","id":"method-getActiveErrors"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{"private":true},"name":"getBodyColspan","id":"method-getBodyColspan"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{"private":true},"name":"getErrorMsgCls","id":"method-getErrorMsgCls"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{"template":true},"name":"getFieldLabel","id":"method-getFieldLabel"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{},"name":"getInputId","id":"method-getInputId"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{"private":true},"name":"getInsertionRenderData","id":"method-getInsertionRenderData"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{"private":true},"name":"getLabelCellAttrs","id":"method-getLabelCellAttrs"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{"private":true},"name":"getLabelCellStyle","id":"method-getLabelCellStyle"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{"private":true},"name":"getLabelCls","id":"method-getLabelCls"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{"private":true},"name":"getLabelStyle","id":"method-getLabelStyle"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{},"name":"getLabelWidth","id":"method-getLabelWidth"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{"protected":true},"name":"getLabelableRenderData","id":"method-getLabelableRenderData"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{"protected":true},"name":"getSubTplMarkup","id":"method-getSubTplMarkup"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{},"name":"hasActiveError","id":"method-hasActiveError"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{},"name":"hasVisibleLabel","id":"method-hasVisibleLabel"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{},"name":"initLabelable","id":"method-initLabelable"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{"private":true},"name":"renderActiveError","id":"method-renderActiveError"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{},"name":"setActiveError","id":"method-setActiveError"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{},"name":"setActiveErrors","id":"method-setActiveErrors"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{},"name":"setFieldDefaults","id":"method-setFieldDefaults"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{},"name":"setFieldLabel","id":"method-setFieldLabel"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{},"name":"trimLabelSeparator","id":"method-trimLabelSeparator"},{"tagname":"method","owner":"Ext.form.Labelable","meta":{},"name":"unsetActiveError","id":"method-unsetActiveError"}],"event":[{"tagname":"event","owner":"Ext.form.Labelable","meta":{},"name":"errorchange","id":"event-errorchange"}],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.form.Labelable","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.form.Labelable","mixins":[],"mixedInto":["Ext.form.FieldContainer","Ext.form.field.Base"]});