Ext.data.JsonP.Ext_layout_SizePolicy({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/SizePolicy.html#Ext-layout-SizePolicy' target='_blank'>SizePolicy.js</a></div></pre><div class='doc-contents'><p>This class describes how a layout will interact with a component it manages.</p>\n\n<p>There are special instances of this class stored as static properties to avoid object\ninstantiation. All instances of this class should be treated as readonly objects.</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div id='property-readsHeight' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.SizePolicy'>Ext.layout.SizePolicy</span><br/><a href='source/SizePolicy.html#Ext-layout-SizePolicy-property-readsHeight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.SizePolicy-property-readsHeight' class='name not-expandable'>readsHeight</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='readonly signature' >readonly</strong></div><div class='description'><div class='short'><p>Indicates that the <code>height</code> of the component is consumed.</p>\n</div><div class='long'><p>Indicates that the <code>height</code> of the component is consumed.</p>\n</div></div></div><div id='property-readsWidth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.SizePolicy'>Ext.layout.SizePolicy</span><br/><a href='source/SizePolicy.html#Ext-layout-SizePolicy-property-readsWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.SizePolicy-property-readsWidth' class='name not-expandable'>readsWidth</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='readonly signature' >readonly</strong></div><div class='description'><div class='short'><p>Indicates that the <code>width</code> of the component is consumed.</p>\n</div><div class='long'><p>Indicates that the <code>width</code> of the component is consumed.</p>\n</div></div></div><div id='property-setsHeight' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.SizePolicy'>Ext.layout.SizePolicy</span><br/><a href='source/SizePolicy.html#Ext-layout-SizePolicy-property-setsHeight' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.SizePolicy-property-setsHeight' class='name not-expandable'>setsHeight</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='readonly signature' >readonly</strong></div><div class='description'><div class='short'><p>Indicates that the <code>height</code> of the component will be set (i.e., calculated).</p>\n</div><div class='long'><p>Indicates that the <code>height</code> of the component will be set (i.e., calculated).</p>\n</div></div></div><div id='property-setsWidth' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.layout.SizePolicy'>Ext.layout.SizePolicy</span><br/><a href='source/SizePolicy.html#Ext-layout-SizePolicy-property-setsWidth' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.layout.SizePolicy-property-setsWidth' class='name not-expandable'>setsWidth</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='readonly signature' >readonly</strong></div><div class='description'><div class='short'><p>Indicates that the <code>width</code> of the component will be set (i.e., calculated).</p>\n</div><div class='long'><p>Indicates that the <code>width</code> of the component will be set (i.e., calculated).</p>\n</div></div></div></div></div></div></div>","superclasses":[],"meta":{"protected":true},"requires":[],"html_meta":{"protected":null},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"SizePolicy.html#Ext-layout-SizePolicy","filename":"SizePolicy.js"}],"linenr":3,"members":{"property":[{"tagname":"property","owner":"Ext.layout.SizePolicy","meta":{"readonly":true},"name":"readsHeight","id":"property-readsHeight"},{"tagname":"property","owner":"Ext.layout.SizePolicy","meta":{"readonly":true},"name":"readsWidth","id":"property-readsWidth"},{"tagname":"property","owner":"Ext.layout.SizePolicy","meta":{"readonly":true},"name":"setsHeight","id":"property-setsHeight"},{"tagname":"property","owner":"Ext.layout.SizePolicy","meta":{"readonly":true},"name":"setsWidth","id":"property-setsWidth"}],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.layout.SizePolicy","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.layout.SizePolicy","mixins":[],"mixedInto":[]});