Ext.data.JsonP.Ext_data_Model({"alternateClassNames":["Ext.data.Record"],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":["Ext.data.TreeModel","Ext.grid.property.Property"],"extends":"Ext.Base","uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Alternate names</h4><div class='alternate-class-name'>Ext.data.Record</div><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><strong>Ext.data.Model</strong></div></div><h4>Mixins</h4><div class='dependency'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='docClass'>Ext.util.Observable</a></div><h4>Requires</h4><div class='dependency'><a href='#!/api/Ext.ModelManager' rel='Ext.ModelManager' class='docClass'>Ext.ModelManager</a></div><div class='dependency'><a href='#!/api/Ext.data.Errors' rel='Ext.data.Errors' class='docClass'>Ext.data.Errors</a></div><div class='dependency'><a href='#!/api/Ext.data.Field' rel='Ext.data.Field' class='docClass'>Ext.data.Field</a></div><div class='dependency'><a href='#!/api/Ext.data.IdGenerator' rel='Ext.data.IdGenerator' class='docClass'>Ext.data.IdGenerator</a></div><div class='dependency'><a href='#!/api/Ext.data.Operation' rel='Ext.data.Operation' class='docClass'>Ext.data.Operation</a></div><div class='dependency'><a href='#!/api/Ext.data.validations' rel='Ext.data.validations' class='docClass'>Ext.data.validations</a></div><div class='dependency'><a href='#!/api/Ext.util.MixedCollection' rel='Ext.util.MixedCollection' class='docClass'>Ext.util.MixedCollection</a></div><h4>Subclasses</h4><div class='dependency'><a href='#!/api/Ext.data.TreeModel' rel='Ext.data.TreeModel' class='docClass'>Ext.data.TreeModel</a></div><div class='dependency'><a href='#!/api/Ext.grid.property.Property' rel='Ext.grid.property.Property' class='docClass'>Ext.grid.property.Property</a></div><h4>Files</h4><div class='dependency'><a href='source/Model.html#Ext-data-Model' target='_blank'>Model.js</a></div></pre><div class='doc-contents'><p>A Model represents some object that your application manages. For example, one might define a Model for Users,\nProducts, Cars, or any other real-world object that we want to model in the system. Models are registered via the\n<a href=\"#!/api/Ext.ModelManager\" rel=\"Ext.ModelManager\" class=\"docClass\">model manager</a>, and are used by <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">stores</a>, which are in turn used by many\nof the data-bound components in Ext.</p>\n\n<p>Models are defined as a set of fields and any arbitrary methods and properties relevant to the model. For example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('User', {\n    extend: '<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>',\n    fields: [\n        {name: 'name',  type: 'string'},\n        {name: 'age',   type: 'int', convert: null},\n        {name: 'phone', type: 'string'},\n        {name: 'alive', type: 'boolean', defaultValue: true, convert: null}\n    ],\n\n    changeName: function() {\n        var oldName = this.get('name'),\n            newName = oldName + \" The Barbarian\";\n\n        this.set('name', newName);\n    }\n});\n</code></pre>\n\n<p>The fields array is turned into a <a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">MixedCollection</a> automatically by the <a href=\"#!/api/Ext.ModelManager\" rel=\"Ext.ModelManager\" class=\"docClass\">ModelManager</a>, and all other functions and properties are copied to the new Model's prototype.</p>\n\n<p>A Model definition always has an <em>identifying field</em> which should yield a unique key for each instance. By default, a field\nnamed \"id\" will be created with a <a href=\"#!/api/Ext.data.Field-cfg-mapping\" rel=\"Ext.data.Field-cfg-mapping\" class=\"docClass\">mapping</a> of \"id\". This happens because of the default\n<a href=\"#!/api/Ext.data.Model-cfg-idProperty\" rel=\"Ext.data.Model-cfg-idProperty\" class=\"docClass\">idProperty</a> provided in Model definitions.</p>\n\n<p>To alter which field is the identifying field, use the <a href=\"#!/api/Ext.data.Model-cfg-idProperty\" rel=\"Ext.data.Model-cfg-idProperty\" class=\"docClass\">idProperty</a> config.</p>\n\n<p>If the Model should not have any identifying field (for example if you are defining ab abstract base class for your\napplication models), configure the {@liknk <a href=\"#!/api/Ext.data.Model-cfg-idProperty\" rel=\"Ext.data.Model-cfg-idProperty\" class=\"docClass\">idProperty</a>} as <code>null</code>.</p>\n\n<p>By default, the built in numeric and boolean field types have a <a href=\"#!/api/Ext.data.Field-cfg-convert\" rel=\"Ext.data.Field-cfg-convert\" class=\"docClass\">Ext.data.Field.convert</a> function which coerces string\nvalues in raw data into the field's type. For better performance with <a href=\"#!/api/Ext.data.reader.Json\" rel=\"Ext.data.reader.Json\" class=\"docClass\">Json</a> or <a href=\"#!/api/Ext.data.reader.Array\" rel=\"Ext.data.reader.Array\" class=\"docClass\">Array</a>\nreaders <em>if you are in control of the data fed into this Model</em>, you can null out the default convert function which will cause\nthe raw property to be copied directly into the Field's value.</p>\n\n<p>Now we can create instances of our User model and call any model logic we defined:</p>\n\n<pre><code>var user = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('User', {\n    id   : 'ABCD12345',\n    name : 'Conan',\n    age  : 24,\n    phone: '************'\n});\n\nuser.changeName();\nuser.get('name'); //returns \"Conan The Barbarian\"\n</code></pre>\n\n<h1>Validations</h1>\n\n<p>Models have built-in support for validations, which are executed against the validator functions in <a href=\"#!/api/Ext.data.validations\" rel=\"Ext.data.validations\" class=\"docClass\">Ext.data.validations</a> (<a href=\"#!/api/Ext.data.validations\" rel=\"Ext.data.validations\" class=\"docClass\">see all validation functions</a>). Validations are easy to add to\nmodels:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('User', {\n    extend: '<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>',\n    fields: [\n        {name: 'name',     type: 'string'},\n        {name: 'age',      type: 'int'},\n        {name: 'phone',    type: 'string'},\n        {name: 'gender',   type: 'string'},\n        {name: 'username', type: 'string'},\n        {name: 'alive',    type: 'boolean', defaultValue: true}\n    ],\n\n    validations: [\n        {type: 'presence',  field: 'age'},\n        {type: 'length',    field: 'name',     min: 2},\n        {type: 'inclusion', field: 'gender',   list: ['Male', 'Female']},\n        {type: 'exclusion', field: 'username', list: ['Admin', 'Operator']},\n        {type: 'format',    field: 'username', matcher: /([a-z]+)[0-9]{2,3}/}\n    ]\n});\n</code></pre>\n\n<p>The validations can be run by simply calling the <a href=\"#!/api/Ext.data.Model-method-validate\" rel=\"Ext.data.Model-method-validate\" class=\"docClass\">validate</a> function, which returns a <a href=\"#!/api/Ext.data.Errors\" rel=\"Ext.data.Errors\" class=\"docClass\">Ext.data.Errors</a>\nobject:</p>\n\n<pre><code>var instance = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('User', {\n    name: 'Ed',\n    gender: 'Male',\n    username: 'edspencer'\n});\n\nvar errors = instance.validate();\n</code></pre>\n\n<h1>Associations</h1>\n\n<p>Models can have associations with other Models via <a href=\"#!/api/Ext.data.association.HasOne\" rel=\"Ext.data.association.HasOne\" class=\"docClass\">Ext.data.association.HasOne</a>,\n<a href=\"#!/api/Ext.data.association.BelongsTo\" rel=\"Ext.data.association.BelongsTo\" class=\"docClass\">belongsTo</a> and <a href=\"#!/api/Ext.data.association.HasMany\" rel=\"Ext.data.association.HasMany\" class=\"docClass\">hasMany</a> associations.\nFor example, let's say we're writing a blog administration application which deals with Users, Posts and Comments.\nWe can express the relationships between these models like this:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Post', {\n    extend: '<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>',\n    fields: ['id', 'user_id'],\n\n    belongsTo: 'User',\n    hasMany  : {model: 'Comment', name: 'comments'}\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Comment', {\n    extend: '<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>',\n    fields: ['id', 'user_id', 'post_id'],\n\n    belongsTo: 'Post'\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('User', {\n    extend: '<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>',\n    fields: ['id'],\n\n    hasMany: [\n        'Post',\n        {model: 'Comment', name: 'comments'}\n    ]\n});\n</code></pre>\n\n<p>See the docs for <a href=\"#!/api/Ext.data.association.HasOne\" rel=\"Ext.data.association.HasOne\" class=\"docClass\">Ext.data.association.HasOne</a>, <a href=\"#!/api/Ext.data.association.BelongsTo\" rel=\"Ext.data.association.BelongsTo\" class=\"docClass\">Ext.data.association.BelongsTo</a> and\n<a href=\"#!/api/Ext.data.association.HasMany\" rel=\"Ext.data.association.HasMany\" class=\"docClass\">Ext.data.association.HasMany</a> for details on the usage and configuration of associations.\nNote that associations can also be specified like this:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('User', {\n    extend: '<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>',\n    fields: ['id'],\n\n    associations: [\n        {type: 'hasMany', model: 'Post',    name: 'posts'},\n        {type: 'hasMany', model: 'Comment', name: 'comments'}\n    ]\n});\n</code></pre>\n\n<h1>Using a Proxy</h1>\n\n<p>Models are great for representing types of data and relationships, but sooner or later we're going to want to load or\nsave that data somewhere. All loading and saving of data is handled via a <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Proxy</a>, which\ncan be set directly on the Model:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('User', {\n    extend: '<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>',\n    fields: ['id', 'name', 'email'],\n\n    proxy: {\n        type: 'rest',\n        url : '/users'\n    }\n});\n</code></pre>\n\n<p>Here we've set up a <a href=\"#!/api/Ext.data.proxy.Rest\" rel=\"Ext.data.proxy.Rest\" class=\"docClass\">Rest Proxy</a>, which knows how to load and save data to and from a\nRESTful backend. Let's see how this works:</p>\n\n<pre><code>var user = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('User', {name: 'Ed Spencer', email: '<EMAIL>'});\n\nuser.save(); //POST /users\n</code></pre>\n\n<p>Calling <a href=\"#!/api/Ext.data.Model-method-save\" rel=\"Ext.data.Model-method-save\" class=\"docClass\">save</a> on the new Model instance tells the configured RestProxy that we wish to persist this Model's\ndata onto our server. RestProxy figures out that this Model hasn't been saved before because it doesn't have an id,\nand performs the appropriate action - in this case issuing a POST request to the url we configured (/users). We\nconfigure any Proxy on any Model and always follow this API - see <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a> for a full list.</p>\n\n<p>Loading data via the Proxy is equally easy:</p>\n\n<pre><code>//get a reference to the User model class\nvar User = <a href=\"#!/api/Ext.ModelManager-method-getModel\" rel=\"Ext.ModelManager-method-getModel\" class=\"docClass\">Ext.ModelManager.getModel</a>('User');\n\n//Uses the configured RestProxy to make a GET request to /users/123\nUser.load(123, {\n    success: function(user) {\n        console.log(user.getId()); //logs 123\n    }\n});\n</code></pre>\n\n<p>Models can also be updated and destroyed easily:</p>\n\n<pre><code>//the user Model we loaded in the last snippet:\nuser.set('name', 'Edward Spencer');\n\n//tells the Proxy to save the Model. In this case it will perform a PUT request to /users/123 as this Model already has an id\nuser.save({\n    success: function() {\n        console.log('The User was updated');\n    }\n});\n\n//tells the Proxy to destroy the Model. Performs a DELETE request to /users/123\nuser.destroy({\n    success: function() {\n        console.log('The User was destroyed!');\n    }\n});\n</code></pre>\n\n<h1>Usage in Stores</h1>\n\n<p>It is very common to want to load a set of Model instances to be displayed and manipulated in the UI. We do this by\ncreating a <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Store</a>:</p>\n\n<pre><code>var store = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a>', {\n    model: 'User'\n});\n\n//uses the Proxy we set up on Model to load the Store data\nstore.load();\n</code></pre>\n\n<p>A Store is just a collection of Model instances - usually loaded from a server somewhere. Store can also maintain a\nset of added, updated and removed Model instances to be synchronized with the server via the Proxy. See the <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Store docs</a> for more information on Stores.</p>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-cfg'>Config options</h3><div class='subsection'><div id='cfg-associations' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-cfg-associations' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-cfg-associations' class='name not-expandable'>associations</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]</span></div><div class='description'><div class='short'><p>An array of <a href=\"#!/api/Ext.data.association.Association\" rel=\"Ext.data.association.Association\" class=\"docClass\">associations</a> for this model.</p>\n</div><div class='long'><p>An array of <a href=\"#!/api/Ext.data.association.Association\" rel=\"Ext.data.association.Association\" class=\"docClass\">associations</a> for this model.</p>\n</div></div></div><div id='cfg-belongsTo' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-cfg-belongsTo' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-cfg-belongsTo' class='name not-expandable'>belongsTo</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]</span></div><div class='description'><div class='short'><p>One or more <a href=\"#!/api/Ext.data.association.BelongsTo\" rel=\"Ext.data.association.BelongsTo\" class=\"docClass\">BelongsTo associations</a> for this model.</p>\n</div><div class='long'><p>One or more <a href=\"#!/api/Ext.data.association.BelongsTo\" rel=\"Ext.data.association.BelongsTo\" class=\"docClass\">BelongsTo associations</a> for this model.</p>\n</div></div></div><div id='cfg-clientIdProperty' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-cfg-clientIdProperty' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-cfg-clientIdProperty' class='name expandable'>clientIdProperty</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The name of a property that is used for submitting this Model's unique client-side identifier\nto the server when mult...</div><div class='long'><p>The name of a property that is used for submitting this Model's unique client-side identifier\nto the server when multiple phantom records are saved as part of the same <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Operation</a>.\nIn such a case, the server response should include the client id for each record\nso that the server response data can be used to update the client-side records if necessary.\nThis property cannot have the same name as any of this Model's fields.</p>\n</div></div></div><div id='cfg-defaultProxyType' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-cfg-defaultProxyType' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-cfg-defaultProxyType' class='name expandable'>defaultProxyType</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span></div><div class='description'><div class='short'>The string type of the default Model Proxy. ...</div><div class='long'><p>The string type of the default Model Proxy. Defaults to 'ajax'.</p>\n<p>Defaults to: <code>'ajax'</code></p></div></div></div><div id='cfg-fields' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-cfg-fields' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-cfg-fields' class='name expandable'>fields</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</span></div><div class='description'><div class='short'>The fields for this model. ...</div><div class='long'><p>The fields for this model. This is an Array of <strong><a href=\"#!/api/Ext.data.Field\" rel=\"Ext.data.Field\" class=\"docClass\">Field</a></strong> definition objects. A Field\ndefinition may simply be the <em>name</em> of the Field, but a Field encapsulates <a href=\"#!/api/Ext.data.Field-cfg-type\" rel=\"Ext.data.Field-cfg-type\" class=\"docClass\">data type</a>,\n<a href=\"#!/api/Ext.data.Field-cfg-convert\" rel=\"Ext.data.Field-cfg-convert\" class=\"docClass\">custom conversion</a> of raw data, and a <a href=\"#!/api/Ext.data.Field-cfg-mapping\" rel=\"Ext.data.Field-cfg-mapping\" class=\"docClass\">mapping</a>\nproperty to specify by name of index, how to extract a field's value from a raw data object, so it is best practice\nto specify a full set of <a href=\"#!/api/Ext.data.Field\" rel=\"Ext.data.Field\" class=\"docClass\">Field</a> config objects.</p>\n</div></div></div><div id='cfg-hasMany' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-cfg-hasMany' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-cfg-hasMany' class='name not-expandable'>hasMany</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]</span></div><div class='description'><div class='short'><p>One or more <a href=\"#!/api/Ext.data.association.HasMany\" rel=\"Ext.data.association.HasMany\" class=\"docClass\">HasMany associations</a> for this model.</p>\n</div><div class='long'><p>One or more <a href=\"#!/api/Ext.data.association.HasMany\" rel=\"Ext.data.association.HasMany\" class=\"docClass\">HasMany associations</a> for this model.</p>\n</div></div></div><div id='cfg-idProperty' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-cfg-idProperty' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-cfg-idProperty' class='name expandable'>idProperty</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Ext.data.Field\" rel=\"Ext.data.Field\" class=\"docClass\">Ext.data.Field</a></span></div><div class='description'><div class='short'>The name of the field treated as this Model's unique id. ...</div><div class='long'><p>The name of the field treated as this Model's unique id. Defaults to 'id'.</p>\n\n<p>This may also be specified as a Field config object. This means that the identifying field can be calculated\nusing a <a href=\"#!/api/Ext.data.Field-cfg-convert\" rel=\"Ext.data.Field-cfg-convert\" class=\"docClass\">convert</a> function which might aggregate several values from the\nraw data object to use as an identifier.</p>\n\n<p>The resulting <a href=\"#!/api/Ext.data.Field\" rel=\"Ext.data.Field\" class=\"docClass\">Field</a> is added to the Model's field collection unless there is already\na configured field with a mapping that reads the same property.</p>\n\n<p>If defining an <strong>abstract</strong> base Model class, the <code>idProperty</code> may be configured as <code>null</code> which will mean that\nno identifying field will be generated.</p>\n<p>Defaults to: <code>'id'</code></p></div></div></div><div id='cfg-idgen' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-cfg-idgen' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-cfg-idgen' class='name expandable'>idgen</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>The id generator to use for this model. ...</div><div class='long'><p>The id generator to use for this model. The default id generator does not generate\nvalues for the <a href=\"#!/api/Ext.data.Model-cfg-idProperty\" rel=\"Ext.data.Model-cfg-idProperty\" class=\"docClass\">idProperty</a>.</p>\n\n<p>This can be overridden at the model level to provide a custom generator for a model.\nThe simplest form of this would be:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('MyApp.data.MyModel', {\n     extend: '<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>',\n     requires: ['<a href=\"#!/api/Ext.data.SequentialIdGenerator\" rel=\"Ext.data.SequentialIdGenerator\" class=\"docClass\">Ext.data.SequentialIdGenerator</a>'],\n     idgen: 'sequential',\n     ...\n });\n</code></pre>\n\n<p>The above would generate <a href=\"#!/api/Ext.data.SequentialIdGenerator\" rel=\"Ext.data.SequentialIdGenerator\" class=\"docClass\">sequential</a> id's such\nas 1, 2, 3 etc..</p>\n\n<p>Another useful id generator is <a href=\"#!/api/Ext.data.UuidGenerator\" rel=\"Ext.data.UuidGenerator\" class=\"docClass\">Ext.data.UuidGenerator</a>:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('MyApp.data.MyModel', {\n     extend: '<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>',\n     requires: ['<a href=\"#!/api/Ext.data.UuidGenerator\" rel=\"Ext.data.UuidGenerator\" class=\"docClass\">Ext.data.UuidGenerator</a>'],\n     idgen: 'uuid',\n     ...\n });\n</code></pre>\n\n<p>An id generation can also be further configured:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('MyApp.data.MyModel', {\n     extend: '<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>',\n     idgen: {\n         type: 'sequential',\n         seed: 1000,\n         prefix: 'ID_'\n     }\n });\n</code></pre>\n\n<p>The above would generate id's such as ID_1000, ID_1001, ID_1002 etc..</p>\n\n<p>If multiple models share an id space, a single generator can be shared:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('MyApp.data.MyModelX', {\n     extend: '<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>',\n     idgen: {\n         type: 'sequential',\n         id: 'xy'\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('MyApp.data.MyModelY', {\n     extend: '<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>',\n     idgen: {\n         type: 'sequential',\n         id: 'xy'\n     }\n });\n</code></pre>\n\n<p>For more complex, shared id generators, a custom generator is the best approach.\nSee <a href=\"#!/api/Ext.data.IdGenerator\" rel=\"Ext.data.IdGenerator\" class=\"docClass\">Ext.data.IdGenerator</a> for details on creating custom id generators.</p>\n</div></div></div><div id='cfg-listeners' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-cfg-listeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-cfg-listeners' class='name expandable'>listeners</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'>A config object containing one or more event handlers to be added to this object during initialization. ...</div><div class='long'><p>A config object containing one or more event handlers to be added to this object during initialization. This\nshould be a valid listeners config object as specified in the <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> example for attaching multiple\nhandlers at once.</p>\n\n<p><strong>DOM events from Ext JS <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a></strong></p>\n\n<p>While <em>some</em> Ext JS Component classes export selected DOM events (e.g. \"click\", \"mouseover\" etc), this is usually\nonly done when extra value can be added. For example the <a href=\"#!/api/Ext.view.View\" rel=\"Ext.view.View\" class=\"docClass\">DataView</a>'s <strong><code><a href=\"#!/api/Ext.view.View-event-itemclick\" rel=\"Ext.view.View-event-itemclick\" class=\"docClass\">itemclick</a></code></strong> event passing the node clicked on. To access DOM events directly from a\nchild element of a Component, we need to specify the <code>element</code> option to identify the Component property to add a\nDOM listener to:</p>\n\n<pre><code>new <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>({\n    width: 400,\n    height: 200,\n    dockedItems: [{\n        xtype: 'toolbar'\n    }],\n    listeners: {\n        click: {\n            element: 'el', //bind to the underlying el property on the panel\n            fn: function(){ console.log('click el'); }\n        },\n        dblclick: {\n            element: 'body', //bind to the underlying body property on the panel\n            fn: function(){ console.log('dblclick body'); }\n        }\n    }\n});\n</code></pre>\n</div></div></div><div id='cfg-persistenceProperty' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-cfg-persistenceProperty' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-cfg-persistenceProperty' class='name expandable'>persistenceProperty</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='deprecated signature' >deprecated</strong></div><div class='description'><div class='short'>The name of the property on this Persistable object that its data is saved to. ...</div><div class='long'><p>The name of the property on this Persistable object that its data is saved to. Defaults to 'data'\n(i.e: all persistable data resides in <code>this.data</code>.)</p>\n<p>Defaults to: <code>'data'</code></p>        <div class='signature-box deprecated'>\n        <p>This cfg has been <strong>deprecated</strong> </p>\n        <p>This config is deprecated. In future this will no longer be configurable and will be data.</p>\n\n        </div>\n</div></div></div><div id='cfg-proxy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-cfg-proxy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-cfg-proxy' class='name not-expandable'>proxy</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a></span></div><div class='description'><div class='short'><p>The <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">proxy</a> to use for this model.</p>\n</div><div class='long'><p>The <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">proxy</a> to use for this model.</p>\n</div></div></div><div id='cfg-validations' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-cfg-validations' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-cfg-validations' class='name not-expandable'>validations</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]</span></div><div class='description'><div class='short'><p>An array of <a href=\"#!/api/Ext.data.validations\" rel=\"Ext.data.validations\" class=\"docClass\">validations</a> for this model.</p>\n</div><div class='long'><p>An array of <a href=\"#!/api/Ext.data.validations\" rel=\"Ext.data.validations\" class=\"docClass\">validations</a> for this model.</p>\n</div></div></div></div></div><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-_singleProp' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-property-_singleProp' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-property-_singleProp' class='name expandable'>_singleProp</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>This object is used whenever the set() method is called and given a string as the\nfirst argument. ...</div><div class='long'><p>This object is used whenever the set() method is called and given a string as the\nfirst argument. This approach saves memory (and GC costs) since we could be called\na lot.</p>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-dirty' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-property-dirty' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-property-dirty' class='name expandable'>dirty</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='readonly signature' >readonly</strong></div><div class='description'><div class='short'>True if this Record has been modified. ...</div><div class='long'><p>True if this Record has been modified.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-editing' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-property-editing' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-property-editing' class='name expandable'>editing</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='readonly signature' >readonly</strong></div><div class='description'><div class='short'>Internal flag used to track whether or not the model instance is currently being edited. ...</div><div class='long'><p>Internal flag used to track whether or not the model instance is currently being edited.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-emptyData' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-property-emptyData' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-property-emptyData' class='name expandable'>emptyData</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Used as a dummy source array when constructor is called with no args ...</div><div class='long'><p>Used as a dummy source array when constructor is called with no args</p>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-evented' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-property-evented' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-property-evented' class='name expandable'>evented</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-eventsSuspended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-property-eventsSuspended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-property-eventsSuspended' class='name expandable'>eventsSuspended</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Initial suspended call count. ...</div><div class='long'><p>Initial suspended call count. Incremented when <a href=\"#!/api/Ext.util.Observable-method-suspendEvents\" rel=\"Ext.util.Observable-method-suspendEvents\" class=\"docClass\">suspendEvents</a> is called, decremented when <a href=\"#!/api/Ext.util.Observable-method-resumeEvents\" rel=\"Ext.util.Observable-method-resumeEvents\" class=\"docClass\">resumeEvents</a> is called.</p>\n<p>Defaults to: <code>0</code></p></div></div></div><div id='property-fields' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-property-fields' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-property-fields' class='name expandable'>fields</a><span> : <a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">Ext.util.MixedCollection</a></span></div><div class='description'><div class='short'>A Collection of the fields defined for this Model (including fields defined in superclasses)\n\nThis is a collection of...</div><div class='long'><p>A <a href=\"#!/api/Ext.util.MixedCollection\" rel=\"Ext.util.MixedCollection\" class=\"docClass\">Collection</a> of the fields defined for this Model (including fields defined in superclasses)</p>\n\n<p>This is a collection of <a href=\"#!/api/Ext.data.Field\" rel=\"Ext.data.Field\" class=\"docClass\">Ext.data.Field</a> instances, each of which encapsulates information that the field was configured with.\nBy default, you can specify a field as simply a String, representing the <em>name</em> of the field, but a Field encapsulates\n<a href=\"#!/api/Ext.data.Field-cfg-type\" rel=\"Ext.data.Field-cfg-type\" class=\"docClass\">data type</a>, <a href=\"#!/api/Ext.data.Field-cfg-convert\" rel=\"Ext.data.Field-cfg-convert\" class=\"docClass\">custom conversion</a> of raw data, and a <a href=\"#!/api/Ext.data.Field-cfg-mapping\" rel=\"Ext.data.Field-cfg-mapping\" class=\"docClass\">mapping</a>\nproperty to specify by name of index, how to extract a field's value from a raw data object.</p>\n</div></div></div><div id='property-hasListeners' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-property-hasListeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-property-hasListeners' class='name expandable'>hasListeners</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='readonly signature' >readonly</strong></div><div class='description'><div class='short'>This object holds a key for any event that has a listener. ...</div><div class='long'><p>This object holds a key for any event that has a listener. The listener may be set\ndirectly on the instance, or on its class or a super class (via <a href=\"#!/api/Ext.util.Observable-static-method-observe\" rel=\"Ext.util.Observable-static-method-observe\" class=\"docClass\">observe</a>) or\non the <a href=\"#!/api/Ext.app.EventBus\" rel=\"Ext.app.EventBus\" class=\"docClass\">MVC EventBus</a>. The values of this object are truthy\n(a non-zero number) and falsy (0 or undefined). They do not represent an exact count\nof listeners. The value for an event is truthy if the event must be fired and is\nfalsy if there is no need to fire the event.</p>\n\n<p>The intended use of this property is to avoid the expense of fireEvent calls when\nthere are no listeners. This can be particularly helpful when one would otherwise\nhave to call fireEvent hundreds or thousands of times. It is used like this:</p>\n\n<pre><code> if (this.hasListeners.foo) {\n     this.fireEvent('foo', this, arg1);\n }\n</code></pre>\n</div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-internalId' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-property-internalId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-property-internalId' class='name not-expandable'>internalId</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'><p>An internal unique ID for each Model instance, used to identify Models that don't have an ID yet</p>\n</div><div class='long'><p>An internal unique ID for each Model instance, used to identify Models that don't have an ID yet</p>\n</div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isModel' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-property-isModel' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-property-isModel' class='name expandable'>isModel</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true in this class to identify an object as an instantiated Model, or subclass thereof. ...</div><div class='long'><p><code>true</code> in this class to identify an object as an instantiated Model, or subclass thereof.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-isObservable' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-property-isObservable' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-property-isObservable' class='name expandable'>isObservable</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>true in this class to identify an object as an instantiated Observable, or subclass thereof. ...</div><div class='long'><p><code>true</code> in this class to identify an object as an instantiated Observable, or subclass thereof.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-modified' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-property-modified' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-property-modified' class='name not-expandable'>modified</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'><p>Key: value pairs of all fields whose values have changed</p>\n</div><div class='long'><p>Key: value pairs of all fields whose values have changed</p>\n</div></div></div><div id='property-phantom' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-property-phantom' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-property-phantom' class='name expandable'>phantom</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span></div><div class='description'><div class='short'>True when the record does not yet exist in a server-side database (see setDirty). ...</div><div class='long'><p>True when the record does not yet exist in a server-side database (see <a href=\"#!/api/Ext.data.Model-method-setDirty\" rel=\"Ext.data.Model-method-setDirty\" class=\"docClass\">setDirty</a>).\nAny record which has a real database pk set as its id property is NOT a phantom -- it's real.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='property-raw' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-property-raw' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-property-raw' class='name not-expandable'>raw</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span></div><div class='description'><div class='short'><p>The raw data used to create this model if created via a reader.</p>\n</div><div class='long'><p>The raw data used to create this model if created via a reader.</p>\n</div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div><div id='property-store' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-property-store' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-property-store' class='name expandable'>store</a><span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a></span></div><div class='description'><div class='short'>The Store to which this instance belongs. ...</div><div class='long'><p>The <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Store</a> to which this instance belongs. NOTE: If this\ninstance is bound to multiple stores, this property will reference only the\nfirst. To examine all the stores, use the <a href=\"#!/api/Ext.data.Model-property-stores\" rel=\"Ext.data.Model-property-stores\" class=\"docClass\">stores</a> property instead.</p>\n</div></div></div><div id='property-stores' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-property-stores' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-property-stores' class='name not-expandable'>stores</a><span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a>[]</span></div><div class='description'><div class='short'><p>The <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Stores</a> to which this instance is bound.</p>\n</div><div class='long'><p>The <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Stores</a> to which this instance is bound.</p>\n</div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='static-property-AUTO_ID' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-static-property-AUTO_ID' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-static-property-AUTO_ID' class='name expandable'>AUTO_ID</a><span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>1</code></p></div></div></div><div id='static-property-COMMIT' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-static-property-COMMIT' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-static-property-COMMIT' class='name expandable'>COMMIT</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='static signature' >static</strong></div><div class='description'><div class='short'>The update operation of type 'commit'. ...</div><div class='long'><p>The update operation of type 'commit'. Used by <a href=\"#!/api/Ext.data.Store-event-update\" rel=\"Ext.data.Store-event-update\" class=\"docClass\">Store.update</a> event.</p>\n<p>Defaults to: <code>'commit'</code></p></div></div></div><div id='static-property-EDIT' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-static-property-EDIT' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-static-property-EDIT' class='name expandable'>EDIT</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='static signature' >static</strong></div><div class='description'><div class='short'>The update operation of type 'edit'. ...</div><div class='long'><p>The update operation of type 'edit'. Used by <a href=\"#!/api/Ext.data.Store-event-update\" rel=\"Ext.data.Store-event-update\" class=\"docClass\">Store.update</a> event.</p>\n<p>Defaults to: <code>'edit'</code></p></div></div></div><div id='static-property-PREFIX' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-static-property-PREFIX' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-static-property-PREFIX' class='name expandable'>PREFIX</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'ext-record'</code></p></div></div></div><div id='static-property-REJECT' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-static-property-REJECT' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-static-property-REJECT' class='name expandable'>REJECT</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='static signature' >static</strong></div><div class='description'><div class='short'>The update operation of type 'reject'. ...</div><div class='long'><p>The update operation of type 'reject'. Used by <a href=\"#!/api/Ext.data.Store-event-update\" rel=\"Ext.data.Store-event-update\" class=\"docClass\">Store.update</a> event.</p>\n<p>Defaults to: <code>'reject'</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-constructor' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-constructor' target='_blank' class='view-source'>view source</a></div><strong class='new-keyword'>new</strong><a href='#!/api/Ext.data.Model-method-constructor' class='name expandable'>Ext.data.Model</a>( <span class='pre'>data</span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></div><div class='description'><div class='short'>Creates new Model instance. ...</div><div class='long'><p>Creates new Model instance.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>data</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>An object containing keys corresponding to this model's fields, and their associated values</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-addEvents' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-addEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-addEvents' class='name expandable'>addEvents</a>( <span class='pre'>eventNames</span> )</div><div class='description'><div class='short'>Adds the specified events to the list of events which this Observable may fire. ...</div><div class='long'><p>Adds the specified events to the list of events which this Observable may fire.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventNames</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>...<div class='sub-desc'><p>Either an object with event names as properties with\na value of <code>true</code>. For example:</p>\n\n<pre><code>this.addEvents({\n    storeloaded: true,\n    storecleared: true\n});\n</code></pre>\n\n<p>Or any number of event names as separate parameters. For example:</p>\n\n<pre><code>this.addEvents('storeloaded', 'storecleared');\n</code></pre>\n</div></li></ul></div></div></div><div id='method-addListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-addListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-addListener' class='name expandable'>addListener</a>( <span class='pre'>eventName, [fn], [scope], [options]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Appends an event handler to this object. ...</div><div class='long'><p>Appends an event handler to this object.  For example:</p>\n\n<pre><code>myGridPanel.on(\"mouseover\", this.onMouseOver, this);\n</code></pre>\n\n<p>The method also allows for a single argument to be passed which is a config object\ncontaining properties which specify multiple events. For example:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: this.onCellClick,\n    mouseover: this.onMouseOver,\n    mouseout: this.onMouseOut,\n    scope: this // Important. Ensure \"this\" is correct during handler execution\n});\n</code></pre>\n\n<p>One can also specify options for each event handler separately:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: this.onCellClick, scope: this, single: true},\n    mouseover: {fn: panel.onMouseOver, scope: panel}\n});\n</code></pre>\n\n<p><em>Names</em> of methods in a specified scope may also be used. Note that\n<code>scope</code> MUST be specified to use this option:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: 'onCellClick', scope: this, single: true},\n    mouseover: {fn: 'onMouseOver', scope: panel}\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The name of the event to listen for.\nMay also be an object who's property names are event names.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The method the event invokes, or <em>if <code>scope</code> is specified, the </em>name* of the method within\nthe specified <code>scope</code>.  Will be called with arguments\ngiven to <a href=\"#!/api/Ext.util.Observable-method-fireEvent\" rel=\"Ext.util.Observable-method-fireEvent\" class=\"docClass\">fireEvent</a> plus the <code>options</code> parameter described below.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is\nexecuted. <strong>If omitted, defaults to the object which fired the event.</strong></p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>An object containing handler configuration.</p>\n\n\n\n\n<p><strong>Note:</strong> Unlike in ExtJS 3.x, the options object will also be passed as the last\nargument to every event handler.</p>\n\n\n\n\n<p>This object may contain any of the following properties:</p>\n\n<ul><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is executed. <strong>If omitted,\n  defaults to the object which fired the event.</strong></p>\n\n</div></li><li><span class='pre'>delay</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of milliseconds to delay the invocation of the handler after the event fires.</p>\n\n</div></li><li><span class='pre'>single</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to add a handler to handle just the next firing of the event, and then remove itself.</p>\n\n</div></li><li><span class='pre'>buffer</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Causes the handler to be scheduled to run in an <a href=\"#!/api/Ext.util.DelayedTask\" rel=\"Ext.util.DelayedTask\" class=\"docClass\">Ext.util.DelayedTask</a> delayed\n  by the specified number of milliseconds. If the event fires again within that time,\n  the original handler is <em>not</em> invoked, but the new handler is scheduled in its place.</p>\n\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a><div class='sub-desc'><p>Only call the handler if the event was fired on the target Observable, <em>not</em> if the event\n  was bubbled up from a child Observable.</p>\n\n</div></li><li><span class='pre'>element</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p><strong>This option is only valid for listeners bound to <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a>.</strong>\n  The name of a Component property which references an element to add a listener to.</p>\n\n\n\n\n<p>  This option is useful during Component construction to add DOM event listeners to elements of\n  <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a> which will exist only after the Component is rendered.\n  For example, to add a click listener to a Panel's body:</p>\n\n\n\n\n<pre><code>  new <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>({\n      title: 'The title',\n      listeners: {\n          click: this.handlePanelClick,\n          element: 'body'\n      }\n  });\n</code></pre>\n\n</div></li><li><span class='pre'>destroyable</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>When specified as <code>true</code>, the function returns A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call.</p>\n\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>priority</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>An optional numeric priority that determines the order in which event handlers\n  are run. Event handlers with no priority will be run as if they had a priority\n  of 0. Handlers with a higher priority will be prioritized to run sooner than\n  those with a lower priority.  Negative numbers can be used to set a priority\n  lower than the default. Internally, the framework uses a range of 1000 or\n  greater, and -1000 or lesser for handers that are intended to run before or\n  after all others, so it is recommended to stay within the range of -999 to 999\n  when setting the priority of event handlers in application-level code.</p>\n\n\n\n\n<p><strong>Combining Options</strong></p>\n\n\n\n\n<p>Using the options argument, it is possible to combine different types of listeners:</p>\n\n\n\n\n<p>A delayed, one-time listener.</p>\n\n\n\n\n<pre><code>myPanel.on('hide', this.handleClick, this, {\n    single: true,\n    delay: 100\n});\n</code></pre>\n\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><strong>Only when the <code>destroyable</code> option is specified. </strong></p>\n\n\n\n\n<p> A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call. For example:</p>\n\n\n\n\n<pre><code>this.btnListeners =  = myButton.on({\n    destroyable: true\n    mouseover:   function() { console.log('mouseover'); },\n    mouseout:    function() { console.log('mouseout'); },\n    click:       function() { console.log('click'); }\n});\n</code></pre>\n\n\n\n\n<p>And when those listeners need to be removed:</p>\n\n\n\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.btnListeners);\n</code></pre>\n\n\n\n\n<p>or</p>\n\n\n\n\n<pre><code>this.btnListeners.destroy();\n</code></pre>\n\n</div></li></ul></div></div></div><div id='method-addManagedListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-addManagedListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-addManagedListener' class='name expandable'>addManagedListener</a>( <span class='pre'>item, ename, [fn], [scope], [options]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Adds listeners to any Observable object (or Ext.Element) which are automatically removed when this Component is\ndestr...</div><div class='long'><p>Adds listeners to any Observable object (or <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>) which are automatically removed when this Component is\ndestroyed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The item to which to add a listener/listeners.</p>\n\n</div></li><li><span class='pre'>ename</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The event name, or an object containing event name properties.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the handler function.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the scope (<code>this</code> reference)\nin which the handler function is executed.</p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the\n<a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> options.</p>\n\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><strong>Only when the <code>destroyable</code> option is specified. </strong></p>\n\n\n\n\n<p> A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call. For example:</p>\n\n\n\n\n<pre><code>this.btnListeners =  = myButton.mon({\n    destroyable: true\n    mouseover:   function() { console.log('mouseover'); },\n    mouseout:    function() { console.log('mouseout'); },\n    click:       function() { console.log('click'); }\n});\n</code></pre>\n\n\n\n\n<p>And when those listeners need to be removed:</p>\n\n\n\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.btnListeners);\n</code></pre>\n\n\n\n\n<p>or</p>\n\n\n\n\n<pre><code>this.btnListeners.destroy();\n</code></pre>\n\n</div></li></ul></div></div></div><div id='method-afterCommit' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-afterCommit' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-afterCommit' class='name expandable'>afterCommit</a>( <span class='pre'>[modifiedFieldNames]</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>If this Model instance has been joined to a store, the store's\nafterCommit method is called, ...</div><div class='long'><p>If this Model instance has been <a href=\"#!/api/Ext.data.Model-method-join\" rel=\"Ext.data.Model-method-join\" class=\"docClass\">joined</a> to a <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">store</a>, the store's\nafterCommit method is called,</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>modifiedFieldNames</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[] (optional)<div class='sub-desc'><p>Array of field names changed by syncing this field with the server.</p>\n</div></li></ul></div></div></div><div id='method-afterEdit' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-afterEdit' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-afterEdit' class='name expandable'>afterEdit</a>( <span class='pre'>[modifiedFieldNames]</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>If this Model instance has been joined to a store, the store's\nafterEdit method is called. ...</div><div class='long'><p>If this Model instance has been <a href=\"#!/api/Ext.data.Model-method-join\" rel=\"Ext.data.Model-method-join\" class=\"docClass\">joined</a> to a <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">store</a>, the store's\nafterEdit method is called.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>modifiedFieldNames</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[] (optional)<div class='sub-desc'><p>Array of field names changed during edit.</p>\n</div></li></ul></div></div></div><div id='method-afterReject' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-afterReject' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-afterReject' class='name expandable'>afterReject</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>If this Model instance has been joined to a store, the store's\nafterReject method is called. ...</div><div class='long'><p>If this Model instance has been <a href=\"#!/api/Ext.data.Model-method-join\" rel=\"Ext.data.Model-method-join\" class=\"docClass\">joined</a> to a <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">store</a>, the store's\nafterReject method is called.</p>\n</div></div></div><div id='method-beginEdit' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-beginEdit' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-beginEdit' class='name expandable'>beginEdit</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Begins an edit. ...</div><div class='long'><p>Begins an edit. While in edit mode, no events (e.g.. the <code>update</code> event) are relayed to the containing store.\nWhen an edit has begun, it must be followed by either <a href=\"#!/api/Ext.data.Model-method-endEdit\" rel=\"Ext.data.Model-method-endEdit\" class=\"docClass\">endEdit</a> or <a href=\"#!/api/Ext.data.Model-method-cancelEdit\" rel=\"Ext.data.Model-method-cancelEdit\" class=\"docClass\">cancelEdit</a>.</p>\n</div></div></div><div id='method-callOverridden' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callStore' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-callStore' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-callStore' class='name expandable'>callStore</a>( <span class='pre'>fn</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Helper function used by afterEdit, afterReject and afterCommit. ...</div><div class='long'><p>Helper function used by afterEdit, afterReject and afterCommit. Calls the given method on the\n<a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">store</a> that this instance has <a href=\"#!/api/Ext.data.Model-method-join\" rel=\"Ext.data.Model-method-join\" class=\"docClass\">joined</a>, if any. The store function\nwill always be called with the model instance as its single argument. If this model is joined to\na <a href=\"#!/api/Ext.data.NodeStore\" rel=\"Ext.data.NodeStore\" class=\"docClass\">Ext.data.NodeStore</a>, then this method calls the given method on the NodeStore and the associated <a href=\"#!/api/Ext.data.TreeStore\" rel=\"Ext.data.TreeStore\" class=\"docClass\">Ext.data.TreeStore</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The function to call on the store</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-cancelEdit' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-cancelEdit' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-cancelEdit' class='name expandable'>cancelEdit</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Cancels all changes made in the current edit operation. ...</div><div class='long'><p>Cancels all changes made in the current edit operation.</p>\n</div></div></div><div id='method-captureArgs' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-captureArgs' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-captureArgs' class='name expandable'>captureArgs</a>( <span class='pre'>o, fn, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>o</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-changeId' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-changeId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-changeId' class='name expandable'>changeId</a>( <span class='pre'>oldId, newId</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>oldId</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>newId</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-clearListeners' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-clearListeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-clearListeners' class='name expandable'>clearListeners</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Removes all listeners for this object including the managed listeners ...</div><div class='long'><p>Removes all listeners for this object including the managed listeners</p>\n</div></div></div><div id='method-clearManagedListeners' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-clearManagedListeners' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-clearManagedListeners' class='name expandable'>clearManagedListeners</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Removes all managed listeners for this object. ...</div><div class='long'><p>Removes all managed listeners for this object.</p>\n</div></div></div><div id='method-commit' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-commit' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-commit' class='name expandable'>commit</a>( <span class='pre'>[silent], [modifiedFieldNames]</span> )</div><div class='description'><div class='short'>Usually called by the Ext.data.Store which owns the model instance. ...</div><div class='long'><p>Usually called by the <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a> which owns the model instance. Commits all changes made to the\ninstance since either creation or the last commit operation.</p>\n\n<p>Developers should subscribe to the <a href=\"#!/api/Ext.data.Store-event-update\" rel=\"Ext.data.Store-event-update\" class=\"docClass\">Ext.data.Store.update</a> event to have their code notified of commit\noperations.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>silent</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>Pass <code>true</code> to skip notification of the owning store of the change.</p>\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>modifiedFieldNames</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[] (optional)<div class='sub-desc'><p>Array of field names changed during sync with server if known.\nOmit or pass <code>null</code> if unknown. An empty array means that it is known that no fields were modified\nby the server's response.\nDefaults to false.</p>\n</div></li></ul></div></div></div><div id='method-compareConvertFields' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-compareConvertFields' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-compareConvertFields' class='name expandable'>compareConvertFields</a>( <span class='pre'>f1, f2</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>f1</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>f2</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-continueFireEvent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-continueFireEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-continueFireEvent' class='name expandable'>continueFireEvent</a>( <span class='pre'>eventName, args, bubbles</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Continue to fire event. ...</div><div class='long'><p>Continue to fire event.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'>\n</div></li><li><span class='pre'>bubbles</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-copy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-copy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-copy' class='name expandable'>copy</a>( <span class='pre'>[id]</span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></div><div class='description'><div class='short'>Creates a copy (clone) of this Model instance. ...</div><div class='long'><p>Creates a copy (clone) of this Model instance.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>A new id, defaults to the id of the instance being copied.\nSee <code><a href=\"#!/api/Ext.data.Model-static-method-id\" rel=\"Ext.data.Model-static-method-id\" class=\"docClass\">id</a></code>. To generate a phantom instance with a new id use:</p>\n\n<pre><code>var rec = record.copy(); // clone the record\n<a href=\"#!/api/Ext.data.Model-static-method-id\" rel=\"Ext.data.Model-static-method-id\" class=\"docClass\">Ext.data.Model.id</a>(rec); // automatically generate a unique sequential id\n</code></pre>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-copyFrom' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-copyFrom' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-copyFrom' class='name expandable'>copyFrom</a>( <span class='pre'>sourceRecord</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Copies data from the passed record into this record. ...</div><div class='long'><p>Copies data from the passed record into this record. If the passed record is undefined, does nothing.</p>\n\n<p>If this is a phantom record (represented only in the client, with no corresponding database entry), and\nthe source record is not a phantom, then this record acquires the id of the source record.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>sourceRecord</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The record to copy data from.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</span><div class='sub-desc'><p>The names of the fields which changed value.</p>\n</div></li></ul></div></div></div><div id='method-createRelayer' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-createRelayer' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-createRelayer' class='name expandable'>createRelayer</a>( <span class='pre'>newName, [beginEnd]</span> ) : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Creates an event handling function which refires the event from this object as the passed event name. ...</div><div class='long'><p>Creates an event handling function which refires the event from this object as the passed event name.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>newName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name under which to refire the passed parameters.</p>\n</div></li><li><span class='pre'>beginEnd</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a> (optional)<div class='sub-desc'><p>The caller can specify on which indices to slice.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-destroy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-destroy' class='name expandable'>destroy</a>( <span class='pre'>options</span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></div><div class='description'><div class='short'>Destroys the model using the configured proxy. ...</div><div class='long'><p>Destroys the model using the configured proxy.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>Options to pass to the proxy. Config object for <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></span><div class='sub-desc'><p>The Model instance</p>\n</div></li></ul><p>Overrides: <a href='#!/api/Ext.Base-method-destroy' rel='Ext.Base-method-destroy' class='docClass'>Ext.Base.destroy</a></p></div></div></div><div id='method-enableBubble' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-enableBubble' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-enableBubble' class='name expandable'>enableBubble</a>( <span class='pre'>eventNames</span> )</div><div class='description'><div class='short'>Enables events fired by this Observable to bubble up an owner hierarchy by calling this.getBubbleTarget() if\npresent. ...</div><div class='long'><p>Enables events fired by this Observable to bubble up an owner hierarchy by calling <code>this.getBubbleTarget()</code> if\npresent. There is no implementation in the Observable base class.</p>\n\n<p>This is commonly used by Ext.Components to bubble events to owner Containers.\nSee <a href=\"#!/api/Ext.Component-method-getBubbleTarget\" rel=\"Ext.Component-method-getBubbleTarget\" class=\"docClass\">Ext.Component.getBubbleTarget</a>. The default implementation in <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Ext.Component</a> returns the\nComponent's immediate owner. But if a known target is required, this can be overridden to access the\nrequired target more quickly.</p>\n\n<p>Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.overrides.form.field.Base', {\n    override: '<a href=\"#!/api/Ext.form.field.Base\" rel=\"Ext.form.field.Base\" class=\"docClass\">Ext.form.field.Base</a>',\n\n    //  Add functionality to Field's initComponent to enable the change event to bubble\n    initComponent: function () {\n        this.callParent();\n        this.enableBubble('change');\n    }\n});\n\nvar myForm = <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">Ext.create</a>('<a href=\"#!/api/Ext.form.Panel\" rel=\"Ext.form.Panel\" class=\"docClass\">Ext.form.Panel</a>', {\n    title: 'User Details',\n    items: [{\n        ...\n    }],\n    listeners: {\n        change: function() {\n            // Title goes red if form has been modified.\n            myForm.header.setStyle('color', 'red');\n        }\n    }\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventNames</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>The event name to bubble, or an Array of event names.</p>\n</div></li></ul></div></div></div><div id='method-endEdit' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-endEdit' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-endEdit' class='name expandable'>endEdit</a>( <span class='pre'>[silent], [modifiedFieldNames]</span> )</div><div class='description'><div class='short'>Ends an edit. ...</div><div class='long'><p>Ends an edit. If any data was modified, the containing store is notified\n(ie, the store's <code>update</code> event will fire).</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>silent</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to not notify the store of the change</p>\n</div></li><li><span class='pre'>modifiedFieldNames</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[] (optional)<div class='sub-desc'><p>Array of field names changed during edit.</p>\n</div></li></ul></div></div></div><div id='method-fireEvent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-fireEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-fireEvent' class='name expandable'>fireEvent</a>( <span class='pre'>eventName, args</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Fires the specified event with the passed parameters (minus the event name, plus the options object passed\nto addList...</div><div class='long'><p>Fires the specified event with the passed parameters (minus the event name, plus the <code>options</code> object passed\nto <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a>).</p>\n\n<p>An event may be set to bubble up an Observable parent hierarchy (See <a href=\"#!/api/Ext.Component-method-getBubbleTarget\" rel=\"Ext.Component-method-getBubbleTarget\" class=\"docClass\">Ext.Component.getBubbleTarget</a>) by\ncalling <a href=\"#!/api/Ext.util.Observable-method-enableBubble\" rel=\"Ext.util.Observable-method-enableBubble\" class=\"docClass\">enableBubble</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the event to fire.</p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>...<div class='sub-desc'><p>Variable number of parameters are passed to handlers.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>returns false if any of the handlers return false otherwise it returns true.</p>\n</div></li></ul></div></div></div><div id='method-fireEventArgs' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-fireEventArgs' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-fireEventArgs' class='name expandable'>fireEventArgs</a>( <span class='pre'>eventName, args</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Fires the specified event with the passed parameter list. ...</div><div class='long'><p>Fires the specified event with the passed parameter list.</p>\n\n<p>An event may be set to bubble up an Observable parent hierarchy (See <a href=\"#!/api/Ext.Component-method-getBubbleTarget\" rel=\"Ext.Component-method-getBubbleTarget\" class=\"docClass\">Ext.Component.getBubbleTarget</a>) by\ncalling <a href=\"#!/api/Ext.util.Observable-method-enableBubble\" rel=\"Ext.util.Observable-method-enableBubble\" class=\"docClass\">enableBubble</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the event to fire.</p>\n</div></li><li><span class='pre'>args</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>[]<div class='sub-desc'><p>An array of parameters which are passed to handlers.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>returns false if any of the handlers return false otherwise it returns true.</p>\n</div></li></ul></div></div></div><div id='method-get' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-get' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-get' class='name expandable'>get</a>( <span class='pre'>fieldName</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Returns the value of the given field ...</div><div class='long'><p>Returns the value of the given field</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fieldName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The field to fetch the value for</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The value</p>\n</div></li></ul></div></div></div><div id='method-getAssociatedData' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-getAssociatedData' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-getAssociatedData' class='name expandable'>getAssociatedData</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Gets all of the data from this Models loaded associations. ...</div><div class='long'><p>Gets all of the data from this Models <em>loaded</em> associations. It does this recursively - for example if we have a\nUser which hasMany Orders, and each Order hasMany OrderItems, it will return an object like this:</p>\n\n<pre><code>{\n    orders: [\n        {\n            id: 123,\n            status: 'shipped',\n            orderItems: [\n                ...\n            ]\n        }\n    ]\n}\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The nested data set for the Model's loaded associations</p>\n</div></li></ul></div></div></div><div id='method-getBubbleParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-getBubbleParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-getBubbleParent' class='name expandable'>getBubbleParent</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Gets the bubbling parent for an Observable ...</div><div class='long'><p>Gets the bubbling parent for an Observable</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a></span><div class='sub-desc'><p>The bubble parent. null is returned if no bubble target exists</p>\n</div></li></ul></div></div></div><div id='method-getChanges' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-getChanges' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-getChanges' class='name expandable'>getChanges</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Gets a hash of only the fields that have been modified since this Model was created or commited. ...</div><div class='long'><p>Gets a hash of only the fields that have been modified since this Model was created or commited.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getData' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-getData' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-getData' class='name expandable'>getData</a>( <span class='pre'>includeAssociated</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Gets all values for each field in this model and returns an object\ncontaining the current data. ...</div><div class='long'><p>Gets all values for each field in this model and returns an object\ncontaining the current data.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>includeAssociated</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to also include associated data. Defaults to false.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>An object hash containing all the values in this model</p>\n</div></li></ul></div></div></div><div id='method-getId' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-getId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-getId' class='name expandable'>getId</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></div><div class='description'><div class='short'>Returns the unique ID allocated to this model instance as defined by idProperty. ...</div><div class='long'><p>Returns the unique ID allocated to this model instance as defined by <a href=\"#!/api/Ext.data.Model-cfg-idProperty\" rel=\"Ext.data.Model-cfg-idProperty\" class=\"docClass\">idProperty</a>.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>The id</p>\n</div></li></ul></div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-getModifiedFieldNames' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-getModifiedFieldNames' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-getModifiedFieldNames' class='name expandable'>getModifiedFieldNames</a>( <span class='pre'>[saved]</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Gets the names of all the fields that were modified during an edit ...</div><div class='long'><p>Gets the names of all the fields that were modified during an edit</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>saved</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The currently saved data. Defaults to\nthe dataSave property on the object.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</span><div class='sub-desc'><p>An array of modified field names</p>\n</div></li></ul></div></div></div><div id='method-getObservableId' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-getObservableId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-getObservableId' class='name expandable'>getObservableId</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-getProxy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-getProxy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-getProxy' class='name expandable'>getProxy</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a></div><div class='description'><div class='short'>Returns the configured Proxy for this Model. ...</div><div class='long'><p>Returns the configured Proxy for this Model.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a></span><div class='sub-desc'><p>The proxy</p>\n</div></li></ul></div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-hasId' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-hasId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-hasId' class='name expandable'>hasId</a>( <span class='pre'>[id]</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Checks if this model has an id assigned ...</div><div class='long'><p>Checks if this model has an id assigned</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The id, if not passed it will call getId()</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if the model has an id</p>\n</div></li></ul></div></div></div><div id='method-hasListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-hasListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-hasListener' class='name expandable'>hasListener</a>( <span class='pre'>eventName</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks to see if this object has any listeners for a specified event, or whether the event bubbles. ...</div><div class='long'><p>Checks to see if this object has any listeners for a specified event, or whether the event bubbles. The answer\nindicates whether the event needs firing or not.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The name of the event to check for</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p><code>true</code> if the event is being listened for or bubbles, else <code>false</code></p>\n</div></li></ul></div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-isEqual' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-isEqual' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-isEqual' class='name expandable'>isEqual</a>( <span class='pre'>a, b</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Checks if two values are equal, taking into account certain\nspecial factors, for example dates. ...</div><div class='long'><p>Checks if two values are equal, taking into account certain\nspecial factors, for example dates.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>a</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The first value</p>\n</div></li><li><span class='pre'>b</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The second value</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if the values are equal</p>\n</div></li></ul></div></div></div><div id='method-isModified' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-isModified' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-isModified' class='name expandable'>isModified</a>( <span class='pre'>fieldName</span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns true if the passed field name has been modified since the load or last commit. ...</div><div class='long'><p>Returns true if the passed field name has been <code><a href=\"#!/api/Ext.data.Model-property-modified\" rel=\"Ext.data.Model-property-modified\" class=\"docClass\">modified</a></code> since the load or last commit.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fieldName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p><a href=\"#!/api/Ext.data.Field-cfg-name\" rel=\"Ext.data.Field-cfg-name\" class=\"docClass\">Ext.data.Field.name</a></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-isValid' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-isValid' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-isValid' class='name expandable'>isValid</a>( <span class='pre'></span> ) : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Checks if the model is valid. ...</div><div class='long'><p>Checks if the model is valid. See <a href=\"#!/api/Ext.data.Model-method-validate\" rel=\"Ext.data.Model-method-validate\" class=\"docClass\">validate</a>.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p>True if the model is valid.</p>\n</div></li></ul></div></div></div><div id='method-itemNameFn' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-itemNameFn' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-itemNameFn' class='name expandable'>itemNameFn</a>( <span class='pre'>item</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-join' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-join' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-join' class='name expandable'>join</a>( <span class='pre'>store</span> )</div><div class='description'><div class='short'>Tells this model instance that it has been added to a store. ...</div><div class='long'><p>Tells this model instance that it has been added to a store.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>store</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>The store to which this model has been added.</p>\n</div></li></ul></div></div></div><div id='method-markDirty' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-markDirty' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-markDirty' class='name expandable'>markDirty</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'><p><debug></p>\n</div></div></div><div id='method-mon' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-mon' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-mon' class='name expandable'>mon</a>( <span class='pre'>item, ename, [fn], [scope], [options]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Shorthand for addManagedListener. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.util.Observable-method-addManagedListener\" rel=\"Ext.util.Observable-method-addManagedListener\" class=\"docClass\">addManagedListener</a>.</p>\n\n<p>Adds listeners to any Observable object (or <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>) which are automatically removed when this Component is\ndestroyed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The item to which to add a listener/listeners.</p>\n\n</div></li><li><span class='pre'>ename</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The event name, or an object containing event name properties.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the handler function.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the scope (<code>this</code> reference)\nin which the handler function is executed.</p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the\n<a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> options.</p>\n\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><strong>Only when the <code>destroyable</code> option is specified. </strong></p>\n\n\n\n\n<p> A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call. For example:</p>\n\n\n\n\n<pre><code>this.btnListeners =  = myButton.mon({\n    destroyable: true\n    mouseover:   function() { console.log('mouseover'); },\n    mouseout:    function() { console.log('mouseout'); },\n    click:       function() { console.log('click'); }\n});\n</code></pre>\n\n\n\n\n<p>And when those listeners need to be removed:</p>\n\n\n\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.btnListeners);\n</code></pre>\n\n\n\n\n<p>or</p>\n\n\n\n\n<pre><code>this.btnListeners.destroy();\n</code></pre>\n\n</div></li></ul></div></div></div><div id='method-mun' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-mun' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-mun' class='name expandable'>mun</a>( <span class='pre'>item, ename, [fn], [scope]</span> )</div><div class='description'><div class='short'>Shorthand for removeManagedListener. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.util.Observable-method-removeManagedListener\" rel=\"Ext.util.Observable-method-removeManagedListener\" class=\"docClass\">removeManagedListener</a>.</p>\n\n<p>Removes listeners that were added by the <a href=\"#!/api/Ext.util.Observable-method-mon\" rel=\"Ext.util.Observable-method-mon\" class=\"docClass\">mon</a> method.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The item from which to remove a listener/listeners.</p>\n\n</div></li><li><span class='pre'>ename</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The event name, or an object containing event name properties.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the handler function.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the scope (<code>this</code> reference)\nin which the handler function is executed.</p>\n\n</div></li></ul></div></div></div><div id='method-on' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-on' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-on' class='name expandable'>on</a>( <span class='pre'>eventName, [fn], [scope], [options]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Shorthand for addListener. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a>.</p>\n\n<p>Appends an event handler to this object.  For example:</p>\n\n<pre><code>myGridPanel.on(\"mouseover\", this.onMouseOver, this);\n</code></pre>\n\n<p>The method also allows for a single argument to be passed which is a config object\ncontaining properties which specify multiple events. For example:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: this.onCellClick,\n    mouseover: this.onMouseOver,\n    mouseout: this.onMouseOut,\n    scope: this // Important. Ensure \"this\" is correct during handler execution\n});\n</code></pre>\n\n<p>One can also specify options for each event handler separately:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: this.onCellClick, scope: this, single: true},\n    mouseover: {fn: panel.onMouseOver, scope: panel}\n});\n</code></pre>\n\n<p><em>Names</em> of methods in a specified scope may also be used. Note that\n<code>scope</code> MUST be specified to use this option:</p>\n\n<pre><code>myGridPanel.on({\n    cellClick: {fn: 'onCellClick', scope: this, single: true},\n    mouseover: {fn: 'onMouseOver', scope: panel}\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The name of the event to listen for.\nMay also be an object who's property names are event names.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The method the event invokes, or <em>if <code>scope</code> is specified, the </em>name* of the method within\nthe specified <code>scope</code>.  Will be called with arguments\ngiven to <a href=\"#!/api/Ext.util.Observable-method-fireEvent\" rel=\"Ext.util.Observable-method-fireEvent\" class=\"docClass\">fireEvent</a> plus the <code>options</code> parameter described below.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is\nexecuted. <strong>If omitted, defaults to the object which fired the event.</strong></p>\n\n</div></li><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>An object containing handler configuration.</p>\n\n\n\n\n<p><strong>Note:</strong> Unlike in ExtJS 3.x, the options object will also be passed as the last\nargument to every event handler.</p>\n\n\n\n\n<p>This object may contain any of the following properties:</p>\n\n<ul><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The scope (<code>this</code> reference) in which the handler function is executed. <strong>If omitted,\n  defaults to the object which fired the event.</strong></p>\n\n</div></li><li><span class='pre'>delay</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The number of milliseconds to delay the invocation of the handler after the event fires.</p>\n\n</div></li><li><span class='pre'>single</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True to add a handler to handle just the next firing of the event, and then remove itself.</p>\n\n</div></li><li><span class='pre'>buffer</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Causes the handler to be scheduled to run in an <a href=\"#!/api/Ext.util.DelayedTask\" rel=\"Ext.util.DelayedTask\" class=\"docClass\">Ext.util.DelayedTask</a> delayed\n  by the specified number of milliseconds. If the event fires again within that time,\n  the original handler is <em>not</em> invoked, but the new handler is scheduled in its place.</p>\n\n</div></li><li><span class='pre'>target</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a><div class='sub-desc'><p>Only call the handler if the event was fired on the target Observable, <em>not</em> if the event\n  was bubbled up from a child Observable.</p>\n\n</div></li><li><span class='pre'>element</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p><strong>This option is only valid for listeners bound to <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a>.</strong>\n  The name of a Component property which references an element to add a listener to.</p>\n\n\n\n\n<p>  This option is useful during Component construction to add DOM event listeners to elements of\n  <a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a> which will exist only after the Component is rendered.\n  For example, to add a click listener to a Panel's body:</p>\n\n\n\n\n<pre><code>  new <a href=\"#!/api/Ext.panel.Panel\" rel=\"Ext.panel.Panel\" class=\"docClass\">Ext.panel.Panel</a>({\n      title: 'The title',\n      listeners: {\n          click: this.handlePanelClick,\n          element: 'body'\n      }\n  });\n</code></pre>\n\n</div></li><li><span class='pre'>destroyable</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>When specified as <code>true</code>, the function returns A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call.</p>\n\n<p>Defaults to: <code>false</code></p></div></li><li><span class='pre'>priority</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>An optional numeric priority that determines the order in which event handlers\n  are run. Event handlers with no priority will be run as if they had a priority\n  of 0. Handlers with a higher priority will be prioritized to run sooner than\n  those with a lower priority.  Negative numbers can be used to set a priority\n  lower than the default. Internally, the framework uses a range of 1000 or\n  greater, and -1000 or lesser for handers that are intended to run before or\n  after all others, so it is recommended to stay within the range of -999 to 999\n  when setting the priority of event handlers in application-level code.</p>\n\n\n\n\n<p><strong>Combining Options</strong></p>\n\n\n\n\n<p>Using the options argument, it is possible to combine different types of listeners:</p>\n\n\n\n\n<p>A delayed, one-time listener.</p>\n\n\n\n\n<pre><code>myPanel.on('hide', this.handleClick, this, {\n    single: true,\n    delay: 100\n});\n</code></pre>\n\n</div></li></ul></div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p><strong>Only when the <code>destroyable</code> option is specified. </strong></p>\n\n\n\n\n<p> A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which removes all listeners added in this call. For example:</p>\n\n\n\n\n<pre><code>this.btnListeners =  = myButton.on({\n    destroyable: true\n    mouseover:   function() { console.log('mouseover'); },\n    mouseout:    function() { console.log('mouseout'); },\n    click:       function() { console.log('click'); }\n});\n</code></pre>\n\n\n\n\n<p>And when those listeners need to be removed:</p>\n\n\n\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.btnListeners);\n</code></pre>\n\n\n\n\n<p>or</p>\n\n\n\n\n<pre><code>this.btnListeners.destroy();\n</code></pre>\n\n</div></li></ul></div></div></div><div id='method-onClassExtended' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-onClassExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-onClassExtended' class='name expandable'>onClassExtended</a>( <span class='pre'>cls, data, hooks</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>cls</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>data</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>hooks</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-prepareAssociatedData' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-prepareAssociatedData' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-prepareAssociatedData' class='name expandable'>prepareAssociatedData</a>( <span class='pre'>seenKeys, depth</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>This complex-looking method takes a given Model instance and returns an object containing all data from\nall of that M...</div><div class='long'><p>This complex-looking method takes a given Model instance and returns an object containing all data from\nall of that Model's <em>loaded</em> associations. See <a href=\"#!/api/Ext.data.Model-method-getAssociatedData\" rel=\"Ext.data.Model-method-getAssociatedData\" class=\"docClass\">getAssociatedData</a></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>seenKeys</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>A hash of all the associations we've already seen</p>\n</div></li><li><span class='pre'>depth</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The current depth</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>The nested data set for the Model's loaded associations</p>\n</div></li></ul></div></div></div><div id='method-prepareClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-prepareClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-prepareClass' class='name expandable'>prepareClass</a>( <span class='pre'>T</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Prepares a given class for observable instances. ...</div><div class='long'><p>Prepares a given class for observable instances. This method is called when a\nclass derives from this class or uses this class as a mixin.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>T</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The class constructor to prepare.</p>\n</div></li></ul></div></div></div><div id='method-reject' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-reject' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-reject' class='name expandable'>reject</a>( <span class='pre'>[silent]</span> )</div><div class='description'><div class='short'>Usually called by the Ext.data.Store to which this model instance has been joined. ...</div><div class='long'><p>Usually called by the <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a> to which this model instance has been <a href=\"#!/api/Ext.data.Model-method-join\" rel=\"Ext.data.Model-method-join\" class=\"docClass\">joined</a>. Rejects\nall changes made to the model instance since either creation, or the last commit operation. Modified fields are\nreverted to their original values.</p>\n\n<p>Developers should subscribe to the <a href=\"#!/api/Ext.data.Store-event-update\" rel=\"Ext.data.Store-event-update\" class=\"docClass\">Ext.data.Store.update</a> event to have their code notified of reject\noperations.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>silent</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to skip notification of the owning store of the change.\nDefaults to false.</p>\n</div></li></ul></div></div></div><div id='method-relayEvents' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-relayEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-relayEvents' class='name expandable'>relayEvents</a>( <span class='pre'>origin, events, [prefix]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Relays selected events from the specified Observable as if the events were fired by this. ...</div><div class='long'><p>Relays selected events from the specified Observable as if the events were fired by <code>this</code>.</p>\n\n<p>For example if you are extending Grid, you might decide to forward some events from store.\nSo you can do this inside your initComponent:</p>\n\n<pre><code>this.relayEvents(this.getStore(), ['load']);\n</code></pre>\n\n<p>The grid instance will then have an observable 'load' event which will be passed the\nparameters of the store's load event and any function fired with the grid's load event\nwould have access to the grid using the <code>this</code> keyword.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>origin</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The Observable whose events this object is to relay.</p>\n</div></li><li><span class='pre'>events</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]<div class='sub-desc'><p>Array of event names to relay.</p>\n</div></li><li><span class='pre'>prefix</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>A common prefix to prepend to the event names. For example:</p>\n\n<pre><code>this.relayEvents(this.getStore(), ['load', 'clear'], 'store');\n</code></pre>\n\n<p>Now the grid will forward 'load' and 'clear' events of store as 'storeload' and 'storeclear'.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>A <code>Destroyable</code> object. An object which implements the <code>destroy</code> method which, when destroyed, removes all relayers. For example:</p>\n\n<pre><code>this.storeRelayers = this.relayEvents(this.getStore(), ['load', 'clear'], 'store');\n</code></pre>\n\n<p>Can be undone by calling</p>\n\n<pre><code><a href=\"#!/api/Ext-method-destroy\" rel=\"Ext-method-destroy\" class=\"docClass\">Ext.destroy</a>(this.storeRelayers);\n</code></pre>\n\n<p>or</p>\n\n<pre><code>this.store.relayers.destroy();\n</code></pre>\n</div></li></ul></div></div></div><div id='method-removeListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-removeListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-removeListener' class='name expandable'>removeListener</a>( <span class='pre'>eventName, fn, [scope]</span> )</div><div class='description'><div class='short'>Removes an event handler. ...</div><div class='long'><p>Removes an event handler.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The type of event the handler was associated with.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The handler to remove. <strong>This must be a reference to the function passed into the\n<a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> call.</strong></p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope originally specified for the handler. It must be the same as the\nscope argument specified in the original call to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> or the listener will not be removed.</p>\n\n</div></li></ul></div></div></div><div id='method-removeManagedListener' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-removeManagedListener' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-removeManagedListener' class='name expandable'>removeManagedListener</a>( <span class='pre'>item, ename, [fn], [scope]</span> )</div><div class='description'><div class='short'>Removes listeners that were added by the mon method. ...</div><div class='long'><p>Removes listeners that were added by the <a href=\"#!/api/Ext.util.Observable-method-mon\" rel=\"Ext.util.Observable-method-mon\" class=\"docClass\">mon</a> method.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>item</span> : <a href=\"#!/api/Ext.util.Observable\" rel=\"Ext.util.Observable\" class=\"docClass\">Ext.util.Observable</a>/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a><div class='sub-desc'><p>The item from which to remove a listener/listeners.</p>\n\n</div></li><li><span class='pre'>ename</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The event name, or an object containing event name properties.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the handler function.</p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>If the <code>ename</code> parameter was an event name, this is the scope (<code>this</code> reference)\nin which the handler function is executed.</p>\n\n</div></li></ul></div></div></div><div id='method-removeManagedListenerItem' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-removeManagedListenerItem' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-removeManagedListenerItem' class='name expandable'>removeManagedListenerItem</a>( <span class='pre'>isClear, managedListener</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Remove a single managed listener item ...</div><div class='long'><p>Remove a single managed listener item</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>isClear</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>True if this is being called during a clear</p>\n</div></li><li><span class='pre'>managedListener</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The managed listener item\nSee removeManagedListener for other args</p>\n</div></li></ul></div></div></div><div id='method-resumeEvent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-resumeEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-resumeEvent' class='name expandable'>resumeEvent</a>( <span class='pre'>eventName</span> )</div><div class='description'><div class='short'>Resumes firing of the named event(s). ...</div><div class='long'><p>Resumes firing of the named event(s).</p>\n\n<p>After calling this method to resume events, the events will fire when requested to fire.</p>\n\n<p><strong>Note that if the <a href=\"#!/api/Ext.util.Observable-method-suspendEvent\" rel=\"Ext.util.Observable-method-suspendEvent\" class=\"docClass\">suspendEvent</a> method is called multiple times for a certain event,\nthis converse method will have to be called the same number of times for it to resume firing.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>...<div class='sub-desc'><p>Multiple event names to resume.</p>\n</div></li></ul></div></div></div><div id='method-resumeEvents' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-resumeEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-resumeEvents' class='name expandable'>resumeEvents</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Resumes firing events (see suspendEvents). ...</div><div class='long'><p>Resumes firing events (see <a href=\"#!/api/Ext.util.Observable-method-suspendEvents\" rel=\"Ext.util.Observable-method-suspendEvents\" class=\"docClass\">suspendEvents</a>).</p>\n\n<p>If events were suspended using the <code>queueSuspended</code> parameter, then all events fired\nduring event suspension will be sent to any listeners now.</p>\n</div></div></div><div id='method-save' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-save' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-save' class='name expandable'>save</a>( <span class='pre'>[options]</span> ) : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></div><div class='description'><div class='short'>Saves the model instance using the configured proxy. ...</div><div class='long'><p>Saves the model instance using the configured proxy.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>options</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>Options to pass to the proxy. Config object for <a href=\"#!/api/Ext.data.Operation\" rel=\"Ext.data.Operation\" class=\"docClass\">Ext.data.Operation</a>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a></span><div class='sub-desc'><p>The Model instance</p>\n</div></li></ul></div></div></div><div id='method-set' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-set' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-set' class='name expandable'>set</a>( <span class='pre'>fieldName, newValue</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</div><div class='description'><div class='short'>Sets the given field to the given value, marks the instance as dirty ...</div><div class='long'><p>Sets the given field to the given value, marks the instance as dirty</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fieldName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The field to set, or an object containing key/value pairs</p>\n</div></li><li><span class='pre'>newValue</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The value to set</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>[]</span><div class='sub-desc'><p>The array of modified field names or null if nothing was modified.</p>\n</div></li></ul></div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setDirty' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-setDirty' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-setDirty' class='name expandable'>setDirty</a>( <span class='pre'></span> )</div><div class='description'><div class='short'>Marks this Record as dirty. ...</div><div class='long'><p>Marks this <strong>Record</strong> as <code><a href=\"#!/api/Ext.data.Model-property-dirty\" rel=\"Ext.data.Model-property-dirty\" class=\"docClass\">dirty</a></code>. This method is used interally when adding <code><a href=\"#!/api/Ext.data.Model-property-phantom\" rel=\"Ext.data.Model-property-phantom\" class=\"docClass\">phantom</a></code> records\nto a <a href=\"#!/api/Ext.data.proxy.Server-cfg-writer\" rel=\"Ext.data.proxy.Server-cfg-writer\" class=\"docClass\">writer enabled store</a>.</p>\n\n<p>Marking a record <code><a href=\"#!/api/Ext.data.Model-property-dirty\" rel=\"Ext.data.Model-property-dirty\" class=\"docClass\">dirty</a></code> causes the phantom to be returned by <a href=\"#!/api/Ext.data.Store-method-getUpdatedRecords\" rel=\"Ext.data.Store-method-getUpdatedRecords\" class=\"docClass\">Ext.data.Store.getUpdatedRecords</a>\nwhere it will have a create action composed for it during <a href=\"#!/api/Ext.data.Model-method-save\" rel=\"Ext.data.Model-method-save\" class=\"docClass\">model save</a> operations.</p>\n</div></div></div><div id='method-setId' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-setId' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-setId' class='name expandable'>setId</a>( <span class='pre'>id</span> )</div><div class='description'><div class='short'>Sets the model instance's id field to the given id. ...</div><div class='long'><p>Sets the model instance's id field to the given id.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The new id</p>\n</div></li></ul></div></div></div><div id='method-setProxy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-setProxy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-setProxy' class='name expandable'>setProxy</a>( <span class='pre'>proxy</span> ) : <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a></div><div class='description'><div class='short'>Sets the Proxy to use for this model. ...</div><div class='long'><p>Sets the Proxy to use for this model. Accepts any options that can be accepted by\n<a href=\"#!/api/Ext-method-createByAlias\" rel=\"Ext-method-createByAlias\" class=\"docClass\">Ext.createByAlias</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>proxy</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a><div class='sub-desc'><p>The proxy</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-suspendEvent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-suspendEvent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-suspendEvent' class='name expandable'>suspendEvent</a>( <span class='pre'>eventName</span> )</div><div class='description'><div class='short'>Suspends firing of the named event(s). ...</div><div class='long'><p>Suspends firing of the named event(s).</p>\n\n<p>After calling this method to suspend events, the events will no longer fire when requested to fire.</p>\n\n<p><strong>Note that if this is called multiple times for a certain event, the converse method\n<a href=\"#!/api/Ext.util.Observable-method-resumeEvent\" rel=\"Ext.util.Observable-method-resumeEvent\" class=\"docClass\">resumeEvent</a> will have to be called the same number of times for it to resume firing.</strong></p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>...<div class='sub-desc'><p>Multiple event names to suspend.</p>\n</div></li></ul></div></div></div><div id='method-suspendEvents' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-suspendEvents' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-suspendEvents' class='name expandable'>suspendEvents</a>( <span class='pre'>queueSuspended</span> )</div><div class='description'><div class='short'>Suspends the firing of all events. ...</div><div class='long'><p>Suspends the firing of all events. (see <a href=\"#!/api/Ext.util.Observable-method-resumeEvents\" rel=\"Ext.util.Observable-method-resumeEvents\" class=\"docClass\">resumeEvents</a>)</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>queueSuspended</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a><div class='sub-desc'><p>Pass as true to queue up suspended events to be fired\nafter the <a href=\"#!/api/Ext.util.Observable-method-resumeEvents\" rel=\"Ext.util.Observable-method-resumeEvents\" class=\"docClass\">resumeEvents</a> call instead of discarding all suspended events.</p>\n</div></li></ul></div></div></div><div id='method-un' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.util.Observable' rel='Ext.util.Observable' class='defined-in docClass'>Ext.util.Observable</a><br/><a href='source/Observable.html#Ext-util-Observable-method-un' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Observable-method-un' class='name expandable'>un</a>( <span class='pre'>eventName, fn, [scope]</span> )</div><div class='description'><div class='short'>Shorthand for removeListener. ...</div><div class='long'><p>Shorthand for <a href=\"#!/api/Ext.util.Observable-method-removeListener\" rel=\"Ext.util.Observable-method-removeListener\" class=\"docClass\">removeListener</a>.</p>\n\n<p>Removes an event handler.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>eventName</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The type of event the handler was associated with.</p>\n\n</div></li><li><span class='pre'>fn</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a><div class='sub-desc'><p>The handler to remove. <strong>This must be a reference to the function passed into the\n<a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> call.</strong></p>\n\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>The scope originally specified for the handler. It must be the same as the\nscope argument specified in the original call to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">addListener</a> or the listener will not be removed.</p>\n\n</div></li></ul></div></div></div><div id='method-unjoin' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-unjoin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-unjoin' class='name expandable'>unjoin</a>( <span class='pre'>store</span> )</div><div class='description'><div class='short'>Tells this model instance that it has been removed from the store. ...</div><div class='long'><p>Tells this model instance that it has been removed from the store.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>store</span> : <a href=\"#!/api/Ext.data.Store\" rel=\"Ext.data.Store\" class=\"docClass\">Ext.data.Store</a><div class='sub-desc'><p>The store from which this model has been removed.</p>\n</div></li></ul></div></div></div><div id='method-validate' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-validate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-validate' class='name expandable'>validate</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.data.Errors\" rel=\"Ext.data.Errors\" class=\"docClass\">Ext.data.Errors</a></div><div class='description'><div class='short'>Validates the current data against all of its configured validations. ...</div><div class='long'><p>Validates the current data against all of its configured <a href=\"#!/api/Ext.data.Model-cfg-validations\" rel=\"Ext.data.Model-cfg-validations\" class=\"docClass\">validations</a>.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Errors\" rel=\"Ext.data.Errors\" class=\"docClass\">Ext.data.Errors</a></span><div class='sub-desc'><p>The errors object</p>\n</div></li></ul></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-create' class='name expandable'>create</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create a new instance of this Class. ...</div><div class='long'><p>Create a new instance of this Class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.create({\n    someConfig: true\n});\n</code></pre>\n\n<p>All parameters are passed to the constructor of the class.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the created instance.</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getFields' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-method-getFields' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-method-getFields' class='name expandable'>getFields</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.data.Field\" rel=\"Ext.data.Field\" class=\"docClass\">Ext.data.Field</a>[]<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Returns an Array of Field definitions which define this Model's structure\n\nFields are sorted upon Model class definit...</div><div class='long'><p>Returns an Array of <a href=\"#!/api/Ext.data.Field\" rel=\"Ext.data.Field\" class=\"docClass\">Field</a> definitions which define this Model's structure</p>\n\n<p>Fields are sorted upon Model class definition. Fields with custom <a href=\"#!/api/Ext.data.Field-cfg-convert\" rel=\"Ext.data.Field-cfg-convert\" class=\"docClass\">convert</a> functions\nare moved to <em>after</em> fields with no convert functions. This is so that convert functions which rely on existing\nfield values will be able to read those field values.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.Field\" rel=\"Ext.data.Field\" class=\"docClass\">Ext.data.Field</a>[]</span><div class='sub-desc'><p>The defined Fields for this Model.</p>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-getProxy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-static-method-getProxy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-static-method-getProxy' class='name expandable'>getProxy</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Returns the configured Proxy for this Model ...</div><div class='long'><p>Returns the configured Proxy for this Model</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a></span><div class='sub-desc'><p>The proxy</p>\n</div></li></ul></div></div></div><div id='static-method-id' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-static-method-id' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-static-method-id' class='name expandable'>id</a>( <span class='pre'>rec</span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Generates a sequential id. ...</div><div class='long'><p>Generates a sequential id. This method is typically called when a record is <a href=\"#!/api/Ext-method-create\" rel=\"Ext-method-create\" class=\"docClass\">create</a>d and <a href=\"#!/api/Ext.data.Model-method-constructor\" rel=\"Ext.data.Model-method-constructor\" class=\"docClass\">no id has been specified</a> either as a parameter, or through the <a href=\"#!/api/Ext.data.Model-cfg-idProperty\" rel=\"Ext.data.Model-cfg-idProperty\" class=\"docClass\">idProperty</a>\nin the passed data. The generated id will automatically be assigned to the\nrecord. The returned id takes the form: {PREFIX}-{AUTO_ID}.</p>\n\n<ul>\n<li><strong>PREFIX</strong> : String - <a href=\"#!/api/Ext.data.Model-static-property-PREFIX\" rel=\"Ext.data.Model-static-property-PREFIX\" class=\"docClass\">Ext.data.Model.PREFIX</a> (defaults to 'ext-record')</li>\n<li><strong>AUTO_ID</strong> : String - <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>.AUTO_ID (defaults to 1 initially)</li>\n</ul>\n\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>rec</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'><p>The record being created. The record does not exist, it's a <a href=\"#!/api/Ext.data.Model-property-phantom\" rel=\"Ext.data.Model-property-phantom\" class=\"docClass\">phantom</a>.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>auto-generated string id, <code>\"ext-record-i++\"</code>;</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-load' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-static-method-load' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-static-method-load' class='name expandable'>load</a>( <span class='pre'>id, [config]</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Asynchronously loads a model instance by id. ...</div><div class='long'><p>Asynchronously loads a model instance by id. Sample usage:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('MyApp.User', {\n    extend: '<a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a>',\n    fields: [\n        {name: 'id', type: 'int'},\n        {name: 'name', type: 'string'}\n    ]\n});\n\nMyApp.User.load(10, {\n    scope: this,\n    failure: function(record, operation) {\n        //do something if the load failed\n        //record is null\n    },\n    success: function(record, operation) {\n        //do something if the load succeeded\n    },\n    callback: function(record, operation, success) {\n        //do something whether the load succeeded or failed\n        //if operation is unsuccessful, record is null\n    }\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>id</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The id of the model to load</p>\n</div></li><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>config object containing success, failure and callback functions, plus\noptional scope</p>\n</div></li></ul></div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-setFields' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-static-method-setFields' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-static-method-setFields' class='name expandable'>setFields</a>( <span class='pre'>fields, idProperty, clientIdProperty</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Apply a new set of field and/or property definitions to the existing model. ...</div><div class='long'><p>Apply a new set of field and/or property definitions to the existing model. This will replace any existing\nfields, including fields inherited from superclasses. Mainly for reconfiguring the\nmodel based on changes in meta data (called from Reader's onMetaChange method).</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fields</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>idProperty</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>clientIdProperty</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-setProxy' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-static-method-setProxy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-static-method-setProxy' class='name expandable'>setProxy</a>( <span class='pre'>proxy</span> ) : <a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Sets the Proxy to use for this model. ...</div><div class='long'><p>Sets the Proxy to use for this model. Accepts any options that can be accepted by\n<a href=\"#!/api/Ext-method-createByAlias\" rel=\"Ext-method-createByAlias\" class=\"docClass\">Ext.createByAlias</a>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>proxy</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/<a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a><div class='sub-desc'><p>The proxy</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.data.proxy.Proxy\" rel=\"Ext.data.proxy.Proxy\" class=\"docClass\">Ext.data.proxy.Proxy</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-event'>Events</h3><div class='subsection'><div id='event-idchanged' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.data.Model'>Ext.data.Model</span><br/><a href='source/Model.html#Ext-data-Model-event-idchanged' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.data.Model-event-idchanged' class='name expandable'>idchanged</a>( <span class='pre'>this, oldId, newId, eOpts</span> )</div><div class='description'><div class='short'>Fired when this model's id changes ...</div><div class='long'><p>Fired when this model's id changes</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>this</span> : <a href=\"#!/api/Ext.data.Model\" rel=\"Ext.data.Model\" class=\"docClass\">Ext.data.Model</a><div class='sub-desc'>\n</div></li><li><span class='pre'>oldId</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The old id</p>\n</div></li><li><span class='pre'>newId</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The new id</p>\n</div></li><li><span class='pre'>eOpts</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The options object passed to <a href=\"#!/api/Ext.util.Observable-method-addListener\" rel=\"Ext.util.Observable-method-addListener\" class=\"docClass\">Ext.util.Observable.addListener</a>.</p>\n\n\n\n</div></li></ul></div></div></div></div></div></div></div>","superclasses":["Ext.Base"],"meta":{"author":["Ed Spencer"]},"code_type":"ext_define","requires":["Ext.ModelManager","Ext.data.Errors","Ext.data.Field","Ext.data.IdGenerator","Ext.data.Operation","Ext.data.validations","Ext.util.MixedCollection"],"html_meta":{"author":null},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"},{"tagname":"property","owner":"Ext.data.Model","meta":{"static":true,"private":true},"name":"AUTO_ID","id":"static-property-AUTO_ID"},{"tagname":"property","owner":"Ext.data.Model","meta":{"static":true},"name":"COMMIT","id":"static-property-COMMIT"},{"tagname":"property","owner":"Ext.data.Model","meta":{"static":true},"name":"EDIT","id":"static-property-EDIT"},{"tagname":"property","owner":"Ext.data.Model","meta":{"static":true,"private":true},"name":"PREFIX","id":"static-property-PREFIX"},{"tagname":"property","owner":"Ext.data.Model","meta":{"static":true},"name":"REJECT","id":"static-property-REJECT"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.data.Model","meta":{"static":true},"name":"getFields","id":"method-getFields"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.data.Model","meta":{"static":true},"name":"getProxy","id":"static-method-getProxy"},{"tagname":"method","owner":"Ext.data.Model","meta":{"static":true},"name":"id","id":"static-method-id"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.data.Model","meta":{"static":true},"name":"load","id":"static-method-load"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.data.Model","meta":{"static":true},"name":"setFields","id":"static-method-setFields"},{"tagname":"method","owner":"Ext.data.Model","meta":{"static":true},"name":"setProxy","id":"static-method-setProxy"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"Model.html#Ext-data-Model","filename":"Model.js"}],"linenr":1,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.data.Model","meta":{"private":true},"name":"_singleProp","id":"property-_singleProp"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.data.Model","meta":{"readonly":true},"name":"dirty","id":"property-dirty"},{"tagname":"property","owner":"Ext.data.Model","meta":{"readonly":true},"name":"editing","id":"property-editing"},{"tagname":"property","owner":"Ext.data.Model","meta":{"private":true},"name":"emptyData","id":"property-emptyData"},{"tagname":"property","owner":"Ext.data.Model","meta":{"private":true},"name":"evented","id":"property-evented"},{"tagname":"property","owner":"Ext.util.Observable","meta":{"private":true},"name":"eventsSuspended","id":"property-eventsSuspended"},{"tagname":"property","owner":"Ext.data.Model","meta":{},"name":"fields","id":"property-fields"},{"tagname":"property","owner":"Ext.util.Observable","meta":{"readonly":true},"name":"hasListeners","id":"property-hasListeners"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.data.Model","meta":{"private":true},"name":"internalId","id":"property-internalId"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.data.Model","meta":{},"name":"isModel","id":"property-isModel"},{"tagname":"property","owner":"Ext.util.Observable","meta":{},"name":"isObservable","id":"property-isObservable"},{"tagname":"property","owner":"Ext.data.Model","meta":{},"name":"modified","id":"property-modified"},{"tagname":"property","owner":"Ext.data.Model","meta":{},"name":"phantom","id":"property-phantom"},{"tagname":"property","owner":"Ext.data.Model","meta":{},"name":"raw","id":"property-raw"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"},{"tagname":"property","owner":"Ext.data.Model","meta":{},"name":"store","id":"property-store"},{"tagname":"property","owner":"Ext.data.Model","meta":{},"name":"stores","id":"property-stores"}],"cfg":[{"tagname":"cfg","owner":"Ext.data.Model","meta":{},"name":"associations","id":"cfg-associations"},{"tagname":"cfg","owner":"Ext.data.Model","meta":{},"name":"belongsTo","id":"cfg-belongsTo"},{"tagname":"cfg","owner":"Ext.data.Model","meta":{},"name":"clientIdProperty","id":"cfg-clientIdProperty"},{"tagname":"cfg","owner":"Ext.data.Model","meta":{},"name":"defaultProxyType","id":"cfg-defaultProxyType"},{"tagname":"cfg","owner":"Ext.data.Model","meta":{},"name":"fields","id":"cfg-fields"},{"tagname":"cfg","owner":"Ext.data.Model","meta":{},"name":"hasMany","id":"cfg-hasMany"},{"tagname":"cfg","owner":"Ext.data.Model","meta":{},"name":"idProperty","id":"cfg-idProperty"},{"tagname":"cfg","owner":"Ext.data.Model","meta":{"markdown":true},"name":"idgen","id":"cfg-idgen"},{"tagname":"cfg","owner":"Ext.util.Observable","meta":{},"name":"listeners","id":"cfg-listeners"},{"tagname":"cfg","owner":"Ext.data.Model","meta":{"deprecated":{"text":"This config is deprecated. In future this will no longer be configurable and will be data."}},"name":"persistenceProperty","id":"cfg-persistenceProperty"},{"tagname":"cfg","owner":"Ext.data.Model","meta":{},"name":"proxy","id":"cfg-proxy"},{"tagname":"cfg","owner":"Ext.data.Model","meta":{},"name":"validations","id":"cfg-validations"}],"css_var":[],"method":[{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"constructor","id":"method-constructor"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"addEvents","id":"method-addEvents"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"addListener","id":"method-addListener"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"addManagedListener","id":"method-addManagedListener"},{"tagname":"method","owner":"Ext.data.Model","meta":{"private":true},"name":"afterCommit","id":"method-afterCommit"},{"tagname":"method","owner":"Ext.data.Model","meta":{"private":true},"name":"afterEdit","id":"method-afterEdit"},{"tagname":"method","owner":"Ext.data.Model","meta":{"private":true},"name":"afterReject","id":"method-afterReject"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"beginEdit","id":"method-beginEdit"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.data.Model","meta":{"private":true},"name":"callStore","id":"method-callStore"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"cancelEdit","id":"method-cancelEdit"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"captureArgs","id":"method-captureArgs"},{"tagname":"method","owner":"Ext.data.Model","meta":{"private":true},"name":"changeId","id":"method-changeId"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"clearListeners","id":"method-clearListeners"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"clearManagedListeners","id":"method-clearManagedListeners"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"commit","id":"method-commit"},{"tagname":"method","owner":"Ext.data.Model","meta":{"private":true},"name":"compareConvertFields","id":"method-compareConvertFields"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"continueFireEvent","id":"method-continueFireEvent"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"copy","id":"method-copy"},{"tagname":"method","owner":"Ext.data.Model","meta":{"private":true},"name":"copyFrom","id":"method-copyFrom"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"createRelayer","id":"method-createRelayer"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"enableBubble","id":"method-enableBubble"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"endEdit","id":"method-endEdit"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"fireEvent","id":"method-fireEvent"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"fireEventArgs","id":"method-fireEventArgs"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"get","id":"method-get"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"getAssociatedData","id":"method-getAssociatedData"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"getBubbleParent","id":"method-getBubbleParent"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"getChanges","id":"method-getChanges"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"getData","id":"method-getData"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"getId","id":"method-getId"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.data.Model","meta":{"private":true},"name":"getModifiedFieldNames","id":"method-getModifiedFieldNames"},{"tagname":"method","owner":"Ext.data.Model","meta":{"private":true},"name":"getObservableId","id":"method-getObservableId"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"getProxy","id":"method-getProxy"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.data.Model","meta":{"private":true},"name":"hasId","id":"method-hasId"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"hasListener","id":"method-hasListener"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.data.Model","meta":{"private":true},"name":"isEqual","id":"method-isEqual"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"isModified","id":"method-isModified"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"isValid","id":"method-isValid"},{"tagname":"method","owner":"Ext.data.Model","meta":{"private":true},"name":"itemNameFn","id":"method-itemNameFn"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"join","id":"method-join"},{"tagname":"method","owner":"Ext.data.Model","meta":{"private":true},"name":"markDirty","id":"method-markDirty"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"mon","id":"method-mon"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"mun","id":"method-mun"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"on","id":"method-on"},{"tagname":"method","owner":"Ext.data.Model","meta":{"private":true},"name":"onClassExtended","id":"method-onClassExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.data.Model","meta":{"private":true},"name":"prepareAssociatedData","id":"method-prepareAssociatedData"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"prepareClass","id":"method-prepareClass"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"reject","id":"method-reject"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"relayEvents","id":"method-relayEvents"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"removeListener","id":"method-removeListener"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"removeManagedListener","id":"method-removeManagedListener"},{"tagname":"method","owner":"Ext.util.Observable","meta":{"private":true},"name":"removeManagedListenerItem","id":"method-removeManagedListenerItem"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"resumeEvent","id":"method-resumeEvent"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"resumeEvents","id":"method-resumeEvents"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"save","id":"method-save"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"set","id":"method-set"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"setDirty","id":"method-setDirty"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"setId","id":"method-setId"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"setProxy","id":"method-setProxy"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"suspendEvent","id":"method-suspendEvent"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"suspendEvents","id":"method-suspendEvents"},{"tagname":"method","owner":"Ext.util.Observable","meta":{},"name":"un","id":"method-un"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"unjoin","id":"method-unjoin"},{"tagname":"method","owner":"Ext.data.Model","meta":{},"name":"validate","id":"method-validate"}],"event":[{"tagname":"event","owner":"Ext.data.Model","meta":{},"name":"idchanged","id":"event-idchanged"}],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.data.Model","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.data.Model","mixins":["Ext.util.Observable"],"mixedInto":[]});