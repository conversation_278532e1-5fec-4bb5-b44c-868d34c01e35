Ext.data.JsonP.Global_CSS({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":null,"uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Files</h4><div class='dependency'><a href='source/Component.scss3.html#Global_CSS' target='_blank'>Component.scss</a></div><div class='dependency'><a href='source/Component.scss5.html#Global_CSS' target='_blank'>Component.scss</a></div></pre><div class='doc-contents'>\n</div><div class='members'><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-css_var'>CSS Variables</h3><div class='subsection'><div id='css_var-S-base-color' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss5.html#Global_CSS-css_var-S-base-color' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-base-color' class='name expandable'>$base-color</a><span> : color</span></div><div class='description'><div class='short'>The base color to be used throughout the theme. ...</div><div class='long'><p>The base color to be used throughout the theme.</p>\n<p>Defaults to: <code>#808080</code></p></div></div></div><div id='css_var-S-base-gradient' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss5.html#Global_CSS-css_var-S-base-gradient' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-base-gradient' class='name expandable'>$base-gradient</a><span> : string</span></div><div class='description'><div class='short'>The base gradient to be used throughout the theme. ...</div><div class='long'><p>The base gradient to be used throughout the theme.</p>\n<p>Defaults to: <code>'matte'</code></p></div></div></div><div id='css_var-S-body-background-color' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss5.html#Global_CSS-css_var-S-body-background-color' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-body-background-color' class='name expandable'>$body-background-color</a><span> : color</span></div><div class='description'><div class='short'>Background color to apply to the body element ...</div><div class='long'><p>Background color to apply to the body element</p>\n<p>Defaults to: <code>transparent</code></p></div></div></div><div id='css_var-S-color' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss5.html#Global_CSS-css_var-S-color' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-color' class='name expandable'>$color</a><span> : color</span></div><div class='description'><div class='short'>The default text color to be used throughout the theme. ...</div><div class='long'><p>The default text color to be used throughout the theme.</p>\n<p>Defaults to: <code>#000</code></p></div></div></div><div id='css_var-S-css-shadow-border-radius' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss3.html#Global_CSS-css_var-S-css-shadow-border-radius' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-css-shadow-border-radius' class='name expandable'>$css-shadow-border-radius</a><span> : measurement</span></div><div class='description'><div class='short'>The border radius for CSS shadows ...</div><div class='long'><p>The border radius for CSS shadows</p>\n<p>Defaults to: <code>5px</code></p></div></div></div><div id='css_var-S-font-family' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss5.html#Global_CSS-css_var-S-font-family' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-font-family' class='name expandable'>$font-family</a><span> : string</span></div><div class='description'><div class='short'>The default font-family to be used throughout the theme. ...</div><div class='long'><p>The default font-family to be used throughout the theme.</p>\n<p>Defaults to: <code>helvetica , arial , verdana , sans-serif</code></p></div></div></div><div id='css_var-S-font-size' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss5.html#Global_CSS-css_var-S-font-size' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-font-size' class='name expandable'>$font-size</a><span> : string</span></div><div class='description'><div class='short'>The default font-family to be used throughout the theme. ...</div><div class='long'><p>The default font-family to be used throughout the theme.</p>\n<p>Defaults to: <code>13px</code></p></div></div></div><div id='css_var-S-image-extension' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss3.html#Global_CSS-css_var-S-image-extension' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-image-extension' class='name expandable'>$image-extension</a><span> : string</span></div><div class='description'><div class='short'>default file extension to use for images (defaults to 'png'). ...</div><div class='long'><p>default file extension to use for images (defaults to 'png').</p>\n<p>Defaults to: <code>'png'</code></p></div></div></div><div id='css_var-S-image-search-path' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss3.html#Global_CSS-css_var-S-image-search-path' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-image-search-path' class='name expandable'>$image-search-path</a><span> : string</span></div><div class='description'><div class='short'>Default search path for images ...</div><div class='long'><p>Default search path for images</p>\n<p>Defaults to: <code>'.'</code></p></div></div></div><div id='css_var-S-include-chrome' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss3.html#Global_CSS-css_var-S-include-chrome' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-include-chrome' class='name expandable'>$include-chrome</a><span> : boolean</span></div><div class='description'><div class='short'>True to include Chrome specific rules ...</div><div class='long'><p>True to include Chrome specific rules</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='css_var-S-include-content-box' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss3.html#Global_CSS-css_var-S-include-content-box' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-include-content-box' class='name expandable'>$include-content-box</a><span> : boolean</span></div><div class='description'><div class='short'>True to include rules for browsers that do not support the border-box model\n(IE6 strict and IE7 strict) ...</div><div class='long'><p>True to include rules for browsers that do not support the border-box model\n(IE6 strict and IE7 strict)</p>\n<p>Defaults to: <code>$include-ie</code></p></div></div></div><div id='css_var-S-include-default-uis' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss3.html#Global_CSS-css_var-S-include-default-uis' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-include-default-uis' class='name expandable'>$include-default-uis</a><span> : boolean</span></div><div class='description'><div class='short'>True to include the default UI for each component. ...</div><div class='long'><p>True to include the default UI for each component.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='css_var-S-include-ff' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss3.html#Global_CSS-css_var-S-include-ff' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-include-ff' class='name expandable'>$include-ff</a><span> : boolean</span></div><div class='description'><div class='short'>True to include Firefox specific rules ...</div><div class='long'><p>True to include Firefox specific rules</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='css_var-S-include-ie' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss3.html#Global_CSS-css_var-S-include-ie' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-include-ie' class='name expandable'>$include-ie</a><span> : boolean</span></div><div class='description'><div class='short'>True to include Internet Explorer specific rules ...</div><div class='long'><p>True to include Internet Explorer specific rules</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='css_var-S-include-not-found-images' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss3.html#Global_CSS-css_var-S-include-not-found-images' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-include-not-found-images' class='name not-expandable'>$include-not-found-images</a><span> : boolean</span></div><div class='description'><div class='short'><p>True to include files which are not found when compiling your SASS</p>\n</div><div class='long'><p>True to include files which are not found when compiling your SASS</p>\n</div></div></div><div id='css_var-S-include-opera' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss3.html#Global_CSS-css_var-S-include-opera' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-include-opera' class='name expandable'>$include-opera</a><span> : boolean</span></div><div class='description'><div class='short'>True to include Opera specific rules ...</div><div class='long'><p>True to include Opera specific rules</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='css_var-S-include-safari' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss3.html#Global_CSS-css_var-S-include-safari' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-include-safari' class='name expandable'>$include-safari</a><span> : boolean</span></div><div class='description'><div class='short'>True to include Safari specific rules ...</div><div class='long'><p>True to include Safari specific rules</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='css_var-S-include-shadow-images' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss3.html#Global_CSS-css_var-S-include-shadow-images' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-include-shadow-images' class='name expandable'>$include-shadow-images</a><span> : color</span></div><div class='description'><div class='short'>True to include all shadow images. ...</div><div class='long'><p>True to include all shadow images.</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='css_var-S-include-webkit' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss3.html#Global_CSS-css_var-S-include-webkit' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-include-webkit' class='name expandable'>$include-webkit</a><span> : boolean</span></div><div class='description'><div class='short'>True to include Webkit specific rules ...</div><div class='long'><p>True to include Webkit specific rules</p>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='css_var-S-neutral-color' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss5.html#Global_CSS-css_var-S-neutral-color' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-neutral-color' class='name expandable'>$neutral-color</a><span> : color</span></div><div class='description'><div class='short'>The neutral color to be used throughout the theme. ...</div><div class='long'><p>The neutral color to be used throughout the theme.</p>\n<p>Defaults to: <code>#dcdcdc</code></p></div></div></div><div id='css_var-S-prefix' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss3.html#Global_CSS-css_var-S-prefix' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-prefix' class='name expandable'>$prefix</a><span> : string</span></div><div class='description'><div class='short'>The prefix to be applied to all CSS selectors. ...</div><div class='long'><p>The prefix to be applied to all CSS selectors. If this is changed, it must also be changed in your\nJavaScript application.</p>\n<p>Defaults to: <code>'x-'</code></p></div></div></div><div id='css_var-S-relative-image-path-for-uis' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss3.html#Global_CSS-css_var-S-relative-image-path-for-uis' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-relative-image-path-for-uis' class='name expandable'>$relative-image-path-for-uis</a><span> : boolean/string</span></div><div class='description'><div class='short'>True to use a relative image path for all new UIs. ...</div><div class='long'><p>True to use a relative image path for all new UIs. If true, the path will be \"../images/\".\nIt can also be a string of the path value.\nIt defaults to false, which means it will look for the images in the ExtJS SDK folder.</p>\n<p>Defaults to: <code>false</code></p></div></div></div><div id='css_var-S-slicer-image-extension' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss3.html#Global_CSS-css_var-S-slicer-image-extension' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-slicer-image-extension' class='name expandable'>$slicer-image-extension</a><span> : string</span></div><div class='description'><div class='short'>default file extension to use for slicer images (defaults to 'gif'). ...</div><div class='long'><p>default file extension to use for slicer images (defaults to 'gif').</p>\n<p>Defaults to: <code>'gif'</code></p></div></div></div><div id='css_var-S-theme-resource-path' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/Component.scss3.html#Global_CSS-css_var-S-theme-resource-path' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_var-S-theme-resource-path' class='name expandable'>$theme-resource-path</a><span> : string</span></div><div class='description'><div class='short'>The base path relative to the CSS output directory to use for theme resources. ...</div><div class='long'><p>The base path relative to the CSS output directory to use for theme resources.  For example\nif the theme's images live one directory up from the generated CSS output in a directory\nnamed 'foo/images/', you would need to set this variable to '../foo/' in order for the image\npaths in the CSS output to be generated correctly. By default this is the same as the\nCSS output directory.</p>\n<p>Defaults to: <code>''</code></p></div></div></div></div></div><div class='members-section'><div class='definedBy'>Defined By</div><h3 class='members-title icon-css_mixin'>CSS Mixins</h3><div class='subsection'><div id='css_mixin-background-gradient' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Global_CSS'>Global_CSS</span><br/><a href='source/background-gradient.scss.html#Global_CSS-css_mixin-background-gradient' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Global_CSS-css_mixin-background-gradient' class='name expandable'>background-gradient</a>( <span class='pre'>$bg-color, [$type], [$direction]</span> )</div><div class='description'><div class='short'>Creates a background gradient. ...</div><div class='long'><p>Creates a background gradient.</p>\n\n<p>Example usage:</p>\n\n<pre><code>.foo {\n     @include background-gradient(#808080, matte, left);\n}\n</code></pre>\n<ul><li><span class='pre'>$bg-color</span> : Color<div class='sub-desc'><p>The background color of the gradient</p>\n</div></li><li><span class='pre'>$type</span> : String/List (optional)<div class='sub-desc'><p>The type of gradient to be used. Can either\nbe a String which is a predefined gradient name, or it can can be a list of color stops.\nIf null is passed, this mixin will still set the <code>background-color</code> to $bg-color.\nThe available predefined gradient names are:</p>\n\n<ul>\n<li>bevel</li>\n<li>glossy</li>\n<li>recessed</li>\n<li>matte</li>\n<li>matte-reverse</li>\n<li>panel-header</li>\n<li>tabbar</li>\n<li>tab</li>\n<li>tab-active</li>\n<li>tab-over</li>\n<li>tab-disabled</li>\n<li>grid-header</li>\n<li>grid-header-over</li>\n<li>grid-row-over</li>\n<li>grid-cell-special</li>\n<li>glossy-button</li>\n<li>glossy-button-over</li>\n<li>glossy-button-pressed</li>\n</ul>\n\n\n<p>Each of these gradient names corresponds to a function named linear-gradient[name].\nThemes can override these functions to customize the color stops that they return.\nFor example, to override the glossy-button gradient function add a function named\n\"linear-gradient-glossy-button\" to a file named \"sass/etc/mixins/background-gradient.scss\"\nin your theme.  The function should return the result of calling the Compass linear-gradient\nfunction with the desired direction and color-stop information for the gradient.  For example:</p>\n\n<pre><code>@function linear-gradient-glossy-button($direction, $bg-color) {\n    @return linear-gradient($direction, color_stops(\n        mix(#fff, $bg-color, 10%),\n        $bg-color 50%,\n        mix(#000, $bg-color, 5%) 51%,\n        $bg-color\n    ));\n}\n</code></pre>\n<p>Defaults to: <code>$base-gradient</code></p></div></li><li><span class='pre'>$direction</span> : String (optional)<div class='sub-desc'><p>The direction of the gradient. Can either be\n<code>top</code> or <code>left</code>.</p>\n<p>Defaults to: <code>top</code></p></div></li></ul></div></div></div></div></div></div></div>","superclasses":[],"meta":{},"requires":[],"html_meta":{},"statics":{"property":[],"cfg":[],"css_var":[],"method":[],"event":[],"css_mixin":[]},"files":[{"href":"Component.scss3.html#Global_CSS","filename":"Component.scss"},{"href":"Component.scss5.html#Global_CSS","filename":"Component.scss"}],"linenr":1,"members":{"property":[],"cfg":[],"css_var":[{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$base-color","id":"css_var-S-base-color"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$base-gradient","id":"css_var-S-base-gradient"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$body-background-color","id":"css_var-S-body-background-color"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$color","id":"css_var-S-color"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$css-shadow-border-radius","id":"css_var-S-css-shadow-border-radius"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$font-family","id":"css_var-S-font-family"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$font-size","id":"css_var-S-font-size"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$image-extension","id":"css_var-S-image-extension"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$image-search-path","id":"css_var-S-image-search-path"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$include-chrome","id":"css_var-S-include-chrome"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$include-content-box","id":"css_var-S-include-content-box"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$include-default-uis","id":"css_var-S-include-default-uis"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$include-ff","id":"css_var-S-include-ff"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$include-ie","id":"css_var-S-include-ie"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$include-not-found-images","id":"css_var-S-include-not-found-images"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$include-opera","id":"css_var-S-include-opera"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$include-safari","id":"css_var-S-include-safari"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$include-shadow-images","id":"css_var-S-include-shadow-images"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$include-webkit","id":"css_var-S-include-webkit"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$neutral-color","id":"css_var-S-neutral-color"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$prefix","id":"css_var-S-prefix"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$relative-image-path-for-uis","id":"css_var-S-relative-image-path-for-uis"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$slicer-image-extension","id":"css_var-S-slicer-image-extension"},{"tagname":"css_var","owner":"Global_CSS","meta":{},"name":"$theme-resource-path","id":"css_var-S-theme-resource-path"}],"method":[],"event":[],"css_mixin":[{"tagname":"css_mixin","owner":"Global_CSS","meta":{},"name":"background-gradient","id":"css_mixin-background-gradient"}]},"inheritable":null,"private":null,"component":false,"name":"Global_CSS","singleton":false,"override":null,"inheritdoc":null,"id":"class-Global_CSS","mixins":[],"mixedInto":[]});