Ext.data.JsonP.Ext_util_Positionable({"alternateClassNames":[],"aliases":{},"enum":null,"parentMixins":[],"tagname":"class","subclasses":[],"extends":"Ext.Base","uses":[],"html":"<div><pre class=\"hierarchy\"><h4>Hierarchy</h4><div class='subclass first-child'><a href='#!/api/Ext.Base' rel='Ext.Base' class='docClass'>Ext.Base</a><div class='subclass '><strong>Ext.util.Positionable</strong></div></div><h4>Mixed into</h4><div class='dependency'><a href='#!/api/Ext.AbstractComponent' rel='Ext.AbstractComponent' class='docClass'>Ext.AbstractComponent</a></div><h4>Files</h4><div class='dependency'><a href='source/Positionable.html#Ext-util-Positionable' target='_blank'>Positionable.js</a></div></pre><div class='doc-contents'><p>This mixin provides a common interface for objects that can be positioned, e.g.\n<a href=\"#!/api/Ext.Component\" rel=\"Ext.Component\" class=\"docClass\">Components</a> and <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Elements</a></p>\n</div><div class='members'><div class='members-section'><h3 class='members-title icon-property'>Properties</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Properties</h3><div id='property-S-className' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-S-className' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-S-className' class='name expandable'>$className</a><span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>'Ext.Base'</code></p></div></div></div><div id='property-_alignRe' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-property-_alignRe' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-property-_alignRe' class='name expandable'>_alignRe</a><span> : <a href=\"#!/api/RegExp\" rel=\"RegExp\" class=\"docClass\">RegExp</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>/^([a-z]+)-([a-z]+)(\\?)?$/</code></p></div></div></div><div id='property-_positionTopLeft' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-property-_positionTopLeft' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-property-_positionTopLeft' class='name expandable'>_positionTopLeft</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>['position', 'top', 'left']</code></p></div></div></div><div id='property-configMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-configMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-configMap' class='name expandable'>configMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-convertPositionSpec' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-property-convertPositionSpec' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-property-convertPositionSpec' class='name expandable'>convertPositionSpec</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'>By default this method does nothing but return the position spec passed to it. ...</div><div class='long'><p>By default this method does nothing but return the position spec passed to it. In\nrtl mode it is overridden to convert \"l\" to \"r\" and vice versa when required.</p>\n</div></div></div><div id='property-initConfigList' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigList' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigList' class='name expandable'>initConfigList</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div><div id='property-initConfigMap' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-initConfigMap' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-initConfigMap' class='name expandable'>initConfigMap</a><span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>{}</code></p></div></div></div><div id='property-isInstance' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-isInstance' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-isInstance' class='name expandable'>isInstance</a><span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>true</code></p></div></div></div><div id='property-self' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-property-self' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-property-self' class='name expandable'>self</a><span> : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the current class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the current class from which this object was instantiated. Unlike <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>,\n<code>this.self</code> is scope-dependent and it's meant to be used for dynamic inheritance. See <a href=\"#!/api/Ext.Base-method-statics\" rel=\"Ext.Base-method-statics\" class=\"docClass\">statics</a>\nfor a detailed comparison</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        alert(this.self.speciesName); // dependent on 'this'\n    },\n\n    clone: function() {\n        return new this.self();\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n    statics: {\n        speciesName: 'Snow Leopard'         // My.SnowLeopard.speciesName = 'Snow Leopard'\n    }\n});\n\nvar cat = new My.Cat();                     // alerts 'Cat'\nvar snowLeopard = new My.SnowLeopard();     // alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));             // alerts 'My.SnowLeopard'\n</code></pre>\n</div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Properties</h3><div id='static-property-S-onExtended' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-property-S-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-property-S-onExtended' class='name expandable'>$onExtended</a><span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a></span><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Defaults to: <code>[]</code></p></div></div></div></div></div><div class='members-section'><h3 class='members-title icon-method'>Methods</h3><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Instance Methods</h3><div id='method-adjustForConstraints' class='member first-child not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-adjustForConstraints' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-adjustForConstraints' class='name expandable'>adjustForConstraints</a>( <span class='pre'>xy, parent</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>private ==>  used outside of core\nTODO: currently only used by ToolTip. ...</div><div class='long'><p>private ==>  used outside of core\nTODO: currently only used by ToolTip. does this method belong here?</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xy</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>parent</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-afterSetPosition' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-afterSetPosition' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-afterSetPosition' class='name expandable'>afterSetPosition</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Stub implementation called after positioning. ...</div><div class='long'><p>Stub implementation called after positioning.\nMay be implemented in subclasses. AbstractComponent has an implementation.</p>\n</div></div></div><div id='method-alignTo' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-alignTo' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-alignTo' class='name expandable'>alignTo</a>( <span class='pre'>element, [position], [offsets], [animate]</span> ) : <a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Aligns the element with another element relative to the specified anchor points. ...</div><div class='long'><p>Aligns the element with another element relative to the specified anchor points. If\nthe other element is the document it aligns it to the viewport. The position\nparameter is optional, and can be specified in any one of the following formats:</p>\n\n<ul>\n<li><strong>Blank</strong>: Defaults to aligning the element's top-left corner to the target's\nbottom-left corner (\"tl-bl\").</li>\n<li><strong>One anchor (deprecated)</strong>: The passed anchor position is used as the target\nelement's anchor point.  The element being aligned will position its top-left\ncorner (tl) to that point. <em>This method has been deprecated in favor of the newer\ntwo anchor syntax below</em>.</li>\n<li><strong>Two anchors</strong>: If two values from the table below are passed separated by a dash,\nthe first value is used as the element's anchor point, and the second value is\nused as the target's anchor point.</li>\n</ul>\n\n\n<p>In addition to the anchor points, the position parameter also supports the \"?\"\ncharacter. If \"?\" is passed at the end of the position string, the element will\nattempt to align as specified, but the position will be adjusted to constrain to\nthe viewport if necessary. Note that the element being aligned might be swapped to\nalign to a different position than that specified in order to enforce the viewport\nconstraints. Following are all of the supported anchor positions:</p>\n\n<pre>Value  Description\n-----  -----------------------------\ntl     The top left corner (default)\nt      The center of the top edge\ntr     The top right corner\nl      The center of the left edge\nc      In the center of the element\nr      The center of the right edge\nbl     The bottom left corner\nb      The center of the bottom edge\nbr     The bottom right corner\n</pre>\n\n\n<p>Example Usage:</p>\n\n<pre><code>// align el to other-el using the default positioning\n// (\"tl-bl\", non-constrained)\nel.alignTo(\"other-el\");\n\n// align the top left corner of el with the top right corner of other-el\n// (constrained to viewport)\nel.alignTo(\"other-el\", \"tr?\");\n\n// align the bottom right corner of el with the center left edge of other-el\nel.alignTo(\"other-el\", \"br-l?\");\n\n// align the center of el with the bottom left corner of other-el and\n// adjust the x position by -6 pixels (and the y position by 0)\nel.alignTo(\"other-el\", \"c-bl\", [-6, 0]);\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>element</span> : <a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a>/HTMLElement/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The Positionable,\nHTMLElement, or id of the element to align to.</p>\n</div></li><li><span class='pre'>position</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The position to align to</p>\n<p>Defaults to: <code>&quot;tl-bl?&quot;</code></p></div></li><li><span class='pre'>offsets</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[] (optional)<div class='sub-desc'><p>Offset the positioning by [x, y]</p>\n</div></li><li><span class='pre'>animate</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>true for the default animation or a standard\nElement animation config object</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-anchorTo' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-anchorTo' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-anchorTo' class='name expandable'>anchorTo</a>( <span class='pre'>element, [position], [offsets], [animate], [monitorScroll], [callback]</span> ) : <a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Anchors an element to another element and realigns it when the window is resized. ...</div><div class='long'><p>Anchors an element to another element and realigns it when the window is resized.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>element</span> : <a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a>/HTMLElement/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The Positionable,\nHTMLElement, or id of the element to align to.</p>\n</div></li><li><span class='pre'>position</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The position to align to</p>\n<p>Defaults to: <code>&quot;tl-bl?&quot;</code></p></div></li><li><span class='pre'>offsets</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[] (optional)<div class='sub-desc'><p>Offset the positioning by [x, y]</p>\n</div></li><li><span class='pre'>animate</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>true for the default animation or a standard\nElement animation config object</p>\n</div></li><li><span class='pre'>monitorScroll</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a>/<a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>True to monitor body scroll and\nreposition. If this parameter is a number, it is used as the buffer delay in\nmilliseconds.</p>\n<p>Defaults to: <code>50</code></p></div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Function\" rel=\"Function\" class=\"docClass\">Function</a> (optional)<div class='sub-desc'><p>The function to call after the animation finishes</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-calculateAnchorXY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-calculateAnchorXY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-calculateAnchorXY' class='name expandable'>calculateAnchorXY</a>( <span class='pre'>[anchor], [extraX], [extraY], [size]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Calculates x,y coordinates specified by the anchor position on the element, adding\nextraX and extraY values. ...</div><div class='long'><p>Calculates x,y coordinates specified by the anchor position on the element, adding\nextraX and extraY values.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>anchor</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The specified anchor position.\nSee <a href=\"#!/api/Ext.util.Positionable-method-alignTo\" rel=\"Ext.util.Positionable-method-alignTo\" class=\"docClass\">alignTo</a> for details on supported anchor positions.</p>\n<p>Defaults to: <code>'tl'</code></p></div></li><li><span class='pre'>extraX</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>value to be added to the x coordinate</p>\n</div></li><li><span class='pre'>extraY</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>value to be added to the y coordinate</p>\n</div></li><li><span class='pre'>size</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>An object containing the size to use for calculating anchor\nposition {width: (target width), height: (target height)} (defaults to the\nelement's current size)</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</span><div class='sub-desc'><p>[x, y] An array containing the element's x and y coordinates</p>\n</div></li></ul></div></div></div><div id='method-calculateConstrainedPosition' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-calculateConstrainedPosition' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-calculateConstrainedPosition' class='name expandable'>calculateConstrainedPosition</a>( <span class='pre'>[constrainTo], [proposedPosition], [local], [proposedSize]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</div><div class='description'><div class='short'>Calculates the new [x,y] position to move this Positionable into a constrain region. ...</div><div class='long'><p>Calculates the new [x,y] position to move this Positionable into a constrain region.</p>\n\n<p>By default, this Positionable is constrained to be within the container it was added to, or the element it was\nrendered to.</p>\n\n<p>Priority is given to constraining the top and left within the constraint.</p>\n\n<p>An alternative constraint may be passed.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>constrainTo</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/HTMLElement/<a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.Element</a>/<a href=\"#!/api/Ext.util.Region\" rel=\"Ext.util.Region\" class=\"docClass\">Ext.util.Region</a> (optional)<div class='sub-desc'><p>The Element or <a href=\"#!/api/Ext.util.Region\" rel=\"Ext.util.Region\" class=\"docClass\">Region</a>\ninto which this Component is to be constrained. Defaults to the element into which this Positionable\nwas rendered, or this Component's {@link <a href=\"#!/api/Ext.Component-cfg-constrainTo\" rel=\"Ext.Component-cfg-constrainTo\" class=\"docClass\">Ext.Component.constrainTo</a>.</p>\n</div></li><li><span class='pre'>proposedPosition</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[] (optional)<div class='sub-desc'><p>A proposed <code>[X, Y]</code> position to test for validity\nand to coerce into constraints instead of using this Positionable's current position.</p>\n</div></li><li><span class='pre'>local</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>The proposedPosition is local <em>(relative to floatParent if a floating Component)</em></p>\n</div></li><li><span class='pre'>proposedSize</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[] (optional)<div class='sub-desc'><p>A proposed <code>[width, height]</code> size to use when calculating\nconstraints instead of using this Positionable's current size.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</span><div class='sub-desc'><p><strong>If</strong> the element <em>needs</em> to be translated, the new <code>[X, Y]</code> position within\nconstraints if possible, giving priority to keeping the top and left edge in the constrain region.\nOtherwise, <code>false</code>.</p>\n</div></li></ul></div></div></div><div id='method-callOverridden' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callOverridden' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callOverridden' class='name expandable'>callOverridden</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='deprecated signature' >deprecated</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the original method that was previously overridden with override\n\nExt.define('My.Cat', {\n    constructor: functi...</div><div class='long'><p>Call the original method that was previously overridden with <a href=\"#!/api/Ext.Base-static-method-override\" rel=\"Ext.Base-static-method-override\" class=\"docClass\">override</a></p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callOverridden();\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> </p>\n        <p>as of 4.1. Use <a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a> instead.</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callOverridden(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the overridden method</p>\n</div></li></ul></div></div></div><div id='method-callParent' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callParent' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callParent' class='name expandable'>callParent</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Call the \"parent\" method of the current method. ...</div><div class='long'><p>Call the \"parent\" method of the current method. That is the method previously\noverridden by derivation or by an override (see <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>).</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Base', {\n     constructor: function (x) {\n         this.x = x;\n     },\n\n     statics: {\n         method: function (x) {\n             return x;\n         }\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived', {\n     extend: 'My.Base',\n\n     constructor: function () {\n         this.callParent([21]);\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // alerts 21\n</code></pre>\n\n<p>This can be used with an override as follows:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.DerivedOverride', {\n     override: 'My.Derived',\n\n     constructor: function (x) {\n         this.callParent([x*2]); // calls original My.Derived constructor\n     }\n });\n\n var obj = new My.Derived();\n\n alert(obj.x);  // now alerts 42\n</code></pre>\n\n<p>This also works with static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2', {\n     extend: 'My.Base',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Base.method\n         }\n     }\n });\n\n alert(My.Base.method(10);     // alerts 10\n alert(My.Derived2.method(10); // alerts 20\n</code></pre>\n\n<p>Lastly, it also works with overridden static methods.</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Derived2Override', {\n     override: 'My.Derived2',\n\n     statics: {\n         method: function (x) {\n             return this.callParent([x*2]); // calls My.Derived2.method\n         }\n     }\n });\n\n alert(My.Derived2.method(10); // now alerts 40\n</code></pre>\n\n<p>To override a method and replace it and also call the superclass method, use\n<a href=\"#!/api/Ext.Base-method-callSuper\" rel=\"Ext.Base-method-callSuper\" class=\"docClass\">callSuper</a>. This is often done to patch a method to fix a bug.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callParent(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the parent method</p>\n</div></li></ul></div></div></div><div id='method-callSuper' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-callSuper' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-callSuper' class='name expandable'>callSuper</a>( <span class='pre'>args</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>This method is used by an override to call the superclass method but bypass any\noverridden method. ...</div><div class='long'><p>This method is used by an override to call the superclass method but bypass any\noverridden method. This is often done to \"patch\" a method that contains a bug\nbut for whatever reason cannot be fixed directly.</p>\n\n<p>Consider:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.Class', {\n     method: function () {\n         console.log('Good');\n     }\n });\n\n <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Ext.some.DerivedClass', {\n     method: function () {\n         console.log('Bad');\n\n         // ... logic but with a bug ...\n\n         this.callParent();\n     }\n });\n</code></pre>\n\n<p>To patch the bug in <code>DerivedClass.method</code>, the typical solution is to create an\noverride:</p>\n\n<pre><code> <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('App.paches.DerivedClass', {\n     override: 'Ext.some.DerivedClass',\n\n     method: function () {\n         console.log('Fixed');\n\n         // ... logic but with bug fixed ...\n\n         this.callSuper();\n     }\n });\n</code></pre>\n\n<p>The patch method cannot use <code>callParent</code> to call the superclass <code>method</code> since\nthat would call the overridden method containing the bug. In other words, the\nabove patch would only produce \"Fixed\" then \"Good\" in the console log, whereas,\nusing <code>callParent</code> would produce \"Fixed\" then \"Bad\" then \"Good\".</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>args</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/Arguments<div class='sub-desc'><p>The arguments, either an array or the <code>arguments</code> object\nfrom the current method, for example: <code>this.callSuper(arguments)</code></p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>Returns the result of calling the superclass method</p>\n</div></li></ul></div></div></div><div id='method-configClass' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-configClass' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-configClass' class='name expandable'>configClass</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div><div id='method-destroy' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-destroy' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-destroy' class='name expandable'>destroy</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<p>Overrides: <a href='#!/api/Ext.util.ElementContainer-method-destroy' rel='Ext.util.ElementContainer-method-destroy' class='docClass'>Ext.util.ElementContainer.destroy</a>, <a href='#!/api/Ext.AbstractComponent-method-destroy' rel='Ext.AbstractComponent-method-destroy' class='docClass'>Ext.AbstractComponent.destroy</a>, <a href='#!/api/Ext.AbstractPlugin-method-destroy' rel='Ext.AbstractPlugin-method-destroy' class='docClass'>Ext.AbstractPlugin.destroy</a></p></div></div></div><div id='method-getAlignToXY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-getAlignToXY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-getAlignToXY' class='name expandable'>getAlignToXY</a>( <span class='pre'>element, [position], [offsets]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</div><div class='description'><div class='short'>Gets the x,y coordinates to align this element with another element. ...</div><div class='long'><p>Gets the x,y coordinates to align this element with another element. See\n<a href=\"#!/api/Ext.util.Positionable-method-alignTo\" rel=\"Ext.util.Positionable-method-alignTo\" class=\"docClass\">alignTo</a> for more info on the supported position values.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>element</span> : <a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a>/HTMLElement/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The Positionable,\nHTMLElement, or id of the element to align to.</p>\n</div></li><li><span class='pre'>position</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The position to align to</p>\n<p>Defaults to: <code>&quot;tl-bl?&quot;</code></p></div></li><li><span class='pre'>offsets</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[] (optional)<div class='sub-desc'><p>Offset the positioning by [x, y]</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</span><div class='sub-desc'><p>[x, y]</p>\n</div></li></ul></div></div></div><div id='method-getAnchor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-getAnchor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-getAnchor' class='name expandable'>getAnchor</a>( <span class='pre'></span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'>private ...</div><div class='long'><p>private</p>\n</div></div></div><div id='method-getAnchorToXY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-getAnchorToXY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-getAnchorToXY' class='name expandable'>getAnchorToXY</a>( <span class='pre'>el, [anchor], [local], [size]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]<strong class='private signature' >private</strong></div><div class='description'><div class='short'>Gets the x,y coordinates of an element specified by the anchor position on the\nelement. ...</div><div class='long'><p>Gets the x,y coordinates of an element specified by the anchor position on the\nelement.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>el</span> : <a href=\"#!/api/Ext.dom.Element\" rel=\"Ext.dom.Element\" class=\"docClass\">Ext.dom.Element</a><div class='sub-desc'><p>The element</p>\n</div></li><li><span class='pre'>anchor</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The specified anchor position.\nSee <a href=\"#!/api/Ext.util.Positionable-method-alignTo\" rel=\"Ext.util.Positionable-method-alignTo\" class=\"docClass\">alignTo</a> for details on supported anchor positions.</p>\n<p>Defaults to: <code>'tl'</code></p></div></li><li><span class='pre'>local</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to get the local (element top/left-relative) anchor\nposition instead of page coordinates</p>\n</div></li><li><span class='pre'>size</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>An object containing the size to use for calculating anchor\nposition {width: (target width), height: (target height)} (defaults to the\nelement's current size)</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</span><div class='sub-desc'><p>[x, y] An array containing the element's x and y coordinates</p>\n</div></li></ul></div></div></div><div id='method-getAnchorXY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-getAnchorXY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-getAnchorXY' class='name expandable'>getAnchorXY</a>( <span class='pre'>[anchor], [local], [size]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</div><div class='description'><div class='short'>Gets the x,y coordinates specified by the anchor position on the element. ...</div><div class='long'><p>Gets the x,y coordinates specified by the anchor position on the element.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>anchor</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>The specified anchor position.\nSee <a href=\"#!/api/Ext.util.Positionable-method-alignTo\" rel=\"Ext.util.Positionable-method-alignTo\" class=\"docClass\">alignTo</a> for details on supported anchor positions.</p>\n<p>Defaults to: <code>'tl'</code></p></div></li><li><span class='pre'>local</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>True to get the local (element top/left-relative) anchor\nposition instead of page coordinates</p>\n</div></li><li><span class='pre'>size</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>An object containing the size to use for calculating anchor\nposition {width: (target width), height: (target height)} (defaults to the\nelement's current size)</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</span><div class='sub-desc'><p>[x, y] An array containing the element's x and y coordinates</p>\n</div></li></ul></div></div></div><div id='method-getBorderPadding' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-getBorderPadding' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-getBorderPadding' class='name expandable'>getBorderPadding</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Returns the size of the element's borders and padding. ...</div><div class='long'><p>Returns the size of the element's borders and padding.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>an object with the following numeric properties\n- beforeX\n- afterX\n- beforeY\n- afterY</p>\n</div></li></ul></div></div></div><div id='method-getBox' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-getBox' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-getBox' class='name expandable'>getBox</a>( <span class='pre'>[contentBox], [local]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Return an object defining the area of this Element which can be passed to\nsetBox to set another Element's size/locati...</div><div class='long'><p>Return an object defining the area of this Element which can be passed to\n<a href=\"#!/api/Ext.util.Positionable-method-setBox\" rel=\"Ext.util.Positionable-method-setBox\" class=\"docClass\">setBox</a> to set another Element's size/location to match this element.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>contentBox</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>If true a box for the content of the element is\nreturned.</p>\n</div></li><li><span class='pre'>local</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a> (optional)<div class='sub-desc'><p>If true the element's left and top relative to its\n<code>offsetParent</code> are returned instead of page x/y.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>box An object in the format:</p>\n\n<pre><code>{\n    x: &lt;Element's X position&gt;,\n    y: &lt;Element's Y position&gt;,\n    left: &lt;Element's X position (an alias for x)&gt;,\n    top: &lt;Element's Y position (an alias for y)&gt;,\n    width: &lt;Element's width&gt;,\n    height: &lt;Element's height&gt;,\n    bottom: &lt;Element's lower bound&gt;,\n    right: &lt;Element's rightmost bound&gt;\n}\n</code></pre>\n\n<p>The returned object may also be addressed as an Array where index 0 contains the X\nposition and index 1 contains the Y position. The result may also be used for\n<a href=\"#!/api/Ext.util.Positionable-method-setXY\" rel=\"Ext.util.Positionable-method-setXY\" class=\"docClass\">setXY</a></p>\n</div></li></ul></div></div></div><div id='method-getConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getConfig' class='name expandable'>getConfig</a>( <span class='pre'>name</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-getConstrainVector' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-getConstrainVector' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-getConstrainVector' class='name expandable'>getConstrainVector</a>( <span class='pre'>[constrainTo], [proposedPosition], [proposedSize]</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]/<a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></div><div class='description'><div class='short'>Returns the [X, Y] vector by which this Positionable's element must be translated to make a best\nattempt to constrain...</div><div class='long'><p>Returns the <code>[X, Y]</code> vector by which this Positionable's element must be translated to make a best\nattempt to constrain within the passed constraint. Returns <code>false</code> if the element\ndoes not need to be moved.</p>\n\n<p>Priority is given to constraining the top and left within the constraint.</p>\n\n<p>The constraint may either be an existing element into which the element is to be\nconstrained, or a <a href=\"#!/api/Ext.util.Region\" rel=\"Ext.util.Region\" class=\"docClass\">Region</a> into which this element is to be\nconstrained.</p>\n\n<p>By default, any extra shadow around the element is <strong>not</strong> included in the constrain calculations - the edges\nof the element are used as the element bounds. To constrain the shadow within the constrain region, set the\n<code>constrainShadow</code> property on this element to <code>true</code>.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>constrainTo</span> : <a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a>/HTMLElement/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Ext.util.Region\" rel=\"Ext.util.Region\" class=\"docClass\">Ext.util.Region</a> (optional)<div class='sub-desc'><p>The\nPositionable, HTMLElement, element id, or Region into which the element is to be\nconstrained.</p>\n</div></li><li><span class='pre'>proposedPosition</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[] (optional)<div class='sub-desc'><p>A proposed <code>[X, Y]</code> position to test for validity\nand to produce a vector for instead of using the element's current position</p>\n</div></li><li><span class='pre'>proposedSize</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[] (optional)<div class='sub-desc'><p>A proposed <code>[width, height]</code> size to constrain\ninstead of using the element's current size</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]/<a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a></span><div class='sub-desc'><p><strong>If</strong> the element <em>needs</em> to be translated, an <code>[X, Y]</code>\nvector by which this element must be translated. Otherwise, <code>false</code>.</p>\n</div></li></ul></div></div></div><div id='method-getInitialConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-getInitialConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-getInitialConfig' class='name expandable'>getInitialConfig</a>( <span class='pre'>[name]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</div><div class='description'><div class='short'>Returns the initial configuration passed to constructor when instantiating\nthis class. ...</div><div class='long'><p>Returns the initial configuration passed to constructor when instantiating\nthis class.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a> (optional)<div class='sub-desc'><p>Name of the config option to return.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a>/Mixed</span><div class='sub-desc'><p>The full config object or a single config value\nwhen <code>name</code> parameter specified.</p>\n</div></li></ul></div></div></div><div id='method-getLocalX' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-getLocalX' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-getLocalX' class='name expandable'>getLocalX</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the x coordinate of this element reletive to its offsetParent. ...</div><div class='long'><p>Returns the x coordinate of this element reletive to its <code>offsetParent</code>.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The local x coordinate</p>\n</div></li></ul></div></div></div><div id='method-getLocalXY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-getLocalXY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-getLocalXY' class='name expandable'>getLocalXY</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</div><div class='description'><div class='short'>Returns the x and y coordinates of this element relative to its offsetParent. ...</div><div class='long'><p>Returns the x and y coordinates of this element relative to its <code>offsetParent</code>.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</span><div class='sub-desc'><p>The local XY position of the element</p>\n</div></li></ul></div></div></div><div id='method-getLocalY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-getLocalY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-getLocalY' class='name expandable'>getLocalY</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Returns the y coordinate of this element reletive to its offsetParent. ...</div><div class='long'><p>Returns the y coordinate of this element reletive to its <code>offsetParent</code>.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The local y coordinate</p>\n</div></li></ul></div></div></div><div id='method-getOffsetsTo' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-getOffsetsTo' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-getOffsetsTo' class='name expandable'>getOffsetsTo</a>( <span class='pre'>offsetsTo</span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</div><div class='description'><div class='short'>Returns the offsets of this element from the passed element. ...</div><div class='long'><p>Returns the offsets of this element from the passed element. The element must both\nbe part of the DOM tree and not have display:none to have page coordinates.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>offsetsTo</span> : <a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a>/HTMLElement/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The Positionable,\nHTMLElement, or element id to get get the offsets from.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</span><div class='sub-desc'><p>The XY page offsets (e.g. <code>[100, -200]</code>)</p>\n</div></li></ul></div></div></div><div id='method-getRegion' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-getRegion' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-getRegion' class='name expandable'>getRegion</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.util.Region\" rel=\"Ext.util.Region\" class=\"docClass\">Ext.util.Region</a></div><div class='description'><div class='short'>Returns a region object that defines the area of this element. ...</div><div class='long'><p>Returns a region object that defines the area of this element.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Region\" rel=\"Ext.util.Region\" class=\"docClass\">Ext.util.Region</a></span><div class='sub-desc'><p>A Region containing \"top, left, bottom, right\" properties.</p>\n</div></li></ul></div></div></div><div id='method-getViewRegion' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-getViewRegion' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-getViewRegion' class='name expandable'>getViewRegion</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.util.Region\" rel=\"Ext.util.Region\" class=\"docClass\">Ext.util.Region</a></div><div class='description'><div class='short'>Returns the content region of this element. ...</div><div class='long'><p>Returns the <strong>content</strong> region of this element. That is the region within the borders\nand padding.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Region\" rel=\"Ext.util.Region\" class=\"docClass\">Ext.util.Region</a></span><div class='sub-desc'><p>A Region containing \"top, left, bottom, right\" member data.</p>\n</div></li></ul></div></div></div><div id='method-getX' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-getX' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-getX' class='name expandable'>getX</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Gets the current X position of the DOM element based on page coordinates. ...</div><div class='long'><p>Gets the current X position of the DOM element based on page coordinates.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The X position of the element</p>\n</div></li></ul></div></div></div><div id='method-getXY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-getXY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-getXY' class='name expandable'>getXY</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</div><div class='description'><div class='short'>Gets the current position of the DOM element based on page coordinates. ...</div><div class='long'><p>Gets the current position of the DOM element based on page coordinates.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]</span><div class='sub-desc'><p>The XY position of the element</p>\n</div></li></ul></div></div></div><div id='method-getY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-getY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-getY' class='name expandable'>getY</a>( <span class='pre'></span> ) : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></div><div class='description'><div class='short'>Gets the current Y position of the DOM element based on page coordinates. ...</div><div class='long'><p>Gets the current Y position of the DOM element based on page coordinates.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a></span><div class='sub-desc'><p>The Y position of the element</p>\n</div></li></ul></div></div></div><div id='method-hasConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-hasConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-hasConfig' class='name expandable'>hasConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-initConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-initConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-initConfig' class='name expandable'>initConfig</a>( <span class='pre'>config</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Initialize configuration for this class. ...</div><div class='long'><p>Initialize configuration for this class. a typical example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Class', {\n    // The default config\n    config: {\n        name: 'Awesome',\n        isAwesome: true\n    },\n\n    constructor: function(config) {\n        this.initConfig(config);\n    }\n});\n\nvar awesome = new My.awesome.Class({\n    name: 'Super Awesome'\n});\n\nalert(awesome.getName()); // 'Super Awesome'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-move' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-move' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-move' class='name expandable'>move</a>( <span class='pre'>direction, distance, [animate]</span> )</div><div class='description'><div class='short'>Move the element relative to its current position. ...</div><div class='long'><p>Move the element relative to its current position.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>direction</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>Possible values are:</p>\n\n<ul>\n<li><code>\"l\"</code> (or <code>\"left\"</code>)</li>\n<li><code>\"r\"</code> (or <code>\"right\"</code>)</li>\n<li><code>\"t\"</code> (or <code>\"top\"</code>, or <code>\"up\"</code>)</li>\n<li><code>\"b\"</code> (or <code>\"bottom\"</code>, or <code>\"down\"</code>)</li>\n</ul>\n\n</div></li><li><span class='pre'>distance</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>How far to move the element in pixels</p>\n</div></li><li><span class='pre'>animate</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>true for the default animation or a standard\nElement animation config object</p>\n</div></li></ul></div></div></div><div id='method-onConfigUpdate' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-onConfigUpdate' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-onConfigUpdate' class='name expandable'>onConfigUpdate</a>( <span class='pre'>names, callback, scope</span> )<strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>names</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>callback</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-removeAnchor' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-removeAnchor' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-removeAnchor' class='name expandable'>removeAnchor</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Remove any anchor to this element. ...</div><div class='long'><p>Remove any anchor to this element. See <a href=\"#!/api/Ext.util.Positionable-method-anchorTo\" rel=\"Ext.util.Positionable-method-anchorTo\" class=\"docClass\">anchorTo</a>.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setBox' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-setBox' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-setBox' class='name expandable'>setBox</a>( <span class='pre'>box, [animate]</span> ) : <a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Sets the element's box. ...</div><div class='long'><p>Sets the element's box. If animate is true then x, y, width, and height will be\nanimated concurrently.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>box</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The box to fill {x, y, width, height}</p>\n</div></li><li><span class='pre'>animate</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>true for the default animation or a standard\nElement animation config object</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setConfig' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-setConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-setConfig' class='name expandable'>setConfig</a>( <span class='pre'>config, applyIfNotSet</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>applyIfNotSet</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setLocalX' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-setLocalX' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-setLocalX' class='name expandable'>setLocalX</a>( <span class='pre'>x</span> ) : <a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Sets the local x coordinate of this element using CSS style. ...</div><div class='long'><p>Sets the local x coordinate of this element using CSS style. When used on an\nabsolute positioned element this method is symmetrical with <a href=\"#!/api/Ext.util.Positionable-method-getLocalX\" rel=\"Ext.util.Positionable-method-getLocalX\" class=\"docClass\">getLocalX</a>, but\nmay not be symmetrical when used on a relatively positioned element.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>x</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The x coordinate. A value of <code>null</code> sets the left style to 'auto'.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setLocalXY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-setLocalXY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-setLocalXY' class='name expandable'>setLocalXY</a>( <span class='pre'>x, [y]</span> ) : <a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Sets the local x and y coordinates of this element using CSS style. ...</div><div class='long'><p>Sets the local x and y coordinates of this element using CSS style. When used on an\nabsolute positioned element this method is symmetrical with <a href=\"#!/api/Ext.util.Positionable-method-getLocalXY\" rel=\"Ext.util.Positionable-method-getLocalXY\" class=\"docClass\">getLocalXY</a>, but\nmay not be symmetrical when used on a relatively positioned element.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>x</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The x coordinate or an array containing [x, y]. A value of\n<code>null</code> sets the left style to 'auto'</p>\n</div></li><li><span class='pre'>y</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>The y coordinate, required if x is not an array. A value of\n<code>null</code> sets the top style to 'auto'</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setLocalY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-setLocalY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-setLocalY' class='name expandable'>setLocalY</a>( <span class='pre'>y</span> ) : <a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Sets the local y coordinate of this element using CSS style. ...</div><div class='long'><p>Sets the local y coordinate of this element using CSS style. When used on an\nabsolute positioned element this method is symmetrical with <a href=\"#!/api/Ext.util.Positionable-method-getLocalY\" rel=\"Ext.util.Positionable-method-getLocalY\" class=\"docClass\">getLocalY</a>, but\nmay not be symmetrical when used on a relatively positioned element.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>y</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>The y coordinate. A value of <code>null</code> sets the top style to 'auto'.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setRegion' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-setRegion' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-setRegion' class='name expandable'>setRegion</a>( <span class='pre'>region, [animate]</span> ) : <a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Sets the element's position and size to the specified region. ...</div><div class='long'><p>Sets the element's position and size to the specified region. If animation is true\nthen width, height, x and y will be animated concurrently.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>region</span> : <a href=\"#!/api/Ext.util.Region\" rel=\"Ext.util.Region\" class=\"docClass\">Ext.util.Region</a><div class='sub-desc'><p>The region to fill</p>\n</div></li><li><span class='pre'>animate</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>true for the default animation or a standard\nElement animation config object</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setX' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-setX' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-setX' class='name expandable'>setX</a>( <span class='pre'>The, [animate]</span> ) : <a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Sets the X position of the DOM element based on page coordinates. ...</div><div class='long'><p>Sets the X position of the DOM element based on page coordinates.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>The</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>X position</p>\n</div></li><li><span class='pre'>animate</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>True for the default animation, or a standard\nElement animation config object</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setXY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-setXY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-setXY' class='name expandable'>setXY</a>( <span class='pre'>pos, [animate]</span> ) : <a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Sets the position of the DOM element in page coordinates. ...</div><div class='long'><p>Sets the position of the DOM element in page coordinates.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>pos</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>[]<div class='sub-desc'><p>Contains X &amp; Y [x, y] values for new position (coordinates\nare page-based)</p>\n</div></li><li><span class='pre'>animate</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>True for the default animation, or a standard\nElement animation config object</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-setY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-setY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-setY' class='name expandable'>setY</a>( <span class='pre'>The, [animate]</span> ) : <a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a><strong class='chainable signature' >chainable</strong></div><div class='description'><div class='short'>Sets the Y position of the DOM element based on page coordinates. ...</div><div class='long'><p>Sets the Y position of the DOM element based on page coordinates.</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>The</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a><div class='sub-desc'><p>Y position</p>\n</div></li><li><span class='pre'>animate</span> : <a href=\"#!/api/Boolean\" rel=\"Boolean\" class=\"docClass\">Boolean</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a> (optional)<div class='sub-desc'><p>True for the default animation, or a standard\nElement animation config object</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.util.Positionable\" rel=\"Ext.util.Positionable\" class=\"docClass\">Ext.util.Positionable</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='method-statics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-method-statics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-method-statics' class='name expandable'>statics</a>( <span class='pre'></span> ) : <a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a><strong class='protected signature' >protected</strong></div><div class='description'><div class='short'>Get the reference to the class from which this object was instantiated. ...</div><div class='long'><p>Get the reference to the class from which this object was instantiated. Note that unlike <a href=\"#!/api/Ext.Base-property-self\" rel=\"Ext.Base-property-self\" class=\"docClass\">self</a>,\n<code>this.statics()</code> is scope-independent and it always returns the class from which it was called, regardless of what\n<code>this</code> points to during run-time</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    statics: {\n        totalCreated: 0,\n        speciesName: 'Cat' // My.Cat.speciesName = 'Cat'\n    },\n\n    constructor: function() {\n        var statics = this.statics();\n\n        alert(statics.speciesName);     // always equals to 'Cat' no matter what 'this' refers to\n                                        // equivalent to: My.Cat.speciesName\n\n        alert(this.self.speciesName);   // dependent on 'this'\n\n        statics.totalCreated++;\n    },\n\n    clone: function() {\n        var cloned = new this.self;                      // dependent on 'this'\n\n        cloned.groupName = this.statics().speciesName;   // equivalent to: My.Cat.speciesName\n\n        return cloned;\n    }\n});\n\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.SnowLeopard', {\n    extend: 'My.Cat',\n\n    statics: {\n        speciesName: 'Snow Leopard'     // My.SnowLeopard.speciesName = 'Snow Leopard'\n    },\n\n    constructor: function() {\n        this.callParent();\n    }\n});\n\nvar cat = new My.Cat();                 // alerts 'Cat', then alerts 'Cat'\n\nvar snowLeopard = new My.SnowLeopard(); // alerts 'Cat', then alerts 'Snow Leopard'\n\nvar clone = snowLeopard.clone();\nalert(<a href=\"#!/api/Ext-method-getClassName\" rel=\"Ext-method-getClassName\" class=\"docClass\">Ext.getClassName</a>(clone));         // alerts 'My.SnowLeopard'\nalert(clone.groupName);                 // alerts 'Cat'\n\nalert(My.Cat.totalCreated);             // alerts 3\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Class\" rel=\"Ext.Class\" class=\"docClass\">Ext.Class</a></span><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='method-translatePoints' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-translatePoints' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-translatePoints' class='name expandable'>translatePoints</a>( <span class='pre'>x, [y]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></div><div class='description'><div class='short'>Translates the passed page coordinates into left/top css values for the element ...</div><div class='long'><p>Translates the passed page coordinates into left/top css values for the element</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>x</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The page x or an array containing [x, y]</p>\n</div></li><li><span class='pre'>y</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>The page y, required if x is not an array</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>An object with left and top properties. e.g.\n{left: (value), top: (value)}</p>\n</div></li></ul></div></div></div><div id='method-translateXY' class='member  not-inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><span class='defined-in' rel='Ext.util.Positionable'>Ext.util.Positionable</span><br/><a href='source/Positionable.html#Ext-util-Positionable-method-translateXY' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.util.Positionable-method-translateXY' class='name expandable'>translateXY</a>( <span class='pre'>x, [y]</span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='private signature' >private</strong></div><div class='description'><div class='short'>Translates the passed page coordinates into x and y css values for the element ...</div><div class='long'><p>Translates the passed page coordinates into x and y css values for the element</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>x</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a>/<a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a><div class='sub-desc'><p>The page x or an array containing [x, y]</p>\n</div></li><li><span class='pre'>y</span> : <a href=\"#!/api/Number\" rel=\"Number\" class=\"docClass\">Number</a> (optional)<div class='sub-desc'><p>The page y, required if x is not an array</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>An object with x and y properties. e.g.\n{x: (value), y: (value)}</p>\n</div></li></ul></div></div></div></div><div class='subsection'><div class='definedBy'>Defined By</div><h4 class='members-subtitle'>Static Methods</h3><div id='static-method-addConfig' class='member first-child inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addConfig' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addConfig' class='name expandable'>addConfig</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addInheritableStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addInheritableStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addInheritableStatics' class='name expandable'>addInheritableStatics</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMember' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMember' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMember' class='name expandable'>addMember</a>( <span class='pre'>name, member</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>member</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addMembers' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addMembers' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addMembers' class='name expandable'>addMembers</a>( <span class='pre'>members</span> )<strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add methods / properties to the prototype of this class. ...</div><div class='long'><p>Add methods / properties to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.awesome.Cat', {\n    constructor: function() {\n        ...\n    }\n});\n\n My.awesome.Cat.addMembers({\n     meow: function() {\n        alert('Meowww...');\n     }\n });\n\n var kitty = new My.awesome.Cat;\n kitty.meow();\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-addStatics' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addStatics' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addStatics' class='name expandable'>addStatics</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Add / override static properties of this class. ...</div><div class='long'><p>Add / override static properties of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.addStatics({\n    someProperty: 'someValue',      // My.cool.Class.someProperty = 'someValue'\n    method1: function() { ... },    // My.cool.Class.method1 = function() { ... };\n    method2: function() { ... }     // My.cool.Class.method2 = function() { ... };\n});\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-addXtype' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-addXtype' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-addXtype' class='name expandable'>addXtype</a>( <span class='pre'>xtype</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>xtype</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-borrow' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-borrow' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-borrow' class='name expandable'>borrow</a>( <span class='pre'>fromClass, members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Borrow another class' members to the prototype of this class. ...</div><div class='long'><p>Borrow another class' members to the prototype of this class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Bank', {\n    money: '$$$',\n    printMoney: function() {\n        alert('$$$$$$$');\n    }\n});\n\n<a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('Thief', {\n    ...\n});\n\nThief.borrow(Bank, ['money', 'printMoney']);\n\nvar steve = new Thief();\n\nalert(steve.money); // alerts '$$$'\nsteve.printMoney(); // alerts '$$$$$$$'\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fromClass</span> : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><div class='sub-desc'><p>The class to borrow members from</p>\n</div></li><li><span class='pre'>members</span> : <a href=\"#!/api/Array\" rel=\"Array\" class=\"docClass\">Array</a>/<a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><div class='sub-desc'><p>The names of the members to borrow</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this</p>\n</div></li></ul></div></div></div><div id='static-method-create' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-create' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-create' class='name expandable'>create</a>( <span class='pre'></span> ) : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create a new instance of this Class. ...</div><div class='long'><p>Create a new instance of this Class.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    ...\n});\n\nMy.cool.Class.create({\n    someConfig: true\n});\n</code></pre>\n\n<p>All parameters are passed to the constructor of the class.</p>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a></span><div class='sub-desc'><p>the created instance.</p>\n</div></li></ul></div></div></div><div id='static-method-createAlias' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-createAlias' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-createAlias' class='name expandable'>createAlias</a>( <span class='pre'>alias, origin</span> )<strong class='static signature' >static</strong></div><div class='description'><div class='short'>Create aliases for existing prototype methods. ...</div><div class='long'><p>Create aliases for existing prototype methods. Example:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    method1: function() { ... },\n    method2: function() { ... }\n});\n\nvar test = new My.cool.Class();\n\nMy.cool.Class.createAlias({\n    method3: 'method1',\n    method4: 'method2'\n});\n\ntest.method3(); // test.method1()\n\nMy.cool.Class.createAlias('method5', 'method3');\n\ntest.method5(); // test.method3() -&gt; test.method1()\n</code></pre>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>alias</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The new method name, or an object to set multiple aliases. See\n<a href=\"#!/api/Ext.Function-method-flexSetter\" rel=\"Ext.Function-method-flexSetter\" class=\"docClass\">flexSetter</a></p>\n</div></li><li><span class='pre'>origin</span> : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a>/<a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The original method name</p>\n</div></li></ul></div></div></div><div id='static-method-extend' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-extend' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-extend' class='name expandable'>extend</a>( <span class='pre'>config</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>config</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-getName' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-getName' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-getName' class='name expandable'>getName</a>( <span class='pre'></span> ) : <a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Get the current class' name in string format. ...</div><div class='long'><p>Get the current class' name in string format.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.cool.Class', {\n    constructor: function() {\n        alert(this.self.getName()); // alerts 'My.cool.Class'\n    }\n});\n\nMy.cool.Class.getName(); // 'My.cool.Class'\n</code></pre>\n<h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/String\" rel=\"String\" class=\"docClass\">String</a></span><div class='sub-desc'><p>className</p>\n</div></li></ul></div></div></div><div id='static-method-implement' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-implement' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-implement' class='name expandable'>implement</a>( <span class='pre'></span> )<strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Adds members to class. ...</div><div class='long'><p>Adds members to class.</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1</p>\n        <p>Use <a href=\"#!/api/Ext.Base-static-method-addMembers\" rel=\"Ext.Base-static-method-addMembers\" class=\"docClass\">addMembers</a> instead.</p>\n\n        </div>\n</div></div></div><div id='static-method-mixin' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-mixin' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-mixin' class='name expandable'>mixin</a>( <span class='pre'>name, mixinClass</span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Used internally by the mixins pre-processor ...</div><div class='long'><p>Used internally by the mixins pre-processor</p>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>name</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>mixinClass</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-onExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-onExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-onExtended' class='name expandable'>onExtended</a>( <span class='pre'>fn, scope</span> )<strong class='chainable signature' >chainable</strong><strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>fn</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li><li><span class='pre'>scope</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'>\n</div></li></ul></div></div></div><div id='static-method-override' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-override' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-override' class='name expandable'>override</a>( <span class='pre'>members</span> ) : <a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a><strong class='chainable signature' >chainable</strong><strong class='deprecated signature' >deprecated</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'>Override members of this class. ...</div><div class='long'><p>Override members of this class. Overridden methods can be invoked via\n<a href=\"#!/api/Ext.Base-method-callParent\" rel=\"Ext.Base-method-callParent\" class=\"docClass\">callParent</a>.</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.Cat', {\n    constructor: function() {\n        alert(\"I'm a cat!\");\n    }\n});\n\nMy.Cat.override({\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n\nvar kitty = new My.Cat(); // alerts \"I'm going to be a cat!\"\n                          // alerts \"I'm a cat!\"\n                          // alerts \"Meeeeoooowwww\"\n</code></pre>\n\n<p>As of 4.1, direct use of this method is deprecated. Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>\ninstead:</p>\n\n<pre><code><a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a>('My.CatOverride', {\n    override: 'My.Cat',\n    constructor: function() {\n        alert(\"I'm going to be a cat!\");\n\n        this.callParent(arguments);\n\n        alert(\"Meeeeoooowwww\");\n    }\n});\n</code></pre>\n\n<p>The above accomplishes the same result but can be managed by the <a href=\"#!/api/Ext.Loader\" rel=\"Ext.Loader\" class=\"docClass\">Ext.Loader</a>\nwhich can properly order the override and its target class and the build process\ncan determine whether the override is needed based on the required state of the\ntarget class (My.Cat).</p>\n        <div class='signature-box deprecated'>\n        <p>This method has been <strong>deprecated</strong> since 4.1.0</p>\n        <p>Use <a href=\"#!/api/Ext-method-define\" rel=\"Ext-method-define\" class=\"docClass\">Ext.define</a> instead</p>\n\n        </div>\n<h3 class=\"pa\">Parameters</h3><ul><li><span class='pre'>members</span> : <a href=\"#!/api/Object\" rel=\"Object\" class=\"docClass\">Object</a><div class='sub-desc'><p>The properties to add to this class. This should be\nspecified as an object literal containing one or more properties.</p>\n</div></li></ul><h3 class='pa'>Returns</h3><ul><li><span class='pre'><a href=\"#!/api/Ext.Base\" rel=\"Ext.Base\" class=\"docClass\">Ext.Base</a></span><div class='sub-desc'><p>this class</p>\n</div></li></ul></div></div></div><div id='static-method-triggerExtended' class='member  inherited'><a href='#' class='side expandable'><span>&nbsp;</span></a><div class='title'><div class='meta'><a href='#!/api/Ext.Base' rel='Ext.Base' class='defined-in docClass'>Ext.Base</a><br/><a href='source/Base.html#Ext-Base-static-method-triggerExtended' target='_blank' class='view-source'>view source</a></div><a href='#!/api/Ext.Base-static-method-triggerExtended' class='name expandable'>triggerExtended</a>( <span class='pre'></span> )<strong class='private signature' >private</strong><strong class='static signature' >static</strong></div><div class='description'><div class='short'> ...</div><div class='long'>\n</div></div></div></div></div></div></div>","superclasses":["Ext.Base"],"meta":{},"code_type":"ext_define","requires":[],"html_meta":{},"statics":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"$onExtended","id":"static-property-S-onExtended"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"addConfig","id":"static-method-addConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addInheritableStatics","id":"static-method-addInheritableStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addMember","id":"static-method-addMember"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addMembers","id":"static-method-addMembers"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true},"name":"addStatics","id":"static-method-addStatics"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"addXtype","id":"static-method-addXtype"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"borrow","id":"static-method-borrow"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"create","id":"static-method-create"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"createAlias","id":"static-method-createAlias"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"extend","id":"static-method-extend"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true},"name":"getName","id":"static-method-getName"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"deprecated":{"text":"Use {@link #addMembers} instead.","version":"4.1"}},"name":"implement","id":"static-method-implement"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"mixin","id":"static-method-mixin"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"private":true},"name":"onExtended","id":"static-method-onExtended"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"chainable":true,"markdown":true,"deprecated":{"text":"Use {@link Ext#define Ext.define} instead","version":"4.1.0"}},"name":"override","id":"static-method-override"},{"tagname":"method","owner":"Ext.Base","meta":{"static":true,"private":true},"name":"triggerExtended","id":"static-method-triggerExtended"}],"event":[],"css_mixin":[]},"files":[{"href":"Positionable.html#Ext-util-Positionable","filename":"Positionable.js"}],"linenr":2,"members":{"property":[{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"$className","id":"property-S-className"},{"tagname":"property","owner":"Ext.util.Positionable","meta":{"private":true},"name":"_alignRe","id":"property-_alignRe"},{"tagname":"property","owner":"Ext.util.Positionable","meta":{"private":true},"name":"_positionTopLeft","id":"property-_positionTopLeft"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"configMap","id":"property-configMap"},{"tagname":"property","owner":"Ext.util.Positionable","meta":{"private":true},"name":"convertPositionSpec","id":"property-convertPositionSpec"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigList","id":"property-initConfigList"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"initConfigMap","id":"property-initConfigMap"},{"tagname":"property","owner":"Ext.Base","meta":{"private":true},"name":"isInstance","id":"property-isInstance"},{"tagname":"property","owner":"Ext.Base","meta":{"protected":true},"name":"self","id":"property-self"}],"cfg":[],"css_var":[],"method":[{"tagname":"method","owner":"Ext.util.Positionable","meta":{"private":true},"name":"adjustForConstraints","id":"method-adjustForConstraints"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{"private":true},"name":"afterSetPosition","id":"method-afterSetPosition"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{"chainable":true},"name":"alignTo","id":"method-alignTo"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{"chainable":true},"name":"anchorTo","id":"method-anchorTo"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{"private":true},"name":"calculateAnchorXY","id":"method-calculateAnchorXY"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{},"name":"calculateConstrainedPosition","id":"method-calculateConstrainedPosition"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true,"deprecated":{"text":"as of 4.1. Use {@link #callParent} instead."}},"name":"callOverridden","id":"method-callOverridden"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callParent","id":"method-callParent"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"callSuper","id":"method-callSuper"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"configClass","id":"method-configClass"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"destroy","id":"method-destroy"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{},"name":"getAlignToXY","id":"method-getAlignToXY"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{"private":true},"name":"getAnchor","id":"method-getAnchor"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{"private":true},"name":"getAnchorToXY","id":"method-getAnchorToXY"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{},"name":"getAnchorXY","id":"method-getAnchorXY"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{"private":true},"name":"getBorderPadding","id":"method-getBorderPadding"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{},"name":"getBox","id":"method-getBox"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"getConfig","id":"method-getConfig"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{},"name":"getConstrainVector","id":"method-getConstrainVector"},{"tagname":"method","owner":"Ext.Base","meta":{},"name":"getInitialConfig","id":"method-getInitialConfig"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{},"name":"getLocalX","id":"method-getLocalX"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{},"name":"getLocalXY","id":"method-getLocalXY"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{},"name":"getLocalY","id":"method-getLocalY"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{},"name":"getOffsetsTo","id":"method-getOffsetsTo"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{},"name":"getRegion","id":"method-getRegion"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{},"name":"getViewRegion","id":"method-getViewRegion"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{},"name":"getX","id":"method-getX"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{},"name":"getXY","id":"method-getXY"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{},"name":"getY","id":"method-getY"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"hasConfig","id":"method-hasConfig"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"protected":true},"name":"initConfig","id":"method-initConfig"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{},"name":"move","id":"method-move"},{"tagname":"method","owner":"Ext.Base","meta":{"private":true},"name":"onConfigUpdate","id":"method-onConfigUpdate"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{"chainable":true},"name":"removeAnchor","id":"method-removeAnchor"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{"chainable":true},"name":"setBox","id":"method-setBox"},{"tagname":"method","owner":"Ext.Base","meta":{"chainable":true,"private":true},"name":"setConfig","id":"method-setConfig"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{"chainable":true},"name":"setLocalX","id":"method-setLocalX"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{"chainable":true},"name":"setLocalXY","id":"method-setLocalXY"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{"chainable":true},"name":"setLocalY","id":"method-setLocalY"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{"chainable":true},"name":"setRegion","id":"method-setRegion"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{"chainable":true},"name":"setX","id":"method-setX"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{"chainable":true},"name":"setXY","id":"method-setXY"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{"chainable":true},"name":"setY","id":"method-setY"},{"tagname":"method","owner":"Ext.Base","meta":{"protected":true},"name":"statics","id":"method-statics"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{},"name":"translatePoints","id":"method-translatePoints"},{"tagname":"method","owner":"Ext.util.Positionable","meta":{"private":true},"name":"translateXY","id":"method-translateXY"}],"event":[],"css_mixin":[]},"inheritable":null,"private":null,"component":false,"name":"Ext.util.Positionable","singleton":false,"override":null,"inheritdoc":null,"id":"class-Ext.util.Positionable","mixins":[],"mixedInto":["Ext.AbstractComponent"]});