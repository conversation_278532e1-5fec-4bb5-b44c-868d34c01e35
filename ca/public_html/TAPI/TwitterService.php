<?php
/**
 * @file
 *
 * This implements the twitter interface.
 *
 * Since the OAuthService baseclass do not handle the full OAuth protocol,
 * and therefore requires a valid access token (user_token in $config)
 * we do not define any of the apiDefs for obtaining an access token, if one is not present.
 *
 */


require_once (dirname(__FILE__)."/OAuthService.php");


/**
* including OAuthJsonResponse so we can make the tweet response easy to access for the caller of tweet.
*/
require_once (dirname(__FILE__)."/OAuthJsonResponse.php");

/**
 * we have not defined any api calls that return xml so we dont include the easy handling of xml responses.
 * uncomment below if you need to use OAuthXmlResponse
 */
//require_once (dirname(__FILE__)."/OAuthXmlResponse.php");

/**
 * uncomment below if you only want to use OAuthResponse
 *
 * its required in the Json and Xml response classes so if you include one of them this is not needed.
 */
//require_once (dirname(__FILE__)."/OAuthResponse.php");


class TwitterService extends OAuthService {

	public $response="";
	public $lastError=0;

	public function __construct(array $config = null) {
		parent::__construct($config);
		$this->apiDefs['tweet'] = array(
			'url'=>'api.twitter.com/1/statuses/update.json',
			'method' => 'POST',
			'scheme' => 'https',
			'args' => array('status')
		);
/*		$this->apiDefs['accesstoken'] = array(
			'url'=>'api.twitter.com/1/oauth/access_token',
			'method' => 'POST',
			'scheme' => 'https'
		);
		$this->apiDefs['requesttoken'] = array(
			'url'=>'api.twitter.com/1/oauth/request_token',
			'method' => 'POST',
			'scheme' => 'https'
		);
		$this->apiDefs['authenticate'] = array(
			'url'=>'api.twitter.com/1/oauth/authenticate',
			'method' => 'GET',
			'scheme' => 'https'
		);
		$this->apiDefs['authorize'] = array(
			'url'=>'api.twitter.com/1/oauth/authorize',
			'method' => 'GET',
			'scheme' => 'https'
		);
*/
	}

	/**
	* implementing the abstract method from OAuthService
	* we will use HMAC_SHA1 encryption
	*/
	public function getSignatureMethod() {
		return "HMAC_SHA1";
	}
	/**
	* easy method for tweeting via the 'tweet' api definition.
	*/
	public function tweet( $message ) {
		try {
			$res = $this->callService( 'tweet', array( 'status' => $message) );
		} catch (OAuthException $e) {
			$this->response = "Exception:".$e->getMessage();
			$this->lastError= -1;
			// we could log the error here...
			// for now we just return false
			return false;
		}
		$err = $res->getErrNo();
		if ( $err > 0) {
			$this->response = "curl_error: ".$res->getErrMsg();
			$this->lastError = $err;
			return false;
		}
		if ( $res['header']['http_code'] != "200" ) {
			$this->response = "http_error";
			$this->lastError = $res['header']['http_code'];
			return false;
		}
		$this->response = new OAuthJsonResponse($res);
		$this->lastError = 0;
		return true;
	}

}

?>
