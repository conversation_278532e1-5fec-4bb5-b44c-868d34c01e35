<?php
/**
 * @file
 * OAuth response handler that can be used for easy access to content that is xml
 */

require_once (dirname(__FILE__)."/OAuthResponse.php");

class OAuthXmlResponse extends OAuthResponse {

	protected function parseData() {
		$content = $this->getContent();
		$xml = simplexml_load_string($content);
		$json = json_encode($xml);
		return json_decode($json, true);
	}

	public function __get($name) {
		if ($this->getErrorNo() > 0 ) {
			return false;
		}
		$result = $this->parseData();
		foreach ($result as $key => $val) {
			$this->$key = $val;
		}
		return $result[$name];
	}


}

?>
