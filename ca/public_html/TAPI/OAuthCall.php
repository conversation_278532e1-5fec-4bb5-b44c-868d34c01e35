<?php
/**
 * @file
 *
 * This implements the actual OAuth protocol for access tokens
 *
 * It uses <PERSON>ur<PERSON> to do the actual calls to the remote service.
 */



class OAuthCall {


	/**
	* for storing the passed config from the constructor.
	* $config is expected to be an associative array
	*/
	protected $config;

	/**
	* default CURL settings
	*/
	protected $curl_defaults = array(
			CURLOPT_RETURNTRANSFER => TRUE, // return webpage
			CURLOPT_HEADER    => FALSE,
			CURLOPT_FOLLOWLOCATION => TRUE, // follow redirects
			CURLOPT_AUTOREFERER    => TRUE, // set referer on redirect
			CURLOPT_ENCODING  => "",
			CURLOPT_CONNECTTIMEOUT => 120, // timeout on connect
			CURLOPT_TIMEOUT    => 120, // timeout on response
			CURLOPT_MAXREDIRS => 10, // stop after 10 redirects
			CURLOPT_SSL_VERIFYHOST => 0, // don't verify ssl
			CURLOPT_SSL_VERIFYPEER => false, // don't verify peer ssl
			CURLOPT_VERBOSE    => 1
		);

	/**
	* curl options to use
	*/
	protected $curl_options;

	/**
	* used to store and manipulate oauth data before its send to the remote service
	*/
	protected $oauth;

	/**
	* ease of use access to the encoded & combined secrects.
	* setup in __construct
	*/
	protected $encoded_secrets;


	/**
	* @param $config
	* the raw config associative array as passed to the object.
	*/
	public function __construct(array $config = null) {
		if ( is_array($config) ) {
			$this->curl_options = array();
			foreach ($config as $key => $val) {
				if ( is_string($key) ) {
					$k = strtoupper($key);
					if ( substr( $k, 0, 8 )=="CURLOPT_" && defined($k) ) {
						$this->curl_options[constant($k)] = $val;
					}
				}
			}
			// The + operator returns the right-hand array appended to the left-hand array;
			// for keys that exist in both arrays, the elements from the left-hand array will be used,
			// and the matching elements from the right-hand array will be ignored.
			// (see : http://php.net/manual/en/language.operators.array.php )
			$this->curl_options += $this->curl_defaults;
			$this->config = $config;
		} else {
			$this->curl_options = $this->curl_defaults;
			$this->config = array();
		}
		// unprepared oauth data
		$this->oauth['oauth_consumer_key'] = $this->config['consumer_key'];
		$this->oauth['oauth_nonce'] = isset($this->config['nonce']) ? $this->config['nonce'] : $this->generateNonce();
		$this->oauth['oauth_signature_method'] = isset($this->config['signature_method']) ? $this->config['signature_method'] : 'HMAC-SHA1';
		$this->oauth['oauth_timestamp'] = isset($this->config['timestamp']) ? $this->config['timestamp'] : time();
		$this->oauth['oauth_token'] = $this->config['user_token'];
		$this->oauth['oauth_version'] = isset($this->config['version']) ? $this->config['version'] : '1.0';

		$this->encoded_secrets = $this->encode($this->config['consumer_secret'])."&".$this->encode($this->config['user_secret']);
	}

	/**
	* entry point for making the actual requests to an url
	*
	* @param $url
	*
	* @return associative array containing
	* - err     => curl error or 0
	* - errmsg  => error message or ""
	* - content => the response body from the curl call.
	* - header  =>
	*/
	public function httpRequest($url, $params = null, $method = 'GET', $scheme= 'http' ) {
		$url = "{$scheme}://{$url}";
		$this->makeOAuthSignature($method, $url, $params);
		$this->setHttpHeaders($url);
		switch ($method) {
			case 'POST':
				return $this->httpPost($url, $params);
			case 'GET':
				return $this->httpGet($url, $params);
		}
	}

	/**
	* encode
	*/
	private function makeOAuthSignature( $method, $url, $params ) {
		$params = is_array($params) ? $params : array();
		array_walk( $this->oauth, array($this,'encode') );
		array_walk( $params, array($this,'encode') );
		$encodedParams = array_merge( $this->oauth, $params );
		ksort($encodedParams);
		// encoded params is only used to create the signature.
		$this->oauth['oauth_signature'] = $this->encode( $this->generateSignature( $method, $url, $encodedParams) );
	}


	/**
	* handling post requests
	*/
	private function httpPost( $url, $params) {
		$this->curl_options[CURLOPT_URL] = $url;
		$this->curl_options[CURLOPT_POSTFIELDS] = http_build_query($params);
		$this->curl_options[CURLOPT_POST] = true;
		return $this->curl();
	}


	/**
	* handling get requests
	*/
	private function httpGet( $url, $params ) {
		if ( is_array( $params ) ) {
			$url .= "?".http_build_query($params);
			$this->curl_options[CURLOPT_URL] = $url;
			$url = substr($url,0,-1);
		}
		return $this->curl();
	}

	/**
	* set the http headers according to the OAuth protocol.
	*/
	private function setHttpHeaders( $url ) {
		$h = array('Except:');
		$urlparts = parse_url($url);
		$header='Authorization: OAuth';
		ksort($this->oauth);
		foreach($this->oauth as $name => $val) {
			$header .= " {$name}=\"{$val}\",";
		}
		// remove last ,
		// and push to headers array
		$header=substr( $header, 0, -1);
		$h[] = $header;
		$this->curl_options[CURLOPT_HTTPHEADER] = $h;

	}

	/*
	* generate the signature based on method, url, params
	* following the OAuth protocol.
	*/
	private function generateSignature($method, $url, $params) {
		$strParams = '';
		foreach ( $params as $k => $v) {
			$v = $this->encode($v);
			$strParams .= "{$k}={$v}&";
		}
		$strParams = $this->encode( substr($strParams, 0, -1 ) );

		$normalizedURL = $this->encode( $this->normalizeURL( $url ) );

		// just incase we ever use really wierd methods...
		// cant really imagine that happening but it dont hurt to do it
		// so might as well.
		$method = $this->encode($method);

		$signatureBaseString = "{$method}&{$normalizedURL}&{$strParams}";
		return $this->signString($signatureBaseString);
	}

	/*
	* normalize the url
	* following the OAuth protocol.
	*/
	private function normalizeURL( $url ) {
		$parts = parse_url( $url );
		$scheme = strtolower( $parts['scheme'] );
		$host = strtolower( $parts['host'] );
		$res = "{$scheme}://{$host}";
		if ( isset( $parts['port'] ) ) {
			$port = intval( $parts['port'] );
			if ( $port > 0 && ( $scheme == 'http' && $post !== 80 ) || ( $scheme == 'https' && $port !== 443 ) ) {
				$res .= ":{$port}";
			}
		}
		$res .= $parts['path'];
		if ( !empty($parts['query']) ) {
			$res .= "?{$parts['query']}";
		}
		return $res;
	}
	/*
	* sign the string using the consumer_secret and access_token_secret (access_token_secret is the user_secret in the config)
	*/
	private function signString( $str ) {
		switch ( $this->oauth['oauth_signature_method'] ) {
			// only implementing HMAC-SHA1 for now
			case "HMAC-SHA1":

				if ( function_exists("hash_hmac") ) {
					return base64_encode( hash_hmac( 'sha1', $str, $this->encoded_secrets, true ) );
				} else {
					return $this->hmac_sha1($str,$this->encoded_secrets);
				}
			break;
		}


	}


	// The nonce value MUST be unique across all requests with the
    // same timestamp, client credentials, and token combinations.
	private function generateNonce($length=12, $include_time=true) {
		$sequence = array_merge(range(0,9), range('A','Z'), range('a','z'));
		$length = $length > count($sequence) ? count($sequence) : $length;
		shuffle($sequence);
		$prefix = $include_time ? microtime() : '';
		$nonce = md5(substr($prefix . implode($sequence), 0, $length));
		return $nonce;
	}

	/**
	* encode using the rfc3986 method as defined in the OAuth protocol
	* note that starting in php 5.3.0 rawurlencode does this for utf8 encoded strings.
	*/
	protected function encode($input) {
		if ( is_array($input) ) {
			return array_map(array($this,'encode'), $input);
		} else if ( is_scalar($input) ) {
			if ( version_compare( PHP_VERSION, '5.3.0' ) >= 0 ) {
				return rawurlencode( utf8_encode($input) );
			} else {
				return $this->rfc3986_encode($input);
			}
		}
	}

	// encoding both keys and values using rfc3986 urlencode
	// and returning them as a string
	private function assoc_encode_combine(array $params) {
		$arr=array();
		foreach ($params as $key => $val) {
			$arr[$this->encode($key)] = $this->encode($val);
		}
		$res = "";
		foreach ($arr as $key => $val) {
			$res .= "&{$key}={$val}";
		}
		return $res;
	}
	// function to encode rfc3986
	private function rfc3986_encode($input) {
	    return str_replace('%7E', '~', rawurlencode($input));
	}

	/**
	* auxilary hmac_sha1 function
	* used when hash_hmac is not available (
	*/

	private function hmac_sha1($key, $data) {
		// Adjust key to exactly 64 bytes
		if (strlen($key) > 64) {
		    $key = str_pad(sha1($key, true), 64, chr(0));
		}
		if (strlen($key) < 64) {
		    $key = str_pad($key, 64, chr(0));
		}

		// Outter and Inner pad
		$opad = str_repeat(chr(0x5C), 64);
		$ipad = str_repeat(chr(0x36), 64);

		// Xor key with opad & ipad
		for ($i = 0; $i < strlen($key); $i++) {
		    $opad[$i] = $opad[$i] ^ $key[$i];
		    $ipad[$i] = $ipad[$i] ^ $key[$i];
		}

		return sha1($opad.sha1($ipad.$data, true));
	}

	/**
	* do the curl call
	* @return assoc array containing
	* - content => the body of the response
	* - err     => curl error or 0
	* - errmsg  => string for curl error
	* - header  => array of information from the curl call
	*/
	protected function curl() {
		$ch = curl_init();
		curl_setopt_array($ch, $this->curl_options);
		$result = array(
			'content' => curl_exec($ch),
			'err' => curl_errno($ch),
			'errmsg' => curl_error($ch),
			'header' => curl_getinfo($ch)
		);
		curl_close($ch);

		return $result;
	}
}
?>
