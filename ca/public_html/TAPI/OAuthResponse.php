<?php
/**
 * @file
 * simple OAuthResponse handler that is extended in OAuthJsonResponse and OAthXmlResponse.
 *
 * OAuthXmlResponse processes the content as XMl and
 * OAuthJsonResponse processes the content as json.
 */


class OAuthResponse {
	private $content;
	private $err;
	private $errmsg;
	private $header;

	public function __construct(array $res=null ) {
		$res = ($res===NULL) ? array() : $res;
		$this->content = isset($res['content']) ? $res['content']: "";
		$this->err = isset($res['err']) ? $res['err']: 0;
		$this->errmsg = isset($res['errmsg']) ? $res['errmsg']: "";
		$this->header = isset($res['header']) ? $res['header']: array();

	}

	public function getErrNo(){
		return $this->err;
	}
	public function getErrMsg(){
		return $this->errmsg;
	}

	public function getInfo($part=""){
		if ($part==="") {
			return $this->header;
		}
		return isset($this->header[$part]) ? $this->header[$part]: NULL;

	}
	public function getContent(){
		return $this->content;
	}
}

?>
