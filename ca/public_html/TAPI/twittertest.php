<?

require_once (dirname(__FILE__)."/TwitterService.php");

// possible config options
/*
	All CURLOPT can be overwritte by using the constant as a string.
	example: want to set CURLOPT_CRLF
	then add
	'CURLOPT_CRLF' => FALSE
	to the $config array.

	other config options
	'timestamp' => will become the oauth_timestamp that will be used

*/

$config = array(
	'consumer_key' => 'asgCE5BRpUOO5wQ2s18rug',
	'consumer_secret' => 'TnJNhYTktrWU9YQmbcHnjezdUtlyCH7cZnfqvLtKQg',
	'user_token'	=> '409148633-xa8epHTkVl4cej0OcDchYwIk5btANxUTmlX5dIXz',
	'user_secret' => 'OroFm4EcXSAmuJAZdQLwMF7gNM5Tei8Iqw5MlFXMEk'
);


$twitter = new TwitterService($config);

// tweeting "test message"
$msg=(isset($_GET['m']) ? $_GET['m']: time() );

if ( !$twitter->tweet($msg)) {
	echo "send error (".$twitter->lastError.")<hr>";
	print_r($twitter->response);

} else {
	echo "send successfull<hr>";
	print_r($twitter->response);
}
