<?php

	// updateTwitterStatus( $sContent, $responseFormat="xml" )
	// @param sContent the post data
	//
	// @param $responseFormat, if passed and not set to 'json' we request a 'xml' response.
	// if set to 'json' we request a json response.
	function updateTwitterStatus( $sContent , $responseFormat="xml"){
		$responseFormat = strtolower($responseFormat);
		if ($responseFormat != 'json') {
			$responseFormat = 'xml';
		} 
		$hCurl = curl_init();

		curl_setopt( $hCurl, CURLOPT_RETURNTRANSFER, 1 );
		curl_setopt( $hCurl, CURLOPT_URL, "http://api.twitter.com/1/statuses/update.".$format );
		curl_setopt( $hCurl, CURLOPT_USERPWD, TWITTER_USERNAME.":".TWITTER_PASSWORD );
		curl_setopt( $hCurl, CURLOPT_TIMEOUT, 3);
		curl_setopt( $hCurl, CURLOPT_POST, 1);
		curl_setopt( $hCurl, CURLOPT_POSTFIELDS, "status=".urlencode( $sContent ) );

		$sResult = curl_exec($hCurl);
		
		$sResultInfo = curl_getinfo( $ch );
		curl_close($hCurl);
		if ( intval( $sResultInfo[ 'http_code' ] ) == 200 ) {
			// status update succeeded
			// 
		} else {
			// status update failed
		}
		
	}
?>
