<?php
/**
 * @file
 *
 * This contains the abstract baseclass for building an OAuth Service
 * where you want to send on behalf of a, to the service known, consumer
 *
 * The idea is that you inherit this class and define your services
 * in the apiDef. (see example TwitterService.php)
 */

require_once (dirname(__FILE__)."/OAuthCall.php");
// we expect that all classes that inherit OAuthService will implement

// OAuthService can throw the following exceptions

class OAuthException extends Exception {}
class OAuthUndefinedService extends OAuthException {}
class OAuthMissingServiceParameter extends OAuthException {}
class OAuthBadServiceParameterType extends OAuthException {}
class OAuthBadServiceDefinition extends OAuthException {}
class OAuthMissingConfigEntries extends OAuthException {}

/**
 * base class for services
 */
abstract class OAuthService {

	/**
	* for storing the passed config from the constructor.
	* $config is expected to be an associative array
	*/
	protected $config;


	/**
 	 * an associative array that the inheriting class can add services to
	 * Each entry should have an unique name as the key and an associative array as value that contains
	 * 'url' => host and path to service
	 * 'method' => 'POST' | 'GET', optional defaults to GET
	 * 'scheme' => 'https' | 'http', optional defaults to http
	 * 'args' => list of required parameters in for the service call.
	 */
	protected $apiDefs;


	/**
	 * constructor
	 *
	 * @param array $config
	 * requited key value pairs in the config are
	 * 'consumer_key'     => a valid oauth_consumer_key for the service
	 * 'consumer_secret'  => the corresponding secret
	 * 'user_key'         => a valid oauth_token that gives the needed access to the service.
	 * 'user_secret'      => the corresponding secret
	 *
	 * it is also possible to pass CURL options via the config
	 * to do so use the name of the CURLOPT constant you want to modify and set the value
	 * example: setting CURLOPT_USERAGENT to "secunia spider 1A"
	 * add "CURLOPT_USERAGENT" => "secunia spider 1A"
	 * etc.
	 * (see  http://php.net/manual/en/function.curl-setopt.php for more CURLOPT flags)
	 *
	 * @throw OAuthMissingConfigEntries
	 * throws OAuthMissingConfigEntries with information on which required config settings are missing.
	 */
	public function __construct(array $config) {
		if (! is_array($this->apiDefs) ) {
			$this->apiDefs = array();
		}
		$this->config = $config;
	}

	/**
	 * must be implemented in the extending class
	 *
	 * @return
	 *   - "HMAC-SHA1" (only one implemented)
	 *   - "RSA-SHA1"
	 */
	abstract protected function getSignatureMethod();


	/**
	 * @param $name
	 * 	a defined service name.
	 *  see $apiDefs
	 *
	 * @param $params
	 *	associative array with named parameters to be passed to the service
	 *
	 * @return associative array
	 * array (
	 *	'content' => body of response as a string,
	 *	'err' => curl error or 0
	 *  'errmsg' => errormessage for the err
	 *	'header' => all curl header data except request_header. (see curl_getinfo, http://dk2.php.net/manual/en/function.curl-getinfo.php)
	 * )
	 *
	 * @throws OAuthMissingConfigEntries if there are required config keys that is not set.
	 * @throws OAuthUndefinedService if the param $name is not defined
	 * @throws OAuthBadServiceDefinition if there is no url connected to the service
	 * @throws OAuthMissingServiceParameter if param $params do not contain the service specified required parameters
	 */
	protected function callService($name,array $params=null) {
		$missing_required=array();
		if (! isset($this->config['consumer_key']) ) {
			$missing_required[] = 'consumer_key';
		}
		if (! isset($this->config['consumer_secret']) ) {
			$missing_required[] = 'consumer_secret';
		}
		// since the full protocol is not implemented we also require that we get a user_token and user_secret passed in the config.
		if (! isset($this->config['user_token']) ) {
			$missing_required[] = 'user_token';
		}
		if (! isset($this->config['user_secret']) ) {
			$missing_required[] = 'user_secret';
		}
		if ( count( $missing_required ) > 0 ) {
			throw new OAuthMissingConfigEntries( "missing config keys, '".implode("','", $missing_required )."'" );
		}

		if ( ! isset( $this->apiDefs[$name] ) ) {
			throw new OAuthUndefinedService($name);
		}
		$def = $this->apiDefs[$name];
		if ( ! isset( $def['url'] ) ) {
			throw new OAuthBadServiceDefinition('no service url defined');
		}
		$url = $def['url'];
		$method = isset($def['method']) ? strtoupper($def['method']) : 'GET';
		$scheme = isset($def['scheme']) ? strtolower($def['scheme']) : 'http';
		foreach ($def['args'] as $arg) {
			if (! isset($params[$arg]) ) {
				throw new OAuthMissingServiceParameter($arg);
			}
		}
		// setup an OAuthCall
		$call= new OAuthCall($this->config, $this->getSignatureMethod());
		return $call->httpRequest( $url, $params, $method, $scheme );

	}
}



?>
