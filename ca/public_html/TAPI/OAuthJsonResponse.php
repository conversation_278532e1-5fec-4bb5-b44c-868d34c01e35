<?php
/**
 * @file
 * OAuth response handler that can be used for easy access to content that is json
 */

require_once (dirname(__FILE__)."/OAuthResponse.php");
class OAuthJsonResponse extends OAuthResponse {

	protected function parseData() {
		$content = $this->getContent();
		return json_decode($content, true);
	}
	public function __get($name) {
		if ($this->getErrorNo() > 0 ) {
			return false;
		}
		$result = $this->parseData();
		foreach ($result as $key => $val) {
			$this->$key = $val;
		}
		return $result[$name];
	}


}

?>
