<?php
ini_set('memory_limit', '-1');
ini_set('display_startup_errors',1);
ini_set('display_errors',1);
error_reporting(E_ALL);

require_once( './sfwex.class.php' );
require_once( './configuration.php' );
require_once( './db.class.php' );
require_once( './query.class.php' );

define('SCHEMA_NAME', 'software_suggestions_vt');
define('SCHEMA_NAME_VT', 'vuln_track');
define('SUGGESTION_BASETABLE', 'software_suggestions_raw');
define('SUGGESTION_DEDUP', 'software_suggestions');

$GLOBALS['db'] = new DB( DB_HOST, SCHEMA_NAME, DB_USER, DB_PASS );
//$GLOBALS['db_vt'] = new DB( DB_HOST_VT, SCHEMA_NAME_VT, DB_USER_VT, DB_PASS_VT );


//Create a variable for start time

$time_start = microtime(true);


/* Fetch records from RAW table for  Dedup*/

$queryCount = "SELECT count(*) as totalRecords FROM ".SCHEMA_NAME.".".SUGGESTION_BASETABLE." WHERE platform IN (2,3)";
$rowsCount = $GLOBALS['db']->execRaw( $queryCount)->fetchAll();

$recordsPerIteration = 10000 ;
$totalCount = $rowsCount[0]['totalRecords'];

$start = 0;
print "\n total records need to insert/update for dedup ".$totalCount."\n";
$fileCount = 0 ;
$fileName = "VT_INSERT_SQLStatement_".$fileCount.".sql" ;
$myfile = fopen($fileName, "w") or die("Unable to open file!");
if ( $totalCount > 0 ) {
	 
	while ( $totalCount > 0 ) {
		
		if (file_exists ($fileName) ) {
			clearstatcache();
			$getFileSize = filesize ($fileName)  ;	
			$sizeInMB = round($getFileSize / 1024 /1024 , 1) ;
			print "\n file name is ".$fileName." and file size is ".$sizeInMB." MB\n" ;
			if ($sizeInMB > 100) {
				fclose($myfile) ;
				unset($myfile) ;
				$myfile = null ;
				$fileCount = $fileCount + 1 ;
				$fileName = "VT_INSERT_SQLStatement_".$fileCount.".sql" ;
				$myfile = fopen($fileName, "w") or die("Unable to open file!");
			} 
		}		
		$GLOBALS['db'] = new DB( DB_HOST, SCHEMA_NAME, DB_USER, DB_PASS );
		
		$query = "SELECT *  FROM ".SCHEMA_NAME.".".SUGGESTION_BASETABLE."
					WHERE platform IN (2,3) 
					ORDER BY ".SUGGESTION_BASETABLE.".created_date ASC
					LIMIT ".$start.",".$recordsPerIteration;
		
		$getAllRows = $GLOBALS['db']->execRaw( $query )->fetchAll();
		$GLOBALS['db']->queryString = null;
		
		$insertRow  = " INSERT INTO ".SCHEMA_NAME_VT.".".SUGGESTION_DEDUP." (
							FileName,
							CompanyName,
							ProductName,
							FileDescription,
							LegalCopyright,
							ProductVersion,
							FileVersion,
							Path,
							platform,
							match_value,
							InternalName,
							PETimestamp,
							AltPETimestamp,
							PEMachine,
							AltPEMachine,
							AGVersion,
							Version,
							sr_match_file_id,
							AppName,
							Status,
							cron_modified_date)  VALUE  " ;
		
		//Building INSERT SQL for all fetched records
		foreach ($getAllRows as  &$getRow) {
			
			if (empty ($getRow['sr_match_file_id'])) $getRow['sr_match_file_id'] = 0 ;
			
			$insertRow.= 		"(".$GLOBALS['db']->quote($getRow['FileName']).",
								".$GLOBALS['db']->quote($getRow['CompanyName']).",
								".$GLOBALS['db']->quote($getRow['ProductName']).",
								".$GLOBALS['db']->quote($getRow['FileDescription']).",
								".$GLOBALS['db']->quote($getRow['LegalCopyright']).",
								".$GLOBALS['db']->quote($getRow['ProductVersion']).",
								".$GLOBALS['db']->quote($getRow['FileVersion']).",
								".$GLOBALS['db']->quote($getRow['Path']).",
								".$GLOBALS['db']->quote($getRow['platform']).",
								".$GLOBALS['db']->quote($getRow['match_value']).",
								".$GLOBALS['db']->quote($getRow['InternalName']).",
								".$GLOBALS['db']->quote($getRow['PETimestamp']).",
								".$GLOBALS['db']->quote($getRow['AltPETimestamp']).",
								".$GLOBALS['db']->quote($getRow['PEMachine']).",
								".$GLOBALS['db']->quote($getRow['AltPEMachine']).",
								".$GLOBALS['db']->quote($getRow['AGVersion']).",
								".$GLOBALS['db']->quote($getRow['Version']).",
								".$GLOBALS['db']->quote($getRow['sr_match_file_id']).",
								".$GLOBALS['db']->quote($getRow['AppName']).",
								".$getRow['Status'].",
								".$GLOBALS['db']->quote($getRow['created_date'])."),";
		}
		
		$insertRow=rtrim($insertRow,','); // Remove last ','
		$insertRow.= " ON DUPLICATE KEY UPDATE ".SUGGESTION_DEDUP.".count = (".SUGGESTION_DEDUP.".count + 1) , cron_modified_date =  '".$getRow['created_date']."'; \n" ;
		$getRow = null ;
		fwrite($myfile, $insertRow);
		//fclose($myfile);
		//unset($myfile) ;
		//$myfile = null ; 
		//Wrinting 10K records in file at one GO
		
		//Flushing all data to freeup RAM
		gc_enable();
		gc_collect_cycles() ;
		$GLOBALS['db'] = null ;
		$insertRow = null;		

		$totalCount	= $totalCount- $recordsPerIteration;
		$start 		+= $recordsPerIteration ;
		$getAllRows = array();
		$getAllRows = null;

		
		print " \n Records needs to process ".$totalCount."\n";
	}
	
} else {
	print "\n Nothing to process \n";
}

$time_end = microtime(true);
$time = $time_end - $time_start;

echo "\n Total Execution time : ".($time/60)." Minutes \n";
?>
