<?php

  /**
   * @file db.class.php
   * Database Abstraction Layer using prepared statements.
   *
   */

class DB {

	/*
	 * Instances of the DB class i.e. common, private etc
	 */
	private static $instances = array();

	/*
	 * An array of all the connections i.e. pdo objects
	 * Format:  $connections[ $dsn ] [ $username ] = $connection
	 */
	private static $connections = array();

	/*
	 * A PDO object fetched on successful connection
	 */
	private $connection = null;

	/*
	 * Cache of pdoStatements for this connection
	 */
	private $queryCache = array();

	/*
	 * Flags
	 */
	private static $connectionCachingEnabled = true;
	// private static $queryCachingEnabled = false; // @todo: use this flag where queries are being cached

	/**
	 * DB constructor.
	 *
	 * @param String $host
	 * @param String $dbname
	 * @param String $username
	 * @param String $password
	 */
	function __construct( $host = "", $dbname = "", $username = "", $password = "", $utc = false ) {
		if ( !$host || !$dbname || !$username ) {
			throw new DbEx( "Invalid Arguments: Host = " . $host . ", Database Name: " . $dbname . ", Username: " . $username, DbEx::INVALID_ARGS );
		}

		$dsn = $this->generateDsn( $host, $dbname );

		if ( self::$connectionCachingEnabled && self::connectionExists( $dsn, $username ) ) {
			$this->connection = self::$connections[$dsn][$username];
		} else {
			try {
				$connection = new PDO( $dsn, $username, $password );
				$connection->setAttribute( PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION );

				if ( $utc ) {
					$this->setUtc( $connection );
				}

				$this->connection = $connection;

				self::$connections[$dsn][$username] = self::$connectionCachingEnabled ? $this->connection : NULL;

			} catch ( PDOException $ex ) {
				//throw new DbEx( "Initialize Failed: DSN = " . $dsn, DbEx::INIT_FAILED, $ex );
			}
		}
	}

	public static function setConnectionCachingEnabled( $value ) {
		self::$connectionCachingEnabled = $value ? true : false;
	}

	/**
	 * Close the connection
	 *
	 * @param String $dsn
	 * @param String $username
	 *
	 * @return boolean
	 *   TRUE on success, else FALSE
	 */
	public static function close( $dsn = "", $username = "" ) {
		if ( !$dsn || !$username ) {
			return false;
		}

		if ( self::connectionExists( $dsn, $username ) ) {
			// @todo : close
			unset( self::$connections[$dsn][$username] );
			return true;
		}

		return false;
	}


	/**
	 * Determine if the connection exists or not.
	 *
	 * @param String $dsn
	 * @param String $username
	 *
	 * @return boolean
	 *   TRUE if connection exists, else FALSE
	 */
	private static function connectionExists( $dsn, $username ) {
		if ( isset( self::$connections[$dsn][$username] ) ) {
			if ( self::$connections[$dsn][$username] instanceof PDO ) {
				/*
				 * @todo: if the connection isn't used for a long time, the mysql server
				 * close the connection. Therefore, we should verify that the connection
				 * exists before returning true.
				 */
				return true;
			}
		}
		return false;
	}

	/**
	 * Applies PDO::quote to all elements of $array recursively. This will
	 * result in all elements of $array being enclosed in single quotes with
	 * any NULL values being converted to empty '' strings. If your SQL code
	 * already has quotes enclosing the values then you may have to remove them.
	 *
	 * @note: use named parameters instead of this. This is only for
	 * queries where named parameters are not viable.
	 *
	 * @param array &$array
	 *   $array is passed by reference, this method doesn't return a value
	 */
	public function escapeArray( &$array ) {
		array_walk_recursive( $array, create_function( '&$v, $k, $connection', '$v = $connection->quote($v);' ), $this->connection );
	}

	/*
	 * PDO::quote function
	 * This is just a wrapper as $this->connection is private.
	 *
	 * Should only be used when using named params is not usable at all.
	 *
	 * @todo: Check performance since this is just a wrapper
	 */
	public function quote( $value ) {
		return $this->connection->quote( $value );
	}

	public function escapeLike( $value ) {
		return Query::escapeLike( $value );
	}

	/*
	 * PDO::lastInsertId function
	 * This is just a wrapper as $this->connection is private.
	 *
	 * @todo: Check performance since this is just a wrapper
	 */
	public function lastInsertId() {
		return $this->connection->lastInsertId();
	}

	/**
	 * Setting the utc time for a connection
	 */
	private function setUtc( PDO $connection ) {
		$query = "SET time_zone = '+0:00'";
		$connection->query( $query );
	}

	/**
	 * Generate the Data Source Name (DSN).
	 *
	 * @param String $host
	 * @param String $dbname
	 *
	 * @return String
	 */
	private function generateDsn( $host, $dbname = "" ) {

		// Sanitize the host and dbname
		if ( !Query::isSafe( $host, array( Query::INC_DASH ) ) || !Query::isSafe( $dbname ) ) {
			return false;
		}

		$dsn = "mysql:host=" . $host . ";";
		if ( $dbname ) {
			$dsn .= "dbname=" . $dbname . ";";
		}
		return $dsn;
	}


	/*
	 * Exec functions
	 * i.e. they execute the query objects and return the results/status etc
	 */


	/**
	 * @param Select $select
	 *   An object that represents the select statement.
	 *
	 * @return bool|array
	 *   Failure: FALSE
	 *   Success: Associative array of selected rows
	 */
	private function execSelect( Select $select ) {

		$select->build();

		$pdoStatement = $this->execRaw( $select->getQuery(), $select->getValues() );
		if ( $select->getOption( Select::RETURN_PDO_STATEMENT ) ) {
			return $pdoStatement;
		}
		$rows = $pdoStatement->fetchAll();

		return $rows;
	}

	/**
	 * @param Insert $insert
	 *   An object that represents the insert statement.
	 *
	 * @return int
	 *   id of the row inserted
	 */
	private function execInsert( Insert $insert ) {
		$insert->build();
		$pdoStatement = $this->execRaw( $insert->getQuery(), $insert->getValues() );
		$id = (int) $this->connection->lastInsertId();
		return $id;
	}

	/**
	 * @param BatchInsert $batchInsert
	 *   An object that represents the batch insert statement.
	 *
	 * @return int
	 *   Number of rows inserted
	 */
	private function execBatchInsert( BatchInsert $batchInsert ) {
		$batchInsert->build();
		return $this->execRawNoCache( $batchInsert->getQuery(), $batchInsert->getValues() )->rowCount();
	}

	/**
	 * @param Replace $replace
	 *   An object that represents the replace statement.
	 *
	 * @return int
	 *   Number of rows affected i.e. rows delete + rows inserted
	 */
	private function execReplace( Replace $replace ) {
		$replace->build();
		$pdoStatement = $this->execRaw( $replace->getQuery(), $replace->getValues() );
		$affectedCount = $pdoStatement->rowCount();
		return $affectedCount;
	}

	/**
	 * @param Update $update
	 *   An object that represents the update statement.
	 *
	 * @return int
	 *   Number of rows updated
	 */
	private function execUpdate( Update $update ) {
		$update->build();
		$pdoStatement = $this->execRaw( $update->getQuery(), $update->getValues() );
		$affectedCount = $pdoStatement->rowCount();
		return $affectedCount;
	}

	/**
	 * @param Delete $delete
	 *   An object that represents the delete statement.
	 *
	 * @return int
	 *   Number of rows deleted
	 */
	private function execDelete( Delete $delete ) {
		$delete->build();
		$pdoStatement = $this->execRaw( $delete->getQuery(), $delete->getValues() );
		$affectedCount = $pdoStatement->rowCount();
		return $affectedCount;
	}


	/**
	 * Executes $query
	 * Before a Query is executed, a check is done to to see if the
	 * Query requires write permission and if the current user has
	 * write permission. If the current user lacks write permission
	 * while the Query requires it then execution is aborted and
	 * boolean false is returned.
	 *
	 * @param Query $query
	 * @return mixed
	 */
	public function exec( Query $query ) {

		// Check if write_permission is required to execute this query.
		// To be compatible with cron jobs who don't set permissions we
		// default to allow writes and then set permission to false when
		// needed
		if ( true === $query->getRequiresWritePermission() ) {
			if ( isset( $GLOBALS['write_permission'] ) && $GLOBALS['write_permission'] === false ) {
				// TODO: consider a solution to pass outcome to calling code $query->setError(Query::ERROR_NO_PERMISSION)
				$this->log( 'Query Not Executed. Write Permission Check Failed (' . get_class( $query ) . ')', $query );
				return false;
			}
		}

		if ( $query instanceof Select ) {
			return $this->execSelect( $query );
		}
		if ( $query instanceof Replace ) {
			return $this->execReplace( $query );
		}
		if ( $query instanceof BatchInsert ) {
			return $this->execBatchInsert( $query );
		}
		if ( $query instanceof Insert ) {
			return $this->execInsert( $query );
		}
		if ( $query instanceof Update ) {
			return $this->execUpdate( $query );
		}
		if ( $query instanceof Delete ) {
			return $this->execDelete( $query );
		}
		return false;
	}

	/**
	 * Execute a Raw Mysql Query
	 *
	 * @param String $query
	 *   A string ready for prepare()
	 * @param Array $values
	 *
	 * @return PDOStatement
	 *   A PDO Statement Object.
	 */
	public function execRaw( $query = "", array $values = NULL ) {

		if ( !is_string( $query ) ) {
			throw new DbEx( "Query is not a String", DbEx::INVALID_ARGS );
		}

		$pdoStatement = NULL;
		try {
			if ( isset( $this->queryCache[$query] )) {
				$pdoStatement = $this->queryCache[$query];
			} else {
				$pdoStatement = $this->connection->prepare( (string) $query );
				$pdoStatement->setFetchMode( PDO::FETCH_ASSOC );
				$this->queryCache[$query] = $pdoStatement;
			}
			if ( is_array( $values) && !empty( $values ) ) {
				foreach( $values as $key => $value ) {
					if ( is_int( $value ) ) {
						$pdoStatement->bindValue( $key, $value, PDO::PARAM_INT );
					} else {
						$pdoStatement->bindValue( $key, $value, PDO::PARAM_STR  );
					}

				}
			}

			$pdoStatement->execute();
			//$this->logQuery( "Query Executed: ", $query, $values );
		} catch ( PDOException $ex ) {
			unset( $this->queryCache[$query] );
			throw new DbEx( "Query Failed: '" . $query . "'", DbEx::QUERY_FAILED, $ex );
		}
		return $pdoStatement;
	}

	/**
	 * Execute a Raw Mysql Query without caching it
	 *
	 * @param String $query
	 *   A string ready for prepare()
	 * @param Array $values
	 *
	 * @return PDOStatement
	 *   A PDO Statement Object.
	 */
	public function execRawNoCache( $query = "", array $values = NULL ) {

		if ( !is_string( $query ) ) {
			throw new DbEx( "Query is not a String", DbEx::INVALID_ARGS );
		}

		$pdoStatement = NULL;
		try {
			$pdoStatement = $this->connection->prepare( (string) $query );
			$pdoStatement->setFetchMode( PDO::FETCH_ASSOC );

			if ( is_array( $values) && !empty( $values ) ) {
				foreach( $values as $key => $value ) {
					$pdoStatement->bindValue( $key, $value );
				}
			}

			$pdoStatement->execute();
			$this->logQuery( "Query Executed: ", $query, $values );
		} catch ( PDOException $ex ) {
			throw new DbEx( "Query Failed: '" . $query . "'", DbEx::QUERY_FAILED, $ex );
		}
		return $pdoStatement;
	}

	private function logQuery( $msg = "", $query = "", $values = array() ) {
		// if the query is larger than 10k then only log the first and last 5k
		if ( strlen( $query ) > 10000 ) {
			$query = "(Log truncated to show the first 5000 chars and then the last 5000 chars of the query):\n\n"
				. substr( $query, 0, 5000 ) . "\n\n(Showing last 5000 chars of the query):\n\n" . substr( $query, 0, -5000 );
		}
		$GLOBALS['debug']->storeActionLog( $msg, array( "pdoQuery" => $query, "pdoValues" => $values ) );

	}

	/**
	 * Logs an entry to the action log file
	 * @param string $msg
	 *   Message to log
	 * @param mixed $variableToPrint
	 *   A variable which will be output to the log file by using
	 *   print_r($variableToPrint, true)
	 */
	private function log( $msg = "", $variableToPrint = null ) {
		$GLOBALS['debug']->storeActionLog( $msg, $variableToPrint  );
	}

	/*
	 * Public Functions
	 */

	/**
	 * @param Select $select
	 *
	 * @return Integer Number of rows, ignoring the limit
	 *
	 * @todo: evaluate SQL_CALC_FOUND_ROWS options and benchmark
	 *
	 * @todo: FIX BUG !!
	 *   If a group function is used in the $select, this function doesn't work.
	 *   This needs to be fixed.
	 */
	public function rowCountIgnoreLimit( Select $select ) {

		// Caching the columns and the limit
		$cachedColumns = $select->getColumns();
		$cachedOrder = $select->getOrder();
		$cachedLimit = $select->getLimit();

		$rows = $select
			->columns( array( "count" => "COUNT(1)" ) )
			->resetOrder()
			->resetLimit()
			->exec();
		//print " select us $select  " ;
		//print_r ($rows) ;
		

		$count = $rows[0]['count'];

		// Restore the original select statement
		$select->columns( $cachedColumns ? $cachedColumns : array() );

		if ( !empty( $cachedLimit ) ) {
			$select->limit( $cachedLimit['offset'], $cachedLimit['count'] );
		}
		if ( !empty( $cachedOrder ) ) {
			$select->orderBy( $cachedOrder );
		}

		return $count;
	}

	/*
	 * Factory Methods
	 */


	public function select() {
		$select = new Select( $this );
		return $select;
	}

	public function insert() {
		$insert = new Insert( $this );
		return $insert;
	}

	public function insertIgnore() {
		$insert = new Insert( $this );
		$insert->setOption( Insert::IGNORE, true );
		return $insert;
	}

	/**
	 * Creates and returns a new BatchInsert query object
	 *
	 * @since 28.06.2012
	 * @return BatchInsert
	 */
	public function batchInsert() {
		$insert = new BatchInsert( $this );
		return $insert;
	}

	/**
	 * Creates and returns a new BatchInsert query object which is set to
	 * ignore duplicate primary and unique key errors. The rows which cause
	 * the duplication errors will not be inserted while all the other rows
	 * will be.
	 *
	 * @since 03.07.2012
	 * @return BatchInsert
	 */
	public function batchInsertIgnore() {
		$insert = new BatchInsert( $this );
		$insert->setOption( Insert::IGNORE, true );
		return $insert;
	}

	public function update() {
		$update = new Update( $this );
		return $update;
	}

	public function replace() {
		$replace = new Replace( $this );
		return $replace;
	}

	/**
	 * Returns a Delete Query object that is used to delete rows from the database.
	 * To limit the amount of rows being deleted see Delete::limit()
	 *
	 * @return mixed
	 *	false	:	delete failed
	 *	0		:	no rows deleted
 	 *	> 0		:	number of deleted rows
	 */
	public function delete() {
		$delete = new Delete( $this );
		return $delete;
	}

	/**
	 * Returns a Delete Query object that is used to delete rows from the database.
	 *
	 * @deprecated
	 * @todo TODO Changes existing usages of this method to the delete() method.
	 *
	 * @return mixed
	 *	false	:	delete failed
	 *	0		:	no rows deleted
 	 *	> 0		:	number of deleted rows
	 */
	public function deleteAll() {
		return $this->delete();
	}

	/**
	 * Disables triggers for the session.
	 */
	public function disableTrigger() {
		$this->connection->query( 'SET @TRIGGER_CHECKS = FALSE' );
	}

	/**
	 * Returns the DB instance with the specified name
	 *
	 * @param string $name
	 * @return DB
	 *	Returns null if the DB instance isn't found
	 */
	public static function getInstanceByName( $name ) {
		if ( isset( self::$instances[$name] ) ) {
			return self::$instances[$name];
		}
	}

	/**
	 * Sets a DB instance that can be later fetched with {@link DATABASE::getInstanceByName()}
	 * using the specified $name.
	 *
	 * @param string $name
	 * @param DB $db
	 */
	public static function setInstance( $name, $db ) {
		self::$instances[$name] = $db;
	}

}

class DbEx extends SfwEx {
	
	const UNKNOWN = -1;
	const INVALID_ARGS = 1;
	const INIT_FAILED = 2;
	const QUERY_FAILED = 3;
	
	function __construct( $message = "", $code = self::UNKNOWN, Exception $ex = null ) {
		parent::__construct( $message, $code, $ex );
	}
}

