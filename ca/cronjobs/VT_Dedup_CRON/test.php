<?php

ini_set('memory_limit', '-1');
require_once( './sfwex.class.php' );
require_once( './configuration.php' );
require_once( './db.class.php' );
require_once( './query.class.php' );

define('SCHEMA_NAME', 'software_suggestions_vt');
define('SCHEMA_NAME_VT', 'vuln_track');
define('SUGGESTION_BASETABLE', 'software_suggestions_raw');
define('SUGGESTION_DEDUP', 'software_suggestions');


$GLOBALS['db'] = new DB( DB_HOST, SCHEMA_NAME, DB_USER, DB_PASS );

$GLOBALS['db_vt'] = new DB( DB_HOST_VT, SCHEMA_NAME_VT, DB_USER_VT, DB_PASS_VT );


//Create a variable for start time

$time_start = microtime(true);

/* fetch the last inserted rows in Prod VT
 * For first time run,check the created date because cron_modidied_date column may be empty
 */

$query = "SELECT max(cron_modified_date) AS getLastModifiedRow  FROM ".SCHEMA_NAME_VT.".".SUGGESTION_DEDUP." WHERE platform IN (2,3)";

$rows = $GLOBALS['db_vt']->execRaw( $query )->fetchAll();

$recordsToSelect  = '' ;

if ( !empty ($rows[0]['getLastModifiedRow'])) {
	$recordsToSelect =  $rows[0]['getLastModifiedRow'] ;
}


print "\n Max modified date in VT Prod ".$rows[0]['getLastModifiedRow'];

$dateTimeSubQuery = '' ;

if (!empty ($recordsToSelect)) {
	$dateTimeSubQuery = " and created_date > '".$recordsToSelect."' " ;
}

/* Fetch records from RAW table for  Dedup*/

$queryCount = "SELECT count(*) as totalRecords FROM ".SCHEMA_NAME.".".SUGGESTION_BASETABLE." WHERE platform IN (2,3) ".$dateTimeSubQuery;
print  $queryCount;
$rowsCount = $GLOBALS['db']->execRaw( $queryCount)->fetchAll();

$totalCount = $rowsCount[0]['totalRecords'];
print "\n Total records need to process ".$rowsCount[0]['totalRecords'];

$time_end = microtime(true);
$time = $time_end - $time_start;

echo 'Total Execution time : '.$time.' seconds';
?>
