<?php
ini_set('memory_limit', '-1');

require_once( './sfwex.class.php' );
require_once( './configuration.php' );

require_once( './db.class.php' );
require_once( './query.class.php' );

define('SCHEMA_NAME', 'ssvt_old');
define('SCHEMA_NAME_VT', 'ssvt_date_check_onvt');
define('SUGGESTION_BASETABLE', 'software_suggestions');
define('SUGGESTION_DEDUP', 'software_suggestions');

$GLOBALS['db'] = new DB( DB_HOST, SCHEMA_NAME, DB_USER, DB_PASS );
$GLOBALS['db_vt'] = new DB( DB_HOST_VT, SCHEMA_NAME_VT, DB_USER_VT, DB_PASS_VT );

//Create a variable for start time

$time_start = microtime(true);

/* Fetch records counts from RAW table for which created_date available*/

$queryCount = "SELECT count(*) as totalRecords FROM ".SCHEMA_NAME.".".SUGGESTION_BASETABLE." WHERE platform IN (2,3) ";
$rowsCount = $GLOBALS['db']->execRaw( $queryCount)->fetchAll();

$recordsPerIteration = 10000 ;
$totalCount = $rowsCount[0]['totalRecords'];
print "\n total records need to modify ".$totalCount;
$start = 0;
$i  = 0 ;
if ( $totalCount > 0 ) {
	while ( $totalCount > 0 ) {
		
		$GLOBALS['db'] = new DB( DB_HOST, SCHEMA_NAME, DB_USER, DB_PASS );
		$GLOBALS['db_vt'] = new DB( DB_HOST_VT, SCHEMA_NAME_VT, DB_USER_VT, DB_PASS_VT );
		
		//Fetching 10K records at a time to update the created_date based on suggestion_id
		$query = "SELECT suggestion_id,created_date  FROM ".SCHEMA_NAME.".".SUGGESTION_BASETABLE."
					WHERE platform IN (2,3) 
					ORDER BY ".SUGGESTION_BASETABLE.".suggestion_id ASC
					LIMIT ".$start.",".$recordsPerIteration;
			
		$getAllRows = $GLOBALS['db']->execRaw( $query )->fetchAll();
		$GLOBALS['db']->queryString = null;
		
		//updating indvidual records
		foreach ($getAllRows as  $getRow) {
			
			$updateRow = "UPDATE ".SCHEMA_NAME_VT.".".SUGGESTION_DEDUP." SET created_date='".$getRow['created_date']."' WHERE suggestion_id=".$getRow['suggestion_id'];
			
			try {
				$GLOBALS['db_vt']->execRaw( $updateRow ) ;
			} catch (Exception $ex) {
				$response['debug'] =$ex->getMessage() ;
				$response['code'] = $ex->getCode();
				print_r ($response) ;
				exit();
			}
		}
		
		//Flushing all data to freeup RAM
		gc_enable();
		gc_collect_cycles() ;
		$GLOBALS['db_vt'] = null ;
		$GLOBALS['db'] = null ;
		
		$totalCount	= $totalCount- $recordsPerIteration;
		$start 		+= $recordsPerIteration ;
		$getAllRows = array();
		$getAllRows = null;
		print " \n Records needs to update ".$totalCount."\n";
	}
	
} else {
	print "\n Nothing to update \n";
}


$time_end = microtime(true);
$time = $time_end - $time_start;

echo 'Total Dedup time : '.($time/60).' Minutes';

?>
