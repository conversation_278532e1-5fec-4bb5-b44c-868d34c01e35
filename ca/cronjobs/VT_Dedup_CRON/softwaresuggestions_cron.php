<?php

ini_set('memory_limit', '-1');

require_once( './sfwex.class.php' );
require_once( './configuration.php' );
require_once( './db.class.php' );
require_once( './query.class.php' );

define('SCHEMA_NAME', 'software_suggestions_vt');
define('SCHEMA_NAME_VT', 'vuln_track');
define('SUGGESTION_BASETABLE', 'software_suggestions_raw');
define('SUGGESTION_DEDUP', 'software_suggestions');


$GLOBALS['db'] = new DB( DB_HOST, SCHEMA_NAME, DB_USER, DB_PASS );
$GLOBALS['db_vt'] = new DB( DB_HOST_VT, SCHEMA_NAME_VT, DB_USER_VT, DB_PASS_VT );


//Create a variable for start time

$time_start = microtime(true);

/* fetch the last inserted rows in Prod VT
 * Check the  cron_modidied_date column, it may be empty
 */

$query = "SELECT max(cron_modified_date) AS getLastModifiedRow  FROM ".SCHEMA_NAME_VT.".".SUGGESTION_DEDUP." WHERE platform IN (2,3)";

$rows = $GLOBALS['db_vt']->execRaw( $query )->fetchAll();

$recordsToSelect  = '' ;

if ( !empty ($rows[0]['getLastModifiedRow'])) {
	$recordsToSelect =  $rows[0]['getLastModifiedRow'] ;
}


$dateTimeSubQuery = '' ;

if (!empty ($recordsToSelect)) {
	$dateTimeSubQuery = " and created_date > '".$recordsToSelect."' " ;
}

/* Fetch records from RAW table for  Dedup*/

$queryCount = "SELECT count(*) as totalRecords FROM ".SCHEMA_NAME.".".SUGGESTION_BASETABLE." WHERE platform IN (2,3) ".$dateTimeSubQuery;
$rowsCount = $GLOBALS['db']->execRaw( $queryCount)->fetchAll();

$recordsPerIteration = 10000 ;
$totalCount = $rowsCount[0]['totalRecords'];
$start = 0;
print "\n total records need to dedup ".$totalCount;

if ( $totalCount > 0 ) {
	while ( $totalCount > 0 ) {
		
		$GLOBALS['db'] = new DB( DB_HOST, SCHEMA_NAME, DB_USER, DB_PASS );
		$GLOBALS['db_vt'] = new DB( DB_HOST_VT, SCHEMA_NAME_VT, DB_USER_VT, DB_PASS_VT );
		
		$query = "SELECT *  FROM ".SCHEMA_NAME.".".SUGGESTION_BASETABLE."
					WHERE platform IN (2,3) ".$dateTimeSubQuery."
					ORDER BY ".SUGGESTION_BASETABLE.".created_date ASC
					LIMIT ".$start.",".$recordsPerIteration;
		
		//print "sql is $query" ;
		
		$getAllRows = $GLOBALS['db']->execRaw( $query )->fetchAll();
		$GLOBALS['db']->queryString = null;
		
		$insertRow  = " INSERT INTO ".SCHEMA_NAME_VT.".".SUGGESTION_DEDUP." (
							FileName,
							CompanyName,
							ProductName,
							FileDescription,
							LegalCopyright,
							ProductVersion,
							FileVersion,
							Path,
							platform,
							match_value,
							InternalName,
							PETimestamp,
							AltPETimestamp,
							PEMachine,
							AltPEMachine,
							AGVersion,
							Version,
							sr_match_file_id,
							AppName,
							Status,
							cron_modified_date)  VALUE  " ;
		
		//Building INSERT SQL for all fetched records
		foreach ($getAllRows as  &$getRow) {
			
			if (empty ($getRow['sr_match_file_id'])) $getRow['sr_match_file_id'] = 0 ;
			
			$insertRow.= 		"(".$GLOBALS['db_vt']->quote($getRow['FileName']).",
								".$GLOBALS['db_vt']->quote($getRow['CompanyName']).",
								".$GLOBALS['db_vt']->quote($getRow['ProductName']).",
								".$GLOBALS['db_vt']->quote($getRow['FileDescription']).",
								".$GLOBALS['db_vt']->quote($getRow['LegalCopyright']).",
								".$GLOBALS['db_vt']->quote($getRow['ProductVersion']).",
								".$GLOBALS['db_vt']->quote($getRow['FileVersion']).",
								".$GLOBALS['db_vt']->quote($getRow['Path']).",
								".$GLOBALS['db_vt']->quote($getRow['platform']).",
								".$GLOBALS['db_vt']->quote($getRow['match_value']).",
								".$GLOBALS['db_vt']->quote($getRow['InternalName']).",
								".$GLOBALS['db_vt']->quote($getRow['PETimestamp']).",
								".$GLOBALS['db_vt']->quote($getRow['AltPETimestamp']).",
								".$GLOBALS['db_vt']->quote($getRow['PEMachine']).",
								".$GLOBALS['db_vt']->quote($getRow['AltPEMachine']).",
								".$GLOBALS['db_vt']->quote($getRow['AGVersion']).",
								".$GLOBALS['db_vt']->quote($getRow['Version']).",
								".$GLOBALS['db_vt']->quote($getRow['sr_match_file_id']).",
								".$GLOBALS['db_vt']->quote($getRow['AppName']).",
								".$getRow['Status'].",
								".$GLOBALS['db_vt']->quote($getRow['created_date'])."),";
		}
		
		$insertRow=rtrim($insertRow,','); // Remove last ','
		$insertRow.= " ON DUPLICATE KEY UPDATE ".SUGGESTION_DEDUP.".count = (".SUGGESTION_DEDUP.".count + 1) , cron_modified_date =  '".$getRow['created_date']."'" ;
		$getRow = null ;
		//Inserting 50K records at one GO
		try {
			$GLOBALS['db_vt']->execRaw( $insertRow ) ;
		} catch (Exception $ex) {
			$response['debug'] =$ex->getMessage() ;
			$response['code'] = $ex->getCode();
			//print_r ($response) ;
			//exit();
		}
		//Flushing all data to freeup RAM
		gc_enable();
		gc_collect_cycles() ;
		$GLOBALS['db_vt'] = null ;
		$GLOBALS['db'] = null ;
		$insertRow = null;
		
		$totalCount	= $totalCount- $recordsPerIteration;
		$start 		+= $recordsPerIteration ;
		$getAllRows = array();
		$getAllRows = null;
		print " \n Records needs to process ".$totalCount."\n";
	}
	
} else {
	print "\n Nothing to process \n";
}

$time_end = microtime(true);
$time = $time_end - $time_start;

echo "Total Execution time : ".($time/60)." Minutes";
?>
