<?php

  /**
   * @file query.class.php
   *   Helper classes to build simple queries to be used in PDOs
   *
   * @verbatim
   * Schema:




                               +---------------------+
                               |                     |
                               |        Query        |
                               |      (abstract)     |
                               +---------------------+
                                          |
                  +-----------------------+---------------------- -+
                  |                       |                        |
                  |                       |                        |
                  V                       V                        V
       +------------------+     +-------------------+    +-------------------+
       |                  |     |                   |    |                   |
       |     Select       |     |     Delete        |    |     WriteQuery    |
       |                  |     |                   |    |     (abstract)    |
       +------------------+     +-------------------+    +---------+---------+
                                                                   |
                                                       +-----------+------------+
                                                       |                        |
                                                       |                        |
                                                       V                        V
                                               +-----------------+      +----------------+
                                               |                 |      |                |
                                               |     Insert      |      |     Update     |
                                               |                 |      |                |
                                               +-----------------+      +----------------+
                                                        |
                                            +-----------+----------+
                                            |                      |
                                            |                      |
                                            V                      V
                                    +---------------+      +----------------+
                                    |               |      |                |
                                    |  BatchInsert  |      |    Replace     |
                                    |               |      |                |
                                    +---------------+      +----------------+


   @endverbatim
   @todo:
      - Move to exceptions
	  - Make a function for encapsulating the logic for generateParamName and binding the
	    value with it.
   */


abstract class Query {

	// Database connection
	protected $db;

	// Table which needs to be queried.
	// The query builder options for a single table.
	protected $table;

	// Query Options
	protected $columns;
	protected $where;
	protected $order; // @todo: move to the select class
	protected $limit;

	// The query that is built when build() is called
	protected $query;

	// Values for the Query
	protected $values;

	/**
	 * An array containing options that can be set
	 * for the curreny query object
	 */
	protected $options = array();

	protected $lastValueId;

	/**
	 * The central query object that is responsible
	 * for creating the correct named parameters assignments
	 * @var Query
	 */
	protected $container;

	/**
	 * Setting for whether the Query requires global write_permission or not to
	 * execute. This value is set automatically by the constructor of child
	 * classes and can be overridden by calling setRequiresWritePermission()
	 * @var boolean
	 */
	protected $requiresWritePermission = false;

	function __construct( DB $db = NULL ) {

		if ( !is_null( $db ) ) {
			$this->db = $db;
		}
		$this->query = "";
		/**
		 * The container is the Query object
		 * that centrally handles the named parameters
		 * assignments. Usually it is the actual object
		 * @var Query
		 */
		$this->container = $this;
		$this->reset();
	}

	/**
	 * Check if the string is save for insertion in the query
	 * Only alphanumeric characters and underscores are allowed.
	 * Spaces are NOT allowed as it can lead to injection
	 * e.g. $string = " OR 1" is invalid
	 *
	 */
	const INC_BRACKETS = 1;
	const INC_DASH = 2;
	const INC_SINGLE_QUOTE = 3;
	const INC_COMMA = 4;
	const INC_COMPARISON = 5;
	const INC_ADDITION = 6;
	public static function isSafe( $string, $options = array() ) {
		if ( !is_string( $string ) ) {
			return false;
		}
		$additionalChars = "";
		if ( $options ) {
			foreach( $options as $option ) {
				switch( $option ) {
				case self::INC_BRACKETS:
					$additionalChars .= '()';
					break;
				case self::INC_SINGLE_QUOTE:
					$additionalChars .= "'";
					break;
				case self::INC_DASH:
					$additionalChars .= '-';
					break;
				case self::INC_COMMA:
					$additionalChars .= ',';
					break;
				case self::INC_COMPARISON:
					$additionalChars .= '!<=>';
					break;
				case self::INC_ADDITION:
					$additionalChars .= '+';
					break;
				}
			}
		}
		$underscore = '_';
		$period = '.';
		if ( preg_match( '/[^A-Za-z0-9' . $underscore . $period . $additionalChars . ']/', $string ) ) {
			return false;
		}
		return true;
	}

	public static function escapeLike( $value ) {
		return str_replace( array( Exp::ESCAPE_CHAR, '%', '_' ), array( Exp::ESCAPE_CHAR . Exp::ESCAPE_CHAR, Exp::ESCAPE_CHAR . '%', Exp::ESCAPE_CHAR . '_' ), $value );
	}

	protected function reset() {
		$this->values = array();
		$this->lastValueId = 0;
	}

	protected function nextValueId() {
		return $this->lastValueId++;
	}

	/*
	protected function generateNamedParam( $key ) {
		$namedParam = ":_" . $key . "_" . $this->nextValueId();
		return $namedParam;
	}
	*/
	/*
	 * @todo: The key isn't being used anymore.. make changes
	 */
	protected function generateNamedParam( $key = null ) {
		$namedParam = ":_" . $this->nextValueId();
		return $namedParam;
	}

	/*
	 * Generates a named param and binds the value with it
	 * Also returns the named param so that it can be used in the queries.
	 *
	 */
	public function bindValue( $value ) {
		$namedParam = $this->generateNamedParam();
		$this->values[$namedParam] = $value;
		return $namedParam;
	}

	/**
	 * Sets the container of the current query object
	 * to another instance that handles the named parameters
	 * @param Query $container
	 */
	public function setContainer( Query $container ) {
		$this->container = $container;
	}

	protected function buildColumns() {
		$columns = $this->columns;

		// Select all columns by default
		if ( !is_array( $columns ) || empty( $columns ) ) {
			return "*";
		}

		$columnsQuery = "";

		foreach( $columns as $key => $value ) {
			$columnQuery = "";
			/**
			 * If the key is numeric than it is basically ignored when constructing the query.
			 * If however, the key is not numeric, it must be a valid string.
			 */
			if (
				!is_numeric( $key )
				&& !self::isSafe ( $key )
			) {
				throw new SfwEx( "Invalid column name." );
			}

			switch ( true ) {
			case $value instanceof SqlFn:
				$value->setContainer( $this );
				$columnQuery = $value->getQuery();
				break;
			case $value instanceof Select:
				$value->setContainer( $this );
				$value->build();
				$columnQuery = "(" . $value->getQuery() . ")";
				break;
			default:
				if ( !self::isSafe ( $value , array( self::INC_BRACKETS, self::INC_SINGLE_QUOTE, self::INC_COMMA, self::INC_COMPARISON, self::INC_ADDITION, self::INC_DASH ) ) ) {
					throw new SfwEx( "Invalid column value" );
				}
				$columnQuery = $value;
				break;
			}

			if ( !is_numeric( $key ) ) {
				$columnQuery = $columnQuery . " AS " . $key ;
			}

			$columnsQuery .= ( $columnsQuery === "" ) ? $columnQuery : ( ", " . $columnQuery );
		}
		return $columnsQuery;
	}

	protected function buildIn( $key, $values ) {

		$inQuery = "";
		foreach ( $values as $value ) {
			if ( $inQuery !== "" ) {
				$inQuery .= ", ";
			}
			switch ( true ) {
			case ( is_numeric( $value ) || is_string( $value ) ):
				$namedParam = $this->container->bindValue( $value );
				$inQuery .= $namedParam;
				break;
			default:
				continue;
				break;
			}
		}
		return $key . " IN ( " . ( $inQuery === "" ? "''" : $inQuery ) . " ) ";
	}

	public static function buildGroupedConditions( $container, $key, array $values, $condition = "" ) {
		switch ( $condition ) {
		case "OR":
		case "AND":
			break;
		default:
			return false;
		}
		$query = "";
		if ( empty( $values ) ) {
			return $query;
		}
		foreach ( $values as $value ) {
			if ( $query !== "" ) {
				$query .= " " . $condition . " ";
			}
			switch ( true ) {
			case $value instanceof Select:
				$value->setContainer( $container );
				$value->build();
				$query .=  $key . " = ( " . $value->getQuery() . " ) ";
				break;
			case $value instanceof Exp:
				$value->setContainer( $container );
				$query .=  $key . " " . $value->getQuery();
				break;
			case ( is_numeric( $value ) || is_string( $value ) ):
				$namedParam = $container->bindValue( $value );
				$query .= $key . " = " . $namedParam;
				break;
			default:
				continue;
				break;
			}
		}
		return " ( " . $query . " ) ";
	}

	protected function buildArrayValues( $key, $values ) {

		if ( !self::isSafe( $key ) ) {
			// @todo, throw error !
			return '';
		}

		if ( !is_array( $values ) ) {
			return "";
		}

		if ( is_numeric( $key ) ) {
			return "";
		}

		/*
		 * Check if the values only contain numbers or strings
		 */
		$buildIn = true;
		foreach ( $values as $value ) {
			if ( !is_numeric( $value ) && !is_string( $value ) ) {
				$buildIn = false;
			}
		}

		$query = "";
		if ( $buildIn ) {
			$query =  $this->buildIn( $key, $values );
		} else {
			$query = self::buildGroupedConditions( $this, $key, $values, "OR" );
		}

		return $query;
	}

	protected function buildWhere( $where = NULL, $condition = "AND" ) {

		if ( !$where ) {
			$where = $this->where;
		}

		if ( !is_array( $where ) || empty( $where ) ) {
			return "";
		}

		$whereQuery = "";
		foreach( $where as $key => $value ) {
			/**
			 * Validation ( Key/Value ):
			 *
			 * Decide on how to validate the key and the value
			 * of the where condition
			 */
			switch ( true ) {
				/**
				 * If the key is a string, then it should be possible
				 * to be used for a column condition
				 */
			case is_string( $key ):
				if ( !self::isSafe( $key ) ) {
					throw new SfwEx(
									"`" . $key . "` is an invalid column specification for a where condition" );
				}
				break;
				/**
				 * If the key is integer, it means that it has not been
				 * specified explicitly so the value itself should be able
				 * to handle the sql condition.
				 */
			case is_int( $key ):
				switch ( true ) {
				case ( $value instanceof SqlFn ):
				case ( $value instanceof Exp ):
				case ( is_array( $value ) ):
					break;
				default:
					throw new SfwEx( "Argument " . $key . " of the where condition cannot be translated to SQL" );

				}
				break;
				/**
				 * Any other key type is invalid
				 */
			default:
				throw new SfwEx(
								"One of the left side where components of the where"
								." condition is of an unrecognised type" );
				break;
			}

			if ( $whereQuery !== "" ) {
				$whereQuery .= " " . $condition . " ";
			}

			// Handle nested conditions
			if ( is_numeric( $key ) && is_array( $value ) ) {
				$whereQuery .= " ( " . $this->buildWhere( $value, $condition ) . " ) ";
				continue;
			}

			/*
			 * The key contains the column name or the condition i.e AND/OR
			 */
			switch ( strtoupper( $key ) ) {
			case "OR":
			case "AND":
				$whereQuery .= " ( " . $this->buildWhere( $value, $key ) . " ) ";
				break;
			default:
				if ( is_array( $value ) ) {
					$whereQuery .= $this->buildArrayValues( $key, $value );
				}  else if ( $value instanceof Exp ) {
					$expression = $value;
					$expression->setContainer( $this );
					if ( !$expression->isKeySet() ) {
						$whereQuery .= " " . $key . " ";
					}
					$whereQuery .= $expression->getQuery();
				} else if ( $value instanceof SqlFn ) {
					$value->setContainer( $this );
					/**
					 * If the key is a string than it can be specified
					 * as the left hand side argument of the where condition,
					 * otherwise the SqlFn instance handles all sql
					 */
					if ( is_string( $key ) ) {
						$whereQuery .=  $key . " = ";
					}
					$whereQuery .= $value->getQuery();
				} else if ( $value instanceof Select ) {
					$value->setContainer( $this );
					if ( is_string( $key ) ) {
						$whereQuery .=  $key . " = ";
					}
					$value->build();
					$whereQuery .= " ( " . $value->getQuery() . " ) ";
				} else if ( is_numeric( $value ) || is_string( $value ) ) {
					$namedParam = $this->container->bindValue( $value );
					$whereQuery .=  $key . " = " . $namedParam;
				} else if ( $value instanceof Column ) {
					/**
					 * When PHP is upgraded above 5.2, the toString call
					 * should be removed as it will automatically be handled
					 * by the magic method __toString
					 */
					$whereQuery .= $key . " = " . $value->toString();
				} else if ( !isset( $value ) ) {
					throw new SfwEx( "Value is not set for key: " . $key );
				} else {
					throw new SfwEx( "Invalid Object found in query." );
				}
			}

		}

		return $whereQuery;
	}

	protected function buildOrder() {
		$order = $this->order;
		// By default, Order by nothing to improve performance
		if ( !is_array( $order ) || empty( $order ) ) {
			return " ORDER BY NULL";
		}

		$orderQuery = "";
		foreach ( $order as $column => $direction ) {
			if ( self::isSafe( $column . $direction, array( self::INC_BRACKETS ) ) ) {
				if ( $orderQuery !== "" ) {
					$orderQuery .= ", ";
				}
				$orderQuery .=  $column . " " . $direction;
			}
		}

		if ( $orderQuery !== "" ) {
			$orderQuery  = " ORDER BY " . $orderQuery;
		}

		return $orderQuery;
	}

	/*
	 * @todo: use named params for building the limit instead of hardcoding them in the
	 * query
	 */
	protected function buildLimit() {
		$limit = $this->limit;
		if ( !is_array( $limit ) || empty( $limit ) ) {
			return "";
		}

		$limitQuery = "";
		$offset = isset( $limit['offset'] ) ? (int) $limit['offset'] : 0;
		$count = isset( $limit['count'] ) ? (int) $limit['count'] : NULL;
		if ( $count !== NULL ) {

			if ( $offset > 0 ) {
				$limitQuery = $offset . " , ";
			}

			$limitQuery = " LIMIT " . $limitQuery . $count;
		}

		return $limitQuery;
	}

	/*
	 * Getters
	 */

	public function getQuery() {
		return $this->query;
	}

	public function getValues() {
		if ( empty( $this->values ) ) {
			return array();
		}
		return $this->values;
	}

	public function getColumns() {
		return isset( $this->columns ) ? $this->columns : NULL;
	}

	public function getOrder() {
		return isset( $this->order ) ? $this->order : NULL;
	}

	public function getLimit() {
		return isset( $this->limit ) ? $this->limit : NULL;
	}

	/*
	 * Query execution functions
	 */

	public function exec() {
		return $this->db->exec( $this );
	}
	
	public function execRaw( $query = "", array $values = array() ) {
		
		if ( !is_string( $query ) ) {
			throw new DbEx( "Query is not a String", DbEx::INVALID_ARGS );
		}
		
		$pdoStatement = NULL;
		try {
			if ( isset( $this->queryCache[$query] )) {
				$pdoStatement = $this->queryCache[$query];
			} else {
				$pdoStatement = $this->connection->prepare( (string) $query );
				$pdoStatement->setFetchMode( PDO::FETCH_ASSOC );
				$this->queryCache[$query] = $pdoStatement;
			}
			if ( !empty( $values ) ) {
				foreach( $values as $key => $value ) {
					if ( is_int( $value ) ) {
						$pdoStatement->bindValue( $key, $value, PDO::PARAM_INT );
					} else {
						$pdoStatement->bindValue( $key, $value, PDO::PARAM_STR  );
					}
					
				}
			}
			
			$this->logQuery( $query, $values );
			$pdoStatement = $this->execWithRetry( $pdoStatement, $query, $values );
		} catch ( PDOException $ex ) {
			unset( $this->queryCache[$query] );
			$this->logQuery( $query, $values, DbEx::QUERY_FAILED );
			throw new DbEx( "Query Failed: '" . $query . "'", DbEx::QUERY_FAILED, $ex );
		}
		return $pdoStatement;
	}

	/*
	 * Query Building Functions
	 * Only for building single table and simple queries
	 */

	protected function setTable( $table = "" ) {
		if ( self::isSafe( $table ) ) {
			$this->table = $table;
			return $this;
		}
		return false;
	}

	public function where( array $where = NULL ) {
		$this->where = $where;
		return $this;
	}

	public function whereAnd( array $where = NULL ) {
		$this->where[] = $where;
		return $this;
	}

	public function orderBy( array $order = NULL ) {
		$this->order = $order;
		return $this;
	}

	public function limit( $offset, $count ) {

		if ( !isset( $offset ) || !isset( $count ) ) {
			return false;
		}
		$this->limit = array( "offset" => $offset, "count" => $count );
		return $this;
	}

	/**
	 * Set an existing option's property value
	 * @param int $option the code of the option
	 * @param mixed $value the value of the option
	 * @return Query return the same object
	 */
	public function setOption( $option, $value ) {
		if ( !$this->hasOption( $option ) ) {
			throw new SfwEx( "The query object does not have a '" . $option . "' option." );
		}
		$this->options[ $option ] = $value;
		return $this;
	}

	/**
	 * Get an existing option's property value
	 * @param int $option the code of the option
	 * @return mixed the value of the option
	 */
	public function getOption( $option ) {
		if ( !$this->hasOption( $option ) ) {
			throw new SfwEx( "The query object does not have a '" . $option . "' option." );
		}
		return $this->options[ $option ];
	}

	/**
	 * Checks if a certain option exists exists for the object
	 * @param int $option the id of the option
	 * @return boolean
	 */
	protected function hasOption( $option ) {
		if ( isset( $this->options[ $option ] ) ) {
			return true;
		}
		return false;
	}

	/**
	 * Sets the requiresWritePermission variable
	 *
	 * By default, any user who lacks write_permission will be unable
	 * to execute write queries (INSERT, UPDATE, REPLACE and DELETE)
	 *
	 * By settings requiresWritePermission to false, you allow a Query
	 * to execute as if it had write permissions.
	 *
	 * @since 20.07.2012
	 * @param boolean $required
	 * @return Query return the object on which the flag is being set on
	 */
	public function setRequiresWritePermission( $required ) {
		$this->requiresWritePermission = !!$required;
		return $this;
	}

	/**
	 * Gets the requiresWritePermission variable
	 * @return boolean
	 */
	public function getRequiresWritePermission() {
		return $this->requiresWritePermission;
	}

	/*
	 * Abstract Method: build()
	 * Must be implemented by the classes extending the Query class. The function should
	 * result in updating the $this->query variable.
	 *
	 * Builds the query string with the named params. The query is ready to be used in
	 * the PDO's prepare() function. Also sets the values that should be bound the the
	 * query.
	 *
	 * - The query can be fetched using $this->getQuery()
	 * - The values can be be fetched using $this->getValues()
	 *
	 */

	abstract public function build();
}

/**
 * @class Select
 *   A query object that builds the select statement
 *
 *
 * @code
 *
 *   $select = new Select();
 *   $select
 *         ->columns( array( "account_id", "account_username" ) )
 *         ->from( "accounts" )
 *         ->where( array( "account_id" => array( 142, 1668 ) ) );
 *
 * @endcode
 *
 */
class Select extends Query {

	protected $tables;

	// String that holds the join information
	protected $joinQuery;

	protected $groupBy;

	/**
	 * @var bool
	 * Holds whether the Select Query has a DISTINCT clause or not. If true then
	 * the Select Query will be SELECT DISTINCT otherwise it will just be SELECT.
	 */
	private $distinct = false;

	function __construct( DB $db = null ) {
		parent::__construct( $db );

		$this->joinQuery = "";
	}

	const RETURN_PDO_STATEMENT = 1;

	/**
	 * A set of options applicabale to Select queries
	 */
	protected $options = array(
		/**
		 * The result of the exec method on the query will return
		 * a PDO statement and will NOT fetchAll results. This will
		 * primarly be used to take advantage of the default unbuffered
		 * behaviour of PDO. Defaults to false.
		 */
		self::RETURN_PDO_STATEMENT => false
	);


	const SIMPLE_JOIN = 0;
	const RIGHT_JOIN = 1;
	const LEFT_JOIN = 2;

	/**
	 * Builds the DISTINCT clause for the Select Query.
	 *
	 * @return string
	 * 	Returns a spacebar padded (0x20) ' DISTINCT ' string if the DISTINCT
	 * 	clause is enabled for a Select Query otherwise returns an empty string.
	 */
	protected function buildDistinct() {
		return ( $this->distinct ? ' DISTINCT ' : '' );
	}

	protected function buildJoin( $type = self::SIMPLE_JOIN) {
		$count = count($this->tables);
		if ( $count > 1 ) {
			switch( $type ) {
			case self::RIGHT_JOIN:
				$this->joinQuery .= " RIGHT";
				break;
			case self::LEFT_JOIN:
				$this->joinQuery .= " LEFT";
				break;
			}
			$this->joinQuery .= " JOIN " . $this->tables[$count-1];
			return true;
		}
		return false;
	}

	protected function buildOn( array $conditions = NULL ) {
		$condition = "";
		foreach( $conditions as $key => $value ) {
			if ( !self::isSafe( $key . $value ) ) {
				throw new SfwEx( "The Select Query's ON condition failed it's isSafe() check" );
			}
			if ( $condition !== "" ) {
				$condition .= " AND ";
			}
			$condition .= $key . " = " . $value;
		}
		$this->joinQuery .= " ON (" . $condition . ") ";
	}

	protected function buildUsing( array $columns = NULL ) {
		foreach( $columns as $column ) {
			if ( !self::isSafe( $column ) ) {
				throw new SfwEx( "The Select Query's USING condition failed it's isSafe() check" );
			}
		}
		$this->joinQuery .= " USING (" . implode( ",", $columns ) . ") ";
	}

	protected function fetchJoin() {
		return " FROM " . $this->tables[0] . $this->joinQuery;
	}

	protected function buildGroupBy() {

		$groupBy = $this->groupBy;

		if ( empty( $groupBy ) ) {
			return "";
		}

		$query = "";
		foreach ( $groupBy as $column ) {
			if ( !self::isSafe( $column ) ) {
				continue; // @todo: throw error
			}
			if ( $query !== "" ) {
				$query .= ", ";
			}
			$query .= $column;
		}
		if ( $query !== "" ) {
			$query = " GROUP BY " . $query;
		}
		return $query;
	}

	/*
	 * Overloaded Functions
	 */
	protected function setTable( $table = "", $alias = "" ) {
		if ( !empty( $table ) && self::isSafe( $alias . $table ) ) {
			if ( $alias !== "" ) {
				$table = $table . " AS " . $alias ;
			}
			$this->tables[] = $table;

			return $this;
		}
		$table = $alias ? ( $table . " AS " . $alias ) : $table;
		throw new SfwEx( "The table specified in the Select Query is invalid: " . $table );
	}

	/**
	 * Builds the columns contained in the Select Query object and returns the
	 * result as an SQL string.
	 *
	 * This method overloads Query::buildColumns() making it public so it's
	 * column building functionality can be used to clean and process other
	 * columns.
	 *
	 * @return string
	 */
	public function buildColumns() {
		return parent::buildColumns();
	}

 	/*
	 * Implementing abstract methods
	 */

	public function build() {

		$this->reset();

		$query = "SELECT ";
		$query .= $this->buildDistinct();
		$query .= $this->buildColumns();
		$query .= $this->fetchJoin();
		$whereQuery = $this->buildWhere();
		$query .= ( trim( $whereQuery ) ? " WHERE " . $whereQuery : "" );
		$query .= $this->buildGroupBy();
		$query .= $this->buildOrder();
		$query .= $this->buildLimit();

		$this->query = $query;
	}


	/*
	 * Query Building Functions
	 */

	/**
	 * @var bool
	 * Sets whether the Select Query has a DISTINCT clause or not. If true then
	 * the Select Query will be 'SELECT DISTINCT' otherwise it will be 'SELECT'.
	 *
	 * @param bool $distinct If true the Select Query will be DISTINCT.
	 */
	public function distinct( $distinct = true ) {
		$this->distinct = $distinct;
		return $this;
	}

	public function columns( array $columns ) {
		$this->columns = $columns;
		return $this;
	}

	public function from( $table = "", $alias = "" ) {
		return $this->setTable( $table, $alias );
	}

	public function join( $table = "", $alias = "", $type = self::SIMPLE_JOIN ) {
		$this->setTable( $table, $alias );
		$this->buildJoin( $type );
		return $this;
	}

	public function leftJoin( $table = "", $alias = "" ) {
		return $this->join( $table, $alias, self::LEFT_JOIN );
	}

	public function rightJoin( $table = "", $alias = "" ) {
		return $this->join( $table, $alias, self::RIGHT_JOIN );
	}

	public function on( array $conditions ) {
		$this->buildOn( $conditions );
		return $this;
	}

	public function using( array $columns ) {
		$this->buildUsing( $columns );
		return $this;
	}

	public function groupBy( array $groupBy ) {
		$this->groupBy = $groupBy;
		return $this;
	}

	public function limit( $arg1, $arg2 = NULL ) {
		if ( !isset( $arg1 ) ) {
			return false;
		}
		$offset = 0;
		$count = 0;
		if ( is_null( $arg2 ) ) {
			$count = $arg1;
		} else {
			$offset = $arg1;
			$count = $arg2;
		}
		return parent::limit( $offset, $count );
	}

	public function resetOrder() {
		$this->order = NULL;
		return $this;
	}

	public function resetLimit() {
		$this->limit = NULL;
		return $this;
	}

	/*
	 * Query execution
	 */

	public function rowCountIgnoreLimit() {
		return $this->db->rowCountIgnoreLimit( $this );
	}
}


class Delete extends Query {

	function __construct( DB $db = null ) {
		parent::__construct( $db );
		$this->requiresWritePermission = true;
	}

	/*
	 * Query Building functions
	 */
	public function from( $table ) {
		return $this->setTable( $table );
	}

	/**
	 * Limits the maximum number of rows this Delete can delete from the database.
	 *
	 * @param int $count
	 * @return Delete
	 */
	public function limit( $count = 0 ) {
		$this->limit = array( "offset" => 0, "count" => $count );
		return $this;
	}

 	/*
	 * Implementing abstract methods
	 */

	public function build() {

		$this->reset();

		$query = "DELETE ";
		$query .= " FROM " . $this->table;
		$whereQuery = $this->buildWhere();
		$query .= ( trim( $whereQuery ) ? " WHERE " . $whereQuery : "" );
		$query .= $this->buildLimit();

		$this->query = $query;
	}

}

/**
 * Edit Queries i.e.
 *
 * - INSERT
 * - UPDATE
 *
 */
abstract class WriteQuery extends Query {

	protected $set;

	function __construct( DB $db = null ) {
		parent::__construct( $db );
		$this->requiresWritePermission = true;
	}

	/*
	 * Build query to be used in the edit queries e.g. insert/update.
	 *
	 * Syntax:
	 *
	 * INSERT INTO x
	 * SET a = 1, b = 2, c = 3
	 *
	 * UPDATE x
	 * SET a = 1, b = 2
	 * WHERE c = 3
	 *
	 */
	protected function buildSet() {

		$set = $this->set;
		if ( !is_array( $set ) || empty( $set ) ) {
			return "";
		}

		$setQuery = "";
		foreach( $set as $key => $value ) {
			if ( !self::isSafe( $key ) ) {
				// @todo
				// Throw error !!
				continue;
			}

			if ( $setQuery !== "" ) {
				$setQuery .= ", ";
			}

			switch ( true ) {
			case $value instanceof SqlFn:
				$value->setContainer( $this );
				$setQuery .=  $key . " = " . $value->getQuery();
				break;
			default:
				$namedParam = $this->container->bindValue( $value );
				$setQuery .=  $key . " = " . $namedParam;
				break;
			}

		}

		if ( $setQuery !== "" ) {
			$setQuery = " SET " . $setQuery;
		}

		return $setQuery;
	}

	/*
	 * Query Building Functions
	 */

	public function set( array $set = NULL ) {
		$this->set = $set;
		return $this;
	}

}

/*
 * @class Insert
 *   A query class that is used to build the Insert Statement
 *
 * Examples:
 *
 * @code
 *
 *   $insert = new Insert();
 *   $insert
 *         ->into( "accounts" )
 *         ->set( array( "account_gen_pwd" => 0 ) )
 *         ->where( array( "account_id" => 142 )  );
 *
 * @endcode
 *
 * or using a Select statement as the input:
 *
 * @code
 *
 *   $select = new Select();
 *   $select
 *         ->columns( array( "account_name", "account_gen_pwd" ) )
 *         ->where( array( "account_id" => array( 142, 1001 ) ) );
 *
 *   $insert
 *         ->into( "accounts" )
 *         ->columns( array( "account_name", "account_gen_pwd" ) )
 *         ->select( $select );
 *
 *
 * @endcode
 *
 */
class Insert extends WriteQuery {

	protected $select;

	const IGNORE = 1;

	/**
	 * A set of options applicabale to Select queries
	 */
	protected $options = array(
		/**
		 * This flag determines if the insert operation will skip
		 * inserting records if there are conflicting KEYS
		 */
		self::IGNORE => false
	);

	function __construct( DB $db = null ) {
		parent::__construct( $db );
	}

	protected function buildQuery() {
		$query = "";
		$query .= $this->table;

		/*
		 * We can have two syntaxes:
		 *
		 * 1. Fetching values from other tables (can result in multiple entries):
		 *
		 * INSERT INTO table ( col1, col2 ..)
		 * SELECT col1, col2 FROM table2 WHERE ..
		 *
		 * or
		 *
		 * 2. Manually inserting the values:
		 *
		 * INSERT INTO table
		 * SET ...
		 *
		 */
		if ( isset( $this->select ) ) {
			// Build the columns if they are specified
			if ( isset( $this->columns ) && !empty( $this->columns ) ) {
				$query .= "( " . $this->buildColumns() . " ) ";
			}
			$this->select->build();
			$query .= " " . $this->select->getQuery();
			$this->values = $this->select->getValues();
		} else {
			$query .= $this->buildSet();
		}
		return $query;
	}

	/*
	 * Query building functions
	 */

	public function select( Select $select ) {
		$this->select = $select;
		return $this;
	}

	public function into( $table ) {
		return $this->setTable( $table );
	}

	public function columns( array $columns ) {
		$this->columns = $columns;
		return $this;
	}

 	/*
	 * Implementing abstract methods
	 */

	public function build() {

		$this->reset();

		$query = "INSERT ";
		if ( $this->getOption( self::IGNORE ) ) {
			$query .= "IGNORE ";
		}
		$query .= "INTO ";
		$query .= $this->buildQuery();

		$this->query = $query;
	}

}

/**
 * @class Update
 *   A query class that is used to build the Update Statement
 *
 *
 * @code
 *
 *   $update = new Update();
 *   $update
 *         ->table( "accounts" )
 *         ->set( array( "account_gen_pwd" => 0 ) )
 *         ->where( array( "account_id" => 142 )  );
 *
 * @endcode
 *
 */

class Update extends WriteQuery {

	function __construct( DB $db = null ) {
		parent::__construct( $db );
	}

	protected function buildQuery() {
		$query = "";
		$query .= $this->table;

		$query .= $this->buildSet();

		$whereQuery = $this->buildWhere();
		$query .= ( trim( $whereQuery ) ? " WHERE " . $whereQuery : "" );

		$query .= $this->buildLimit();
		return $query;
	}

	/*
	 * Query building functions
	 */

	public function table( $table ) {
		return $this->setTable( $table );
	}

 	/*
	 * Implementing abstract methods
	 */

	public function build() {

		$this->reset();

		$query = "UPDATE ";
		$query .= $this->buildQuery();
		$this->query = $query;
	}

}

/**
 * Class BatchInsert
 * A query class that is used to build a batch insert statement.
 * The inserted row count is returned by the exec() function.
 *
 * If the insert would cause a duplicate primary or unique key then the batch
 * insert will raise an error and abort. In this situation you can use
 * DB::batchInsertIgnore which will make mysql treat these errors as warnings
 * and not abort the statement, allowing valid rows to still be inserted.
 *
 * NOTE: The max named placeholders we can support with prepared statements
 *       is 65,535. You can estimate it with rows*cols. If our data exceeds
 *       this count then buildValues() will throw an exception  which  will
 *		 stop the query build process.
 *
 *       Batch inserting using prepared  statements can be expensive due to
 *       the amount of values that need to be bound so consider a different
 *       solution if you need to insert a lot of rows.
 *
 * @example 1:
		<pre><code>
		// $values is an array of rows.
		// Each row is a numerically indexed array of values.
		$values = getValues();
		$batchInsert = new BatchInsert();
		$batchInsert
			  ->into( 'csi_wsus_update_event' )
			  ->columns( array(
					'wsus_event_id'
					,'computer_guid'
					,'update_guid'
					,'event_date'
					,'installation_status'
			  ) )
			  ->values( $values );
		$rowsInserted = $batchInsert->exec(); // returns number of rows inserted
		</code></pre>
 * @example 2:
		<pre><code>
		// $db is an instance of DB
		$rowsInserted = $db->batchInsert() // or batchInsertIgnore()
			->into( 'csi_wsus_state_change' )
			->columns( array( 'computer_guid', 'update_guid', 'event_date', 'installation_status' ) )
			->values( $values )
			->exec();
		</code></pre>
 *
 * @package DB
 * @subpackage Query
 * @since 28.06.2012
 */
class BatchInsert extends Insert {
    /**
     * An array of rows. Each row is an array of column values.
     * @var array
     */
    protected $_values = array();

    /**
     * Assigns the array of values used for inserting the rows
     *
     * @param array $values
     * 	An array of rows where each row is a numerically indexed array of values
     * @return BatchInsert
     */
	public function values( array $values ) {
	    $this->_values = $values;
	    return $this;
	}

    /**
     * Builds and returns the dynamic portion of the query string
     *
     * @return string
     */
 	protected function buildQuery() {
 		// The columns portion of the query isn't required
 		$columns = '';
 		if ( !empty( $this->columns ) ) {
 	    	$columns = '(' . $this->buildColumns() . ')';
 	    }
 	    $values = $this->buildValues();
		return "{$this->table} {$columns} VALUES {$values}";
	}

    /**
     * Builds and returns the VALUES content of the insert query
     *
     * @throws DbEx
     * @return string
     */
	private function buildValues() {
	    $sql = '';

	    $rowCount = count($this->_values);
	    if ( 0 === $rowCount ) {
	    	return $sql; // if no rows, no need to process further
	    }

	    $colCount = count($this->columns);
	    if ( 0 === $colCount ) {
	    	$colCount = count(reset($this->_values));
	    }

		// check if prepared statements can handle our data
	    $paramCount = $rowCount * max(count($this->columns), 1) + count($this->set);
	    if ( $paramCount > 65535 ) {
	    	throw new DbEx( 'Too many placeholders for prepared statement in BatchInsert::buildValues' );
	    }

	    $lastRowCommaIndex = $rowCount - 1;
	    $lastColCommaIndex = $colCount - 1;

	    // create a long sql query string
	    for ($r = 0; $r < $rowCount; $r++) { // rows
	        $sql .= '(';
	        for ($c = 0; $c < $colCount; $c++) { // columns
	            $namedParam = $this->container->bindValue( $this->_values[$r][$c] );
	            if ($c < $lastColCommaIndex) {
	                $sql .= "{$namedParam}, ";
	            } else {
	                $sql .= "{$namedParam}";
	            }
	        }
	        if ($r < $lastRowCommaIndex) {
	            $sql .= '), ';
	        } else {
	            $sql .= ')';
	        }
	    }
		return $sql;
	}

} // END BatchInsert


/**
 * @class Replace
 *   A query object that builds the repalce statement
 *
 */

class Replace extends Insert {

	function __construct( DB $db = null ) {
		parent::__construct( $db );
	}

 	/*
	 * Implementing abstract methods
	 */

	public function build() {

		$this->reset();

		$query = "REPLACE ";
		$query .= $this->buildQuery();
		$this->query = $query;
	}

}

class Exp {

	/*
	 * The container / query object that holds this expression and maintains the named
	 * params. It is set by the container itself. The expression can not be build if
	 * the $container is not set.
	 */
	private $container;

	private $operator;
	private $key; // operand 1
	private $value; // operand 2
	private $options;

	/*
	 * Constants
	 */
	const ESCAPE_CHAR = '=';

	/*
	 * Private constructor so that there is no need to verify the $operator
	 */
	private function __construct( $operator = "=", $operand1 = NULL, $operand2 = NULL, Array $options = null ) {

		$this->operator = $operator;
		$this->options = $options;

		if ( !is_null( $operand1 ) && !Query::isSafe( $operand1 ) ) {
			throw new SfwEx( "Column name is invalid." );
		}

		// Validate types
		if (
			is_numeric( $operand2 )
			|| is_string( $operand2 )
			|| $operand2 instanceof SqlFn
			|| $operand2 instanceof Column
			|| ( is_string( $operand1 ) && is_array( $operand2 ) )
		) {
			$this->key = $operand1;
			$this->value = $operand2;
		} else if ( !is_null( $operand2 ) ) {
			throw new SfwEx( "Operand2: '" . $operand2 . "' has invalid type." );
		}
	}

	private function build() {
		if ( is_null( $this->container ) ) {
			throw new SfwEx( "Container not found for Expression object" );
		}
		switch ( true ) {
		case $this->value instanceof SqlFn:
			$this->value->setContainer( $this->container );
			$this->query = $this->operator . " " . $this->value->getQuery();
			break;
		case $this->value instanceof Column:
			/**
			 * When PHP is upgraded above 5.2, the toString call
			 * should be removed as it will automatically be handled
			 * by the magic method __toString
			 */
			$this->query = $this->operator . " " . $this->value->toString();
			break;
		case is_array( $this->value ):
			if ( is_array( $this->value ) && !empty( $this->value ) ) {
				$this->query = Query::buildGroupedConditions( $this->container, $this->key, $this->value, $this->operator );
			}
			break;
		default:
			/*
			 * Some operators don't need an operand on the right hand side
			 * e.g. IS NULL. Therefore, we should always append the operator even if
			 * the value is not set.
			 */
			$query = $this->operator;
			if ( isset( $this->value ) ) {
				$namedParam = $this->container->bindValue( $this->value );
				$query .= " " . $namedParam;
			}
			if ( !empty( $this->options )  ) {
				if ( isset( $this->options['escape_like'] ) && $this->options['escape_like'] === true ) {
					switch( $this->operator ) {
					case 'LIKE':
					case 'NOT LIKE':
						$query .= ' ESCAPE "' . self::ESCAPE_CHAR . '"';
						break;
					}
				}
			}
			$this->query = $query;
			break;
		}
	}


	/*
	 * Public functions
	 */

	public function setContainer( Query $container ) {
		$this->container = $container;
	}

	public function getQuery( ) {
		$this->build();
		return $this->query;
	}

	/**
	 * Check if operator 1 is there or not
	 */
	public function isKeySet() {
		return ( $this->key !== NULL );
	}

	/*
	 * Factory
	 */

	public static function lt( $value ) {
		return new Exp( "<", NULL, $value );
	}

	public static function gt( $value ) {
		return new Exp( ">", NULL, $value );
	}

	public static function eq( $value ) {
		return new Exp( "=", NULL, $value );
	}

	public static function le( $value ) {
		return new Exp( "<=", NULL, $value );
	}

	public static function ge( $value ) {
		return new Exp( ">=", NULL, $value );
	}

	public static function ne( $value ) {
		return new Exp( "!=", NULL, $value );
	}

	public static function bwor( $value ) {
		return new Exp( "|", NULL, $value );
	}

	public static function bwand( $value ) {
		return new Exp( "&", NULL, $value );
	}

	/**
	 * Creates a LIKE condition
	 *
	 * {example}
	 	$privateDb->select()
			->from( 'csi_device_software' )
			->where( array(
				'product_name' => Exp::LIKE( 'adobe' )
			))
			->exec();
	 * {/example}
	 *
	 * @param string $value The operand of the LIKE condition
	 * @return Exp
	 */
	public static function LIKE( $value, $isEscaped = false ) {
		$options = array();
		if ( $isEscaped === true  ) {
			$options['escape_like'] = true;
		}
		return new Exp( "LIKE", null,  $value, $options );
	}

	/**
	 * Creates a NOT LIKE condition
	 *
	 * {example}
		 $privateDb->select()
			 ->from( 'csi_device_software' )
			 ->where( array(
			 	'product_name' => Exp::NOT_LIKE( 'adobe' )
			 ))
			 ->exec();
	 * {/example}
	 *
	 * @param string $value The operand of the NOT LIKE condition
	 * @return Exp
	 */
	public static function NOT_LIKE( $value, $isEscaped = false ) {
		$options = array();
		if ( $isEscaped === true  ) {
			$options['escape_like'] = true;
		}
		return new Exp( "NOT LIKE", null, $value, $options );
	}

	public static function IS_NULL() {
		return new Exp( "IS NULL" );
	}

	public static function IS_NOT_NULL() {
		return new Exp( "IS NOT NULL" );
	}

	public static function ANDs( $columnName = NULL, array $values ) {
		return new Exp( "AND", $columnName, $values );
	}

	public static function ORs( $columnName = NULL, array $values ) {
		return new Exp( "OR", $columnName, $values );
	}
}

/**
 * Provides functionality for executing
 * native SQL functions.
 */
class SqlFn {

	/**
	 * These are date units to be used when working with dates
	 */
	private static $dateUnits = array(
		'MICROSECOND'
		,'SECOND'
		,'MINUTE'
		,'HOUR'
		,'DAY'
		,'WEEK'
		,'MONTH'
		,'QUARTER'
		,'YEAR'
	);

	/*
	 * The container / query object that holds this expression and maintains the named
	 * params. It is set by the container itself. The expression can not be build if
	 * the $container is not set.
	 */
	private $container;

	private $query = '';
	private $template = '';
	private $params = array();

	/**
	 * The __constructor is private so that
	 * it can only be called by the factory methods inside the class
	 */
	private function __construct( $template, array $params = array() ) {
		$this->template = $template;
		$this->params = $params;
	}

	public function setContainer( Query $container ) {
		$this->container = $container;
	}

	private function build() {
		if ( is_null( $this->container ) ) {
			throw new SfwEx( "Container not found for Expression object" );
		}
		$query = $this->template;
		foreach ( $this->params as $key => $param ) {
			switch ( true ) {
			case $param instanceof SqlFn:
				$param->setContainer( $this->container );
				$subQuery = $param->getQuery();
				break;
			case $param instanceof Select:
				$param->setContainer( $this->container );
				$param->build();
				$subQuery = $param->getQuery();
				break;
			default:
				/**
				 * This is actually a named parameter
				 */
				$subQuery = $this->container->bindValue( $param );
				break;
			}
			$query = str_replace( "{" . $key . "}", $subQuery, $query );
		}
		$this->query = $query;
	}

	public function getQuery() {
		$this->build();
		return $this->query;
	}

	public static function DATE_SUB( $date, $expr, $unit ) {
		if ( !in_array( $unit, self::$dateUnits ) ) {
			throw new SfwEx( "Invalid date unit provided." );
		}
		return new SqlFn( "DATE_SUB( {0}, INTERVAL {1} " . $unit . " )", array( $date, $expr ) );
	}

	public static function DATE_ADD( $date, $expr, $unit ) {
		if ( !in_array( $unit, self::$dateUnits ) ) {
			throw new SfwEx( "Invalid date unit provided." );
		}
		return new SqlFn( "DATE_ADD( {0}, INTERVAL {1} " . $unit . " )", array( $date, $expr ) );
	}

	public static function NOW() {
		return new SqlFn( "NOW()" );
	}

	/**
	 * Counts the distinct occurrences of the specified colums in the table
	 *
	 * @example:
		<pre><code>
		// Get the count the distinct product_id and product_name pairs in the `csi_device_software` table

		$results = $privateDb->select()
			->columns( array(
				'count' => SqlFn::countDistinct( array(	'product_id', 'product_name' ) )
			) )
			->from( 'csi_device_software' )
			->limit( 1 )
			->setOption( Select::RETURN_PDO_STATEMENT, 1 )
			->exec();

		$count = $stmt->fetch( PDO::FETCH_COLUMN );

		$response['data'] = $this->getCars($options);
		..

		// the getCars method
		private function getCars(array $options) {
			# Build Sorters
			$sorters = $this->getSorterOptions( $options );

			# Limit/offset
			$options['limit'] = isset( $options['limit'] ) ? $options['limit'] : 25;
			$options['offset'] = isset( $options['start'] ) ? $options['start'] : 0;

			return $this->privateDb
				->select()
				->from('cars')
				->orderBy($sorters)
				->limit($options['offset'], $options['limit'])
				->exec();
		}
		</code></pre>
	 *
	 *  @param array $columns
	 *  @return SqlFn
	 */
	public static function countDistinct( array $columns ) {
		// create a dummy Select object so we can process our columns with it
		$dummySelect = new Select();
		$dummySelect->columns( $columns );
		$columns = $dummySelect->buildColumns();
		return new SqlFn( 'COUNT(DISTINCT ' . $columns . ')' );
	}

	public static function UTC_TIMESTAMP() {
		return new SqlFn( "UTC_TIMESTAMP()" );
	}

	public static function IFF( $expr1, $expr2, $expr3 ) {
		$template = "IF( {1}, {2}, {3} )";
		$params = array();
		for ( $i = 1; $i <= 3; $i++ ) {
			$varName = "expr" . $i;
			switch ( true ) {
			case $$varName instanceof Column:
				/**
				 * When PHP is upgraded above 5.2, the toString call
				 * should be removed as it will automatically be handled
				 * by the magic method __toString
				 */
				$template = str_replace( "\{" . $i . "\}", $$varName->toString(), $template );
				break;
			case
				is_string( $$varName )
				|| is_numeric( $$varName )
				|| $$varName instanceof SqlFn:
				$params[] = $$varName;
				$template = str_replace( '{' . $i . '}'
										 ,'{' . ( count( $params ) - 1 ) . '}'
										 ,$template );
				break;
			default:
				throw new SfwEx( "Invalid parameter provided to the IFF." );
				break;
			}
		}
		return new SqlFn( $template, $params );
	}

	public static function IS_NULL( $expr ) {
		switch ( true ) {
		case $expr instanceof Column:
			/**
			 * When PHP is upgraded above 5.2, the toString call
			 * should be removed as it will automatically be handled
			 * by the magic method __toString
			 */
			return new SqlFn( $expr->toString() . " IS NULL" );
			break;
		case
			is_string( $$varName )
			|| is_numeric( $$varName )
			|| $$varName instanceof SqlFn:
			return new SqlFn( "{0} IS NULL", array( $expr ) );
			break;
		default:
			throw new SfwEx( "Invalid parameter provided to the IS NULL." );
			break;
		}
	}

	public static function NOT_EXISTS( Select $innerSelect ) {
		return new SqlFn( "NOT EXISTS ({0})", array( $innerSelect ) );
	}

	public static function EXISTS( Select $innerSelect ) {
		return new SqlFn( "EXISTS ({0})", array( $innerSelect ) );
	}

	/**
	 * Converts a hexidecimal string to a binary string
	 *
	 * @code
	 * mysql> SELECT UNHEX('68656c6c6f') AS `unhexed`;
	 * +---------+
	 * | unhexed |
	 * +---------+
	 * | hello   |
	 * +---------+
	 * @endcode
	 *
	 * @param string $hexString
	 * @return SqlFn
	 */
	public static function UNHEX( $hexString ) {
		return new SqlFn( "UNHEX( {0} )", array( $hexString ) );
	}
}

/**
 * This class creates represent objects that map
 * to columns in the database
 */
class Column {

	/**
	 * The table
	 * @var string
	 */
	private $table = "";
	/**
	 * The column
	 * @var string
	 */
	private $column = "";

	/**
	 * The database
	 * @var string
	 */
	private $database = "";

	/**
	 * The __constructor is private so that
	 * it can only be called by the factory methods inside the class
	 * @param string $table  The table name if specified
	 * @param string $column The column name
	 */
	private function __construct( $database = "", $table = "", $column = "" ) {
		if ( !empty( $database ) ) {
			if ( !Query::isSafe( $database ) ) {
				throw new SfwEx( "Invalid database name provided." );
			}
			$this->database = $database;
		}
		if ( !empty( $table ) ) {
			if ( !Query::isSafe( $table ) ) {
				throw new SfwEx( "Invalid table name provided." );
			}
			$this->table = $table;
		}
		if ( empty( $column ) || !Query::isSafe( $column ) ) {
			throw new SfwEx( "Invalid column name provided." );
		}
		$this->column = $column;
	}

	/**
	 * Creates a Column instance from a fully qualified
	 * database, table and column name
	 * @param  string $database The database name
	 * @param  string $table    The table name
	 * @param  string $column   The column name
	 * @return Column           A column instance
	 */
	public static function databaseTableColumn( $database, $table, $column ) {
		return new Column( $database, $table, $column );
	}

	/**
	 * Creates a Column instance from a fully qualified
	 * table name and column name
	 * @param  string $table  The table name
	 * @param  string $column The column name
	 * @return Column         A Column instance
	 */
	public static function tableColumn( $table, $column ) {
		return new Column( "", $table, $column );
	}

	/**
	 * Creates a Column instance from a column name
	 * @param  string $column The column name
	 * @return Column         A Column instance
	 */
	public static function column( $column ) {
		return new Column( "", "", $column );
	}

	/**
	 * Magic method to be used when needing the textual
	 * representation of the Column
	 * @return string the database ready textual representation of the column
	 */
	public function __toString() {
		$columnStringRepresentation = "";
		if ( !empty( $this->table ) ) {
			$columnStringRepresentation .= "`" . $this->table . "`.";
		}
		$columnStringRepresentation .= "`" . $this->column . "`";
		return $columnStringRepresentation;
	}

	/**
	 * TODO:
	 * This function must be removed when PHP is updated above PHP 5.2
	 * All its usages must be adapated. This is just an alias
	 * @return string the return of the magic __toString
	 */
	public function toString() {
		return $this->__toString();
	}
}
