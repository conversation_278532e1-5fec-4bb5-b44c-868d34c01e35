<?php
require_once( './sfwex.class.php' );
require_once( './configuration.php' );
require_once( './db.class.php' );
require_once( './query.class.php' );

define('SCHEMA_NAME', 'software_suggestions_vt');
define('SUGGESTION_BASETABLE', 'software_suggestions_raw');
define('SUGGESTION_DEDUP', 'software_suggestions_dedup');
define('COMMENTS_DEDUP', 'software_suggestions_comment_dedup');
define('IGNORERULES_DEDUP', 'ignore_rules_dedup');

$GLOBALS['db'] = new DB( DB_HOST, 'software_suggestions_vt', DB_USER, DB_PASS, DB_UTC_TIMEZONE );

//Create a variable for start time

$time_start = microtime(true);
$query = "SELECT COUNT(*) AS totalRows FROM ".SCHEMA_NAME.".".SUGGESTION_BASETABLE." WHERE platform IN (2,3)";

$rows = $GLOBALS['db']->execRaw( $query )->fetchAll();

$recordsPerIteration = 50000 ;
$start = 0;
$totalCount = $rows[0]['totalRows']; 
print "Total numbers of rows in Software_suggestions table ".$totalCount ;
//Inserting 50000 records in each batch
if ( $totalCount > 0 ) {
	while ( $totalCount > 0 ) {
		$querySoftwareSuggestions = 'INSERT INTO '.SCHEMA_NAME.'.'.SUGGESTION_DEDUP.' (
			Suggestion_id,
		    	FileName,
		    	CompanyName,
		    	ProductName,
		    	FileDescription,
		    	LegalCopyright,
		    	ProductVersion,
				FileVersion,
				Path,
				platform,
		  		match_value,
		  		InternalName,
		  		PETimestamp,
		  		AltPETimestamp ,
		    	PEMachine,
		   		AltPEMachine ,
		  		AGVersion,
		  		Version,
		   		sr_match_file_id ,
		  		AppName,
		  		Status
		) SELECT
				Suggestion_id,
		    	FileName,
		    	CompanyName,
		    	ProductName,
		    	FileDescription,
		    	LegalCopyright,
		    	ProductVersion,
				FileVersion,
				Path,
				platform,
		  		match_value,
		  		InternalName,
		  		PETimestamp,
		  		AltPETimestamp ,
		    	PEMachine,
		   		AltPEMachine ,
		  		AGVersion,
		  		Version,
		   		sr_match_file_id ,
		  		AppName,
		  		Status
		FROM '.SCHEMA_NAME.'.'.SUGGESTION_BASETABLE.'
		WHERE platform IN (2,3)
		ORDER BY '.SUGGESTION_BASETABLE.'.suggestion_id DESC LIMIT '.$start.','.$recordsPerIteration.'
		ON DUPLICATE KEY UPDATE '.SUGGESTION_DEDUP.'.count = '.SUGGESTION_DEDUP.'.count + 1';
	
		try {
			$GLOBALS['db']->execRaw( $querySoftwareSuggestions );
		} catch (Exception $ex) {
			$response['debug'] =$ex->getMessage() ;
			$response['code'] = $ex->getCode();
			print_r ($response) ;
			exit();
		}
		$totalCount	= $totalCount- $recordsPerIteration; 
		$start 		+= $recordsPerIteration ;
		print " \n Records needs to process ".$totalCount;
	}
}


$time_end = microtime(true);
$time = $time_end - $time_start;

$query = "SELECT COUNT(*) AS totalRows FROM ".SCHEMA_NAME.".".SUGGESTION_DEDUP." ";
$rows = $GLOBALS['db']->execRaw( $query )->fetchAll();
$totalCount = $rows[0]['totalRows']; 

print " Total numbers of de-dup rows in new Software_suggestions de-dup table ".$totalCount ;

print PHP_EOL."Software_Suggestions_dedup ended in ".$time;


/*
 * Software_Suggestions_comment table dedup
 *
 */

$time_start_comments = microtime(true);

$queryDelereComments = 'DELETE FROM '.SCHEMA_NAME.'.'.COMMENTS_DEDUP.' 
						WHERE suggestion_id  NOT IN (SELECT suggestion_id FROM '.SCHEMA_NAME.'.'.SUGGESTION_DEDUP.')' ;
$GLOBALS['db']->execRaw( $queryDelereComments );
$time_end_comments= microtime(true);
$time_comments = $time_end_comments - $time_start_comments ;

print PHP_EOL."Software_Suggestions_comment_dedup ended in ".$time_comments." ".PHP_EOL;

/*
 * Ignore_rule table dedup
 *
 */

$time_start_igrules = microtime(true);
$query = "SELECT ignore_rule_id, ignore_rule_value FROM ".SCHEMA_NAME.".".IGNORERULES_DEDUP." ";
$rows = $GLOBALS['db']->execRaw( $query )->fetchAll();
foreach ($rows as $value) {

	$query = "SELECT count(*) as counts FROM ".SCHEMA_NAME.".".SUGGESTION_DEDUP." where path like '%".$value['ignore_rule_value']."%'";
	$rows = $GLOBALS['db']->execRaw( $query )->fetchAll();
	if ($rows[0]['counts'] < 1 ){
		$notUse[] = $value['ignore_rule_id'] ;
	}
}

print " Total ".count($notUse)." records marked for deletion in table Ignore_Rules" ;
$stringForDelete = implode(', ', $notUse);  
$deleteIgrules = "DELETE FROM ".SCHEMA_NAME.".".IGNORERULES_DEDUP." WHERE ignore_rule_id IN (".$stringForDelete.")";
$GLOBALS['db']->execRaw( $deleteIgrules );

///Execution times
$time_end_igrules = microtime(true);
$time_igrules = $time_end_igrules - $time_start_igrules;
print PHP_EOL."Ignore_Rules_dedup ended in ".$time_igrules;
$total_execution_tile = $time_igrules + $time_comments + $time ;
echo PHP_EOL.'Total Execution time : '.$total_execution_tile.' seconds';
?>
