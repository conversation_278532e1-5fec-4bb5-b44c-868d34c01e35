<?php
require_once( './sfwex.class.php' );
require_once( './configuration.php' );
require_once( './db.class.php' );
require_once( './query.class.php' );

define('SCHEMA_NAME', 'software_suggestions_vt');
define('SUGGESTION_BASETABLE', 'software_suggestions');
define('SUGGESTION_DEDUP', 'software_suggestions_dedup');
define('COMMENTS_DEDUP', 'software_suggestions_comment_dedup');
define('IGNORERULES_DEDUP', 'ignore_rules_dedup');

$GLOBALS['db'] = new DB( DB_HOST, 'software_suggestions_vt', DB_USER, DB_PASS, DB_UTC_TIMEZONE );

//Create a variable for start time

$time_start = microtime(true);
$query = "SELECT COUNT(*) AS totalRows FROM ".SCHEMA_NAME.".".SUGGESTION_BASETABLE." WHERE platform IN (2,3)";

$rows = $GLOBALS['db']->execRaw( $query )->fetchAll();

$recordsPerIteration = 50000 ;
$start = 0;
$totalCount = $rows[0]['totalRows']; 
$time_end = microtime(true);
$time = $time_end - $time_start;
print "Total numbers of rows in Software_suggestions table ".$totalCount ;

echo "Execution time : ".$time." seconds \n <br/>";

?>
