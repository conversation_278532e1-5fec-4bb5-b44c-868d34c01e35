<?php

class SfwEx extends Exception {

	public $prevEx;

	public function __construct( $msg = null, $code = 0, Exception $prevEx = null) {

		$this->prevEx = $prevEx;

		// In php 5.3.0 it is possible to pass in the previous exception
		// Currently, we can get message for the previous exception
		$msg = $this->generatePreviousExceptionMessage( $prevEx ) . $msg;

		parent::__construct( $msg , $code );
	}

	/*
	 * @deprecated
	 *   This function needs to die as it dumps the stack trace in the log file and the
	 *   stack track could contain sensitive information.
	 *   Maybe, we can dump the information ONLY if the error_reporting is E_ALL and
	 *   we know that on the live side, it will never be on.
	 */
	public function log() {

		$msg = "EXCEPTION :: '" . get_class( $this ).  "' # " . $this->getCode() . "\n";

		$userMessage = $this->getMessage();

		$msg .= " - MESSAGE :";
		if ( $userMessage ) {
			$msg .= "\n" . $userMessage . "\n";
		} else {
			$msg .= " NULL\n";
		}

		$msg .= " - TRACE :\n";
		$msg .= $this->getTraceAsString() . "\n";

		$msg .= "\n";

		$GLOBALS['debug']->storeActionLog( $msg );
	}

	public function generatePreviousExceptionMessage( Exception $ex = null ) {
		if ( !$ex ) {
			return "";
		}
		$msg = "Exception caught: '" . get_class( $ex ) . "' ";
		$msg .= "[ " . $ex->getMessage() . " ]\n";

		return $msg;
	}

}