<?php

  /**
   * The VIM 4 server edition is relying on the sends_register table
   * to send out the advisories. The advisory sending mechanism for
   * the VIM 4 Hosted and Server edition should be the same,
   * unfortunately, this is not the case.
   *
   * VIM 4 Hosted: relies on 'cronjob_sender'
   * VIM 4 Server: relies on 'sends_register'
   *
   * The sends_register was being updated by cronjobs running for the
   * EVM which is EOL and the cronjon is not runnning anymore. The
   * following script on does the bare essential to keep the VIM 4
   * Server edition running.
   *
   * The following has been copied from the deprecated script:
   * ..vuln_track/sender/cmdl_cronjob_sender.php
   *
   */

require __DIR__ . '/../../public_html/functions.php';

require_once( GLOBAL_CLASSES_PATH . 'json.class.php' );
require_once( GLOBAL_CLASSES_PATH . 'util.class.php' );
require_once( GLOBAL_CLASSES_PATH . 'debug.class.php' );
require_once( GLOBAL_CLASSES_PATH . 'input.class.php' );

require_once( ROOT_PATH . "global/debug.class.php" );
require_once( ROOT_PATH . "global/sfwex.class.php" );
require_once( ROOT_PATH . "global/db.class.php" );
require_once( ROOT_PATH . "global/query.class.php" );
require_once( ROOT_PATH . "global/grid.class.php" );

$GLOBALS['debug'] = new DEBUG();
$GLOBALS['util'] = new UTIL();
$GLOBALS['json'] = new JSON();


// Input params
$throttleNumber = isset( $argv[1] ) ? (int) $argv[1] : false;

if ( !$throttleNumber ) {
	echo date( 'd M Y H:i:s' ) . ' :  No throttle parameter provided.' . PHP_EOL;
	exit();
}

// Lang Ids @todo: should be global constants in the configuration file
$en = 1;
$da = 2;
$de = 5;

$vtDb = new DB( DB_HOST, 'vuln_track', DB_USER, DB_PASS, DB_UTC_TIMEZONE );

// Keep track of all the advisories
$advisories = array(
	 $en => array()
	,$da => array()
	,$de => array()
);

$rows  = $vtDb
	->execRaw( 'SELECT * FROM cronjob_sender WHERE type IN ("vts_1", "vts_2", "vts_5") ORDER BY type DESC LIMIT ' . (int) $throttleNumber )
	->fetchAll();

$langId = null;
foreach ( $rows as $row ) {
	$vulnId = (int) $row[ 'vuln_id' ];

	switch ( $row['type'] )	{
	case 'vts_1':
		$langId = $en;
		break;
	case 'vts_2':
		$langId = $da;
		break;
	case 'vts_5':
		$langId = $de;
		break;
	default:
		continue 2;
	}

	// delete from the cronjob sender
	$num = $vtDb->delete()
		->from( 'cronjob_sender' )
		->where( array( 'id' => $row['id'] ) )
		->limit( 1)
		->exec();
	// $vtDb->execRaw( "delete from cronjob_sender where id = '" . (int) $row['id'] . "' limit 1" );

	// Insert in 'sends_register'
	if ( $num === 1 ) {
		$vtDb->insert()
			->into( 'sends_register' )
			->set( array(
				'vuln_id' => $vulnId
				,'lang_id' => $langId
				,'sent' => SqlFn::NOW()
				,'email_only' => $row['email_only'] // no idea why this is required
			) )
			->exec();
		// $vtDb->execRaw( "insert into vuln_track.sends_register (vuln_id, lang_id, sent, email_only) values('" . $vulnId . "', '" . $langId . "', now(), '" . (int) $row['email_only'] . "')" );
		$advisories[ $langId ][] = $vulnId;
		echo 'Added: ' . $vulnId . ' (' . $langId . ')' . PHP_EOL;

	}


}

echo PHP_EOL;

echo date( 'd M Y H:i:s' ) . ' :  '
	. 'English: ' . count( $advisories[ $en ] ) . ', '
	. 'Danish : ' . count( $advisories[ $da ] ) . ', '
	. 'German : ' . count( $advisories[ $de ] ) . ', '
	. ' advisories added to the sends_register.';

echo PHP_EOL;