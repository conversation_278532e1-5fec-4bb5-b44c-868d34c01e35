<?php

  /*
   * Required files
   */
require( dirname( __FILE__ ) . '/detectreferences.class.php' );
$configFile = dirname( __FILE__ ) . '/configuration.php';
date_default_timezone_set('UTC');

// openlog( 'DetectReferences', LOG_PID | LOG_PERROR, LOG_CRON );

function logMsg( $msg ) {
	// syslog( LOG_INFO, $msg );
	echo date( 'r', microtime( true ) ) . ' : ' . $msg . "\n";
}

$errorCode = DetectReferences::init( $configFile );
if ( $errorCode !== DetectReferences::SUCCESS ) {
	logMsg( 'Unable to initialize class: DetectReferences. Error: ' . $errorCode );
	exit();
 }

logMsg( 'Starting process' );
$errorCode = DetectReferences::run();

if ( $errorCode !== DetectReferences::SUCCESS ) {
	logMsg( 'Error occurred in the process. Error: ' . $errorCode . '. '
			. 'Exiting !' );
	exit();
 }

logMsg( 'Succesfully Completed!' );
closelog();