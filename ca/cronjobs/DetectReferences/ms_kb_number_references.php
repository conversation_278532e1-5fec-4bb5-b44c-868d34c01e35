<?php

  /*
   * Deprecated.
   *
   * Use the detect_references.php script which is a more generated script for detecting references. Handles:
   * - MS KB Numbers
   * - Redhat References
   */

error_reporting( E_ALL );

// Functions
function logMsg( $msg ) {
	echo date( 'r', microtime( true ) ) . ' : ' . $msg . "\n";
}


// Open DB Connection
// mysql_connect('***************', 'crm', '1234');
mysql_connect('localhost', 'root', '');

mysql_select_db('vuln_track');

// Array of Microsoft Products
// $aMicrosoftOffice = array(4904,4970,7700,6997,3292,5274,3170,1092,4908,5523,6463,10986,35,4043,26,34,2223,157,30,1091,2150,36,3054,27,33,3052,158,29,2149,5275,1807,14702,14703,13799,14161,14162,6456,667,6473,6472);

logMsg( 'Fetching Data' );
// Select all advisories matching 'KB[0-9]{5,7}'
$rRes = mysql_query("SELECT * FROM vuln_track.text, vuln_track.vuln WHERE vuln_status != 4 && text_text REGEXP '[0-9]{5,7}' && text_type_id in(5,6) && vuln.vuln_id = text.vuln_id");

logMsg( 'Flushing previous entries' );
// Clear Database
mysql_query("DELETE FROM vuln_track.nsi_ms_kb_articles");

logMsg( 'Processing' );
// Loop through result
while ( $aRow = mysql_fetch_array($rRes) ) {
	// Match the KB Number
	preg_match_all('/\(?KB([0-9]{5,7}),? ?\)?/', $aRow['text_text'], $aFound);

	if ( isset( $aFound[1][0] ) && $aFound[1][0] == 955218 ) {
		$aFound[1][] = 955069;
		$aFound[1][] = 954430;
		$aFound[1][] = 954459;
		$aFound[1][] = 951550;
		$aFound[1][] = 951597;
	}

	// Loop through each found KB Number
	while ( list($iKey, $iKBNumber) = each($aFound[1]) ) {
		// Select all products affected by this advisory
		$rSoftRes = mysql_query("SELECT * FROM vuln_track.os_soft_rel WHERE vuln_id = '" . $aRow['vuln_id'] . "'");
		while ( $aSoftRow = mysql_fetch_array($rSoftRes) ) {
			// Software in "AND" relation?
			if ( $aSoftRow['os_id'] && $aSoftRow['soft_id'] ) {
				$iProductID = $aSoftRow['soft_id'];
			} else {
				$iProductID = $aSoftRow['os_id'] + $aSoftRow['soft_id'];
			}

			$key = $iProductID . '_' . $aRow['vuln_id'] . '_' . $iKBNumber;
			// Check if MS Office Product - then skip handled via File Signatures
			if ( /*in_array($iProductID, $aMicrosoftOffice) ||*/
				isset( $aDone[ $key ] ) && $aDone[ $key ] ) {
				continue;
			}
			$aDone[ $key ] = true;

			// Store in Database
			mysql_query("INSERT INTO vuln_track.nsi_ms_kb_articles (product_id, kb_article, vuln_id, added) values('" . $iProductID . "', '" . $iKBNumber . "', '" . $aRow['vuln_id'] . "', now())");
		}
	}
}

logMsg( 'Done!' );