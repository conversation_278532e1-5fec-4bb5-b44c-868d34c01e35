<?php

class DetectReferences {

	/*
	 * Error Codes
	 */
	const UNKNOWN = -1;
	const SUCCESS = 0;
	const ERR_INVALID_CONFIG = 1;

	/*
	 * Databases
	 */
	protected static $vulnTrackDb;

	/*
	 * Table name containing the references
	 * @todo: determine the correct table name
	 */
	const TABLE_REFERENCES = 'vuln_reference_external';


	/*
	 * Static constructor
	 *
	 * @todo: should use the values from the Cfg class
	 * @todo: instead of getting a file, we should get an instance, maybe?
	 */
	public static function init( $configFile = false ) {
		if ( !$configFile || !file_exists( $configFile ) ) {
			return self::ERR_INVALID_CONFIG;
		}

		/*
		 * Including the Config File
		 */
		require_once( $configFile );

		if ( !defined( 'DB_HOST_VT' )
			 || !defined( 'DB_USER_VT' )
			 || !defined( 'DB_PASS_VT' ) ) {

			return self::ERR_INVALID_CONFIG;
		}

		/*
		 * Dependencies
		 */
		require_once(CLASSES_PATH.'global/debug.class.php');
		require_once(CLASSES_PATH.'global/sfwex.class.php');
		require_once(CLASSES_PATH.'global/db.class.php');
		require_once(CLASSES_PATH.'global/query.class.php');

		/*
		 * Init Databases
		 */
		self::$vulnTrackDb = new DB( DB_HOST_VT, 'vuln_track', DB_USER_VT, DB_PASS_VT );

		/*
		 * Global Variables [Supporting old practices]
		 */
		$GLOBALS['debug'] = new DEBUG();


		return self::SUCCESS;
	}

	/*
	 * Select all advisories matching:
	 * 1. Microsoft References: 'KB[0-9]{5,7}'
	 * 2. Redhat references. @todo
	 *
	 * @return $pdo
	 */
	private static function fetchReferencesRaw() {
		$rows = self::$vulnTrackDb
			->execRaw( 'SELECT vuln_id, text_text
				FROM vuln_track.text
				JOIN vuln_track.vuln
				USING ( vuln_id )
				WHERE vuln_status != 4 -- remove confidential data
				AND ( text_text REGEXP "[0-9]{5,7}" -- detecting ms kb numbers
					OR text_text REGEXP "RHSA" )
				AND text_type_id IN ( 5, 6 ) -- Original Advisory / Other References
				-- AND vuln_id = 11633 -- for testing
			' )
			->fetchAll();
		return $rows;
	}

	/*
	 * Flush the table with the references
	 */
	private static function flushReferences() {
		self::$vulnTrackDb->execRaw( 'DELETE FROM ' . self::TABLE_REFERENCES );
	}

	/*
	 * Detecting Microsoft References i.e. KB Numbers
	 */
	private static function extractMicrosoftReferences( $text ) {

		$kbNumbers = array();

		$matches = array(); // Matches
		// Match the KB Number
		preg_match_all('/\(?(KB([0-9]{5,7})),? ?\)?/', $text, $matches );

		if ( isset( $matches[1][0] ) ) {
			if ( $matches[1][0] == 'KB955218' ) {
				$matches[1][] = 'KB955069';
				$matches[1][] = 'KB954430';
				$matches[1][] = 'KB954459';
				$matches[1][] = 'KB951550';
				$matches[1][] = 'KB951597';
			}
			$kbNumbers = $matches[1];
		}

		return $kbNumbers;
	}

	/*
	 * Detecting Redhat References
	 */
	private static function extractRedhatReferences( $text ) {

		/*
		 * Match the following:
		 *
		 * RHSA-2012-1234
		 *
		 * The following is not detected:
		 * RHSA-2012:1234-1, because then we end up with two entries of the same thing one with the '-1'
		 * appended and the other without it (because the rhsa id repeats in the website link)
		 *
		 */
		preg_match_all('/(?P<reference>RHSA-[0-9]{4}[-:][0-9]{4}' . /*'(-[0-9]*)?' .*/ ')/', $text, $matches );
		$rhAdvisories = array();
		if ( !empty( $matches['reference'] ) ) {
			$rhAdvisories = $matches['reference'];
			/*
			 * Making a standard format of the Red Had Advisories
			 * i.e.
			 * RHSA-2012-1234 is converted to RHSA-2012:1234
			 */
			foreach ( $rhAdvisories as &$rhAdvisory ) {
				$position = strpos( $rhAdvisory, '-', 9);
				if ( $position !== false ) {
					$rhAdvisory[ $position ] = ':';
				}
			}
			$rhAdvisories = array_unique( $rhAdvisories );
		}
		return $rhAdvisories;
	}

	private static function fetchAffectedProducts( $vulnId ) {
		$rows = self::$vulnTrackDb->select()
			->columns( array( 'os_id', 'soft_id' ) )
			->from( 'os_soft_rel' )
			->where( array( 'vuln_id' => $vulnId ) )
			->exec();
		return $rows;
	}

	/*
	 * Storing References:
	 * - Microsoft References i.e. KB Numbers
	 */
	private static function storeReference( $productId, $reference, $vulnId ) {

		self::$vulnTrackDb->insert()
			->into( self::TABLE_REFERENCES )
			->set( array( 'product_id' => $productId
						  ,'reference' => $reference
						  ,'vuln_id' => $vulnId
						  ,'added' => SqlFn::NOW() ) )
			->exec();

	}


	public static function run() {

		self::flushReferences();
		$rows = self::fetchReferencesRaw();

		$stored = array(); // Stored referrences

		foreach ( $rows as $row ) {

			$vulnId = $row[ 'vuln_id' ];
			$vulnText = $row[ 'text_text' ];

			// Microsoft References
			$references = self::extractMicrosoftReferences( $vulnText );

			// Redhat References
			if ( empty( $references ) ) {
				$references = self::extractRedhatReferences( $vulnText );
			}

			/*
			 * For every reference found, find the affected products and store the references in the database.
			 */
			foreach ( $references as $reference ) {

				/*
				 * @todo:
				 * Performance Note:
				 * This function call should go outside the loop, just before entering it.
				 */
				$productRows = self::fetchAffectedProducts( $vulnId );

				/*
				 * Store the reference for each affected product found.
				 */
				foreach ( $productRows as $productRow ) {

					$productId = false;

					// The product can either be a OS / Software
					// Logic copied from the old ms_kb_number_references.php script
					if ( $productRow['os_id'] && $productRow['soft_id'] ) {
						$productId = $productRow['soft_id'];
					} else {
						$productId = $productRow['os_id'] + $productRow['soft_id'];
					}

					$key = $productId . '_' . $reference . '_' . $vulnId;

					if ( isset( $stored[ $key ] ) && $stored[ $key ] ) {
						continue;
					}

					self::storeReference( $productId, $reference, $vulnId );

					$stored[ $key ] = true;
				}
			}
			unset( $row );
		}

		return self::SUCCESS;
	}

 }