<?php

  /**
   * Due to VT-301 'Published advisories not showing up in the
   * pipeline', we missed some advisories that were supposed to be
   * translated. This scripts determines the missed advisories and
   * re-populates the translation pipeline for the missed advisories.
   */


require __DIR__ . '/../../public_html/functions.php';

require_once( GLOBAL_CLASSES_PATH . 'json.class.php' );
require_once( GLOBAL_CLASSES_PATH . 'util.class.php' );
require_once( G<PERSON><PERSON><PERSON><PERSON>_CLASSES_PATH . 'debug.class.php' );
require_once( GLOBAL_CLASSES_PATH . 'input.class.php' );

require_once( ROOT_PATH . "global/debug.class.php" );
require_once( ROOT_PATH . "global/sfwex.class.php" );
require_once( ROOT_PATH . "global/db.class.php" );
require_once( ROOT_PATH . "global/query.class.php" );
require_once( ROOT_PATH . "global/grid.class.php" );

$GLOBALS['debug'] = new DEBUG();
$GLOBALS['util'] = new UTIL();
$GLOBALS['json'] = new JSON();

$vtDb = new DB( DB_HOST, 'vuln_track', DB_USER, DB_PASS, DB_UTC_TIMEZONE );
$caDb = new DB( DB_HOST_CA, 'ca', DB_USER_CA, DB_PASS_CA, DB_UTC_TIMEZONE );

$start = '2014-09-27'; // Date of the VT db server move
$end = '2014-10-15 08:24:00'; // Date and time when the fix for VT-301 was pushed live

// Get all the advisories that were missed i.e. released/updated
// during the outage an are not present in the vuln_translation already
$rows  = $vtDb
	->execRaw(
		'SELECT vuln_id
		FROM vuln
		LEFT JOIN vuln_translation AS T
		USING ( vuln_id )
		WHERE vuln_modified_date >= :start AND vuln_modified_date < :end
		AND T.vuln_id IS NULL'
		,array(
			':start' => $start
			,':end' => $end
		)
	)
	->fetchAll();

// Convert to array of vuln_ids
$vulnIds = array();
foreach ( $rows as $row ) {
	$vulnIds[] = (int) $row[ 'vuln_id' ];
}

// Get the `os_id` and `soft_id`s related to the vulnerabilities
// We need these to filter out the advisories with products that are
// being tracked by customers
$rows = $vtDb->select()
	->columns( array( 'vuln_id', 'soft_id', 'os_id' ) )
	->from( 'os_soft_rel') // we only care about the live data and not the confidential data
	->where( array(
		'vuln_id' => $vulnIds
	) )
	->exec();

// Index the $rows so that the vuln_id can be fetched from the product_id
$indexedVulns = array();
foreach( $rows as $row ) {
	if ( $row[ 'os_id' ] ) {
		$indexedVulns[ (int) $row[ 'os_id' ] ] = $row[ 'vuln_id' ];
	}
	if ( $row[ 'soft_id' ] ) {
		$indexedVulns[ (int) $row[ 'soft_id' ] ] = $row[ 'vuln_id' ];
	}

}

$relevantVulnIds = array();

// Filter the `vuln_id`s : only keep the vulns that need to be
// translated by checking if the associated products are being tracked
// by the customers
$query =
	"SELECT DISTINCT os_soft_id
	FROM ca.vi_assets
	  ,ca.vi_asset_products
	  ,ca.accounts
	WHERE
	  accounts.account_expires >= now()
	  && accounts.account_id = vi_assets.account_id
	  && vi_assets.asset_id = vi_asset_products.asset_id
	  && vi_asset_products.os_soft_id IN (" . implode( ',', array_keys( $indexedVulns )  ) . ")";

$rows = $caDb
	->execRaw( $query )
	->fetchAll();

foreach ( $rows as $row ) {
	$vulnId = $indexedVulns[ $row[ 'os_soft_id' ]  ]; // Find the relevant vuln_id from the $indexedVulns
	$relevantVulnIds[ $vulnId ] = $vulnId;  // Use the vuln_id as the index to keep unique values
}


// Insert all the relevant vuln_ids into the pipeline
$count = 0;
$query = 'INSERT INTO vuln_translation (added, vuln_id, lang_id) VALUES (now(), :vuln_id, 5)';
foreach( $relevantVulnIds as $vulnId ) {
	echo 'Added : ' . $vulnId . PHP_EOL;
	// $vtDb->execRaw( $query, array( ':vuln_id' => $vulnId ) );
	$count++;
}

echo PHP_EOL;
echo date( 'd M Y H:i:s' ) . ' :  Added ' . $count . ' advisories to the tranlation pipeline.';
echo PHP_EOL;
