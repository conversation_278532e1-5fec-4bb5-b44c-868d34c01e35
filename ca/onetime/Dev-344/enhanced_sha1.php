<?php

/**
 * DEV-344 missing sha1 hashes for enhanced packages
 * get all the enhanced packaged from vuln track database, calculate the sha1sum for all the files and put the hash in database
 * Database tables that are effected by the script are 'solution' and 'version_rule_solution_map' in vuln_track database
 */

// In the script he sha1sums can be obtained by two methods
// 1) download each and every enhanced package and calculate the sha1sum for it ( this approach though gives latest result
//    from server but is slow and takes alot of time )
// 2) Another approach (default) is to get a file containing package names and sha1sums from devops (devops-1690)for them its quicker
//    but they have to get the latest entries every time the script needs to run. The file is named sha1sums and is placed
//    in the same folder as the script is.

// The script can be run in a debug/test mode ( default ) where all the database inserts and errors are printed on the console and 
// in a log file named log.txt. The log file shows the packages that are missing sha1sums and that needs to be resolved as this is not
// the expected behaviour.

$start_time = microtime(true);

$myFile = fopen('log.txt','w') or die('Unable to open log file');

require __DIR__ . '/../../public_html/functions.php';

require_once( GLOBAL_CLASSES_PATH . 'debug.class.php' );

require_once( ROOT_PATH . "global/debug.class.php" );
require_once( ROOT_PATH . "global/sfwex.class.php" );
require_once( ROOT_PATH . "global/db.class.php" );
require_once( ROOT_PATH . "global/query.class.php" );

$GLOBALS['debug'] = new DEBUG();

$vtDb = new DB( DB_HOST, 'vuln_track', DB_USER, DB_PASS, DB_UTC_TIMEZONE );

$tempFile = 'SecuniaPackage.exe';

//Step 1: Get all the version rules that have optional parameters ( these are basically all the enhanced packages )
$enhanced_packages  = $vtDb
    ->execRaw(
        'SELECT id, direct_download, direct_download_64, silent_install_parameters
        FROM vuln_track.sr_product_secure
        WHERE id IN ( SELECT version_rule_id
                  FROM vuln_track.version_rule_parameter_group_map
                ) AND vuln_track.sr_product_secure.truly_direct_download = 1 AND regex not like "%###%" '//regex ### means disabled rule that we don't care about anymore
    )
    ->fetchAll();

var_dump( 'total number of enhanced packages are ' . count($enhanced_packages));
log_processing($myFile, 'total number of enhanced packages are ' . count($enhanced_packages) );

$package_links = array();

//Step 2: Get the download link including any language download links
foreach ( $enhanced_packages as $package ) {

    //language download links
    $links  = $vtDb
        ->execRaw(
            'SELECT version_rule_id, url, "" as sha1, arch, iso_code, "' . $package['silent_install_parameters'] . '" as special_silent_install_parameters
            FROM vuln_track.sr_solution_download
            WHERE version_rule_id = ' . $package['id']
        )
        ->fetchAll();

    foreach( $links as $link ) {
        array_push($package_links, $link );
    }

    // create an entry in array for generic/regular version rule entry, both 32 and 64 bit
    array_push($package_links, array ( 'version_rule_id' => $package['id']
                                ,'url' => $package['direct_download']
                                ,'sha1' => ''
                                ,'arch' => 1
                                ,'iso_code' => ''
                                ,'special_silent_install_parameters' => $package['silent_install_parameters']
    ));
    array_push($package_links, array ( 'version_rule_id' => $package['id']
                                ,'url' => $package['direct_download_64']
                                ,'sha1' => ''
                                ,'arch' => 2
                                ,'iso_code' => ''
                                ,'special_silent_install_parameters' => $package['silent_install_parameters']
    ));
}

$package_and_sha1sum = array();

//Step 3 ( methods 1, currently deactivated ): Download the enhanced packages and calculate the sha1sum for each
if (0) {
    //https://csi7.secunia.com/csi/api/?uid=Y8hEvEn6mqkm6Ig0G2OXSCJDDhul2lkuvhzoBFBa62CrVyLmc0NrLMUM3MrXYXw9&action=sps_package&which=token_for_download
    $uid = 'Y8hEvEn6mqkm6Ig0G2OXSCJDDhul2lkuvhzoBFBa62CrVyLmc0NrLMUM3MrXYXw9';
    //$token = 'TZEvlHtL3vZ39ydauN1hDx8RtWydhhlj3LNSHdR6FE5hzKaxgmG5NkBMqDguJbmO';

foreach ( $package_links as $package_entry ) {

    $token = 'https://csi7.secunia.com/csi/api/?uid=' . $uid . '&action=sps_package&which=token_for_download';
    $package_url = $package_entry['url'];

    // extract package name from url and create csi7 enhanced package url
    $parts = explode('/',$package_url,5);
    $package_name = $parts[count($parts)-1];
    $enhanced_package_name = str_replace( ".exe", "-enhanced.exe", $package_name );
    $download_url = 'https://dl.csi7.secunia.com/?action=download&package=' . $package_name . '&token=' . $token;

    // download and calculate sha1sum only if it has not already been calculated
    // there are many duplicates across various version rules
    if ( !array_key_exists ($enhanced_package_name, $package_and_sha1sum ) ) {
        // download file
       download_remote( $download_url, $tempFile );
        // check for existence of file
        if ( !file_exists( $tempFile ) || filesize($tempFile) == 0 ) {
           $errorMsg = ' *** Download of package failed ( ' . $package_url . ' ) for version rule id ' . $package_entry['version_rule_id'];
           var_dump( $errorMsg );
           log_processing($myFile, $errorMsg );
           continue;
        }

        // get sha1sum for the downloaded file
        $sha1sum = sha1_file( $tempFile );
        unlink( $tempFile );

        $package_and_sha1sum[$enhanced_package_name] = $sha1sum;
    }
}
}

// step 3 ( method 2 ): read the sha1sums from the file provided by devops
$file_name = 'sha1sums';
$data = fopen($file_name, 'r');

while( !feof($data) ) {
    $line = fgets($data);
    $d = explode(' ',$line);
    $package_name = str_replace(array("\n","\r"," "),'',$d[0]);
    $package_sha1sum = str_replace(array("\n","\r"," "),'',$d[1]);

    $package_and_sha1sum[$package_name] = $package_sha1sum;
}
fclose($data);

$missing = array();

//Step 4: Insert the entries in database
foreach ( $package_links as $package_entry ) {

    //https://dl.csi7.secunia.com/?action=download&package=AdobeReader_10.1.16_cs-CZ_SPS-enhanced.exe&token=TZEvlHtL3vZ39ydauN1hDx8RtWydhhlj3LNSHdR6FE5hzKaxgmG5NkBMqDguJbmO
    $package_url = $package_entry['url'];
    // extract package name from url and create csi7 enhanced package url
    $parts = explode('/',$package_url,5);
    $package_name = $parts[count($parts)-1];
    $enhanced_package_name = str_replace( ".exe", "-enhanced.exe", $package_name );


    if ( !array_key_exists ($enhanced_package_name, $package_and_sha1sum ) ) {
        // missing sha1sum for the enhanced package ($enhanced_package_name )
        if ( !array_key_exists ($enhanced_package_name, $missing ) || ( array_key_exists ($enhanced_package_name, $missing ) && $missing[$enhanced_package_name] != $package_entry['version_rule_id'] ) ) {
            $missing[$enhanced_package_name] = $missing[$enhanced_package_name] . ',' . $package_entry['version_rule_id'];
        }
        continue; //skip the insertion if sha1sum doesn't exist
    }

    // check if the entry already exists in the database then reuse that entry
    $query = 'SELECT * FROM vuln_track.solution WHERE type = 2 AND url = "' . $package_url . '" AND sha1 = "' . $package_and_sha1sum[$enhanced_package_name] . '" AND arch = ' . $package_entry['arch'] . ' AND iso_code = "' . $package_entry['iso_code'] . '" AND silent_param = "' . $package_entry['special_silent_install_parameters'] . '"';
    $exists = $vtDb->execRaw( $query )->fetchAll();
    if ( !count($exists) ) {
        // write the enhanced solution to tables 'solution' and 'version_solution_map'
        $query = 'INSERT INTO vuln_track.solution ( type, url, sha1, arch, iso_code, silent_param )
                    VALUES ( 2, "' . $package_url . '","' . $package_and_sha1sum[$enhanced_package_name] . '",' . $package_entry['arch'] . ',"' . $package_entry['iso_code'] . '","' . $package_entry['special_silent_install_parameters'] . '")';
        //$vtDb->execRaw( $query );
        var_dump( $query );
        log_processing($myFile, $query );
        $solution_id = $vtDb->lastInsertId();
    }
    else {
        var_dump($exists[0]['solution_id']);
        $solution_id =  $exists[0]['solution_id'];
    }
    $query = 'INSERT INTO vuln_track.version_rule_solution_map ( version_rule_id, solution_id )
            VALUES ( ' . $package_entry['version_rule_id'] . ',' . $solution_id . ' )';
    //$vtDb->execRaw( $query );
    var_dump( $query );
    log_processing($myFile, $query );

}

log_processing($myFile, '*********** ERROR - missing packages ..............');
reset($missing);
while( list($key, $val) = each($missing) ) {
    log_processing($myFile, $key . '=>' . $val );
}

$end_time = microtime(true);

var_dump( 'Total execution time in minutes ' . ($end_time - $start_time)/60 );
log_processing($myFile, 'Total execution time in minutes ' . ($end_time - $start_time)/60 );

fclose($myFile);

function log_processing($file, $str){
    fwrite( $file, $str );
    fwrite( $file, "\n" );
}

// function for downloading a file from given $url and downloading it at $save_path
function download_remote($url , $save_path){
    $content = file_get_contents($url);
    file_put_contents($save_path,$content);
}

