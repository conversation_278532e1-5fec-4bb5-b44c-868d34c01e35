<?php

/**
 * VT-490
 * Script to generate english and german version of the already translated Secunia advisories
 */

// configurable input
// 1) sender_email ( email address to which the advisories are to be sent )
// 2) start_date - end_date ( interval to select all the advisories created between that time period )


/* processing
 The script takes into consideration all the released advisories that were translated into german language
 during a certain time period specified by start_date and end_date in input. It creates a two text files containing
 advisory information in english and german. The text files are named uniquely after the advisory id and are emailed
 to the email address provided as input.
*/

// output
//  An email is sent to sender_email with a zipped archive containing all the advisories

require __DIR__ . '/../../public_html/functions.php';

require_once( GLOBAL_CLASSES_PATH . 'debug.class.php' );

require_once( ROOT_PATH . "global/debug.class.php" );
require_once( ROOT_PATH . "global/sfwex.class.php" );
require_once( ROOT_PATH . "global/db.class.php" );
require_once( ROOT_PATH . "global/query.class.php" );

$GLOBALS['debug'] = new DEBUG();
$vtDb = new DB( DB_HOST, 'vuln_track', DB_USER, DB_PASS, DB_UTC_TIMEZONE );

if ( $argc !== 4 ){
    echo "wrong number of arguments. kindly provide with a valid recipient email address, start date and end date to get advisories within that interval";
    die();
}

mkdir("files");

$SENDER_EMAIL = $argv[1];//"<EMAIL>";
$START_DATE = $argv[2];//'2014-10-01';
$END_DATE = $argv[3];//'2014-11-01';

$ENGLISH = 1;
$GERMAN = 5;

// Get all the advisories created within START_DATE and END_DATE
$advisories  = $vtDb
    ->execRaw(
        " SELECT vuln_id,vuln.vuln_title as title_eng, vuln_reference.vuln_title as title_de
        FROM vuln
        JOIN vuln_reference
        WHERE vuln_id = m_vuln_id AND vuln_create_date BETWEEN '" . $START_DATE . "' AND '" . $END_DATE . "'
        "
    )
    ->fetchAll();

foreach( $advisories as $advisory ) {

    getAndWriteAdvisory( $vtDb, $advisory, $ENGLISH, "_EN.txt", $advisory['title_eng'] );
    getAndWriteAdvisory( $vtDb, $advisory, $GERMAN, "_DE.txt", $advisory['title_de'] );
}

// zip all the files
$zip = new ZipArchive();
if ( $zip->open("zipfiles.zip", ZIPARCHIVE::CREATE) !== true ) {
    echo "error creating zip";
}
foreach( glob("files/*") as $file ) {
    $zip->addFile( $file );
}
$zip->close();

// send the zipped file as email

$subject = 'Files for the preparation of translation memory';
$message = '<p>Kindly find attached with the email files containing advisory information in both english and german.</p>

    <p>Kind regards,<br />
    Flexera Software</p>';

sendAttachmentAsEmail( "zipfiles.zip", $SENDER_EMAIL, $subject, $message );

function getAndWriteAdvisory( $vtDb, $advisory, $language, $fileExtension, $advisoryTitle ) {

    //collect the information of an advisory
    $advisoryDetails  = $vtDb
        ->execRaw(
            " SELECT text_type_name, text_type_value, text_text
            FROM text
            JOIN text_type
            USING (text_type_id)
            WHERE vuln_id = "  . $advisory['vuln_id']  . " AND lang_id = " . $language . " AND text_type_value BETWEEN 1 AND 4
                ORDER BY text_type_value ASC"
        )
        ->fetchAll();

    // write the obtained information in file
    $fileName = 'SA' . $advisory['vuln_id'] . $fileExtension;
    $file = fopen("files/" . $fileName,'w');
    fwrite( $file, "TITLE:\r\n" );
    fwrite( $file, $advisoryTitle . "\r\n" );
    fwrite( $file, "DESCRIPTION:\r\n" );
    fwrite( $file, $advisoryDetails[0]['text_text'] . "\r\n" );
    fwrite( $file, "SOLUTION:\r\n" );
    fwrite( $file, $advisoryDetails[1]['text_text'] . "\r\n" );
    fwrite( $file, "CREDITS:\r\n" );
    array_key_exists( 2, $advisoryDetails) && $advisoryDetails[2]['text_type_value'] == 3? fwrite( $file, $advisoryDetails[2]['text_text'] . "\r\n" ):fwrite( $file, "" . "\r\n" );
    fwrite( $file, "CHANGELOG:\r\n" );
    if ( array_key_exists( 3, $advisoryDetails) ) {
        $advisoryDetails[3]['text_type_value'] == 4?fwrite( $file, $advisoryDetails[3]['text_text'] . "\r\n" ):'';
    }
    else{
        $advisoryDetails[2]['text_type_value'] == 4? fwrite( $file, $advisoryDetails[2]['text_text'] . "\r\n" ):fwrite( $file, "" . "\r\n" );
    }
    fclose($file);
}