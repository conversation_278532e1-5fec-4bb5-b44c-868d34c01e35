name: Create Global ECR repository

on:
  workflow_dispatch:
    inputs:
      repo_name:
        description: 'Name of the Repository'
        required: true
        default: 'update_cvss4_threat_metrics'

permissions: 
  contents: read 
  actions: read
  
jobs:
  create_ecr_repo:
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.GLOBAL_FLEXERA_ECR_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.GLOBAL_FLEXERA_ECR_AWS_SECRET_ACCESS_KEY }}
          aws-region: 'us-east-1'

      - name: Create ECR Repository
        run: |
          aws ecr create-repository --image-scanning-configuration scanOnPush=true --repository-name ${{ github.event.inputs.repo_name }}