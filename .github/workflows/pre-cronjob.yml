name: <PERSON>ron job preparation
on:
  workflow_call:
    outputs:
      workdir:
        value: ${{ jobs.prep.outputs.workdir }}

env:
  TMP_DIR: /var/tmp

permissions: 
  contents: read 
  actions: read
  
jobs:
  prep:
    name: Prepare
    runs-on: [self-hosted, linux, 'infra_id:vt']
    outputs:
      workdir: ${{ steps.workdir.outputs.workdir }}
    steps:
    - name: Working directory
      id: workdir
      run: |
        workdir=$TMP_DIR/$$/$(date +%s)
        echo "workdir=$workdir" >> $GITHUB_OUTPUT
        for sub in log persist; do
            mkdir -vp $workdir/$sub
        done
