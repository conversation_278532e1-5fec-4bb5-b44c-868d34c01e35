name: VT download_cvelistv5
on:
  #schedule:
  #  - cron: '40 2 * * *'
  workflow_dispatch:

env:
  AWS_DEFAULT_REGION: eu-west-1
  ECR_AWS_ACCOUNT: ************
  ECR_AWS_DEFAULT_REGION: us-east-1
  PERSISTENT_BUCKET: s3://vtcron-persistent

permissions: 
  contents: read 
  actions: read 
  
jobs:
  pre-cronjob:
    uses: ./.github/workflows/pre-cronjob.yml

  main:
    name: "download_cvelistv5"
    needs: [pre-cronjob]
    runs-on: [self-hosted, linux, 'infra_id:vt']
    defaults:
      run:
        working-directory: ${{ needs.pre-cronjob.outputs.workdir }}

    steps:
      - name: Git Checkout
        uses: actions/checkout@v4

      # Configure AWS credentials for Flexera ECR
      - name: Configure AWS Credentials for Flexera ECR
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.GLOBAL_FLEXERA_ECR_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.GLOBAL_FLEXERA_ECR_AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      # Login to Amazon ECR
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
  
      - name: Run
        run: |
          ECR_ENDPOINT="$ECR_AWS_ACCOUNT.dkr.ecr.$ECR_AWS_DEFAULT_REGION.amazonaws.com"
          docker run --rm \
          -v "$(pwd)/log:/job/log" \
          -v "$(pwd)/persist:/job/persist" \
          --log-driver none -a stderr -a stdout \
          $ECR_ENDPOINT/download_cvelistv5 \
          > log/stdout 2> log/stderr
          
          echo "Listing all files in the persist directory:"
          ls -l persist
      
      - name: Ensure Persist Directory Exists
        run: |
          mkdir -p persist

      - name: Verify cves.zip in Persist Directory
        run: |
          if [ ! -f persist/cves.zip ]; then
            echo "Error: persist/cves.zip not found!" >&2
            exit 1
          fi

      - name: Upload cves.zip to S3 Bucket
        run: |
          aws s3 cp --no-progress persist/cves.zip "${PERSISTENT_BUCKET}/cves.zip"

  post-cronjob:
    name: Post processing
    if: always()
    needs:
      - pre-cronjob
      - main
    uses: ./.github/workflows/post-cronjob.yml
    with:
      workdir: ${{ needs.pre-cronjob.outputs.workdir }}
      jobname: download_cvelistv5
