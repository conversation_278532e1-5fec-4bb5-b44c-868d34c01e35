#ca user
## This cronjob updates the table that links OS patches from external references (for MS and RedHat) and vulnerabilities.
#0 6 * * *  /bin/php /home/<USER>/vuln_track_cron/cronjobs/DetectReferences/run.php >> /home/<USER>/cron_logs/DetectReferences.log 2>> /home/<USER>/cron_logs/DetectReferences.err

name: VT DetectReferences/run.php
on:
  schedule:
    - cron: '5 6 * * *'
  workflow_dispatch:

env:
  AWS_DEFAULT_REGION: eu-west-1

permissions: 
  contents: read 
  actions: read
  
jobs:
  ecr-setup:
    uses: ./.github/workflows/ecr-setup.yml
  pre-cronjob:
    uses: ./.github/workflows/pre-cronjob.yml

  main:
    name: "detect_references"
    needs: [ecr-setup, pre-cronjob]
    runs-on: [self-hosted, linux, 'infra_id:vt']
    defaults:
      run:
        working-directory: ${{ needs.pre-cronjob.outputs.workdir }}
    steps:
      - name: Run
        run: |
          secret_json=$(aws --region $AWS_DEFAULT_REGION secretsmanager get-secret-value --output text --query SecretString --secret-id 'prod/prod-vt/vuln_track_cron')
          docker run --rm \
          -v "$(pwd)/log:/job/log" \
          -e DB_USER_VT="$(echo $secret_json | jq -r .username)" \
          -e DB_PASS_VT="$(echo $secret_json | jq -r .password)" \
          -e DB_HOST_VT="$(echo $secret_json | jq -r .host)" \
          --log-driver none -a stderr -a stdout \
          ${{ needs.ecr-setup.outputs.ecr_endpoint }}/detect_references \
          > log/stdout 2> log/stderr

  post-cronjob:
    name: Post processing
    if: always()
    needs:
      - pre-cronjob
      - main
    uses: ./.github/workflows/post-cronjob.yml
    with:
      workdir: ${{ needs.pre-cronjob.outputs.workdir }}
      jobname: detect_references

  mail:
    name: Send mail
    if: always()
    needs: main
    uses: flexera/vt/.github/workflows/sendmail.yml@vtcronjobs
    with:
        subject: "Workflow ${{ github.workflow }} result."
        body: |
            repository: ${{ github.repository }}
            workflow: ${{ github.workflow }}
            result: ${{ needs.main.result }}
            See logs: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}
        mail_to: <EMAIL>, <EMAIL>
