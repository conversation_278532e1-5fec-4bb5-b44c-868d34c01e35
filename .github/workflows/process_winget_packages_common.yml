name: Process Winget Packages - Reusable Workflow

on:
  workflow_call:
    inputs:
      job_type:
        description: 'Job type (delta, bulk)'
        required: true
        type: string
      
      time_period:
        description: 'Time period for delta jobs (e.g., 4h, 24h)'
        required: false
        type: string
        default: '4h'
        
      dry_run:
        description: 'Run in dry-run mode'
        required: false
        type: boolean
        default: true
        
      max_parallel_folders:
        description: 'Maximum parallel folders to process'
        required: false
        type: string
        default: '2'
        
      log_level:
        description: 'Log level'
        required: false
        type: string
        default: 'INFO'
        
      no_git_release_date:
        description: 'Skip git commit date lookups for release dates'
        required: false
        type: boolean
        default: false
        
      immediate_commit:
        description: 'Commit database transaction immediately after each operation'
        required: false
        type: boolean
        default: false

env:
  AWS_DEFAULT_REGION: eu-west-1
  ECR_AWS_ACCOUNT: ************
  ECR_AWS_DEFAULT_REGION: us-east-1

permissions: 
  contents: read 
  actions: read
    
jobs:
  pre-cronjob:
    uses: ./.github/workflows/pre-cronjob.yml

  process-winget:
    name: "process_winget_packages_enhanced_${{ inputs.job_type }}"
    needs: [pre-cronjob]
    runs-on: [self-hosted, linux, 'infra_id:vt']
    defaults:
      run:
        working-directory: ${{ needs.pre-cronjob.outputs.workdir }}
  
    steps:
      - name: Git Checkout
        uses: actions/checkout@v4

      # Configure AWS credentials for Flexera ECR
      - name: Configure AWS Credentials for Flexera ECR
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.GLOBAL_FLEXERA_ECR_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.GLOBAL_FLEXERA_ECR_AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      # Login to Amazon ECR
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2      
        
      # Setup winget-pkgs repository
      - name: Setup winget-pkgs repository
        run: |
          # Create a persistent directory for winget-pkgs if it doesn't exist
          PERSISTENT_DIR="/tmp/winget-pkgs-cache"
          
          if [ ! -d "$PERSISTENT_DIR" ]; then
            echo "Creating persistent directory for winget-pkgs cache..."
            mkdir -p "$PERSISTENT_DIR"
          fi
          
          # Clone or update winget-pkgs repository in persistent location
          if [ ! -d "$PERSISTENT_DIR/.git" ]; then
            echo "Cloning winget-pkgs repository..."
            git clone --depth 1 https://github.com/microsoft/winget-pkgs.git "$PERSISTENT_DIR"
          else
            echo "Updating existing winget-pkgs repository..."
            cd "$PERSISTENT_DIR"
            # For delta jobs, we need the full history, so fetch more
            if [ "${{ inputs.job_type }}" = "delta" ]; then
              echo "Delta job detected - fetching recent history..."
              git fetch --depth 50 origin master
            else
              echo "Bulk job detected - using shallow update..."
              git fetch --depth 1 origin master
            fi
            git reset --hard origin/master
            # Clean any untracked files
            git clean -fd
            cd -
          fi
          
          # Create a symlink in the working directory
          ln -sf "$PERSISTENT_DIR" winget-pkgs
          
          # Verify the repository structure
          if [ ! -d "winget-pkgs/manifests" ]; then
            echo "Error: winget-pkgs/manifests directory not found!"
            exit 1
          fi
          
          echo "winget-pkgs repository ready. Manifest count:"
          find winget-pkgs/manifests -name "*.yaml" -o -name "*.yml" | wc -l
          
          echo "Repository last updated:"
          cd winget-pkgs && git log -1 --format="%cd %s" --date=iso
          
          # For delta jobs, show recent commit history
          if [ "${{ inputs.job_type }}" = "delta" ]; then
            echo "Recent commits (for delta processing):"
            git log --oneline -10
          fi
      
      - name: Build Docker command arguments
        id: docker-args
        run: |
          # Base arguments
          DOCKER_ARGS="--log-level ${{ inputs.log_level }}"
          
          # Add dry-run flag if enabled
          if [ "${{ inputs.dry_run }}" = "true" ]; then
            DOCKER_ARGS="$DOCKER_ARGS --dry-run"
          fi
          
          # Job-specific arguments
          if [ "${{ inputs.job_type }}" = "delta" ]; then
            # Delta job arguments
            DOCKER_ARGS="$DOCKER_ARGS --delta ${{ inputs.time_period }}"
          elif [ "${{ inputs.job_type }}" = "bulk" ]; then
            # Bulk job arguments
            DOCKER_ARGS="$DOCKER_ARGS --max-parallel-folders ${{ inputs.max_parallel_folders }}"
            
            if [ "${{ inputs.no_git_release_date }}" = "true" ]; then
              DOCKER_ARGS="$DOCKER_ARGS --no-git-release-date"
            fi
            
            if [ "${{ inputs.immediate_commit }}" = "true" ]; then
              DOCKER_ARGS="$DOCKER_ARGS --immediate-commit"
            fi
          fi
          
          echo "docker_args=$DOCKER_ARGS" >> $GITHUB_OUTPUT
          echo "Docker arguments: $DOCKER_ARGS"
      
      - name: Run process_winget_packages_enhanced (${{ inputs.job_type }})
        run: |
          ECR_ENDPOINT="$ECR_AWS_ACCOUNT.dkr.ecr.$ECR_AWS_DEFAULT_REGION.amazonaws.com"
          
          # Ensure the winget-pkgs directory has proper permissions
          sudo chown -R $(id -u):$(id -g) winget-pkgs 2>/dev/null || true
          
          echo "Starting ${{ inputs.job_type }} job with arguments: ${{ steps.docker-args.outputs.docker_args }}"
          
          docker run --rm \
          -v "$(pwd)/log:/job/log" \
          -v "$(pwd)/persist:/job/persist" \
          -v "$(pwd)/winget-pkgs:/job/winget-pkgs" \
          -e "GIT_TERMINAL_PROMPT=0" \
          --log-driver none -a stderr -a stdout \
          $ECR_ENDPOINT/process_winget_packages_enhanced ${{ steps.docker-args.outputs.docker_args }} \
          > log/stdout 2> log/stderr
          
          exit_code=$?
          echo "Docker container execution completed. Exit code: $exit_code"
          
          # Show log summary
          echo "=== STDOUT Summary ==="
          if [ -f log/stdout ]; then
            tail -20 log/stdout
          else
            echo "No stdout log found"
          fi
          
          echo "=== STDERR Summary ==="
          if [ -f log/stderr ]; then
            tail -20 log/stderr
          else
            echo "No stderr log found"
          fi
          
          exit $exit_code

      - name: Upload logs as artifacts
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: winget-logs-${{ inputs.job_type }}-${{ github.run_number }}
          path: |
            log/
            persist/
          retention-days: 7

  post-cronjob:
    name: Post processing
    if: always()
    needs:
      - pre-cronjob
      - process-winget
    uses: ./.github/workflows/post-cronjob.yml
    with:
      workdir: ${{ needs.pre-cronjob.outputs.workdir }}
      jobname: process_winget_packages_enhanced_${{ inputs.job_type }}
