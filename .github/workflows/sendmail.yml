name: Send mail
on:
  workflow_call:
    inputs:
      subject:
        description: Email subject
        type: string
        required: true
      body:
        description: Plain text email body (not HTML)
        type: string
        required: true
      html_body:
        description: Plain text email body (not HTML)
        type: string
        required: false
      mail_to:
        description: Comma-separated list of recipients
        type: string
        required: false
        default: <PERSON><PERSON><PERSON><PERSON><PERSON>@flexera.com
      mail_from:
        description: Sender address, must match /.+ <.+@.+>/ else it gets mangled
        type: string
        required: false
        default: Github runner for ${{ github.repository }} <<EMAIL>>
      attachments:
        description: files to attach
        type: string
        required: false
      aws_region:
        description: AWS region, eu-west-1 by default
        type: string
        required: false
        default: 'eu-west-1'

permissions: 
  contents: read 
  actions: read

jobs:
  sendmail:
    name: Send mail
    runs-on: [self-hosted, linux, 'infra_id:vt']
    steps:
        - name: Get configuration
          id: config
          run: |
            smtp_pass="$(aws --region '${{ inputs.aws_region }}' secretsmanager get-secret-value --output text --query SecretString --secret-id prod/secunia/EMAIL_HOST_PASSWORD)"
            echo "smtp_pass=$smtp_pass" >> $GITHUB_OUTPUT
            echo "::add-mask::$smtp_pass"

            echo '::echo::on'

            echo "smtp_user=$(aws --region '${{ inputs.aws_region }}' secretsmanager get-secret-value --output text --query SecretString --secret-id prod/secunia/EMAIL_HOST_USER)" >> $GITHUB_OUTPUT

            echo 'echo -n <SMTP password> | sha1sum -'
            echo -n "$smtp_pass" | sha1sum -

        - name: Send email
          id: send
          uses: dawidd6/action-send-mail@v4
          with:
            server_address: email-smtp.${{ inputs.aws_region }}.amazonaws.com
            server_port: 465
            username: ${{ steps.config.outputs.smtp_user }}
            password: ${{ steps.config.outputs.smtp_pass }}
            subject: ${{ inputs.subject }}
            html_body: ${{ inputs.html_body }}
            body: ${{ inputs.body }}
            to: ${{ inputs.mail_to }}
            from: ${{ inputs.mail_from }}
            attachments: ${{ inputs.attachments }}
