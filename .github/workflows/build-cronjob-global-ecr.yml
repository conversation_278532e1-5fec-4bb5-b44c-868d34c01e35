name: Build cron job Docker images to Global account
on:
  push:
    branches: [ 'master' ]
    paths:
      - 'cronjobs/**'
  workflow_dispatch:

env:
  AWS_DEFAULT_REGION: us-east-1
  AWS_ACCOUNT: ************

permissions: 
  contents: read 
  actions: read
  
jobs:
  build:
    name: Do
    runs-on: [self-hosted, linux, 'infra_id:vt']
    strategy:
      max-parallel: 2
      fail-fast: false
      matrix:
        jobname:
          - update_cvss4_threat_metrics
          - download_cvelistv5
          - process_winget_packages_enhanced
          - cpe23_matches_cleanup
          - winget_mapper
          - redmond_reconciliation
    steps:
      - name: Git Checkout
        uses: actions/checkout@v4
      
      # Configure AWS credentials for Flexera ECR
      - name: Configure AWS Credentials for Flexera ECR
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.GLOBAL_FLEXERA_ECR_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.GLOBAL_FLEXERA_ECR_AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      # Login to Amazon ECR
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build and push
        id: build
        working-directory: cronjobs
        run: |
          ECR_ENDPOINT="${AWS_ACCOUNT}.dkr.ecr.${AWS_DEFAULT_REGION}.amazonaws.com"
          image="$ECR_ENDPOINT/${{ matrix.jobname }}"
          docker build -t $image -f ${{ matrix.jobname }}/Dockerfile .
          docker push $image
