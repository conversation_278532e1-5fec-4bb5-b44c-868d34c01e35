name: ECR setup
on:
  workflow_call:
    outputs:
      ecr_endpoint:
        value: ${{ jobs.ecr.outputs.ecr_endpoint }}

env:
  AWS_DEFAULT_REGION: eu-west-1

permissions: 
  contents: read 
  actions: read
  
jobs:
  ecr:
    name: ECR setup
    runs-on: [self-hosted, linux, 'infra_id:vt']
    outputs:
      ecr_endpoint: ${{ steps.ecr-setup.outputs.ecr_endpoint }}
    steps:
      - name: ECR setup
        id: ecr-setup
        run: |
          AWS_ACCOUNT=$(aws sts get-caller-identity --output text --query 'Account')
          ECR_ENDPOINT="${AWS_ACCOUNT}.dkr.ecr.${AWS_DEFAULT_REGION}.amazonaws.com"
          echo "ecr_endpoint=$ECR_ENDPOINT" >> $GITHUB_OUTPUT
          aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $ECR_ENDPOINT
