name: VT ref-sys/update.php
on:
  schedule:
    -  cron: '2 1,4,7,10,13,16,19,22 * * *'
  workflow_dispatch:

env:
  AWS_DEFAULT_REGION: eu-west-1

permissions: 
  contents: read 
  actions: read 

jobs:
  ecr-setup:
    uses: ./.github/workflows/ecr-setup.yml
  pre-cronjob:
    uses: ./.github/workflows/pre-cronjob.yml

  main:
    name: "ref-sys/update.php"
    needs: [ecr-setup, pre-cronjob]
    runs-on: [self-hosted, linux, 'infra_id:vt']
    defaults:
      run:
        working-directory: ${{ needs.pre-cronjob.outputs.workdir }}
    steps:
      - name: Run
        run: |
          secret_json=$(aws --region $AWS_DEFAULT_REGION secretsmanager get-secret-value --output text --query SecretString --secret-id 'prod/prod-vt/vuln_track_cron')

          for php in secunia_update cert_update
          do
            echo "$php start $(date -u)" >> log/timing
            docker run --rm \
            -v "$(pwd)/log:/job/log" \
            -e DB_USER_VT="$(echo $secret_json | jq -r .username)" \
            -e DB_PASS_VT="$(echo $secret_json | jq -r .password)" \
            -e DB_HOST_VT="$(echo $secret_json | jq -r .host)" \
            --log-driver none -a stderr -a stdout \
            ${{ needs.ecr-setup.outputs.ecr_endpoint }}/$php \
            > log/$php.stdout 2> log/$php.stderr
            echo "$php end $(date -u)" >> log/timing
          done

      - name: Collect errors
        if: always()
        run: grep ^ log/*.stderr /dev/null >> log/stderr || true

  post-cronjob:
    name: Post processing
    if: always()
    needs:
      - pre-cronjob
      - main
    uses: ./.github/workflows/post-cronjob.yml
    with:
      workdir: ${{ needs.pre-cronjob.outputs.workdir }}
      jobname: ref_sys_update

  mail:
    name: Send mail
    if: always()
    needs: main
    uses: flexera/vt/.github/workflows/sendmail.yml@vtcronjobs
    with:
        subject: "Workflow ${{ github.workflow }} result."
        body: |
            repository: ${{ github.repository }}
            workflow: ${{ github.workflow }}
            result: ${{ needs.main.result }}
            See logs: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}
        mail_to: <EMAIL>, <EMAIL>
