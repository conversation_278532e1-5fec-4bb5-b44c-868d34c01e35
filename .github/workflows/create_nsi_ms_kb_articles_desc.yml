name: VT create_nsi_ms_kb_articles_desc
on:
  schedule:
    - cron: '7 2 * * *'
  workflow_dispatch:

env:
  AWS_DEFAULT_REGION: eu-west-1
  PERSISTENT_BUCKET: s3://vtcron-persistent

permissions: 
  contents: read 
  actions: read
  
jobs:
  ecr-setup:
    uses: ./.github/workflows/ecr-setup.yml
  pre-cronjob:
    uses: ./.github/workflows/pre-cronjob.yml

  populate-persist:
    name: Populate persistent data from S3
    needs: pre-cronjob
    runs-on: [self-hosted, linux, 'infra_id:vt']
    defaults:
      run:
        working-directory: ${{ needs.pre-cronjob.outputs.workdir }}
    steps:
      - name: Get nsi_ms_kb_articles_desc.csv from S3
        run: |
          aws s3 cp ${PERSISTENT_BUCKET}/nsi_ms_kb_articles_desc.csv ./persist
      # - name: Get cvrf_files from S3
      #   run: |
      #     aws s3 cp --recursive ${PERSISTENT_BUCKET}/cvrf_files/ ./persist

  main:
    name: "create_nsi_ms_kb_articles_desc"
    needs:
      - ecr-setup
      - pre-cronjob
      - populate-persist
    runs-on: [self-hosted, linux, 'infra_id:vt']
    defaults:
      run:
        working-directory: ${{ needs.pre-cronjob.outputs.workdir }}

    steps:
      - name: Run
        run: |
          docker run --rm \
          -v "$(pwd)/log:/job/log" \
          -v "$(pwd)/persist:/job/persist" \
          --log-driver none -a stderr -a stdout \
          ${{ needs.ecr-setup.outputs.ecr_endpoint }}/create_nsi_ms_kb_articles_desc \
          > log/stdout 2> log/stderr
      - name: Update nsi_ms_kb_articles_desc.csv to s3
        run: |
          aws s3 cp --no-progress ./persist/nsi_ms_kb_articles_desc.csv  "${PERSISTENT_BUCKET}"

  post-cronjob:
    name: Post processing
    if: always()
    needs:
      - pre-cronjob
      - main
    uses: ./.github/workflows/post-cronjob.yml
    with:
      workdir: ${{ needs.pre-cronjob.outputs.workdir }}
      jobname: create_nsi_ms_kb_articles_desc

  mail:
    name: Send mail
    if: always()
    needs: main
    uses: flexera/vt/.github/workflows/sendmail.yml@vtcronjobs
    with:
        subject: "Workflow ${{ github.workflow }} result."
        body: |
            repository: ${{ github.repository }}
            workflow: ${{ github.workflow }}
            result: ${{ needs.main.result }}
            See logs: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}
        mail_to: <EMAIL>, <EMAIL>
