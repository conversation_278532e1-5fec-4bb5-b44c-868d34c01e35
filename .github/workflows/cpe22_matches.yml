name: 'VT cpe22_matches'
on:
  #schedule:
  #  - cron: '15 5 * * 4'
  workflow_dispatch:

env:
  AWS_DEFAULT_REGION: eu-west-1

permissions: 
  contents: read 
  actions: read
  
jobs:
  ecr-setup:
    uses: ./.github/workflows/ecr-setup.yml
  pre-cronjob:
    uses: ./.github/workflows/pre-cronjob.yml

  main:
    name: "cpe22_matches"
    needs: [ecr-setup, pre-cronjob]
    runs-on: [self-hosted, linux, 'infra_id:vt']
    timeout-minutes: 2880
    defaults:
      run:
        working-directory: ${{ needs.pre-cronjob.outputs.workdir }}
  
    steps:
      - name: Run
        run: |
          secret_json=$(aws --region $AWS_DEFAULT_REGION secretsmanager get-secret-value --output text --query SecretString --secret-id 'prod/prod-vt/vuln_track_cron')
          docker run --rm \
          -v "$(pwd)/log:/job/log" \
          -e DB_USER_VT="$(echo $secret_json | jq -r .username)" \
          -e DB_PASS_VT="$(echo $secret_json | jq -r .password)" \
          -e DB_HOST_VT="$(echo $secret_json | jq -r .host)" \
          -v "$(pwd)/persist:/job/persist" \
          --log-driver none -a stderr -a stdout \
          ${{ needs.ecr-setup.outputs.ecr_endpoint }}/cpe22_matches \
          > log/stdout 2> log/stderr

  post-cronjob:
    name: Post processing
    if: always()
    needs:
      - pre-cronjob
      - main
    uses: ./.github/workflows/post-cronjob.yml
    with:
      workdir: ${{ needs.pre-cronjob.outputs.workdir }}
      jobname: update_cpe22_matches
