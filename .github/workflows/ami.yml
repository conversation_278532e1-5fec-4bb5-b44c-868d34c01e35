name: build AMIs
on:
  push:
    branches:
      - qa
  workflow_dispatch:
env:
  AWS_REGION: eu-west-1
  AWS_DEFAULT_REGION: eu-west-1
permissions: 
  contents: read 
  actions: read
jobs:
#   scale-infra-runner:
#     name: Scale up the runner
#     # Use share workflow from flexera/workflows for scale-runner job
#     uses: flexera/workflows/.github/workflows/scale-up-runner.yml@v2
#     permissions:
#       id-token: write
#     # Required parameters for scale-runner job
#     # Typically provide the output values from flexera/terraform-aws-github-runner module
#     with:
#       aws-region: eu-west-1
#       role-to-assume: "arn:aws:iam::633760448281:role/vt-mgmt-gh-actions-iam"
#       auto-scaling-group-name: "vt-mgmt-github-runner-tuzke"
 base:
    name: Build base AMI
    runs-on: [self-hosted, linux, 'infra_id:vt']
    env:
      HOME: /home
    steps:
      - name: Git Checkout
        uses: actions/checkout@v4
        with:
          ref: ci
      - name: Packer version
        run: /usr/bin/packer --version
      - name: Packer plugin
        run: /usr/bin/packer plugins install github.com/hashicorp/amazon
      - name: Build amazon linux2 base
        working-directory: vt_images/amazonlinux2
        run: |
              /usr/bin/packer plugins install github.com/hashicorp/amazon
              /usr/bin/packer build -var 'env=init' -color=false -only=amazon-ebs packer.json
      - name: Build vtweb image
        working-directory: vt_images/vtweb
        run: |
              /usr/bin/packer plugins install github.com/hashicorp/amazon
              /usr/bin/packer build -var 'env=init' -color=false -only=amazon-ebs packer.json
