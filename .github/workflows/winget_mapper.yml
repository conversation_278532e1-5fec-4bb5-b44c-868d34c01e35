name: VT winget_mapper
on:
  schedule:
    - cron: '0 5 * * *'  # Run at 5 AM UTC
  workflow_dispatch:

env:
  AWS_DEFAULT_REGION: eu-west-1
  ECR_AWS_ACCOUNT: ************
  ECR_AWS_DEFAULT_REGION: us-east-1

permissions:
  contents: read
  actions: read

jobs:
  pre-cronjob:
    uses: ./.github/workflows/pre-cronjob.yml

  docker-pull:
    name: "winget_mapper pull"
    needs: [pre-cronjob]
    runs-on: [self-hosted, linux, 'infra_id:vt']
    defaults:
      run:
        working-directory: ${{ needs.pre-cronjob.outputs.workdir }}

    steps:
      - name: Configure AWS Credentials for Flexera ECR
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.GLOBAL_FLEXERA_ECR_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.GLOBAL_FLEXERA_ECR_AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Docker Pull
        run: |
          ECR_ENDPOINT="$ECR_AWS_ACCOUNT.dkr.ecr.$ECR_AWS_DEFAULT_REGION.amazonaws.com"
          docker pull $ECR_ENDPOINT/winget_mapper

  docker-run:
    name: "winget_mapper run"
    needs:
      - pre-cronjob
      - docker-pull
    runs-on: [self-hosted, linux, 'infra_id:vt']
    defaults:
      run:
        working-directory: ${{ needs.pre-cronjob.outputs.workdir }}

    steps:
      - name: Docker Run
        run: |
          secret_json=$(aws --region $AWS_DEFAULT_REGION secretsmanager get-secret-value --output text --query SecretString --secret-id 'prod/prod-vt/vuln_track_cron')
          ECR_ENDPOINT="$ECR_AWS_ACCOUNT.dkr.ecr.$ECR_AWS_DEFAULT_REGION.amazonaws.com"
          docker run --rm \
          -v "$(pwd)/log:/job/log" \
          -e DB_USER_VT="$(echo $secret_json | jq -r .username)" \
          -e DB_PASS_VT="$(echo $secret_json | jq -r .password)" \
          -e DB_HOST_VT="$(echo $secret_json | jq -r .host)" \
          -v "$(pwd)/persist:/job/persist" \
          --log-driver none -a stderr -a stdout \
          $ECR_ENDPOINT/winget_mapper \
          > log/stdout 2> log/stderr

  post-cronjob:
    name: Post processing
    if: always()
    needs:
      - pre-cronjob
      - docker-pull
      - docker-run
    uses: ./.github/workflows/post-cronjob.yml
    with:
      workdir: ${{ needs.pre-cronjob.outputs.workdir }}
      jobname: winget_mapper
