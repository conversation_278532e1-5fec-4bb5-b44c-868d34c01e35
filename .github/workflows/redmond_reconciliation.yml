name: VT <PERSON><PERSON> Reconciliation
on:
  # Scheduled run - uncomment and adjust as needed
  # schedule:
  #   # Run every hour
  #   - cron: '0 * * * *'

  # Manual trigger with optional parameters
  workflow_dispatch:
    inputs:
      # IDs input options
      ids:
        description: 'Comma-separated Redmond IDs (e.g., 12345,12346,12347)'
        required: false
        type: string

      ids_file:
        description: 'CSV file name containing Redmond IDs (redmond_ids.csv)'
        required: false
        type: string

      # Type input options
      type:
        description: 'Package type filter (e.g., MSI, exe, Legacy)'
        required: false
        type: string

      type_file:
        description: 'CSV file name containing package types (types.csv)'
        required: false
        type: string

      # Product filter
      product:
        description: 'Product name filter (e.g., "Microsoft Office")'
        required: false
        type: string

      # Log level
      log_level:
        description: 'Log level'
        required: false
        default: 'INFO'
        type: choice
        options: ['DEBUG', 'INFO', 'WARNING', 'ERROR']

env:
  AWS_DEFAULT_REGION: eu-west-1
  ECR_AWS_ACCOUNT: ************
  ECR_AWS_DEFAULT_REGION: us-east-1

permissions:
  contents: read
  actions: read

jobs:
  pre-cronjob:
    uses: ./.github/workflows/pre-cronjob.yml

  docker-pull:
    name: "redmond_reconciliation pull"
    needs: [pre-cronjob]
    runs-on: [self-hosted, linux, 'infra_id:vt']
    defaults:
      run:
        working-directory: ${{ needs.pre-cronjob.outputs.workdir }}

    steps:
      - name: Configure AWS Credentials for Flexera ECR
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.GLOBAL_FLEXERA_ECR_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.GLOBAL_FLEXERA_ECR_AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Docker Pull
        run: |
          ECR_ENDPOINT="$ECR_AWS_ACCOUNT.dkr.ecr.$ECR_AWS_DEFAULT_REGION.amazonaws.com"
          docker pull $ECR_ENDPOINT/redmond_reconciliation

  docker-run:
    name: "redmond_reconciliation run"
    needs:
      - pre-cronjob
      - docker-pull
    runs-on: [self-hosted, linux, 'infra_id:vt']
    defaults:
      run:
        working-directory: ${{ needs.pre-cronjob.outputs.workdir }}

    steps:
      - name: Build Docker command arguments
        id: docker-args
        run: |
          # Start with base arguments
          DOCKER_ARGS=""

          # Add log level based on input
          case "${{ inputs.log_level }}" in
            "DEBUG")
              DOCKER_ARGS="$DOCKER_ARGS --debug"
              ;;
            "INFO")
              DOCKER_ARGS="$DOCKER_ARGS --verbose"
              ;;
            "WARNING"|"ERROR")
              # Default log level (no extra flags)
              ;;
          esac

          # Add IDs if provided
          if [ -n "${{ inputs.ids }}" ]; then
            DOCKER_ARGS="$DOCKER_ARGS --ids '${{ inputs.ids }}'"
          fi

          # Add IDs file if provided
          if [ -n "${{ inputs.ids_file }}" ]; then
            DOCKER_ARGS="$DOCKER_ARGS --ids_file '${{ inputs.ids_file }}'"
          fi

          # Add type if provided
          if [ -n "${{ inputs.type }}" ]; then
            DOCKER_ARGS="$DOCKER_ARGS --type '${{ inputs.type }}'"
          fi

          # Add type file if provided
          if [ -n "${{ inputs.type_file }}" ]; then
            DOCKER_ARGS="$DOCKER_ARGS --type_file '${{ inputs.type_file }}'"
          fi

          # Add product filter if provided
          if [ -n "${{ inputs.product }}" ]; then
            DOCKER_ARGS="$DOCKER_ARGS --product '${{ inputs.product }}'"
          fi

          echo "docker_args=$DOCKER_ARGS" >> $GITHUB_OUTPUT
          echo "Docker arguments: $DOCKER_ARGS"

      - name: Docker Run
        run: |
          secret_json=$(aws --region $AWS_DEFAULT_REGION secretsmanager get-secret-value --output text --query SecretString --secret-id 'prod/prod-vt/vuln_track_cron')
          ECR_ENDPOINT="$ECR_AWS_ACCOUNT.dkr.ecr.$ECR_AWS_DEFAULT_REGION.amazonaws.com"

          echo "Starting Redmond Reconciliation with arguments: ${{ steps.docker-args.outputs.docker_args }}"

          docker run --rm \
          -v "$(pwd)/log:/job/log" \
          -v "$(pwd)/persist:/job/persist" \
          --log-driver none -a stderr -a stdout \
          -e "secret_json=$secret_json" \
          $ECR_ENDPOINT/redmond_reconciliation ${{ steps.docker-args.outputs.docker_args }} \
          > log/stdout 2> log/stderr

  post-cronjob:
    name: Post processing
    if: always()
    needs:
      - pre-cronjob
      - docker-run
    uses: ./.github/workflows/post-cronjob.yml
    with:
      workdir: ${{ needs.pre-cronjob.outputs.workdir }}
      jobname: redmond_reconciliation
