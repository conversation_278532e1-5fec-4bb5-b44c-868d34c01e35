# VPM -- SVMDEVOPS-3690
# 1 0,12 * * * cd /home/<USER>/nsi_rules/nsi_ms_kb_articles && /usr/bin/python3 vendor_patch_module_importer.py

name: VT vendor_patch_module_importer -x
on:
  schedule:
    - cron: '*/30 * * * *'
  workflow_dispatch:

env:
  AWS_DEFAULT_REGION: eu-west-1

permissions: 
  contents: read 
  actions: read 

jobs:
  ecr-setup:
    uses: ./.github/workflows/ecr-setup.yml
  pre-cronjob:
    uses: ./.github/workflows/pre-cronjob.yml

  main:
    name: "vendor_patch_module_importer"
    needs: [ecr-setup, pre-cronjob]
    runs-on: [self-hosted, linux, 'infra_id:vt']
    defaults:
      run:
        working-directory: ${{ needs.pre-cronjob.outputs.workdir }}
    steps:
      # These setup steps have values also present in the Python script's config file.
      # It would be good if the Python script did this stuff.
      - name: Get SSH host key
        run: |
          ssh-keyscan s-5d69210f69224436a.server.transfer.eu-west-1.amazonaws.com | tee s-5d69210f69224436a_host_key
      - name: Get SSH private key from Secrets Manger
        run: |
          aws --region $AWS_DEFAULT_REGION secretsmanager get-secret-value --output text --query SecretString --secret-id 'prod/vtcron/liquit_private_key' > liquit_private_key
          ls -l liquit_private_key
          sha1sum liquit_private_key
      - name: Run
        run: |
          docker run --rm \
          -v "$(pwd)/log:/job/log" \
          -v "$(pwd)/persist:/job/persist" \
          -v "$(pwd)/liquit_private_key:/job/liquit_private_key" \
          -v "$(pwd)/s-5d69210f69224436a_host_key:/root/.ssh/known_hosts" \
          --log-driver none -a stderr -a stdout \
          ${{ needs.ecr-setup.outputs.ecr_endpoint }}/vendor_patch_module_importer --log log/vpm_importer.log -x \
          > log/stdout 2> log/stderr

  post-cronjob:
    name: Post processing
    if: always()
    needs:
      - pre-cronjob
      - main
    uses: ./.github/workflows/post-cronjob.yml
    with:
      workdir: ${{ needs.pre-cronjob.outputs.workdir }}
      jobname: vendor_patch_module_importer

  mail:
    name: Send mail
    if: always()
    needs: main
    uses: flexera/vt/.github/workflows/sendmail.yml@vtcronjobs
    with:
        subject: "Workflow ${{ github.workflow }} result."
        body: |
            repository: ${{ github.repository }}
            workflow: ${{ github.workflow }}
            result: ${{ needs.main.result }}
            See logs: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}
        mail_to: <EMAIL>, <EMAIL>
