name: VT process_winget_packages_enhanced
on:
  # schedule:
  # - cron: '30 * * * *'
  workflow_dispatch:

env:
  AWS_DEFAULT_REGION: eu-west-1
  ECR_AWS_ACCOUNT: ************
  ECR_AWS_DEFAULT_REGION: us-east-1

permissions: 
  contents: read 
  actions: read
    
jobs:
  pre-cronjob:
    uses: ./.github/workflows/pre-cronjob.yml

  main:
    name: "process_winget_packages_enhanced"
    needs: [pre-cronjob]
    runs-on: [self-hosted, linux, 'infra_id:vt']
    defaults:
      run:
        working-directory: ${{ needs.pre-cronjob.outputs.workdir }}
  
    steps:
      - name: Git Checkout
        uses: actions/checkout@v4

      # Configure AWS credentials for Flexera ECR
      - name: Configure AWS Credentials for Flexera ECR
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.GLOBAL_FLEXERA_ECR_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.GLOBAL_FLEXERA_ECR_AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      # Login to Amazon ECR
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2      
        
      # Setup winget-pkgs repository
      - name: Setup winget-pkgs repository
        run: |
          # Create a persistent directory for winget-pkgs if it doesn't exist
          PERSISTENT_DIR="/tmp/winget-pkgs-cache"
          
          if [ ! -d "$PERSISTENT_DIR" ]; then
            echo "Creating persistent directory for winget-pkgs cache..."
            mkdir -p "$PERSISTENT_DIR"
          fi
          
          # Clone or update winget-pkgs repository in persistent location
          if [ ! -d "$PERSISTENT_DIR/.git" ]; then
            echo "Cloning winget-pkgs repository..."
            git clone --depth 1 https://github.com/microsoft/winget-pkgs.git "$PERSISTENT_DIR"
          else
            echo "Updating existing winget-pkgs repository, dry run..."
            cd "$PERSISTENT_DIR"
            # Reset any local changes and update
          #  git fetch --depth 50 origin master
          #  git reset --hard origin/master
            # Clean any untracked files
          #  git clean -fd
             git fetch --dry-run
             cd -
          fi
          
          # Create a symlink in the working directory
          ln -sf "$PERSISTENT_DIR" winget-pkgs
          
          # Verify the repository structure
          if [ ! -d "winget-pkgs/manifests" ]; then
            echo "Error: winget-pkgs/manifests directory not found!"
            exit 1
          fi
          
          echo "winget-pkgs repository ready. Manifest count:"
          find winget-pkgs/manifests -name "*.yaml" -o -name "*.yml" | wc -l
          
          echo "Repository last updated:"
          cd winget-pkgs && git log -1 --format="%cd %s" --date=iso      
      
      - name: Run process_winget_packages_enhanced
        run: |
          ECR_ENDPOINT="$ECR_AWS_ACCOUNT.dkr.ecr.$ECR_AWS_DEFAULT_REGION.amazonaws.com"
          
          # Ensure the winget-pkgs directory has proper permissions
          sudo chown -R $(id -u):$(id -g) winget-pkgs 2>/dev/null || true
          
          docker run --rm \
          -v "$(pwd)/log:/job/log" \
          -v "$(pwd)/persist:/job/persist" \
          -v "$(pwd)/winget-pkgs:/job/winget-pkgs" \
          -e "GIT_TERMINAL_PROMPT=0" \
          --log-driver none -a stderr -a stdout \
          $ECR_ENDPOINT/process_winget_packages_enhanced --delta 24h --log-level INFO --dry-run \
          > log/stdout 2> log/stderr
          
          echo "Docker container execution completed. Exit code: $?"

  post-cronjob:
    name: Post processing
    if: always()
    needs:
      - pre-cronjob
      - main
    uses: ./.github/workflows/post-cronjob.yml
    with:
      workdir: ${{ needs.pre-cronjob.outputs.workdir }}
      jobname: process_winget_packages_enhanced
