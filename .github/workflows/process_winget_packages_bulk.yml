name: VT process_winget_packages_enhanced_bulk

permissions: 
  contents: read 
  actions: read

on:
  #schedule:
    # Run weekly on Sundays at 2 AM UTC
    #- cron: '0 2 * * 0'
  workflow_dispatch:
    inputs:
      max_parallel_folders:
        description: 'Maximum parallel folders to process'
        required: false
        default: '2'
        type: string
        
      dry_run:
        description: 'Run in dry-run mode'
        required: false
        default: false
        type: boolean
        
      log_level:
        description: 'Log level'
        required: false
        default: 'INFO'
        type: choice
        options: ['DEBUG', 'INFO', 'WARNING', 'ERROR']
        
      no_git_release_date:
        description: 'Skip git commit date lookups for performance'
        required: false
        default: true
        type: boolean
        
      immediate_commit:
        description: 'Commit database transaction immediately after each operation'
        required: false
        default: true
        type: boolean

jobs:
  bulk-processing:
    uses: ./.github/workflows/process_winget_packages_common.yml
    with:
      job_type: 'bulk'
      time_period: '24h'  # Not used for bulk jobs, but required by common workflow
      dry_run: ${{ inputs.dry_run || false }}
      log_level: ${{ inputs.log_level || 'INFO' }}
      max_parallel_folders: ${{ inputs.max_parallel_folders || '2' }}
      no_git_release_date: ${{ inputs.no_git_release_date != false }}
      immediate_commit: ${{ inputs.immediate_commit || true }}
