name: Terraform VT Web

permissions:
  id-token: write
  contents: read 
  issues: write
  actions: read
  security-events: write
  pull-requests: write

on:
  workflow_dispatch:
  push:
    branches:
      - prod
  workflow_run:
    workflows: ["build AMIs"]
    types: [completed]
  repository_dispatch:
    types: [run-terraform]

env:
  AWS_REGION: eu-west-1
  AWS_DEFAULT_REGION: eu-west-1
  TF_IN_AUTOMATION: true

jobs:
  terraform-plan:
    name: "Terraform Plan"
    runs-on: [self-hosted, linux, 'infra_id:vt']
    outputs:
      branch_name: ${{steps.current_branch.outputs.branch}}
    defaults:
      run:
        shell: bash
    steps:
      - name: SSH Agent Setup
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.GLOBAL_FLEXERA_CI_PRIVATE_SSH_KEY }}
      
      - name: Branch
        id: current_branch
        run: echo "branch=${GITHUB_REF##*/}" >> $GITHUB_OUTPUT

      - name: Checkout
        uses: actions/checkout@v4

      - name: Terraform version
        id: version
        run: terraform version

      - name: Terraform Init
        id: init
        run: terraform init
        working-directory: vtweb-tf/env/${{ steps.current_branch.outputs.branch }}

      - name: Terraform plan
        id: plan
        run: terraform plan  -no-color -out=${{ steps.current_branch.outputs.branch }}-plan.out | tee ${{ steps.current_branch.outputs.branch }}-plan.txt
        working-directory: vtweb-tf/env/${{ steps.current_branch.outputs.branch }}

      - name: Save plan as artifact
        id: save-plan
        uses: actions/upload-artifact@v4.4.3
        with:
          path: vtweb-tf/env/${{ steps.current_branch.outputs.branch }}/${{ steps.current_branch.outputs.branch }}-plan.out
          name: ${{ steps.current_branch.outputs.branch }}-plan.out
          retention-days: 10

      - name: Save plan description as artifact
        id: save-txt
        uses: actions/upload-artifact@v4.4.3
        with:
          path: vtweb-tf/env/${{ steps.current_branch.outputs.branch }}/${{ steps.current_branch.outputs.branch }}-plan.txt
          name: ${{ steps.current_branch.outputs.branch }}-plan.txt
          retention-days: 10
  
  terraform-apply:
    name: "Terraform Apply"
    runs-on: [self-hosted, linux, 'infra_id:vt']
    needs: [terraform-plan]
    environment:
      name: ${{ needs.terraform-plan.outputs.branch_name }}
    steps:
      - name: SSH Agent Setup
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.GLOBAL_FLEXERA_CI_PRIVATE_SSH_KEY }}

      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ needs.terraform-plan.outputs.branch_name }}

      - name: Terraform Init
        id: init
        run: terraform init
        working-directory: vtweb-tf/env/${{ needs.terraform-plan.outputs.branch_name }}

      - name: Download saved artifact
        id: download
        uses: actions/download-artifact@v4.3.0
        with:
          name: ${{ needs.terraform-plan.outputs.branch_name }}-plan.out
          path: vtweb-tf/env/${{ needs.terraform-plan.outputs.branch_name }}

      - name: Terraform apply
        id: apply
        run: terraform apply -no-color ${{ needs.terraform-plan.outputs.branch_name }}-plan.out
        working-directory: vtweb-tf/env/${{ needs.terraform-plan.outputs.branch_name }}
        
