name: VT process_winget_packages_enhanced_delta

permissions: 
  contents: read 
  actions: read
  
on:
  schedule:
    # Run every 4 hours
    - cron: '0 */1 * * *'
  workflow_dispatch:
    inputs:
      time_period:
        description: 'Time period for delta processing (e.g., 4h, 24h, 7d)'
        required: false
        default: '1h'
        type: string
      
      dry_run:
        description: 'Run in dry-run mode'
        required: false
        default: false
        type: boolean
        
      log_level:
        description: 'Log level'
        required: false
        default: 'INFO'
        type: choice
        options: ['DEBUG', 'INFO', 'WARNING', 'ERROR']
      
jobs:
  delta-processing:
    uses: ./.github/workflows/process_winget_packages_common.yml
    with:
      job_type: 'delta'
      time_period: ${{ inputs.time_period || '1h' }}
      dry_run: ${{ inputs.dry_run || false }}
      log_level: ${{ inputs.log_level || 'INFO' }}
      max_parallel_folders: '2'  # Conservative for delta jobs
      no_git_release_date: false  # Enable git lookups for delta jobs
      immediate_commit: false     # Batch commits for delta jobs
