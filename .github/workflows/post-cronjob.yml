name: <PERSON>ron job post processing
on:
  workflow_call:
    inputs:
      workdir:
        description: where to find job logs, etc.
        type: string
        required: true
      jobname:
        description: one-word name of this job, used for archiving
        type: string
        required: true
      log-bucket:
        description: S3 bucket where logs go
        type: string
        default: 's3://vtcron-jobs-logs'

permissions: 
  contents: read 
  actions: read
  
jobs:
  archive:
    name: Archive results in S3
    runs-on: [self-hosted, linux, 'infra_id:vt']
    steps:
      - name: Save logs to S3
        id: save
        working-directory: ${{ inputs.workdir }}
        run: |
          ARCHIVE_NAME="${{ inputs.jobname }}-$(date "+%Y-%m-%d_%H:%M").tar.gz"
          tar -C log -cvzf ./$ARCHIVE_NAME .
          aws s3 cp ./$ARCHIVE_NAME ${{ inputs.log-bucket }}/${{ inputs.jobname }}/

  summarize:
    name: Summarize job results
    runs-on: [self-hosted, linux, 'infra_id:vt']
    defaults:
      run:
        working-directory: ${{ inputs.workdir }}
    steps:
      - name: Line count the logs
        run: wc -l log/*
      - name: Errors from stderr
        run: cat -n log/stderr
      - name: All logs and outputs
        run: grep -n ^ log/* /dev/null || true

  delete:
    name: Delete the working directory
    runs-on: [self-hosted, linux, 'infra_id:vt']
    needs:
      - archive
      - summarize
    steps:
      - name: rm -rf
        run: rm -rvf ${{ inputs.workdir }}
