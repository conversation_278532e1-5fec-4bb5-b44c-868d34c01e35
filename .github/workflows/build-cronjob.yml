name: Build cron job Docker images
on:
  push:
    branches: [ 'master' ]
    paths:
      - 'cronjobs/**'
  workflow_dispatch:
env:
  AWS_DEFAULT_REGION: eu-west-1
permissions: 
  contents: read 
  actions: read
jobs:
  ecr-setup:
    uses: ./.github/workflows/ecr-setup.yml
  build:
    # Very short name because the GUI interface cuts off the names of the images
    name: Do
    runs-on: [self-hosted, linux, 'infra_id:vt']
    needs:
      - ecr-setup
    strategy:
      max-parallel: 3
      fail-fast: false
      matrix:
        jobname:
          - download_ms_cvrf_files
          - trim_vuln_sys_log
          - create_new_england_cve_threats
          - create_nsi_ms_kb_articles_desc
          - create_nsi_ms_kb_articles_table
          - create_sr_product_secure_ranges
          - detect_references
          - cert_update
          - secunia_update
          - update_sr_solution_list
          - vendor_patch_module_importer
          - update_vt_nvd_data
          - update_nvd_cve_refsys_entries
          - cpe23_matches
          - vendor_patch_module_setups
          - cpe22_matches

          # still on old vtcron:
          # - advisory_sender
          # - scap
    steps:
      - name: Git Checkout
        uses: actions/checkout@v4
      - name: Make ECR repo
        run: aws ecr create-repository --repository-name ${{ matrix.jobname }} || true
      - name: Build and push
        id: build
        working-directory: cronjobs
        run: |
          image="${{ needs.ecr-setup.outputs.ecr_endpoint }}/${{ matrix.jobname }}"
          DOCKER_BUILDKIT=1 docker build -t $image -f ${{ matrix.jobname }}/Dockerfile .
          docker push $image
