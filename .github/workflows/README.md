# Process Winget Packages Enhanced - GitHub Actions Workflows

This directory contains GitHub Actions workflows for processing Windows Package Manager (winget) manifest files and uploading package information to a database.

## Architecture Overview

The implementation uses a **reusable workflow pattern** with three main workflow files:

### Core Workflows

1. **`process_winget_packages_common.yml`** - Reusable workflow template
2. **`process_winget_packages_delta.yml`** - Delta processing (frequent, incremental updates)
3. **`process_winget_packages_bulk.yml`** - Bulk processing (full dataset, less frequent)

### Legacy Workflow

- **`process_winget_packages_enhanced.yml`** - Original workflow (now shows both modern and legacy approaches)

## Workflow Details

### Delta Processing (`process_winget_packages_delta.yml`)

**Purpose**: Process only packages that have changed recently, suitable for frequent execution.

**Schedule**: Every 4 hours (`0 */4 * * *`)

**Key Features**:
- Uses `--delta` flag with configurable time period
- Enables git commit date lookups for accurate release dates
- Conservative resource usage (2 parallel folders)
- Batch database commits for efficiency

**Manual Trigger Parameters**:
- `time_period`: Time window for changes (default: '4h')
- `dry_run`: Run without database changes (default: false)
- `log_level`: Logging verbosity (default: 'INFO')
- `force_update_repo`: Update git repo before processing (default: true)

### Bulk Processing (`process_winget_packages_bulk.yml`)

**Purpose**: Process the entire winget package dataset, optimized for performance.

**Schedule**: Weekly on Sundays at 2 AM UTC (`0 2 * * 0`)

**Key Features**:
- Processes all packages without delta filtering
- Skips git commit date lookups for performance (`--no-git-release-date`)
- Higher parallelism (6 parallel folders)
- Immediate database commits for reliability (`--immediate-commit`)

**Manual Trigger Parameters**:
- `max_parallel_folders`: Parallelism level (default: '6')
- `dry_run`: Run without database changes (default: false)
- `log_level`: Logging verbosity (default: 'INFO')
- `no_git_release_date`: Skip git lookups (default: true)
- `immediate_commit`: Immediate DB commits (default: true)

### Common Workflow (`process_winget_packages_common.yml`)

**Purpose**: Reusable template that handles the core processing logic.

**Key Features**:
- Single Docker image for consistency
- Dynamic argument building based on job type
- Comprehensive git repository management
- Artifact collection for logs and debugging
- Enhanced error handling and reporting

**Input Parameters**:
- `job_type`: 'delta' or 'bulk' (required)
- `time_period`: Delta time window (default: '4h')
- `dry_run`: Validation-only mode (default: true)
- `max_parallel_folders`: Parallelism (default: '2')
- `log_level`: Logging level (default: 'INFO')
- `no_git_release_date`: Skip git operations (default: false)
- `immediate_commit`: Immediate DB commits (default: false)

## Usage Examples

### Running Delta Processing Manually

```yaml
# Via GitHub Actions UI
workflow_dispatch:
  inputs:
    time_period: '8h'        # Process changes from last 8 hours
    dry_run: false           # Actually perform database operations
    log_level: 'DEBUG'       # Verbose logging
```

### Running Bulk Processing Manually

```yaml
# Via GitHub Actions UI
workflow_dispatch:
  inputs:
    max_parallel_folders: '4'    # Reduce parallelism
    dry_run: true                # Test run without DB changes
    no_git_release_date: false   # Enable git lookups
    immediate_commit: false      # Use batch commits
```

## Docker Command Examples

The workflows generate different Docker commands based on the job type:

### Delta Job Command
```bash
docker run --rm \
  -v "$(pwd)/log:/job/log" \
  -v "$(pwd)/persist:/job/persist" \
  -v "$(pwd)/winget-pkgs:/job/winget-pkgs" \
  -e "GIT_TERMINAL_PROMPT=0" \
  $ECR_ENDPOINT/process_winget_packages_enhanced \
  --delta 4h --log-level INFO
```

### Bulk Job Command
```bash
docker run --rm \
  -v "$(pwd)/log:/job/log" \
  -v "$(pwd)/persist:/job/persist" \
  -v "$(pwd)/winget-pkgs:/job/winget-pkgs" \
  -e "GIT_TERMINAL_PROMPT=0" \
  $ECR_ENDPOINT/process_winget_packages_enhanced \
  --no-git-release-date --max-parallel-folders 6 --immediate-commit --log-level INFO
```

## Git Repository Management

### Delta Processing
- Fetches recent history (`--depth 50`) for accurate change detection
- Updates to latest master branch
- Shows recent commit history for debugging

### Bulk Processing  
- Uses shallow clone (`--depth 1`) for efficiency
- Simple repository update without extensive history

## Monitoring and Debugging

### Log Artifacts
Each workflow run uploads log artifacts including:
- `log/stdout` - Standard output from the Python process
- `log/stderr` - Error output from the Python process  
- `persist/` - Any persistent data or cache files
- Retention: 7 days

### Log Summary
Workflows automatically display the last 20 lines of both stdout and stderr for quick debugging.

### Error Handling
- Docker exit codes are preserved and reported
- Failed runs trigger artifact collection
- Post-processing cleanup always runs

## Migration from Shell Scripts

The new workflows replace the shell script approach:

### Before (Shell Scripts)
- `run_redmond_importer.sh` - Basic delta processing
- `run_redmond_importer_bulk.sh` - Bulk processing with resource management

### After (GitHub Actions)
- Centralized configuration and monitoring
- Better error handling and reporting
- Automatic artifact collection
- Consistent execution environment
- Easy parameter adjustment via UI

## Configuration

### Environment Variables
```yaml
env:
  AWS_DEFAULT_REGION: eu-west-1
  ECR_AWS_ACCOUNT: ************  
  ECR_AWS_DEFAULT_REGION: us-east-1
```

### Secrets Required
- `GLOBAL_FLEXERA_ECR_AWS_ACCESS_KEY_ID`
- `GLOBAL_FLEXERA_ECR_AWS_SECRET_ACCESS_KEY`

### Runner Requirements
- Self-hosted Linux runners with 'infra_id:vt' label
- Docker runtime
- Git installed
- AWS CLI configured (via actions)

## Troubleshooting

### Common Issues

1. **Git Repository Updates Failing**
   - Check network connectivity to GitHub
   - Verify git permissions in runner environment

2. **Docker Image Not Found**
   - Verify ECR login is successful
   - Check image exists in specified ECR repository

3. **High Resource Usage**
   - Reduce `max_parallel_folders` for bulk jobs
   - Use `dry_run: true` for testing

4. **Database Connection Issues**
   - Verify secrets are properly configured
   - Check database connectivity from runner

### Debug Mode
Enable debug logging by setting `log_level: 'DEBUG'` in manual triggers.

## Best Practices

### Scheduling
- **Delta**: Run frequently (every 4 hours) to catch changes quickly
- **Bulk**: Run weekly during low-usage periods (Sunday 2 AM UTC)

### Resource Management
- Use lower parallelism during business hours
- Monitor runner resource usage
- Enable immediate commits for bulk processing to avoid long transactions

### Testing
- Always test with `dry_run: true` first
- Use shorter time periods for delta testing
- Monitor log artifacts for debugging

## Future Enhancements

Potential improvements to consider:

1. **Matrix Builds**: Support multiple configurations in parallel
2. **Conditional Execution**: Skip runs if no changes detected
3. **Notification Integration**: Slack/email alerts for failures
4. **Metrics Collection**: Processing time and success rate tracking
5. **Auto-scaling**: Dynamic parallelism based on change volume
