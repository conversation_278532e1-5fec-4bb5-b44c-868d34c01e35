name: VT download_ms_cvrf_files
on:
  schedule:
    - cron: '22 0 * * 1,3'
    - cron: '4 23 * * 2'
  workflow_dispatch:

env:
  AWS_DEFAULT_REGION: eu-west-1
  PERSISTENT_BUCKET: s3://vtcron-persistent

permissions: 
  contents: read 
  actions: read
  
jobs:
  ecr-setup:
    uses: ./.github/workflows/ecr-setup.yml
  pre-cronjob:
    uses: ./.github/workflows/pre-cronjob.yml

  main:
    name: "Download ms_cvrf files"
    needs: [ecr-setup, pre-cronjob]
    runs-on: [self-hosted, linux, 'infra_id:vt']
    defaults:
      run:
        working-directory: ${{ needs.pre-cronjob.outputs.workdir }}

    steps:
      - name: Run
        run: |
          docker run --rm \
          -v "$(pwd)/log:/job/log" \
          -v "$(pwd)/persist:/job/persist" \
          --log-driver none -a stderr -a stdout \
          ${{ needs.ecr-setup.outputs.ecr_endpoint }}/download_ms_cvrf_files \
          > log/stdout 2> log/stderr
      - name: Upload files to s3 bucket
        run: |
            aws s3 cp --no-progress persist/cvrf_files "${PERSISTENT_BUCKET}/cvrf_files/" --recursive

  post-cronjob:
    name: Post processing
    if: always()
    needs:
      - pre-cronjob
      - main
    uses: ./.github/workflows/post-cronjob.yml
    with:
      workdir: ${{ needs.pre-cronjob.outputs.workdir }}
      jobname: download_ms_cvrf_files

  mail:
    name: Send mail
    if: always()
    needs: main
    uses: flexera/vt/.github/workflows/sendmail.yml@vtcronjobs
    with:
        subject: "Workflow ${{ github.workflow }} result."
        body: |
            repository: ${{ github.repository }}
            workflow: ${{ github.workflow }}
            result: ${{ needs.main.result }}
            See logs: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}
        mail_to: <EMAIL>, <EMAIL>

        
