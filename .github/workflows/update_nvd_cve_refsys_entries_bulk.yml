name: VT update_nvd_cve_refsys_entries --bulk=1
on:
  workflow_dispatch:

env:
  AWS_DEFAULT_REGION: eu-west-1

permissions: 
  contents: read 
  actions: read
  
jobs:
  ecr-setup:
    uses: ./.github/workflows/ecr-setup.yml
  pre-cronjob:
    uses: ./.github/workflows/pre-cronjob.yml

  main:
    name: "update_nvd_cve_refsys_entries --bulk=1"
    needs: [ecr-setup, pre-cronjob]
    runs-on: [self-hosted, linux, 'infra_id:vt']
    defaults:
      run:
        working-directory: ${{ needs.pre-cronjob.outputs.workdir }}
  
    steps:
      - name: Run
        run: |
          docker run --rm \
          -v "$(pwd)/log:/job/log" \
          -v "$(pwd)/persist:/job/persist" \
          --log-driver none -a stderr -a stdout \
          ${{ needs.ecr-setup.outputs.ecr_endpoint }}/update_nvd_cve_refsys_entries --cve_ids=1 \
          > log/stdout 2> log/stderr

  post-cronjob:
    name: Post processing
    if: always()
    needs:
      - pre-cronjob
      - main
    uses: ./.github/workflows/post-cronjob.yml
    with:
      workdir: ${{ needs.pre-cronjob.outputs.workdir }}
      jobname: update_nvd_cve_refsys_entries_bulk
